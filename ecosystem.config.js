module.exports = {
    apps: [{
        name: 'my-app',
        script: 'npm',
        args: 'run start:ci',
		node_args: "--max-old-space-size=5120",
        watch: false,
		out_file: "/home/<USER>/app/logs/out.log",
		error_file: "/home/<USER>/app/logs/error.log",
		merge_logs: true,
		log_date_format: "DD-MM HH:mm:ss Z",
		log_type: "json",
        env: {

        },
        env_production: {

        },
        post_update: ["npm run build"]
    }]
};
