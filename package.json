{"name": "raleon-webapp", "version": "0.0.1", "description": "raleon_webapp", "keywords": ["loopback-application", "loopback"], "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": "12 || 14 || 16 || 17"}, "scripts": {"build": "npm run build:ui && lb-tsc", "build:watch": "lb-tsc --watch", "lint": "npm run eslint && npm run prettier:check", "lint:fix": "npm run eslint:fix && npm run prettier:fix", "prettier:cli": "lb-prettier \"**/*.ts\" \"**/*.js\"", "prettier:check": "npm run prettier:cli -- -l", "prettier:fix": "npm run prettier:cli -- --write", "eslint": "lb-eslint --report-unused-disable-directives .", "eslint:fix": "npm run eslint -- --fix", "pretest": "npx tsc -p tsconfig.test.json || true", "test": "NODE_PATH=./test/stubs node --test test/*.test.js", "test:client": "vitest", "test:dev": "lb-mocha --allow-console-logs dist/__tests__/**/*.js && npm run posttest", "posttest": "echo 'skip lint'", "docker:build": "docker build -t raleon-webapp .", "docker:run": "docker run -p 80:3030 -d raleon-webapp", "premigrate": "npm run build", "migrate": "node ./dist/migrate", "premigrate-prod": "npm run build", "migrate-prod": "node ./dist/migrate -- --skip", "preopenapi-spec": "npm run build", "openapi-spec": "node ./dist/openapi-spec", "prestart": "npm run export:env && npm run rebuild", "start": "node -r source-map-support/register --inspect .", "start:dev": "npm run prestart && cross-env dev=true node -r source-map-support/register --inspect .", "start:ci": "node --max-old-space-size=5120 -r source-map-support/register . --inspect", "nobuild:start": "node -r source-map-support/register . --inspect", "clean": "lb-clean dist *.tsbuildinfo .eslintcache && lb-clean public/dist", "rebuild": "npm run clean && npm run build", "dev:ui": "vite", "build:ui": "lb-clean public/dist && vite build", "build:watchui": "vite build --watch", "export:env": "node scripts/export-environment"}, "repository": {"type": "git", "url": ""}, "author": " <<EMAIL>>", "license": "", "files": ["README.md", "dist", "src", "!*/__tests__"], "dependencies": {"@alch/alchemy-web3": "^1.4.7", "@heroicons/vue": "^2.2.0", "@hubspot/api-client": "^9.1.1", "@loopback/authentication": "^9.0.1", "@loopback/authentication-jwt": "^0.12.1", "@loopback/authorization": "^0.12.1", "@loopback/boot": "^5.0.1", "@loopback/core": "^4.0.1", "@loopback/repository": "^5.0.1", "@loopback/rest": "^12.0.1", "@loopback/rest-crud": "^0.15.2", "@loopback/rest-explorer": "^5.0.1", "@loopback/security": "^0.8.1", "@loopback/service-proxy": "^5.0.1", "@popperjs/core": "^2.11.6", "@tailwindcss/forms": "^0.5.2", "@tiptap/extension-highlight": "^2.11.7", "@tiptap/pm": "^2.11.7", "@tiptap/starter-kit": "^2.11.7", "@tiptap/vue-3": "^2.11.7", "@types/marked": "^5.0.2", "@types/node-fetch": "^2.6.2", "@types/passport-http": "^0.3.9", "@vuelidate/core": "^2.0.0", "@vuelidate/validators": "^2.0.0", "aws-sdk": "^2.1175.0", "aws4": "^1.12.0", "axios": "^0.27.2", "canvas": "^2.11.2", "chart.js": "^3.8.0", "chartjs-adapter-moment": "^1.0.0", "cheerio": "^1.0.0-rc.12", "color": "^4.2.3", "crisp-sdk-web": "^1.0.25", "cron": "^4.3.1", "crypto-js": "^4.1.1", "csv-parser": "^3.0.0", "d3": "^7.7.0", "d3-sankey": "^0.12.3", "dotenv": "^16.0.1", "eth-sig-util": "^3.0.1", "ethers": "^5.7.2", "express": "^4.18.1", "flatpickr": "^4.6.9", "fuse.js": "^7.0.0", "google-auth-library": "^9.0.0", "got": "^12.0.4", "highcharts": "^11.1.0", "highcharts-vue": "^1.4.3", "image-q": "^4.0.0", "isemail": "^3.2.0", "jsonrepair": "^3.12.0", "kmeansjs": "^0.0.3", "leader-line-new": "^1.1.9", "lightvue": "^1.5.1", "loopback-component-oauth2": "^3.2.0", "loopback-connector-postgresql": "^5.5.0", "lottie-web": "^5.9.6", "lottie-web-vue": "^2.0.6", "marked": "^15.0.8", "memcached": "^2.2.2", "mitt": "^3.0.1", "moment": "^2.29.4", "moment-timezone": "^0.5.45", "multer": "^1.4.5-lts.1", "node-cache": "^5.1.2", "node-fetch": "^2.6.7", "openai": "^4.96.2", "papaparse": "^5.4.1", "passport-http": "^0.3.0", "passport-local": "^1.0.0", "pg": "^8.11.3", "pinia": "^2.0.32", "sharp": "0.32.3", "stripe": "^18.1.0", "tiptap-markdown": "^0.8.10", "toobusy-js": "^0.5.1", "ts-node": "^10.8.1", "tslib": "^2.0.0", "tslint": "^6.1.3", "typescript": "~4.7.3", "underscore": "^1.13.4", "unsplash-js": "^7.0.19", "url-metadata": "^3.3.0", "uuid": "^8.3.2", "uuidv4": "^6.2.13", "v8": "^0.1.0", "vue": "^3.2.31", "vue-cli-service": "^5.0.10", "vue-debounce": "^4.0.0", "vue-email-editor": "^2.1.5", "vue-flatpickr-component": "^9.0.5", "vue-router": "^4.0.16", "web3": "^1.9.0", "winston": "^3.11.0", "winston-cloudwatch": "^6.2.0"}, "optionalDependencies": {"esbuild-darwin-64": "^0.14.49"}, "devDependencies": {"@faker-js/faker": "^9.4.0", "@loopback/build": "^9.0.1", "@loopback/cli": "^6.1.1", "@loopback/eslint-config": "^13.0.1", "@loopback/testlab": "^5.0.1", "@tailwindcss/typography": "^0.5.16", "@types/aws4": "^1.11.2", "@types/cron": "^2.0.1", "@types/memcached": "^2.2.10", "@types/multer": "^1.4.7", "@types/node": "^18.0.0", "@types/passport-oauth2-client-password": "^0.1.2", "@types/pg": "^8.10.2", "@types/uuid": "^8.3.4", "@types/vue": "^1.0.31", "@types/winston": "^2.4.4", "@vitejs/plugin-vue": "^2.3.3", "@vue/compiler-sfc": "^3.2.37", "@vue/test-utils": "^2.4.6", "ajv": "^7.2.4", "autoprefixer": "^10.3.7", "cross-env": "^7.0.3", "cypress": "^12.5.1", "cypress-slow-down": "^1.2.1", "eslint": "^8.17.0", "happy-dom": "^15.7.4", "hardhat": "^2.13.0", "postcss": "^8.4.14", "prompt": "^1.3.0", "sass": "1.58.3", "source-map-support": "^0.5.21", "tailwindcss": "^3.1.4", "typescript": "~4.7.3", "vite": "^2.9.13", "vitest": "^2.1.2"}, "overrides": {"types-ramda": "0.29.4"}}