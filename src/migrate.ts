import {RaleonWebappApplication} from './application';
import {DatabaseManualMigration} from './database-manual-migration';
import {DevDbDataSource} from './datasources';
const prompt = require('prompt');

export async function migrate(args: string[]) {
	const existingSchema = args.includes('--rebuild') ? 'drop' : 'alter';
	const shouldSkip = args.includes('--skip');
	console.log('Migrating schemas (%s existing schema)', existingSchema);

	const databaseName = args[2];

	// Use the provided database name if it exists, otherwise use the one from the environment variable
	const database = databaseName || process.env.DATABASE_NAME;

	if (!shouldSkip) {
		prompt.start();
		const result = await prompt.get({
			properties: {
				confirm: {
					description: `You are about to migrate ${database}. Are you sure? (y/N)`,
					type: 'string',
					pattern: /^[yYnN]$/,
					message: 'Please enter y or n',
					required: true,
				},
			}
		});

		console.log(result);

		if (result.confirm !== 'y' && result.confirm !== 'Y') {
			console.log('Migration aborted');
			return;
		}
	}

	const app = new RaleonWebappApplication();
	await app.boot();

	const ds = await app.get('datasources.dev_db') as DevDbDataSource;

	const manualMigrations = new DatabaseManualMigration();
	await manualMigrations.executeDropConstraints(ds);

	await app.migrateSchema({existingSchema});

	await manualMigrations.executeManualMigrations(ds);

	await ds.disconnect();
	process.exit(0);
}

migrate(process.argv).catch(err => {
	console.error('Cannot migrate database schema', err);
	process.exit(1);
});
