import {lifeCycleObserver} from '@loopback/core';
import {MetricSegment} from '../models';
import {MetricSegmentRepository} from '../repositories';
import {ALifeCycleObserver} from './a-lifecycle-observer';
import {repository} from '@loopback/repository';

@lifeCycleObserver('MetricSegment')
export class MetricSegmentObserver extends ALifeCycleObserver {
  constructor(
	@repository(MetricSegmentRepository)
	private metricSegmentRepository: MetricSegmentRepository
  ) {
    super();
  }

  /**
   * This method will be invoked when the application starts.
   */
  async start(): Promise<void> {
	if (!this.shouldRun()) return;

    await this.populateMetricSegments();
  }

  /**
   * Populate the Plan model with predefined metric segments.
   */
  private async populateMetricSegments(): Promise<void> {
    const metricSegmentData: Partial<MetricSegment>[] = [
	{ segmentKey: "at-risk-engaged-customers", name: "At Risk Engaged", type: "raleon", isSignal: true, isDefault: true, description: "Customers that have engaged with your site recently, but still have a high probability of churning based on Raleon's churn prediction model.", query:`{ "operator": "and", "conditions": [ { "field": "churnRisk", "operator": ">", "value": "500" }, { "field": "engagement30days", "operator": ">", "value": "0" } ] }` },
	{ segmentKey: "replenishment-desire", name: "Ready to Replenish", type: "raleon", isSignal: true, isDefault: true, description: "Customers that are likely to rebuy the same product in the next ~30 days (product specific).", query:`{ "operator": "and", "conditions": [ { "field": "replenishmentScore", "operator": ">", "value": "500" }, { "field": "daysSinceLastSubscription", "operator": "IS NOT", "value": "NULL" } ] }` },
	{ segmentKey: "has-had-subscriptions", name: "Has Subscription History", type: "raleon", isSignal: true, isDefault: true, description: "Customers that have had a subscription at some point.", query:`{ "conditions": [ { "field": "daysSinceLastSubscription", "operator": ">", "value": "0" } ] }` },
	{ segmentKey: "subscription-downsell", name: "Subscription Downsell", type: "model", isSignal: false, description: "", query:`{ "conditions": [ { "field": "loyaltySegment", "operator": "=", "value": "Very Loyal" } ] }` },
	{ segmentKey: "likely-for-cross-sell", name: "Likely for cross-sell", type: "model", isSignal: false, description: "", query:`{ "conditions": [ { "field": "loyaltySegment", "operator": "=", "value": "Very Loyal" } ] }` },
	{ segmentKey: "email-engaged", name: "Email Engaged", type: "rule", isSignal: false, description: "", query:`{ "conditions": [ { "field": "loyaltySegment", "operator": "=", "value": "Very Loyal" } ] }` },
	{ segmentKey: "losing-engagement", name: "Losing engagement", type: "raleon", isSignal: false, description: "", query:`{ "conditions": [ { "field": "loyaltySegment", "operator": "=", "value": "Very Loyal" } ] }` },
	{ segmentKey: "single-category-buyer", name: "Single-category buyer", type: "rule", isSignal: false, description: "", query:`{ "conditions": [ { "field": "loyaltySegment", "operator": "=", "value": "Very Loyal" } ] }` },
	{ segmentKey: "multi-category-buyer", name: "Multi-category buyer", type: "rule", isSignal: false, description: "", query:`{ "conditions": [ { "field": "loyaltySegment", "operator": "=", "value": "Very Loyal" } ] }` },
	{ segmentKey: "discounter-seekers", name: "Promo Responsive", type: "raleon", isSignal: true, isDefault: true, description: "Customers that are less likely to purchase without a discount based on their purchase history. Discount Seekers are included in this segment.", query:`{ "conditions": [ { "field": "discountScore", "operator": ">", "value": "400" } ] }` },
	{ segmentKey: "heavy-discounter", name: "Discount Seekers", type: "raleon", isSignal: true, isDefault: true, description: "Customers that very rarely buy without a discount. These are the most Promo Responsive.", query:`{ "conditions": [ { "field": "discountScore", "operator": ">", "value": "700" } ] }` },
	{ segmentKey: "not-price-sensitive", name: "Not price sensitive", type: "model", isSignal: false, description: "", query:`{ "conditions": [ { "field": "loyaltySegment", "operator": "=", "value": "Very Loyal" } ] }` },
	{ segmentKey: "shipping-sensitive", name: "Shipping sensitive", type: "model", isSignal: false, description: "", query:`{ "conditions": [ { "field": "loyaltySegment", "operator": "=", "value": "Very Loyal" } ] }` },
	{ segmentKey: "seasonal", name: "Seasonal", type: "model", isSignal: false, description: "", query:`{ "conditions": [ { "field": "loyaltySegment", "operator": "=", "value": "Very Loyal" } ] }` },
	{ segmentKey: "returner", name: "Has Returned", type: "model", isSignal: true, isDefault: true, description: "Customers that have a returned at least 1 time.", query:`{ "conditions": [ { "field": "totalRefunds", "operator": ">", "value": "500" } ] }` },
	{ segmentKey: "abandoned-cart-recently", name: "Abandoned Cart Recently", type: "rule", isSignal: true,	isDefault: true, description: "", query:`{ "conditions": [ { "field": "loyaltySegment", "operator": "=", "value": "Very Loyal" } ] }` },
	{ segmentKey: "very-loyal", name: "Very Loyal", type: "raleon", isSignal: true, isDefault: true, description: "Your most loyal customers, based on lifetime value, repeat purchase rate, average order value, and other factors.", query:`{ "conditions": [ { "field": "loyaltySegment", "operator": "=", "value": "Very Loyal" } ] }` },
	{ segmentKey: "upsell-opportunity", name: "Upsell Opportunity", type: "raleon", isSignal: true, isDefault: true, description: "Customers that show potential to be loyal, but aren't quite there yet.", query:`{ "conditions": [ { "field": "loyaltySegment", "operator": "=", "value": "Growth" } ] }` },
	{ segmentKey: "new-users", name: "New Customers", type: "raleon", isSignal: true, isDefault: true, description: "Customers that are newer to the brand and have made no more than one purchase.", query:`{ "conditions": [ { "field": "loyaltySegment", "operator": "=", "value": "New Users" } ] }` },
	{ segmentKey: "winback", name: "Winback Customers", type: "raleon", isSignal: true, isDefault: true, description: "Customers that were previously Very Loyal but recently show higher churn risk (may require up to 90 days after installation to fully train).", query:`{ "conditions": [ { "field": "winbackscore", "operator": ">", "value": "0" } ] }` },
	{ segmentKey: "site-engagement-30", name: "On-Site Engagement (L30)", type: "raleon", isSignal: true, isDefault: true, description: "Customers that have visited your site and signaled high intent to purchase over the last 30 days.", query:`{ "conditions": [ { "field": "engagement30days", "operator": ">", "value": "50" } ] }` },
	{ segmentKey: "site-engagement-60", name: "On-Site Engagement (L60)", type: "raleon", isSignal: true, isDefault: true, description: "Customers that have visited your site and signaled high intent to purchase over the last 60 days.", query:`{ "conditions": [ { "field": "engagement60days", "operator": ">", "value": "50" } ] }` },
	{ segmentKey: "site-engagement-90", name: "On-Site Engagement (L90)", type: "raleon", isSignal: true, isDefault: true, description: "Customers that have visited your site and signaled high intent to purchase over the last 90 days.", query:`{ "conditions": [ { "field": "engagement90days", "operator": ">", "value": "50" } ] }` },
	{ segmentKey: "high-value-customers", name: "High Value Customers", type: "model", isSignal: true, isDefault: true, description: "Customers with the highest CLTV.", query:`{ "conditions": [ { "field": "ltvdistribution", "operator": ">", "value": "700" } ] }` },
	{ segmentKey: "everyone", name: "Everyone", type: "model", isSignal: true, isDefault: true, description: "A simple signal to be able to target everyone.", query:`{ "conditions": [ { "field": "revenue", "operator": ">", "value": "-1" } ] }` },
	{ segmentKey: "refund-prone", name: "Refund Prone", type: "raleon", isSignal: true, isDefault: true, description: "Customers that have a high refund to order rate, and appear to have a higher likelihood to refund again.", query:`{ "conditions": [ { "field": "refundPropensity", "operator": ">", "value": "500" } ] }` },
	{ segmentKey: "one-time-buyers", name: "One Time Buyers", type: "rule", isSignal: true, isDefault: true, description: "Customers that have only made a single purchase.", query:`{ "conditions": [ { "field": "totalOrders", "operator": "=", "value": "1" } ] }` },
	{ segmentKey: "repeat-buyers", name: "Repeat Buyers", type: "model", isSignal: true, isDefault: true, description: "Customers that have purchased 2 or more times recently.", query:`{ "conditions": [ { "field": "totalOrders", "operator": ">", "value": "1" } ] }` },
	{ segmentKey: "likely-for-rebuy-soon", name: "Ready to Buy Again", type: "raleon", isSignal: true, isDefault: true, description: "Customers that are likely to make a repeat purchase from your store (not product specific).", query:`{ "conditions": [ { "field": "rebuyPropensity", "operator": ">", "value": "500" } ] }` },
	{ segmentKey: 'recent-subscriber', name: 'Recent Subscriber', type: 'rule', isSignal: true, isDefault: true, description: 'Customers that have subscribed recently, defaults to 60 days.', query: `{ "conditions": [ { "field": "daysSinceLastSubscription", "operator": "<", "value": "60" } ] }` },
	{ segmentKey: 'clicked-email-30', integrationId:2, name: 'Clicked Email (L30)', type: 'rule', isSignal: true, isDefault: true, description: 'Customers who clicked on a link in an email in the last 30 days.', query: `{ "conditions": [ { "field": "Clicked Email", "operator": ">", "value": "0", "days":30 } ] }` },
	{ segmentKey: 'clicked-email-60', integrationId:2, name: 'Clicked Email (L60)', type: 'rule', isSignal: true, isDefault: true, description: 'Customers who clicked on a link in an email in the last 60 days.', query: `{ "conditions": [ { "field": "Clicked Email", "operator": ">", "value": "0", "days":60 } ] }` },
	{ segmentKey: 'clicked-email-90', integrationId:2, name: 'Clicked Email (L90)', type: 'rule', isSignal: true, isDefault: true, description: 'Customers who clicked on a link in an email in the last 90 days.', query: `{ "conditions": [ { "field": "Clicked Email", "operator": ">", "value": "0", "days":90 } ] }` },
	{ segmentKey: 'bounced-email-30', integrationId:2, name: 'Bounced Email (L30)', type: 'rule', isSignal: true, isDefault: true, description: 'Customers who had a bounced email in the last 30 days.', query: `{ "conditions": [ { "field": "Bounced Email", "operator": ">", "value": "0", "days":30 } ] }` },
	{ segmentKey: 'bounced-email-60', integrationId:2, name: 'Bounced Email (L60)', type: 'rule', isSignal: true, isDefault: true, description: 'Customers who had a bounced email in the last 60 days.', query: `{ "conditions": [ { "field": "Bounced Email", "operator": ">", "value": "0", "days":60 } ] }` },
	{ segmentKey: 'bounced-email-90', integrationId:2, name: 'Bounced Email (L90)', type: 'rule', isSignal: true, isDefault: true, description: 'Customers who had a bounced email in the last 90 days.', query: `{ "conditions": [ { "field": "Bounced Email", "operator": ">", "value": "0", "days":90 } ] }` },
	{ segmentKey: 'opened-email-30', integrationId:2, name: 'Opened Email (L30)', type: 'rule', isSignal: true, isDefault: true, description: 'Customers who opened an email in the last 30 days.', query: `{ "conditions": [ { "field": "Opened Email", "operator": ">", "value": "0", "days":30 } ] }` },
	{ segmentKey: 'opened-email-60', integrationId:2, name: 'Opened Email (L60)', type: 'rule', isSignal: true, isDefault: true, description: 'Customers who opened an email in the last 60 days.', query: `{ "conditions": [ { "field": "Opened Email", "operator": ">", "value": "0", "days":60 } ] }` },
	{ segmentKey: 'opened-email-90', integrationId:2, name: 'Opened Email (L90)', type: 'rule', isSignal: true, isDefault: true, description: 'Customers who opened an email in the last 90 days.', query: `{ "conditions": [ { "field": "Opened Email", "operator": ">", "value": "0", "days":90 } ] }` },
	{ segmentKey: 'clicked-sms-30', integrationId:2, name: 'Clicked SMS (L30)', type: 'rule', isSignal: true, isDefault: true, description: 'Customers clicked an SMS in the last 30 days.', query: `{ "conditions": [ { "field": "Clicked SMS", "operator": ">", "value": "0", "days":30 } ] }` },
	{ segmentKey: 'clicked-sms-60', integrationId:2, name: 'Clicked SMS (L60)', type: 'rule', isSignal: true, isDefault: true, description: 'Customers clicked an SMS in the last 60 days.', query: `{ "conditions": [ { "field": "Clicked SMS", "operator": ">", "value": "0", "days":60 } ] }` },
	{ segmentKey: 'clicked-sms-90', integrationId:2, name: 'Clicked SMS (L90)', type: 'rule', isSignal: true, isDefault: true, description: 'Customers clicked an SMS in the last 90 days.', query: `{ "conditions": [ { "field": "Clicked SMS", "operator": ">", "value": "0", "days":90 } ] }` },
	{ segmentKey: 'can-receive-email-marketing', integrationId:2, name: 'Email Marketing Opt-In', type: 'rule', isSignal: true, isDefault: true, description: 'Customers that can get email marketing', query: `{ "conditions": [ { "field": "can_receive_marketing", "operator": "=", "value": "true" } ] }` },
	{ segmentKey: 'subscription-propensity', name: 'Subscription Upsell', type: 'raleon', isSignal: true, isDefault: true, description: 'Customers that have some characteristics that indicate they might subscribe.', query: `{ "operator": "and", "conditions": [ { "field": "subscriptionpropensity", "operator": ">", "value": "20" }, { "field": "daysSinceLastSubscription", "operator": "IS NOT", "value": "NULL" } ] }` },
	{ segmentKey: 'has-abandoned-checkout', name: 'Has Abandoned Cart', type: 'rule', isSignal: true, isDefault: true, description: 'Customers that currently have an abandoned cart', query: `{ "conditions": [ { "field": "has_abandoned_checkout", "operator": ">", "value": "0" } ] }` },
	{ segmentKey: 'engaged-email-90', integrationId: 2, name: 'Engaged Email (L90)', type: 'rule', isSignal: true, isDefault: true, description: 'Customers who engaged with an email in the last 90 days.', query: `{ "operator": "and", "conditions": [ { "field": "Clicked Email", "operator": ">", "value": "0", "days": 90 }, { "field": "Received Email", "operator": ">", "value": "4", "days": 90 } ] }` },
	// Add more metric segments as needed
    ];

	const existingMetricSegments = await this.metricSegmentRepository.find();
	/*for (const metricSegment of existingMetricSegments) {
		if (metricSegmentData.every((metricSegmentData) => metricSegmentData.segmentKey !== metricSegment.segmentKey)) {
			throw new Error(`Metric segment key mismatch error.

				The metric segment in the database with key:
					${metricSegment.segmentKey} (${metricSegment.name})
				is missing from the source code data.

				This could be caused by either:
				- A manually added metric segment in the database:
					- Add this metric segment to the array
					- Delete the metric segment from the database
						*** WARNING: this will break all segments that reference the old signal ***
				- A metric segment key was changed either in the array, or in the database
					- Change this key back to what it used to be
					- There may be references to this signal that are currently broken because of the key change

			`);
		}
	}*/

    for (const metricSegment of metricSegmentData) {
      let existingMetricSegments = [] as MetricSegment[];
	  try {
		existingMetricSegments = await this.metricSegmentRepository.find({
			where: {
				segmentKey: metricSegment.segmentKey,
			}
		});
	  } catch (e) {
		console.error(e);
	  }

      if (!existingMetricSegments?.length) {
        await this.metricSegmentRepository.create(metricSegment);
        console.log(`Created metric segment: ${metricSegment.name}`);
      } else if (existingMetricSegments?.length > 1) {
		throw new Error(`Multiple metric segments found with key: ${metricSegment.segmentKey}. Manually correct`);
	  } else {
        await this.metricSegmentRepository.updateById(existingMetricSegments[0].id, metricSegment);
        console.log(`Updated metric segment: ${metricSegment.name}`);
      }
    }
  }

  /**
   * This method will be invoked when the application stops.
   */
  async stop(): Promise<void> {
    // Implement any cleanup logic if necessary
    console.log('MetricSegmentObserver has stopped.');
  }
}
