import {
	/* inject, Application, CoreBindings, */
	lifeCycleObserver, // The decorator
	LifeCycleObserver, // The interface
} from '@loopback/core';
import {repository} from '@loopback/repository';
import {OnboardingTaskRepository} from '../repositories';
import {ALifeCycleObserver} from './a-lifecycle-observer';

/**
 * This class will be bound to the application as a `LifeCycleObserver` during
 * `boot`
 */
@lifeCycleObserver('Data')
export class OnboardingTasksObserver extends ALifeCycleObserver {

	constructor(
		@repository(OnboardingTaskRepository) private onboardingTaskRepo: OnboardingTaskRepository,
	) {
		super();
	}


	/**
	 * This method will be invoked when the application initializes. It will be
	 * called at most once for a given application instance.
	 */
	async init(): Promise<void> {
		// Add your logic for init
	}

	/**
	 * This method will be invoked when the application starts.
	 */
	async start(): Promise<void> {
		if (!super.shouldRun()) return;
		// Add your logic for start
		const onboardingTasks = [
			{
				"id": 1,
				"taskName": "1. Customize your Loyalty Sidebar's look and feel",
				"taskDescription": "Raleon Copilot has applied your store branding. Please review and adjust as needed to align with your brand identity. No coding required.",
				"priority": 0,
				"type": "Branding",
				"userAction": "/loyalty/branding?tab=sidebar",
				"taskPendingName": "1. Customize your brand's look and feel",
				"taskPendingDescription": "To save you time, Raleon Copilot is currently learning about your store and applying your brand style to Raleon. This takes about 2 minutes.",
				"taskCta": "Setup"
			},
			{
				"id": 2,
				"taskName": "Member Insights",
				"taskDescription": "Your tailored member insights are ready! Dive into these AI-driven analytics to uncover personalized strategies for customer loyalty. These insights are your tool for deeper customer connection, and used by your co-pilot to create recommendations for you.",
				"priority": 1,
				"type": "Member Insights",
				"userAction": "/analytics/segmentation",
				"taskPendingName": "Gathering Customer Insights",
				"taskPendingDescription": "Your Co-Pilot is currently analyzing your customer data to generate unique insights. This process ensures up-to-date and personalized understanding of your customers, crucial for creating targeted loyalty strategies that drive revenue."
			},
			{
				"id": 3,
				"taskName": "2. Confirm your loyalty program",
				"taskDescription": "Raleon Copilot has created a starter loyalty & reward program for you, tailored to your customers and your products. Take a look and make sure it meets your needs.",
				"priority": 2,
				"type": "Loyalty Program",
				"userAction": "/loyalty/program",
				"taskPendingName": "2. Preview your loyalty program",
				"taskPendingDescription": "Your Copilot is currently crafting a loyalty program specially tailored to your store. By analyzing your customer profiles and product types, we're laying the groundwork for an effective loyalty strategy - no expertise needed on your part.",
				"taskCta": "Review"
			},
			{
				"id": 5,
				"taskName": "3. Preview on your Store",
				"taskDescription": "Preview your branding and loyalty program on your store before you launch.",
				"priority": 3,
				"type": "Preview Program",
				"userAction": "",
				"taskCta": "Preview"
			},
			{
				"id": 4,
				"taskName": "4. Launch on Store",
				"taskDescription": "When you're ready, activate your loyalty program with a single click, making it visible to all your customers. To enhance customer engagement, consider sending an announcement email as well.",
				"priority": 4,
				"type": "Launch Program",
				"userAction": "/settings",
				"taskCta": "Launch"
			}
		];

		for (const onboardingTask of onboardingTasks) {
			const existingTask = await this.onboardingTaskRepo.findOne({
				where: {id: onboardingTask.id},
			});

			if (!existingTask) {
				await this.onboardingTaskRepo.create(onboardingTask);
				console.log(`Created new onboardingTask: ${onboardingTask.taskName}`);
			} else {
				console.log(`onboardingTask ${onboardingTask.taskName} already exists, updating metadata`);
				//Forcing an update on the model
				await this.onboardingTaskRepo.updateById(existingTask.id, onboardingTask);
			}
		}

	}

	/**
	 * This method will be invoked when the application stops.
	 */
	async stop(): Promise<void> {
		// Add your logic for stop
	}
}
