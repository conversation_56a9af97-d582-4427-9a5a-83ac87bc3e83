import {
	lifeCycleObserver,
	LifeCycleObserver,
} from '@loopback/core';
import {repository} from '@loopback/repository';
import {LoyaltyEventRepository} from '../repositories';
import {EventStreamEvent} from '../services/event-stream/loyalty-event-publisher.service';
import {ALifeCycleObserver} from './a-lifecycle-observer';

@lifeCycleObserver('Data')
export class LoyaltyEventObserver extends ALifeCycleObserver {
	constructor(
		@repository(LoyaltyEventRepository) private loyaltyEventRepository: LoyaltyEventRepository,
	) {
		super();
	 }

	async init(): Promise<void> { }

	async start(): Promise<void> {
		if (!super.shouldRun()) return;
		console.log("STARTING EVENT OBSERVER");
		if(process.env.NODE_ENV != 'production') {
			console.log("This is a dev environment")
		}

		let events = [
			{
				id: 1,
				name: "referral_shared",
				friendlyName: "Raleon Referral Shared",
				dataStructure: {
					"friendEmail": {
						type: "string",
						required: false,
					},
				},
				description: 'Sent when a customer shares their referral code with a friend.'
			},
			{
				id: 2,
				name: "referral_completed",
				friendlyName: "Raleon Referral Completed",
				description: 'Sent when a customer refers a friend that completes a purchase.',
			},
			{
				id: 3,
				name: "point_balance_change",
				friendlyName: "Raleon Point Balance Change",
				dataStructure: {
					"balanceChange": {
						type: "number",
						required: true,
					},
					"totalPoints": {
						type: "number",
						required: true,
					},
					"earnName": {
						type: "string",
						required: false,
					}
				},
				description: 'Sent when a customer\'s point balance has increased.' //Raleon Emails are not sent when balance decreases but Klaviyo events are
			},{
				id: 4,
				name: "customer_birthday",
				friendlyName: "Raleon Customer Birthday",
				dataStructure: {
					"birthday": {
						type: "string",
						required: true,
					},
				},
				description: 'Sent when a customer provides their birthday in the Birthday Bonus Way to Earn.'
			},
			{
				id: 5,
				name: "referral_received",
				friendlyName: "Raleon Referral Received",
				dataStructure: {
					"email": {
						type: "string",
						required: true,
					},
					"referralLink": {
						type: "string",
						required: true,
					}
				},
				description: 'Sent when a customer refers a friend by inputting the friend\'s email address into the Raleon Referral Section.'
			},
			{
				id: 6,
				name: "vip_tier_updated",
				friendlyName: "Raleon VIP Tier Updated",
				dataStructure: {
					"vipTier": {
						type: "string",
						required: true,
					},
					"pointsToNextTier": {
						type: "number",
						required: false,
					}
				},
				description: 'Sent whenever a customer enters a higher VIP tier. Does not send when a customer moves down a tier.'
			},
			{
				id: 7,
				name: "offer_received",
				friendlyName: "Raleon Offer Received",
			},
			{
				id: 8,
				name: EventStreamEvent.NEXT_REWARD_UPDATE,
				friendlyName: "Raleon Next Reward Update",
				dataStructure: {
					"nextReward": {
						type: "string",
						required: true,
					},
					"deficit": {
						type: "number",
						required: true,
					},
				}
			},
			{
				id: 9,
				name: EventStreamEvent.JOINED_LOYALTY,
				friendlyName: "Raleon Joined Loyalty",
				description: 'Sent when a customer signs up for loyalty'
			},
			{
				id: 10,
				name: EventStreamEvent.REWARD_EXPIRING,
				friendlyName: "Raleon Reward Expiring",
				description: 'Sent when a customer has a reward expiring within 24 hours or 28 days from today',
				dataStructure: {
					"expirationDate": {
						type: "string",
						required: true,
					},
					"rewardName": {
						type: "string",
						required: true,
					},
					"couponId": {
						type: "string",
						required: true,
					},
				}
			},
			{
				id: 11,
				name: EventStreamEvent.BIRTHDAY_REWARD_GRANTED,
				friendlyName: "Raleon Birthday Reward",
				description: 'Sent on the first day of the month when a customer has been granted their birthday reward',
				dataStructure: {
					"rewardName": {
						type: "string",
						required: true,
					},
					"birthday": {
						type: "string",
						required: true,
					},
					"couponId": {
						type: "string",
						required: false,
					}
				}
			},
			{
				id: 12,
				name: EventStreamEvent.REWARD_GRANTED,
				friendlyName: "Raleon Reward Granted",
				description: 'Sent when a customer has been granted a reward',
				dataStructure: {
					"rewardName": {
						type: "string",
						required: true,
					},
					"expirationDate": {
						type: "string",
						required: true,
					},
					"couponId": {
						type: "string",
						required: true,
					},
				}
			},
		];

		for (const event of events) {
			const existingEvent = await this.loyaltyEventRepository.findOne({
				where: {name: event.name},
			});

			if (!existingEvent) {
				await this.loyaltyEventRepository.create(event);
				console.log(`Created new event: ${event.name}`);
			} else {
				console.log(`Event ${event.name} already exists, updating metadata`);
				await this.loyaltyEventRepository.updateById(existingEvent.id, event);
			}
		}
	}

	async stop(): Promise<void> {}
}
