import {
	/* inject, Application, CoreBindings, */
	lifeCycleObserver, // The decorator
	LifeCycleObserver, // The interface
} from '@loopback/core';
import {repository} from '@loopback/repository';
import {SupportedCurrenciesRepository} from '../repositories';
import {ALifeCycleObserver} from './a-lifecycle-observer';

/**
 * This class will be bound to the application as a `LifeCycleObserver` during
 * `boot`
 */
@lifeCycleObserver('Data')
export class CurrencyObserver extends ALifeCycleObserver {
	constructor(
		@repository(SupportedCurrenciesRepository) private supportedCurrenciesRepository: SupportedCurrenciesRepository,
	) {
		super();
	}


	/**
	 * This method will be invoked when the application initializes. It will be
	 * called at most once for a given application instance.
	 */
	async init(): Promise<void> {
		// Add your logic for init
	}

	/**
	 * This method will be invoked when the application starts.
	 */
	async start(): Promise<void> {
		if (!super.shouldRun()) return;
		if(process.env.NODE_ENV != 'production') {
			console.log("This is a dev environment")
		}

		// Add your logic for start
		let currencies = [
			{"id": 1, "name": "USD", "prefix": "$", "postfix": "", "conversionToUSD": 1.0},
			{"id": 2, "name": "GBP", "prefix": "£", "postfix": "", "conversionToUSD": 1.39},
			{"id": 3, "name": "EUR", "prefix": "€", "postfix": "", "conversionToUSD": 1.21},
			{"name": "AED", "prefix": "د.إ", "postfix": "", "conversionToUSD": 0.27},
			{"name": "AFN", "prefix": "؋", "postfix": "", "conversionToUSD": 0.013},
			{"name": "ALL", "prefix": "L", "postfix": "", "conversionToUSD": 0.0090},
			{"name": "AMD", "prefix": "֏", "postfix": "", "conversionToUSD": 0.0021},
			{"name": "ANG", "prefix": "ƒ", "postfix": "", "conversionToUSD": 0.56},
			{"name": "AOA", "prefix": "Kz", "postfix": "", "conversionToUSD": 0.0015},
			{"name": "ARS", "prefix": "$", "postfix": "", "conversionToUSD": 0.011},
			{"name": "AUD", "prefix": "A$", "postfix": "", "conversionToUSD": 0.72},
			{"name": "AWG", "prefix": "ƒ", "postfix": "", "conversionToUSD": 0.56},
			{"name": "AZN", "prefix": "₼", "postfix": "", "conversionToUSD": 0.59},
			{"name": "BAM", "prefix": "KM", "postfix": "", "conversionToUSD": 0.58},
			{"name": "BBD", "prefix": "$", "postfix": "", "conversionToUSD": 0.50},
			{"name": "BDT", "prefix": "৳", "postfix": "", "conversionToUSD": 0.012},
			{"name": "BGN", "prefix": "лв", "postfix": "", "conversionToUSD": 0.58},
			{"name": "BIF", "prefix": "FBu", "postfix": "", "conversionToUSD": 0.00051},
			{"name": "BMD", "prefix": "$", "postfix": "", "conversionToUSD": 1.0},
			{"name": "BND", "prefix": "$", "postfix": "", "conversionToUSD": 0.74},
			{"name": "BOB", "prefix": "Bs.", "postfix": "", "conversionToUSD": 0.14},
			{"name": "BRL", "prefix": "R$", "postfix": "", "conversionToUSD": 0.19},
			{"name": "BSD", "prefix": "$", "postfix": "", "conversionToUSD": 1.0},
			{"name": "BWP", "prefix": "P", "postfix": "", "conversionToUSD": 0.092},
			{"name": "BZD", "prefix": "BZ$", "postfix": "", "conversionToUSD": 0.50},
			{"name": "CAD", "prefix": "$", "postfix": "", "conversionToUSD": 0.79},
			{"name": "CDF", "prefix": "FC", "postfix": "", "conversionToUSD": 0.00051},
			{"name": "CHF", "prefix": "CHF", "postfix": "", "conversionToUSD": 1.1},
			{"name": "CLP", "prefix": "$", "postfix": "", "conversionToUSD": 0.0013},
			{"name": "CNY", "prefix": "¥", "postfix": "", "conversionToUSD": 0.16},
			{"name": "COP", "prefix": "$", "postfix": "", "conversionToUSD": 0.00026},
			{"name": "CRC", "prefix": "₡", "postfix": "", "conversionToUSD": 0.0016},
			{"name": "CVE", "prefix": "$", "postfix": "", "conversionToUSD": 0.010},
			{"name": "CZK", "prefix": "Kč", "postfix": "", "conversionToUSD": 0.045},
			{"name": "DJF", "prefix": "Fdj", "postfix": "", "conversionToUSD": 0.0056},
			{"name": "DKK", "prefix": "kr", "postfix": "", "conversionToUSD": 0.15},
			{"name": "DOP", "prefix": "RD$", "postfix": "", "conversionToUSD": 0.018},
			{"name": "DZD", "prefix": "دج", "postfix": "", "conversionToUSD": 0.0074},
			{"name": "EGP", "prefix": "£", "postfix": "", "conversionToUSD": 0.064},
			{"name": "ETB", "prefix": "Br", "postfix": "", "conversionToUSD": 0.023},
			{"name": "FJD", "prefix": "$", "postfix": "", "conversionToUSD": 0.48},
			{"name": "FKP", "prefix": "£", "postfix": "", "conversionToUSD": 1.39},
			{"name": "GEL", "prefix": "₾", "postfix": "", "conversionToUSD": 0.32},
			{"name": "GIP", "prefix": "£", "postfix": "", "conversionToUSD": 1.39},
			{"name": "GMD", "prefix": "D", "postfix": "", "conversionToUSD": 0.020},
			{"name": "GNF", "prefix": "FG", "postfix": "", "conversionToUSD": 0.00010},
			{"name": "GTQ", "prefix": "Q", "postfix": "", "conversionToUSD": 0.13},
			{"name": "GYD", "prefix": "$", "postfix": "", "conversionToUSD": 0.0048},
			{"name": "HKD", "prefix": "HK$", "postfix": "", "conversionToUSD": 0.13},
			{"name": "HNL", "prefix": "L", "postfix": "", "conversionToUSD": 0.042},
			{"name": "HTG", "prefix": "G", "postfix": "", "conversionToUSD": 0.010},
			{"name": "HUF", "prefix": "Ft", "postfix": "", "conversionToUSD": 0.0033},
			{"name": "IDR", "prefix": "Rp", "postfix": "", "conversionToUSD": 0.000070},
			{"name": "ILS", "prefix": "₪", "postfix": "", "conversionToUSD": 0.30},
			{"name": "INR", "prefix": "₹", "postfix": "", "conversionToUSD": 0.013},
			{"name": "ISK", "prefix": "kr", "postfix": "", "conversionToUSD": 0.0079},
			{"name": "JMD", "prefix": "$", "postfix": "", "conversionToUSD": 0.0067},
			{"name": "JPY", "prefix": "¥", "postfix": "", "conversionToUSD": 0.0091},
			{"name": "KES", "prefix": "Sh", "postfix": "", "conversionToUSD": 0.0090},
			{"name": "KGS", "prefix": "с", "postfix": "", "conversionToUSD": 0.012},
			{"name": "KHR", "prefix": "៛", "postfix": "", "conversionToUSD": 0.00025},
			{"name": "KMF", "prefix": "CF", "postfix": "", "conversionToUSD": 0.0023},
			{"name": "KRW", "prefix": "₩", "postfix": "", "conversionToUSD": 0.00084},
			{"name": "KYD", "prefix": "$", "postfix": "", "conversionToUSD": 1.20},
			{"name": "KZT", "prefix": "₸", "postfix": "", "conversionToUSD": 0.0023},
			{"name": "LAK", "prefix": "₭", "postfix": "", "conversionToUSD": 0.00011},
			{"name": "LBP", "prefix": "ل.ل", "postfix": "", "conversionToUSD": 0.00066},
			{"name": "LKR", "prefix": "Rs", "postfix": "", "conversionToUSD": 0.0049},
			{"name": "LRD", "prefix": "$", "postfix": "", "conversionToUSD": 0.0052},
			{"name": "LSL", "prefix": "L", "postfix": "", "conversionToUSD": 0.067},
			{"name": "MAD", "prefix": "MAD", "postfix": "", "conversionToUSD": 0.11},
			{"name": "MDL", "prefix": "L", "postfix": "", "conversionToUSD": 0.055},
			{"name": "MGA", "prefix": "Ar", "postfix": "", "conversionToUSD": 0.00025},
			{"name": "MKD", "prefix": "ден", "postfix": "", "conversionToUSD": 0.019},
			{"name": "MMK", "prefix": "K", "postfix": "", "conversionToUSD": 0.00056},
			{"name": "MNT", "prefix": "₮", "postfix": "", "conversionToUSD": 0.00035},
			{"name": "MOP", "prefix": "MOP$", "postfix": "", "conversionToUSD": 0.12},
			{"name": "MUR", "prefix": "₨", "postfix": "", "conversionToUSD": 0.025},
			{"name": "MVR", "prefix": "Rf", "postfix": "", "conversionToUSD": 0.065},
			{"name": "MWK", "prefix": "MK", "postfix": "", "conversionToUSD": 0.0012},
			{"name": "MXN", "prefix": "$", "postfix": "", "conversionToUSD": 0.050},
			{"name": "MYR", "prefix": "RM", "postfix": "", "conversionToUSD": 0.24},
			{"name": "MZN", "prefix": "MT", "postfix": "", "conversionToUSD": 0.016},
			{"name": "NAD", "prefix": "$", "postfix": "", "conversionToUSD": 0.067},
			{"name": "NGN", "prefix": "₦", "postfix": "", "conversionToUSD": 0.0024},
			{"name": "NIO", "prefix": "C$", "postfix": "", "conversionToUSD": 0.029},
			{"name": "NOK", "prefix": "kr", "postfix": "", "conversionToUSD": 0.11},
			{"name": "NPR", "prefix": "रू", "postfix": "", "conversionToUSD": 0.0083},
			{"name": "NZD", "prefix": "$", "postfix": "", "conversionToUSD": 0.70},
			{"name": "PAB", "prefix": "B/.", "postfix": "", "conversionToUSD": 1.0},
			{"name": "PEN", "prefix": "S/.", "postfix": "", "conversionToUSD": 0.27},
			{"name": "PGK", "prefix": "K", "postfix": "", "conversionToUSD": 0.28},
			{"name": "PHP", "prefix": "₱", "postfix": "", "conversionToUSD": 0.020},
			{"name": "PKR", "prefix": "₨", "postfix": "", "conversionToUSD": 0.0060},
			{"name": "PLN", "prefix": "zł", "postfix": "", "conversionToUSD": 0.26},
			{"name": "PYG", "prefix": "₲", "postfix": "", "conversionToUSD": 0.00014},
			{"name": "QAR", "prefix": "ر.ق", "postfix": "", "conversionToUSD": 0.27},
			{"name": "RON", "prefix": "lei", "postfix": "", "conversionToUSD": 0.23},
			{"name": "RSD", "prefix": "дин", "postfix": "", "conversionToUSD": 0.010},
			{"name": "RUB", "prefix": "₽", "postfix": "", "conversionToUSD": 0.013},
			{"name": "RWF", "prefix": "FRw", "postfix": "", "conversionToUSD": 0.0010},
			{"name": "SAR", "prefix": "ر.س", "postfix": "", "conversionToUSD": 0.27},
			{"name": "SBD", "prefix": "$", "postfix": "", "conversionToUSD": 0.12},
			{"name": "SCR", "prefix": "₨", "postfix": "", "conversionToUSD": 0.074},
			{"name": "SEK", "prefix": "kr", "postfix": "", "conversionToUSD": 0.11},
			{"name": "SGD", "prefix": "S$", "postfix": "", "conversionToUSD": 0.74},
			{"name": "SHP", "prefix": "£", "postfix": "", "conversionToUSD": 1.39},
			{"name": "SLL", "prefix": "Le", "postfix": "", "conversionToUSD": 0.00010},
			{"name": "SRD", "prefix": "$", "postfix": "", "conversionToUSD": 0.071},
			{"name": "STD", "prefix": "Db", "postfix": "", "conversionToUSD": 0.000048},
			{"name": "SZL", "prefix": "E", "postfix": "", "conversionToUSD": 0.067},
			{"name": "THB", "prefix": "฿", "postfix": "", "conversionToUSD": 0.030},
			{"name": "TJS", "prefix": "ЅМ", "postfix": "", "conversionToUSD": 0.088},
			{"name": "TOP", "prefix": "T$", "postfix": "", "conversionToUSD": 0.44},
			{"name": "TRY", "prefix": "₺", "postfix": "", "conversionToUSD": 0.12},
			{"name": "TTD", "prefix": "TT$", "postfix": "", "conversionToUSD": 0.15},
			{"name": "TWD", "prefix": "NT$", "postfix": "", "conversionToUSD": 0.036},
			{"name": "TZS", "prefix": "Sh", "postfix": "", "conversionToUSD": 0.00043},
			{"name": "UAH", "prefix": "₴", "postfix": "", "conversionToUSD": 0.036},
			{"name": "UGX", "prefix": "USh", "postfix": "", "conversionToUSD": 0.00027},
			{"name": "UYU", "prefix": "$", "postfix": "", "conversionToUSD": 0.023},
			{"name": "UZS", "prefix": "лв", "postfix": "", "conversionToUSD": 0.000095},
			{"name": "VND", "prefix": "₫", "postfix": "", "conversionToUSD": 0.000043},
			{"name": "VUV", "prefix": "Vt", "postfix": "", "conversionToUSD": 0.0089},
			{"name": "WST", "prefix": "WS$", "postfix": "", "conversionToUSD": 0.39},
			{"name": "XAF", "prefix": "FCFA", "postfix": "", "conversionToUSD": 0.0017},
			{"name": "XCD", "prefix": "$", "postfix": "", "conversionToUSD": 0.37},
			{"name": "XOF", "prefix": "CFA", "postfix": "", "conversionToUSD": 0.0017},
			{"name": "XPF", "prefix": "₣", "postfix": "", "conversionToUSD": 0.0094},
			{"name": "YER", "prefix": "﷼", "postfix": "", "conversionToUSD": 0.0040},
			{"name": "ZAR", "prefix": "R", "postfix": "", "conversionToUSD": 0.067},
			{"name": "ZMW", "prefix": "ZK", "postfix": "", "conversionToUSD": 0.056}
		]

		//Set id on each currency
		currencies = currencies.map((currency, index) => {
			currency.id = index + 1;
			return currency;
		});
		// Get all existing currencies in one query
		const existingCurrencies = await this.supportedCurrenciesRepository.find();
		const existingCurrenciesMap = new Map(existingCurrencies.map(c => [c.id, c]));

		for (const currency of currencies) {
			const existingCurrency = existingCurrenciesMap.get(currency.id);

			if (!existingCurrency) {
				await this.supportedCurrenciesRepository.create(currency);
				console.log(`Created new currency: ${currency.name}`);
			} else {
				console.log(`Currency ${currency.name} already exists, updating metadata`);
				//Forcing an update on the model
				await this.supportedCurrenciesRepository.updateById(existingCurrency.id, currency);
			}
		}
	}

	/**
	 * This method will be invoked when the application stops.
	 */
	async stop(): Promise<void> {
		// Add your logic for stop
	}
}
