import {
	lifeCycleObserver,
	LifeCycleObserver,
  } from '@loopback/core';
  import {repository} from '@loopback/repository';
  import {
	OrganizationPlanRepository,
	PlanFeatureRepository,
	MetricRepository,
	OrganizationMetricRepository
  } from '../repositories';
  import {ALifeCycleObserver} from './a-lifecycle-observer';
import {Metric} from '../models';

  @lifeCycleObserver('Data')
  export class OrganizationMetricsObserver extends ALifeCycleObserver {
	constructor(
	  @repository(OrganizationPlanRepository)
	  private organizationPlanRepository: OrganizationPlanRepository,
	  @repository(PlanFeatureRepository)
	  private planFeatureRepository: PlanFeatureRepository,
	  @repository(MetricRepository)
	  private metricRepository: MetricRepository,
	  @repository(OrganizationMetricRepository)
	  private organizationMetricRepository: OrganizationMetricRepository,
	) {
	  super();
	}

	async init(): Promise<void> {
	  // Add your logic for init
	}

	async start(): Promise<void> {
	  if (!super.shouldRun()) return;
	  console.log("STARTING ORGANIZATION METRICS OBSERVER");

	  // Get all active organization plans
	  const organizationPlans = await this.organizationPlanRepository.find({
		where: {status: 'ACTIVE'},
		include: [{relation: 'plan'}],
	  });

	  for (const orgPlan of organizationPlans) {
		try {
		  await this.updateOrganizationMetrics(orgPlan);
		} catch (error) {
		  console.error(`Error updating metrics for organization ${orgPlan.orgId}:`, error);
		}
	  }
	}

	private async updateOrganizationMetrics(organizationPlan: any): Promise<void> {
	  const orgId = organizationPlan.orgId;
	  const plan = organizationPlan.plan;

	  if (!plan) {
		console.warn(
		  `No active plan found for organization ID: ${orgId}. Only deleting default metrics.`,
		);

		await this.organizationMetricRepository.deleteAll({
		  orgId,
		  and: [
			{ metricId: { neq: 44 } },
		  ]
		});
		return;
	  }

	  // Fetch plan features and their associated features
	  const planFeatures = await this.planFeatureRepository.find({
		where: {
		  planId: plan.id,
		  enabled: true
		},
		include: [{relation: 'feature'}],
	  });

	  const featureIds = planFeatures.map(pf => pf.featureId);

	  if (featureIds.length === 0) {
		await this.organizationMetricRepository.deleteAll({
		  orgId,
		  and: [
			{ metricId: { neq: 44 } },
		  ]
		});
		return;
	  }

	  // Fetch all default Metrics associated with these features
	  const metrics = await this.metricRepository.find({
		where: {
		  featureId: {inq: featureIds},
		  isDefault: true,
		},
	  });

	  const metricIds = metrics.map(m => m.id!);

	  // Get existing organization metrics
	  const existingOrgMetrics = await this.organizationMetricRepository.find({
		where: {
		  orgId,
		  metricId: {inq: metricIds},
		},
	  });

	  // Create set of existing metric IDs
	  const existingMetricIds = new Set(existingOrgMetrics.map(om => om.metricId));

	  // Find metrics that need to be created
	  const metricsToCreate = metrics.filter(metric => !existingMetricIds.has(metric.id!));

	  // Prepare and create new organization metrics
	  const newOrgMetrics = metricsToCreate.map(metric => ({
		orgId,
		metricId: metric.id!,
		runFrequency: metric.defaultRunFrequency,
	  }));

	  if (newOrgMetrics.length > 0) {
		await this.organizationMetricRepository.createAll(newOrgMetrics);
		console.log(`Created ${newOrgMetrics.length} new metrics for organization ${orgId}`);
	  }
	  const customMetrics = await this.metricRepository.find({
		where: {
		  isDefault: false,
		},
	  });
	  const customMetricIds = customMetrics.map(m => m.id!);
	  metricIds.push(...customMetricIds);
	  // Delete only default metrics that are no longer associated with the plan
	  // Preserve metric 44 and non-default metrics
	  await this.organizationMetricRepository.deleteAll({
		orgId,
		and: [
		  { metricId: { nin: metricIds } }, // Not in current plan's metrics
		  { metricId: { neq: 44 } },
		]
	  });
	}

	async stop(): Promise<void> {
	  // Add your logic for stop
	}
  }
