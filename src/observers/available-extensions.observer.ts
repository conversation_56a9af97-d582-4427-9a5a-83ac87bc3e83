import {
	/* inject, Application, CoreBindings, */
	lifeCycleObserver, // The decorator
	LifeCycleObserver, // The interface
} from '@loopback/core';
import {repository} from '@loopback/repository';
import {AvailableExtensionsRepository, OnboardingTaskRepository} from '../repositories';
import {ALifeCycleObserver} from './a-lifecycle-observer';

/**
 * This class will be bound to the application as a `LifeCycleObserver` during
 * `boot`
 */
@lifeCycleObserver('Data')
export class AvailableExtensions extends ALifeCycleObserver {

	constructor(
		@repository(AvailableExtensionsRepository) private availableExtensionsRepo: AvailableExtensionsRepository,
	) {
		super();
	}


	/**
	 * This method will be invoked when the application initializes. It will be
	 * called at most once for a given application instance.
	 */
	async init(): Promise<void> {
		// Add your logic for init
	}

	/**
	 * This method will be invoked when the application starts.
	 */
	async start(): Promise<void> {
		if (!super.shouldRun()) return;
		// Add your logic for start
		const isDev = process.env.NODE_ENV != 'production';
		const availableExtensions = [
			{
				"id": 1,
				"name": "VIP Shipping (Free Shipping Delivery Customization)",
				"description": "Customize your delivery options for free shipping",
				"endpoint": "/extensions/vip-shipping",
				"functionId": isDev ? "e5a487d9-8d65-4ec5-af69-4c43246d3032" : "18457341-f65a-4ce7-98fd-ad70c89870bd"
			},
			{
				"id": 2,
				"name": "Free Gift with Purchase",
				"description": "Choose a product to gift your customers.",
				"endpoint": "/extensions/free-gift",
				"functionId": isDev ? "9c11dfb1-7608-4fed-bf85-51e596719905" : "9887b483-13d2-4e7b-835d-e78ce27d713f"
			}
		];

		for (const availableExtension of availableExtensions) {
			const existingTask = await this.availableExtensionsRepo.findOne({
				where: {id: availableExtension.id},
			});

			if (!existingTask) {
				await this.availableExtensionsRepo.create(availableExtension);
				console.log(`Created new availableExtension: ${availableExtension.name}`);
			} else {
				console.log(`availableExtension ${availableExtension.name} already exists, updating metadata`);
				//Forcing an update on the model
				await this.availableExtensionsRepo.updateById(existingTask.id, availableExtension);
			}
		}

	}

	/**
	 * This method will be invoked when the application stops.
	 */
	async stop(): Promise<void> {
		// Add your logic for stop
	}
}
