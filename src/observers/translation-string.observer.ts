import {
	/* inject, Application, CoreBindings, */
	lifeCycleObserver, // The decorator
	LifeCycleObserver,
	service, // The interface
} from '@loopback/core';
import {repository} from '@loopback/repository';
import {TranslationStringRepository} from '../repositories';
import {TranslationString} from '../models'; // Import the TranslationStringModel interface
import {TranslationService} from '../services';
import {ALifeCycleObserver} from './a-lifecycle-observer';

/**
 * This class will be bound to the application as a `LifeCycleObserver` during
 * `boot`
 */
@lifeCycleObserver('Data')
export class TranslationStringsObserver extends ALifeCycleObserver {

	constructor(
		@repository(TranslationStringRepository) private translationStringRepo: TranslationStringRepository,
		@service(TranslationService) private translationService: TranslationService
	) {
		super();
	}


	/**
	 * This method will be invoked when the application initializes. It will be
	 * called at most once for a given application instance.
	 */
	async init(): Promise<void> {
		// Add your logic for init
	}

	/**
	 * This method will be invoked when the application starts.
	 */
	async start(): Promise<void> {

		// Add your logic for start
		const translations: Array<Partial<TranslationString>> = [
			// English - Static
			{language: "en", key: "join", value: "Join"},
			{language: "en", key: "sign_in", value: "Sign-In"},
			{language: "en", key: "loyalty_by_raleon_link", value: "Loyalty ✨ by {{link}}"},
			{language: "en", key: "home_available_points", value: "available {{pointsName}}"},
			{language: "en", key: "earn_summary_ways_to_earn", value: "Ways to Earn"},
			{language: "en", key: "referral_bonus_reward_description", value: `Get {{value}} off your next purchase when you refer a friend`},
			{language: "en", key: "referral_bonus_reward_points_description", value: `Get {{value}} when you refer a friend`},
			{language: "en", key: "inventory_summary_no_rewards", value: "You don't have any rewards yet."},
			{language: "en", key: "points", value: "Points"},
			{language: "en", key: "points_abbreviated", value: "pts"},
			{language: "en", key: "rewards", value: "Rewards"},
			{language: "en", key: "redeem_rewards", value: "Redeem Rewards"},
			{language: "en", key: "see_all", value: "See All"},
			{language: "en", key: "copy_referral_link", value: "Copy Unique Referral Link"},
			{language: "en", key: "copied_referral_link", value: "Copied to Clipboard"},
			{language: "en", key: "referral_socials", value: "Or share the link via social media:"},
			{language: "en", key: "full_inventory_my_inventory", value: "My Inventory"},
			{language: "en", key: "reward_detail_minimum_order_total", value: "Minimum order size of {{minOrderTotal}}."},
			{language: "en", key: "reward_detail_claim_count_current", value: "You've claimed {{redeemed}} / {{maxUserRedemptions}}."},
			{language: "en", key: "reward_detail_expiration_summary", value: "Expires {{expiresInDays}} days after claimed."},
			{language: "en", key: "reward_detail_your_next_purchase", value: "your next purchase"},
			{language: "en", key: "reward_detail_insufficient_points", value: "Insufficient {{pointsName}}"},
			{language: "en", key: "reward_detail_claim_button_alt", value: "Claim and Apply"},
			{language: "en", key: "youve_got_rewards", value: "You've got rewards!"},
			{language: "en", key: "youve_got_referral_rewards", value: "Your friend sent you a referral reward, click here or sign up to claim"},
			{language: "en", key: "rewards_unlocked", value: "Rewards Unlocked!"},
			{language: "en", key: "apply_to_cart", value: "Apply to Cart"},
			{language: "en", key: "applied_to_cart", value: "Already In Cart"},
			{language: "en", key: "apply_to_cart_description", value: `Clicking "Apply to Cart" below will apply the {{itemName}} coupon to your cart.`},
			{language: "en", key: "on_next_purchase", value: "on your next purchase."},
			{language: "en", key: "spend_minimum", value: "You must spend at least {{minOrderTotal}} to use this reward"},
			{language: "en", key: "choose_product_from_collection", value: "Choose product from {{collection}} Collection"},
			{language: "en", key: "choose_free_product", value: "Choose your Free Product"},
			{language: "en", key: "choose_dollar_off_product", value: "Apply and View Product"},
			{language: "en", key: "choose_percent_off_product", value: "Apply and View Product"},
			{language: "en", key: "coupon_expiration", value: "This expires in {{timeValue}} {{timeLabel}}."},
			{language: "en", key: "vip-tiers-title", value: 'Membership Tiers'},
			{language: "en", key: "points-ttm", value: '{{pointsName}}'},
			{language: "en", key: "perks_title", value: 'Perks'},
			{language: "en", key: "points_to_next_tier", value: '{{pointsName}} to next tier'},
			{language: "en", key: "learn_more_about_tiers", value: 'Learn about tiers'},
			{language: "en", key: "referral_sign_in", value: "Sign In To Refer A Friend"},
			{language: "en", key: "email_placeholder_referral", value: "Your Friend's Email"},
			{language: "en", key: "referral_send", value: "Send Invite"},
			{language: "en", key: "referral_sent", value: "Invite Sent"},
			{language: "en", key: "coupon_waiting", value: "Sign in to claim your offer. Click here to join."},
			{language: "en", key: "coupon_added_to_cart", value: "Reward added to cart"},
			{language: "en", key: "coupon", value: "coupon"},

			// English - "Generated"
			{language: "en", key: "reward_pill_points", value: "{{value}} {{pointsAbbreviated}}"},
			{language: "en", key: "reward_pill_points_per_dollar", value: "{{value}} {{pointsAbbreviated}}/{{prefix}}{{postfix}}"},
			{language: "en", key: "reward_pill_dollars_off_coupon", value: "{{prefix}}{{value}} off"},
			{language: "en", key: "reward_pill_percent_discount", value: "{{value}}% off"},
			{language: "en", key: "reward_pill_free_shipping", value: "Free Shipping"},
			{language: "en", key: "reward_pill_free_product", value: "Free Product"},
			{language: "en", key: "reward_pill_percent_off_product", value: "% Off Product"},
			{language: "en", key: "reward_pill_dollar_off_product", value: "Dollar Off Product"},
			{language: "en", key: "reward_pill_giveaway_entry", value: "1 Giveaway Entry"},
			{language: "en", key: "reward_pill_giveaway_entry_multiple", value: "{{count}} Giveaway Entries"},
			{language: "en", key: "reward_points_per_dollar", value: "Earn {{points}} {{pointsName}} for every {{dollarName}} spent"},
			{language: "en", key: "reward_points", value: "Earn {{points}} {{pointsName}}"},
			{language: "en", key: "reward_dollars_off_coupon", value: "Earn a {{prefix}}{{postfix}}{{amount}} off coupon"},
			{language: "en", key: "reward_percent_discount", value: "Earn a {{amount}}% off coupon"},
			{language: "en", key: "reward_product_percent_discount", value: "Earn a {{amount}}% off coupon for a specific product"},
			{language: "en", key: "reward_product_dollar_discount", value: "Earn a {{prefix}}{{postfix}}{{amount}} off coupon for a specific product"},
			{language: "en", key: "show_discount_code", value: "Show Discount Code"},
			{language: "en", key: "condition_click_to_redeem", value: `Click to Enter`},
			{language: "en", key: "condition_auto_redeem", value: `You're automatically entered!`},
			{language: "en", key: "condition_successfully_redeemed", value: `You've been entered into the giveaway!`},
			{language: "en", key: "condition_dollar_spent", value: `Make a Purchase to Earn {{pointsName}} for every {{currency_value:1}} spent`},
			{language: "en", key: "condition_welcome_bonus", value: `Earn Rewards when you join`},
			{language: "en", key: "condition_product_review", value: `Leave A Review To Earn A Reward`},
			{language: "en", key: "condition_product_photo_review", value: `Add a Photo to a Review To Earn a Reward`},
			{language: "en", key: "condition_birthday_bonus", value: `Earn Rewards on your Birthday`},
			{language: "en", key: "enter_birthday_text", value: `Enter your Birthday`},
			{language: "en", key: "condition_nth_purchase", value: `Make {{counts}} Purchases`},
			{language: "en", key: "condition_nth_purchase_single", value: `Make {{counts}} Purchase`},
			{language: "en", key: "condition_nth_purchase_progress", value: `Make {{counts}} more purchases`},
			{language: "en", key: "condition_nth_purchase_progress_single", value: `Make {{counts}} more purchase`},
			{language: "en", key: "condition_nth_purchase_progress_complete", value: `Target reached`},
			{language: "en", key: "condition_timed_purchase", value: `Make {{orderCounts}} Purchases in {{daysUntilEnd}} days`},
			{language: "en", key: "condition_timed_purchase_single", value: `Make {{orderCounts}} Purchase in {{daysUntilEnd}} days`},
			{language: "en", key: "condition_follow_on_instagram", value: `<a href="https://www.instagram.com/{{handle}}" target="_blank" class="raleon-social-wte">Follow @{{handle}} on Instagram</a>`},
			{language: "en", key: "condition_follow_on_tiktok", value: `<a href="https://www.tiktok.com/{{handle}}" target="_blank" class="raleon-social-wte">Follow {{handle}} on TikTok</a>`},
			{language: "en", key: "condition_follow_on_facebook", value: `<a href="https://www.facebook.com/{{handle}}" target="_blank" class="raleon-social-wte">Like {{handle}} on Facebook</a>`},
			{language: "en", key: "condition_follow_facebook_group", value: `<a href="https://www.facebook.com/groups/{{handle}}" target="_blank" class="raleon-social-wte">Join {{handle}} on Facebook</a>`},
			{language: "en", key: "condition_follow_on_youtube", value: `<a href="https://www.youtube.com/{{handle}}" target="_blank" class="raleon-social-wte">Subscribe to {{handle}} on YouTube</a>`},
			{language: "en", key: "condition_follow_on_custom", value: `<a href="{{url}}" target="_blank" class="raleon-social-wte">Follow Us</a>`},
			{language: "en", key: "condition_short_nth_purchase", value: `{{count}} Purchase`},
			{language: "en", key: "condition_short_follow_on_instagram", value: `Follow @{{handle}}`},
			{language: "en", key: "condition_short_follow_on_tiktok", value: `Follow {{handle}}`},
			{language: "en", key: "condition_short_follow_on_facebook", value: `Like {{handle}}`},
			{language: "en", key: "condition_short_follow_on_youtube", value: `Subscribe to {{handle}}`},
			{language: "en", key: "condition_short_follow_on_custom", value: `Follow Us`},
			{language: "en", key: "condition_specific_product_purchase", value: `Buy Specific <a href="https://{{domain}}/products/{{handle}}" class="raleon-social-wte">Product</a>`},
			{language: "en", key: "condition_collection_purchase", value: `Purchase {{count}} from <a href="https://{{domain}}/collections/{{handle}}" class="raleon-social-wte">Collection</a>`},
			{language: "en", key: "condition_specific_product_short_purchase", value: `Buy Product`},
			{language: "en", key: "condition_collection_short_purchase", value: `{{count}} from Collection`},
			{language: "en", key: "condition_referrer_bonus", value: `Refer a friend`},
			{language: "en", key: "condition_subscription_purchase", value: `Make a Subscription Purchase`},
			{language: "en", key: "condition_subscription_purchase_multiple", value: `Make {{value}} Subscription Purchases`},
			{language: "en", key: "free_product_from_collection_name", value: `Free product from the {{value}} Collection`},
			{language: "en", key: "referral_bonus_reward_name", value: `{{value}} off`},
			{language: "en", key: "points_ttm_earned", value: `{{points}} {{pointsName}} earned`},
			{language: "en", key: "subview_loyalty", value: "Loyalty"},
			{language: "en", key: "subview_vip", value: "VIP Tiers"},
			{language: "en", key: "subview_history", value: "History"},
			{language: "en", key: "subview_my_rewards", value: "My Rewards"},
			{language: "en", key: "checkout_ui_points_header", value: "You have {{points}} {{points_name}}"},
			{language: "en", key: "checkout_ui_login_link_prefix", value: "Don't miss out!"},
			{language: "en", key: "checkout_ui_login_link_text", value: "Log in"},
			{language: "en", key: "checkout_ui_login_link_suffix", value: " to earn and redeem rewards"},
			{language: "en", key: "checkout_ui_not_enough_points_warning", value: "You need {{points}} more {{points_name}} to redeem a reward"},
			{language: "en", key: "checkout_ui_rewards_dropdown_label", value: "Rewards"},
			{language: "en", key: "checkout_ui_rewards_dropdown_placeholder", value: "Select a reward"},
			{language: "en", key: "checkout_ui_rewards_dropdown_item_text", value: "{{reward_name}} - {{points}} {{points_name}}"},
			{language: "en", key: "checkout_ui_apply_reward_button_text", value: "Apply"},
			{language: "en", key: "checkout_ui_rewards_dropdown_item_in_use", value: "{{reward_name}} - In Use"},
			{language: "en", key: "checkout_ui_stackability_replacement_warning_title", value: "Coupons Replaced"},
			{language: "en", key: "checkout_ui_stackability_replacement_warning", value: "One of your coupons doesn't stack. We applied the better coupon for better savings."},
			{language: "en", key: "checkout_ui_stackability_replacement_warning_replacements", value: "What we replaced:"},
			{language: "en", key: "checkout_ui_stackability_replacement_warning_undo", value: "Undo"},
			{language: "en", key: "checkout_ui_stackability_error_title", value: "Coupon Not Applied"},
			{language: "en", key: "checkout_ui_stackability_error", value: "One of your coupons doesn't stack."},
			{language: "en", key: "checkout_ui_stackability_error_line_2", value: "Your current coupon offers a better discount. We've kept it for you."},
			{language: "en", key: "checkout_ui_apply_error_title", value: "Coupon Could Not Be Applied"},
			{language: "en", key: "checkout_ui_apply_error", value: "The reward you selected could not be applied. Please try again."},
			{language: "en", key: "product_page_loyalty_banner_text", value: "Loyalty"},
			{language: "en", key: "product_page_guest_earn_teaser", value: "Earn {{points}} {{pointsName}} and more from this item"},
			{language: "en", key: "product_page_guest_join", value: "Join Loyalty"},
			{language: "en", key: "product_page_points_deficit", value: "You need {{points}} more {{pointsName}} to redeem {{rewardName}}"},
			{language: "en", key: "product_page_claim_and_apply_reward", value: "Claim & Apply {{rewardName}}"},
			{language: "en", key: "product_page_apply_reward", value: "Apply your {{rewardName}}"},
			{language: "en", key: "product_page_reward_applied", value: "Applied your {{rewardName}}!"},
			{language: "en", key: "product_page_points_balance", value: "You have {{points}} {{points_name}}"},
			{language: "en", key: "product_page_rewards_dropdown_label", value: "Rewards"},
			{language: "en", key: "product_page_rewards_dropdown_placeholder", value: "Select a reward"},
			{language: "en", key: "product_page_rewards_dropdown_item_text", value: "{{reward_name}} - {{points}} {{points_name}}"},
			{language: "en", key: "product_page_rewards_dropdown_item_in_use", value: "{{reward_name}} - In Use"},
			{language: "en", key: "thankyou_would_have_earned_estimate", value: "You would have earned at least {{estimate}} points on this purchase"},
			{language: "en", key: "thankyou_would_have_earned_exact", value: "You would have earned {{points}} points on this purchase"},
			{language: "en", key: "thankyou_earned_estimate", value: "You earned at least {{estimate}} points on this purchase"},
			{language: "en", key: "thankyou_earned_exact", value: "You earned {{points}} points on this purchase"},
			{language: "en", key: "thankyou_vip_perk", value: "Enjoy your {{vipTier}} Tier Perk, {{vipPerk}} on your next purchase"},
			{language: "en", key: "thankyou_points_deficit", value: "Earn {{deficit}} more points to redeem a {{reward}} for {{price}} {{pointsName}}"},
			{language: "en", key: "thankyou_points_enough", value: "You have enough points to redeem a {{reward}} for {{price}} {{pointsName}}"},
			{language: "en", key: "thankyou_top_text_logged_in", value: "Rewards update for you, {{name}}"},
			{language: "en", key: "thankyou_top_text_logged_out", value: "Don't miss out on rewards!"},
			{language: "en", key: "thankyou_points_summary_line_1_estimate", value: "At least {{points}} {{pointsName}}"},
			{language: "en", key: "thankyou_points_summary_line_1", value: "{{points}} {{pointsName}}"},
			{language: "en", key: "thankyou_points_summary_line_2_earned", value: "earned"},
			{language: "en", key: "thankyou_points_summary_line_2_would_have_earned", value: "waiting for you"},
			{language: "en", key: "thankyou_reward_summary_line_1_deficit", value: "{{points}} more {{pointsName}}"},
			{language: "en", key: "thankyou_reward_summary_line_1_enough", value: "You have enough {{pointsName}}"},
			{language: "en", key: "thankyou_reward_summary_line_2", value: "for {{rewardName}}"},
			{language: "en", key: "thankyou_reward_summary_line_2_tier", value: "until you reach {{tierName}}"},
			{language: "en", key: "thankyou_claim_link", value: "Claim your {{points}} {{pointsName}}"},
			{language: "en", key: "view_external_link", value: "Get more Details"},
			{language: "en", key: "product_page_guest_earn_teaser_divider", value: "|"},
			{ language: "en", key: "cart_free_gift_threshold_not_met_choice", value: "Add {{amount}} more to your cart to earn a {{gift_name}}" },
			{ language: "en", key: "cart_free_gift_threshold_not_met_single", value: "Add {{amount}} more to your cart to earn a {{gift_name}}" },
			{ language: "en", key: "cart_free_gift_threshold_met_choice", value: "You've earned a {{gift_name}}. It has been automatically added to your cart" },
			{ language: "en", key: "cart_free_gift_threshold_met_single", value: "You've earned a {{gift_name}}. It has been automatically added to your cart" },
			{ language: "en", key: "free_gift", value: "Free Gift" },




			// French - Static
			{language: "fr", key: "join", value: "Rejoindre"},
			{language: "fr", key: "sign_in", value: "Se connecter"},
			{language: "fr", key: "loyalty_by_raleon_link", value: "Loyauté ✨ par {{link}}"},
			{language: "fr", key: "home_available_points", value: "{{pointsName}} disponibles"},
			{language: "fr", key: "earn_summary_ways_to_earn", value: "Moyens de gagner"},
			{language: "fr", key: "free_product_from_collection_name", value: "Produit gratuit de la Collection {{value}}"},
			{language: "fr", key: "inventory_summary_no_rewards", value: "Vous n'avez pas encore de récompenses."},
			{language: "fr", key: "points", value: "Points"},
			{language: "fr", key: "points_abbreviated", value: "pts"},
			{language: "fr", key: "rewards", value: "Récompenses"},
			{language: "fr", key: "redeem_rewards", value: "Échanger des récompenses"},
			{language: "fr", key: "see_all", value: "Voir tout"},
			{language: "fr", key: "copy_referral_link", value: "Copier le lien de parrainage unique"},
			{language: "fr", key: "copied_referral_link", value: "Copié dans le presse-papiers"},
			{language: "fr", key: "referral_socials", value: "Ou envoyer via les réseaux sociaux"},
			{language: "fr", key: "full_inventory_my_inventory", value: "Mon inventaire"},
			{language: "fr", key: "reward_detail_minimum_order_total", value: "Commande minimale de {{minOrderTotal}}."},
			{language: "fr", key: "reward_detail_claim_count_current", value: "Vous avez réclamé {{redeemed}} / {{maxUserRedemptions}}."},
			{language: "fr", key: "reward_detail_expiration_summary", value: "Expire {{expiresInDays}} jours après réclamation."},
			{language: "fr", key: "reward_detail_your_next_purchase", value: "votre prochain achat"},
			{language: "fr", key: "reward_detail_insufficient_points", value: "{{pointsName}} insuffisants"},
			{language: "fr", key: "reward_detail_claim_button_alt", value: "Réclamer et Appliquer"},
			{language: "fr", key: "youve_got_rewards", value: "Vous avez des récompenses!"},
			{language: "fr", key: "apply_to_cart", value: "Appliquer au panier"},
			{language: "fr", key: "applied_to_cart", value: "Déjà dans le panier"},
			{language: "fr", key: "apply_to_cart_description", value: "En cliquant sur « Appliquer au panier » ci-dessous, vous appliquerez le coupon {{itemName}} à votre panier."},
			{language: "fr", key: "on_next_purchase", value: "sur votre prochain achat."},
			{language: "fr", key: "spend_minimum", value: "Vous devez dépenser au moins {{minOrderTotal}} pour utiliser cette récompense"},
			{language: "fr", key: "choose_product_from_collection", value: "Choisissez un produit de la Collection {{collection}}"},
			{language: "fr", key: "choose_free_product", value: "Choisissez votre produit gratuit"},
			{language: "fr", key: "choose_dollar_off_product", value: "Appliquer et voir le produit"},
			{language: "fr", key: "choose_percent_off_product", value: "Appliquer et voir le produit"},
			{language: "fr", key: "coupon_expiration", value: "Cela expire dans {{timeValue}} {{timeLabel}}."},
			{language: "fr", key: "coupon", value: "coupon"},
			{
				language: "fr",
				key: "youve_got_referral_rewards",
				value: "Votre ami vous a envoyé une récompense de parrainage, cliquez ici ou inscrivez-vous pour la réclamer"
			},
			{language: "fr", key: "rewards_unlocked", value: "Récompenses Débloquées !"},
			{language: "fr", key: "vip-tiers-title", value: "Niveaux de Membres"},
			{language: "fr", key: "points-ttm", value: "{{pointsName}}"},
			{language: "fr", key: "perks_title", value: "Avantages"},
			{language: "fr", key: "points_to_next_tier", value: "{{pointsName}} pour le prochain niveau"},
			{language: "fr", key: "learn_more_about_tiers", value: "En savoir plus sur les niveaux"},
			{language: "fr", key: "referral_sign_in", value: "Connectez-vous pour parrainer un ami"},
			{language: "fr", key: "email_placeholder_referral", value: "L'email de votre ami"},
			{language: "fr", key: "referral_send", value: "Envoyer l'invitation"},
			{language: "fr", key: "referral_sent", value: "Invitation envoyée"},
			{language: "fr", key: "subview_loyalty", value: "Fidélité" },
			{language: "fr", key: "subview_vip", value: "Niveaux VIP" },
			{language: "fr", key: "subview_history", value: "Historique" },
			{language: "fr", key: "subview_my_rewards", value: "Mes Récompenses" },
			{ "language": "fr", "key": "coupon_waiting", "value": "Connectez-vous pour réclamer votre offre. Cliquez ici pour rejoindre." },
			{language: "fr", key: "coupon_added_to_cart", value: "Récompense ajoutée au panier"},
			{language: "fr", key: "product_page_guest_earn_teaser_divider", value: "|"},


			// French - "Generated"
			{language: "fr", key: "reward_pill_points", value: "{{value}} {{pointsAbbreviated}}"},
			{language: "fr", key: "reward_pill_points_per_dollar", value: "{{value}} {{pointsAbbreviated}}/{{prefix}}{{postfix}}"},
			{language: "fr", key: "reward_pill_dollars_off_coupon", value: "{{prefix}}{{value}} de réduction"},
			{language: "fr", key: "reward_pill_percent_discount", value: "Réduction de {{value}}%"},
			{language: "fr", key: "reward_pill_free_shipping", value: "Livraison gratuite"},
			{language: "fr", key: "reward_pill_free_product", value: "Produit gratuit"},
			{ "language": "fr", "key": "reward_pill_percent_off_product", "value": "% de réduction sur le produit" },
  			{ "language": "fr", "key": "reward_pill_dollar_off_product", "value": "Réduction en dollars sur le produit" },
			{language: "fr", key: "reward_pill_giveaway_entry", value: "1 Participation au concours" },
			{language: "fr", key: "reward_pill_giveaway_entry_multiple", value: "{{count}} Participations au concours" },
			{language: "fr", key: "reward_points_per_dollar", value: "Gagnez {{points}} {{pointsName}} pour chaque {{dollarName}} dépensé"},
			{language: "fr", key: "reward_points", value: "Gagner {{points}} {{pointsName}}"},
			{language: "fr", key: "reward_dollars_off_coupon", value: "Gagnez un coupon de réduction de {{prefix}}{{postfix}}{{amount}}"},
			{language: "fr", key: "reward_percent_discount", value: "Gagnez un coupon de réduction de {{amount}}%"},
			{ language: "fr", key: "reward_product_percent_discount", value: "Obtenez un coupon de {{amount}}% de réduction sur un produit spécifique" },
  			{ language: "fr", key: "reward_product_dollar_discount", value: "Obtenez un coupon de réduction de {{prefix}}{{postfix}}{{amount}} sur un produit spécifique" },
			{language: "fr", key: "show_discount_code", value: "Afficher le code de réduction" },
			{language: "fr", key: "condition_click_to_redeem", value: "Cliquez pour entrer" },
			{language: "fr", key: "condition_auto_redeem", value: "Vous êtes automatiquement inscrit !" },
			{language: "fr", key: "condition_successfully_redeemed", value: "Vous avez été inscrit au tirage au sort!"},
			{language: "fr", key: "condition_dollar_spent", value: `Faites un achat pour gagner des {{pointsName}} pour chaque {{currency_value:1}} dépensé`},
			{language: "fr", key: "condition_welcome_bonus", value: `Gagnez des récompenses en vous inscrivant`},
			{language: "fr", key: "condition_product_review", value: "Laissez un avis pour gagner une récompense"},
			{language: "fr", key: "condition_product_photo_review", value: `Laissez un avis pour gagner une récompense`},
			{language: "fr", key: "condition_birthday_bonus", value: `Gagnez des récompenses le jour de votre anniversaire`},
			{ language: "fr", key: "enter_birthday_text", value: "Entrez votre date de naissance" },
			{language: "fr", key: "condition_nth_purchase", value: `Effectuez {{counts}} achats`},
			{language: "fr", key: "condition_nth_purchase_single", value: "Faire {{counts}} achat"},
			{language: "fr", key: "condition_nth_purchase_progress", value: "Faites encore {{counts}} achats"},
			{language: "fr", key: "condition_nth_purchase_progress_single", value: "Faites encore {{counts}} achat"},
			{language: "fr", key: "condition_nth_purchase_progress_complete", value: "Objectif atteint"},
			{language: "fr", key: "condition_timed_purchase", value: `Effectuez {{orderCounts}} achats en {{daysUntilEnd}} jours`},
			{language: "fr", key: "condition_timed_purchase_single", value: "Effectuer {{orderCounts}} achat dans {{daysUntilEnd}} jours"},
			{language: "fr", key: "condition_follow_on_instagram", value: `<a href="https://www.instagram.com/{{handle}}" target="_blank" class="raleon-social-wte">Suivez @{{handle}} sur Instagram</a>`},
			{language: "fr", key: "condition_follow_on_tiktok", value: `<a href="https://www.tiktok.com/{{handle}}" target="_blank" class="raleon-social-wte">Suivez {{handle}} sur TikTok</a>`},
			{language: "fr", key: "condition_follow_on_facebook", value: `<a href="https://www.facebook.com/{{handle}}" target="_blank" class="raleon-social-wte">Aimez {{handle}} sur Facebook</a>`},
			{language: "fr", key: "condition_follow_facebook_group", value: `<a href="https://www.facebook.com/groups/{{handle}}" target="_blank" class="raleon-social-wte">Rejoignez {{handle}} sur Facebook</a>`},
			{language: "fr", key: "condition_follow_on_youtube", value: `<a href="https://www.youtube.com/{{handle}}" target="_blank" class="raleon-social-wte">Abonnez-vous à {{handle}} sur YouTube</a>`},
			{ "language": "fr", "key": "condition_follow_on_custom", "value": "<a href=\"{{url}}\" target=\"_blank\" class=\"raleon-social-wte\">Suivez-nous</a>" },
			{language: "fr", key: "condition_short_nth_purchase", value: `{{count}} achat`},
			{language: "fr", key: "condition_short_follow_on_instagram", value: `Suivez @{{handle}}`},
			{language: "fr", key: "condition_short_follow_on_tiktok", value: `Suivez {{handle}}`},
			{language: "fr", key: "condition_short_follow_on_facebook", value: `Aimez {{handle}}`},
			{ "language": "fr", "key": "condition_short_follow_on_youtube", "value": "Abonnez-vous à {{handle}}" },
			{ "language": "fr", "key": "condition_short_follow_on_custom", "value": "Suivez-nous" },
			{language: "fr", key: "condition_specific_product_purchase", value: `Acheter un <a href="https://{{domain}}/products/{{handle}}" class="raleon-social-wte">produit spécifique</a>`},
			{language: "fr", key: "condition_collection_purchase", value: `Acheter {{count}} de la <a href="https://{{domain}}/collections/{{handle}}" class="raleon-social-wte">collection</a>`},
			{language: "fr", key: "condition_specific_product_short_purchase", value: `Acheter le produit`},
			{language: "fr", key: "condition_collection_short_purchase", value: `{{count}} de la collection`},
			{language: "fr", key: "condition_referrer_bonus", value: "Parrainez un ami"},
			{language: "fr", key: "condition_subscription_purchase", value: "Effectuez un achat d'abonnement"},
			{language: "fr", key: "condition_subscription_purchase_multiple", value: "Effectuez {{value}} achats d'abonnement"},
			{language: "fr", key: "referral_bonus_reward_name", value: "{{value}} de réduction"},
			{language: "fr", key: "referral_bonus_reward_description", value: "Obtenez {{value}} de réduction sur votre prochain achat en parrainant un ami"},
			{language: "fr", key: "referral_bonus_reward_points_description", value: "Obtenez {{value}} lorsque vous parrainez un ami"},
			{language: "fr", key: "points_ttm_earned", value: "{{points}} {{pointsName}} gagnés"},
			{language: "fr", key: "checkout_ui_points_header", value: "Vous avez {{points}} {{points_name}}"},
			{language: "fr", key: "checkout_ui_login_link_prefix", value: "Ne manquez pas!"},
			{language: "fr", key: "checkout_ui_login_link_text", value: "Connexion"},
			{language: "fr", key: "checkout_ui_login_link_suffix", value: " pour gagner et échanger des récompenses"},
			{language: "fr", key: "checkout_ui_not_enough_points_warning", value: "Vous avez besoin de {{points}} {{points_name}} de plus pour échanger une récompense"},
			{language: "fr", key: "checkout_ui_rewards_dropdown_label", value: "Récompenses"},
			{language: "fr", key: "checkout_ui_rewards_dropdown_placeholder", value: "Sélectionnez une récompense"},
			{language: "fr", key: "checkout_ui_rewards_dropdown_item_text", value: "{{reward_name}} - {{points}} {{points_name}}"},
			{language: "fr", key: "checkout_ui_apply_reward_button_text", value: "Appliquer"},
			{language: "fr", key: "checkout_ui_rewards_dropdown_item_in_use", value: "{{reward_name}} - En cours d'utilisation"},
			{language: "fr", key: "checkout_ui_stackability_replacement_warning_title", value: "Coupons Remplacés"},
			{language: "fr", key: "checkout_ui_stackability_replacement_warning", value: "L'un de vos coupons n'est pas cumulable. Nous avons appliqué le meilleur coupon pour plus d'économies."},
			{language: "fr", key: "checkout_ui_stackability_replacement_warning_replacements", value: "Ce que nous avons remplacé :"},
			{language: "fr", key: "checkout_ui_stackability_replacement_warning_undo", value: "Annuler"},
			{language: "fr", key: "checkout_ui_stackability_error_title", value: "Coupon Non Appliqué"},
			{language: "fr", key: "checkout_ui_stackability_error", value: "L'un de vos coupons n'est pas cumulable."},
			{language: "fr", key: "checkout_ui_stackability_error_line_2", value: "Votre coupon actuel offre une meilleure réduction. Nous l'avons conservé pour vous."},
			{language: "fr", key: "checkout_ui_apply_error_title", value: "Le Coupon N'a Pas Pu Être Appliqué"},
			{language: "fr", key: "checkout_ui_apply_error", value: "La récompense que vous avez sélectionnée n'a pas pu être appliquée. Veuillez réessayer."},
			{
				"language": "fr",
				"key": "product_page_loyalty_banner_text",
				"value": "Loyauté"
			},
			{
				"language": "fr",
				"key": "product_page_guest_earn_teaser",
				"value": "Gagnez {{points}} {{pointsName}} et plus avec cet article"
			},
			{
				"language": "fr",
				"key": "product_page_guest_join",
				"value": "Rejoignez Loyauté"
			},
			{
				"language": "fr",
				"key": "product_page_points_deficit",
				"value": "Vous avez besoin de {{points}} {{pointsName}} supplémentaires pour échanger {{rewardName}}"
			},
			{
				"language": "fr",
				"key": "product_page_claim_and_apply_reward",
				"value": "Réclamer & Appliquer {{rewardName}}"
			},
			{
				"language": "fr",
				"key": "product_page_apply_reward",
				"value": "Appliquez votre {{rewardName}}"
			},
			{
				"language": "fr",
				"key": "product_page_reward_applied",
				"value": "{{rewardName}} appliqué !"
			},
			{
				"language": "fr",
				"key": "product_page_points_balance",
				"value": "Vous avez {{points}} {{points_name}}"
			},
			{
				"language": "fr",
				"key": "product_page_rewards_dropdown_label",
				"value": "Récompenses"
			},
			{
				"language": "fr",
				"key": "product_page_rewards_dropdown_placeholder",
				"value": "Sélectionnez une récompense"
			},
			{
				"language": "fr",
				"key": "product_page_rewards_dropdown_item_text",
				"value": "{{reward_name}} - {{points}} {{points_name}}"
			},
			{language: "fr", key: "product_page_rewards_dropdown_item_in_use", value: "{{reward_name}} - En cours d'utilisation"},
			{
				"language": "fr",
				"key": "thankyou_would_have_earned_estimate",
				"value": "Vous auriez gagné au moins {{estimate}} points avec cet achat"
			},
			{
				"language": "fr",
				"key": "thankyou_would_have_earned_exact",
				"value": "Vous auriez gagné {{points}} points avec cet achat"
			},
			{
				"language": "fr",
				"key": "thankyou_earned_estimate",
				"value": "Vous avez gagné au moins {{estimate}} points avec cet achat"
			},
			{
				"language": "fr",
				"key": "thankyou_earned_exact",
				"value": "Vous avez gagné {{points}} points avec cet achat"
			},
			{
				"language": "fr",
				"key": "thankyou_vip_perk",
				"value": "Profitez de votre Privilège Niveau {{vipTier}}, {{vipPerk}} lors de votre prochain achat"
			},
			{
				"language": "fr",
				"key": "thankyou_points_deficit",
				"value": "Gagnez {{deficit}} points supplémentaires pour échanger une {{reward}} pour {{price}} {{pointsName}}"
			},
			{
				"language": "fr",
				"key": "thankyou_points_enough",
				"value": "Vous avez assez de points pour échanger une {{reward}} pour {{price}} {{pointsName}}"
			},{
				"language": "fr",
				"key": "thankyou_top_text_logged_in",
				"value": "Mise à jour des récompenses pour vous, {{name}}"
			},
			{
				"language": "fr",
				"key": "thankyou_top_text_logged_out",
				"value": "Ne manquez pas les récompenses!"
			},
			{
				"language": "fr",
				"key": "thankyou_points_summary_line_1_estimate",
				"value": "Au moins {{points}} {{pointsName}}"
			},
			{
				"language": "fr",
				"key": "thankyou_points_summary_line_1",
				"value": "{{points}} {{pointsName}}"
			},
			{
				"language": "fr",
				"key": "thankyou_points_summary_line_2_earned",
				"value": "gagné"
			},
			{
				"language": "fr",
				"key": "thankyou_points_summary_line_2_would_have_earned",
				"value": "vous attendent"
			},
			{
				"language": "fr",
				"key": "thankyou_reward_summary_line_1_deficit",
				"value": "{{points}} {{pointsName}}"
			},
			{
				"language": "fr",
				"key": "thankyou_reward_summary_line_1_enough",
				"value": "Vous avez assez de {{pointsName}}"
			},
			{
				"language": "fr",
				"key": "thankyou_reward_summary_line_2",
				"value": "pour {{rewardName}}"
			},
			{
				"language": "fr",
				"key": "thankyou_claim_link",
				"value": "Réclamez vos {{points}} {{pointsName}}"
			},
			{language: "fr", key: "thankyou_reward_summary_line_2_tier", value: "jusqu'à ce que vous atteigniez {{tierName}}"},
			{language: "fr", key: "view_external_link", value: "Obtenez plus de détails"},
			{ language: "fr", key: "cart_free_gift_threshold_not_met_choice", value: "Ajoutez {{amount}} de plus à votre panier pour obtenir un {{gift_name}}" },
			{ language: "fr", key: "cart_free_gift_threshold_not_met_single", value: "Ajoutez {{amount}} de plus à votre panier pour obtenir un {{gift_name}}" },
			{ language: "fr", key: "cart_free_gift_threshold_met_choice", value: "Vous avez gagné un {{gift_name}}. Il a été automatiquement ajouté à votre panier" },
			{ language: "fr", key: "cart_free_gift_threshold_met_single", value: "Vous avez gagné un {{gift_name}}. Il a été automatiquement ajouté à votre panier" },
			{ language: "fr", key: "free_gift", value: "Cadeau Gratuit" },


			// Spanish - Static
			{language: "es", key: "join", value: "Unirse"},
			{language: "es", key: "sign_in", value: "Iniciar Sesión"},
			{language: "es", key: "loyalty_by_raleon_link", value: "Lealtad ✨ por {{link}}"},
			{language: "es", key: "home_available_points", value: "{{pointsName}} disponibles"},
			{language: "es", key: "earn_summary_ways_to_earn", value: "Formas de ganar"},
			{language: "es", key: "free_product_from_collection_name", value: "Producto gratuito de la Colección {{value}}"},
			{language: "es", key: "inventory_summary_no_rewards", value: "Aún no tienes ninguna recompensa."},
			{language: "es", key: "points", value: "Puntos"},
			{language: "es", key: "points_abbreviated", value: "pts"},
			{language: "es", key: "rewards", value: "Recompensas"},
			{language: "es", key: "redeem_rewards", value: "Canjear Recompensas"},
			{language: "es", key: "see_all", value: "Ver todo"},
			{language: "es", key: "copy_referral_link", value: "Copiar enlace de referencia único"},
			{language: "es", key: "copied_referral_link", value: "Copiado al portapapeles"},
			{language: "es", key: "referral_socials", value: "O enviar a través de las redes sociales"},
			{language: "es", key: "full_inventory_my_inventory", value: "Mi Inventario"},
			{language: "es", key: "reward_detail_minimum_order_total", value: "Tamaño mínimo de pedido de {{minOrderTotal}}."},
			{language: "es", key: "reward_detail_claim_count_current", value: "Has reclamado {{redeemed}} / {{maxUserRedemptions}}."},
			{language: "es", key: "reward_detail_expiration_summary", value: "Expira {{expiresInDays}} días después de reclamar."},
			{language: "es", key: "reward_detail_your_next_purchase", value: "tu próxima compra"},
			{language: "es", key: "reward_detail_insufficient_points", value: "{{pointsName}} insuficientes"},
			{language: "es", key: "reward_detail_claim_button_alt", value: "Reclamar y Aplicar"},
			{language: "es", key: "youve_got_rewards", value: "¡Tienes recompensas!"},
			{language: "es", key: "apply_to_cart", value: "Aplicar al carrito"},
			{language: "es", key: "applied_to_cart", value: "Ya en el carrito"},
			{language: "es", key: "apply_to_cart_description", value: "Al hacer clic en \"Aplicar al carrito\" abajo, aplicarás el cupón {{itemName}} a tu carrito."},
			{language: "es", key: "on_next_purchase", value: "en tu próxima compra."},
			{language: "es", key: "spend_minimum", value: "Debes gastar al menos {{minOrderTotal}} para usar esta recompensa"},
			{language: "es", key: "choose_product_from_collection", value: "Elige un producto de la Colección {{collection}}"},
			{language: "es", key: "choose_free_product", value: "Elige tu producto gratuito"},
			{language: "es", key: "choose_dollar_off_product", value: "Aplicar y ver producto"},
			{language: "es", key: "choose_percent_off_product", value: "Aplicar y ver producto"},
			{language: "es", key: "coupon_expiration", value: "Esto expira en {{timeValue}} {{timeLabel}}."},
			{language: "es", key: "coupon", value: "cupón"},
			{language: "es", key: "youve_got_referral_rewards", value: "Tu amigo te ha enviado una recompensa por referido, haz clic aquí o regístrate para reclamarla"},
			{language: "es", key: "rewards_unlocked", value: "¡Recompensas Desbloqueadas!"},
			{language: "es", key: "vip-tiers-title", value: "Niveles de Membresía"},
			{language: "es", key: "points-ttm", value: "{{pointsName}}"},
			{language: "es", key: "perks_title", value: "Beneficios"},
			{language: "es", key: "points_to_next_tier", value: "{{pointsName}} para el siguiente nivel"},
			{language: "es", key: "learn_more_about_tiers", value: "Aprender sobre los niveles"},
			{language: "es", key: "referral_sign_in", value: "Iniciar sesión para recomendar a un amigo"},
			{language: "es", key: "email_placeholder_referral", value: "El correo electrónico de tu amigo"},
			{language: "es", key: "referral_send", value: "Enviar invitación"},
			{language: "es", key: "referral_sent", value: "Invitación enviada"},
			{language: "es", key: "subview_loyalty", value: "Lealtad" },
			{language: "es", key: "subview_vip", value: "Niveles VIP" },
			{language: "es", key: "subview_history", value: "Historial" },
			{language: "es", key: "subview_my_rewards", value: "Mis Recompensas" },
			{ "language": "es", "key": "coupon_waiting", "value": "Inicia sesión para reclamar tu oferta. Haz clic aquí para unirte." },
			{language: "es", key: "coupon_added_to_cart", value: "Recompensa añadida al carrito"},
			{language: "es", key: "product_page_guest_earn_teaser_divider", value: "|"},



			// Spanish - "Generated"
			{language: "es", key: "reward_pill_points", value: "{{value}} {{pointsAbbreviated}}"},
			{language: "es", key: "reward_pill_points_per_dollar", value: "{{value}} {{pointsAbbreviated}}/{{prefix}}{{postfix}}"},
			{language: "es", key: "reward_pill_dollars_off_coupon", value: "{{prefix}}{{value}} de descuento"},
			{language: "es", key: "reward_pill_percent_discount", value: "{{value}}% de descuento"},
			{language: "es", key: "reward_pill_free_shipping", value: "Envío Gratis"},
			{language: "es", key: "reward_pill_free_product", value: "Producto Gratis"},
			{ "language": "es", "key": "reward_pill_percent_off_product", "value": "% de descuento en el producto" },
			{ "language": "es", "key": "reward_pill_dollar_off_product", "value": "Descuento en dólares en el producto" },
			{language: "es", key: "reward_pill_giveaway_entry", value: "1 Entrada al sorteo" },
			{language: "es", key: "reward_pill_giveaway_entry_multiple", value: "{{count}} Entradas al sorteo" },
			{language: "es", key: "reward_points_per_dollar", value: "Gana {{points}} {{pointsName}} por cada {{dollarName}} gastado"},
			{language: "es", key: "reward_points", value: "Gana {{points}} {{pointsName}}"},
			{language: "es", key: "reward_dollars_off_coupon", value: "Gana un cupón de {{prefix}}{{postfix}}{{amount}} de descuento"},
			{language: "es", key: "reward_percent_discount", value: "Gana un cupón de {{amount}}% de descuento"},
			{ language: "es", key: "reward_product_percent_discount", value: "Obtén un cupón de {{amount}}% de descuento en un producto específico" },
  			{ language: "es", key: "reward_product_dollar_discount", value: "Obtén un cupón de descuento de {{prefix}}{{postfix}}{{amount}} en un producto específico" },
			{language: "es", key: "show_discount_code", value: "Mostrar código de descuento" },
			{language: "es", key: "condition_click_to_redeem", value: "Haz clic para entrar" },
			{language: "es", key: "condition_auto_redeem", value: "¡Estás automáticamente inscrito!" },
			{language: "es", key: "condition_successfully_redeemed", value: "¡Has sido inscrito en el sorteo!"},
			{language: "es", key: "condition_dollar_spent", value: `Realiza una compra para ganar {{pointsName}} por cada {{currency_value:1}} gastado`},
			{language: "es", key: "condition_welcome_bonus", value: `Gana recompensas al unirte`},
			{language: "es", key: "condition_product_review", value: "Deja un comentario para obtener una recompensa"},
			{language: "es", key: "condition_product_photo_review", value: `Deja una reseña para ganar una recompensa`},
			{language: "es", key: "condition_birthday_bonus", value: `Gana Recompensas en tu Cumpleaños`},
			{ language: "es", key: "enter_birthday_text", value: "Introduce tu fecha de nacimiento" },
			{language: "es", key: "condition_nth_purchase", value: `Realiza {{counts}} Compras`},
			{language: "es", key: "condition_nth_purchase_single", value: "Realizar {{counts}} compra"},
			{language: "es", key: "condition_nth_purchase_progress", value: "Haga {{counts}} compras más"},
			{language: "es", key: "condition_nth_purchase_progress_single", value: "Haga {{counts}} compra más"},
			{language: "es", key: "condition_nth_purchase_progress_complete", value: "Objetivo alcanzado"},
			{language: "es", key: "condition_timed_purchase", value: `Realiza {{orderCounts}} compras en {{daysUntilEnd}} días`},
			{language: "es", key: "condition_timed_purchase_single", value: "Realizar {{orderCounts}} compra en {{daysUntilEnd}} días"},
			{language: "es", key: "condition_follow_on_instagram", value: `<a href="https://www.instagram.com/{{handle}}" target="_blank" class="raleon-social-wte">Sigue a @{{handle}} en Instagram</a>`},
			{language: "es", key: "condition_follow_on_tiktok", value: `<a href="https://www.tiktok.com/{{handle}}" target="_blank" class="raleon-social-wte">Sigue a {{handle}} en TikTok</a>`},
			{language: "es", key: "condition_follow_on_facebook", value: `<a href="https://www.facebook.com/{{handle}}" target="_blank" class="raleon-social-wte">Dale Me Gusta a {{handle}} en Facebook</a>`},
			{language: "es", key: "condition_follow_facebook_group", value: `<a href="https://www.facebook.com/groups/{{handle}}" target="_blank" class="raleon-social-wte">Únete a {{handle}} en Facebook</a>`},
			{language: "es", key: "condition_follow_on_youtube", value: `<a href="https://www.youtube.com/{{handle}}" target="_blank" class="raleon-social-wte">Suscríbete a {{handle}} en YouTube</a>`},
			{ "language": "es", "key": "condition_follow_on_custom", "value": "<a href=\"{{url}}\" target=\"_blank\" class=\"raleon-social-wte\">Síguenos</a>" },
			{language: "es", key: "condition_short_nth_purchase", value: `{{count}} Compra`},
			{language: "es", key: "condition_short_follow_on_instagram", value: `Sigue a @{{handle}}`},
			{language: "es", key: "condition_short_follow_on_tiktok", value: `Sigue a {{handle}}`},
			{language: "es", key: "condition_short_follow_on_facebook", value: `Dale Me Gusta a {{handle}}`},
			{ "language": "es", "key": "condition_short_follow_on_youtube", "value": "Suscríbete a {{handle}}" },
			{ "language": "es", "key": "condition_short_follow_on_custom", "value": "Síguenos" },
			{language: "es", key: "condition_specific_product_purchase", value: `Comprar <a href="https://{{domain}}/products/{{handle}}" class="raleon-social-wte">producto específico</a>`},
			{language: "es", key: "condition_collection_purchase", value: `Compra {{count}} de la <a href="https://{{domain}}/collections/{{handle}}" class="raleon-social-wte">colección</a>`},
			{language: "es", key: "condition_specific_product_short_purchase", value: `Comprar producto`},
			{language: "es", key: "condition_collection_short_purchase", value: `{{count}} de la colección`},
			{language: "es", key: "condition_referrer_bonus", value: "Recomienda a un amigo"},
			{language: "es", key: "condition_subscription_purchase", value: "Realiza una compra de suscripción"},
			{language: "es", key: "condition_subscription_purchase_multiple", value: "Realiza {{value}} compras de suscripción"},
			{language: "es", key: "referral_bonus_reward_name", value: "{{value}} de descuento"},
			{language: "es", key: "referral_bonus_reward_description", value: "Consigue {{value}} de descuento en tu próxima compra al recomendar a un amigo"},
			{language: "es", key: "referral_bonus_reward_points_description", value: "Consigue {{value}} cuando refieras a un amigo"},
			{language: "es", key: "points_ttm_earned", value: "{{points}} {{pointsName}} ganados"},
			{language: "es", key: "checkout_ui_points_header", value: "Tienes {{points}} {{points_name}}"},
			{language: "es", key: "checkout_ui_login_link_prefix", value: "¡No te lo pierdas!"},
			{language: "es", key: "checkout_ui_login_link_text", value: "Iniciar sesión"},
			{language: "es", key: "checkout_ui_login_link_suffix", value: " para ganar y canjear recompensas"},
			{language: "es", key: "checkout_ui_not_enough_points_warning", value: "Necesitas {{points}} más {{points_name}} para canjear una recompensa"},
			{language: "es", key: "checkout_ui_rewards_dropdown_label", value: "Recompensas"},
			{language: "es", key: "checkout_ui_rewards_dropdown_placeholder", value: "Selecciona una recompensa"},
			{language: "es", key: "checkout_ui_rewards_dropdown_item_text", value: "{{reward_name}} - {{points}} {{points_name}}"},
			{language: "es", key: "checkout_ui_apply_reward_button_text", value: "Aplicar"},
			{language: "es", key: "checkout_ui_rewards_dropdown_item_in_use", value: "{{reward_name}} - En uso"},
			{language: "es", key: "checkout_ui_stackability_replacement_warning_title", value: "Cupones Reemplazados"},
			{language: "es", key: "checkout_ui_stackability_replacement_warning", value: "Uno de tus cupones no se puede acumular. Aplicamos el mejor cupón para un mayor ahorro."},
			{language: "es", key: "checkout_ui_stackability_replacement_warning_replacements", value: "Lo que reemplazamos:"},
			{language: "es", key: "checkout_ui_stackability_replacement_warning_undo", value: "Deshacer"},
			{language: "es", key: "checkout_ui_stackability_error_title", value: "Cupón No Aplicado"},
			{language: "es", key: "checkout_ui_stackability_error", value: "Uno de tus cupones no se puede acumular."},
			{language: "es", key: "checkout_ui_stackability_error_line_2", value: "Tu cupón actual ofrece un mejor descuento. Lo hemos mantenido para ti."},
			{language: "es", key: "checkout_ui_apply_error_title", value: "El Cupón No Pudo Ser Aplicado"},
			{language: "es", key: "checkout_ui_apply_error", value: "La recompensa que seleccionaste no pudo ser aplicada. Por favor, intenta de nuevo."},
			{
				"language": "es",
				"key": "product_page_loyalty_banner_text",
				"value": "Lealtad"
			},
			{
				"language": "es",
				"key": "product_page_guest_earn_teaser",
				"value": "Gana {{points}} {{pointsName}} y más de este artículo"
			},
			{
				"language": "es",
				"key": "product_page_guest_join",
				"value": "Únete a Lealtad"
			},
			{
				"language": "es",
				"key": "product_page_points_deficit",
				"value": "Necesitas {{points}} más {{pointsName}} para canjear {{rewardName}}"
			},
			{
				"language": "es",
				"key": "product_page_claim_and_apply_reward",
				"value": "Reclamar y aplicar {{rewardName}}"
			},
			{
				"language": "es",
				"key": "product_page_apply_reward",
				"value": "Aplica tu {{rewardName}}"
			},
			{
				"language": "es",
				"key": "product_page_reward_applied",
				"value": "¡Aplicado tu {{rewardName}}!"
			},
			{
				"language": "es",
				"key": "product_page_points_balance",
				"value": "Tienes {{points}} {{points_name}}"
			},
			{
				"language": "es",
				"key": "product_page_rewards_dropdown_label",
				"value": "Recompensas"
			},
			{
				"language": "es",
				"key": "product_page_rewards_dropdown_placeholder",
				"value": "Selecciona una recompensa"
			},
			{
				"language": "es",
				"key": "product_page_rewards_dropdown_item_text",
				"value": "{{reward_name}} - {{points}} {{points_name}}"
			},
			{language: "es", key: "product_page_rewards_dropdown_item_in_use", value: "{{reward_name}} - En uso"},
			{
				"language": "es",
				"key": "thankyou_would_have_earned_estimate",
				"value": "Habrías ganado al menos {{estimate}} puntos con esta compra"
			},
			{
				"language": "es",
				"key": "thankyou_would_have_earned_exact",
				"value": "Habrías ganado {{points}} puntos con esta compra"
			},
			{
				"language": "es",
				"key": "thankyou_earned_estimate",
				"value": "Has ganado al menos {{estimate}} puntos con esta compra"
			},
			{
				"language": "es",
				"key": "thankyou_earned_exact",
				"value": "Has ganado {{points}} puntos con esta compra"
			},
			{
				"language": "es",
				"key": "thankyou_vip_perk",
				"value": "Disfruta de tu Beneficio de Nivel {{vipTier}}, {{vipPerk}} en tu próxima compra"
			},
			{
				"language": "es",
				"key": "thankyou_points_deficit",
				"value": "Gana {{deficit}} puntos más para canjear un {{reward}} por {{price}} {{pointsName}}"
			},
			{
				"language": "es",
				"key": "thankyou_points_enough",
				"value": "Tienes suficientes puntos para canjear un {{reward}} por {{price}} {{pointsName}}"
			},{
				"language": "es",
				"key": "thankyou_top_text_logged_in",
				"value": "Actualización de recompensas para ti, {{name}}"
			},
			{
				"language": "es",
				"key": "thankyou_top_text_logged_out",
				"value": "¡No te pierdas las recompensas!"
			},
			{
				"language": "es",
				"key": "thankyou_points_summary_line_1_estimate",
				"value": "Al menos {{points}} {{pointsName}}"
			},
			{
				"language": "es",
				"key": "thankyou_points_summary_line_1",
				"value": "{{points}} {{pointsName}}"
			},
			{
				"language": "es",
				"key": "thankyou_points_summary_line_2_earned",
				"value": "ganado"
			},
			{
				"language": "es",
				"key": "thankyou_points_summary_line_2_would_have_earned",
				"value": "esperando por ti"
			},
			{
				"language": "es",
				"key": "thankyou_reward_summary_line_1_deficit",
				"value": "{{points}} más {{pointsName}}"
			},
			{
				"language": "es",
				"key": "thankyou_reward_summary_line_1_enough",
				"value": "Tienes suficientes {{pointsName}}"
			},
			{
				"language": "es",
				"key": "thankyou_reward_summary_line_2",
				"value": "para {{rewardName}}"
			},
			{
				"language": "es",
				"key": "thankyou_claim_link",
				"value": "Reclama tus {{points}} {{pointsName}}"
			},
			{language: "es", key: "thankyou_reward_summary_line_2_tier", value: "hasta que alcances {{tierName}}"},
			{language: "es", key: "view_external_link", value: "Obtener más detalles"},
			{ language: "es", key: "cart_free_gift_threshold_not_met_choice", value: "Añade {{amount}} más a tu carrito para ganar un {{gift_name}}" },
			{ language: "es", key: "cart_free_gift_threshold_not_met_single", value: "Añade {{amount}} más a tu carrito para ganar un {{gift_name}}" },
			{ language: "es", key: "cart_free_gift_threshold_met_choice", value: "Has ganado un {{gift_name}}. Se ha añadido automáticamente a tu carrito" },
			{ language: "es", key: "cart_free_gift_threshold_met_single", value: "Has ganado un {{gift_name}}. Se ha añadido automáticamente a tu carrito" },
			{ language: "es", key: "free_gift", value: "Regalo Gratis" },


			// Italian - Static
			{language: "it", key: "join", value: "Unisciti"},
			{language: "it", key: "sign_in", value: "Accedi"},
			{language: "it", key: "loyalty_by_raleon_link", value: "Fedeltà ✨ da {{link}}"},
			{language: "it", key: "home_available_points", value: "{{pointsName}} disponibili"},
			{language: "it", key: "earn_summary_ways_to_earn", value: "Modi per guadagnare"},
			{language: "it", key: "free_product_from_collection_name", value: "Prodotto gratuito dalla Collezione {{value}}"},
			{language: "it", key: "inventory_summary_no_rewards", value: "Non hai ancora premi."},
			{language: "it", key: "points", value: "Punti"},
			{language: "it", key: "points_abbreviated", value: "pt"},
			{language: "it", key: "rewards", value: "Ricompense"},
			{language: "it", key: "redeem_rewards", value: "Riscatta Ricompense"},
			{language: "it", key: "see_all", value: "Vedi tutto"},
			{language: "it", key: "copy_referral_link", value: "Copia il link di riferimento unico"},
			{language: "it", key: "copied_referral_link", value: "Copiato negli appunti"},
			{language: "it", key: "referral_socials", value: "Oppure invia tramite i social"},
			{language: "it", key: "full_inventory_my_inventory", value: "Il mio inventario"},
			{language: "it", key: "reward_detail_minimum_order_total", value: "Ordine minimo di {{minOrderTotal}}."},
			{language: "it", key: "reward_detail_claim_count_current", value: "Hai richiesto {{redeemed}} / {{maxUserRedemptions}}."},
			{language: "it", key: "reward_detail_expiration_summary", value: "Scade {{expiresInDays}} giorni dopo la richiesta."},
			{language: "it", key: "reward_detail_your_next_purchase", value: "il tuo prossimo acquisto"},
			{language: "it", key: "reward_detail_insufficient_points", value: "{{pointsName}} insufficienti"},
			{language: "it", key: "reward_detail_claim_button_alt", value: "Richiedi e Applica"},
			{language: "it", key: "youve_got_rewards", value: "Hai delle ricompense!"},
			{language: "it", key: "apply_to_cart", value: "Applica al carrello"},
			{language: "it", key: "applied_to_cart", value: "Già nel carrello"},
			{language: "it", key: "apply_to_cart_description", value: "Cliccando su \"Applica al carrello\" qui sotto applicherai il coupon {{itemName}} al tuo carrello."},
			{language: "it", key: "on_next_purchase", value: "sul tuo prossimo acquisto."},
			{language: "it", key: "spend_minimum", value: "Devi spendere almeno {{minOrderTotal}} per utilizzare questo premio"},
			{language: "it", key: "choose_product_from_collection", value: "Scegli un prodotto dalla Collezione {{collection}}"},
			{language: "it", key: "choose_free_product", value: "Scegli il tuo prodotto gratuito"},
			{language: "it", key: "choose_dollar_off_product", value: "Applica e visualizza prodotto"},
			{language: "it", key: "choose_percent_off_product", value: "Applica e visualizza prodotto"},
			{language: "it", key: "coupon_expiration", value: "Scade tra {{timeValue}} {{timeLabel}}."},
			{language: "it", key: "coupon", value: "buono"},
			{language: "it", key: "youve_got_referral_rewards", value: "Il tuo amico ti ha inviato un premio di riferimento, clicca qui o registrati per richiederlo"},
			{language: "it", key: "rewards_unlocked", value: "Premi Sbloccati!"},
			{language: "it", key: "vip-tiers-title", value: "Livelli di Adesione"},
			{language: "it", key: "points-ttm", value: "{{pointsName}}"},
			{language: "it", key: "perks_title", value: "Vantaggi"},
			{language: "it", key: "points_to_next_tier", value: "{{pointsName}} al prossimo livello"},
			{language: "it", key: "learn_more_about_tiers", value: "Scopri di più sui livelli"},
			{language: "it", key: "referral_sign_in", value: "Accedi per invitare un amico"},
			{language: "it", key: "email_placeholder_referral", value: "L'email del tuo amico"},
			{language: "it", key: "referral_send", value: "Invia invito"},
			{language: "it", key: "referral_sent", value: "Invito inviato"},
			{language: "it", key: "subview_loyalty", value: "Fedeltà" },
			{language: "it", key: "subview_vip", value: "Livelli VIP" },
			{language: "it", key: "subview_history", value: "Storico" },
			{language: "it", key: "subview_my_rewards", value: "Le Mie Ricompense" },
			{ "language": "it", "key": "coupon_waiting", "value": "Accedi per richiedere la tua offerta. Clicca qui per unirti." },
			{language: "it", key: "coupon_added_to_cart", value: "Ricompensa aggiunta al carrello"},
			{language: "it", key: "product_page_guest_earn_teaser_divider", value: "|"},

			// Italian - "Generated"
			{language: "it", key: "reward_pill_points", value: "{{value}} {{pointsAbbreviated}}"},
			{language: "it", key: "reward_pill_points_per_dollar", value: "{{value}} {{pointsAbbreviated}}/{{prefix}}{{postfix}}"},
			{language: "it", key: "reward_pill_dollars_off_coupon", value: "Sconto di {{prefix}}{{value}}"},
			{language: "it", key: "reward_pill_percent_discount", value: "Sconto del {{value}}%"},
			{language: "it", key: "reward_pill_free_shipping", value: "Spedizione Gratuita"},
			{language: "it", key: "reward_pill_free_product", value: "Prodotto Gratuito"},
			{ "language": "it", "key": "reward_pill_percent_off_product", "value": "% di sconto sul prodotto" },
  			{ "language": "it", "key": "reward_pill_dollar_off_product", "value": "Sconto in dollari sul prodotto" },
			{language: "it", key: "reward_pill_giveaway_entry", value: "1 Iscrizione al concorso" },
			{language: "it", key: "reward_pill_giveaway_entry_multiple", value: "{{count}} Iscrizioni al concorso" },
			{language: "it", key: "reward_points_per_dollar", value: "Guadagna {{points}} {{pointsName}} per ogni {{dollarName}} speso"},
			{language: "it", key: "reward_points", value: "Guadagna {{points}} {{pointsName}}"},
			{language: "it", key: "reward_dollars_off_coupon", value: "Guadagna un buono sconto di {{prefix}}{{postfix}}{{amount}}"},
			{language: "it", key: "reward_percent_discount", value: "Guadagna un buono sconto del {{amount}}%"},
			{ language: "it", key: "reward_product_percent_discount", value: "Ottieni un coupon del {{amount}}% di sconto su un prodotto specifico" },
  			{ language: "it", key: "reward_product_dollar_discount", value: "Ottieni un coupon di {{prefix}}{{postfix}}{{amount}} di sconto su un prodotto specifico" },
			{language: "it", key: "show_discount_code", value: "Mostra codice sconto" },
			{language: "it", key: "condition_click_to_redeem", value: "Clicca per entrare" },
			{language: "it", key: "condition_auto_redeem", value: "Sei automaticamente iscritto!" },
			{language: "it", key: "condition_successfully_redeemed", value: "Sei stato iscritto al concorso!"},
			{language: "it", key: "condition_dollar_spent", value: "Effettua un Acquisto per Guadagnare {{pointsName}} per ogni {{currency_value:1}} speso"},
			{language: "it", key: "condition_welcome_bonus", value: "Guadagna ricompense quando ti unisci"},
			{language: "it", key: "condition_product_review", value: "Lascia una recensione per ricevere un premio"},
			{language: "it", key: "condition_product_photo_review", value: `Lascia una recensione per ricevere un premio`},
			{language: "it", key: "condition_birthday_bonus", value: "Guadagna Premi nel tuo Compleanno"},
			{ language: "it", key: "enter_birthday_text", value: "Inserisci la tua data di nascita" },
			{language: "it", key: "condition_nth_purchase", value: "Effettua {{counts}} Acquisti"},
			{language: "it", key: "condition_nth_purchase_single", value: "Effettua {{counts}} acquisto"},
			{language: "it", key: "condition_nth_purchase_progress", value: "Fai ancora {{counts}} acquisti"},
			{language: "it", key: "condition_nth_purchase_progress_single", value: "Fai ancora {{counts}} acquisto"},
			{language: "it", key: "condition_nth_purchase_progress_complete", value: "Obiettivo raggiunto"},
			{language: "it", key: "condition_timed_purchase", value: "Effettua {{orderCounts}} Acquisti in {{daysUntilEnd}} giorni"},
			{language: "it", key: "condition_timed_purchase_single", value: "Effettuare {{orderCounts}} acquisto in {{daysUntilEnd}} giorni"},
			{language: "it", key: "condition_follow_on_instagram", value: "<a href='https://www.instagram.com/{{handle}}' target='_blank' class='raleon-social-wte'>Segui @{{handle}} su Instagram</a>"},
			{language: "it", key: "condition_follow_on_tiktok", value: "<a href='https://www.tiktok.com/{{handle}}' target='_blank' class='raleon-social-wte'>Segui {{handle}} su TikTok</a>"},
			{language: "it", key: "condition_follow_on_facebook", value: "<a href='https://www.facebook.com/{{handle}}' target='_blank' class='raleon-social-wte'>Metti Mi Piace a {{handle}} su Facebook</a>"},
			{language: "it", key: "condition_follow_facebook_group", value: `<a href="https://www.facebook.com/groups/{{handle}}" target="_blank" class="raleon-social-wte">Unisciti a {{handle}} su Facebook</a>`},
			{language: "it", key: "condition_follow_on_youtube", value: `<a href="https://www.youtube.com/{{handle}}" target="_blank" class="raleon-social-wte">Iscriviti a {{handle}} su YouTube</a>`},
			{ "language": "it", "key": "condition_follow_on_custom", "value": "<a href=\"{{url}}\" target=\"_blank\" class=\"raleon-social-wte\">Seguici</a>" },
			{language: "it", key: "condition_short_nth_purchase", value: "{{count}} Acquisto"},
			{language: "it", key: "condition_short_follow_on_instagram", value: "Segui @{{handle}}"},
			{language: "it", key: "condition_short_follow_on_tiktok", value: "Segui {{handle}}"},
			{language: "it", key: "condition_short_follow_on_facebook", value: "Metti Mi Piace a {{handle}}"},
			{ "language": "it", "key": "condition_short_follow_on_youtube", "value": "Iscriviti a {{handle}}" },
			{ "language": "it", "key": "condition_short_follow_on_custom", "value": "Seguici" },
			{language: "it", key: "condition_specific_product_purchase", value: `Acquista un <a href="https://{{domain}}/products/{{handle}}" class="raleon-social-wte">prodotto specifico</a>`},
			{language: "it", key: "condition_collection_purchase", value: `Acquista {{count}} dalla <a href="https://{{domain}}/collections/{{handle}}" class="raleon-social-wte">collezione</a>`},
			{language: "it", key: "condition_specific_product_short_purchase", value: `Acquista prodotto`},
			{language: "it", key: "condition_collection_short_purchase", value: `{{count}} dalla collezione`},
			{language: "it", key: "condition_referrer_bonus", value: "Riferisci un amico"},
			{language: "it", key: "condition_subscription_purchase", value: "Effettua un acquisto di abbonamento"},
			{language: "it", key: "condition_subscription_purchase_multiple", value: "Effettua {{value}} acquisti di abbonamento"},
			{language: "it", key: "referral_bonus_reward_name", value: "Sconto di {{value}}"},
			{language: "it", key: "referral_bonus_reward_description", value: "Ottieni uno sconto di {{value}} sul tuo prossimo acquisto quando riferisci un amico"},
			{language: "it", key: "referral_bonus_reward_points_description", value: "Ottieni {{value}} quando riferisci un amico"},
			{language: "it", key: "points_ttm_earned", value: "{{points}} {{pointsName}} guadagnati"},
			{language: "it", key: "checkout_ui_points_header", value: "Hai {{points}} {{points_name}}"},
			{language: "it", key: "checkout_ui_login_link_prefix", value: "Non perdere!"},
			{language: "it", key: "checkout_ui_login_link_text", value: "Accedi"},
			{language: "it", key: "checkout_ui_login_link_suffix", value: " per guadagnare e riscattare premi"},
			{language: "it", key: "checkout_ui_not_enough_points_warning", value: "Hai bisogno di {{points}} {{points_name}} in più per riscattare una ricompensa"},
			{language: "it", key: "checkout_ui_rewards_dropdown_label", value: "Ricompense"},
			{language: "it", key: "checkout_ui_rewards_dropdown_placeholder", value: "Seleziona una ricompensa"},
			{language: "it", key: "checkout_ui_rewards_dropdown_item_text", value: "{{reward_name}} - {{points}} {{points_name}}"},
			{language: "it", key: "checkout_ui_apply_reward_button_text", value: "Applica"},
			{language: "it", key: "checkout_ui_rewards_dropdown_item_in_use", value: "{{reward_name}} - In uso"},
			{language: "it", key: "checkout_ui_stackability_replacement_warning_title", value: "Coupon Sostituiti"},
			{language: "it", key: "checkout_ui_stackability_replacement_warning", value: "Uno dei tuoi coupon non è cumulabile. Abbiamo applicato il miglior coupon per un risparmio maggiore."},
			{language: "it", key: "checkout_ui_stackability_replacement_warning_replacements", value: "Cosa abbiamo sostituito:"},
			{language: "it", key: "checkout_ui_stackability_replacement_warning_undo", value: "Annulla"},
			{language: "it", key: "checkout_ui_stackability_error_title", value: "Coupon Non Applicato"},
			{language: "it", key: "checkout_ui_stackability_error", value: "Uno dei tuoi coupon non è cumulabile."},
			{language: "it", key: "checkout_ui_stackability_error_line_2", value: "Il tuo attuale coupon offre uno sconto migliore. Lo abbiamo mantenuto per te."},
			{language: "it", key: "checkout_ui_apply_error_title", value: "Il Coupon Non Può Essere Applicato"},
			{language: "it", key: "checkout_ui_apply_error", value: "La ricompensa che hai selezionato non può essere applicata. Per favore, prova di nuovo."},
			{"language": "it", "key": "product_page_loyalty_banner_text", "value": "Fedeltà"},
			{"language": "it", "key": "product_page_guest_earn_teaser", "value": "Guadagna {{points}} {{pointsName}} e altro da questo articolo"},
			{"language": "it", "key": "product_page_guest_join", "value": "Unisciti a Fedeltà"},
			{"language": "it", "key": "product_page_points_deficit", "value": "Ti servono altri {{points}} {{pointsName}} per riscattare {{rewardName}}"},
			{"language": "it", "key": "product_page_claim_and_apply_reward", "value": "Richiedi & Applica {{rewardName}}"},
			{"language": "it", "key": "product_page_apply_reward", "value": "Applica il tuo {{rewardName}}"},
			{"language": "it", "key": "product_page_reward_applied", "value": "Applicato il tuo {{rewardName}}!"},
			{"language": "it", "key": "product_page_points_balance", "value": "Hai {{points}} {{points_name}}"},
			{"language": "it", "key": "product_page_rewards_dropdown_label", "value": "Premi"},
			{"language": "it", "key": "product_page_rewards_dropdown_placeholder", "value": "Seleziona un premio"},
			{"language": "it", "key": "product_page_rewards_dropdown_item_text", "value": "{{reward_name}} - {{points}} {{points_name}}"},
			{language: "it", key: "product_page_rewards_dropdown_item_in_use", value: "{{reward_name}} - In uso"},
			{
				"language": "it",
				"key": "thankyou_would_have_earned_estimate",
				"value": "Avresti guadagnato almeno {{estimate}} punti con questo acquisto"
			},
			{
				"language": "it",
				"key": "thankyou_would_have_earned_exact",
				"value": "Avresti guadagnato {{points}} punti con questo acquisto"
			},
			{
				"language": "it",
				"key": "thankyou_earned_estimate",
				"value": "Hai guadagnato almeno {{estimate}} punti con questo acquisto"
			},
			{
				"language": "it",
				"key": "thankyou_earned_exact",
				"value": "Hai guadagnato {{points}} punti con questo acquisto"
			},
			{
				"language": "it",
				"key": "thankyou_vip_perk",
				"value": "Goditi il tuo Vantaggio di Livello {{vipTier}}, {{vipPerk}} sul tuo prossimo acquisto"
			},
			{
				"language": "it",
				"key": "thankyou_points_deficit",
				"value": "Guadagna altri {{deficit}} punti per riscattare un {{reward}} per {{price}} {{pointsName}}"
			},
			{
				"language": "it",
				"key": "thankyou_points_enough",
				"value": "Hai abbastanza punti per riscattare un {{reward}} per {{price}} {{pointsName}}"
			},
			{
				"language": "it",
				"key": "thankyou_top_text_logged_in",
				"value": "Aggiornamento premi per te, {{name}}"
			},
			{
				"language": "it",
				"key": "thankyou_top_text_logged_out",
				"value": "Non perderti i premi!"
			},
			{
				"language": "it",
				"key": "thankyou_points_summary_line_1_estimate",
				"value": "Almeno {{points}} {{pointsName}}"
			},
			{
				"language": "it",
				"key": "thankyou_points_summary_line_1",
				"value": "{{points}} {{pointsName}}"
			},
			{
				"language": "it",
				"key": "thankyou_points_summary_line_2_earned",
				"value": "guadagnato"
			},
			{
				"language": "it",
				"key": "thankyou_points_summary_line_2_would_have_earned",
				"value": "in attesa per te"
			},
			{
				"language": "it",
				"key": "thankyou_reward_summary_line_1_deficit",
				"value": "{{points}} {{pointsName}} in più"
			},
			{
				"language": "it",
				"key": "thankyou_reward_summary_line_1_enough",
				"value": "Hai abbastanza {{pointsName}}"
			},
			{
				"language": "it",
				"key": "thankyou_reward_summary_line_2",
				"value": "per {{rewardName}}"
			},
			{
				"language": "it",
				"key": "thankyou_claim_link",
				"value": "Richiedi i tuoi {{points}} {{pointsName}}"
			},
			{language: "it", key: "thankyou_reward_summary_line_2_tier", value: "fino a quando raggiungi {{tierName}}"},
			{language: "it", key: "view_external_link", value: "Ottieni più dettagli"},
			{ language: "it", key: "cart_free_gift_threshold_not_met_choice", value: "Aggiungi {{amount}} in più al carrello per ottenere un {{gift_name}}" },
			{ language: "it", key: "cart_free_gift_threshold_not_met_single", value: "Aggiungi {{amount}} in più al carrello per ottenere un {{gift_name}}" },
			{ language: "it", key: "cart_free_gift_threshold_met_choice", value: "Hai guadagnato un {{gift_name}}. È stato aggiunto automaticamente al tuo carrello" },
			{ language: "it", key: "cart_free_gift_threshold_met_single", value: "Hai guadagnato un {{gift_name}}. È stato aggiunto automaticamente al tuo carrello" },
			{ language: "it", key: "free_gift", value: "Regalo Gratuito" },





			// German - Static
			{language: "de", key: "join", value: "Beitreten"},
			{language: "de", key: "sign_in", value: "Anmelden"},
			{language: "de", key: "loyalty_by_raleon_link", value: "Loyalität ✨ von {{link}}"},
			{language: "de", key: "home_available_points", value: "verfügbare {{pointsName}}"},
			{language: "de", key: "earn_summary_ways_to_earn", value: "Möglichkeiten zum Verdienen"},
			{language: "de", key: "free_product_from_collection_name", value: "Kostenloses Produkt aus der {{value}} Kollektion"},
			{language: "de", key: "inventory_summary_no_rewards", value: "Du hast noch keine Belohnungen."},
			{language: "de", key: "points", value: "Punkte"},
			{language: "de", key: "points_abbreviated", value: "Pkte"},
			{language: "de", key: "rewards", value: "Belohnungen"},
			{language: "de", key: "redeem_rewards", value: "Belohnungen einlösen"},
			{language: "de", key: "see_all", value: "Alle anzeigen"},
			{language: "de", key: "copy_referral_link", value: "Einzigartigen Empfehlungslink kopieren"},
			{language: "de", key: "copied_referral_link", value: "In die Zwischenablage kopiert"},
			{language: "de", key: "referral_socials", value: "Oder über Soziale Netzwerke senden"},
			{language: "de", key: "full_inventory_my_inventory", value: "Mein Inventar"},
			{language: "de", key: "reward_detail_minimum_order_total", value: "Mindestbestellwert von {{minOrderTotal}}."},
			{language: "de", key: "reward_detail_claim_count_current", value: "Du hast {{redeemed}} / {{maxUserRedemptions}} eingelöst."},
			{language: "de", key: "reward_detail_expiration_summary", value: "Läuft {{expiresInDays}} Tage nach Einlösung ab."},
			{language: "de", key: "reward_detail_your_next_purchase", value: "dein nächster Kauf"},
			{language: "de", key: "reward_detail_insufficient_points", value: "Unzureichende {{pointsName}}"},
			{language: "de", key: "reward_detail_claim_button_alt", value: "Beanspruchen und Anwenden"},
			{language: "de", key: "youve_got_rewards", value: "Du hast Prämien!"},
			{language: "de", key: "apply_to_cart", value: "Zum Warenkorb hinzufügen"},
			{language: "de", key: "applied_to_cart", value: "Bereits im Warenkorb"},
			{language: "de", key: "apply_to_cart_description", value: "Wenn Sie unten auf \"Zum Warenkorb hinzufügen\" klicken, wird der Gutschein {{itemName}} Ihrem Warenkorb hinzugefügt."},
			{language: "de", key: "on_next_purchase", value: "bei Ihrem nächsten Einkauf."},
			{language: "de", key: "spend_minimum", value: "Sie müssen mindestens {{minOrderTotal}} ausgeben, um diese Belohnung zu nutzen"},
			{language: "de", key: "choose_product_from_collection", value: "Wählen Sie ein Produkt aus der {{collection}} Kollektion"},
			{language: "de", key: "choose_free_product", value: "Wählen Sie Ihr kostenloses Produkt"},
			{language: "de", key: "choose_dollar_off_product", value: "Anwenden und Produkt anzeigen"},
			{language: "de", key: "choose_percent_off_product", value: "Anwenden und Produkt anzeigen"},
			{language: "de", key: "coupon_expiration", value: "Dies läuft in {{timeValue}} {{timeLabel}} ab."},
			{language: "de", key: "coupon", value: "gutschein"},
			{language: "de", key: "youve_got_referral_rewards", value: "Dein Freund hat dir eine Empfehlungsprämie gesendet, klicke hier oder melde dich an, um sie zu beanspruchen"},
			{language: "de", key: "rewards_unlocked", value: "Belohnungen Freigeschaltet!"},
			{language: "de", key: "vip-tiers-title", value: "Mitgliedschaftsstufen"},
			{language: "de", key: "points-ttm", value: "{{pointsName}}"},
			{language: "de", key: "perks_title", value: "Vorteile"},
			{language: "de", key: "points_to_next_tier", value: "{{pointsName}} bis zur nächsten Stufe"},
			{language: "de", key: "learn_more_about_tiers", value: "Mehr über die Stufen erfahren"},
			{language: "de", key: "referral_sign_in", value: "Anmelden, um einen Freund zu werben"},
			{language: "de", key: "email_placeholder_referral", value: "Die E-Mail Ihres Freundes"},
			{language: "de", key: "referral_send", value: "Einladung senden"},
			{language: "de", key: "referral_sent", value: "Einladung gesendet"},
			{language: "de", key: "subview_loyalty", value: "Treue" },
			{language: "de", key: "subview_vip", value: "VIP-Stufen" },
			{language: "de", key: "subview_history", value: "Verlauf" },
			{language: "de", key: "subview_my_rewards", value: "Meine Belohnungen" },
			{language: "de", key: "product_page_guest_earn_teaser_divider", value: "|"},

			//German - "Generated"
			{language: "de", key: "reward_pill_points", value: "{{value}} {{pointsAbbreviated}}"},
			{language: "de", key: "reward_pill_points_per_dollar", value: "{{value}} {{pointsAbbreviated}}/{{prefix}}{{postfix}}"},
			{language: "de", key: "reward_pill_dollars_off_coupon", value: "{{prefix}}{{value}} Rabatt"},
			{language: "de", key: "reward_pill_percent_discount", value: "{{value}}% Rabatt"},
			{language: "de", key: "reward_pill_free_shipping", value: "Kostenloser Versand"},
			{language: "de", key: "reward_pill_free_product", value: "Kostenloses Produkt"},
			{ "language": "de", "key": "reward_pill_percent_off_product", "value": "% Rabatt auf das Produkt" },
  			{ "language": "de", "key": "reward_pill_dollar_off_product", "value": "Dollar-Rabatt auf das Produkt" },
			{language: "de", key: "reward_pill_giveaway_entry", value: "1 Gewinnspielteilnahme" },
			{language: "de", key: "reward_pill_giveaway_entry_multiple", value: "{{count}} Gewinnspielteilnahmen" },
			{language: "de", key: "reward_points_per_dollar", value: "Verdiene {{points}} {{pointsName}} für jeden ausgegebenen {{dollarName}}"},
			{language: "de", key: "reward_points", value: "Verdiene {{points}} {{pointsName}}"},
			{language: "de", key: "reward_dollars_off_coupon", value: "Verdiene einen {{prefix}}{{postfix}}{{amount}} Rabatt-Gutschein"},
			{language: "de", key: "reward_percent_discount", value: "Verdiene einen {{amount}}% Rabatt-Gutschein"},
			{ language: "de", key: "reward_product_percent_discount", value: "Erhalten Sie einen {{amount}}% Rabattcoupon für ein bestimmtes Produkt" },
  			{ language: "de", key: "reward_product_dollar_discount", value: "Erhalten Sie einen Rabattcoupon über {{prefix}}{{postfix}}{{amount}} für ein bestimmtes Produkt" },
			{language: "de", key: "show_discount_code", value: "Rabattcode anzeigen" },
			{language: "de", key: "condition_click_to_redeem", value: "Klicken Sie, um einzutreten" },
			{language: "de", key: "condition_auto_redeem", value: "Du bist automatisch eingetragen!" },
			{language: "de", key: "condition_successfully_redeemed", value: "Sie wurden in die Verlosung eingetragen!"},
			{language: "de", key: "condition_dollar_spent", value: `Tätige einen Kauf, um {{pointsName}} für jeden {{currency_value:1}} ausgegebenen zu verdienen`},
			{language: "de", key: "condition_welcome_bonus", value: `Verdiene Belohnungen, wenn du beitrittst`},
			{language: "de", key: "condition_product_review", value: "Hinterlassen Sie eine Bewertung, um eine Belohnung zu erhalten"},
			{language: "de", key: "condition_product_photo_review", value: `Hinterlassen Sie eine Bewertung, um eine Belohnung zu erhalten`},
			{language: "de", key: "condition_birthday_bonus", value: `Verdiene Belohnungen an deinem Geburtstag`},
			{ language: "de", key: "enter_birthday_text", value: "Geben Sie Ihr Geburtsdatum ein" },
			{language: "de", key: "condition_nth_purchase", value: `Tätige {{counts}} Käufe`},
			{language: "de", key: "condition_nth_purchase_single", value: "Machen Sie {{counts}} Einkauf"},
			{language: "de", key: "condition_nth_purchase_progress", value: "Machen Sie noch {{counts}} Einkäufe"},
			{language: "de", key: "condition_nth_purchase_progress_single", value: "Machen Sie noch {{counts}} Einkauf"},
			{language: "de", key: "condition_nth_purchase_progress_complete", value: "Ziel erreicht"},
			{language: "de", key: "condition_timed_purchase", value: `Tätige {{orderCounts}} Käufe in {{daysUntilEnd}} Tagen`},
			{language: "de", key: "condition_timed_purchase_single", value: "Machen Sie {{orderCounts}} Einkauf in {{daysUntilEnd}} Tagen"},
			{language: "de", key: "condition_follow_on_instagram", value: `<a href="https://www.instagram.com/{{handle}}" target="_blank" class="raleon-social-wte">Folge @{{handle}} auf Instagram</a>`},
			{language: "de", key: "condition_follow_on_tiktok", value: `<a href="https://www.tiktok.com/{{handle}}" target="_blank" class="raleon-social-wte">Folge {{handle}} auf TikTok</a>`},
			{language: "de", key: "condition_follow_on_facebook", value: `<a href="https://www.facebook.com/{{handle}}" target="_blank" class="raleon-social-wte">Gefällt mir {{handle}} auf Facebook</a>`},
			{language: "de", key: "condition_follow_facebook_group", value: `<a href="https://www.facebook.com/groups/{{handle}}" target="_blank" class="raleon-social-wte">Treten Sie {{handle}} bei Facebook bei</a>`},
			{language: "de", key: "condition_follow_on_youtube", value: `<a href="https://www.youtube.com/{{handle}}" target="_blank" class="raleon-social-wte">Abonnieren Sie {{handle}} auf YouTube</a>`},
			{ "language": "de", "key": "condition_follow_on_custom", "value": "<a href=\"{{url}}\" target=\"_blank\" class=\"raleon-social-wte\">Folge uns</a>" },
			{language: "de", key: "condition_short_nth_purchase", value: `{{count}} Kauf`},
			{language: "de", key: "condition_short_follow_on_instagram", value: `Folge @{{handle}}`},
			{language: "de", key: "condition_short_follow_on_tiktok", value: `Folge {{handle}}`},
			{language: "de", key: "condition_short_follow_on_facebook", value: `Gefällt mir {{handle}}`},
			{ "language": "de", "key": "condition_short_follow_on_youtube", "value": "Abonniere {{handle}}" },
			{ "language": "de", "key": "condition_short_follow_on_custom", "value": "Folge uns" },
			{language: "de", key: "condition_specific_product_purchase", value: `Spezifisches <a href="https://{{domain}}/products/{{handle}}" class="raleon-social-wte">Produkt kaufen</a>`},
			{language: "de", key: "condition_collection_purchase", value: `Kaufe {{count}} aus der <a href="https://{{domain}}/collections/{{handle}}" class="raleon-social-wte">Kollektion</a>`},
			{language: "de", key: "condition_specific_product_short_purchase", value: `Produkt kaufen`},
			{language: "de", key: "condition_collection_short_purchase", value: `{{count}} aus der Kollektion`},
			{language: "de", key: "condition_referrer_bonus", value: "Werben Sie einen Freund"},
			{language: "de", key: "condition_subscription_purchase", value: "Tätigen Sie einen Abonnementkauf"},
			{language: "de", key: "condition_subscription_purchase_multiple", value: "Tätigen Sie {{value}} Abonnementkäufe"},
			{language: "de", key: "referral_bonus_reward_name", value: "{{value}} Rabatt"},
			{language: "de", key: "referral_bonus_reward_description", value: "Erhalten Sie {{value}} Rabatt auf Ihren nächsten Einkauf, wenn Sie einen Freund werben"},
			{language: "de", key: "referral_bonus_reward_points_description", value: "Erhalten Sie {{value}} wenn Sie einen Freund werben"},
			{language: "de", key: "points_ttm_earned", value: "{{points}} {{pointsName}} verdient"},
			{language: "de", key: "checkout_ui_points_header", value: "Sie haben {{points}} {{points_name}}"},
			{language: "de", key: "checkout_ui_login_link_prefix", value: "Verpassen Sie nicht!"},
			{language: "de", key: "checkout_ui_login_link_text", value: "Einloggen"},
			{language: "de", key: "checkout_ui_login_link_suffix", value: " um Belohnungen zu verdienen und einzulösen"},
			{language: "de", key: "checkout_ui_not_enough_points_warning", value: "Sie benötigen {{points}} mehr {{points_name}}, um eine Belohnung einzulösen"},
			{language: "de", key: "checkout_ui_rewards_dropdown_label", value: "Belohnungen"},
			{language: "de", key: "checkout_ui_rewards_dropdown_placeholder", value: "Wählen Sie eine Belohnung"},
			{language: "de", key: "checkout_ui_rewards_dropdown_item_text", value: "{{reward_name}} - {{points}} {{points_name}}"},
			{language: "de", key: "checkout_ui_apply_reward_button_text", value: "Anwenden"},
			{language: "de", key: "checkout_ui_rewards_dropdown_item_in_use", value: "{{reward_name}} - In Verwendung"},
			{language: "de", key: "checkout_ui_stackability_replacement_warning_title", value: "Gutscheine Ersetzt"},
			{language: "de", key: "checkout_ui_stackability_replacement_warning", value: "Einer deiner Gutscheine ist nicht stapelbar. Wir haben den besseren Gutschein für größere Einsparungen angewendet."},
			{language: "de", key: "checkout_ui_stackability_replacement_warning_replacements", value: "Was wir ersetzt haben:"},
			{language: "de", key: "checkout_ui_stackability_replacement_warning_undo", value: "Rückgängig machen"},
			{language: "de", key: "checkout_ui_stackability_error_title", value: "Gutschein Nicht Angewendet"},
			{language: "de", key: "checkout_ui_stackability_error", value: "Einer deiner Gutscheine ist nicht stapelbar."},
			{language: "de", key: "checkout_ui_stackability_error_line_2", value: "Dein aktueller Gutschein bietet einen besseren Rabatt. Wir haben ihn für dich behalten."},
			{language: "de", key: "checkout_ui_apply_error_title", value: "Gutschein Konnte Nicht Angewendet Werden"},
			{language: "de", key: "checkout_ui_apply_error", value: "Die ausgewählte Belohnung konnte nicht angewendet werden. Bitte versuche es erneut."},
			{ "language": "de", "key": "coupon_waiting", "value": "Melde dich an, um dein Angebot zu beanspruchen. Klicke hier, um beizutreten." },
			{language: "de", key: "coupon_added_to_cart", value: "Belohnung zum Warenkorb hinzugefügt"},
			{
				"language": "de",
				"key": "product_page_loyalty_banner_text",
				"value": "Treue"
			},
			{
				"language": "de",
				"key": "product_page_guest_earn_teaser",
				"value": "Verdiene {{points}} {{pointsName}} und mehr von diesem Artikel"
			},
			{
				"language": "de",
				"key": "product_page_guest_join",
				"value": "Treueprogramm beitreten"
			},
			{
				"language": "de",
				"key": "product_page_points_deficit",
				"value": "Du benötigst {{points}} weitere {{pointsName}}, um {{rewardName}} einzulösen"
			},
			{
				"language": "de",
				"key": "product_page_claim_and_apply_reward",
				"value": "{{rewardName}} beanspruchen & anwenden"
			},
			{
				"language": "de",
				"key": "product_page_apply_reward",
				"value": "Deinen {{rewardName}} anwenden"
			},
			{
				"language": "de",
				"key": "product_page_reward_applied",
				"value": "Dein {{rewardName}} wurde angewendet!"
			},
			{
				"language": "de",
				"key": "product_page_points_balance",
				"value": "Du hast {{points}} {{points_name}}"
			},
			{
				"language": "de",
				"key": "product_page_rewards_dropdown_label",
				"value": "Belohnungen"
			},
			{
				"language": "de",
				"key": "product_page_rewards_dropdown_placeholder",
				"value": "Wähle eine Belohnung aus"
			},
			{
				"language": "de",
				"key": "product_page_rewards_dropdown_item_text",
				"value": "{{reward_name}} - {{points}} {{points_name}}"
			},
			{language: "de", key: "product_page_rewards_dropdown_item_in_use", value: "{{reward_name}} - In Verwendung"},
			{
				"language": "de",
				"key": "thankyou_would_have_earned_estimate",
				"value": "Sie hätten mindestens {{estimate}} Punkte mit diesem Kauf verdient"
			},
			{
				"language": "de",
				"key": "thankyou_would_have_earned_exact",
				"value": "Sie hätten {{points}} Punkte mit diesem Kauf verdient"
			},
			{
				"language": "de",
				"key": "thankyou_earned_estimate",
				"value": "Sie haben mindestens {{estimate}} Punkte mit diesem Kauf verdient"
			},
			{
				"language": "de",
				"key": "thankyou_earned_exact",
				"value": "Sie haben {{points}} Punkte mit diesem Kauf verdient"
			},
			{
				"language": "de",
				"key": "thankyou_vip_perk",
				"value": "Genießen Sie Ihr {{vipTier}} Tier-Vorteil, {{vipPerk}} beim nächsten Kauf"
			},
			{
				"language": "de",
				"key": "thankyou_points_deficit",
				"value": "Verdiene noch {{deficit}} Punkte mehr, um einen {{reward}} für {{price}} {{pointsName}} einzulösen"
			},
			{
				"language": "de",
				"key": "thankyou_points_enough",
				"value": "Sie haben genug Punkte, um einen {{reward}} für {{price}} {{pointsName}} einzulösen"
			},{
				"language": "de",
				"key": "thankyou_top_text_logged_in",
				"value": "Prämienupdate für dich, {{name}}"
			},
			{
				"language": "de",
				"key": "thankyou_top_text_logged_out",
				"value": "Verpasse nicht die Prämien!"
			},
			{
				"language": "de",
				"key": "thankyou_points_summary_line_1_estimate",
				"value": "Mindestens {{points}} {{pointsName}}"
			},
			{
				"language": "de",
				"key": "thankyou_points_summary_line_1",
				"value": "{{points}} {{pointsName}}"
			},
			{
				"language": "de",
				"key": "thankyou_points_summary_line_2_earned",
				"value": "verdient"
			},
			{
				"language": "de",
				"key": "thankyou_points_summary_line_2_would_have_earned",
				"value": "warten auf dich"
			},
			{
				"language": "de",
				"key": "thankyou_reward_summary_line_1_deficit",
				"value": "{{points}} mehr {{pointsName}}"
			},
			{
				"language": "de",
				"key": "thankyou_reward_summary_line_1_enough",
				"value": "Du hast genug {{pointsName}}"
			},
			{
				"language": "de",
				"key": "thankyou_reward_summary_line_2",
				"value": "für {{rewardName}}"
			},
			{
				"language": "de",
				"key": "thankyou_claim_link",
				"value": "Fordere deine {{points}} {{pointsName}} ein"
			},
			{language: "de", key: "thankyou_reward_summary_line_2_tier", value: "bis du {{tierName}} erreichst"},
			{language: "de", key: "view_external_link", value: "Erhalten Sie weitere Details"},
			{ language: "de", key: "cart_free_gift_threshold_not_met_choice", value: "Füge {{amount}} mehr in deinen Warenkorb hinzu, um ein {{gift_name}} zu erhalten" },
			{ language: "de", key: "cart_free_gift_threshold_not_met_single", value: "Füge {{amount}} mehr in deinen Warenkorb hinzu, um ein {{gift_name}} zu erhalten" },
			{ language: "de", key: "cart_free_gift_threshold_met_choice", value: "Du hast ein {{gift_name}} verdient. Es wurde automatisch deinem Warenkorb hinzugefügt" },
			{ language: "de", key: "cart_free_gift_threshold_met_single", value: "Du hast ein {{gift_name}} verdient. Es wurde automatisch deinem Warenkorb hinzugefügt" },
			{ language: "de", key: "free_gift", value: "Gratisgeschenk" },



			// Hindi - Static
			{language: "hi", key: "join", value: "शामिल हों"},
			{language: "hi", key: "sign_in", value: "साइन इन करें"},
			{language: "hi", key: "loyalty_by_raleon_link", value: "{{link}} द्वारा वफादारी ✨"},
			{language: "hi", key: "home_available_points", value: "उपलब्ध {{pointsName}}"},
			{language: "hi", key: "earn_summary_ways_to_earn", value: "कमाने के तरीके"},
			{language: "hi", key: "free_product_from_collection_name", value: "{{value}} संग्रह से मुफ्त उत्पाद"},
			{language: "hi", key: "inventory_summary_no_rewards", value: "आपके पास अभी तक कोई पुरस्कार नहीं हैं।"},
			{language: "hi", key: "points", value: "अंक"},
			{language: "hi", key: "points_abbreviated", value: "अंक"},
			{language: "hi", key: "rewards", value: "पुरस्कार"},
			{language: "hi", key: "redeem_rewards", value: "पुरस्कार रिडीम करें"},
			{language: "hi", key: "see_all", value: "सभी देखें"},
			{language: "hi", key: "copy_referral_link", value: "अनूठा रेफरल लिंक कॉपी करें"},
			{language: "hi", key: "copied_referral_link", value: "क्लिपबोर्ड पर कॉपी किया गया"},
			{language: "hi", key: "referral_socials", value: "या सोशल मीडिया के माध्यम से भेजें"},
			{language: "hi", key: "full_inventory_my_inventory", value: "मेरी इन्वेंटरी"},
			{language: "hi", key: "reward_detail_minimum_order_total", value: "{{minOrderTotal}} का न्यूनतम आदेश राशि।"},
			{language: "hi", key: "reward_detail_claim_count_current", value: "आपने {{redeemed}} / {{maxUserRedemptions}} दावे किए हैं।"},
			{language: "hi", key: "reward_detail_expiration_summary", value: "दावा करने के {{expiresInDays}} दिनों बाद समाप्त हो जाता है।"},
			{language: "hi", key: "reward_detail_your_next_purchase", value: "आपकी अगली खरीदारी"},
			{language: "hi", key: "reward_detail_insufficient_points", value: "अपर्याप्त {{pointsName}}"},
			{language: "hi", key: "reward_detail_claim_button_alt", value: "दावा करें और लागू करें"},
			{language: "hi", key: "youve_got_rewards", value: "आपके पास पुरस्कार हैं!"},
			{language: "hi", key: "apply_to_cart", value: "कार्ट में लागू करें"},
			{language: "hi", key: "applied_to_cart", value: "पहले से कार्ट में"},
			{language: "hi", key: "apply_to_cart_description", value: "नीचे 'कार्ट में लागू करें' पर क्लिक करने पर {{itemName}} कूपन आपके कार्ट में लागू हो जाएगा।"},
			{language: "hi", key: "on_next_purchase", value: "आपकी अगली खरीद पर।"},
			{language: "hi", key: "spend_minimum", value: "इस इनाम का उपयोग करने के लिए आपको कम से कम {{minOrderTotal}} खर्च करना होगा"},
			{language: "hi", key: "choose_product_from_collection", value: "{{collection}} संग्रह से एक उत्पाद चुनें"},
			{language: "hi", key: "choose_free_product", value: "अपना मुफ्त उत्पाद चुनें"},
			{language: "hi", key: "choose_dollar_off_product", value: "लागू करें और उत्पाद देखें"},
			{language: "hi", key: "choose_percent_off_product", value: "लागू करें और उत्पाद देखें"},
			{language: "hi", key: "coupon_expiration", value: "यह {{timeValue}} {{timeLabel}} में समाप्त हो जाएगा।"},
			{language: "hi", key: "coupon", value: "कूपन"},
			{language: "hi", key: "youve_got_referral_rewards", value: "आपके दोस्त ने आपको एक रेफरल इनाम भेजा है, इसे दावा करने के लिए यहाँ क्लिक करें या साइन अप करें"},
			{language: "hi", key: "rewards_unlocked", value: "पुरस्कार अनलॉक किए गए!"},
			{language: "hi", key: "vip-tiers-title", value: "सदस्यता स्तर"},
			{language: "hi", key: "points-ttm", value: "{{pointsName}}"},
			{language: "hi", key: "perks_title", value: "लाभ"},
			{language: "hi", key: "points_to_next_tier", value: "अगले स्तर तक के {{pointsName}}"},
			{language: "hi", key: "learn_more_about_tiers", value: "स्तरों के बारे में और जानें"},
			{language: "hi", key: "referral_sign_in", value: "दोस्त को रेफर करने के लिए साइन इन करें"},
			{language: "hi", key: "email_placeholder_referral", value: "आपके दोस्त का ईमेल"},
			{language: "hi", key: "referral_send", value: "निमंत्रण भेजें"},
			{language: "hi", key: "referral_sent", value: "निमंत्रण भेजा गया"},
			{language: "hi", key: "subview_loyalty", value: "निष्ठा" },
			{language: "hi", key: "subview_vip", value: "वीआईपी स्तर" },
			{language: "hi", key: "subview_history", value: "इतिहास" },
			{language: "hi", key: "subview_my_rewards", value: "मेरे इनाम" },
			{ "language": "hi", "key": "coupon_waiting", "value": "अपने ऑफर का दावा करने के लिए साइन इन करें। जुड़ने के लिए यहां क्लिक करें।" },
			{language: "hi", key: "coupon_added_to_cart", value: "इनाम को कार्ट में जोड़ा गया"},
			{language: "hi", key: "product_page_guest_earn_teaser_divider", value: "|"},

			// Hindi - "Generated"
			{language: "hi", key: "reward_pill_points", value: "{{value}} {{pointsAbbreviated}}"},
			{language: "hi", key: "reward_pill_points_per_dollar", value: "{{value}} {{pointsAbbreviated}}/{{prefix}}{{postfix}}"},
			{language: "hi", key: "reward_pill_dollars_off_coupon", value: "{{prefix}}{{value}} छूट"},
			{language: "hi", key: "reward_pill_percent_discount", value: "{{value}}% छूट"},
			{language: "hi", key: "reward_pill_free_shipping", value: "मुफ्त शिपिंग"},
			{language: "hi", key: "reward_pill_free_product", value: "मुफ्त उत्पाद"},
			{ "language": "hi", "key": "reward_pill_percent_off_product", "value": "% छूट उत्पाद पर" },
  			{ "language": "hi", "key": "reward_pill_dollar_off_product", "value": "डॉलर छूट उत्पाद पर" },
			{language: "hi", key: "reward_pill_giveaway_entry", value: "१ गिवअवे प्रवेश" },
			{language: "hi", key: "reward_pill_giveaway_entry_multiple", value: "{{count}} गिवअवे प्रवेश" },
			{language: "hi", key: "reward_points_per_dollar", value: "{{dollarName}} खर्च करने पर {{points}} {{pointsName}} कमाएं"},
			{language: "hi", key: "reward_points", value: "{{points}} {{pointsName}} कमाएं"},
			{language: "hi", key: "reward_dollars_off_coupon", value: "{{prefix}}{{postfix}}{{amount}} छूट का कूपन कमाएं"},
			{language: "hi", key: "reward_percent_discount", value: "{{amount}}% छूट का कूपन कमाएं"},
			{ language: "hi", key: "reward_product_percent_discount", value: "एक विशिष्ट उत्पाद के लिए {{amount}}% की छूट कूपन प्राप्त करें" },
  			{ language: "hi", key: "reward_product_dollar_discount", value: "एक विशिष्ट उत्पाद के लिए {{prefix}}{{postfix}}{{amount}} की छूट कूपन प्राप्त करें" },
			{language: "hi", key: "show_discount_code", value: "छूट कोड दिखाएं" },
			{language: "hi", key: "condition_click_to_redeem", value: "प्रवेश करने के लिए क्लिक करें" },
			{language: "hi", key: "condition_successfully_redeemed", value: "आपको गिवअवे में दर्ज किया गया है!"},
			{language: "hi", key: "condition_auto_redeem", value: "आप स्वचालित रूप से प्रवेश कर चुके हैं!" },
			{language: "hi", key: "condition_dollar_spent", value: `हर {{currency_value:1}} खर्च करने पर {{pointsName}} कमाने के लिए खरीदारी करें`},
			{language: "hi", key: "condition_welcome_bonus", value: `जुड़ने पर इनाम प्राप्त करें`},
			{language: "hi", key: "condition_product_review", value: "इनाम पाने के लिए समीक्षा छोड़ें"},
			{language: "hi", key: "condition_product_photo_review", value: `इनाम पाने के लिए समीक्षा छोड़ें`},
			{language: "hi", key: "condition_birthday_bonus", value: `अपने जन्मदिन पर पुरस्कार कमाएं`},
			{ language: "hi", key: "enter_birthday_text", value: "अपनी जन्मतिथि दर्ज करें" },
			{language: "hi", key: "condition_nth_purchase", value: `{{counts}} खरीदारी करें`},
			{language: "hi", key: "condition_nth_purchase_single", value: "{{counts}} खरीदी करें"},
			{language: "hi", key: "condition_nth_purchase_progress", value: "{{counts}} और खरीदारी करें"},
			{language: "hi", key: "condition_nth_purchase_progress_single", value: "{{counts}} और खरीदारी करें"},
			{language: "hi", key: "condition_nth_purchase_progress_complete", value: "लक्ष्य प्राप्त हुआ"},
			{language: "hi", key: "condition_timed_purchase", value: `{{daysUntilEnd}} दिनों में {{orderCounts}} खरीदारी करें`},
			{language: "hi", key: "condition_timed_purchase_single", value: "{{orderCounts}} खरीदी करें {{daysUntilEnd}} दिनों में"},
			{language: "hi", key: "condition_follow_on_instagram", value: `<a href="https://www.instagram.com/{{handle}}" target="_blank" class="raleon-social-wte"Instagram पर @{{handle}} को फॉलो करें</a>`},
			{language: "hi", key: "condition_follow_on_tiktok", value: `<a href="https://www.tiktok.com/{{handle}}" target="_blank" class="raleon-social-wte">TikTok पर {{handle}} को फॉलो करें</a>`},
			{language: "hi", key: "condition_follow_on_facebook", value: `<a href="https://www.facebook.com/{{handle}}" target="_blank" class="raleon-social-wte">Facebook पर {{handle}} को लाइक करें</a>`},
			{language: "hi", key: "condition_follow_facebook_group", value: `<a href="https://www.facebook.com/groups/{{handle}}" target="_blank" class="raleon-social-wte">फेसबुक पर {{handle}} में शामिल हों</a>`},
			{language: "hi", key: "condition_follow_on_youtube", value: `<a href="https://www.youtube.com/{{handle}}" target="_blank" class="raleon-social-wte">{{handle}} को YouTube पर सब्सक्राइब करें</a>`},
			{ "language": "hi", "key": "condition_follow_on_custom", "value": "<a href=\"{{url}}\" target=\"_blank\" class=\"raleon-social-wte\">हमें फॉलो करें</a>" },
			{language: "hi", key: "condition_short_nth_purchase", value: `{{count}} खरीदारी`},
			{language: "hi", key: "condition_short_follow_on_instagram", value: `@{{handle}} को फॉलो करें`},
			{language: "hi", key: "condition_short_follow_on_tiktok", value: `{{handle}} को फॉलो करें`},
			{language: "hi", key: "condition_short_follow_on_facebook", value: `{{handle}} को लाइक करें`},
			{ "language": "hi", "key": "condition_short_follow_on_youtube", "value": "{{handle}} को सब्सक्राइब करें" },
			{ "language": "hi", "key": "condition_short_follow_on_custom", "value": "हमें फॉलो करें" },
			{language: "hi", key: "condition_specific_product_purchase", value: `विशेष <a href="https://{{domain}}/products/{{handle}}" class="raleon-social-wte">उत्पाद खरीदें</a>`},
			{language: "hi", key: "condition_collection_purchase", value: `संग्रह से {{count}} <a href="https://{{domain}}/collections/{{handle}}" class="raleon-social-wte">खरीदें</a>`},
			{language: "hi", key: "condition_specific_product_short_purchase", value: `उत्पाद खरीदें`},
			{language: "hi", key: "condition_collection_short_purchase", value: `संग्रह से {{count}}`},
			{language: "hi", key: "condition_referrer_bonus", value: "एक मित्र को रेफर करें"},
			{language: "hi", key: "condition_subscription_purchase", value: "सदस्यता की खरीदारी करें"},
			{language: "hi", key: "condition_subscription_purchase_multiple", value: "{{value}} सदस्यता खरीदें"},
			{language: "hi", key: "referral_bonus_reward_name", value: "{{value}} की छूट"},
			{language: "hi", key: "referral_bonus_reward_description", value: "जब आप एक मित्र को रेफर करते हैं तो अपनी अगली खरीद पर {{value}} की छूट पाएं"},
			{language: "hi", key: "referral_bonus_reward_points_description", value: "जब आप किसी मित्र का संदर्भ देते हैं तो {{value}} प्राप्त करें"},
			{language: "hi", key: "points_ttm_earned", value: "{{points}} {{pointsName}} अर्जित किए गए"},
			{language: "hi", key: "checkout_ui_points_header", value: "आपके पास {{points}} {{points_name}} हैं"},
			{language: "hi", key: "checkout_ui_login_link_prefix", value: "मिस मत करो!"},
			{language: "hi", key: "checkout_ui_login_link_text", value: "लॉगिन करें"},
			{language: "hi", key: "checkout_ui_login_link_suffix", value: " पुरस्कार कमाने और भुनाने के लिए"},
			{language: "hi", key: "checkout_ui_not_enough_points_warning", value: "इनाम भुनाने के लिए आपको और {{points}} {{points_name}} की आवश्यकता है"},
			{language: "hi", key: "checkout_ui_rewards_dropdown_label", value: "पुरस्कार"},
			{language: "hi", key: "checkout_ui_rewards_dropdown_placeholder", value: "एक पुरस्कार चुनें"},
			{language: "hi", key: "checkout_ui_rewards_dropdown_item_text", value: "{{reward_name}} - {{points}} {{points_name}}"},
			{language: "hi", key: "checkout_ui_apply_reward_button_text", value: "लागू करें"},
			{language: "hi", key: "checkout_ui_rewards_dropdown_item_in_use", value: "{{reward_name}} - प्रयोग में है"},
			{language: "hi", key: "checkout_ui_stackability_replacement_warning_title", value: "कूपन्स बदल दिए गए"},
			{language: "hi", key: "checkout_ui_stackability_replacement_warning", value: "आपके एक कूपन को जोड़ा नहीं जा सकता। हमने बेहतर बचत के लिए बेहतर कूपन लागू किया।"},
			{language: "hi", key: "checkout_ui_stackability_replacement_warning_replacements", value: "हमने क्या बदला:"},
			{language: "hi", key: "checkout_ui_stackability_replacement_warning_undo", value: "पूर्ववत करें"},
			{language: "hi", key: "checkout_ui_stackability_error_title", value: "कूपन लागू नहीं किया गया"},
			{language: "hi", key: "checkout_ui_stackability_error", value: "आपके एक कूपन को जोड़ा नहीं जा सकता।"},
			{language: "hi", key: "checkout_ui_stackability_error_line_2", value: "आपके मौजूदा कूपन पर बेहतर छूट है। हमने इसे आपके लिए रखा है।"},
			{language: "hi", key: "checkout_ui_apply_error_title", value: "कूपन लागू नहीं किया जा सका"},
			{language: "hi", key: "checkout_ui_apply_error", value: "आपके द्वारा चुना गया इनाम लागू नहीं किया जा सका। कृपया पुनः प्रयास करें।"},
			{"language": "hi", "key": "product_page_loyalty_banner_text", "value": "वफादारी"},
			{"language": "hi", "key": "product_page_guest_earn_teaser", "value": "इस आइटम से {{points}} {{pointsName}} और अधिक कमाएँ"},
			{"language": "hi", "key": "product_page_guest_join", "value": "वफादारी में शामिल हों"},
			{"language": "hi", "key": "product_page_points_deficit", "value": "{{rewardName}} को रिडीम करने के लिए आपको और {{points}} {{pointsName}} की आवश्यकता है"},
			{"language": "hi", "key": "product_page_claim_and_apply_reward", "value": "दावा करें & लागू करें {{rewardName}}"},
			{"language": "hi", "key": "product_page_apply_reward", "value": "अपना {{rewardName}} लागू करें"},
			{"language": "hi", "key": "product_page_reward_applied", "value": "लागू किया गया आपका {{rewardName}}!"},
			{"language": "hi", "key": "product_page_points_balance", "value": "आपके पास {{points}} {{points_name}} हैं"},
			{"language": "hi", "key": "product_page_rewards_dropdown_label", "value": "पुरस्कार"},
			{"language": "hi", "key": "product_page_rewards_dropdown_placeholder", "value": "एक पुरस्कार चुनें"},
			{"language": "hi", "key": "product_page_rewards_dropdown_item_text", "value": "{{reward_name}} - {{points}} {{points_name}}"},
			{language: "hi", key: "product_page_rewards_dropdown_item_in_use", value: "{{reward_name}} - प्रयोग में है"},
			{
				"language": "hi",
				"key": "thankyou_would_have_earned_estimate",
				"value": "आपको इस खरीद पर कम से कम {{estimate}} अंक मिलते"
			},
			{
				"language": "hi",
				"key": "thankyou_would_have_earned_exact",
				"value": "आपको इस खरीद पर {{points}} अंक मिलते"
			},
			{
				"language": "hi",
				"key": "thankyou_earned_estimate",
				"value": "आपने इस खरीद पर कम से कम {{estimate}} अंक कमाए"
			},
			{
				"language": "hi",
				"key": "thankyou_earned_exact",
				"value": "आपने इस खरीद पर {{points}} अंक कमाए"
			},
			{
				"language": "hi",
				"key": "thankyou_vip_perk",
				"value": "अपने {{vipTier}} स्तर के फायदे, अगली खरीद पर {{vipPerk}} का आनंद लें "
			},
			{
				"language": "hi",
				"key": "thankyou_points_deficit",
				"value": "{{price}} {{pointsName}} के लिए एक {{reward}} रिडीम करने के लिए और {{deficit}} अंक कमाएं"
			},
			{
				"language": "hi",
				"key": "thankyou_points_enough",
				"value": "आपके पास {{price}} {{pointsName}} के लिए एक {{reward}} रिडीम करने के लिए पर्याप्त अंक हैं"
			},
			{
				"language": "hi",
				"key": "thankyou_top_text_logged_in",
				"value": "पुरस्कार अद्यतन आपके लिए, {{name}}"
			},
			{
				"language": "hi",
				"key": "thankyou_top_text_logged_out",
				"value": "पुरस्कारों को मत छोड़ें!"
			},
			{
				"language": "hi",
				"key": "thankyou_points_summary_line_1_estimate",
				"value": "कम से कम {{points}} {{pointsName}}"
			},
			{
				"language": "hi",
				"key": "thankyou_points_summary_line_1",
				"value": "{{points}} {{pointsName}}"
			},
			{
				"language": "hi",
				"key": "thankyou_points_summary_line_2_earned",
				"value": "अर्जित"
			},
			{
				"language": "hi",
				"key": "thankyou_points_summary_line_2_would_have_earned",
				"value": "आपका इंतजार कर रहे हैं"
			},
			{
				"language": "hi",
				"key": "thankyou_reward_summary_line_1_deficit",
				"value": "{{points}} अधिक {{pointsName}}"
			},
			{
				"language": "hi",
				"key": "thankyou_reward_summary_line_1_enough",
				"value": "आपके पास पर्याप्त {{pointsName}} हैं"
			},
			{
				"language": "hi",
				"key": "thankyou_reward_summary_line_2",
				"value": "{{rewardName}} के लिए"
			},
			{
				"language": "hi",
				"key": "thankyou_claim_link",
				"value": "अपने {{points}} {{pointsName}} का दावा करें"
			},
			{language: "hi", key: "thankyou_reward_summary_line_2_tier", value: "जब तक आप {{tierName}} तक पहुंचते हैं"},
			{language: "hi", key: "view_external_link", value: "अधिक विवरण प्राप्त करें"},
			{ language: "hi", key: "cart_free_gift_threshold_not_met_choice", value: "{{gift_name}} पाने के लिए अपनी कार्ट में {{amount}} और जोड़ें" },
			{ language: "hi", key: "cart_free_gift_threshold_not_met_single", value: "{{gift_name}} पाने के लिए अपनी कार्ट में {{amount}} और जोड़ें" },
			{ language: "hi", key: "cart_free_gift_threshold_met_choice", value: "आपने एक {{gift_name}} अर्जित किया है। इसे स्वचालित रूप से आपकी कार्ट में जोड़ दिया गया है" },
			{ language: "hi", key: "cart_free_gift_threshold_met_single", value: "आपने एक {{gift_name}} अर्जित किया है। इसे स्वचालित रूप से आपकी कार्ट में जोड़ दिया गया है" },
			{ language: "hi", key: "free_gift", value: "मुफ्त उपहार" },





			// Chinese - Static
			{language: "zh", key: "join", value: "加入"},
			{language: "zh", key: "sign_in", value: "登录"},
			{language: "zh", key: "loyalty_by_raleon_link", value: "忠诚度 ✨ 由 {{link}}"},
			{language: "zh", key: "home_available_points", value: "可用 {{pointsName}}"},
			{language: "zh", key: "earn_summary_ways_to_earn", value: "赚取方式"},
			{language: "zh", key: "free_product_from_collection_name", value: "来自{{value}}系列的免费产品"},
			{language: "zh", key: "inventory_summary_no_rewards", value: "您还没有任何奖励。"},
			{language: "zh", key: "points", value: "积分"},
			{language: "zh", key: "points_abbreviated", value: "分"},
			{language: "zh", key: "rewards", value: "奖励"},
			{language: "zh", key: "redeem_rewards", value: "兑换奖励"},
			{language: "zh", key: "see_all", value: "查看全部"},
			{language: "zh", key: "copy_referral_link", value: "复制独特的推荐链接"},
			{language: "zh", key: "copied_referral_link", value: "已复制到剪贴板"},
			{language: "zh", key: "referral_socials", value: "或通过社交媒体发送"},
			{language: "zh", key: "full_inventory_my_inventory", value: "我的库存"},
			{language: "zh", key: "reward_detail_minimum_order_total", value: "最低订单金额 {{minOrderTotal}}。"},
			{language: "zh", key: "reward_detail_claim_count_current", value: "您已领取 {{redeemed}} / {{maxUserRedemptions}}。"},
			{language: "zh", key: "reward_detail_expiration_summary", value: "领取后 {{expiresInDays}} 天到期。"},
			{language: "zh", key: "reward_detail_your_next_purchase", value: "您的下一次购买"},
			{language: "zh", key: "reward_detail_insufficient_points", value: "{{pointsName}}不足"},
			{language: "zh", key: "reward_detail_claim_button_alt", value: "申领并应用"},
			{language: "zh", key: "youve_got_rewards", value: "您有奖励了！"},
			{language: "zh", key: "apply_to_cart", value: "应用到购物车"},
			{language: "zh", key: "applied_to_cart", value: "已在购物车中"},
			{language: "zh", key: "apply_to_cart_description", value: "点击下方的“应用到购物车”将会将{{itemName}}优惠券应用于您的购物车。"},
			{language: "zh", key: "on_next_purchase", value: "在您的下一次购买时。"},
			{language: "zh", key: "spend_minimum", value: "您必须至少花费{{minOrderTotal}}才能使用此奖励"},
			{language: "zh", key: "choose_product_from_collection", value: "从{{collection}}系列选择产品"},
			{language: "zh", key: "choose_free_product", value: "选择您的免费产品"},
			{language: "zh", key: "choose_dollar_off_product", value: "应用并查看产品"},
			{language: "zh", key: "choose_percent_off_product", value: "应用并查看产品"},
			{language: "zh", key: "coupon_expiration", value: "此券将在{{timeValue}}{{timeLabel}}后过期。"},
			{language: "zh", key: "coupon", value: "优惠券"},
			{language: "zh", key: "youve_got_referral_rewards", value: "您的朋友发送给您一个推荐奖励，点击这里或注册以领取"},
			{language: "zh", key: "rewards_unlocked", value: "奖励解锁！"},
			{language: "zh", key: "vip-tiers-title", value: "会员等级"},
			{language: "zh", key: "points-ttm", value: "{{pointsName}}"},
			{language: "zh", key: "perks_title", value: "特权"},
			{language: "zh", key: "points_to_next_tier", value: "距离下一等级的{{pointsName}}"},
			{language: "zh", key: "learn_more_about_tiers", value: "了解更多关于等级的信息"},
			{language: "zh", key: "referral_sign_in", value: "登录以推荐朋友"},
			{language: "zh", key: "email_placeholder_referral", value: "您朋友的电子邮件"},
			{language: "zh", key: "referral_send", value: "发送邀请"},
			{language: "zh", key: "referral_sent", value: "邀请已发送"},
			{language: "zh", key: "subview_loyalty", value: "忠诚度" },
			{language: "zh", key: "subview_vip", value: "VIP等级" },
			{language: "zh", key: "subview_history", value: "历史记录" },
			{language: "zh", key: "subview_my_rewards", value: "我的奖励" },
			{ "language": "zh", "key": "coupon_waiting", "value": "登录以领取您的优惠。点击这里加入。" },
			{language: "zh", key: "coupon_added_to_cart", value: "奖励已添加到购物车"},



			//Chinese - "Generated"
			{language: "zh", key: "reward_pill_points", value: "{{value}} {{pointsAbbreviated}}"},
			{language: "zh", key: "reward_pill_points_per_dollar", value: "{{value}} {{pointsAbbreviated}}/{{prefix}}{{postfix}}"},
			{language: "zh", key: "reward_pill_dollars_off_coupon", value: "{{prefix}}{{value}} 折扣"},
			{language: "zh", key: "reward_pill_percent_discount", value: "{{value}}% 折扣"},
			{language: "zh", key: "reward_pill_free_shipping", value: "免费送货"},
			{language: "zh", key: "reward_pill_free_product", value: "免费产品"},
			{ "language": "zh", "key": "reward_pill_percent_off_product", "value": "产品百分比折扣" },
  			{ "language": "zh", "key": "reward_pill_dollar_off_product", "value": "产品美元折扣" },
			{language: "zh", key: "reward_pill_giveaway_entry", value: "1 次抽奖活动入口" },
			{language: "zh", key: "reward_pill_giveaway_entry_multiple", value: "{{count}} 次抽奖活动入口" },
			{language: "zh", key: "reward_points_per_dollar", value: "每 {{dollarName}} 消费赚取 {{points}} {{pointsName}}"},
			{language: "zh", key: "reward_points", value: "赚取 {{points}} {{pointsName}}"},
			{language: "zh", key: "reward_dollars_off_coupon", value: "赚取 {{prefix}}{{postfix}}{{amount}} 折扣券"},
			{language: "zh", key: "reward_percent_discount", value: "赚取 {{amount}}% 折扣券"},
			{ language: "zh", key: "reward_product_percent_discount", value: "获得特定产品的{{amount}}%折扣券" },
  			{ language: "zh", key: "reward_product_dollar_discount", value: "获得特定产品的{{prefix}}{{postfix}}{{amount}}折扣券" },
			{language: "zh", key: "show_discount_code", value: "显示折扣码" },
			{language: "zh", key: "condition_click_to_redeem", value: "点击进入" },
			{language: "zh", key: "condition_auto_redeem", value: "您已自动参加！" },
			{language: "zh", key: "condition_successfully_redeemed", value: "您已被录入抽奖中！"},
			{language: "zh", key: "condition_dollar_spent", value: `购买商品每 {{currency_value:1}} 消费赚取{{pointsName}}`},
			{language: "zh", key: "condition_welcome_bonus", value: `加入时赚取奖励`},
			{language: "zh", key: "condition_product_review", value: "留下评论以赚取奖励"},
			{language: "zh", key: "condition_product_photo_review", value: `留下评论以赚取奖励`},
			{language: "zh", key: "condition_birthday_bonus", value: `生日当天赚取奖励`},
			{ language: "zh", key: "enter_birthday_text", value: "输入您的生日" },
			{language: "zh", key: "condition_nth_purchase", value: `购买 {{counts}} 次`},
			{language: "zh", key: "condition_nth_purchase_single", value: "进行 {{counts}} 次购买"},
			{language: "zh", key: "condition_nth_purchase_progress", value: "再进行{{counts}}次购买"},
			{language: "zh", key: "condition_nth_purchase_progress_single", value: "再进行{{counts}}次购买"},
			{language: "zh", key: "condition_nth_purchase_progress_complete", value: "目标已达成"},
			{language: "zh", key: "condition_timed_purchase", value: `{{daysUntilEnd}} 天内购买 {{orderCounts}} 次`},
			{language: "zh", key: "condition_timed_purchase_single", value: "在 {{daysUntilEnd}} 天内进行 {{orderCounts}} 次购买"},
			{language: "zh", key: "condition_follow_on_instagram", value: `<a href="https://www.instagram.com/{{handle}}" target="_blank" class="raleon-social-wte">在 Instagram 上关注 @{{handle}}</a>`},
			{language: "zh", key: "condition_follow_on_tiktok", value: `<a href="https://www.tiktok.com/{{handle}}" target="_blank" class="raleon-social-wte">在 TikTok 上关注 {{handle}}</a>`},
			{language: "zh", key: "condition_follow_on_facebook", value: `<a href="https://www.facebook.com/{{handle}}" target="_blank" class="raleon-social-wte">在 Facebook 上点赞 {{handle}}</a>`},
			{language: "zh", key: "condition_follow_facebook_group", value: `<a href="https://www.facebook.com/groups/{{handle}}" target="_blank" class="raleon-social-wte">加入Facebook上的{{handle}}</a>`},
			{language: "zh", key: "condition_follow_on_youtube", value: `<a href="https://www.youtube.com/{{handle}}" target="_blank" class="raleon-social-wte">在YouTube上订阅{{handle}}</a>`},
			{ "language": "zh", "key": "condition_follow_on_custom", "value": "<a href=\"{{url}}\" target=\"_blank\" class=\"raleon-social-wte\">关注我们</a>" },
			{language: "zh", key: "condition_short_nth_purchase", value: `{{count}} 购买`},
			{language: "zh", key: "condition_short_follow_on_instagram", value: `关注 @{{handle}}`},
			{language: "zh", key: "condition_short_follow_on_tiktok", value: `关注 {{handle}}`},
			{language: "zh", key: "condition_short_follow_on_facebook", value: `点赞 {{handle}}`},
			{ "language": "zh", "key": "condition_short_follow_on_youtube", "value": "订阅{{handle}}" },
			{ "language": "zh", "key": "condition_short_follow_on_custom", "value": "关注我们" },
			{language: "zh", key: "condition_specific_product_purchase", value: `购买特定的<a href="https://{{domain}}/products/{{handle}}" class="raleon-social-wte">产品</a>`},
			{language: "zh", key: "condition_collection_purchase", value: `从<a href="https://{{domain}}/collections/{{handle}}" class="raleon-social-wte">系列</a>中购买 {{count}} 个`},
			{language: "zh", key: "condition_specific_product_short_purchase", value: `购买产品`},
			{language: "zh", key: "condition_collection_short_purchase", value: `从系列中 {{count}} 个`},
			{language: "zh", key: "condition_referrer_bonus", value: "推荐朋友"},
			{language: "zh", key: "condition_subscription_purchase", value: "进行订阅购买"},
			{language: "zh", key: "condition_subscription_purchase_multiple", value: "进行{{value}}次订阅购买"},
			{language: "zh", key: "referral_bonus_reward_name", value: "减免{{value}}"},
			{language: "zh", key: "referral_bonus_reward_description", value: "推荐朋友时，您的下一次购买可享{{value}}优惠"},
			{language: "zh", key: "referral_bonus_reward_points_description", value: "当你推荐朋友时，获取{{value}}"},
			{language: "zh", key: "points_ttm_earned", value: "获得了{{points}}分"},
			{language: "zh", key: "checkout_ui_points_header", value: "您有 {{points}} {{points_name}}"},
			{language: "zh", key: "checkout_ui_login_link_prefix", value: "不要错过!"},
			{language: "zh", key: "checkout_ui_login_link_text", value: "登入"},
			{language: "zh", key: "checkout_ui_login_link_suffix", value: " 以赚取和兑换奖励"},
			{language: "zh", key: "checkout_ui_not_enough_points_warning", value: "您需要再获得 {{points}} {{points_name}} 才能兑换奖励"},
			{language: "zh", key: "checkout_ui_rewards_dropdown_label", value: "奖励"},
			{language: "zh", key: "checkout_ui_rewards_dropdown_placeholder", value: "选择奖励"},
			{language: "zh", key: "checkout_ui_rewards_dropdown_item_text", value: "{{reward_name}} - {{points}} {{points_name}}"},
			{language: "zh", key: "checkout_ui_apply_reward_button_text", value: "应用"},
			{language: "zh", key: "checkout_ui_rewards_dropdown_item_in_use", value: "{{reward_name}} - 正在使用"},
			{language: "zh", key: "checkout_ui_stackability_replacement_warning_title", value: "优惠券已替换"},
			{language: "zh", key: "checkout_ui_stackability_replacement_warning", value: "您的一个优惠券无法堆叠。我们已应用更好的优惠券以便获得更多节省。"},
			{language: "zh", key: "checkout_ui_stackability_replacement_warning_replacements", value: "我们替换了："},
			{language: "zh", key: "checkout_ui_stackability_replacement_warning_undo", value: "撤销"},
			{language: "zh", key: "checkout_ui_stackability_error_title", value: "优惠券未应用"},
			{language: "zh", key: "checkout_ui_stackability_error", value: "您的一个优惠券无法堆叠。"},
			{language: "zh", key: "checkout_ui_stackability_error_line_2", value: "您当前的优惠券提供更好的折扣。我们已为您保留。"},
			{language: "zh", key: "checkout_ui_apply_error_title", value: "优惠券无法应用"},
			{language: "zh", key: "checkout_ui_apply_error", value: "您选择的奖励无法应用。请重试。"},
			{"language": "zh", "key": "product_page_loyalty_banner_text", "value": "忠诚度"},
			{"language": "zh", "key": "product_page_guest_earn_teaser", "value": "从此商品赚取{{points}}{{pointsName}}及更多"},
			{"language": "zh", "key": "product_page_guest_join", "value": "加入忠诚度计划"},
			{"language": "zh", "key": "product_page_points_deficit", "value": "您还需要{{points}}{{pointsName}}才能兑换{{rewardName}}"},
			{"language": "zh", "key": "product_page_claim_and_apply_reward", "value": "领取并应用{{rewardName}}⌄"},
			{"language": "zh", "key": "product_page_apply_reward", "value": "应用您的{{rewardName}}⌄"},
			{"language": "zh", "key": "product_page_reward_applied", "value": "已应用您的{{rewardName}}！"},
			{"language": "zh", "key": "product_page_points_balance", "value": "您有{{points}}{{points_name}}"},
			{"language": "zh", "key": "product_page_rewards_dropdown_label", "value": "奖励"},
			{"language": "zh", "key": "product_page_rewards_dropdown_placeholder", "value": "选择一个奖励"},
			{"language": "zh", "key": "product_page_rewards_dropdown_item_text", "value": "{{reward_name}} - {{points}}{{points_name}}"},
			{language: "zh", key: "product_page_rewards_dropdown_item_in_use", value: "{{reward_name}} - 正在使用"},
			{
				"language": "zh",
				"key": "thankyou_would_have_earned_estimate",
				"value": "您本次购物至少能赚取{{estimate}}积分"
			},
			{
				"language": "zh",
				"key": "thankyou_would_have_earned_exact",
				"value": "您本次购物将获得{{points}}积分"
			},
			{
				"language": "zh",
				"key": "thankyou_earned_estimate",
				"value": "您在这次购物中至少赚了{{estimate}}积分"
			},
			{
				"language": "zh",
				"key": "thankyou_earned_exact",
				"value": "您在这次购物中获得了{{points}}"
			},
			{
				"language": "zh",
				"key": "thankyou_vip_perk",
				"value": "享受您的{{vipTier}}级特权，下次购物时可使用{{vipPerk}}"
			},
			{
				"language": "zh",
				"key": "thankyou_points_deficit",
				"value": "再赚取{{deficit}}积分即可兑换{{price}}{{pointsName}}的{{reward}}"
			},
			{
				"language": "zh",
				"key": "thankyou_points_enough",
				"value": "您已有足够的积分可以兑换{{price}}{{pointsName}}的{{reward}}"
			},{
				"language": "zh",
				"key": "thankyou_top_text_logged_in",
				"value": "{{name}}的奖励更新"
			},
			{
				"language": "zh",
				"key": "thankyou_top_text_logged_out",
				"value": "别错过奖励！"
			},
			{
				"language": "zh",
				"key": "thankyou_points_summary_line_1_estimate",
				"value": "至少{{points}} {{pointsName}}"
			},
			{
				"language": "zh",
				"key": "thankyou_points_summary_line_1",
				"value": "{{points}} {{pointsName}}"
			},
			{
				"language": "zh",
				"key": "thankyou_points_summary_line_2_earned",
				"value": "已赚取"
			},
			{
				"language": "zh",
				"key": "thankyou_points_summary_line_2_would_have_earned",
				"value": "在等你"
			},
			{
				"language": "zh",
				"key": "thankyou_reward_summary_line_1_deficit",
				"value": "还需{{points}} {{pointsName}}"
			},
			{
				"language": "zh",
				"key": "thankyou_reward_summary_line_1_enough",
				"value": "你已有足够的{{pointsName}}"
			},
			{
				"language": "zh",
				"key": "thankyou_reward_summary_line_2",
				"value": "可换取{{rewardName}}"
			},
			{
				"language": "zh",
				"key": "thankyou_claim_link",
				"value": "领取你的{{points}} {{pointsName}}"
			},
			{language: "zh", key: "thankyou_reward_summary_line_2_tier", value: "直到你达到{{tierName}}"},
			{language: "zh", key: "view_external_link", value: "获取更多详情"},
			{language: "zh", key: "product_page_guest_earn_teaser_divider", value: "|"},
			{ language: "zh", key: "cart_free_gift_threshold_not_met_choice", value: "再添加{{amount}}到购物车即可获得{{gift_name}}" },
			{ language: "zh", key: "cart_free_gift_threshold_not_met_single", value: "再添加{{amount}}到购物车即可获得{{gift_name}}" },
			{ language: "zh", key: "cart_free_gift_threshold_met_choice", value: "您已获得{{gift_name}}。已自动添加到您的购物车" },
			{ language: "zh", key: "cart_free_gift_threshold_met_single", value: "您已获得{{gift_name}}。已自动添加到您的购物车" },
			{ language: "zh", key: "free_gift", value: "免费礼品" },




			{
				language: "ka",
				key: "join",
				value: "რეგისტრაცია"
			},
			{
				language: "ka",
				key: "sign_in",
				value: "შესვლა"
			},
			{
				language: "ka",
				key: "loyalty_by_raleon_link",
				value: "ერთგული ✨ by {{link}}"
			},
			{
				language: "ka",
				key: "home_available_points",
				value: "დაგროვილი ქულები"
			},
			{
				language: "ka",
				key: "earn_summary_ways_to_earn",
				value: "როგორ მივიღოთ ქულები?"
			},
			{
				language: "ka",
				key: "referral_bonus_reward_description",
				value: "მოიწვიე მეგობარი და მიიღე {{value}} ფასდაკლება"
			},
			{
				language: "ka",
				key: "inventory_summary_no_rewards",
				value: "ჯილდოები ჯერ არ გაქვს"
			},
			{
				language: "ka",
				key: "points",
				value: "ქულები"
			},
			{
				language: "ka",
				key: "rewards",
				value: "პრიზები"
			},
			{
				language: "ka",
				key: "redeem_rewards",
				value: "პრიზების გამოთვლა"
			},
			{
				language: "ka",
				key: "see_all",
				value: "ყველას ნახვა"
			},
			{
				language: "ka",
				key: "copy_referral_link",
				value: "რეფერალული ლინკის დაკოპირება"
			},
			{
				language: "ka",
				key: "copied_referral_link",
				value: "ლინკი დაკოპირებულია!"
			},
			{
				language: "ka",
				key: "referral_socials",
				value: "ან გააზიარე ლინკი სოციალურ მედიაში:"
			},
			{
				language: "ka",
				key: "full_inventory_my_inventory",
				value: "ჩემი ინვენტარი"
			},
			{
				language: "ka",
				key: "reward_detail_minimum_order_total",
				value: "მინიმალური შეკვეთა : {{minOrderTotal}}"
			},
			{
				language: "ka",
				key: "reward_detail_claim_count_current",
				value: "შენ მოითხოვე {{redeemed}} / {{maxUserRedemptions}}."
			},
			{
				language: "ka",
				key: "reward_detail_expiration_summary",
				value: "ვადა იწურება {{expiresInDays}} დღეში"
			},
			{
				language: "ka",
				key: "reward_detail_your_next_purchase",
				value: "შემდეგი შეძენა"
			},
			{
				language: "ka",
				key: "reward_detail_insufficient_points",
				value: "არასაკმარისი ქულები"
			},
			{
				language: "ka",
				key: "reward_detail_claim_button_alt",
				value: "ქულებით შეძენა"
			},
			{
				language: "ka",
				key: "youve_got_rewards",
				value: "გილოცავ პრიზებს!"
			},
			{
				language: "ka",
				key: "youve_got_referral_rewards",
				value: "თქვენმა მეგობარმა მოგიწვიათ საიტზე"
			},
			{
				language: "ka",
				key: "rewards_unlocked",
				value: "პრიზები გელოდება!"
			},
			{
				language: "ka",
				key: "apply_to_cart",
				value: "ჩააგდე კალათაში"
			},
			{language: "ka", key: "applied_to_cart", value: "უკვე კალათაშია"},
			{
				language: "ka",
				key: "apply_to_cart_description",
				value: "ღილაკი \"კალათაში გამოყენება\" ქვემოთ გამოყენებას მოახდენს {{itemName}} კუპონს."
			},
			{
				language: "ka",
				key: "on_next_purchase",
				value: "შემდეგი შეძენისას"
			},
			{
				language: "ka",
				key: "spend_minimum",
				value: "ამ პრიზის მისაღებად, უნდა დახარჯოთ მინიმუმ {{minOrderTotal}}"
			},
			{
				language: "ka",
				key: "choose_product_from_collection",
				value: "აირჩიე კოლექციიდან"
			},
			{language: "ka", key: "choose_free_product", value: "აირჩიეთ თქვენი უფასო პროდუქტი"},
			{language: "ka", key: "choose_dollar_off_product", value: "გამოიყენეთ და ნახეთ პროდუქტი"},
			{language: "ka", key: "choose_percent_off_product", value: "გამოიყენეთ და ნახეთ პროდუქტი"},
			{
				language: "ka",
				key: "coupon_expiration",
				value: "ვადა გასდის {{timeValue}} {{timeLabel}}-ში."
			},
			{
				language: "ka",
				key: "vip-tiers-title",
				value: "წევრთა დონეები"
			},
			{
				language: "ka",
				key: "points-ttm",
				value: "ქულები"
			},
			{
				language: "ka",
				key: "perks_title",
				value: "პრივილეგიები"
			},
			{
				language: "ka",
				key: "points_to_next_tier",
				value: "ქულები შემდეგ დონემდე"
			},
			{
				language: "ka",
				key: "learn_more_about_tiers",
				value: "გაიგე მეტი დონეების შესახებ"
			},
			{
				language: "ka",
				key: "reward_pill_points",
				value: "{{value}} ქულა"
			},
			{
				language: "ka",
				key: "reward_pill_points_per_dollar",
				value: "{{value}} ქულა/{{prefix}}{{postfix}}"
			},
			{
				language: "ka",
				key: "reward_pill_dollars_off_coupon",
				value: "{{prefix}}{{value}} ფასდაკლება"
			},
			{
				language: "ka",
				key: "reward_pill_percent_discount",
				value: "{{value}}% ფასდაკლება"
			},
			{
				language: "ka",
				key: "reward_pill_free_shipping",
				value: "უფასო მიწოდება"
			},
			{
				language: "ka",
				key: "reward_pill_free_product",
				value: "უფასო პროდუქტი"
			},
			{ "language": "ka", "key": "reward_pill_percent_off_product", "value": "% ფასდაკლება პროდუქტზე" },
  			{ "language": "ka", "key": "reward_pill_dollar_off_product", "value": "ფასდაკლება დოლარში პროდუქტზე" },
			{language: "ka", key: "reward_pill_giveaway_entry", value: "1 ლოტერიის შესვლა" },
			{language: "ka", key: "reward_pill_giveaway_entry_multiple", value: "{{count}} ლოტერიის შესვლა" },
			{language: "ka", key: "coupon", value: "კუპონი"},
			{
				language: "ka",
				key: "reward_points_per_dollar",
				value: "მიიღე {{points}} ქულა თითო {{dollarName}} გადახდილისას"
			},
			{
				language: "ka",
				key: "reward_points",
				value: "მიიღე {{points}} ქულა"
			},
			{
				language: "ka",
				key: "reward_percent_discount",
				value: "მიიღე {{amount}}% ფასდაკლების კუპონი"
			},
			{ language: "ka", key: "reward_product_percent_discount", value: "მიიღეთ {{amount}}%-იანი ფასდაკლების კუპონი კონკრეტული პროდუქტისთვის" },
  			{ language: "ka", key: "reward_product_dollar_discount", value: "მიიღეთ {{prefix}}{{postfix}}{{amount}}-იანი ფასდაკლების კუპონი კონკრეტული პროდუქტისთვის" },
			{language: "ka", key: "show_discount_code", value: "ფასდაკლების კოდის ჩვენება" },
			{language: "ka", key: "condition_click_to_redeem", value: "დააჭირეთ შესასვლელად" },
			{language: "ka", key: "condition_successfully_redeemed", value: "თქვენ ჩაირიცხეთ ლოტერიაზე!"},
			{language: "ka", key: "condition_auto_redeem", value: "თქვენ ავტომატურად ხართ შესული!" },
			{
				language: "ka",
				key: "condition_dollar_spent",
				value: "მიიღე ქულები თითო {{currency_value:1}} გადახდილისას"
			},
			{
				language: "ka",
				key: "condition_welcome_bonus",
				value: "რეგისტრაციის ბონუსი"
			},
			{
				language: "ka",
				key: "condition_product_review",
				value: "დატოვე შეფასება ჯილდოს მიღებისთვის"
			},
			{
				language: "ka",
				key: "condition_product_photo_review",
				value: "დაამატე ფოტო შეფასებაში ჯილდოს მისაღებად"
			},
			{
				language: "ka",
				key: "condition_birthday_bonus",
				value: "მიიღე პრიზი დაბადების დღეზე"
			},
			{ language: "ka", key: "enter_birthday_text", value: "შეიყვანეთ თქვენი დაბადების თარიღი" },
			{
				language: "ka",
				key: "condition_nth_purchase",
				value: "გააკეთე {{counts}} შეძენა"
			},
			{
				language: "ka",
				key: "condition_nth_purchase_single",
				value: "გააკეთე {{counts}} შეძენა"
			},
			{language: "ka", key: "condition_nth_purchase_progress", value: "{{counts}} ಇನ್ನಷ್ಟು ಖರೀದಿಗಳು ಮಾಡಿ"},
			{language: "ka", key: "condition_nth_purchase_progress_single", value: "{{counts}} ಇನ್ನಷ್ಟು ಖರೀದಿ ಮಾಡಿ"},
			{language: "ka", key: "condition_nth_purchase_progress_complete", value: "ಗುರಿ ತಲುಪಿದೆ"},
			{
				language: "ka",
				key: "condition_timed_purchase",
				value: "გააკეთე {{orderCounts}} შეძენა {{daysUntilEnd}} დღეში"
			},
			{
				language: "ka",
				key: "condition_timed_purchase_single",
				value: "გააკეთე {{orderCounts}} შეძენა {{daysUntilEnd}} დღეში"
			},
			{
				language: "ka",
				key: "condition_follow_on_instagram",
				value: "<a href=\"https://www.instagram.com/{{handle}}\" target=\"_blank\" class=\"raleon-social-wte\">გამოიწერე @{{handle}} Instagram-ზე</a>"
			},
			{
				language: "ka",
				key: "condition_follow_on_tiktok",
				value: "<a href=\"https://www.tiktok.com/{{handle}}\" target=\"_blank\" class=\"raleon-social-wte\">გამოიწერე {{handle}} TikTok-ზე</a>"
			},
			{
				language: "ka",
				key: "condition_follow_on_facebook",
				value: "<a href=\"https://www.facebook.com/{{handle}}\" target=\"_blank\" class=\"raleon-social-wte\">მოიწონე {{handle}} Facebook-ზე</a>"
			},
			{
				language: "ka",
				key: "condition_follow_facebook_group",
				value: "<a href=\"https://www.facebook.com/groups/{{handle}}\" target=\"_blank\" class=\"raleon-social-wte\">შეუერთდით {{handle}} Facebook-ზე</a>"
			},
			{language: "ka", key: "condition_follow_on_youtube", value: `<a href="https://www.youtube.com/{{handle}}" target="_blank" class="raleon-social-wte">{{handle}}-ის გამოწერა YouTube-ზე</a>`},
			{ "language": "ka", "key": "condition_follow_on_custom", "value": "<a href=\"{{url}}\" target=\"_blank\" class=\"raleon-social-wte\">გამოგვყევით</a>" },
			{
				language: "ka",
				key: "condition_short_nth_purchase",
				value: "{{count}} შეძენა"
			},
			{
				language: "ka",
				key: "condition_short_follow_on_instagram",
				value: "გამოიწერე @{{handle}}"
			},
			{
				language: "ka",
				key: "condition_short_follow_on_tiktok",
				value: "გამოიწერე {{handle}}"
			},
			{
				language: "ka",
				key: "condition_short_follow_on_facebook",
				value: "მოიწონე {{handle}}"
			},
			{ "language": "ka", "key": "condition_short_follow_on_youtube", "value": "{{handle}}-ის გამოწერა" },
			{ "language": "ka", "key": "condition_short_follow_on_custom", "value": "გამოგვყევით" },
			{
				language: "ka",
				key: "condition_specific_product_purchase",
				value: "შეიძინე კონკრეტული <a href=\"https://{{domain}}/products/{{handle}}\" class=\"raleon-social-wte\">პროდუქტი</a>"
			},
			{
				language: "ka",
				key: "condition_collection_purchase",
				value: "შეიძინე {{count}} <a href=\"https://{{domain}}/collections/{{handle}}\" class=\"raleon-social-wte\">კოლექციიდან</a>"
			},
			{
				language: "ka",
				key: "condition_specific_product_short_purchase",
				value: "შეიძინეთ პროდუქტი"
			},
			{
				language: "ka",
				key: "condition_collection_short_purchase",
				value: "{{count}} კოლექციიდან"
			},
			{
				language: "ka",
				key: "condition_referrer_bonus",
				value: "რეკომენდაცია მეგობარს"
			},
			{
				language: "ka",
				key: "condition_subscription_purchase",
				value: "გამოწერა"
			},
			{
				language: "ka",
				key: "condition_subscription_purchase_multiple",
				value: "გააკეთე {{value}} გამოწერა"
			},
			{
				language: "ka",
				key: "free_product_from_collection_name",
				value: "უფასო პროდუქტი {{value}}ს კოლექციიდან"
			},
			{
				language: "ka",
				key: "referral_bonus_reward_name",
				value: "{{value}} ფასდაკლება"
			},
			{
				language: "ka",
				key: "points_ttm_earned",
				value: "{{points}} ქულა მიღებულია"
			},
			{language: "ka", key: "points_abbreviated", value: "ქულები"},
			{language: "ka", key: "referral_bonus_reward_points_description", value: "მიიღეთ {{value}} როდესაც დაურეკომენდებთ მეგობარს"},
			{language: "ka", key: "referral_sign_in", value: "შესვლა მეგობრის რეკომენდაციისთვის"},
			{language: "ka", key: "email_placeholder_referral", value: "თქვენი მეგობრის ელ.ფოსტა"},
			{language: "ka", key: "referral_send", value: "მიწვევის გაგზავნა"},
			{language: "ka", key: "referral_sent", value: "მიწვევა გაგზავნილია"},
			{ "language": "ka", "key": "coupon_waiting", "value": "შესვლა შეთავაზების პირობის მოსაპოვებლად. დააჭირეთ აქ, რომ შემოუერთდეთ." },
			{language: "ka", key: "coupon_added_to_cart", value: "ჯილდო დაემატა კალათას"},
			{ "language": "ka", "key": "subview_loyalty", "value": "ერთგულება" },
			{ "language": "ka", "key": "subview_vip", "value": "VIP დონეები" },
			{ "language": "ka", "key": "subview_history", "value": "ისტორია" },
			{ "language": "ka", "key": "subview_my_rewards", "value": "ჩემი ჯილდოები" },
			{ "language": "ka","key": "reward_dollars_off_coupon","value": "მიიღე {{prefix}}{{postfix}}{{amount}}-იანი ფასდაკლების კუპონი"},
			{"language": "ka", "key": "checkout_ui_points_header", "value": "თქვენ გაქვთ {{points}} {{points_name}}"},
			{"language": "ka", "key": "checkout_ui_login_link_prefix", "value": "არ გამოტოვოთ!"},
			{"language": "ka", "key": "checkout_ui_login_link_text", "value": "Შესვლა"},
			{"language": "ka", "key": "checkout_ui_login_link_suffix", "value": " საჩუქრების მისაღებად და გამოსყიდვისთვის"},
			{"language": "ka", "key": "checkout_ui_not_enough_points_warning", "value": "ჯილდოს გამოსასყიდად დაგჭირდებათ {{points}} {{points_name}} კიდევ"},
			{"language": "ka", "key": "checkout_ui_rewards_dropdown_label", "value": "Ჯილდოები"},
			{"language": "ka", "key": "checkout_ui_rewards_dropdown_placeholder", "value": "აირჩიეთ ჯილდო"},
			{"language": "ka", "key": "checkout_ui_rewards_dropdown_item_text", "value": "{{reward_name}} - {{points}} {{points_name}}"},
			{"language": "ka", "key": "checkout_ui_apply_reward_button_text", "value": "Გამოყენება"},
			{language: "ka", key: "checkout_ui_rewards_dropdown_item_in_use", value: "{{reward_name}} - გამოყენებაშია"},
			{language: "ka", key: "checkout_ui_stackability_replacement_warning_title", value: "კუპონები შეცვლილია"},
			{language: "ka", key: "checkout_ui_stackability_replacement_warning", value: "თქვენი ერთ-ერთი კუპონი არ შეიძლება დააკავშიროთ. ჩვენ გამოვიყენეთ უკეთესი კუპონი, რათა მეტი ზოგადად დაზოგოთ."},
			{language: "ka", key: "checkout_ui_stackability_replacement_warning_replacements", value: "რას შევცვალეთ:"},
			{language: "ka", key: "checkout_ui_stackability_replacement_warning_undo", value: "გაუქმება"},
			{language: "ka", key: "checkout_ui_stackability_error_title", value: "კუპონი არ მიღებულა"},
			{language: "ka", key: "checkout_ui_stackability_error", value: "თქვენი ერთ-ერთი კუპონი არ შეიძლება დააკავშიროთ."},
			{language: "ka", key: "checkout_ui_stackability_error_line_2", value: "თქვენი მიმდინარე კუპონი გთავაზობთ უკეთეს ფასდაკლებას. ჩვენ მას შევინახეთ თქვენთვის."},
			{language: "ka", key: "checkout_ui_apply_error_title", value: "კუპონი ვერ მიღებულა"},
			{language: "ka", key: "checkout_ui_apply_error", value: "თქვენმა შერჩეულმა ჯილდომ ვერ დაიმტკიცა. გთხოვთ, სცადეთ თავიდან."},
			{"language": "ka", "key": "product_page_loyalty_banner_text", "value": "ლოიალურობა"},
			{"language": "ka", "key": "product_page_guest_earn_teaser", "value": "მიიღეთ {{points}} {{pointsName}} და მეტი ამ ნივთიდან"},
			{"language": "ka", "key": "product_page_guest_join", "value": "გაწევრიანდით ლოიალურობაში"},
			{"language": "ka", "key": "product_page_points_deficit", "value": "თქვენ გჭირდებათ კიდევ {{points}} {{pointsName}}, რათა გამოისყიდეთ {{rewardName}}"},
			{"language": "ka", "key": "product_page_claim_and_apply_reward", "value": "მოითხოვეთ & გამოიყენეთ {{rewardName}}"},
			{"language": "ka", "key": "product_page_apply_reward", "value": "გამოიყენეთ თქვენი {{rewardName}}"},
			{"language": "ka", "key": "product_page_reward_applied", "value": "გამოიყენეთ თქვენი {{rewardName}}!"},
			{"language": "ka", "key": "product_page_points_balance", "value": "თქვენ გაქვთ {{points}} {{points_name}}"},
			{"language": "ka", "key": "product_page_rewards_dropdown_label", "value": "ჯილდოები"},
			{"language": "ka", "key": "product_page_rewards_dropdown_placeholder", "value": "აირჩიეთ ჯილდო"},
			{"language": "ka", "key": "product_page_rewards_dropdown_item_text", "value": "{{reward_name}} - {{points}} {{points_name}}"},
			{language: "ka", key: "product_page_rewards_dropdown_item_in_use", value: "{{reward_name}} - გამოყენებაშია"},
			{
				"language": "ka",
				"key": "thankyou_would_have_earned_estimate",
				"value": "თქვენ მიიღებდით არანაკლებ {{estimate}} ქულას ამ შეძენით"
			},
			{
				"language": "ka",
				"key": "thankyou_would_have_earned_exact",
				"value": "თქვენ მიიღებდით {{points}} ქულას ამ შეძენით"
			},
			{
				"language": "ka",
				"key": "thankyou_earned_estimate",
				"value": "თქვენ მიიღეთ არანაკლებ {{estimate}} ქულა ამ შეძენით"
			},
			{
				"language": "ka",
				"key": "thankyou_earned_exact",
				"value": "თქვენ მიიღეთ {{points}} ქულა ამ შეძენით"
			},
			{
				"language": "ka",
				"key": "thankyou_vip_perk",
				"value": "დაიხარჯეთ თქვენი {{vipTier}} პაკეტის პრივილეგიები, {{vipPerk}} თქვენს შემდეგ შეძენაზე"
			},
			{
				"language": "ka",
				"key": "thankyou_points_deficit",
				"value": "მოიგეთ კიდევ {{deficit}} ქულა რათა შეძლოთ {{reward}} გადახდა {{price}} {{pointsName}}"
			},
			{
				"language": "ka",
				"key": "thankyou_points_enough",
				"value": "თქვენი ქულები საკმარისია {{reward}}-ის შესაძენად {{price}} {{pointsName}}"
			},{
				"language": "ka",
				"key": "thankyou_top_text_logged_in",
				"value": "ჯილდოების განახლება თვისთვის, {{name}}"
			},
			{
				"language": "ka",
				"key": "thankyou_top_text_logged_out",
				"value": "არ გამოტოვო ჯილდოები!"
			},
			{
				"language": "ka",
				"key": "thankyou_points_summary_line_1_estimate",
				"value": "მინიმუმ {{points}} {{pointsName}}"
			},
			{
				"language": "ka",
				"key": "thankyou_points_summary_line_1",
				"value": "{{points}} {{pointsName}}"
			},
			{
				"language": "ka",
				"key": "thankyou_points_summary_line_2_earned",
				"value": "მოგერგოთ"
			},
			{
				"language": "ka",
				"key": "thankyou_points_summary_line_2_would_have_earned",
				"value": "გელოდებათ"
			},
			{
				"language": "ka",
				"key": "thankyou_reward_summary_line_1_deficit",
				"value": "{{points}} მეტი {{pointsName}}"
			},
			{
				"language": "ka",
				"key": "thankyou_reward_summary_line_1_enough",
				"value": "გაქვთ საკმაოდ {{pointsName}}"
			},
			{
				"language": "ka",
				"key": "thankyou_reward_summary_line_2",
				"value": "{{rewardName}}-ისთვის"
			},
			{
				"language": "ka",
				"key": "thankyou_claim_link",
				"value": "მოიპოვეთ თქვენი {{points}} {{pointsName}}"
			},
			{language: "ka", key: "thankyou_reward_summary_line_2_tier", value: "{{tierName}} მიღწევამდე"},
			{language: "ka", key: "view_external_link", value: "მოიპოვეთ მეტი დეტალი"},
			{language: "ka", key: "product_page_guest_earn_teaser_divider", value: "|"},
			{ language: "ka", key: "cart_free_gift_threshold_not_met_choice", value: "დაამატეთ {{amount}} კალათაში {{gift_name}}-ის მისაღებად" },
			{ language: "ka", key: "cart_free_gift_threshold_not_met_single", value: "დაამატეთ {{amount}} კალათაში {{gift_name}}-ის მისაღებად" },
			{ language: "ka", key: "cart_free_gift_threshold_met_choice", value: "თქვენ მოიპოვეთ {{gift_name}}. ის ავტომატურად დაემატა თქვენს კალათას" },
			{ language: "ka", key: "cart_free_gift_threshold_met_single", value: "თქვენ მოიპოვეთ {{gift_name}}. ის ავტომატურად დაემატა თქვენს კალათას" },
			{ language: "ka", key: "free_gift", value: "უფასო საჩუქარი" },




			// Hebrew - Static
			{language: "he", key: "join", value: "הצטרף"},
			{language: "he", key: "sign_in", value: "התחבר"},
			{language: "he", key: "loyalty_by_raleon_link", value: "תוכנית נאמנות ✨ מאת {{link}}"},
			{language: "he", key: "home_available_points", value: "{{pointsName}} זמינים"},
			{language: "he", key: "earn_summary_ways_to_earn", value: "דרכים להרוויח"},
			{language: "he", key: "referral_bonus_reward_description", value: "קבל {{value}} הנחה ברכישה הבאה שלך כאשר אתה מפנה חבר"},
			{language: "he", key: "referral_bonus_reward_points_description", value: "קבל {{value}} כאשר אתה מפנה חבר"},
			{language: "he", key: "inventory_summary_no_rewards", value: "עדיין אין לך פרסים."},
			{language: "he", key: "points", value: "נקודות"},
			{language: "he", key: "points_abbreviated", value: "נק'"},
			{language: "he", key: "rewards", value: "פרסים"},
			{language: "he", key: "redeem_rewards", value: "ממש פרסים"},
			{language: "he", key: "see_all", value: "ראה הכל"},
			{language: "he", key: "copy_referral_link", value: "העתק קישור הפניה ייחודי"},
			{language: "he", key: "copied_referral_link", value: "הועתק ללוח"},
			{language: "he", key: "referral_socials", value: "או שתף את הקישור ברשתות החברתיות:"},
			{language: "he", key: "full_inventory_my_inventory", value: "המלאי שלי"},
			{language: "he", key: "reward_detail_minimum_order_total", value: "סכום ההזמנה המינימלי {{minOrderTotal}}."},
			{language: "he", key: "reward_detail_claim_count_current", value: "מימשת {{redeemed}} / {{maxUserRedemptions}}."},
			{language: "he", key: "reward_detail_expiration_summary", value: "פג תוקף {{expiresInDays}} ימים לאחר מימוש."},
			{language: "he", key: "reward_detail_your_next_purchase", value: "הרכישה הבאה שלך"},
			{language: "he", key: "reward_detail_insufficient_points", value: "{{pointsName}} לא מספיקים"},
			{language: "he", key: "reward_detail_claim_button_alt", value: "ממש והשתמש"},
			{language: "he", key: "youve_got_rewards", value: "יש לך פרסים!"},
			{language: "he", key: "youve_got_referral_rewards", value: "חבר שלך שלח לך פרס הפניה, לחץ כאן או הירשם כדי לממש"},
			{language: "he", key: "rewards_unlocked", value: "פרסים נפתחו!"},
			{language: "he", key: "apply_to_cart", value: "הוסף לעגלה"},
			{language: "he", key: "applied_to_cart", value: "כבר בעגלה"},
			{language: "he", key: "apply_to_cart_description", value: "בלחיצה על 'הוסף לעגלה' למטה, תוחלף קופון {{itemName}} בעגלת הקניות שלך."},
			{language: "he", key: "on_next_purchase", value: "ברכישה הבאה שלך."},
			{language: "he", key: "spend_minimum", value: "עליך להוציא לפחות {{minOrderTotal}} כדי להשתמש בפרס זה"},
			{language: "he", key: "choose_product_from_collection", value: "בחר מוצר מאוסף {{collection}}"},
			{language: "he", key: "choose_free_product", value: "בחר את המוצר החינמי שלך"},
			{language: "he", key: "choose_dollar_off_product", value: "החל והצג מוצר"},
			{language: "he", key: "choose_percent_off_product", value: "החל והצג מוצר"},
			{language: "he", key: "coupon_expiration", value: "פג תוקף בעוד {{timeValue}} {{timeLabel}}."},
			{language: "he", key: "coupon", value: "קופון"},
			{language: "he", key: "vip-tiers-title", value: "רמות חברות"},
			{language: "he", key: "points-ttm", value: '{{pointsName}}'},
			{language: "he", key: "perks_title", value: "הטבות"},
			{language: "he", key: "points_to_next_tier", value: '{{pointsName}} לרמה הבאה'},
			{language: "he", key: "learn_more_about_tiers", value: "למד על רמות"},
			{language: "he", key: "referral_sign_in", value: "התחבר כדי להפנות חבר"},
			{language: "he", key: "email_placeholder_referral", value: "האימייל של החבר שלך"},
			{language: "he", key: "referral_send", value: "שלח הזמנה"},
			{language: "he", key: "referral_sent", value: "הזמנה נשלחה"},
			{language: "he", key: "subview_loyalty", value: "נאמנות" },
			{language: "he", key: "subview_vip", value: "רמות VIP" },
			{language: "he", key: "subview_history", value: "היסטוריה" },
			{language: "he", key: "subview_my_rewards", value: "הפרסים שלי" },

			// Hebrew - "Generated"
			{language: "he", key: "reward_pill_points", value: "{{value}} {{pointsAbbreviated}}"},
			{language: "he", key: "reward_pill_points_per_dollar", value: "{{value}} {{pointsAbbreviated}}/{{prefix}}{{postfix}}"},
			{language: "he", key: "reward_pill_dollars_off_coupon", value: "{{prefix}}{{value}} הנחה"},
			{language: "he", key: "reward_pill_percent_discount", value: "{{value}}% הנחה"},
			{language: "he", key: "reward_pill_free_shipping", value: "משלוח חינם"},
			{language: "he", key: "reward_pill_free_product", value: "מוצר חינם"},
			{ "language": "he", "key": "reward_pill_percent_off_product", "value": "% הנחה על מוצר" },
  			{ "language": "he", "key": "reward_pill_dollar_off_product", "value": "הנחה בדולרים על מוצר" },
			{language: "he", key: "reward_pill_giveaway_entry", value: "1 כניסה להגרלה" },
			{language: "he", key: "reward_pill_giveaway_entry_multiple", value: "{{count}} כניסות להגרלה" },
			{language: "he", key: "reward_points_per_dollar", value: "הרווח {{points}} {{pointsName}} עבור כל {{dollarName}} שהוצאת"},
			{language: "he", key: "reward_points", value: "הרווח {{points}} {{pointsName}}"},
			{language: "he", key: "reward_dollars_off_coupon", value: "הרווח קופון הנחה {{prefix}}{{postfix}}{{amount}}"},
			{language: "he", key: "reward_percent_discount", value: "הרווח קופון הנחה {{amount}}%"},
			{ language: "he", key: "reward_product_percent_discount", value: "הרוויחו קופון הנחה של {{amount}}% עבור מוצר ספציפי" },
  			{ language: "he", key: "reward_product_dollar_discount", value: "הרוויחו קופון הנחה של {{prefix}}{{postfix}}{{amount}} עבור מוצר ספציפי" },
			{language: "he", key: "show_discount_code", value: "הצג קוד הנחה" },
			{language: "he", key: "condition_click_to_redeem", value: "לחץ כדי להיכנס" },
			{language: "he", key: "condition_auto_redeem", value: "אתה נרשם אוטומטית!" },
			{language: "he", key: "condition_successfully_redeemed", value: "נרשמת להגרלה!"},
			{language: "he", key: "condition_dollar_spent", value: "בצע רכישה כדי להרוויח {{pointsName}} עבור כל {{currency_value:1}} שהוצא"},
			{language: "he", key: "condition_welcome_bonus", value: "הרוויח פרסים כשאתה מצטרף"},
			{language: "he", key: "condition_product_review", value: "השאר ביקורת כדי להרוויח פרס"},
			{language: "he", key: "condition_product_photo_review", value: "הוסף תמונה לביקורת כדי להרוויח פרס"},
			{language: "he", key: "condition_birthday_bonus", value: "הרוויח פרסים ביום ההולדת שלך"},
			{ language: "he", key: "enter_birthday_text", value: "הכנס את תאריך הלידה שלך" },
			{language: "he", key: "condition_nth_purchase", value: "בצע {{counts}} רכישות"},
			{language: "he", key: "condition_nth_purchase_single", value: "בצע {{counts}} רכישה"},
			{language: "he", key: "condition_nth_purchase_progress", value: "בצע {{counts}} רכישות נוספות"},
			{language: "he", key: "condition_nth_purchase_progress_single", value: "בצע {{counts}} רכישה נוספת"},
			{language: "he", key: "condition_nth_purchase_progress_complete", value: "היעד הושג"},
			{language: "he", key: "condition_timed_purchase", value: "בצע {{orderCounts}} רכישות בתוך {{daysUntilEnd}} ימים"},
			{language: "he", key: "condition_timed_purchase_single", value: "בצע {{orderCounts}} רכישה בתוך {{daysUntilEnd}} ימים"},
			{language: "he", key: "condition_follow_on_instagram", value: `<a href="https://www.instagram.com/{{handle}}" target="_blank" class="raleon-social-wte">עקוב אחרי @{{handle}} באינסטגרם</a>`},
			{language: "he", key: "condition_follow_on_tiktok", value: `<a href="https://www.tiktok.com/{{handle}}" target="_blank" class="raleon-social-wte">עקוב אחרי {{handle}} בטיקטוק</a>`},
			{language: "he", key: "condition_follow_on_facebook", value: `<a href="https://www.facebook.com/{{handle}}" target="_blank" class="raleon-social-wte">עשה לייק ל{{handle}} בפייסבוק</a>`},
			{language: "he", key: "condition_follow_facebook_group", value: `<a href="https://www.facebook.com/groups/{{handle}}" target="_blank" class="raleon-social-wte">הצטרף ל{{handle}} בפייסבוק</a>`},
			{language: "he", key: "condition_follow_on_youtube", value: `<a href="https://www.youtube.com/{{handle}}" target="_blank" class="raleon-social-wte">הירשם ל{{handle}} ביוטיוב</a>`},
			{ "language": "he", "key": "condition_follow_on_custom", "value": "<a href=\"{{url}}\" target=\"_blank\" class=\"raleon-social-wte\">עקבו אחרינו</a>" },
			{language: "he", key: "condition_short_nth_purchase", value: `{{count}} רכישה`},
			{language: "he", key: "condition_short_follow_on_instagram", value: `עקוב אחרי @{{handle}}`},
			{language: "he", key: "condition_short_follow_on_tiktok", value: `עקוב אחרי {{handle}}`},
			{language: "he", key: "condition_short_follow_on_facebook", value: `עשה לייק ל{{handle}}`},
			{ "language": "he", "key": "condition_short_follow_on_youtube", "value": "הירשם ל{{handle}}" },
			{ "language": "he", "key": "condition_short_follow_on_custom", "value": "עקבו אחרינו" },
			{language: "he", key: "condition_specific_product_purchase", value: `קנה <a href="https://{{domain}}/products/{{handle}}" class="raleon-social-wte">מוצר ספציפי</a>`},
			{language: "he", key: "condition_collection_purchase", value: `קנה {{count}} מ<a href="https://{{domain}}/collections/{{handle}}" class="raleon-social-wte">אוסף</a>`},
			{language: "he", key: "condition_specific_product_short_purchase", value: `קנה מוצר ספציפי`},
			{language: "he", key: "condition_collection_short_purchase", value: `{{count}} מאוסף`},
			{language: "he", key: "condition_referrer_bonus", value: `הפנה חבר`},
			{language: "he", key: "condition_subscription_purchase", value: `בצע רכישת מנוי`},
			{language: "he", key: "condition_subscription_purchase_multiple", value: `בצע {{value}} רכישות מנוי`},
			{language: "he", key: "free_product_from_collection_name", value: `מוצר חינם מאוסף {{value}}`},
			{language: "he", key: "referral_bonus_reward_name", value: `{{value}} הנחה`},
			{language: "he", key: "points_ttm_earned", value: `{{points}} {{pointsName}} שהרווחת`},
			{language: "he", key: "checkout_ui_points_header", value: "יש לך {{points}} {{points_name}}"},
			{language: "he", key: "checkout_ui_login_link_prefix", value: "אל תפספס!"},
			{language: "he", key: "checkout_ui_login_link_text", value: "התחבר"},
			{language: "he", key: "checkout_ui_login_link_suffix", value: " כדי להרוויח ולממש פרסים"},
			{language: "he", key: "checkout_ui_not_enough_points_warning", value: "אתה צריך עוד {{points}} {{points_name}} כדי לממש פרס"},
			{language: "he", key: "checkout_ui_rewards_dropdown_label", value: "פרסים"},
			{language: "he", key: "checkout_ui_rewards_dropdown_placeholder", value: "בחר פרס"},
			{language: "he", key: "checkout_ui_rewards_dropdown_item_text", value: "{{reward_name}} - {{points}} {{points_name}}"},
			{language: "he", key: "checkout_ui_apply_reward_button_text", value: "החל"},
			{language: "he", key: "checkout_ui_rewards_dropdown_item_in_use", value: "{{reward_name}} - בשימוש"},
			{language: "he", key: "checkout_ui_stackability_replacement_warning_title", value: "קופונים הוחלפו"},
			{language: "he", key: "checkout_ui_stackability_replacement_warning", value: "אחד מהקופונים שלך לא תואם. החלנו את הקופון הטוב יותר לחסכון טוב יותר."},
			{language: "he", key: "checkout_ui_stackability_replacement_warning_replacements", value: "מה שהחלפנו:"},
			{language: "he", key: "checkout_ui_stackability_replacement_warning_undo", value: "בטל"},
			{language: "he", key: "checkout_ui_stackability_error_title", value: "הקופון לא הוחל"},
			{language: "he", key: "checkout_ui_stackability_error", value: "אחד מהקופונים שלך לא תואם."},
			{language: "he", key: "checkout_ui_stackability_error_line_2", value: "הקופון הנוכחי שלך מציע הנחה טובה יותר. שמרנו אותו בשבילך."},
			{language: "he", key: "checkout_ui_apply_error_title", value: "הקופון לא ניתן להחלה"},
			{language: "he", key: "checkout_ui_apply_error", value: "הפרס שבחרת לא ניתן להחלה. אנא נסה שוב."},
			{ "language": "he", "key": "coupon_waiting", "value": "היכנסו כדי לתבוע את ההצעה שלכם. לחצו כאן להצטרפות." },
			{language: "he", key: "coupon_added_to_cart", value: "תגמול נוסף לעגלה"},
			{"language": "he", "key": "product_page_loyalty_banner_text", "value": "נאמנות"},
			{"language": "he", "key": "product_page_guest_earn_teaser", "value": "צבור {{points}} {{pointsName}} ויותר ממוצר זה"},
			{"language": "he", "key": "product_page_guest_join", "value": "הצטרף לנאמנות"},
			{"language": "he", "key": "product_page_points_deficit", "value": "אתה צריך {{points}} נקודות {{pointsName}} נוספות כדי לממש את {{rewardName}}"},
			{"language": "he", "key": "product_page_claim_and_apply_reward", "value": "תבע & החל {{rewardName}}"},
			{"language": "he", "key": "product_page_apply_reward", "value": "החל את {{rewardName}} שלך"},
			{"language": "he", "key": "product_page_reward_applied", "value": "הוחל {{rewardName}} שלך!"},
			{"language": "he", "key": "product_page_points_balance", "value": "יש לך {{points}} {{points_name}}"},
			{"language": "he", "key": "product_page_rewards_dropdown_label", "value": "פרסים"},
			{"language": "he", "key": "product_page_rewards_dropdown_placeholder", "value": "בחר פרס"},
			{"language": "he", "key": "product_page_rewards_dropdown_item_text", "value": "{{reward_name}} - {{points}} {{points_name}}"},
			{language: "he", key: "product_page_rewards_dropdown_item_in_use", value: "{{reward_name}} - בשימוש"},
			{
				"language": "he",
				"key": "thankyou_would_have_earned_estimate",
				"value": "היית מרוויח לפחות {{estimate}} נקודות ברכישה זו"
			},
			{
				"language": "he",
				"key": "thankyou_would_have_earned_exact",
				"value": "היית מרוויח {{points}} נקודות ברכישה זו"
			},
			{
				"language": "he",
				"key": "thankyou_earned_estimate",
				"value": "הרווית לפחות {{estimate}} נקודות ברכישה זו"
			},
			{
				"language": "he",
				"key": "thankyou_earned_exact",
				"value": "הרווית {{points}} נקודות ברכישה זו"
			},
			{
				"language": "he",
				"key": "thankyou_vip_perk",
				"value": "תהנה מהיתרון שלך ברמה {{vipTier}}, {{vipPerk}} ברכישה הבאה שלך"
			},
			{
				"language": "he",
				"key": "thankyou_points_deficit",
				"value": "הרווי {{deficit}} נקודות נוספות כדי לקבל {{reward}} בעלות של {{price}} {{pointsName}}"
			},
			{
				"language": "he",
				"key": "thankyou_points_enough",
				"value": "יש לך מספיק נקודות לקבלת {{reward}} בעלות של {{price}} {{pointsName}}"
			},
			{
				"language": "he",
				"key": "thankyou_top_text_logged_in",
				"value": "עדכון תגמולים בשבילך, {{name}}"
			},
			{
				"language": "he",
				"key": "thankyou_top_text_logged_out",
				"value": "אל תחמיא על התגמולים!"
			},
			{
				"language": "he",
				"key": "thankyou_points_summary_line_1_estimate",
				"value": "לפחות {{points}} {{pointsName}}"
			},
			{
				"language": "he",
				"key": "thankyou_points_summary_line_1",
				"value": "{{points}} {{pointsName}}"
			},
			{
				"language": "he",
				"key": "thankyou_points_summary_line_2_earned",
				"value": "הרוויח"
			},
			{
				"language": "he",
				"key": "thankyou_points_summary_line_2_would_have_earned",
				"value": "מחכים לך"
			},
			{
				"language": "he",
				"key": "thankyou_reward_summary_line_1_deficit",
				"value": "{{points}} נקודות {{pointsName}} נוספות"
			},
			{
				"language": "he",
				"key": "thankyou_reward_summary_line_1_enough",
				"value": "יש לך מספיק {{pointsName}}"
			},
			{
				"language": "he",
				"key": "thankyou_reward_summary_line_2",
				"value": "ל{{rewardName}}"
			},
			{
				"language": "he",
				"key": "thankyou_claim_link",
				"value": "תבע את {{points}} {{pointsName}} שלך"
			},
			{language: "he", key: "product_page_guest_earn_teaser_divider", value: "|"},
			{language: "he", key: "thankyou_reward_summary_line_2_tier", value: "עד שתגיע ל{{tierName}}"},
			{language: "he", key: "view_external_link", value: "קבלו פרטים נוספים"},
			{ language: "he", key: "cart_free_gift_threshold_not_met_choice", value: "הוסף {{amount}} נוספים לעגלה כדי לקבל {{gift_name}}" },
			{ language: "he", key: "cart_free_gift_threshold_not_met_single", value: "הוסף {{amount}} נוספים לעגלה כדי לקבל {{gift_name}}" },
			{ language: "he", key: "cart_free_gift_threshold_met_choice", value: "זכית ב{{gift_name}}. הוא נוסף אוטומטית לעגלה שלך" },
			{ language: "he", key: "cart_free_gift_threshold_met_single", value: "זכית ב{{gift_name}}. הוא נוסף אוטומטית לעגלה שלך" },
			{ language: "he", key: "free_gift", value: "מתנה חינם" }

		];

		const seen = new Set<string>();
		const duplicates: Array<string> = [];
		const keys = new Set<string>();
		const languages = new Set<string>();
		translations.forEach((x: any) => {
			x['id'] = `${x.language}_${x.key}`;
			if (seen.has(x['id'])) {
				duplicates.push(x['id']);
			}
			keys.add(x.key);
			seen.add(x['id']);
			languages.add(x.language);
		});
		keys.forEach((key: string) => {
			translations.push({language: "test", key, value: `^ ____ ____ $`, id: `test_${key}`});
		});

		// check for missing translations
		const missing: Array<string> = [];
		languages.forEach((language: string) => {
			keys.forEach((key: string) => {
				const id = `${language}_${key}`;
				if (!translations.find(x => x.id === id)) {
					missing.push(id);
				}
			});
		});
		if (missing.length) {
			throw new Error(`



*************** MISSING TRANSLATIONS ***************:
			${missing.map(x => `\n - ${x}`).join('')
				}

****************************************************


			`);
		}

		if (duplicates.length) {
			throw new Error(`

*************** DUPLICATE TRANSLATIONS ***************:
			${duplicates.map(x => `\n - ${x}`).join('')
				}

****************************************************

`);
			}
		if (super.shouldRun()) {
			// Get all existing translations in one query
			const existingTranslations = await this.translationStringRepo.find();
			const existingTranslationsMap = new Map(existingTranslations.map(t => [t.id, t]));

			for (const translation of translations) {
				const existingTranslation = existingTranslationsMap.get(translation.id);

				if (!existingTranslation) {
					await this.translationStringRepo.create(translation);
					console.log(`Created new translation: ${translation.id}`);
				} else {
					console.log(`translation ${translation.id} already exists, updating metadata`);
					//Forcing an update on the model
					await this.translationStringRepo.updateById(existingTranslation.id, translation);
				}
			}
		}

		await this.translationService.populateCache();
	}

	/**
	 * This method will be invoked when the application stops.
	 */
	async stop(): Promise<void> {
		// Add your logic for stop
	}
}
