import {lifeCycleObserver} from '@loopback/core';
import {ALifeCycleObserver} from './a-lifecycle-observer';
import {repository} from '@loopback/repository';
import {PlannerCampaignRepository, TaskRepository, TaskStepRepository, TaskTypeRepository} from '../repositories';
import {PlannerCampaign, Task, TaskStep, TaskType} from '../models';

@lifeCycleObserver('Task')
export class TaskTemplateObserver extends ALifeCycleObserver {
  constructor(
	@repository(TaskRepository)
	private taskRepository: TaskRepository,
	@repository(TaskStepRepository)
	private taskStepRepository: TaskStepRepository,
	@repository(PlannerCampaignRepository)
	private plannerCampaignRepository: PlannerCampaignRepository,
	@repository(TaskTypeRepository)
	private taskTypeRepository: TaskTypeRepository

  ) {
    super();
  }

  /**
   * This method will be invoked when the application starts.
   */
  async start(): Promise<void> {
	if (!this.shouldRun()) return;

    await this.populateTaskTypes();
    await this.populateTaskTemplates();
  }


  /**
   * Populate the with predefined data..
   */
  private async populateTaskTypes(): Promise<void> {
    const taskTypeData: Partial<TaskType>[] = [
		{ id: 1, name: 'Schedule', taskType: 'Email', description: 'Define the email schedule.' },
		{ id: 2, name: 'Segment', taskType: 'Email', description: 'Select the target segment.' },
		{ id: 3, name: 'Content', taskType: 'Email', description: 'Review the email content.' },
		{ id: 4, name: 'Promotion', taskType: 'Email', description: 'Define the promotion details.' },
		{ id: 5, name: 'Schedule', taskType: 'SMS', description: 'Define the SMS schedule.' },
		{ id: 6, name: 'Segment', taskType: 'SMS', description: 'Select the target segment.' },
		{ id: 7, name: 'Content', taskType: 'SMS', description: 'Review the SMS content.' },
		{ id: 8, name: 'Promotion', taskType: 'SMS', description: 'Define the promotion details.' },
		{ id: 9, name: 'Points', taskType: 'Loyalty', description: 'Set the points multiplier.' },
		{ id: 10, name: 'Reward', taskType: 'Loyalty', description: 'Define the reward.' },
		{ id: 11, name: 'Expiry', taskType: 'Loyalty', description: 'Set the expiry date.' },
		{ id: 12, name: 'Review Email Design', taskType: 'Email', description: 'Review and approve email design.' },
		{ id: 13, name: 'Finalize Campaign', taskType: 'Email', description: 'Manage campaign setup and integration.' },
    ];

    for (const taskType of taskTypeData) {
      let existingTaskType: TaskType;
	  try {
		existingTaskType = await this.taskTypeRepository.findById(taskType.id);
	  } catch (e) {
		console.error(e);
	  }

      if (!existingTaskType!) {
        await this.taskTypeRepository.create(taskType);
        console.log(`Created task type: ${taskType.name}`);
      }else {
        await this.taskTypeRepository.updateById(existingTaskType.id, taskType);
        console.log(`Updated task type: ${taskType.name}`);
      }
    }
  }


  /**
   * Populate the with predefined data..
   */
  private async populateTaskTemplates(): Promise<void> {
    const plannerCampaignTemplateData: Partial<PlannerCampaign & { _task: Partial<Task> & { _steps: Partial<TaskStep>[] }}>[] = [
		{
			id: 1,
			name: 'Email Template',
			type: 'email',
			taskType: 'Email',
			scheduledDate: new Date().toISOString(),
			description: 'Template',
			targetSegment: 'default',
			isTemplate: true,
			_task: {
				name: 'Email Template',
				taskType: 'Email',
				scheduledDate: new Date().toISOString(),
				isTemplate: true,
				description: 'Template',
				_steps: [
					{
						name: 'Content',
						description: 'Review the email content.',
						status: 'Not Started',
						taskTypeId: 3,
						position: 0
					},
					{
						name: 'Segment',
						description: 'Select the target segment.',
						status: 'Not Started',
						taskTypeId: 2,
						position: 2
					},
					{
						name: 'Promotion',
						description: 'Define the promotion details.',
						status: 'Not Started',
						taskTypeId: 4,
						position: 3
					},
					{
						name: 'Schedule',
						description: 'Define the email schedule.',
						status: 'Not Started',
						taskTypeId: 1,
						position: 4
					},
					// {
					// 	name: 'Review Email',
					// 	description: 'Review and approve email design.',
					// 	status: 'Not Started',
					// 	taskTypeId: 12,
					// 	position: 1
					// },
					{
						name: 'Klaviyo Campaign',
						description: 'Manage Klaviyo campaign setup and integration.',
						status: 'Not Started',
						taskTypeId: 13,
						position: 5
					}
				]
			}
		},
    ];
	for (const plannerCampaign of plannerCampaignTemplateData) {
		let existingPlannerCampaign: PlannerCampaign;
		try {
			existingPlannerCampaign = await this.plannerCampaignRepository.findById(plannerCampaign.id);
		} catch (e) {
			console.error(e);
		}

		const persistableCampaign = {
			...plannerCampaign,
			_task: undefined as any
		};
		delete persistableCampaign._task;

		if (!existingPlannerCampaign!) {
			existingPlannerCampaign = await this.plannerCampaignRepository.create(persistableCampaign);
			console.log(`Created planner campaign template: ${persistableCampaign.name}`);
		}
		else {
			await this.plannerCampaignRepository.updateById(existingPlannerCampaign.id, persistableCampaign);
			console.log(`Updated planner campaign template: ${persistableCampaign.name}`);
		}

		const taskType = plannerCampaign._task!;
		let existingTaskTemplate: Task;
		try {
			existingTaskTemplate = await this.taskRepository.findOne({
				where: {
					plannerCampaignId: plannerCampaign.id
				}
			}) as Task;
		} catch (e) {
			console.error(e);
		}

		const persistableTaskType = {
			...taskType,
			plannerCampaignId: plannerCampaign.id,
			_steps: undefined as any
		};
		delete persistableTaskType._steps;

		if (!existingTaskTemplate!) {
			existingTaskTemplate = await this.taskRepository.create(persistableTaskType);
			console.log(`Created task template: ${persistableTaskType.name}`);
		} else {
			await this.taskRepository.updateById(existingTaskTemplate.id, persistableTaskType);
			console.log(`Updated task template: ${persistableTaskType.name}`);
		}

		const existingTaskSteps = await this.taskRepository.taskSteps(existingTaskTemplate!.id).find();

		// Delete any existing steps that are no longer in the template
		for (const existingStep of existingTaskSteps) {
			const templateStep = taskType._steps!.find(s => s.taskTypeId === existingStep.taskTypeId);
			if (!templateStep) {
				await this.taskStepRepository.deleteById(existingStep.id);
				console.log(`Deleted outdated task step: ${existingStep.name}`);
			}
		}

		for (const step of taskType._steps!) {
			// Match by taskTypeId instead of position for more reliable updates
			const existingStep = existingTaskSteps.find(s => s.taskTypeId === step.taskTypeId);
			if (!existingStep) {
				await this.taskRepository.taskSteps(existingTaskTemplate!.id).create(step);
				console.log(`Created task step: ${step.name}`);
			} else {
				await this.taskStepRepository.updateById(existingStep.id, step);
				console.log(`Updated task step: ${step.name}`);
			}
		}
	}

  }

  /**
   * This method will be invoked when the application stops.
   */
  async stop(): Promise<void> {
    // Implement any cleanup logic if necessary
    console.log('TaskTypeObserver has stopped.');
  }
}
