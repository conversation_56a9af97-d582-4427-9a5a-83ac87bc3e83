import {
	/* inject, Application, CoreBindings, */
	lifeCycleObserver, // The decorator
	LifeCycleObserver, // The interface
} from '@loopback/core';
import {repository} from '@loopback/repository';
import {UiActionRewardJunctionRepository, UiCustomerActionConditionRepository, UiCustomerActionRepository, UiCustomerRewardRepository, UiRewardRestrictionRepository} from '../repositories';
import {ALifeCycleObserver} from './a-lifecycle-observer';

/**
 * This class will be bound to the application as a `LifeCycleObserver` during
 * `boot`
 */
@lifeCycleObserver('Data')
export class UiCustomerActionsObserver extends ALifeCycleObserver {

	constructor(
		@repository(UiCustomerActionRepository) private uiCustomerActionRepo: UiCustomerActionRepository,
		@repository(UiCustomerActionConditionRepository) private uiCustomerActionConditionRepo: UiCustomerActionConditionRepository,
		@repository(UiCustomerRewardRepository) private uiCustomerRewardRepo: UiCustomerRewardRepository,
		@repository(UiRewardRestrictionRepository) private uiRewardRestrictionRepo: UiRewardRestrictionRepository,
		@repository(UiActionRewardJunctionRepository) private uiActionRewardJunctionRepo: UiActionRewardJunctionRepository
	) {
		super();
	}


	/**
	 * This method will be invoked when the application initializes. It will be
	 * called at most once for a given application instance.
	 */
	async init(): Promise<void> {
		// Add your logic for init
	}

	/**
	 * This method will be invoked when the application starts.
	 */
	async start(): Promise<void> {
		if (!super.shouldRun()) return;
		// Add your logic for start
		let uiCustomerActions = [];
		let uiCustomerActionConditions = [{
			id: 1, label: "purchases", defaultAmount: 3,
			operatorLabel: "Number of", operatorComparison: "==",
			variable: "purchaseCount", uiCustomerActionId: 2,
			propertyType: "number"
		},{
			id: 3, label: "greater than", defaultAmount: 0,
			operatorLabel: "On purchases", operatorComparison: ">",
			variable: "purchaseTotalIncrease", uiCustomerActionId: 1,
			propertyType: "number"
		}, {
			id: 4, label: "Handle",
			operatorLabel: "Instagram", operatorComparison: "==",
			variable: "instagramHandle", uiCustomerActionId: 3,
			propertyType: "string",
			placeholderText: "@raleon",
		}, {
			id: 5, label: "Reviews", defaultAmount: 3,
			operatorLabel: "Max Eligible", operatorComparison: "==",
			variable: "maxReviews", uiCustomerActionId: 4,
			propertyType: "number"
		},{
			id: 6, label: "Handle", defaultAmount: undefined,
			operatorLabel: "TikTok", operatorComparison: "==",
			variable: "tikTokHandle", uiCustomerActionId: 5,
			placeholderText: "@raleon",
			propertyType: "string"
		},{
			id: 7, label: "Account", defaultAmount: undefined,
			operatorLabel: "Facebook", operatorComparison: "==",
			variable: "facebookHandle", uiCustomerActionId: 6,
			placeholderText: "Raleon",
			propertyType: "string"
		},{
			id: 16, label: "Group", defaultAmount: undefined,
			operatorLabel: "Facebook", operatorComparison: "==",
			variable: "facebookGroup", uiCustomerActionId: 16,
			placeholderText: "Raleon",
			propertyType: "string"
		},{
			id: 20, label: "Handle", defaultAmount: undefined,
			operatorLabel: "YouTube", operatorComparison: "==",
			variable: "youtubeHandle", uiCustomerActionId: 21,
			placeholderText: "Raleon",
			propertyType: "string"
		},{
			id: 21, label: "Website", defaultAmount: undefined,
			operatorLabel: "Website", operatorComparison: "==",
			variable: "websiteUrl", uiCustomerActionId: 22,
			placeholderText: "raleon.com",
			propertyType: "string"
		},{
			id: 8, label: "Purchases", defaultAmount: 3,
			operatorLabel: "Number of", operatorComparison: "==",
			variable: "purchaseCount", uiCustomerActionId: 7,
			propertyType: "number"
		},{
			id: 9, label: "joins", defaultAmount: 1,
			operatorLabel: "Customer", operatorComparison: "==",
			variable: "joins", uiCustomerActionId: 8,
			propertyType: "number"
		},{
			id: 10, label: "Product", defaultAmount: undefined,
			operatorLabel: "Customer Buys This", operatorComparison: "==",
			variable: "productId", uiCustomerActionId: 10,
			propertyType: "product"
		},{
			id: 11, label: "Collection", defaultAmount: undefined,
			operatorLabel: "Customer Buys From", operatorComparison: "==",
			variable: "collectionId", uiCustomerActionId: 11,
			propertyType: "collection"

		},{
			id: 12, label: "collection", defaultAmount: 1,
			operatorLabel: "Quantity of items in a single order from this", operatorComparison: "==",
			variable: "purchaseCount", uiCustomerActionId: 11,
			propertyType: "number"
		},{
			id: 13, label: "referred", defaultAmount: 1,
			operatorLabel: "Customer", operatorComparison: "==",
			variable: "referred", uiCustomerActionId: 12,

		},{
			id: 14, label: "referrer", defaultAmount: 1,
			operatorLabel: "Customer", operatorComparison: "==",
			variable: "referrer", uiCustomerActionId: 13
		},{
			id: 15, label: "subscription purchases", defaultAmount: 1,
			operatorLabel: "Number of", operatorComparison: "==",
			variable: "purchaseCount", uiCustomerActionId: 14,
			propertyType: "number"
		}, {
			id: 17, label: "Reviews", defaultAmount: 1,
			operatorLabel: "Max Eligible", operatorComparison: "==",
			variable: "maxReviews", uiCustomerActionId: 17,
			propertyType: "number"
		}, {
			id: 18, label: "Targeting", defaultAmount: undefined,
			operatorLabel: "Giveaway", operatorComparison: "==",
			variable: "segmentId", uiCustomerActionId: 19,
			propertyType: "targetSegment"
		},
		{
			id: 19, label: "Targeting", defaultAmount: undefined,
			operatorLabel: "Giveaway", operatorComparison: "==",
			variable: "segmentId", uiCustomerActionId: 20,
			propertyType: "targetSegment"
		}
		];
		uiCustomerActions.push([
			{
				id: 1, type: 'dollar-spent', name: 'Make a Purchase',
				description: "Earn points for every purchase.",
				chainable: false, triggeredEvent: "orders/create", imageSlotKey: "dollar-spent", maxRewardsForThisAction: 1,
				enabled: true, includeInAI: true
			},
			{
				id: 2, type: 'nth-purchase', name: 'Milestone Purchases',
				description: "Reward customers when they make a specified number of purchases.",
				chainable: true, triggeredEvent: "orders/create", imageSlotKey: "nth-purchase", maxRewardsForThisAction: 5,
				enabled: true, includeInAI: true, includeInAICampaigns: false
			},
			{
				id: 3, type: 'follow-on-instagram', name: 'Follow On Instagram',
				description: "Reward customers when they follow on Instagram. Specify the Instagram handle below.",
				chainable: true, triggeredEvent: "instagram", imageSlotKey: "ig-follow", maxRewardsForThisAction: 5,
				enabled: true, includeInAI: false
			},
			{
				id: 4, type: 'product-review', name: 'Leave a Review',
				description: "Reward customers for leaving a review. Limit customers to the maximum number of eligible reviews below. *An integration needs to be enabled for this to work.",
				chainable: true, triggeredEvent: "product/review", imageSlotKey: "product-review", maxRewardsForThisAction: 5,
				enabled: true, integrationCategory: "product-review", includeInAI: false
			},
			{
				id: 5, type: 'follow-on-tiktok', name: 'Follow On TikTok',
				description: "Follow us on TikTok and get rewarded!",
				chainable: true, triggeredEvent: "tiktok", imageSlotKey: "tt-follow", maxRewardsForThisAction: 5,
				enabled: true, includeInAI: false
			},
			{
				id: 6, type: 'follow-on-facebook', name: 'Like On Facebook',
				description: "Like us on Facebook and get rewarded!",
				chainable: true, triggeredEvent: "facebook", imageSlotKey: "fb-follow", maxRewardsForThisAction: 5,
				enabled: true, includeInAI: false
			},
			{
				id: 16, type: 'follow-facebook-group', name: 'Join Facebook Group',
				description: "Follow our Facebook group and get rewarded!",
				chainable: true, triggeredEvent: "facebook", imageSlotKey: "fb-follow", maxRewardsForThisAction: 5,
				enabled: true, includeInAI: false
			},
			{
				id: 21, type: 'follow-on-youtube', name: 'Subscribe On YouTube',
				description: "Subscribe to our channel on YouTube and get rewarded!",
				chainable: true, triggeredEvent: "youtube", imageSlotKey: "yt-follow", maxRewardsForThisAction: 5,
				enabled: true, includeInAI: false
			},
			{
				id: 22, type: 'follow-on-custom', name: 'Custom Social Follow',
				description: "Follow us and get rewarded!",
				chainable: true, triggeredEvent: "website", imageSlotKey: "website-follow", maxRewardsForThisAction: 5,
				enabled: true, includeInAI: false
			},
			{
				id: 7, type: 'timed-purchase', name: 'Time Based Purchases',
				description: "Reward customers when they make a specified number of purchases before the campaign ends.",
				chainable: true, triggeredEvent: "orders/create", imageSlotKey: "timed-purchase", maxRewardsForThisAction: 5,
				enabled: true, includeInAI: true, includeInAICampaigns: true
			},
			{
				id: 8, type: 'welcome-bonus', name: 'Welcome Bonus',
				description: "Rewards customers when they sign up to become a loyalty member.",
				chainable: true, triggeredEvent: "join", imageSlotKey: "welcome-bonus", maxRewardsForThisAction: 5,
				enabled: true, includeInAI: true, includeInAICampaigns: true
			},
			{
				id: 9, type: 'birthday-bonus', name: 'Birthday Bonus',
				description: "Reward customers on their birthday month when the customer provides their birthday.",
				chainable: true, triggeredEvent: "birthday", imageSlotKey: "birthday-bonus", maxRewardsForThisAction: 1,
				enabled: true, includeInAI: true, includeInAICampaigns: true
			},
			{
				id: 10,
				type: 'specific-product-purchase',
				name: 'Specific Product Purchase',
				description: "Reward customers for purchasing a specific product",
				chainable: true,
				triggeredEvent: "orders/create",
				imageSlotKey: "specific-product-purchase",
				maxRewardsForThisAction: 5,
				enabled: true,
				includeInAI: true
			},
			{
				id: 11,
				type: 'collection-product-purchase',
				name: 'Product From Collection Purchase',
				description: "Reward customers for purchasing a product from a specific collection",
				chainable: true,
				triggeredEvent: "orders/create",
				imageSlotKey: "collection-product-purchase",
				maxRewardsForThisAction: 5,
				enabled: true,
				includeInAI: true
			},
			{
				id: 14,
				type: 'milestone-subscription-purchase',
				name: 'Milestone Subscription Purchase',
				description: "Reward customers for making a specified number of subscription purchases",
				chainable: true,
				triggeredEvent: "orders/create",
				imageSlotKey: "milestone-subscription-purchase",
				maxRewardsForThisAction: 5,
				enabled: true,
				includeInAI: false,
				integrationCategory: 'subscriptions',
			},
			{
				id: 15,
				type: 'first-subscription-purchase',
				name: 'First Subscription Purchase',
				description: "Reward customers for making their first subscription purchase",
				chainable: true,
				triggeredEvent: "orders/create",
				imageSlotKey: "first-subscription-purchase",
				maxRewardsForThisAction: 5,
				enabled: true,
				includeInAI: false,
				integrationCategory: 'subscriptions',
			},
			{
				id: 18,
				type: 'subscription-purchase',
				name: 'Subscription Purchase',
				description: "Reward customers for making a subscription purchase",
				chainable: true,
				triggeredEvent: "orders/create",
				imageSlotKey: "first-subscription-purchase",
				maxRewardsForThisAction: 5,
				enabled: true,
				includeInAI: false,
				integrationCategory: 'subscriptions',
			},
			{
				id: 17, type: 'product-photo-review', name: 'Leave a Photo Review',
				description: "Reward customers for leaving a review including a Photo. Limit customers to the maximum number of eligible reviews below. *An integration needs to be enabled for this to work.",
				chainable: true, triggeredEvent: "product/review", imageSlotKey: "product-photo-review", maxRewardsForThisAction: 1,
				enabled: true, integrationCategory: "product-review", includeInAI: false
			},
		])
		if (process.env.NODE_ENV != 'production') {
			uiCustomerActions.push([
				{
					id: 12, type: 'referred-customer-bonus', name: 'Referral Bonus',
					description: "Rewards new customers when they make a purchase after being referred by someone else.",
					chainable: true, triggeredEvent: "referred", imageSlotKey: "referral-bonus", maxRewardsForThisAction: 5,
					enabled: (process.env.NODE_ENV != 'production'), includeInAI: false, includeInAICampaigns: false
				},
				{
					id: 13, type: 'referrer-bonus', name: 'Referrer Bonus',
					description: "Rewards existing customers when someone they referred makes their first purchase.",
					chainable: true, triggeredEvent: "referrer", imageSlotKey: "referrer-bonus", maxRewardsForThisAction: 5,
					enabled: (process.env.NODE_ENV != 'production'), includeInAI: false, includeInAICampaigns: false
				},
				{
					id: 19,
					type: 'click-to-redeem',
					name: 'Click to Enter Giveaway',
					description: "Reward customers by clicking a button to redeem a reward.",
					chainable: true,
					triggeredEvent: "click-to-redeem",
					imageSlotKey: "click-to-redeem",
					maxRewardsForThisAction: 5,
					enabled: true,
					includeInAI: false,
					hiddenFromWteConfig: true,
				},
				{
					id: 20,
					type: 'auto-redeem',
					name: 'Auto Enter Giveaway',
					description: "Reward customers with no action on their part.",
					chainable: true,
					triggeredEvent: "auto-redeem",
					imageSlotKey: "auto-redeem",
					maxRewardsForThisAction: 5,
					enabled: true,
					includeInAI: false,
					hideFromWteConfig: true
				},
			])
		}
		else {
			uiCustomerActions.push([
			])
		}
		// Get all existing actions in one query
		const existingActions = await this.uiCustomerActionRepo.find();
		const existingActionsMap = new Map(existingActions.map(a => [a.id, a]));

		for(const uiCustomerAction of uiCustomerActions) {
			for(const action of uiCustomerAction) {
				const existingAction = existingActionsMap.get(action.id);

				if (!existingAction) {
					await this.uiCustomerActionRepo.create(action);
					console.log(`Created new uiCustomerAction: ${action.name}`);
				} else {
					console.log(`uiCustomerAction ${action.name} already exists, updating metadata`);
					//Forcing an update on the model
					await this.uiCustomerActionRepo.updateById(existingAction.id, action);
				}
			}
		}

		for (const uiCustomerActionCondition of uiCustomerActionConditions) {
			const existingCondition = await this.uiCustomerActionConditionRepo.findOne({
				where: {id: uiCustomerActionCondition.id},
			});

			if (!existingCondition) {
				await this.uiCustomerActionConditionRepo.create(uiCustomerActionCondition);
				console.log(`Created new uiCustomerActionCondition: ${uiCustomerActionCondition.label}`);
			} else {
				console.log(`uiCustomerActionCondition ${uiCustomerActionCondition.label} already exists, updating metadata`);
				//Forcing an update on the model
				await this.uiCustomerActionConditionRepo.updateById(existingCondition.id, uiCustomerActionCondition);
			}
		}

		await this.updateCustomerRewards();
		await this.updateActionRewardJunctions();
	}

	async updateCustomerRewards(): Promise<void> {
		let uiCustomerRewards = [
			{
				id: 2,
				type: 'points-per-dollar',
				title: 'Points Per <currencyPrefix>1<currencyPostfix> Spent',
				subtitle: 'Customers are awarded points for the amount spent.',
				label: 'Points Per <currencyPrefix>1<currencyPostfix> Spent',
				hasRestrictions: false,
				hasLimits: false,
				typeDataSchema: '{"type": "number"}',
				imageSlotKey: "points-per-dollar-reward",
				canShowInShop: false,
				enabled: true,
				includeInAI: true,
			},
			{
				id: 3,
				type: 'points',
				title: 'Points',
				subtitle: 'Number of points to reward customers.',
				label: 'Number of Points',
				hasRestrictions: false,
				hasLimits: true,
				typeDataSchema: '{"type": "number"}',
				imageSlotKey: "points-per-reward",
				canShowInShop: false,
				enabled: true,
				includeInAI: true,
				includeInAICampaigns: true,
			},
			{
				id: 4,
				type: 'dollars-off-coupon',
				title: 'Cash Coupon',
				subtitle: 'Reward customers with a cash coupon to be used on a future purchase.',
				label: 'Coupon Amount',
				hasRestrictions: true,
				hasLimits: true,
				typeDataSchema: '{"type": "number"}',
				imageSlotKey: "dollar-off-reward",
				canShowInShop: true,
				canShowInPerks: false,
				enabled: true,
				includeInAI: true,
				includeInAICampaigns: true,
			},
			{
				id: 5,
				type: 'percent-discount',
				title: 'Percent Off Coupon',
				subtitle: 'Reward customers with a percent off coupon to be used on a future purchase.',
				label: 'Percent Off Amount',
				hasRestrictions: true,
				hasLimits: true,
				typeDataSchema: '{"type": "number"}',
				imageSlotKey: "percent-off-reward",
				canShowInShop: true,
				canShowInPerks: false,
				enabled: true,
				includeInAI: true,
				includeInAICampaigns: true,
			},
			{
				id: 6,
				type: 'free-product',
				title: 'Free Product',
				subtitle: 'Reward customers with a free product from a collection to be used on a future purchase.',
				label: 'Free Product from Collection',
				hasRestrictions: true,
				hasLimits: true,
				typeDataSchema: '{"type": "number"}',
				imageSlotKey: "free-product-reward",
				canShowInShop: true,
				canShowInPerks: false,
				enabled: true
			},
			{
				id: 7,
				type: 'free-shipping',
				title: 'Free Shipping Coupon',
				subtitle: 'Reward customers with a free shipping coupon to be used on a future purchase.',
				label: 'Free Shipping',
				hasRestrictions: true,
				hasLimits: false,
				typeDataSchema: '{"type": "number"}',
				imageSlotKey: "free-shipping-reward",
				canShowInShop: true,
				canShowInPerks: false,
				enabled: true
			},
			{
				id: 8,
				type: 'points-multiplier',
				title: 'Points Multiplier',
				subtitle: 'Reward customers with bonus points for a limited time or based on a VIP Tier.',
				label: 'Points Multiplier',
				hasRestrictions: false,
				hasLimits: false,
				typeDataSchema: '{"type": "number"}',
				imageSlotKey: "points-multiplier",
				canShowInShop: false,
				canShowInPerks: true,
				enabled: true
			},
			{
				id: 9,
				type: 'giveaway-entry',
				title: 'Giveaway Entry',
				subtitle: 'Reward customers with an entry into a giveaway.',
				label: 'Number of Entries',
				hasRestrictions: false,
				hasLimits: false,
				typeDataSchema: '{"type": "number"}',
				imageSlotKey: "giveaway-entry",
				canShowInShop: false,
				canShowInPerks: false,
				hideFromWteConfig: true,
				showInGiveawayConfig: true,
				enabled: true
			},
			{
				id: 10,
				type: 'static-text',
				title: 'Custom Perk',
				subtitle: 'Custom Perks are used to display a branded message for perks that are manually fulfilled',
				label: 'Custom Perk',
				hasRestrictions: false,
				hasLimits: false,
				typeDataSchema: '{}',
				imageSlotKey: "free-shipping-reward",
				canShowInShop: false,
				canShowInPerks: true,
				enabled: true
			},
			{
				id: 11,
				type: 'percent-off-product',
				title: 'Percent Off Specific Product',
				subtitle: 'Gives a coupon that can be used on a specific product',
				label: 'Percent Off Specific Product',
				hasRestrictions: true,
				hasLimits: false,
				typeDataSchema: '{}',
				imageSlotKey: "free-shipping-reward",
				canShowInShop: true,
				canShowInPerks: false,
				enabled: true
			},
			{
				id: 12,
				type: 'dollar-off-product',
				title: 'Dollars Off Specific Product',
				subtitle: 'Gives a coupon that can be used on a specific product',
				label: 'Dollars Off Specific Product',
				hasRestrictions: true,
				hasLimits: false,
				typeDataSchema: '{}',
				imageSlotKey: "free-shipping-reward",
				canShowInShop: true,
				canShowInPerks: false,
				enabled: true
			},
			{
				id: 13,
				type: 'static-text-click',
				title: 'Custom Perk with Button',
				subtitle: 'Custom Perks are used to display a branded message for perks that are manually fulfilled',
				label: 'Custom Perk',
				hasRestrictions: false,
				hasLimits: false,
				typeDataSchema: '{}',
				imageSlotKey: "free-shipping-reward",
				canShowInShop: false,
				canShowInPerks: true,
				enabled: true
			},
		];
		// Get all existing rewards in one query
		const existingRewards = await this.uiCustomerRewardRepo.find();
		const existingRewardsMap = new Map(existingRewards.map(r => [r.id, r]));

		for(const uiReward of uiCustomerRewards) {
			const existingReward = existingRewardsMap.get(uiReward.id);

			if (!existingReward) {
				await this.uiCustomerRewardRepo.create(uiReward);
				console.log(`Created new uiCustomerReward: ${uiReward.title}`);
			} else {
				console.log(`uiCustomerReward ${uiReward.title} already exists, updating metadata`);
				//Forcing an update on the model
				await this.uiCustomerRewardRepo.updateById(existingReward.id, uiReward);
			}
		}

		// Create the restrictions for the percent off product reward
		await this.addRestriction(11, 'minimumOrderTotal', 'Minimum Order Amount', 'number', 0, 'rewardcoupon');
		await this.addRestriction(11, 'expiresInDays', 'Expires In (Days)', 'number', 30, 'rewardcoupon', 1, true);
		await this.addRestriction(11, 'maximumDiscount', 'Maximum Discount', 'number', 0, 'rewardcoupon');

		// Create the restrictions for the dollar off product reward
		await this.addRestriction(12, 'minimumOrderTotal', 'Minimum Order Amount', 'number', 0, 'rewardcoupon');
		await this.addRestriction(12, 'expiresInDays', 'Expires In (Days)', 'number', 30, 'rewardcoupon', 1, true);
		await this.addRestriction(12, 'maximumDiscount', 'Maximum Discount', 'number', 0, 'rewardcoupon');

		const rewardIds = [6,7];
		for(const rewardId of rewardIds) {
			const existingRestrictions = await this.uiRewardRestrictionRepo.findOne({
				where: {uiCustomerRewardId: rewardId, fieldOnDefinition: 'minimumOrderTotal'},
			});
			if (!existingRestrictions) {
				await this.uiRewardRestrictionRepo.create({
					uiCustomerRewardId: rewardId,
					name: 'Min Order Amount',
					inputType: 'number',
					fieldOnDefinition: 'minimumOrderTotal',
					defaultAmount: 0,
					definitionModel: 'rewardcoupon',
				});
			} else {
				console.log(`uiRewardRestriction already exists, skipping`);
			}
		}


		const existingRestrictions2 = await this.uiRewardRestrictionRepo.findOne({
			where: {uiCustomerRewardId: 7, fieldOnDefinition: 'expiresInDays'},
		});

		if (!existingRestrictions2) {
			await this.uiRewardRestrictionRepo.create({
				uiCustomerRewardId: 7,
				name: 'Expires In (Days)',
				inputType: 'number',
				fieldOnDefinition: 'expiresInDays',
				defaultAmount: 30,
				definitionModel: 'rewardCoupon',
				minimumAmount: 1,
				required: true,
			});
		} else {
			console.log(`uiRewardRestriction for Free Shipping already exists, skipping`);
		}

		const maximumShippingDiscount = await this.uiRewardRestrictionRepo.findOne({
			where: {uiCustomerRewardId: 7, fieldOnDefinition: 'maximumDiscount'},
		});

		if (!maximumShippingDiscount) {
			await this.uiRewardRestrictionRepo.create({
				uiCustomerRewardId: 7,
				name: 'Maximum Shipping Price',
				inputType: 'number',
				fieldOnDefinition: 'maximumDiscount',
				defaultAmount: 0,
				definitionModel: 'rewardcoupon',
			});
		}
	}

	async updateActionRewardJunctions(): Promise<void> {
		const actionRewardPairs = [
			{action: 2, reward: 6},
			{action: 2, reward: 7},
			{action: 2, reward: 11},
			{action: 2, reward: 12},
			{action: 3, reward: 4},
			{action: 3, reward: 5},
			{action: 3, reward: 6},
			{action: 3, reward: 7},
			{action: 3, reward: 11},
			{action: 3, reward: 12},
			{action: 4, reward: 3},
			{action: 4, reward: 4},
			{action: 4, reward: 5},
			{action: 4, reward: 6},
			{action: 4, reward: 7},
			{action: 4, reward: 11},
			{action: 4, reward: 12},
			{action: 5, reward: 3},
			{action: 5, reward: 4},
			{action: 5, reward: 5},
			{action: 5, reward: 6},
			{action: 5, reward: 7},
			{action: 5, reward: 11},
			{action: 5, reward: 12},
			{action: 6, reward: 3},
			{action: 6, reward: 4},
			{action: 6, reward: 5},
			{action: 6, reward: 6},
			{action: 6, reward: 7},
			{action: 6, reward: 11},
			{action: 6, reward: 12},
			{action: 7, reward: 3},
			{action: 7, reward: 4},
			{action: 7, reward: 5},
			{action: 7, reward: 7},
			{action: 7, reward: 11},
			{action: 7, reward: 12},
			{action: 8, reward: 3},
			{action: 8, reward: 4},
			{action: 8, reward: 5},
			{action: 8, reward: 6},
			{action: 8, reward: 7},
			{action: 8, reward: 11},
			{action: 8, reward: 12},
			{action: 9, reward: 3},
			{action: 9, reward: 4},
			{action: 9, reward: 5},
			{action: 9, reward: 6},
			{action: 9, reward: 7},
			{action: 9, reward: 11},
			{action: 9, reward: 12},
			{action: 10, reward: 3},
			{action: 10, reward: 4},
			{action: 10, reward: 5},
			{action: 10, reward: 6},
			{action: 10, reward: 7},
			{action: 10, reward: 11},
			{action: 10, reward: 12},
			{action: 11, reward: 3},
			{action: 11, reward: 4},
			{action: 11, reward: 5},
			{action: 11, reward: 6},
			{action: 11, reward: 7},
			{action: 11, reward: 11},
			{action: 11, reward: 12},
			{action: 12, reward: 3},
			{action: 12, reward: 4},
			{action: 12, reward: 5},
			{action: 12, reward: 6},
			{action: 12, reward: 7},
			{action: 12, reward: 11},
			{action: 12, reward: 12},
			{action: 13, reward: 3},
			{action: 13, reward: 4},
			{action: 13, reward: 5},
			{action: 13, reward: 6},
			{action: 13, reward: 7},
			{action: 13, reward: 11},
			{action: 13, reward: 12},
			{action: 14, reward: 3},
			{action: 14, reward: 4},
			{action: 14, reward: 5},
			{action: 14, reward: 6},
			{action: 14, reward: 7},
			{action: 14, reward: 11},
			{action: 14, reward: 12},
			{action: 15, reward: 3},
			{action: 15, reward: 4},
			{action: 15, reward: 5},
			{action: 15, reward: 6},
			{action: 15, reward: 7},
			{action: 15, reward: 11},
			{action: 15, reward: 12},
			{action: 16, reward: 3},
			{action: 16, reward: 4},
			{action: 16, reward: 5},
			{action: 16, reward: 6},
			{action: 16, reward: 7},
			{action: 16, reward: 11},
			{action: 16, reward: 12},
			{action: 17, reward: 3},
			{action: 17, reward: 4},
			{action: 17, reward: 5},
			{action: 17, reward: 6},
			{action: 17, reward: 7},
			{action: 17, reward: 11},
			{action: 17, reward: 12},
			{action: 18, reward: 3},
			{action: 18, reward: 4},
			{action: 18, reward: 5},
			{action: 18, reward: 6},
			{action: 18, reward: 7},
			{action: 18, reward: 11},
			{action: 18, reward: 12},
			{action: 19, reward: 3},
			{action: 19, reward: 4},
			{action: 19, reward: 5},
			{action: 19, reward: 6},
			{action: 19, reward: 7},
			{action: 19, reward: 11},
			{action: 19, reward: 12},
			{action: 19, reward: 9},
			{action: 20, reward: 3},
			{action: 20, reward: 4},
			{action: 20, reward: 5},
			{action: 20, reward: 6},
			{action: 20, reward: 7},
			{action: 20, reward: 11},
			{action: 20, reward: 12},
			{action: 20, reward: 9},
			{action: 21, reward: 3},
			{action: 21, reward: 4},
			{action: 21, reward: 5},
			{action: 21, reward: 6},
			{action: 21, reward: 7},
			{action: 21, reward: 11},
			{action: 21, reward: 12},
			{action: 22, reward: 3},
			{action: 22, reward: 4},
			{action: 22, reward: 5},
			{action: 22, reward: 6},
			{action: 22, reward: 7},
			{action: 22, reward: 11},
			{action: 22, reward: 12},
		];

		for(const pair of actionRewardPairs) {
			const existingPair = await this.uiActionRewardJunctionRepo.findOne({
				where: {and: [{uiCustomerActionId: pair.action}, {uiCustomerRewardId: pair.reward}]},
			});

			if (!existingPair) {
				await this.uiActionRewardJunctionRepo.create({uiCustomerActionId: pair.action, uiCustomerRewardId: pair.reward});
				console.log(`Created new uiActionRewardJunction: ${pair.action} - ${pair.reward}`);
			} else {
				console.log(`uiActionRewardJunction ${pair.action} - ${pair.reward} already exists, skipping`);
			}
		}
	}

	async addRestriction(idOfReward: number, field: string, name: string, inputType: string, defaultAmount: number, definitionModel: string, minimumAmount?: number, required?: boolean): Promise<void> {
		let obj = await this.uiRewardRestrictionRepo.findOne({
			where: {uiCustomerRewardId: idOfReward, fieldOnDefinition: field},
		});

		if (!obj) {
			await this.uiRewardRestrictionRepo.create({
				uiCustomerRewardId: idOfReward,
				name: name,
				inputType: inputType,
				fieldOnDefinition: field,
				defaultAmount: defaultAmount,
				definitionModel: definitionModel,
				minimumAmount: minimumAmount,
				required: required,
			});
		}
	}

	/**
	 * This method will be invoked when the application stops.
	 */
	async stop(): Promise<void> {
		// Add your logic for stop
	}
}
