import {ASegment} from './segment.abstract';
import {FilterCustomEvents} from './segments/filter-custom-events.segment';
import {FirstTransactionSegment} from './segments/first-transaction.segment';
import {IncludeSmartContractsSegment} from './segments/include-smart-contracts.segment';
import {LastActivitySegment} from './segments/last-activity.segment';
import {ProjectUsersSegment} from './segments/project-users.source.segment';
import {TransactionCountSegment} from './segments/transaction-count.segment';
import {UsersHaveNFTSegment} from './segments/users-have-nft.segment';
import {UsersHaveTokenSegment} from './segments/users-have-token.segment';
import {UsersNetWorth} from './segments/users-net-worth';
import {AnyTransactionCountSegment} from './segments/any-transaction-count.segment';
import {FilterNFTAverageUsdPurchasePrice} from './segments/filter-nft-average-usd-purchase-price.segment';
import { FilterHasMintedAnyNFT } from './segments/filter-has-minted-nft.segment';
import { FilterMintedForEth } from './segments/filter-minted-for-eth.segment';
import { FilterMintedForMatic } from './segments/filter-minted-for-matic.segment';
import { FilterHasBoughtNftForUsd } from './segments/filter-has-bought-nft-for-usd.segment';
import { FilterHasMintedNftForUsd } from './segments/filter-has-minted-nft-for-usd.segment';
import { FilterNftSoldValue } from './segments/filter-nft-sold-value.segment';
import { FilterHasMintedSpecificNft } from './segments/filter-has-minted-specific-nft.segment';
import { FilterUtmCampaign } from './segments/filter-utm-campaign.segment';
import { FilterCustomTrait } from './segments/filter-custom-trait.segment';
import { FilterAddressesThatSentTo } from './segments/filter-addresses-that-sent-to.segment';
import { FilterAddressesThatReceivedFrom } from './segments/filter-addresses-that-received-from.segment';
import {FirstCustomEventSegment} from './segments/first-custom-event.segment';

const segments: any = {
	FirstTransactionSegment,
	FirstCustomEventSegment,
	ProjectUsersSegment,
	AnyTransactionCountSegment,
	TransactionCountSegment,
	LastActivitySegment,
	UsersNetWorth,
	UsersHaveTokenSegment,
	IncludeSmartContractsSegment,
	UsersHaveNFTSegment,
	FilterCustomEvents,
	FilterUtmCampaign,
	FilterCustomTrait,
	FilterHasBoughtNftForUsd,
	FilterNFTAverageUsdPurchasePrice,
	FilterHasMintedAnyNFT,
	FilterHasMintedNftForUsd,
	FilterMintedForEth,
	FilterMintedForMatic,
	FilterNftSoldValue,
	FilterHasMintedSpecificNft,
	FilterAddressesThatSentTo,
	FilterAddressesThatReceivedFrom
}

export class SegmentFactory {

	static createSegment(name: string, ...args: any): ASegment {
		return new segments[name](...args);
	}

	static createSegmentFromInstruction(instruction: string, ...args: any): ASegment {
		const matchedSegment: any = Object.values(segments).find((segment: any) => {
			return segment.INSTRUCTION_NAME === instruction;
		});
		if (!matchedSegment) {
			throw new Error('No segment found for instruction: ' + instruction);
		}
		return new matchedSegment(...args);
	}

	static getSegmentTypes(): any {
		const segmentTypes = [];
		for (const key in segments) {
			segmentTypes.push({
				name: segments[key].FRIENDLY_NAME,
				value: key
			});
		}
		return segmentTypes;
	}

	static getSegmentTypeFromInstruction(instruction: string) {
		for (const key in segments) {
			if (segments[key].INSTRUCTION_NAME === instruction) {
				return {
					name: segments[key].FRIENDLY_NAME,
					value: key
				};
			}
		}
	}
}
