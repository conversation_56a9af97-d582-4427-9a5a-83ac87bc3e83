export abstract class ASegment {

	id: string;
	instruction: string;
	subject: Subject;
	time: TimePredicate;
	requiredParams: ParamPredicate = {} as ParamPredicate;
	supportedSubjectTypes: SubjectType[] = [];

	constructor(id: string, instruction: string, supportedSubjectTypes: SubjectType[]) {
		this.id = id;
		this.instruction = instruction;
		this.supportedSubjectTypes = supportedSubjectTypes;
	}

	abstract requiresParameters(): boolean;
	abstract requiresTime(): boolean;

	setSubject(subject: any): ASegment {
		this.subject = subject;
		return this;
	}

	setTime(time: TimePredicate): ASegment {
		this.time = time;
		return this;
	};

	addRequiredParam(param: ParamPredicate): ASegment {
		this.requiredParams = param;
		return this;
	}

	getPossibleSubjects(): SubjectType[] {
		return this.supportedSubjectTypes;
	};

	supportsTime(): boolean {
		return true;
	}

	isSelectableEventType(): boolean {
		return true;
	}

	hasEventValue(): boolean {
		return false;
	}

	hasUTMCampaignValue(): boolean {
		return false;
	}

	hasCustomTraitValue(): boolean {
		return false;
	}

	buildSegment(): SegmentDefinition {
		if (this.requiresParameters() && !this.requiredParams) {
			throw new Error('Be sure to call addRequiredParam() before calling buildSegment()');
		}

		const segmentDefinition: SegmentDefinition = {
			id: this.id,
			instruction: this.instruction,
			subject: this.subject || {},
			time: {} as TimePredicate,
			required: {} as ParamPredicate
		};

		if (this.time) {
			segmentDefinition.time = this.time;
		}

		if (this.requiredParams) {
			segmentDefinition.required = this.requiredParams;
		}

		return segmentDefinition;
	};
}

export interface SegmentDefinition {
	id: string;
	instruction: string;
	subject: Subject; //previously projectId
	time?: TimePredicate;
	required?: ParamPredicate;
}

export interface TimePredicate {
	start: number;
	end: number;
}

export interface ParamPredicate {
	operator: string;
	value?: string;
	valueString?: string;
}

export interface Subject {
	type: string;
	value: string;
}

export class SubjectType {
	static PROJECT = {
		name: 'Raleon Project',
		value: 'PROJECT'
	};
	static DAPP = {
		name: 'dApp',
		value: 'DAPP'
	};
	static CATEGORY = {
		name: 'Category',
		value: 'CATEGORY'
	};
	static TOKEN = {
		name: 'Token Address',
		value: 'TOKEN'
	};
	static NFT = {
		name: 'NFT Address',
		value: 'NFT'
	};
	static ADDRESS = {
		name: 'Address',
		value: 'ADDRESS'
	};
	static EVENT = {
		name: 'Event',
		value: 'EVENT'
	};
	static UTM_CAMPAIGN = {
		name: 'UTM Campaign',
		value: 'UTM_CAMPAIGN'
	};
	static WALLET_TAG = {
		name: 'Wallet Trait',
		value: 'WALLET_TAG'
	}
}


