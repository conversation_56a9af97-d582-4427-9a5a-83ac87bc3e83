import {ASegment, SubjectType} from '../segment.abstract';

export class FilterHasMintedAnyNFT extends ASegment {
	constructor(id: string) {
		super(
			id,
			FilterHasMintedAnyNFT.INSTRUCTION_NAME,
			[]
		);
	}

	requiresParameters(): boolean {
		return false;
	}

	requiresTime(): boolean {
		return true;
	}

	supportsTime(): boolean {
		return true;
	}

  static INSTRUCTION_NAME = 'filter_users_with_minted_nft';
  static FRIENDLY_NAME = 'Minted Any NFT';
}
