import {ASegment, SubjectType} from '../segment.abstract';

export class FirstTransactionSegment extends ASegment {

	constructor(id: string) {
		super(
			id,
			FirstTransactionSegment.INSTRUCTION_NAME,
			[SubjectType.PROJECT, SubjectType.CATEGORY, SubjectType.ADDRESS, SubjectType.DAPP]
		);
	}

	requiresParameters(): boolean {
		return false;
	}

	requiresTime(): boolean {
		return true;
	}

	static INSTRUCTION_NAME = 'filter_users_with_first_interaction';
	static FRIENDLY_NAME = 'First Interaction';
}
