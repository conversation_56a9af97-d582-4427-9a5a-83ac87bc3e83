import {ASegment, SubjectType} from '../segment.abstract';

export class FilterHasBoughtNftForUsd extends ASegment {
	constructor(id: string) {
		super(
			id,
			FilterHasBoughtNftForUsd.INSTRUCTION_NAME,
			[]
		);
	}

	requiresParameters(): boolean {
		return true;
	}

	requiresTime(): boolean {
		return true;
	}

	supportsTime(): boolean {
		return true;
	}

  static INSTRUCTION_NAME = 'filter_users_with_nft_purchased_for_usd_value';
  static FRIENDLY_NAME = 'Purchased an NFT (USD)';
}
