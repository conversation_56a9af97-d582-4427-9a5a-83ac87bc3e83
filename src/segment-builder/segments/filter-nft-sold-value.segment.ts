import {ASegment, SubjectType} from '../segment.abstract';

export class FilterNftSoldValue extends ASegment {
	constructor(id: string) {
		super(
			id,
			FilterNftSoldValue.INSTRUCTION_NAME,
			[]
		);
	}

	requiresParameters(): boolean {
		return true;
	}

	requiresTime(): boolean {
		return true;
	}

	supportsTime(): boolean {
		return true;
	}

  static INSTRUCTION_NAME = 'filter_nft_sold_usd_value';
  static FRIENDLY_NAME = 'Sold an NFT For Price (USD)';
}
