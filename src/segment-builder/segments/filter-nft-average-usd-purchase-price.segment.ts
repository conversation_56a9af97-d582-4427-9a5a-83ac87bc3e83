import {ASegment, SubjectType} from '../segment.abstract';

export class FilterNFTAverageUsdPurchasePrice extends ASegment {
	constructor(id: string) {
		super(
			id,
			FilterNFTAverageUsdPurchasePrice.INSTRUCTION_NAME,
			[]
		);
	}

	requiresParameters(): boolean {
		return true;
	}

	requiresTime(): boolean {
		return true;
	}

	supportsTime(): boolean {
		return true;
	}

  static INSTRUCTION_NAME = 'filter_users_with_nft_avg_usd_value';
  static FRIENDLY_NAME = 'Average NFT Purchase Price (USD)';
}
