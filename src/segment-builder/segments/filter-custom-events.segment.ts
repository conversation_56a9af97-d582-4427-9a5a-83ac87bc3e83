import {ASegment, SegmentDefinition, SubjectType} from '../segment.abstract';

export class FilterCustomEvents extends ASegment {

	constructor(id: string, private orgId: string) {
		super(
			id,
			FilterCustomEvents.INSTRUCTION_NAME,
			[]
		);

		this.orgId = orgId;

		this.addRequiredParam({
			operator: '=',
			valueString: `${this.orgId}`
		});
	}

	requiresParameters(): boolean {
		return false;
	}

	requiresTime(): boolean {
		return false;
	}

	hasEventValue(): boolean {
		return true;
	}




	static INSTRUCTION_NAME = 'filter_custom_events';
	static FRIENDLY_NAME = 'Off-Chain Event';
}
