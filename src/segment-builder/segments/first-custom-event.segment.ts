import {ASegment, SubjectType} from '../segment.abstract';

export class FirstCustomEventSegment extends ASegment {

	constructor(id: string, private orgId: string) {
		super(
			id,
			FirstCustomEventSegment.INSTRUCTION_NAME,
			[]
		);

		this.orgId = orgId;

		this.addRequiredParam({
			operator: '=',
			valueString: `${this.orgId}`
		});
	}

	requiresParameters(): boolean {
		return false;
	}

	requiresTime(): boolean {
		return true;
	}

	hasEventValue(): boolean {
		return true;
	}


	static INSTRUCTION_NAME = 'filter_users_with_first_custom_event_interaction';
	static FRIENDLY_NAME = 'First Custom Event';
}
