import {ASegment, SubjectType} from '../segment.abstract';

export class FilterMintedForMatic extends ASegment {
	constructor(id: string) {
		super(
			id,
			FilterMintedForMatic.INSTRUCTION_NAME,
			[]
		);
	}

	requiresParameters(): boolean {
		return true;
	}

	requiresTime(): boolean {
		return true;
	}

	supportsTime(): boolean {
		return true;
	}

  static INSTRUCTION_NAME = 'filter_users_with_minted_nft_polygon';
  static FRIENDLY_NAME = 'Minted a NFT For Matic';
}
