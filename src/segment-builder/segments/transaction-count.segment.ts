import {ASegment, SubjectType} from '../segment.abstract';

export class TransactionCountSegment extends ASegment {
	constructor(id: string) {
		super(
			id,
			TransactionCountSegment.INSTRUCTION_NAME,
			[SubjectType.PROJECT, SubjectType.CATEGORY, SubjectType.ADDRESS, SubjectType.DAPP]
		);
	}

	requiresParameters(): boolean {
		return true;
	}

	requiresTime(): boolean {
		return true;
	}

  static INSTRUCTION_NAME = 'filter_users_with_transaction_count';
  static FRIENDLY_NAME = 'Transaction Count';
}
