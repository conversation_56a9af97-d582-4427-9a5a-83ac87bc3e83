import {ASegment, SubjectType} from '../segment.abstract';

/**
 * Deprecated - isSelectableEventType: false instead of removing.
 */
export class LastActivitySegment extends ASegment {

	constructor(id: string) {
		super(
			id,
			LastActivitySegment.INSTRUCTION_NAME,
			[SubjectType.PROJECT, SubjectType.CATEGORY, SubjectType.ADDRESS, SubjectType.DAPP]
		);
	}

	requiresParameters(): boolean {
		return true;
	}

	requiresTime(): boolean {
		return true;
	}

	isSelectableEventType(): boolean {
		return false;
	}

	static INSTRUCTION_NAME = 'filter_users_with_last_activity';
	static FRIENDLY_NAME = 'Last Activity';
}
