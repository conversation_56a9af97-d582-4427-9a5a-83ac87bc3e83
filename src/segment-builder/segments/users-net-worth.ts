import {ASegment} from '../segment.abstract';

export class UsersNetWorth extends ASegment {
	constructor(id: string) {
		super(
			id,
			UsersNetWorth.INSTRUCTION_NAME,
			[]
		);
	}

	requiresParameters(): boolean {
		return true;
	}

	requiresTime(): boolean {
		return false;
	}

	supportsTime(): boolean {
		return false;
	}

	static INSTRUCTION_NAME = 'filter_users_net_worth';
	static FRIENDLY_NAME = 'USD Net Worth (USD)';
}
