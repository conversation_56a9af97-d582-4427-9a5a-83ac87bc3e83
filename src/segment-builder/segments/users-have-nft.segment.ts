import {ASegment, SubjectType} from '../segment.abstract';

export class UsersHaveNFTSegment extends ASegment {

	constructor(id: string) {
		super(
			id,
			UsersHaveNFTSegment.INSTRUCTION_NAME,
			[SubjectType.NFT]
		);
	}

	requiresParameters(): boolean {
		return true;
	}

	requiresTime(): boolean {
		return false;
	}

	supportsTime(): boolean {
		return false;
	}

	static INSTRUCTION_NAME = 'filter_users_have_nft';
	static FRIENDLY_NAME = 'NFT Count';
}
