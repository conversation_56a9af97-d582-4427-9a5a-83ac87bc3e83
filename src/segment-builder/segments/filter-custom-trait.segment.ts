import {ASegment, SegmentDefinition, SubjectType} from '../segment.abstract';

export class FilterCustomTrait extends ASegment {

	constructor(id: string, private orgId: string) {
		super(
			id,
			FilterCustomTrait.INSTRUCTION_NAME,
			[]
		);

		this.orgId = orgId;

		this.addRequiredParam({
			operator: '=',
			valueString: `${this.orgId}`
		});
	}

	requiresParameters(): boolean {
		return false;
	}

	requiresTime(): boolean {
		return false;
	}

   supportsTime(): boolean {
      return false;
   }

   hasCustomTraitValue(): boolean {
      return true;
   }

	static INSTRUCTION_NAME = 'filter_has_tag';
	static FRIENDLY_NAME = 'Traits Include';
}
