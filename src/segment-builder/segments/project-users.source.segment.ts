import {ASegment, SubjectType} from '../segment.abstract';

export class ProjectUsersSegment extends ASegment {

	constructor(id: string) {
		super(id, ProjectUsersSegment.INSTRUCTION_NAME, [SubjectType.PROJECT]);
	}

	getPossibleSubjects(): any[] {
		return [SubjectType.PROJECT];
	}

	requiresParameters(): boolean {
		return false;
	}

	requiresTime(): boolean {
		return false;
	}

	isSelectableEventType(): boolean {
		return false;
	}

	static INSTRUCTION_NAME = 'source_project_users';
	static FRIENDLY_NAME = 'Project Users';
}
