import {ASegment, SubjectType} from '../segment.abstract';

export class UsersHaveTokenSegment extends ASegment {

	constructor(id: string) {
		super(
			id,
			UsersHaveTokenSegment.INSTRUCTION_NAME,
			[SubjectType.TOKEN]
		);
	}

	requiresParameters(): boolean {
		return true;
	}

	requiresTime(): boolean {
		return false;
	}

	supportsTime(): boolean {
		return false;
	}

	static INSTRUCTION_NAME = 'filter_users_have_token';
	static FRIENDLY_NAME = 'Token Count';
}
