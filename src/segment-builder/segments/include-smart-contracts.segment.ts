import {ASegment, SubjectType} from '../segment.abstract';

export class IncludeSmartContractsSegment extends ASegment {

	constructor(id: string) {
		super(
			id,
			IncludeSmartContractsSegment.INSTRUCTION_NAME,
			[]
		);
	}

	requiresParameters(): boolean {
		return true;
	}

	requiresTime(): boolean {
		return false;
	}

	supportsTime(): boolean {
		return false;
	}

	isSelectableEventType(): boolean {
		return false;
	}

	static INSTRUCTION_NAME = 'filter_users_smart_contract';
	static FRIENDLY_NAME = 'Smart Contracts Included';
}
