import {ASegment, SubjectType} from '../segment.abstract';

export class FilterHasMintedNftForUsd extends ASegment {
	constructor(id: string) {
		super(
			id,
			FilterHasMintedNftForUsd.INSTRUCTION_NAME,
			[]
		);
	}

	requiresParameters(): boolean {
		return true;
	}

	requiresTime(): boolean {
		return true;
	}

	supportsTime(): boolean {
		return true;
	}

  static INSTRUCTION_NAME = 'filter_nft_mint_price_usd';
  static FRIENDLY_NAME = 'Minted an NFT Price (USD)';
}
