import {ASegment, SubjectType} from '../segment.abstract';

export class FilterAddressesThatSentTo extends ASegment {
	constructor(id: string) {
		super(
			id,
			FilterAddressesThatSentTo.INSTRUCTION_NAME,
			[]
		);
	}

	requiresParameters(): boolean {
		return false;
	}

	requiresTime(): boolean {
		return false;
	}

	supportsTime(): boolean {
		return false;
	}

  static INSTRUCTION_NAME = 'filter_address_sent_to';
  static FRIENDLY_NAME = 'Addresses That Funded These Wallets';
}
