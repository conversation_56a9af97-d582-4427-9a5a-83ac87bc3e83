import {ASegment, SubjectType} from '../segment.abstract';

export class FilterAddressesThatReceivedFrom extends ASegment {
	constructor(id: string) {
		super(
			id,
			FilterAddressesThatReceivedFrom.INSTRUCTION_NAME,
			[]
		);
	}

	requiresParameters(): boolean {
		return false;
	}

	requiresTime(): boolean {
		return false;
	}

	supportsTime(): boolean {
		return false;
	}

  static INSTRUCTION_NAME = 'filter_address_received_from';
  static FRIENDLY_NAME = 'Addresses Funded By These Wallets';
}
