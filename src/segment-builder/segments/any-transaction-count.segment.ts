import {ASegment, SubjectType} from '../segment.abstract';

export class AnyTransactionCountSegment extends ASegment {
	constructor(id: string) {
		super(
			id,
			AnyTransactionCountSegment.INSTRUCTION_NAME,
			[]
		);
	}

	requiresParameters(): boolean {
		return true;
	}

	requiresTime(): boolean {
		return true;
	}

	supportsTime(): boolean {
		return true;
	}

  static INSTRUCTION_NAME = 'filter_users_with_any_transaction';
  static FRIENDLY_NAME = 'Any Transaction Count';
}
