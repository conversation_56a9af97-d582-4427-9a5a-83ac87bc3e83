import {ASegment, SubjectType} from '../segment.abstract';

export class FilterHasMintedSpecificNft extends ASegment {

	constructor(id: string) {
		super(
			id,
			FilterHasMintedSpecificNft.INSTRUCTION_NAME,
			[SubjectType.NFT]
		);
	}

	requiresParameters(): boolean {
		return true;
	}

	requiresTime(): boolean {
		return false;
	}

	supportsTime(): boolean {
		return false;
	}

	static INSTRUCTION_NAME = 'filter_nft_mint_specific';
	static FRIENDLY_NAME = 'Minted Specific NFT Count';
}
