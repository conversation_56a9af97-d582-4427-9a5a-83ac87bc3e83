import {ASegment, SubjectType} from '../segment.abstract';

export class FilterMintedForEth extends ASegment {
	constructor(id: string) {
		super(
			id,
			FilterMintedForEth.INSTRUCTION_NAME,
			[]
		);
	}

	requiresParameters(): boolean {
		return true;
	}

	requiresTime(): boolean {
		return true;
	}

	supportsTime(): boolean {
		return true;
	}

  static INSTRUCTION_NAME = 'filter_users_with_minted_nft_eth';
  static FRIENDLY_NAME = 'Minted a NFT For ETH';
}
