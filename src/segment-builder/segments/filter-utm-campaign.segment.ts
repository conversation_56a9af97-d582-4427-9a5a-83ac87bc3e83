import {ASegment, SegmentDefinition, SubjectType} from '../segment.abstract';

export class FilterUtmCampaign extends ASegment {

	constructor(id: string, private orgId: string) {
		super(
			id,
			FilterUtmCampaign.INSTRUCTION_NAME,
			[]
		);

		this.orgId = orgId;

		this.addRequiredParam({
			operator: '=',
			valueString: `${this.orgId}`
		});
	}

	requiresParameters(): boolean {
		return false;
	}

	requiresTime(): boolean {
		return false;
	}

	hasUTMCampaignValue(): boolean {
		return true;
	}




	static INSTRUCTION_NAME = 'filter_has_utm_campaign';
	static FRIENDLY_NAME = 'Attribution Campaign';
}
