// @ts-ignore
import toobusy from 'toobusy-js';

process.on('uncaughtException', err => {
  console.error('[Uncaught Exception]', err);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('[Unhandled Rejection]', reason);
});

setInterval(() => {
  const lag = toobusy.lag();
  const memUsage = process.memoryUsage();
  const heapUsedMB = Math.round(memUsage.heapUsed / 1024 / 1024);

  if (lag > 100) {
    console.warn(`[Event Loop Lag] ${lag.toFixed(2)}ms, Heap: ${heapUsedMB}MB — server may be too busy`);
  }

  // Log memory usage every 30s for monitoring
  if (heapUsedMB > 1024) {
    console.warn(`[Memory Warning] Heap usage: ${heapUsedMB}MB`);
  }
}, 30000);

// // Add GC monitoring
// if (global.gc) {
//   let gcCount = 0;
//   setInterval(() => {
//     const beforeGC = process.memoryUsage();
// 	console.log('Forcing GC');
//     global.gc!(); // Use non-null assertion since we checked above
//     const afterGC = process.memoryUsage();
//     const freedMB = Math.round((beforeGC.heapUsed - afterGC.heapUsed) / 1024 / 1024);
//     gcCount++;

//     if (freedMB > 50) {
//       console.log(`[GC #${gcCount}] Freed ${freedMB}MB, Heap: ${Math.round(afterGC.heapUsed / 1024 / 1024)}MB`);
//     }
//   }, 60000); // Force GC every minute
// }

process.on('SIGINT', () => {
  toobusy.shutdown();
  process.exit();
});

//import express from 'express';
import {ApplicationConfig, RaleonWebappApplication} from './application';
import {CleanupService} from './services/cleanup.service';
export * from './application';

export async function main(options: ApplicationConfig = {}) {
  //const mainApp = express();
  const app = new RaleonWebappApplication(options);
  await app.boot();
  await app.start();

  // Start the cleanup service
  try {
    const cleanupService = await app.get('services.CleanupService') as CleanupService;
    cleanupService.start();
  } catch (error) {
    console.error('Failed to start cleanup service:', error);
  }

  const url = app.restServer.url;
  console.log(`Server is running at ${url}`);
  console.log(`Try ${url}/ping`);
  return app;
}

if (require.main === module) {
  // Run the application
  const config = {
    rest: {
      port: +(process.env.PORT ?? 3000),
      host: process.env.HOST,
      gracePeriodForClose: 5000,
      openApiSpec: {
        setServersFromRequest: true,
      },
      cors: {
        origin: '*',
        methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
        preflightContinue: false,
        optionsSuccessStatus: 204,
        credentials: true,
      },
    },
  };
  main(config).catch(err => {
    console.error('Cannot start the application.', err);
    process.exit(1);
  });
}
