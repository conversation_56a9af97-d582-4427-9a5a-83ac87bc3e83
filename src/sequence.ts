import {AuthenticateFn, AuthenticationBindings} from '@loopback/authentication';
import {inject} from '@loopback/context';
import {
	FindRoute,
	InvokeMethod,
	InvokeMiddleware,
	ParseParams,
	Reject,
	RequestContext,
	RestBindings,
	Send,
	SequenceHandler,
} from '@loopback/rest';
import path from 'path';

const SequenceActions = RestBindings.SequenceActions;

export class MySequence implements SequenceHandler {
	@inject(SequenceActions.INVOKE_MIDDLEWARE, {optional: true})
	protected invokeMiddleware: InvokeMiddleware = () => false;

	constructor(
		@inject(SequenceActions.FIND_ROUTE) protected findRoute: FindRoute,
		@inject(SequenceActions.PARSE_PARAMS) protected parseParams: ParseParams,
		@inject(SequenceActions.INVOKE_METHOD) protected invoke: InvokeMethod,
		@inject(SequenceActions.SEND) public send: Send,
		@inject(SequenceActions.REJECT) public reject: Reject,
		@inject(AuthenticationBindings.AUTH_ACTION)
		protected authenticateRequest: AuthenticateFn,
	) { }

	async handle(context: RequestContext) {
		const start = process.hrtime();
		const {request, response} = context;
		let responseSent = false;

		try {
			const finished = await this.invokeMiddleware(context);
			if (finished) return;
			response.header('Access-Control-Allow-Origin', '*');
			response.header(
				'Access-Control-Allow-Headers',
				'Origin, X-Requested-With, Content-Type, Accept',
			);
			if (request.method == 'OPTIONS') {
				response.status(200);
				this.send(response, 'ok');
				responseSent = true;
				return;
			}
			const route = this.findRoute(request);

			await this.authenticateRequest(request);
			const args = await this.parseParams(request, route);
			const result = await this.invoke(route, args);
			this.send(response, result);
			responseSent = true;
		} catch (err) {
			if (!responseSent && context.request.originalUrl.startsWith('/api', 0)) {
				throw err;
			} else if (!responseSent) {
				const {method, originalUrl} = context.request;
				console.warn(`[SPA Fallback] Serving index.html for: ${method} ${originalUrl} - ${err.statusCode || 404}`);
				if (err.statusCode !== 404) {
					throw err;
				}

				try {
					context.response.sendFile('public/dist/index.html', { root: path.join(__dirname, '../') });
					responseSent = true;
					console.log(`Sent file: ${path.join(__dirname, '../public/dist/index.html')}`);
				} catch (rejectErr) {
					console.error(`Failed to send file: ${rejectErr.message}`)
				}
			} else {
				console.error(`Error: ${err.message}`);
			}
		}
	}
}
