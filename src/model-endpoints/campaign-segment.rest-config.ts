import {ModelCrudRestApiConfig} from '@loopback/rest-crud';
import {OrgGuardSingleHopPropertyStrategy} from '../interceptors';
import {Campaign, CampaignSegment} from '../models';
import {CampaignRepository, CampaignSegmentRepository} from '../repositories';

const config: ModelCrudRestApiConfig = {
  model: CampaignSegment,
  pattern: 'CrudRest',
  dataSource: 'dev_db',
  basePath: '/quest-segments',
  repository: CampaignSegmentRepository,
  unauthenticatedMethods: {},
  guardStrategy: new OrgGuardSingleHopPropertyStrategy<CampaignSegment, Campaign>({
	relatedIdPropertyName: 'campaignId',
	relatedOrgIdPropertyName: 'orgId',
	repositoryClass: CampaignSegmentRepository,
	relatedRepositoryClass: CampaignRepository
  }),
  restrictMutationsWithGuard: true,
  restrictReadsWithGuard: true,
};
module.exports = config;
