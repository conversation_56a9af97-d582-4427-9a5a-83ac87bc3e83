import {ModelCrudRestApiConfig} from '@loopback/rest-crud';
import {OrgGuardMultiHopPropertyStrategy, OrgGuardSingleHopPropertyStrategy} from '../interceptors/crud-guard.interceptor';
import {Dashboard, Widget, WidgetRelations} from '../models';
import {DashboardRepository, WidgetRepository} from '../repositories';

const config: ModelCrudRestApiConfig = {
  model: Widget,
  pattern: 'CrudRest',
  dataSource: 'dev_db',
  basePath: '/widgets',
  repository: WidgetRepository,
  unauthenticatedMethods: {},
  skipOrgIdFilterForMethods: {},
  guardStrategy: new OrgGuardSingleHopPropertyStrategy<Widget, Dashboard>({
	relatedIdPropertyName: 'dashboardId',
	relatedOrgIdPropertyName: 'organizationId',
	repositoryClass: WidgetRepository,
	relatedRepositoryClass: DashboardRepository
  }),
  restrictMutationsWithGuard: true,
  restrictReadsWithGuard: true,
};
module.exports = config;
