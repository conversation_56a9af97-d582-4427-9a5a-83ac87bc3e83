import {ModelCrudRestApiConfig} from '@loopback/rest-crud';
import {OrgGuardPropertyStrategy} from '../interceptors/crud-guard.interceptor';
import {Dashboard} from '../models';
import {DashboardRepository} from '../repositories';

const config: ModelCrudRestApiConfig = {
  model: Dashboard,
  pattern: 'CrudRest',
  dataSource: 'dev_db',
  basePath: '/dashboards',
  repository: DashboardRepository,
  unauthenticatedMethods: {},
  guardStrategy: new OrgGuardPropertyStrategy<Dashboard>({
	orgIdModelPropertyName: 'organizationId',
	repositoryClass: DashboardRepository
  }),
  restrictMutationsWithGuard: true,
  restrictReadsWithGuard: true,
};
module.exports = config;
