import {ModelCrudRestApiConfig} from '@loopback/rest-crud';
import {OrgGuardPropertyStrategy} from '../interceptors/crud-guard.interceptor';
import {Segment} from '../models';
import {SegmentRepository} from '../repositories';

const config: ModelCrudRestApiConfig = {
  model: Segment,
  pattern: 'CrudRest',
  dataSource: 'dev_db',
  basePath: '/segments',
  repository: SegmentRepository,
  unauthenticatedMethods: {},
  guardStrategy: new OrgGuardPropertyStrategy<Segment>({
	orgIdModelPropertyName: 'orgid',
	repositoryClass: SegmentRepository
  }),
  restrictMutationsWithGuard: true,
  restrictReadsWithGuard: true,
};
module.exports = config;
