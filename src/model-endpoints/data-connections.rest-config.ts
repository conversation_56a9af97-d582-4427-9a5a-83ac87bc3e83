import {ModelCrudRestApiConfig} from '@loopback/rest-crud';
import {GuardSkipStrategy, OrgGuardSingleHopPropertyStrategy} from '../interceptors';
import {DataConnections, Project} from '../models';
import {DataConnectionsRepository, ProjectRepository} from '../repositories';

const config: ModelCrudRestApiConfig = {
  model: DataConnections,
  pattern: 'CrudRest',
  dataSource: 'dev_db',
  basePath: '/data-connections',
  repository: DataConnectionsRepository,
  unauthenticatedMethods: {},
  guardStrategy: new OrgGuardSingleHopPropertyStrategy<DataConnections, Project>({
	relatedIdPropertyName: 'projectId',
	relatedOrgIdPropertyName: 'organizationId',
	repositoryClass: DataConnectionsRepository,
	relatedRepositoryClass: ProjectRepository
  }),
  restrictMutationsWithGuard: true,
  restrictReadsWithGuard: true,
};
module.exports = config;
