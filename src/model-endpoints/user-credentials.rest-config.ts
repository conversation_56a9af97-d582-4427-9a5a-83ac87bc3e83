import {UserCredentialsRelations} from '@loopback/authentication-jwt';
import {ModelCrudRestApiConfig} from '@loopback/rest-crud';
import {OrgGuardMultiHopPropertyStrategy, OrgGuardSingleHopPropertyStrategy, UserGuardPropertyStrategy} from '../interceptors/crud-guard.interceptor';
import {User, UserCredentials, UserCredentialsWithRelations} from '../models';
import {UserRepository} from '../repositories';
import {UserCredentialsRepository} from '../repositories/user-credentials.repository';

const config: ModelCrudRestApiConfig = {
  model: UserCredentials,
  pattern: 'CrudRest',
  dataSource: 'dev_db',
  basePath: '/user-credentials',
  repository: UserCredentialsRepository,
  unauthenticatedMethods: {},
  guardStrategy: new UserGuardPropertyStrategy<UserCredentials>({
	repositoryClass: UserCredentialsRepository,
	userIdModelPropertyName: 'userId'
  }),
  restrictMutationsWithGuard: true,
  restrictReadsWithGuard: true,
};
module.exports = config;
