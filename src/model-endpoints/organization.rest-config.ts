import {ModelCrudRestApiConfig} from '@loopback/rest-crud';
import {OrgGuardPropertyStrategy} from '../interceptors/crud-guard.interceptor';
import {Organization} from '../models';
import {OrganizationRepository} from '../repositories';

const config: ModelCrudRestApiConfig = {
  model: Organization,
  pattern: 'CrudRest',
  dataSource: 'dev_db',
  basePath: '/organizations',
  repository: OrganizationRepository,
  unauthenticatedMethods: {},
  guardStrategy: new OrgGuardPropertyStrategy<Organization>({
	orgIdModelPropertyName: 'id',
	repositoryClass: OrganizationRepository
  }),
  restrictMutationsWithGuard: true,
  restrictReadsWithGuard: true,
};
module.exports = config;
