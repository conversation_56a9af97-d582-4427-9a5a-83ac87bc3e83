import {ModelCrudRestApiConfig} from '@loopback/rest-crud';
import {OrgGuardPropertyStrategy} from '../interceptors/crud-guard.interceptor';
import {Project} from '../models';
import {ProjectRepository} from '../repositories';

const config: ModelCrudRestApiConfig = {
  model: Project,
  pattern: 'CrudRest',
  dataSource: 'dev_db',
  basePath: '/projects',
  repository: ProjectRepository,
  unauthenticatedMethods: {},
  skipOrgIdFilterForRequests: [],
  guardStrategy: new OrgGuardPropertyStrategy<Project>({
	orgIdModelPropertyName: 'organizationId',
	repositoryClass: ProjectRepository
  }),
  restrictMutationsWithGuard: true,
  restrictReadsWithGuard: true,
};
module.exports = config;
