import {ModelCrudRestApiConfig} from '@loopback/rest-crud';
import {OrgGuardMultiHopPropertyStrategy, OrgGuardSingleHopPropertyStrategy} from '../interceptors/crud-guard.interceptor';
import {Campaign, Quest, QuestRelations, QuestWithRelations} from '../models';
import {CampaignRepository, QuestRepository} from '../repositories';

const config: ModelCrudRestApiConfig = {
  model: Quest,
  pattern: 'CrudRest',
  dataSource: 'dev_db',
  basePath: '/quests',
  repository: QuestRepository,
  unauthenticatedMethods: {},
  guardStrategy: new OrgGuardSingleHopPropertyStrategy<Quest, Campaign>({
	relatedIdPropertyName: 'campaignId',
	relatedOrgIdPropertyName: 'orgId',
	repositoryClass: QuestRepository,
	relatedRepositoryClass: CampaignRepository
  }),
  restrictMutationsWithGuard: true,
  restrictReadsWithGuard: true,
};
module.exports = config;
