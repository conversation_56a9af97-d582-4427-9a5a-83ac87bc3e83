import {injectable, inject, BindingScope} from '@loopback/core';
import {OpenAiService} from '../open-ai.service';
import {CompletionMessage, RouterParams, ToolCall, MessageContent} from './types';
import {BaseRouterTarget} from './base-router-target';

@injectable({scope: BindingScope.TRANSIENT})
export class OpenAiTarget extends BaseRouterTarget {
	id = 'openai';
	systemId = 'OpenAI';
	supportedModels = [
		'openai/*',
		'openai/gpt-4',
		'openai/gpt-4-vision-preview',
		'openai/gpt-4-1106-preview'
	];
	supportedProviders = ['OpenAI'];

	constructor(
		@inject('services.OpenAiService')
		private openAiService: OpenAiService,
	) {
		super();
	}

	protected supportsStreaming(): boolean {
		return false;
	}

	protected async executeCompletion(
		messages: CompletionMessage[],
		params: RouterParams
	): Promise<{content: string; provider: string; model: string; additional?: Record<string, unknown>; toolCalls?: ToolCall[], promptTokens?: number; totalTokens?: number, completionTokens?: number}> {
		// Handle image generation if requested
		if (params.imageGeneration) {
			const model = this.getSelectedModel(params);
			if (model !== 'gpt-image-1') {
				throw new Error(`Model ${model} does not support image generation`);
			}

			const result = await this.openAiService.generateImage(
				params.imageGeneration.prompt,
				{
					model,
					quality: params.imageGeneration.quality,
					style: params.imageGeneration.style,
					size: params.imageGeneration.size,
					n: params.imageGeneration.n
				}
			);

			return {
				content: 'Image generation complete',
				provider: 'OpenAI',
				model,
				promptTokens: result.usage.prompt_tokens,
				completionTokens: result.usage.output_tokens,
				totalTokens: result.usage.total_tokens,
				additional: {
					generatedImages: result.data,
					promptTokens: result.usage.prompt_tokens,
					completionTokens: result.usage.output_tokens,
					totalTokens: result.usage.total_tokens
				}
			};
		}

		// Handle image editing if requested
		if (params.imageEditing) {
			const model = this.getSelectedModel(params);
			if (model !== 'gpt-image-1') {
				throw new Error(`Model ${model} does not support image editing`);
			}

			const result = await this.openAiService.editImage(
				params.imageEditing.imageDataUrls,
				params.imageEditing.maskDataUrl,
				params.imageEditing.prompt,
				{
					model,
					size: params.imageEditing.size,
					n: params.imageEditing.n
				}
			);

			return {
				content: 'Image editing complete',
				provider: 'OpenAI',
				model,
				promptTokens: result.usage.prompt_tokens,
				completionTokens: result.usage.output_tokens,
				totalTokens: result.usage.total_tokens,
				additional: {
					editedImages: result.data,
					promptTokens: result.usage.prompt_tokens,
					completionTokens: result.usage.output_tokens,
					totalTokens: result.usage.total_tokens
				}
			};
		}

		return this.makeRequest(messages, params);
	}

	canHandle(params: RouterParams): boolean {
		// If image generation/editing is requested, only OpenAI can handle it currently
		if (params.imageGeneration || params.imageEditing) {
		  // Check if the model is compatible with the requested operation
		  const model = this.getSelectedModel(params);
		  if (params.imageGeneration && !['gpt-image-1'].includes(model)) {
			return false;
		  }
		  if (params.imageEditing) {
			return true; // We'll force DALL-E-2 for editing
		  }
		  return true;
		}

		return super.canHandle(params);
	}

	private getSelectedModel(params: RouterParams): string {
		const defaultModel = 'gpt-4';
		const visionModel = 'gpt-4-vision-preview';

		// Check if any messages contain image content
		const hasImageContent = params.messages?.some(msg =>
			Array.isArray(msg.content) &&
			msg.content.some((c: MessageContent) => c.type === 'image_url')
		);

		// If we have image content, always use vision model
		if (hasImageContent) {
			return visionModel;
		}

		if (!params.models?.length) {
			return defaultModel;
		}

		// If * is provided, use default
		if (params.models.includes('*')) {
			return defaultModel;
		}

		// Filter to OpenAI models only
		const openaiModels = params.models.filter(m =>
			m.startsWith('openai/') ||
			this.supportedModels.some(pattern => this.matchesWildcard(pattern, m))
		);

		if (!openaiModels.length) {
			return defaultModel;
		}

		// If openai/* is provided, use default
		if (openaiModels.includes('openai/*')) {
			return defaultModel;
		}

		// Use first matching model, removing openai/ prefix
		return openaiModels[0].replace('openai/', '');
	}

	private async makeRequest(
		messages: CompletionMessage[],
		params: RouterParams
	): Promise<{content: string; provider: string; model: string; additional?: Record<string, unknown>; toolCalls?: ToolCall[]}> {
		// Get the appropriate model - for vision support, ensure we use compatible models
		const model = this.getSelectedModel(params);

		// Convert tool definitions to OpenAI format if provided
		const tools = params.tools?.map(tool => ({
			type: tool.type,
			function: {
				name: tool.function.name,
				description: tool.function.description,
				parameters: tool.function.parameters
			}
		}));

		// Check if any messages contain image content
		const hasImageContent = messages.some(msg =>
			Array.isArray(msg.content) &&
			msg.content.some(c => c.type === 'image_url')
		);

		// If we have image content, ensure we're using a vision-capable model
		const effectiveModel = hasImageContent ? 'gpt-4-vision-preview' : model;

		// Call OpenAI service with properly formatted messages
		const response = await this.openAiService.getCompletion(
			messages,
			params.maxTokens,
			effectiveModel,
			false, // Don't force JSON response format for vision queries
			tools
		);

		// For OpenAI, we can provide token counts and temperature from the response
		const additional: Record<string, unknown> = {};
		const toolCalls: ToolCall[] = [];

		if (typeof response === 'object' && response !== null) {
			const {content, usage, model_version, tool_calls} = response as any;
			if (usage) {
				additional.promptTokens = usage.prompt_tokens;
				additional.completionTokens = usage.completion_tokens;
				additional.totalTokens = usage.total_tokens;
			}
			additional.modelVersion = model_version;

			// Handle tool calls if present
			if (tool_calls?.length) {
				toolCalls.push(...tool_calls.map((tc: any) => ({
					type: tc.type as 'function',
					id: tc.id,
					function: {
						name: tc.function.name,
						arguments: tc.function.arguments
					}
				})));
			}

			return {
				content: content || response,
				provider: 'OpenAI',
				model: model,
				additional,
				toolCalls: toolCalls.length > 0 ? toolCalls : undefined
			};
		}

		return {
			content: response as string,
			provider: 'OpenAI',
			model: model,
			additional
		};
	}

	protected async executeToolFunction(name: string, args: any): Promise<any> {
		// Tool execution is handled by BaseRouterTarget using the tools from params
		throw new Error('OpenAI does not execute tools directly');
	}
}
