import {injectable, service, BindingScope} from '@loopback/core';
import {CompletionMessage, RouterParams, ToolCall, MessageContent} from './types';
import {BaseRouterTarget} from './base-router-target';
import {ClaudeService} from '../../services/claude.service';
import {convertMessageContentForAnthropic} from './utils/image-format.utils';

@injectable({scope: BindingScope.TRANSIENT})
export class AnthropicTarget extends BaseRouterTarget {
	id = 'anthropic';
	systemId = 'Anthropic';
	supportedModels = [
		'anthropic/*',
		'anthropic/claude-3-opus',
		'anthropic/claude-3-sonnet',
		'anthropic/claude-3.5-sonnet'
	];
	supportedProviders = ['Anthropic'];

	constructor(
		@service(ClaudeService)
		private claudeService: ClaudeService,
	) {
		super();
	}

	protected supportsStreaming(): boolean {
		return false;
	}

	private getModelVersion(model: string): string {
		// Handle known models with specific versions
		const modelMap: {[key: string]: string} = {
			'anthropic/claude-3.5-sonnet': 'claude-3-5-sonnet-20241022',
			'anthropic/claude-3-sonnet': 'claude-3-sonnet-20240229',
			'anthropic/claude-3-opus': 'claude-3-opus-20240229'
		};

		if (modelMap[model]) {
			return modelMap[model];
		}

		// For other Anthropic models, append -latest and replace periods with dashes
		if (model.startsWith('anthropic/')) {
			const baseName = model.replace('anthropic/', '').replace(/\./g, '-');
			if (baseName.endsWith('-latest')) {
				return baseName;
			}

			return `${baseName}-latest`;
		}

		throw new Error(`Unsupported model: ${model}`);
	}

	private getSelectedModel(params: RouterParams): string {
		const defaultModel = 'anthropic/claude-3.5-sonnet';

		if (!params.models?.length) {
			return defaultModel;
		}

		// If * is provided, use default
		if (params.models.includes('*')) {
			return defaultModel;
		}

		// Filter to Anthropic models only
		const anthropicModels = params.models.filter(m =>
			m.startsWith('anthropic/') ||
			this.supportedModels.some(pattern => this.matchesWildcard(pattern, m))
		);

		if (!anthropicModels.length) {
			return defaultModel;
		}

		// If anthropic/* is provided, use default
		if (anthropicModels.includes('anthropic/*')) {
			return defaultModel;
		}

		// Use first matching model
		return anthropicModels[0];
	}

	protected async executeCompletion(
		messages: CompletionMessage[],
		params: RouterParams
	): Promise<{content: string; provider: string; model: string; additional?: Record<string, unknown>; toolCalls?: ToolCall[]}> {
		const formattedMessages = messages.map(msg => ({
			role: msg.role === 'assistant' ? 'assistant' : 'user',
			content: Array.isArray(msg.content)
				? convertMessageContentForAnthropic(msg.content)
				: [{ type: 'text', text: msg.content }]
		}));

		const requestedModel = this.getSelectedModel(params);
		const model = this.getModelVersion(requestedModel);

		// Add tools to the Claude request if provided
		const tools = params.tools?.map(tool => ({
			name: tool.function.name,
			description: tool.function.description,
			input_schema: tool.function.parameters
		}));

		const response = await this.claudeService.getCompletion(formattedMessages, undefined, model, tools);

		// For Anthropic, include available metadata from their response
		const additional: Record<string, unknown> = {};
		const toolCalls: ToolCall[] = [];

		if (typeof response === 'object' && response !== null) {
			const { content, usage, stop_reason, stop_sequence, tool_calls } = response as any;
			if (usage) {
				additional.promptTokens = usage.input_tokens;
				additional.completionTokens = usage.output_tokens;
				additional.totalTokens = usage.input_tokens + usage.output_tokens;
			}
			if (stop_reason) additional.stopReason = stop_reason;
			if (stop_sequence) additional.stopSequence = stop_sequence;

			// Handle tool calls if present, preserving original type unless it's 'function'
			if (tool_calls?.length) {
				toolCalls.push(...tool_calls.map((tc: any) => ({
					type: tc.type === 'function' ? 'custom' : tc.type, // Only change 'function' to 'custom'
					id: tc.id,
					function: {
						name: tc.function.name,
						arguments: tc.function.arguments
					}
				})));
			}

			return {
				content: content || response,
				provider: 'Anthropic',
				model: model,
				additional,
				toolCalls: toolCalls.length > 0 ? toolCalls : undefined
			};
		}

		return {
			content: response as string,
			provider: 'Anthropic',
			model: model,
			additional,
			toolCalls: toolCalls.length > 0 ? toolCalls : undefined
		};
	}

	protected async executeToolFunction(name: string, args: any): Promise<any> {
		// Tool execution is handled by BaseRouterTarget using the tools from params
		throw new Error('Anthropic does not execute tools directly');
	}
}
