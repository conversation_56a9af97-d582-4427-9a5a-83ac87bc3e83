import { ImageUrlContent, Base64ImageContent, MessageContent } from '../types';

export function parseDataUrl(dataUrl: string): { mediaType: string; base64Data: string } {
  const matches = dataUrl.match(/^data:([^;]+);base64,(.+)$/);
  if (!matches) {
    throw new Error('Invalid data URL format');
  }
  return {
    mediaType: matches[1],
    base64Data: matches[2]
  };
}

export function convertImageUrlToAnthropicFormat(imageUrlContent: ImageUrlContent): Base64ImageContent | null {
  try {
    if (!imageUrlContent.image_url.url.startsWith('data:')) {
      return null; // Not a data URL, let Anthropic handle the URL directly
    }

    const { mediaType, base64Data } = parseDataUrl(imageUrlContent.image_url.url);

    if (!['image/jpeg', 'image/png', 'image/gif', 'image/webp'].includes(mediaType)) {
      throw new Error(`Unsupported media type: ${mediaType}`);
    }

    return {
      type: 'image',
      source: {
        type: 'base64',
        media_type: mediaType as 'image/jpeg' | 'image/png' | 'image/gif' | 'image/webp',
        data: base64Data
      }
    };
  } catch (error) {
    console.error('Error converting image URL to Anthropic format:', error);
    return null;
  }
}

export function convertMessageContentForAnthropic(content: MessageContent[]): any[] {
  return content.map(item => {
    if (item.type === 'image_url') {
      const converted = convertImageUrlToAnthropicFormat(item);
      return converted || item; // If conversion fails, keep original format
    }
    return item;
  });
}
