import {injectable, service} from '@loopback/core';
import {EarnCondition, EarnConditionWithRelations, EarnEffectWithRelations, LoyaltyCurrencyBalance, LoyaltyEarnWithRelations, RaleonUserIdentityWithRelations, VipTier, VipTierWithRelations} from '../models';
import {repository} from '@loopback/repository';
import {FeatureSettingRepository, LoyaltyCampaignRepository, LoyaltyCurrencyBalanceRepository, LoyaltyProgramRepository, RaleonUserIdentityRepository, VipTierRepository} from '../repositories';
import {ShopifyApiInvoker} from './shopify/shopify-api-invoker.service';
import {LoyaltyEventPublisher} from './event-stream/loyalty-event-publisher.service';
import {LoyaltyDetailsService} from './loyalty/loyalty-details.service';

interface TierCondition {
	amount: number;
}

interface Tier {
	id: number;
	name: string;
	ttmCondition: TierCondition;
}

interface TierProgress {
	nextTier: Tier | null;
	pointsNeeded: number | null;
	currentPoints: number;
}

interface CustomerTierInfo {
	tierName: string;
	progress: TierProgress;
}

@injectable()
export class TierService {
	constructor(
		@repository(LoyaltyProgramRepository)
		private loyaltyProgramRepository: LoyaltyProgramRepository,
		@repository(LoyaltyCampaignRepository)
		private loyaltyCampaignRepository: LoyaltyCampaignRepository,
		@repository(RaleonUserIdentityRepository)
		private raleonUserIdentityRepository: RaleonUserIdentityRepository,
		@repository(LoyaltyCurrencyBalanceRepository)
		private loyaltyCurrencyBalanceRepository: LoyaltyCurrencyBalanceRepository,
		@repository(VipTierRepository)
		private vipTierRepository: VipTierRepository,
		@repository(FeatureSettingRepository)
		private featureSettingRepository: FeatureSettingRepository,
		@service(ShopifyApiInvoker)
		private shopifyApiInvoker: ShopifyApiInvoker,
		@service(LoyaltyEventPublisher)
		private loyaltyEventPublisher: LoyaltyEventPublisher,
		@service(LoyaltyDetailsService)
		private loyaltyDetailsService: LoyaltyDetailsService
	) { }

	async getTiers(orgId: number): Promise<Array<VipTier & VipTierWithRelations & {entryConditions: Array<EarnCondition>, ttmCondition: EarnCondition}>> {
		const orgPrograms = await this.loyaltyProgramRepository.find({
			where: {
				orgId,
				active: true
			}
		});

		const orgCampaigns = await this.loyaltyCampaignRepository.find({
			where: {
				loyaltyProgramId: {
					inq: orgPrograms.map(p => p.id!)
				},
				active: true
			},
			include: [
				{
					relation: 'loyaltyEarns',
					scope: {
						order: ['priority ASC'],
						include: [
							{
								relation: 'earnEffects',
								scope: {
									include: [
										// {
										// 	relation: 'loyaltyCurrency',
										// 	scope: {
										// 		fields: {
										// 			conversionToUSD: false
										// 		}
										// 	}
										// },
										{
											relation: 'loyaltyRewardDefinition',
											scope: {
												include: [
													{
														relation: 'rewardCoupon',
													}
												]
											}
										}
									]
								}
							},
							{relation: 'earnConditions'}
						],
						where: {
							active: {inq: [null, true]},
							recommendationState: {inq: ['NONE', 'APPROVED_RECOMMENDATION']}
						}
					}
				}
			]
		});

		const vipTiers = await this.vipTierRepository.find({
			where: {
				loyaltyCampaignId: {
					inq: orgCampaigns.map(c => c.id!)
				},

			},
			include: [
				{
					relation: 'loyaltyCampaign'
				}
			],
		});

		const entryConditionMap = new Map<number, Array<EarnCondition>>();
		orgCampaigns
			.map(x => x.loyaltyEarns)
			.flat()
			.filter(x =>
				x?.active &&
				x?.earnEffects?.some?.(y => (y as EarnEffectWithRelations)?.loyaltyRewardDefinition?.rewardCoupon?.amountType === 'vip-tier')
			)
			.forEach(earn => {
				const effect = (earn?.earnEffects?.find?.(y => (y as EarnEffectWithRelations)?.loyaltyRewardDefinition?.rewardCoupon?.amountType === 'vip-tier'));
				const coupon = (effect as EarnEffectWithRelations)?.loyaltyRewardDefinition?.rewardCoupon;
				const vipTierId = coupon?.amount;

				const condition = earn?.earnConditions?.find?.(x => x?.type === 'vip-entry');
				if (vipTierId && condition) {
					if (!entryConditionMap.has(vipTierId)) {
						entryConditionMap.set(vipTierId, []);
					}
					entryConditionMap.get(vipTierId)!.push(condition);
				}
			});

		const tiers = vipTiers.map(x => ({
			...x,
			entryConditions: entryConditionMap.get(x.id as number) || [],
			ttmCondition: entryConditionMap.get(x.id as number)?.find?.(c => c?.variable === 'points-ttm')
		})).sort((a, b) =>
			(b?.ttmCondition?.amount || 0) -
			(a?.ttmCondition?.amount || 0)
		)

		return tiers as Array<VipTier & VipTierWithRelations & {entryConditions: Array<EarnCondition>, ttmCondition: EarnCondition}>;
	}

	private calculatePointsToNextTier(
		currentPoints: number,
		tiers: any[]
	): {nextTier: Tier | null; pointsNeeded: number | null} {
		// Sort tiers by points threshold ascending
		const sortedTiers = [...tiers].sort(
			(a, b) => (a.ttmCondition?.amount || 0) - (b.ttmCondition?.amount || 0)
		);

		// Find the next tier above current points
		for (const tier of sortedTiers) {
			if (!tier.ttmCondition?.amount) continue;

			if (tier.ttmCondition.amount > currentPoints) {
				return {
					nextTier: tier,
					pointsNeeded: tier.ttmCondition.amount - currentPoints
				};
			}
		}

		// If no next tier found (user is at highest tier)
		return {
			nextTier: null,
			pointsNeeded: null
		};
	}

	async reassignUsersForAllTiers(orgId: number, userIdentitiesParam?: Array<RaleonUserIdentityWithRelations>) {
		const BATCH_SIZE = 1000;
		let skipPaging = false;
		let userIdentities: Array<RaleonUserIdentityWithRelations> = [];
		if (userIdentitiesParam) {
			skipPaging = true;
		}
		let page = 0;
		let hasMoreData = true;
		const featureState = await this.featureSettingRepository.findOne({
			where: {
				name: 'vip',
				organizationId: orgId
			}
		});

		const tiers = featureState?.enabled && featureState?.live
			? await this.getTiers(orgId)
			: [];

		while (hasMoreData) {
			if (!skipPaging) {
				userIdentities = await this.raleonUserIdentityRepository.find({
					where: {
						orgId
					},
					include: [
						{
							relation: 'raleonUser',
							scope: {
								include: [
									{
										relation: 'loyaltyCurrencyBalance',
										scope: {
											include: [
												{
													relation: 'loyaltyCurrencyTxLogs'
												}
											]
										}
									},
								]
							}
						}
					],
					limit: BATCH_SIZE,
					skip: page * BATCH_SIZE
				});
			} else {
				userIdentities = userIdentitiesParam!;
			}

			page += 1;
			hasMoreData = userIdentities.length === BATCH_SIZE;
			if (skipPaging) {
				hasMoreData = false;
			}

			const orgUserBalances = new Map<number, LoyaltyCurrencyBalance>();
			const userIds = userIdentities.map(u => u.raleonUserId!);

			const orgUserBalanceRecords = await this.loyaltyCurrencyBalanceRepository.find({
				where: {
					raleonUserId: {
						inq: userIds
					}
				}
			});

			orgUserBalanceRecords.forEach(b => orgUserBalances.set(b.raleonUserId as number, b));

			if (!userIdentities.length) {
				console.warn('No identities found or passed in for reassigning VIP Tiers');
				return;
			}

			if (userIdentities.every(u => !u.raleonUser)) {
				throw new Error('All identities are missing RaleonUser records. If overriding Identities for VIP Tier updates, ensure the Identities have RaleonUser relation populated');
			}

			const updatedUsers = new Set<RaleonUserIdentityWithRelations>();
			const customerTierProgressMap = new Map<string, TierProgress>();

			for (const userIdentity of userIdentities) {
				if (!userIdentity.raleonUser) {
					console.warn(`User ${userIdentity.id} is missing a RaleonUser, not updating VIP Tier`);
					continue;
				}

				const userBalance = orgUserBalances.get(userIdentity.raleonUserId!);
				const points = userBalance?.trailingTwelveMonthGrantTotal || 0;
				const currentTier = userIdentity.vipTierId;
				const tierProgress = this.calculatePointsToNextTier(points, tiers);

				// Store the tier progress for this user
				customerTierProgressMap.set(userIdentity.identityValue, {
					...tierProgress,
					currentPoints: points
				});

				let matched = false;
				for (const tier of tiers) {
					if (!tier.ttmCondition) {
						continue;
					}
					if (points >= tier.ttmCondition.amount!) {
						userIdentity.vipTierId = tier.id;
						matched = true;
						break;
					}
				}

				if (!matched && currentTier) {
					userIdentity.vipTierId = undefined;
				}

				if (currentTier !== userIdentity.vipTierId) {
					updatedUsers.add(userIdentity);
				}
			}

			const tierNameMap = new Map<number, string>();
			for (const tier of tiers) {
				tierNameMap.set(tier.id as number, tier.name!);
			}

			const customerIdToTierInfo = new Map<string, CustomerTierInfo>();
			const tierUpdates = new Map<number, Array<RaleonUserIdentityWithRelations>>();

			updatedUsers.forEach(u => {
				if (!tierUpdates.has(u.vipTierId!)) {
					tierUpdates.set(u.vipTierId!, []);
				}
				tierUpdates.get(u.vipTierId!)!.push(u);

				const tierProgress = customerTierProgressMap.get(u.identityValue)!;
				customerIdToTierInfo.set(u.identityValue, {
					tierName: tierNameMap.get(u.vipTierId!)!,
					progress: tierProgress
				});
			});

			for (const [tierId, users] of tierUpdates) {
				await this.raleonUserIdentityRepository.updateAll({
					vipTierId: tierId
				}, {
					id: {
						inq: users.map(u => u.id!)
					}
				});
			}
			setImmediate(() => {
				this.updateVipTierMetafields(
					orgId,
					customerIdToTierInfo)});
		}
	}

	private async updateVipTierMetafields(orgId: number, customerTierInfo: Map<string, CustomerTierInfo>) {
		if (customerTierInfo.size === 0) {
		  return;
		}

		const metafields = Array.from(customerTierInfo.entries()).flatMap(([customerId, info]) => [
		  {
			namespace: 'raleonInfo',
			key: 'raleon_vip_tier',
			value: customerTierInfo.get(customerId)?.tierName,
			object: "Customer",
			objectId: customerId
		  }
		]);

		const body = JSON.stringify({ metafields });

		await this.shopifyApiInvoker.invokeAdminApi(
		  orgId,
		  `/raleon-data/metafields`,
		  'POST',
		  body,
		);

		if (customerTierInfo.size === 1) {
		  const customerId = Array.from(customerTierInfo.keys())[0];
		  const info = Array.from(customerTierInfo.values())[0];
		  await this.loyaltyEventPublisher.publishVipTierUpdatedEvent(
			customerId,
			orgId,
			info.tierName,
			info.progress.pointsNeeded ?? 0
		  );
		}
	  }
}
