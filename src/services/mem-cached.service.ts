import {BindingScope, injectable} from '@loopback/core';
import Memcached from 'memcached';

@injectable({scope: BindingScope.SINGLETON})
export class MemcachedLockService {
  private memcached: Memcached;
  private retryCount: number = 5;
  private retryDelay: number = 100; // ms

  constructor() {
    // Hardcoded AWS ElastiCache endpoint
    const memcachedEndpoint = 'memcached-cluster-prod.c8q2rh.cfg.use1.cache.amazonaws.com:11211';
    const memcachedOptions = {
      retries: 3,
      retry: 1000,
      timeout: 5000,
      reconnect: 10000,
      poolSize: 10
    };

    this.memcached = new Memcached(memcachedEndpoint, memcachedOptions);
  }

  async acquire(key: string, ttl: number): Promise<boolean> {
    for (let i = 0; i < this.retryCount; i++) {
      const locked = await new Promise<boolean>((resolve) => {
        this.memcached.add(key, '1', ttl, (err: Error | null, result: any) => {
          if (err) {
            console.error('Memcached lock error:', err);
            resolve(false);
          } else {
            resolve(result);
          }
        });
      });

      if (locked) {
        return true;
      }

      await new Promise(resolve => setTimeout(resolve, this.retryDelay));
    }

    return false;
  }

  async release(key: string): Promise<void> {
    await new Promise<void>((resolve) => {
      this.memcached.del(key, (err: Error | null) => {
        if (err) {
          console.error('Error releasing lock:', err);
        }
        resolve();
      });
    });
  }

  async getStatus(key: string): Promise<any> {
    return new Promise((resolve) => {
      this.memcached.gets(key, (err: Error | null, data: any) => {
        if (err) {
          console.error(`Lock status error for key ${key}:`, err);
          resolve(null);
        } else {
          resolve(data);
        }
      });
    });
  }

  // Helper method to create a standardized lock key
  createLockKey(prefix: string, ...parts: (string | number)[]): string {
    return `${prefix}:${parts.join(':')}`;
  }

  /**
   * Sets a value in the memcached cache with the specified TTL
   *
   * @param key - Cache key to store data under
   * @param value - Data to cache
   * @param ttl - Time to live in seconds
   * @returns Promise that resolves when caching is complete
   */
  async setCache(key: string, value: any, ttl: number): Promise<void> {
    return new Promise((resolve, reject) => {
      this.memcached.set(
        key, 
        typeof value === 'string' ? value : JSON.stringify(value), 
        ttl, 
        (err: Error | null) => {
          if (err) {
            console.error(`Error setting cache for key ${key}:`, err);
            reject(err);
          } else {
            resolve();
          }
        }
      );
    });
  }

  /**
   * Gets a value from the memcached cache
   *
   * @param key - Cache key to retrieve data from
   * @returns Promise that resolves with the cached value or null if not found
   */
  async getCache(key: string): Promise<any> {
    return new Promise((resolve) => {
      this.memcached.get(key, (err: Error | null, data: any) => {
        if (err) {
          console.error(`Error getting cache for key ${key}:`, err);
          resolve(null);
        } else {
          try {
            // Try to parse as JSON first
            const parsed = JSON.parse(data);
            resolve(parsed);
          } catch {
            // If parsing fails, return as-is
            resolve(data);
          }
        }
      });
    });
  }
}
