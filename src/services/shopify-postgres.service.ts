import {BindingScope, injectable} from '@loopback/core';
import { SecretsManager } from 'aws-sdk';
import { Client } from 'pg';

@injectable({scope: BindingScope.TRANSIENT})
export class ShopifyPostgresService {
	async getApiAccessToken(shopDomain: string): Promise<string> {
		const databaseInfo = await this.getDatabaseInfo();

		const client = new Client({
			user: databaseInfo.username,
			password: databaseInfo.password,
			host: databaseInfo.host,
			database: databaseInfo.dbname,
			port: databaseInfo.port,
		});

		await client.connect();

		const query = `SELECT "accessToken" FROM "Session" WHERE "shop" = $1 LIMIT 1`;
		const values = [shopDomain];

		try {
			const res = await client.query(query, values);
			if (res.rows.length) {
				console.log(`row: ${JSON.stringify(res.rows[0])}`);
				return res.rows[0].accessToken;
			} else {
				throw new Error(
					`No access token found for domain: ${shopDomain}`
				);
			}
		} catch (err) {
			console.error("Error querying database", err);
			throw err;
		} finally {
			await client.end();
		}
	}

	private async getDatabaseInfo(): Promise<any> {
		const client = new SecretsManager({
			region: 'us-east-1',
		});

		const secret: any = await client.getSecretValue({
			SecretId: process.env.SHOPIFY_DATABASE_SECRET_ARN!,
		})
		.promise()
		.catch((err) => console.error(err));
		const secretString = JSON.parse(secret.SecretString!);
		console.log(`secret is: ${JSON.stringify(secret.SecretString!)}`);
		return secretString;
	}
}
