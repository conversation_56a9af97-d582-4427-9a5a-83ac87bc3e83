// Default Plan Strategy - Thematic Month approach
export const DEFAULT_PLAN_STRATEGY = `First, I create a Priority Matrix with three layers:
1. Revenue Drivers (High Priority)
* Major shopping holidays or seasonal peaks
* Inventory clearance needs
* New product launches
* Best-seller restocks
* End-of-season transitions
2. Brand Building (Medium Priority)
* Category education opportunities
* Customer story features
* Behind-the-scenes content
* Community initiatives
* Partnership/collaboration launches
3. Maintenance (Baseline Priority)
* Regular newsletter cadence
* Automated flow updates
* Cart abandonment optimization
* Post-purchase nurture content

Then I analyze each potential event/moment through these filters:
* For Seasonal Events/Holidays
* Historical performance data from past years
* Search trend data for seasonal keywords
* Lead time needed for customer purchase decisions
* Competition level during this period
* Inventory position for relevant products

For Sales/Promotions:
* Margin impact at different discount levels
* Inventory aging and turnover goals
* Competitive promotion calendar
* Customer purchase frequency patterns
* Previous promotion performance

For Educational Series:
* Current customer service pain points
* Product category learning curves
* Seasonal usage challenges
* Common customer questions
* User behavior data showing knowledge gaps

For Industry Moments:
* Trade show or conference timing
* Major industry announcements
* Category innovation launches
* Regulatory changes
* Market trend shifts

For Competitor Events:
* Known sale periods
* Product launch patterns
* Historical promotional timing
* Marketing campaign cycles
* Partnership announcements

Then I create a Content Stacking plan where each major moment gets:
* Pre-event buildup
* Main event focus
* Post-event follow-up
* Cross-channel support
* Segmented messaging variations

Finally, Score & Slot Ideas by:
* Rating each idea for Reach, Relevance to Segment, Revenue Potential, Production Effort (1-5 scale)
* Focus on the ones scoring >= 3 on the first three factors and <=3 on effort
* Make sure every major segment has at least two strong fits, if not, spin new angles

When considering the timing and sequence of emails, we want to make sure they flow so look at:
* Example of what we want to accomplish is value-add emails into an ask email (when appropriate)
* Priming Window: send the educational/entertaining primer 24‑72 h before the ask—fresh enough to recall, far enough to avoid back‑to‑back fatigue.
* Purchase Latency: align ask emails with typical decision cycles—complex products may need a 5‑day education drip.
* Pay‑Day Effect: wage‑earner audiences convert best within 24 h of the 1st or 15th; shift big‑ticket asks there.
* Time‑Zone Clustering: split sends when 20%+ of list sits 3+ hours apart so nobody gets a 3 a.m. blast.
* Deliverability Windows: avoid Monday 8‑9 a.m. "inbox crush" and Friday 5 p.m. "bulk folder risk" windows unless historical data proves otherwise.
* Stock & Shipping Dependencies: Always double check with user if inventory >= 10% stock for high‑urgency ask so we don't run out; match shipping‑deadline promos to carrier cut‑offs.

If it makes sense, suggest a test campaign in a test slot:
* The test campaign is to try out new subject and preview lines, along with less proven content formats
* Pick the lowest‑revenue weekday/hour in the scraped‑inbox heat‑map.
* Flag subject line with an emoji 🧪

Lastly, double check ideas to balance the calendar:
* Email frequency by segment
* Promotion vs. value-add content ratio
* Revenue potential distribution
* Resource requirements
* Testing opportunities`;