import {BindingScope, inject, injectable, service} from '@loopback/core';
import {MemcachedLockService} from '../mem-cached.service';
import {PromptContextService} from './prompt-context.service';
import {KlaviyoMetricsService} from '../metrics/klaviyo-metrics.service';
import {EcommerceMetricController} from '../../controllers/ecommerce-metric.controller';
import {repository} from '@loopback/repository';
import {OrganizationPlannerPlanRepository} from '../../repositories';

/**
 * Service to cache prompt context data with a 24-hour TTL
 *
 * This service reduces API load and improves performance by caching:
 * - Metrics data from Klaviyo
 * - Organization context data
 * - Campaign history
 *
 * The service provides both caching functionality and data fetching methods
 * that are shared with PromptContextService for consistent data retrieval.
 */

@injectable({scope: BindingScope.TRANSIENT})
export class PromptCacheService {
  /** Cache TTL of 24 hours in seconds */
  private readonly TTL_24_HOURS = 86400;
  /** Cache key prefix for all prompt-related caches */
  private readonly PREFIX = 'prompt-cache';
  /** Cache type for metrics data */
  private readonly METRICS_CACHE_TYPE = 'metrics';
  /** Cache type for context data */
  private readonly CONTEXT_CACHE_TYPE = 'context';

  constructor(
    @inject('datasources.dev_db')
    private devDbDataSource: any,
    @service(MemcachedLockService)
    private memcachedService: MemcachedLockService,
    @service(KlaviyoMetricsService)
    private klaviyoMetricsService: KlaviyoMetricsService,
    @inject('controllers.EcommerceMetricController')
    private ecommerceMetricController: EcommerceMetricController,
	@repository(OrganizationPlannerPlanRepository)
	private organizationPlannerPlanRepository: OrganizationPlannerPlanRepository
  ) {}

  /**
   * Creates a standardized cache key for prompt context data
   *
   * @param orgId - Organization ID to create key for
   * @param cacheType - Type of cache (e.g., 'metrics', 'context')
   * @returns Formatted cache key
   */
  private createCacheKey(orgId: number, cacheType: string): string {
    return this.memcachedService.createLockKey(this.PREFIX, orgId.toString(), cacheType);
  }

  /**
   * Checks if context cache exists for the given organization
   *
   * @param orgId - Organization ID to check
   * @returns Promise resolving to true if cache exists, false otherwise
   */
  async checkContextCacheExists(orgId: number): Promise<boolean> {
    const cacheKey = this.createCacheKey(orgId, this.CONTEXT_CACHE_TYPE);
    const cachedData = await this.memcachedService.getStatus(cacheKey);
    return !!(cachedData && cachedData.value);
  }

  /**
   * Checks if metrics cache exists for the given organization
   *
   * @param orgId - Organization ID to check
   * @returns Promise resolving to true if cache exists, false otherwise
   */
  async checkMetricsCacheExists(orgId: number): Promise<boolean> {
    const cacheKey = this.createCacheKey(orgId, this.METRICS_CACHE_TYPE);
    const cachedData = await this.memcachedService.getStatus(cacheKey);
    return !!(cachedData && cachedData.value);
  }

  /**
   * Clears all prompt caches for the given organization
   *
   * @param orgId - Organization ID to clear caches for
   * @returns Promise that resolves when clearing is complete
   */
  async clearCache(orgId: number): Promise<void> {
    const contextCacheKey = this.createCacheKey(orgId, this.CONTEXT_CACHE_TYPE);
    const metricsCacheKey = this.createCacheKey(orgId, this.METRICS_CACHE_TYPE);

    await Promise.all([
      this.memcachedService.release(contextCacheKey),
      this.memcachedService.release(metricsCacheKey)
    ]);

    console.log(`Cleared all prompt caches for org ${orgId}`);
  }

  /**
   * Fetches metrics data from Klaviyo and campaign history
   *
   * This is the core shared method used by both cacheMetricsData and
   * PromptContextService.getMetricsData to ensure consistent data retrieval.
   *
   * @param orgId - Organization ID to fetch metrics for
   * @param includeLast50Campaigns - Whether to include last 50 campaigns (defaults to true)
   * @returns Promise resolving to metrics data or null if failed
   */
  async fetchMetricsData(orgId: number, includeLast50Campaigns = true): Promise<any | null> {
    try {
      // Fetch Klaviyo metrics
      const klaviyoMetrics = await this.ecommerceMetricController.fetchMetric(
        'klaviyo_campaigns',
        'none',
        '',
        '',
        'latest',
        orgId
      );

      // Process metrics data
      const metricsData: any = {
        top5SubjectLines: await this.klaviyoMetricsService.getTop5SubjectLines(klaviyoMetrics),
        klaviyoCampaignsAll: await this.klaviyoMetricsService.getKlaviyoCampaignsLast12Months(klaviyoMetrics),
        klaviyoCampaigns12MonthAgo: await this.klaviyoMetricsService.getKlaviyoCampaigns12MonthAgo(klaviyoMetrics),
        klaviyoCampaignSummary: await this.klaviyoMetricsService.getKlaviyoCampaignSummary(klaviyoMetrics),
        klaviyoLastWeek: await this.klaviyoMetricsService.getKlaviyoCampaignsLastWeek(klaviyoMetrics),
        klaviyoLastMonth: await this.klaviyoMetricsService.getKlaviyoCampaignsLastMonth(klaviyoMetrics),
      };

      // Only fetch last 50 campaigns if requested
      if (includeLast50Campaigns) {
        metricsData.last50Campaigns = await this.getLast50Campaigns(orgId);
      }

      return metricsData;
    } catch (error) {
      console.error(`Error fetching metrics data for org ${orgId}:`, error);
      return null;
    }
  }

  /**
   * Fetches and caches metrics data from Klaviyo and campaign history
   *
   * This method uses the shared fetchMetricsData method and then caches the results.
   * It's used both directly and by PromptContextService.getMetricsData.
   *
   * @param orgId - Organization ID to fetch metrics for
   * @returns Promise resolving to true if caching succeeded, false otherwise
   */
  async cacheMetricsData(orgId: number): Promise<boolean> {
    const cacheKey = this.createCacheKey(orgId, this.METRICS_CACHE_TYPE);

    // Check if data is already cached
    const cachedData = await this.memcachedService.getStatus(cacheKey);
    if (cachedData) {
      console.log(`Metrics data already cached for org ${orgId}`);
      return true;
    }

    try {
      // Fetch metrics data using the shared method
      const metricsData = await this.fetchMetricsData(orgId);

      if (!metricsData) {
        return false;
      }

      // Store in cache
      await this.setCache(cacheKey, metricsData);
      console.log(`Cached metrics data for org ${orgId}`);
      return true;
    } catch (error) {
      console.error(`Error caching metrics data for org ${orgId}:`, error);
      return false;
    }
  }

  /**
   * Fetches and caches organization context data like AOV
   *
   * @param orgId - Organization ID to fetch context for
   * @returns Promise resolving to true if caching succeeded, false otherwise
   */
  async cacheContextData(orgId: number): Promise<boolean> {
    const cacheKey = this.createCacheKey(orgId, this.CONTEXT_CACHE_TYPE);

    // Check if data is already cached
    const cachedData = await this.memcachedService.getStatus(cacheKey);
    if (cachedData) {
      console.log(`Context data already cached for org ${orgId}`);
      return true;
    }

    try {
      // Execute SQL for AOV
      const getAverageAovQuery = await this.devDbDataSource.execute(`
        SELECT
          AVG(CASE
            WHEN aov IS NOT NULL AND aov > 0
            THEN aov
            END) as average_aov
        FROM raleonuseridentity
        WHERE
          orgid = $1
      `, [orgId]);

      const averageAov = getAverageAovQuery[0]?.average_aov;

      // Store in cache
      await this.setCache(cacheKey, { averageAov });
      console.log(`Cached context data for org ${orgId}`);
      return true;
    } catch (error) {
      console.error(`Error caching context data for org ${orgId}:`, error);
      return false;
    }
  }

  /**
   * Retrieves cached metrics data if available
   *
   * @param orgId - Organization ID to get cached metrics for
   * @returns Cached metrics data or null if not found/expired
   */
  async getCachedMetricsData(orgId: number): Promise<any> {
    const cacheKey = this.createCacheKey(orgId, this.METRICS_CACHE_TYPE);
    const cachedData = await this.memcachedService.getStatus(cacheKey);

    if (!cachedData || !cachedData.value) {
      return null;
    }

    return JSON.parse(cachedData.value);
  }

  /**
   * Retrieves cached context data if available
   *
   * @param orgId - Organization ID to get cached context for
   * @returns Cached context data or null if not found/expired
   */
  async getCachedContextData(orgId: number): Promise<any> {
    const cacheKey = this.createCacheKey(orgId, this.CONTEXT_CACHE_TYPE);
    const cachedData = await this.memcachedService.getStatus(cacheKey);

    if (!cachedData || !cachedData.value) {
      return null;
    }

    return JSON.parse(cachedData.value);
  }

  /**
   * Sets cache with the proper TTL of 24 hours
   *
   * @param key - Cache key to store data under
   * @param data - Data to cache
   * @returns Promise that resolves when caching is complete
   */
  private async setCache(key: string, data: any): Promise<void> {
    return this.memcachedService.setCache(key, data, this.TTL_24_HOURS);
  }

  /**
   * Gets the last 50 campaigns from the database using a direct SQL query
   * for better performance than the repository approach
   *
   * @param orgId - Organization ID to fetch campaigns for
   * @returns Formatted string containing campaign information
   */
   /**
   * Gets the last 50 campaigns for an organization
   * This method is maintained for backward compatibility
   * The PromptCacheService handles caching for this data
   *
   * @param orgId - Organization ID to fetch campaigns for
   * @returns Formatted string containing campaign information
   */
  async getLast50Campaigns(orgId: number): Promise<string> {
    // This method is now delegated to the PromptCacheService for better caching
    // We're keeping this method for backward compatibility
    try {
      // Get all planner plans for the organization
      const plans = await this.organizationPlannerPlanRepository.find({
        where: { organizationId: orgId },
        include: [{
          relation: 'plannerPlanVersions',
          scope: {
            include: [{
              relation: 'plannerCampaigns'
            }]
          }
        }]
      });

      // Collect all campaigns from all versions and plans
      let allCampaigns = [];
      for (const plan of plans) {
        for (const version of plan.plannerPlanVersions || []) {
          if (version.plannerCampaigns) {
            allCampaigns.push(...version.plannerCampaigns);
          }
        }
      }

      // Sort by id descending to get most recent first and take last 50
      const last50Campaigns = allCampaigns
        .sort((a: any, b: any) => b.id - a.id)
        .slice(0, 50);

      if (last50Campaigns.length === 0) {
        return "No previous campaigns found";
      }

      // Format the campaigns into a readable string
      return last50Campaigns
        .map((c: any) => `Campaign: ${c.name}\nDescription: ${c.description}\nType: ${c.type}\n`)
        .join('\n---\n');

    } catch (error) {
      console.error('Error fetching last 50 campaigns:', error);
      return 'Error fetching campaign history';
    }
  }
}
