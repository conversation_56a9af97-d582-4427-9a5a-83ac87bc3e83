import {BindingScope, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import {OrganizationRepository} from '../../repositories';
import fetch from 'node-fetch';
import {promisify} from 'util';

const jwt = require('jsonwebtoken');
const signAsync = promisify(jwt.sign);
const verifyAsync = promisify(jwt.verify);

@injectable({scope: BindingScope.TRANSIENT})
export class ShopifyApiInvoker {

	constructor(
		@repository(OrganizationRepository)
		private organizationRepository: OrganizationRepository
	){}

	async invokeAdminApi(orgId: number, path: string, method: string, body?: string): Promise<any> {
		const domain = await this.getDomain(orgId);
		const token = await this.generateApiToken(domain!);
		try {
			const params: any = {
				method,
				headers: {
					'Content-Type': 'application/json',
					'Authorization': `Bearer ${token}`,
				},
			};
			if (body) {
				params.body = body;
			}

			if (!path.startsWith('/')) {
				path = `/${path}`;
			}

			const appUrl = process.env.RALEON_SHOPIFY_APP_URL || 'https://loyalty-dev.raleon.io';
			console.log("calling", `${appUrl}${path}`)
			const response = await fetch(`${appUrl}${path}`, params);

			if (!response.ok) {
				throw new Error(`HTTP error! status: ${response.status}`);
			}

			const data = await response.json();
			console.log(data);
			return data;
		} catch (error) {
			console.error('There was a problem with the fetch operation:', error);
		}
	}

	private async getDomain(orgId: number) {
		const org = await this.organizationRepository.find({
			where: {
				id: orgId
			}
		});

		if (!org?.[0]) {
			throw new Error('Organization not found');
		}

		if (!org[0].externalPlanDetails) {
			throw new Error('No plan details found for organization');
		}

		if (!org[0].externalDomain) {
			throw new Error('No domain found for organization');
		}

		return org[0].externalDomain;
	}

	private async generateApiToken(domain: string) {
		const tokenInfo = {
			domain,
			clientId: process.env.SHOPIFY_API_KEY
		};
		return await signAsync(tokenInfo, process.env.SHOPIFY_API_SECRET, {
			expiresIn: 3600,
		});
	}
}
