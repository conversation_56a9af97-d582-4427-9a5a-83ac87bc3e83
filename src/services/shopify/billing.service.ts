import {BindingScope, injectable, service} from '@loopback/core';
import {ShopifyApiInvoker} from './shopify-api-invoker.service';

@injectable({scope: BindingScope.TRANSIENT})
export class BillingService {

	constructor(
		@service(ShopifyApiInvoker)
		private shopifyApiInvoker: ShopifyApiInvoker,
	) {}

	async createSubscription(
		orgId: number,
		amount: number,
		name: string,
		interval: string,
		returnUrl: string,
		test: boolean,
	) {
		console.log(`creating subscription: ${name} for ${orgId}`);
		const subscription = await this.shopifyApiInvoker.invokeAdminApi(
			orgId,
			'/billing',
			'POST',
			JSON.stringify({
				amount,
				test: test || false,
				name,
				interval: interval || 'EVERY_30_DAYS',
				returnUrl: returnUrl || 'https://app.raleon.io/',
			}),
		);

		console.log(`Subscription created: ${JSON.stringify(subscription)}`);
		return subscription;
	}

	async unsubscribe(
		orgId: number,
		subscriptionId: string
	) {
		console.log(`unsubscribe: ${subscriptionId} for ${orgId}`);
		const subscription = await this.shopifyApiInvoker.invokeAdminApi(
			orgId,
			'/billing',
			'POST',
			JSON.stringify({
				subscriptionId
			}),
		);

		console.log(`unsubscribed: ${JSON.stringify(subscription)}`);
		return subscription;
	}
}
