import {BindingScope, injectable, service} from '@loopback/core';
import {ShopifyApiInvoker} from './shopify-api-invoker.service';

const NodeCache = require( "node-cache" );
const cachingSegments = new Set();
const segmentMembersCache = new NodeCache({ stdTTL: 900, checkperiod: 120 });
const segmentMembershipCache = new NodeCache({ stdTTL: 900, checkperiod: 120 });

@injectable({scope: BindingScope.TRANSIENT})
export class SegmentService {

	constructor(
		@service(ShopifyApiInvoker)
		private shopifyApiInvoker: ShopifyApiInvoker,
	) {}

	async getSegments(orgId: number): Promise<any[]> {
		try {
			const response = await this.shopifyApiInvoker.invokeAdminApi(
				orgId,
				'/get-segments',
				'GET'
			);
			return response?.map?.((x: any) => ({
				id: x?.node?.id?.split?.('/')?.pop?.(),
				name: x?.node?.name,
			})) || [];
		} catch (error) {
			console.error('Error fetching segments:', error);
			return [];
		}
	}

	// Method to fetch customer segments from Shopify
	async isCustomerInSegments(orgId: number, customerId: string, segmentIds: Array<string>): Promise<{segmentId: string, isMember: boolean}[]> {
		if (segmentIds.every((segmentId) => segmentMembersCache.has(segmentId) || segmentMembershipCache.has(`${segmentId}-${customerId}`))) {
			console.log('Using cached segments for customer');
			return segmentIds.map((segmentId) => {
				const hash = `${segmentId}-${customerId}`;
				const isMember = segmentMembershipCache.has(hash)
					? segmentMembershipCache.get(hash)
					: (segmentMembersCache.get(segmentId) || [])?.includes(customerId.toString());

				return {
					segmentId,
					isMember
				};
			});
		}

		console.log('Fetching segments for customer');

		// segmentIds.forEach((segmentId) => this.getSegmentCustomers(orgId, segmentId, true).catch(() => {}));

		console.log(`Fetching segments for customer: ${customerId}`);
		try {
			const response = await this.shopifyApiInvoker.invokeAdminApi(
				orgId,
				`/get-customer-segments?id=${customerId}&segmentIds=${segmentIds.join(',')}`,
				'GET'
			);

			const memberships = response?.data?.customerSegmentMembership?.memberships || [];
			for (const membership of memberships) {
				const segmentId = membership.segmentId?.split?.('/')?.pop?.();
				const hash = `${segmentId}-${customerId}`;
				segmentMembershipCache.set(hash, membership.isMember);
			}

			return memberships;
		} catch (error) {
			console.error('Error fetching customer segments:', error);
			return [];
		}
	}

	// Method to check if a customer belongs to a specific segment
	async isCustomerInSegment(orgId:number, customerId: string, segment: string): Promise<boolean> {
		const segments = await this.isCustomerInSegments(orgId, customerId, [segment]);
		return (segments?.[0] as any)?.isMember || false;
	}


	async getSegmentCustomers(orgId: number, segmentId: string, cacheOnly = false): Promise<any[]> {
		if (cachingSegments.has(segmentId) && cacheOnly) {
			console.log('Segment is being fetched');
			return [];
		}

		cachingSegments.add(segmentId);
		if (segmentMembersCache.has(segmentId)) {
			console.log('Using cached segment');
			return segmentMembersCache.get(segmentId);
		}

		console.log(`Fetching customers for segment: ${segmentId}`);
		try {
			const response = await this.shopifyApiInvoker.invokeAdminApi(
				orgId,
				`/get-segment-customers?segmentId=${segmentId}`,
				'GET'
			);
			cachingSegments.delete(segmentId);
			segmentMembersCache.set(segmentId, response?.map((x: any) => x?.node?.id?.split?.('/')?.pop?.()));
			for (const customer of response) {
				const customerdId = customer?.node?.id?.split('/').pop();
				const hash = `${segmentId}-${customerdId}`;
				segmentMembershipCache.set(hash, true);
			}

			return response || [];
		} catch (error) {
			cachingSegments.delete(segmentId);
			console.error('Error fetching segment customers:', error);
			return [];
		}
	}

}
