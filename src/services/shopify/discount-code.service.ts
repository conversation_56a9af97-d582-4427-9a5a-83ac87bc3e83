import {BindingScope, injectable, service} from '@loopback/core';
import {InventoryCouponWithRelations} from '../../models/loyalty/inventory-coupon.model';
import fetch from 'node-fetch';
import {REWARD_COUPON_AMOUNT_TYPES} from '../../models';
import {ShopifyApiInvoker} from './shopify-api-invoker.service';

@injectable({scope: BindingScope.TRANSIENT})
export class DiscountCodeService {

	constructor(
		@service(ShopifyApiInvoker)
		private shopifyApiInvoker: ShopifyApiInvoker,
	) {}

	async createDiscountCode(
		coupon: InventoryCouponWithRelations,
		customerId: string,
		shopifyDomain: string,
		orgId: number,
		loyaltyCampaignId: number | string,
		anonymousCustomer: boolean = false,
	) {
		console.log(`creating discount code for coupon: ${JSON.stringify(coupon)}`)
		console.log(`calling out to shopify admin api`);
		const discount = await this.shopifyApiInvoker.invokeAdminApi(
			orgId,
			'/discount-code',
			'POST',
			JSON.stringify({
				coupon,
				customerId,
				shopifyDomain,
				loyaltyCampaignId,
				anonymousCustomer
			}),
		);

		if (coupon.rewardCoupon.amountType == 'free-product' || coupon.rewardCoupon.amountType == 'percent-off-product') {
			return discount
				.data
				.discountCodeAppCreate
				.codeAppDiscount
				.codes
				.nodes[0]
				.code;
		}

		console.log(`Discount code created: ${JSON.stringify(discount)}`);
		return discount
			.data
			.discountCodeBasicCreate
			.codeDiscountNode
			.codeDiscount
			.codes
			.nodes[0]
			.code;
	}

	async deleteDiscountCode(
		coupon: InventoryCouponWithRelations,
		orgId: number,
	) {
		const discount = await this.shopifyApiInvoker.invokeAdminApi(
			orgId,
			'/discount-code',
			'DELETE',
			JSON.stringify({ coupon }),
		);

		console.log(`Discount code deleted: ${JSON.stringify(discount)}`);
	}

	async createFreeShippingCode(
		coupon: InventoryCouponWithRelations,
		customerId: string,
		shopifyDomain: string,
		orgId: number,
		loyaltyCampaignId: number,
		anonymousCustomer: boolean = false,
	) {
		console.log(`creating discount code for coupon: ${JSON.stringify(coupon)}`)
		console.log(`calling out to shopify admin api`);
		const discount = await this.shopifyApiInvoker.invokeAdminApi(
			orgId,
			'/discount-code',
			'POST',
			JSON.stringify({
				coupon,
				customerId,
				shopifyDomain,
				loyaltyCampaignId,
				anonymousCustomer
			}),
		);

		console.log(`Discount code created: ${JSON.stringify(discount)}`);
		return discount
			.data
			.discountCodeFreeShippingCreate
			.codeDiscountNode
			.codeDiscount
			.codes
			.nodes[0]
			.code;
	}
}
