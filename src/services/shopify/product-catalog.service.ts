import {BindingScope, injectable, service} from '@loopback/core';
import {InventoryCouponWithRelations} from '../../models/loyalty/inventory-coupon.model';
import fetch from 'node-fetch';
import {REWARD_COUPON_AMOUNT_TYPES} from '../../models';
import {ShopifyApiInvoker} from './shopify-api-invoker.service';

@injectable({scope: BindingScope.TRANSIENT})
export class ProoductCatalogService {

	constructor(
		@service(ShopifyApiInvoker)
		private shopifyApiInvoker: ShopifyApiInvoker,
	) {}

	async getProductCatalog(
		customerId: string,
		shopifyDomain: string,
		orgId: number,
	) {
		console.log(`calling out to shopify admin api`);
		const result = await this.shopifyApiInvoker.invokeAdminApi(
			orgId,
			'/product-catalog',
			'POST',
			JSON.stringify({
				customerId,
				shopifyDomain,
			})
		);
		console.log(`product catalog retrieved: ${JSON.stringify(result)}`);
		return result;
	}
}
