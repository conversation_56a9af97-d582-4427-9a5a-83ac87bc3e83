import { uploadNftJsonToBucket, uploadTwitterHtmlToBucket, getTwitterImageUrl } from './raleon-standard-nft-reward-type.service';
const ethSigUtil = require('eth-sig-util');


export class RaleonEthBcnReward {

	async getNftMintData(
		walletAddress: string,
		imageUrl: string,
		htmlFilename: string
	) {
		const { createAlchemyWeb3 } = require("@alch/alchemy-web3");
		const web3 = createAlchemyWeb3(RaleonEthBcnReward.contractData.nftMintUrl);

		const signedVoucher = await this.generateSignedVoucher(walletAddress, imageUrl, htmlFilename, web3);

		const contract = JSON.parse(RaleonEthBcnReward.contractData.nftMintContractAbi);
		const nftContract = new web3.eth.Contract(
			contract.abi,
			RaleonEthBcnReward.contractData.nftMintContractAddress
		);

		const data = {
			'from': walletAddress,
			'to': RaleonEthBcnReward.contractData.nftMintContractAddress,
			'data': nftContract.methods[RaleonEthBcnReward.contractData.nftMintContractFunction](
				[signedVoucher.price, signedVoucher.uri, signedVoucher.buyer, signedVoucher.signature]
			).encodeABI(),
		};
		return data;
	}

	private async generateSignedVoucher(
		walletAddress: string,
		imageUrl: string,
		htmlFilename: string,
		web3: any
	): Promise<any> {
		const domain = [
			{ name: "name", type: "string" },
			{ name: "version", type: "string" },
			{ name: "chainId", type: "uint256" },
			{ name: "verifyingContract", type: "address" }
		];

		const voucherType = [
			{ name: "price", type: "uint256" },
			{ name: "uri", type: "string" },
			{ name: "buyer", type: "address" },
		];

		const contractAddress = RaleonEthBcnReward.contractData.nftMintContractAddress;

		const autoGeneratedJsonFilename = `${walletAddress}-ethbcn.json`;
		const metadataJson = {
			name: `ETH BCN 23: Official Quest NFTs, Powered by Raleon`,
			image: imageUrl,
			description: "Discover these unique digital artifacts from the 2023 ETH BCN conference in this collection of commemorative NFTs designed by the Finnish digital artist Sakke Soini and delivered to attendees by Raleon.io\'s Embedded Quest Platform. These NFTs echo the ethos of ETH BCN and encapsulate the picturesque ambiance of Barcelona. Each NFT is a token of dedication and engagement; they were awarded to conference attendees based on their participation in the official quest in real life and on-chain at ETH BCN 2023. Attendees could ascend through six levels of rarity - Common 1, Common 2, Rare, Epic, Legendary, and the most esteemed, Mythical. The higher the participant\'s engagement, the rarer the NFT awarded. As you explore these pieces, you\'re not just appreciating unique digital art, you\'re journeying through the memories of the ETH BCN conference, captured and embodied in these exclusive NFTs",
			attributes: [
				{
					"display_type": "date",
					"trait_type": "birthday",
					"value": 1688533200
				},
			]
		};
		const twitterImageUrl = getTwitterImageUrl(imageUrl);
		const htmlTwitterContent = `
			<!DOCTYPE html>
			<html>
			<head>
				<meta name="twitter:card" content="summary_large_image"></meta>
				<meta name="twitter:site" content="@Raleon"></meta>
				<meta name="twitter:creator" content="@Raleon"></meta>
				<meta name="twitter:title" content="ETH BCN 23: Official Quest NFTs, Powered by Raleon"></meta>
				<meta name="twitter:description" content="Discover these unique digital artifacts from the 2023 ETH BCN conference in this collection of commemorative NFTs designed by the Finnish digital artist Sakke Soini and delivered to attendees by Raleon.io\'s Embedded Quest Platform. These NFTs echo the ethos of ETH BCN and encapsulate the picturesque ambiance of Barcelona. Each NFT is a token of dedication and engagement; they were awarded to conference attendees based on their participation in the official quest in real life and on-chain at ETH BCN 2023. Attendees could ascend through six levels of rarity - Common 1, Common 2, Rare, Epic, Legendary, and the most esteemed, Mythical. The higher the participant\'s engagement, the rarer the NFT awarded. As you explore these pieces, you\'re not just appreciating unique digital art, you\'re journeying through the memories of the ETH BCN conference, captured and embodied in these exclusive NFTs"></meta>
				<meta name="twitter:image" content="${twitterImageUrl}"></meta>
			</head>
			<body style="background: black">
				<div style="display: flex; justify-content: center;">
					<img src="${twitterImageUrl}" style="height: 600px; width: 450px">
				</div>

				<div style="display: flex; justify-content: center;">
					<a href="https://ethbarcelona.com" style="color: rgb(245, 195, 75)">ETH Barcelona</a>
				</div>
			</body>
			</html>`;

		const voucher = {
			price: 0,
			buyer: walletAddress,
			uri: this.getNftJsonUrl(autoGeneratedJsonFilename)
		};

		await uploadNftJsonToBucket(autoGeneratedJsonFilename, JSON.stringify(metadataJson)).catch();
		await uploadTwitterHtmlToBucket(htmlFilename, htmlTwitterContent).catch();

		//TODO: add a new call similar to the one above to upload the twitter html file to the bucket.

		const domainData = {
			name: "Raleon",
			version: "1",
			chainId: await web3.eth.getChainId(),//Come back and hardcode the ID later
			verifyingContract: contractAddress
		};

		const data = {
			types: {
				EIP712Domain: domain,
				RaleonQuestETHBCNVoucher: voucherType,
			},
			primaryType: "RaleonQuestETHBCNVoucher",
			domain: domainData,
			message: voucher
		};

		const signature = ethSigUtil.signTypedData_v4(
			Buffer.from(
				RaleonEthBcnReward.contractData.nftMinterPrivateKey, 'hex'
			),
			{data}
		);

		//TODO: do we want to store this on the journey?
		// try {
		// 	await this.updateUserDataValue(transaction.raleonUserId, transaction.questId, 'tokenId', 0);
		// } catch (e) {
		// 	console.error('Error updating NFT id in user data for posterity', e);
		// }
		return {
			...voucher,
			signature
		};

	}

	private getNftJsonUrl(filename: string) {
		const s3Domain = 'raleon-nft-images-prod.s3.amazonaws.com';
		const s3Path = '/'+encodeURIComponent(filename);

		return `https://${s3Domain}${s3Path}`;
	}

	private static contractData = {
		nftMintContractNetwork: 'POLYGON',
		nftMintContractAddress: '******************************************',
		nftMintUrl: 'https://polygon-mainnet.g.alchemy.com/v2/hhbExLbbE5EAwmmyd8IYzk-2Vi5fd9UV',
		nftMintAuthorization: 'hhbExLbbE5EAwmmyd8IYzk-2Vi5fd9UV',
		nftMintContractAbi: JSON.stringify(require('../../../../raleon-standard-nft/artifacts/contracts/RaleonEthBarcelonaNFT.sol/RaleonQuestETHBCN2023.json')),
		nftImageUri: '',
		nftMintContractFunction: 'safeMint',
		nftMinterPublicKey: '******************************************',
		nftMinterPrivateKey: '6e2a2dd283958f74ee54ee6ae0f1c045fa992f24ed62282e3c4a779821e78012'
	};
}
