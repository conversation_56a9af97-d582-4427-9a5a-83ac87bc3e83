import {BindingScope, Context, injectable, inject} from '@loopback/core';
import {RewardGrantReceipt, RewardTransaction} from './reward-transaction.abstract';
import {RewardForbiddenError, RewardConfigData, RewardType, RewardUserData, ConfigSchema, RewardInvalidError, RewardHttpError, RewardBadGatewayError, RewardContext} from './reward-type.abstract';
const fetch = require('node-fetch');
import { Response } from 'node-fetch';

export interface WebhookRewardData extends RewardConfigData {
	webhookUrl: string;
	webhookAuthorization: string;
	method?: string;
}

@injectable({scope: BindingScope.SINGLETON})
export class WebhookRewardType extends RewardType<WebhookRewardData, {}, Response> {
	getFriendlyName(): string {
		return 'Webhook';
	}

	getRewardTypeName(): string {
		return 'webhook-reward-v1';
	}

	getConfigDataSchema(): ConfigSchema<WebhookRewardData> {
		return {
			webhookUrl: {
				configKey: 'webhookUrl',
				type: 'string',
				friendlyName: 'Custom Webhook URL',
				description: 'Custom webhook API endpoint to call upon reward grant'
			},
			webhookAuthorization: {
				configKey: 'webhookAuthorization',
				type: 'string',
				friendlyName: 'Custom Webhook Authorization',
				description: 'Authorization header value'
			},
			method: {
				configKey: 'method',
				type: 'string',
				friendlyName: 'Method',
				description: 'HTTP Method to call webhook with',
				optional: true
			}
		};
	}
	getUserDataSchema(): any {
		return undefined;
	}

	shouldAllowRegrant(): boolean {
		return false;
	}

	async executeTransaction(transaction: RewardTransaction<WebhookRewardData, {}, Response>): Promise<Response> {
		const response = await fetch(transaction.configData.webhookUrl, {
			method: transaction.configData.method || 'POST',
			headers: {
				"Authorization": transaction.configData.webhookAuthorization
			}
		});

		if (response.code < 200 || response.code >= 300) {
			console.error('Webhook response status: ', response.status);
			try {
				throw new RewardBadGatewayError(await response.text());
			} catch (e) {
				throw new RewardBadGatewayError('External reward granting service result was not successful');
			}
		}

		return response;
	}

	protected async arePrerequisitesMet(
		context: RewardContext<WebhookRewardData>
	): Promise<true|Error> {
		try {
			new URL(context.configData.webhookUrl);
		} catch (e) {
			return new RewardInvalidError('Webhook URL invalid, reward not grantable');
		}

		// Send OPTIONS request to endpoint to pre-verify?

		return true;
	}

}
