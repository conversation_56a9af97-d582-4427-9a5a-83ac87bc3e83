import {injectable, BindingScope, service} from '@loopback/core';
import {repository} from '@loopback/repository';
import {Journey, JourneyWithRelations} from '../../../models';
import {JourneyRepository} from '../../../repositories';
import {RaleonUserService} from '../../raleon-user.service';
import {RewardGrantReceipt, RewardTransaction} from './reward-transaction.abstract';
import {RewardConfigData, RewardUserData} from './reward-type.abstract';


@injectable({scope: BindingScope.SINGLETON})
export class RewardTransactionManager {
	constructor(
		@repository(JourneyRepository)
		private journeyRepository: JourneyRepository,
		@service(RaleonUserService)
		private userService: RaleonUserService
	) {}

	async getImmutableTransaction(rewardTypeName: string, raleonUserId: number, questId: number, includeError = true): Promise<Readonly<RewardTransaction>|undefined> {
		const journey = await this.getUserJourney(raleonUserId, questId);
		const transactionState = journey?.rewardTransactionStates?.[rewardTypeName];

		if (!transactionState) {
			return transactionState;
		}

		return Object.freeze(this.createMutableTransaction(
			transactionState?.rewardType,
			transactionState?.raleonUserId,
			transactionState?.questId,
			transactionState?.configData,
			transactionState?.userData,
			transactionState?.walletAddress,
			transactionState,
			includeError
		));
	}

	async getMutableTransaction(
		rewardTypeName: string,
		raleonUserId: number,
		questId: number,
		includeError = true
	): Promise<RewardTransaction|undefined> {
		const journey = await this.getUserJourney(raleonUserId, questId);
		const transactionState = journey?.rewardTransactionStates?.[rewardTypeName];

		if (!transactionState) {
			return transactionState;
		}

		return this.createMutableTransaction(
			transactionState?.rewardType,
			transactionState?.raleonUserId,
			transactionState?.questId,
			transactionState?.configData,
			transactionState?.userData,
			transactionState?.walletAddress,
			transactionState,
			includeError,
			false
		);
	}

	async storeTransaction(transactionState: RewardTransaction): Promise<RewardTransaction|undefined> {
		const { rewardType, raleonUserId, questId } = transactionState;

		const status = transactionState.granted ? 'completed' : 'running';
		const journey = await this.userService.findOrCreateJourney(transactionState.questId, transactionState.raleonUserId, status);

		journey!.rewardTransactionStates = journey!.rewardTransactionStates || {};

		journey!.rewardTransactionStates[rewardType] = transactionState;

		/*if (transactionState.granted) {
			journey!.status = 'completed';
		}*/

		await this.updateUserJourney(raleonUserId, questId, journey!);

		return transactionState;
	}

	createMutableTransaction<
		ConfigDataType extends RewardConfigData = RewardConfigData,
		UserDataType extends RewardUserData = RewardUserData,
		ReceiptDataType extends RewardGrantReceipt = RewardGrantReceipt
	>(
		rewardType: string,
		raleonUserId: number,
		questId: number,
		configData: ConfigDataType,
		userData: UserDataType,
		walletAddress?: string,
		state?: RewardTransaction<ConfigDataType, UserDataType, ReceiptDataType>,
		includeError = true,
		seal = true,
	): RewardTransaction<ConfigDataType, UserDataType, ReceiptDataType> {
		if (state) {
			if (
				state.rewardType !== rewardType ||
				state.raleonUserId !== raleonUserId ||
				state.questId !== questId
			) {
				throw new Error('State mismatch. Reward transaction state did not match reward details provided');
			}
		}

		let sealedState: RewardTransaction<ConfigDataType, UserDataType, ReceiptDataType>;
		const temporaryState: RewardTransaction<any, any, any> = {
			rewardType,
			raleonUserId,
			walletAddress,
			questId,
			configData,
			userData,

			executionSequenceCounter: 0,
			executed: false,
			executionTimeMs: undefined,

			granted: false,
			grantTimeMs: undefined,

			grantFailureReason: undefined,
			grantFailureTimeMs: undefined,

			...(state||{}),

			grantResolver: () => undefined,
			grantRejecter: () => undefined,

			grantPromise: undefined as any,

			receipt: state && state.receipt ? Object.freeze({...state.receipt}) : undefined
		};
		temporaryState.grantPromise = temporaryState.executed
			? temporaryState.grantFailureReason
				? includeError ? Promise.reject(temporaryState.grantFailureReason) : undefined as any
				: Promise.resolve(temporaryState.receipt)
			: new Promise<ReceiptDataType>((resolve, reject) => {
				temporaryState.grantResolver = (receipt: ReceiptDataType) => {
					sealedState.granted = true;
					sealedState.grantTimeMs = Date.now();
					sealedState.receipt = Object.freeze({...receipt});
					this.storeTransaction(sealedState as RewardTransaction<any, any, any>);
					resolve(receipt);
				}
				temporaryState.grantRejecter = (grantFailureReason: any) => {
					sealedState.granted = false;
					sealedState.grantFailureReason = grantFailureReason;
					sealedState.grantFailureTimeMs = Date.now();
					this.storeTransaction(sealedState as RewardTransaction<any, any, any>);
					reject(grantFailureReason);
				}
			});

		Object.defineProperties(temporaryState, {
			'rewardType': { value: rewardType, writable: false, configurable: false, enumerable: true },
			'raleonUserId': { value: raleonUserId, writable: false, configurable: false, enumerable: true },
			'questId': { value: questId, writable: false, configurable: false, enumerable: true },
			'configData': { value: Object.freeze({...configData}), writable: false, configurable: false, enumerable: true },
			'userData': { value: Object.freeze({...userData}), writable: false, configurable: false, enumerable: true }
		});

		if (!seal) return temporaryState;

		sealedState = Object.seal(temporaryState);

		return sealedState;
	}


	private async getUserJourney(
		raleonUserId: number,
		questId: number
	): Promise<(Journey & JourneyWithRelations) | null> {
		return this.journeyRepository.findOne({
			where: {
			  raleonUserId,
			  questId,
			}
		  });
	}

	private async updateUserJourney(
		raleonUserId: number,
		questId: number,
		journey: Journey
	): Promise<void> {
		return this.journeyRepository.update(journey);
	}
}
