import {Context, inject, injectable, BindingScope, service} from '@loopback/core';
import {RewardGrantReceipt, RewardTransaction} from './reward-transaction.abstract';
import {ConfigSchema, RewardBadRequestError, RewardConfigData, RewardForbiddenError, RewardType, RewardUserData} from './reward-type.abstract';
const fetch = require('node-fetch');
import { Response } from 'node-fetch';
import {getURL} from '../../../controllers';
import {JourneyEvent} from '../../../models';
import {RaleonUserService} from '../../raleon-user.service';
import {CustomEventRewardConfigData, CustomEventRewardType} from './custom-event-reward-type.service';
const UTM_API = 'z8l1oxcyk6.execute-api.us-east-1.amazonaws.com'

@injectable({scope: BindingScope.SINGLETON})
export class ReserveListRewardType extends CustomEventRewardType {
	getFriendlyName(): string {
		return 'Reserve List';
	}

	getRewardTypeName(): string {
		return 'reserve-list-reward-v1';
	}

	getConfigDataSchema(): ConfigSchema<CustomEventRewardConfigData> {
		return {
			eventName: {
				configKey: 'eventName',
				type: 'string',
				friendlyName: 'Reserve List Custom Event Type',
				description: 'Custom event to emit upon granting reward, used to build reserve list audience',
				optional: true,
				defaultValueTemplate: 'reserve-list-%%CAMPAIGN_NAME%%'
			}
		};
	}
	getUserDataSchema(): any {
		return undefined;
	}

	protected async arePrerequisitesMet(): Promise<true | Error> {
		return true;
	}
}
