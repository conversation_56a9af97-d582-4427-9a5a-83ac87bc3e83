import {Context, inject, injectable, BindingScope, service} from '@loopback/core';
import {RewardGrantReceipt, RewardTransaction} from './reward-transaction.abstract';
import {ConfigSchema, RewardConfigData, RewardContext, RewardType } from './reward-type.abstract';
const UTM_API = 'j0v36abmdj.execute-api.us-east-1.amazonaws.com';

export interface XpRewardConfigData extends RewardConfigData {
	amount: number;
}

@injectable({scope: BindingScope.SINGLETON})
export class XpRewardType extends RewardType<XpRewardConfigData> {

	getFriendlyName(): string {
		return 'XP / Points';
	}

	getRewardTypeName(): string {
		return 'award-xp';
	}

	getConfigDataSchema(): ConfigSchema<XpRewardConfigData> {
		return {
			amount: {
				configKey: 'amount',
				inputType: 'number',
				type: 'number',
				friendlyName: 'Amount of XP',
				description: 'Award XP',
			}
		};
	}
	getUserDataSchema(): any {
		return undefined;
	}

	shouldAllowRegrant(): boolean {
		return false;
	}

	async executeTransaction(
		transaction: RewardTransaction<XpRewardConfigData>,
		context: RewardContext<XpRewardConfigData>
	): Promise<RewardGrantReceipt> {
		return {};
	}

	protected shouldReturnExistingReceipt(): boolean {
		return false;
	}

	protected async arePrerequisitesMet(): Promise<true | Error> {
		return true;
	}
}
