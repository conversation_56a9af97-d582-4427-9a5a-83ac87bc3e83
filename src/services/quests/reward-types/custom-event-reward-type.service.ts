import {Context, inject, injectable, BindingScope, service} from '@loopback/core';
import {RewardGrantReceipt, RewardTransaction} from './reward-transaction.abstract';
import {ConfigSchema, RewardBadRequestError, RewardConfigData, RewardContext, RewardForbiddenError, RewardHttpError, RewardInvalidError, RewardType, RewardUserData} from './reward-type.abstract';
const fetch = require('node-fetch');
import { Response } from 'node-fetch';
import {getURL} from '../../../controllers';
import {JourneyEvent} from '../../../models';
import {RaleonUserService} from '../../raleon-user.service';
import {uuid} from 'uuidv4';
import {transcode} from 'buffer';
const UTM_API = 'j0v36abmdj.execute-api.us-east-1.amazonaws.com';

export interface CustomEventRewardConfigData extends RewardConfigData {
	eventName: string;
}

@injectable({scope: BindingScope.SINGLETON})
export class CustomEventRewardType extends RewardType<CustomEventRewardConfigData> {

	getFriendlyName(): string {
		return 'Custom Event';
	}

	getRewardTypeName(): string {
		return 'custom-event-reward-v1';
	}

	getConfigDataSchema(): ConfigSchema<CustomEventRewardConfigData> {
		return {
			eventName: {
				configKey: 'eventName',
				type: 'string',
				friendlyName: 'Custom Event Type',
				description: 'Custom event to emit upon granting reward',
				optional: true,
				defaultValueTemplate: `custom-event-%%CAMPAIGN_NAME%%`
			}
		};
	}
	getUserDataSchema(): any {
		return undefined;
	}

	shouldAllowRegrant(): boolean {
		return false;
	}

	async executeTransaction(
		transaction: RewardTransaction<CustomEventRewardConfigData>,
		context: RewardContext<CustomEventRewardConfigData>
	): Promise<RewardGrantReceipt> {
		// publish custom event on behalf of this.raleonUserId for this.questId

		const user = await this.userService.getIdentitiesFromUser(transaction.raleonUserId);
		if (!user) {
			throw new RewardForbiddenError('User identity not found, reward granting forbidden');
		}

		const raleon_id = user.raleonUserIdentities.find(x => x.identityType === 'raleon_id')?.identityValue;
		const address = user.raleonUserIdentities.find(x =>
			x.identityType === 'address' &&
			(!transaction.walletAddress || x.identityValue.toLowerCase() === transaction.walletAddress.toLowerCase())
		)?.identityValue;

		if (!address) {
			throw new RewardForbiddenError('User identity not associated with a blockchain address, reward not grantable');
		}

		const eventType = transaction.configData.eventName;
		if (!eventType) {
			throw new RewardInvalidError('Custom event type was not valid for reward, not grantale');
		}

		const journeyEvent = new JourneyEvent({
			event_type: eventType,
			raleon_id,
			network: 'ETH',
			org_id: context.orgId.toString(),
			address,
			data: JSON.stringify({
				...transaction,
				configData: undefined,
				userData: undefined
			}),
			data_type: 'json',
		});

		await this.forwardEvent(journeyEvent);

		return { eventTime: Date.now() };
	}

	private async forwardEvent(journeyEvent: JourneyEvent): Promise<Response> {
		const url = `/api/event`;
		const signedRequest = getURL(url, 'POST', journeyEvent, UTM_API);
		console.log(signedRequest);
		const response = await fetch(`https://${UTM_API}${url}`, signedRequest);

		if (!response.ok || response.status < 200 || response.status > 299) {
			console.error('Reward response status: ', response.status);
			try {
				console.error(await response.text());
			} catch (e) {

			}
			throw new Error('Internal error occurred while granting reward');
		}

		return response.json();
	}


	protected async arePrerequisitesMet(): Promise<true | Error> {
		return true;
	}
}
