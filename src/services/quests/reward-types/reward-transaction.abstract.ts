import {Context, service} from '@loopback/core';
import {RewardTransactionManager} from './reward-transaction-manager.service';
import {RewardUserData, RewardConfigData, RewardType} from './reward-type.abstract';

export type RewardTypeClass = typeof RewardType<any, any>;
export class RewardGrantReceipt {}
export interface RewardTransaction<
	ConfigDataType extends RewardConfigData = RewardConfigData,
	UserDataType extends RewardUserData = RewardUserData,
	ReceiptDataType extends RewardGrantReceipt = RewardGrantReceipt
> {
	readonly rewardType: string;
	readonly raleonUserId: number;
	readonly walletAddress?: string;
	readonly questId: number;

	readonly configData: Readonly<ConfigDataType>;
	readonly userData: Readonly<UserDataType>;

	executionSequenceCounter: number;
	executed: boolean;
	executionTimeMs: number|undefined;

	grantResolver: (receipt: ReceiptDataType) => void;
	grantRejecter: (grantFailureReason: any) => void;
	grantPromise: Promise<ReceiptDataType>;
	receipt: ReceiptDataType|undefined;

	grantFailureReason: any;
	grantFailureTimeMs: number|undefined;

	granted: boolean;
	grantTimeMs: number|undefined;

	transactionHash?: string|undefined;
}
