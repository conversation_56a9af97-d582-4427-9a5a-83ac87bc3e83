import {service} from '@loopback/core';
import {repository} from '@loopback/repository';
import {Goal, GoalWithRelations, Journey, JourneyWithRelations, Quest, QuestWithRelations, Reward, RewardWithRelations, WalletOverview} from '../../../models';
import {GoalRepository, JourneyRepository, QuestRepository, RewardRepository} from '../../../repositories';
import {GoalType, IsGoalCompleteObjectResult, IsGoalCompleteSchema} from '../../../services/quests/goal-types/goal-type.abstract';
import {RewardTransactionManager} from './reward-transaction-manager.service';
import {RewardGrantReceipt, RewardTransaction} from './reward-transaction.abstract';
import {HttpErrors} from '@loopback/rest';
import {FollowOnTwitter} from '../../../services/quests/goal-types/follow-on-twitter.service';
import {HasCustomEvent} from '../../../services/quests/goal-types/has-custom-event.service';
import {JoinDiscordServer} from '../goal-types/join-discord-server.service';
import {HasTokenAction} from '../goal-types/has-token-action.service';
import {RaleonUserService} from '../../raleon-user.service';
import {DistinctEventCount} from '../goal-types/distinct-event-count.service';

export interface RewardConfigData {}
export interface RewardUserData {}
export class RewardInvalidError extends HttpErrors.InternalServerError {}
export class RewardBadRequestError extends HttpErrors.BadRequest {}
export class RewardForbiddenError extends HttpErrors.Forbidden {}
export class RewardBadGatewayError extends HttpErrors.BadGateway {}
export class RewardNotFoundError extends HttpErrors.NotFound {}
export class RewardHttpError extends Error {
	constructor(message: string, public statusCode: number) {
	  super(message);
	}
  }

export type ConfigSchema<ConfigDataType extends RewardConfigData> = {
	[configKey in keyof ConfigDataType]: {
		type: 'string'|'number'|'boolean',
		inputType?: 'text'|'number'|'checkbox'|'file'|'uri',
		configKey: configKey,
		friendlyName: string,
		description?: string,
		icon?: string,
		optional?: boolean,
		omitFromClient?: boolean,
		omitFromBuilder?: boolean,
		defaultValue?: ConfigDataType[configKey];
		defaultValueTemplate?: string;

		fileUploadEndpoint?: string;
		fileUploadMethod?: 'POST'|'PUT'|'PATCH';
	}
}

export type DataSchema<UserDataType extends RewardUserData> = {
	[dataKey in keyof UserDataType]: {
		type: 'string'|'number'|'boolean',
		dataKey: dataKey,
		clientExperienceType?: string,
		friendlyName: string,
		description?: string,
		icon?: string,
		optional?: boolean,
		defaultValue?: UserDataType[dataKey]
	}
}

export interface RewardContext<
	ConfigDataType extends RewardConfigData = RewardConfigData,
	UserDataType extends RewardUserData = RewardUserData
> {
	raleonUserId: number,
	walletAddress?: string,
	orgId: number,
	questId: number,
	quest: Quest & QuestWithRelations,
	reward: Reward & RewardWithRelations,
	configData: ConfigDataType,
	originalConfigData: ConfigDataType,
	userData: UserDataType,
	originalUserData: UserDataType,
	journey: Journey
}

export abstract class RewardType<
	ConfigDataType extends RewardConfigData = RewardConfigData,
	UserDataType extends RewardUserData = RewardUserData,
	ReceiptDataType extends RewardGrantReceipt = RewardGrantReceipt
> {
	abstract getFriendlyName(): string;
	abstract getRewardTypeName(): string;
	getRewardReceiptTypeName(): string|undefined {
		return undefined;
	}

	abstract getConfigDataSchema(): ConfigSchema<ConfigDataType>;
	abstract getUserDataSchema(): DataSchema<UserDataType>;
	abstract shouldAllowRegrant(
		previousTransaction: RewardTransaction<ConfigDataType, UserDataType, ReceiptDataType>,
		context: RewardContext<ConfigDataType, UserDataType>
	): boolean;

	getConfigDataTemplateIdentifyingConfigKey(): keyof ConfigDataType | undefined {
		return undefined;
	}

	getConfigDataTemplates(): Array<ConfigDataType> {
		return [];
	}

	getConfigDataSchemaForClient(): ConfigSchema<ConfigDataType> {
		const schema =  {...this.getConfigDataSchema()};

		for (const key of Object.keys(schema)) {
			const configKey = key as keyof ConfigSchema<ConfigDataType>;
			const configItem = schema[configKey];

			if (configItem.omitFromClient) {
				schema[configKey] = undefined as any;
				delete schema[configKey];
			}
		}

		return schema;

	}

	getConfigDataSchemaForBuilder(): ConfigSchema<ConfigDataType> {
		const schema =  {...this.getConfigDataSchema()};

		for (const key of Object.keys(schema)) {
			const configKey = key as keyof ConfigSchema<ConfigDataType>;
			const configItem = schema[configKey];

			if (configItem.omitFromBuilder) {
				schema[configKey] = undefined as any;
				delete schema[configKey];
			}
		}

		return schema;
	}

	constructor(
	  @repository(JourneyRepository)
	  protected journeyRepository: JourneyRepository,
	  @repository(QuestRepository)
	  protected questRepository: QuestRepository,
	  @service(RewardTransactionManager)
	  private transactionManager: RewardTransactionManager,
	  @service(FollowOnTwitter)
	  private followOnTwitterGoal: FollowOnTwitter,
	  @service(JoinDiscordServer)
	  private joinDiscordServerGoal: JoinDiscordServer,
	  @service(HasCustomEvent)
	  private hasCustomEventGoal: HasCustomEvent,
	  @service(HasTokenAction)
	  private hasTokenAction: HasTokenAction,
	  @service(DistinctEventCount)
	  private distinctEventCount: DistinctEventCount,
	  @repository(RewardRepository)
	  protected rewardRepository: RewardRepository,
	  @service(RaleonUserService)
	  protected userService: RaleonUserService
	) {}

	async hasGranted(raleonUserId: number, questId: number): Promise<boolean> {
		const existingTransaction = await this.getTransaction(raleonUserId, questId);
		if (!existingTransaction) {
			return false;
		}

		return existingTransaction.granted;
	}

	async assertGrantable(
		raleonUserId: number,
		questId: number,
		walletAddress?: string,
		context?: RewardContext<ConfigDataType, UserDataType>,
		existingTransaction?: RewardTransaction<ConfigDataType, UserDataType, ReceiptDataType>,
	): Promise<void> {
		if (!context) {
			const internalContext = await this.getRewardContext(raleonUserId, questId, walletAddress);
			if (internalContext instanceof Error) {
				throw context;
			}
			context = internalContext;
		}

		const result = await this.isGrantableOrError(context, existingTransaction);
		if (result instanceof Error) {
			throw result;
		}
	}

	async isGrantable(
		raleonUserId: number,
		questId: number,
		walletAddress?: string,
		context?: RewardContext<ConfigDataType, UserDataType>,
		existingTransaction?: RewardTransaction<ConfigDataType, UserDataType, ReceiptDataType>
	): Promise<boolean> {
		if (!context) {
			const internalContext = await this.getRewardContext(raleonUserId, questId, walletAddress);
			if (internalContext instanceof Error) {
				return false;
			}
			context = internalContext;
		}

		const result = await this.isGrantableOrError(context, existingTransaction);

		return result === true;
	}

	private async isGrantableOrError(
		context: RewardContext<ConfigDataType, UserDataType>,
		existingTransaction?: RewardTransaction<ConfigDataType, UserDataType, ReceiptDataType>
	): Promise<true | Error> {
		const prerequisitesResult = await this.arePrerequisitesMet(context, existingTransaction);
		if (!prerequisitesResult || prerequisitesResult instanceof Error) {
			if (prerequisitesResult instanceof Error) {
				return prerequisitesResult;
			}

 			return new RewardForbiddenError('Reward prerequisites not completed, reward not grantable');
		}

		if (existingTransaction?.executed && (existingTransaction?.granted || !existingTransaction?.grantFailureTimeMs) && !this.shouldAllowRegrant(existingTransaction, context)) {
			return new RewardForbiddenError('Reward grant already executed, re-granting not permitted');
		}


		return true;
	}

	async requestGrant(
		raleonUserId: number,
		questId: number,
		walletAddress?: string,
		regrant?: boolean,
		userDataUpdates?: Partial<UserDataType>
	): Promise<ReceiptDataType> {
		if (userDataUpdates) {
			await this.updateUserDataValues(raleonUserId, questId, userDataUpdates);
		}

		const existingTransaction = await this.getTransaction(raleonUserId, questId, false);
		if (!existingTransaction?.granted || !regrant) {
			if (existingTransaction?.granted) {
				if (!this.shouldReturnExistingReceipt()) {
					return undefined as any;
				}

				return existingTransaction.receipt!;
			} else if (existingTransaction?.executed && !existingTransaction?.grantFailureTimeMs) {
				return existingTransaction.grantPromise;
			}
		}

		const context = await this.getRewardContext(raleonUserId, questId, walletAddress);
		if (context instanceof Error) {
			throw context;
		}

		await this.assertGrantable(raleonUserId, questId, walletAddress, context, existingTransaction);
		const newTransaction = this.transactionManager.createMutableTransaction<ConfigDataType, UserDataType, ReceiptDataType>(
			this.getRewardTypeName(),
			raleonUserId,
			questId,
			context.configData,
			context.userData,
			walletAddress
		);
		if (existingTransaction) {
			newTransaction.executionSequenceCounter = existingTransaction.executionSequenceCounter;
		}

		return this.execute(newTransaction, context);
	}

	async getTransaction(
		raleonUserId: number,
		questId: number,
		includeError = true
	): Promise<Readonly<RewardTransaction<ConfigDataType, UserDataType, ReceiptDataType> | undefined>> {
		return Object.freeze(this.getImmutableTransaction(this.getRewardTypeName(), raleonUserId, questId, includeError));
	}

	async addTransactionHash(
		raleonUserId: number,
		questId: number,
		transactionHash: string,
	) : Promise<RewardTransaction | undefined> {
		const existingTransaction = await this.transactionManager.getMutableTransaction(
			this.getRewardTypeName(),
			raleonUserId,
			questId,
			true
		);
		if (!existingTransaction) {
			throw new HttpErrors.NotFound('Reward transaction not found');
		}
		existingTransaction.transactionHash = transactionHash;
		return await this.transactionManager.storeTransaction(existingTransaction as RewardTransaction<any, any, any>);
	}

	protected abstract executeTransaction(
		transaction: RewardTransaction<ConfigDataType, UserDataType, ReceiptDataType>,
		context: RewardContext<ConfigDataType, UserDataType>
	): Promise<ReceiptDataType>;

	protected shouldReturnExistingReceipt(): boolean {
		return true;
	}

	protected async execute(
		transaction: RewardTransaction<ConfigDataType, UserDataType, ReceiptDataType>,
		context: RewardContext<ConfigDataType, UserDataType>
	): Promise<ReceiptDataType> {
		if (transaction.executed) {
			return transaction.grantPromise;
		}

		transaction.executed = true;
		transaction.executionTimeMs = Date.now();
		transaction.executionSequenceCounter++;
		await this.transactionManager.storeTransaction(transaction as RewardTransaction<any, any, any>);
		this.executeTransaction(transaction, context).then(transaction.grantResolver).catch(transaction.grantRejecter);

		return transaction.grantPromise;
	}

	// TODO: this needs to cache, sort of like transaction (on user journey or user-journey-reward)
	protected abstract arePrerequisitesMet(
		context: RewardContext<ConfigDataType, UserDataType>,
		previousTransaction?: RewardTransaction<ConfigDataType, UserDataType, ReceiptDataType>
	): Promise<true | Error>

	private async getImmutableTransaction(
		rewardType: string,
		raleonUserId: number,
		questId: number,
		includeError: boolean
	): Promise<RewardTransaction<ConfigDataType, UserDataType, ReceiptDataType> | undefined> {
		return this.transactionManager.getImmutableTransaction(rewardType, raleonUserId, questId, includeError) as Promise<RewardTransaction<ConfigDataType, UserDataType, ReceiptDataType> | undefined>;
	}

	private async resolveUserData(
		context: RewardContext<ConfigDataType, UserDataType>
	): Promise<UserDataType> {
		const resolvedData = { ...(context.userData || {}) };
		const schema = this.getUserDataSchema() || {};

		for (const key of Object.keys(schema)) {
			const userDataKey = key as keyof UserDataType;
			const schemaValue = schema[userDataKey];

			if (!resolvedData.hasOwnProperty(userDataKey)) {
				resolvedData[userDataKey] = undefined as any;
			}

			if (schemaValue.defaultValue && (resolvedData[userDataKey] === undefined || resolvedData[userDataKey] === null)) {
				resolvedData[userDataKey] = schemaValue.defaultValue;
			}
		}

		return resolvedData;
	}

	private async resolveConfigData(
		context: RewardContext<ConfigDataType, UserDataType>
	): Promise<ConfigDataType> {
		const resolvedData = { ...(context.configData || {}) };
		const schema = this.getConfigDataSchema() || {};

		const contextTemplateItems = {
			CAMPAIGN_NAME: context.quest?.campaign?.name,
			CAMPAIGN_ID: context.quest?.campaign?.id,
			QUEST_ID: context.questId,
			ORG_ID: context.orgId,
			RALEON_USER_ID: context.raleonUserId
		};

		for (const key of Object.keys(schema)) {
			const configKey = key as keyof ConfigDataType;
			const schemaValue = schema[configKey];

			if (!resolvedData.hasOwnProperty(configKey)) {
				resolvedData[configKey] = undefined as any;
			}

			if (resolvedData[configKey] === undefined || resolvedData[configKey] === null) {
				if (schemaValue.defaultValueTemplate) {
					resolvedData[configKey] = schemaValue.defaultValueTemplate as any;
				}

				if (schemaValue.defaultValue) {
					resolvedData[configKey] = schemaValue.defaultValue;
				}

				const templateConfigKey = this.getConfigDataTemplateIdentifyingConfigKey();
				if (templateConfigKey) {
					const templates = this.getConfigDataTemplates();
					const matchingTemplate = templates?.find(x => x[templateConfigKey] === resolvedData[templateConfigKey]);

					if (matchingTemplate) {
						resolvedData[configKey] = matchingTemplate[configKey];
					}
				}
			}

			const value = resolvedData[configKey];
			//@ts-ignore
			if (typeof value === 'string' && value.includes('%%')) {
				const resolvedValue = value
					//@ts-ignore
					.replace(/%%(.*?)%%/, (substring, key) => contextTemplateItems.hasOwnProperty(key) ? (contextTemplateItems as any)[key] : substring)
					//@ts-ignore
					.replace(/%%##(.*?)##%%/, (substring, key) => context.userData.hasOwnProperty(key) ? (context.userData as any)[key] : substring);

				if (schemaValue.type === 'string') {
					(resolvedData as any)[configKey] = resolvedValue;
				} else {
					(resolvedData as any)[configKey] = JSON.parse(resolvedValue);
				}
			}
		}

		return resolvedData;
	}

	private async getQuestData(
		questId: number
	): Promise<Quest & QuestWithRelations | null> {
		return this.questRepository.findOne({
			where: {
			  id: questId
			},
			include: ['campaign', 'goals', 'rewards']
		  });
	}

	private async getRewardContext(
		raleonUserId: number,
		questId: number,
		walletAddress?: string
	): Promise<RewardContext<ConfigDataType, UserDataType> | Error> {
		const quest = await this.getQuestData(questId);
		if (!quest) {
			return new RewardNotFoundError('Quest not found, so reward can not be located');
		}

		const [reward, journey] = await Promise.all([
			this.getRewardData(quest),
			this.userService.findOrCreateJourney(questId, raleonUserId)
		]);

		if (!reward) {
			return new RewardForbiddenError('Quest does not contain this reward type, forbidden');
		}
		if (!journey) {
			return new RewardForbiddenError('User does not have a journey for this quest, forbidden');
		}

		const configData = (reward?.configData || {}) as ConfigDataType;
		const userData = await this.getUserData(journey) || {};

		const context = {
			raleonUserId,
			questId,
			orgId: quest.campaign.orgId,
			quest,
			reward,
			configData,
			originalConfigData: configData,
			userData,
			originalUserData: userData,
			journey,
			walletAddress
		};

		try {
			const goalCompletionStatus = await this.getGoalCompletionStatus(context);
			if (goalCompletionStatus instanceof Error) {
				return goalCompletionStatus;
			}
		} catch (e) {
			console.error('Goal check error', e);

			return new RewardForbiddenError('Goal completion could not be verified, unable to grant');
		}

		const resolvedData = await this.resolveUserData(context);
		context.userData = resolvedData;

		const resolvedConfig = await this.resolveConfigData(context);
		context.configData = resolvedConfig;


		return context;
	}

	private async getRewardData(
		quest: Quest & QuestWithRelations
	): Promise<(Reward & RewardWithRelations) | undefined> {
		return quest?.rewards?.find(x => x.type === this.getRewardTypeName());
	}

	private async getUserData(
		journey: Journey
	): Promise<UserDataType> {
		return journey?.rewardUserData?.[this.getRewardTypeName()] as UserDataType;
	}


	private async getUserGoalData(
		raleonUserId: number,
		questId: number,
	): Promise<{ [goalTypeName: string]: any} | undefined> {
		const journey = await this.userService.findOrCreateJourney(questId, raleonUserId);

		return journey?.goalUserData;
	}

	protected async updateUserDataValue<Z extends keyof UserDataType>(
		raleonUserId: number,
		questId: number,
		userDataKey: Z,
		userDataValue: UserDataType[Z]
	): Promise<void> {
		return this.updateUserDataValues(
			raleonUserId,
			questId,
			{ [userDataKey]: userDataValue } as any
		);
	}


	protected async updateUserDataValues(
		raleonUserId: number,
		questId: number,
		userData: Partial<UserDataType>
	): Promise<void> {
		const journey = await this.userService.findOrCreateJourney(questId, raleonUserId);
		if (!journey) {
			throw new Error('No journey');
		}

		journey.rewardUserData = journey.rewardUserData || {};
		journey.rewardUserData[this.getRewardTypeName()] = journey.rewardUserData[this.getRewardTypeName()] || {};

		const data = journey.rewardUserData[this.getRewardTypeName()] as UserDataType;
		(Object.keys(userData) as Array<keyof UserDataType>).forEach(key => data[key] = userData[key] as any);

		await this.journeyRepository.update(journey);
	}



	private async getGoalCompletionStatus(
		context: RewardContext<ConfigDataType, UserDataType>
	): Promise<true | Error> {
		const allGoalTypes = [
			this.followOnTwitterGoal,
			this.joinDiscordServerGoal,
			this.hasCustomEventGoal,
			this.hasTokenAction,
			this.distinctEventCount
		] as Array<GoalType>;

		const questGoalData = (context.quest.goals || []); // await this.getGoalsData(questId);
		const questGoalTypeNames = questGoalData.map(x => x.type);

		const questGoalTypes = allGoalTypes.filter(x =>
			questGoalTypeNames.includes(x.getGoalTypeName())
		);

		const questGoalDataTypeSet = new Set(questGoalData.map(x => x.type));
		if (questGoalTypes.length < questGoalDataTypeSet.size) {
			return new RewardInvalidError('Corresponding GoalType services could not be found for all goals in the quest, therefore goal completion could not be determined, and reward is not grantable');
		}

		// and also gather user-specific goal data
		const questGoalUserData = await this.getUserGoalData(context.raleonUserId, context.quest.id!) || {};

		const goalCompletionResults = await Promise.all(questGoalData.map(data => {
			const type = questGoalTypes.find(x => data.type === x.getGoalTypeName());
			if (!type) {
				return new RewardInvalidError('Corresponding GoalType services could not be found for all goals in the quest, therefore goal completion could not be determined, and reward is not grantable');
			}

			return type.isComplete({
				raleonUserId: context.raleonUserId,
				walletAddress: context.walletAddress,
				orgId: context.quest.campaign.orgId,
				goalId: data.id!,
				questId: context.quest.id!,
				configData: data.requiredData,
				userData: questGoalUserData[data.id!],
			} as IsGoalCompleteSchema)
		}));

		const allComplete = goalCompletionResults.every(x => {
			if (typeof x === 'boolean') {
				return x;
			} else if (this.isIsGoalCompleteObjectResult(x)) {
				return x.isComplete;
			}
		});

		if (!allComplete) {
			return new RewardForbiddenError('Quest goal completion not achieved, reward not grantable');
		}

		return true;
	}

	private isIsGoalCompleteObjectResult(result: any): result is IsGoalCompleteObjectResult {
		return result && result.isComplete !== undefined && result.totalCompleted !== undefined;
	}

}
