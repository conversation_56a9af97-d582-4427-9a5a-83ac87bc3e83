import {BindingScope, injectable} from '@loopback/core';
import {getURL} from '../../../utils/utils';
const fetch = require('node-fetch');
import {ConfigSchema, ContentConfigSchema, GoalConfigData, GoalType, IsGoalCompleteObjectResult, IsGoalCompleteSchema} from './goal-type.abstract';

export const GOAL_API = 'bcie42aco8.execute-api.us-east-1.amazonaws.com';

export interface DistinctEventCountDataSchema extends GoalConfigData {
	eventNames: string[];
	eventCount: number;
}
@injectable({scope: BindingScope.SINGLETON})
export class DistinctEventCount extends GoalType<DistinctEventCountDataSchema> {

	getGoalTypeName(): string {
		return DistinctEventCount.GOAL_TYPE_NAME;
	}

	getFriendlyName(): string {
		return DistinctEventCount.FRIENDLY_NAME;
	}

	getConfigDataSchema(): ConfigSchema<DistinctEventCountDataSchema> {
		return {
			eventNames: {
				type: 'string',
				friendlyName: 'Event Names',
				description: 'The names of the events to the user has to complete',
			},
			eventCount: {
				type: 'number',
				friendlyName: 'Event Count',
				description: 'The number of distinct events from Event Names that the user has to complete',
			}
		};
	}

	getContentConfigSchema(): ContentConfigSchema {
		return {}
	}

	getUserDataSchema(): any {
		return {
			eventCount: 'number'
		}
	}

	async isGoalComplete(data: IsGoalCompleteSchema): Promise<IsGoalCompleteObjectResult> {
		if (!data || !data.walletAddress || !data.orgId || !data.configData) {
			throw new Error('Wallet address, org id, and token address are required');
		}

		const configData = data.configData as ConfigSchema<DistinctEventCountDataSchema>;
		let eventNames = configData.eventNames?.value;
		const eventCount = configData.eventCount?.value;

		if (!eventNames) {
			throw new Error('Event Names are required');
		}
		if (!eventCount) {
			throw new Error('Event Count is required');
		}

		eventNames = eventNames.split(',').map((e: string) => e.trim()).join(',');

		const url = `/dev/goal/custom-event-count` +
			`?address=${data.walletAddress}&orgId=${data.orgId}` +
			`&count=${eventCount}&eventNames=${eventNames};`

		const signedRequest = getURL(url, 'GET', undefined, GOAL_API);
		const response = await fetch(`https://${GOAL_API}${url}`, signedRequest);
		const result = await response.json();
		console.log(`result: ${JSON.stringify(result)}`)
		return {
			isComplete: result.isComplete === true || result.isComplete === 'true',
			totalCompleted: result.totalCompleted || 0,
			totalRequired: parseInt(eventCount),
		};
	}

	static GOAL_TYPE_NAME = 'DistinctEventCount';
	static FRIENDLY_NAME = 'Has Completed X Events';
}
