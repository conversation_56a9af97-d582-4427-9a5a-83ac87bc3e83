import {BindingScope, injectable} from '@loopback/core';
import fetch from 'node-fetch';
import {ConfigSchema, ContentConfigSchema, GoalConfigData, GoalType, IsGoalCompleteSchema} from './goal-type.abstract';


export interface JoinDiscordServerDataSchema extends GoalConfigData {
	discordServerInviteLink: string;
	discordServerId: string;
	discordServerName: string;
}
@injectable({scope: BindingScope.SINGLETON})
export class JoinDiscordServer extends GoalType<JoinDiscordServerDataSchema> {

	getGoalTypeName(): string {
		return JoinDiscordServer.GOAL_TYPE_NAME;
	}

	getFriendlyName(): string {
		return JoinDiscordServer.FRIENDLY_NAME;
	}

	getConfigDataSchema(): ConfigSchema<JoinDiscordServerDataSchema> {
		return {
			discordServerName: {
				type: 'string',
				friendlyName: 'Discord Server Name'
			},
			discordServerInviteLink: {
				type: 'string',
				friendlyName: 'Discord Server Invite Link'
			},
			discordServerId: {
				type: 'string',
				friendlyName: 'Discord Server Id',
				actionDisplayName: 'Join',
			},
		};
	}

	getContentConfigSchema(): ContentConfigSchema {
		return {
			buttonText: {
				type: 'string',
				friendlyName: JoinDiscordServer.FRIENDLY_NAME,
				value: JoinDiscordServer.FRIENDLY_NAME,
				omitFromCB: true,
			},
			buttonUrl: {
				type: 'string',
				friendlyName: 'Discord Server Invite Link',
				value: `https://discord.com/api/oauth2/authorize?client_id=${JoinDiscordServer.CLIENT_ID}&redirect_uri=${JoinDiscordServer.ENCODED_REDIRECT_URI}&response_type=code&scope=identify%20guilds%20guilds.members.read`,
				omitFromCB: true,
			},
			message: {
				type: 'string',
				friendlyName: 'Verification Message',
				value: 'Join our discord server.',
				omitFromCB: true,
			},
		}
	}

	getUserDataSchema(): any {
		return {
			accessToken: 'string',
			refreshToken: 'string',
			goalId: 'string',
			expiresIn: 'number',
		}
	}

	async isGoalComplete(data: IsGoalCompleteSchema): Promise<boolean> {
		const userJourney = await this.journeyRepository.findOne({
			where: {
				raleonUserId: data.raleonUserId,
				questId: data.questId,
			}
		});

		if (!userJourney ||
			!userJourney.goalUserData?.[data.goalId] ||
			!userJourney.goalUserData?.[data.goalId].accessToken
		) {
			return false;
		}

		const response = await this.getDiscordGuilds(
			userJourney.goalUserData?.[data.goalId].accessToken
		);

		const userDiscordGuilds = await response.json();
		console.log(`guilds: ${JSON.stringify(userDiscordGuilds)}`);

		const matchingGuild = userDiscordGuilds.find((x: any) => {
			return x.id === (data.configData as ConfigSchema<JoinDiscordServerDataSchema>).discordServerId?.value;
		});

		try {
			await this.saveDiscordUserData(
				userJourney.goalUserData?.[data.goalId].accessToken,
				data.raleonUserId,
			);
		} catch (e) {
			console.log(`error saving discord user data: ${e}`);
		}

		return !!matchingGuild;
	}

	async getDiscordGuilds(accessToken: string): Promise<any> {
		const response = await fetch(`https://discord.com/api/v10/users/@me/guilds`, {
			headers: {
				'Authorization': `Bearer ${accessToken}`
			}
		});


		if (response.status === 429) {
			console.warn(`Discord rate limited us. Retrying in 300ms`);
			await new Promise(r => setTimeout(r, 300));
			return this.getDiscordGuilds(accessToken);
		}

		return response;
	}

	private async saveDiscordUserData(
		accessToken: string,
		raleonUserId: number,
	): Promise<void> {
		const response = await fetch(`https://discord.com/api/v10/users/@me`, {
			headers: {
				'Authorization': `Bearer ${accessToken}`
			}
		});

		if (response.status === 429) {
			console.warn(`Discord rate limited us. Retrying in 300ms`);
			await new Promise(r => setTimeout(r, 300));
			return await this.saveDiscordUserData(accessToken, raleonUserId);
		}

		const discordUserData = await response.json();
		console.log(`user data: ${JSON.stringify(discordUserData)}`);

		const raleonUser = await this.raleonUserService.getIdentitiesFromUser(raleonUserId);
		let raleon_id = '';
		if (raleonUser && raleonUser.raleonUserIdentities?.length > 0) {
			raleon_id = raleonUser.raleonUserIdentities.find(x => x.identityType === 'raleon_id')?.identityValue || '';
		}

		if (discordUserData && discordUserData.id) {
			let username = discordUserData.username;
			if (discordUserData.username && discordUserData.discriminator && discordUserData.discriminator !== '0') {
				username += `#${discordUserData.discriminator}`;
			}

			const identities = [
				{identityType: 'discord_id', identityValue: discordUserData.id},
				{identityType: 'discord_username', identityValue: username},
				{identityType: 'discord_global_name', identityValue: discordUserData.global_name},
			];

			if (raleon_id) {
				identities.push({identityType: 'raleon_id', identityValue: raleon_id});
			}

			await this.raleonUserService.getUserFromIdentities(identities, raleonUserId);
		}
	}

	static GOAL_TYPE_NAME = 'JoinDiscordServer';
	static FRIENDLY_NAME = 'Join Discord Server';

	//the last values for client id and secret are pointing to MikeRaleon discord app. Need to update to Raleon prod discord app.
	static CLIENT_ID = '1096494427364663458';
	static CLIENT_SECRET = 'ygi6AXv2m5PAiFqnkkj3z1SXtkguTJua';
	static REDIRECT_URI = 'https://app.raleon.io/api/v1/goals/oauth-redirect/discord';
	static ENCODED_REDIRECT_URI = encodeURIComponent(JoinDiscordServer.REDIRECT_URI);
	static OAUTH_TOKEN_URI = 'https://discord.com/api/oauth2/token';
}

