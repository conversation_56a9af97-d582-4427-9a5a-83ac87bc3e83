import {BindingScope, injectable} from '@loopback/core';
import {getURL} from '../../../utils/utils';
const fetch = require('node-fetch');
import {ConfigSchema, GoalType, GoalConfigData, ContentConfigSchema, IsGoalCompleteSchema} from './goal-type.abstract';

export interface HasTokenData extends GoalConfigData {
	tokenAddress: any;
	chainId: any;
}

export const GOAL_API = 'bcie42aco8.execute-api.us-east-1.amazonaws.com';

@injectable({scope: BindingScope.SINGLETON})
export class HasTokenAction extends GoalType<HasTokenData> {

	getGoalTypeName(): string {
		return HasTokenAction.GOAL_TYPE_NAME;
	}

	getFriendlyName(): string {
		return HasTokenAction.FRIENDLY_NAME;
	}

	getConfigDataSchema(): ConfigSchema<HasTokenData> {
		return {
			tokenAddress: {
				type: 'string',
				friendlyName: 'Token Address',
				description: 'Token to track',
			},
			chainId: {
				type: 'string',
				friendlyName: 'Chain Id',
				description: 'Chain id for the token',
			},
		};
	}


	getContentConfigSchema(): ContentConfigSchema {
		return {
			buttonText: {
				type: 'string',
				friendlyName: 'Quest Action Start CTA',
				description: 'The button in the quest summary that describes this action',
			},
			header: {
				type: 'string',
				friendlyName: 'Quest Action Instruction Text',
				description: 'The first message that appears for this quest action. Will contain the button url.',
			},
			buttonUrl: {
				type: 'string',
				friendlyName: 'Quest Action Instruction Link',
				description: 'The url that will be embedded in the action message.',
			},
			message: {
				type: 'string',
				friendlyName: 'Quest Action Verification Text',
				description: 'The second message that appears that tells the user to verify the action.',
			},
		}
	}

	getUserDataSchema(): any {
		return {}
	}

	async isGoalComplete(data: IsGoalCompleteSchema): Promise<boolean> {
		if (!data || !data.walletAddress || !data.orgId || !data.configData) {
			throw new Error('Wallet address, org id, and token address are required');
		}

		const configData = data.configData as ConfigSchema<HasTokenData>;
		const configuredTokenAddress = configData.tokenAddress?.value;
		const configuredChainId = configData.chainId?.value;

		if (!configuredTokenAddress) {
			throw new Error('Token address is required');
		}
		if (!configuredChainId) {
			throw new Error('Chain id is required');
		}

		const url = `/dev/goal/has-token?address=${data.walletAddress}&tokenAddress=${configuredTokenAddress}&orgId=${data.orgId}&chainId=${configuredChainId}`;
		const signedRequest = getURL(url, 'GET', undefined, GOAL_API);
		const response = await fetch(`https://${GOAL_API}${url}`, signedRequest);
		const tokenData = await response.json();

		if (tokenData.error || tokenData.error_code || tokenData.error_message) {
			console.error('Token balance error:', tokenData.error_code, tokenData.error_message);
			throw new Error('Token balance could not be checked');
		}

		const contracts = tokenData.data.items.map((x: any) => x.contract_address.toLowerCase());

		const isComplete = contracts.includes(configuredTokenAddress.toLowerCase());
		console.log('Wallet address has token', data.walletAddress, configuredTokenAddress, isComplete);

		return isComplete;
	}

	static GOAL_TYPE_NAME = 'HasTokenAction';
	static FRIENDLY_NAME = 'Has Token';
}