import {BindingScope, injectable} from '@loopback/core';
import {getURL} from '../../../utils/utils';
const fetch = require('node-fetch');
import {ConfigSchema, GoalType, GoalConfigData, ContentConfigSchema, IsGoalCompleteSchema} from './goal-type.abstract';

export interface HasCustomEventDataSchema extends GoalConfigData {
	eventName: any;
}

export const GOAL_API = 'bcie42aco8.execute-api.us-east-1.amazonaws.com';

@injectable({scope: BindingScope.SINGLETON})
export class HasCustomEvent extends GoalType<HasCustomEventDataSchema> {

	getGoalTypeName(): string {
		return HasCustomEvent.GOAL_TYPE_NAME;
	}

	getFriendlyName(): string {
		return HasCustomEvent.FRIENDLY_NAME;
	}

	getConfigDataSchema(): ConfigSchema<HasCustomEventDataSchema> {
		return {
			eventName: {
				type: 'string',
				friendlyName: 'Custom Event Type',
				description: 'Some custom event description',
			},
		};
	}


	getContentConfigSchema(): ContentConfigSchema {
		return {
			buttonText: {
				type: 'string',
				friendlyName: 'Quest Action Start CTA',
				description: 'The button in the quest summary that describes this action',
			},
			header: {
				type: 'string',
				friendlyName: 'Quest Action Instruction Text',
				description: 'The first message that appears for this quest action. Will contain the button url.',
			},
			buttonUrl: {
				type: 'string',
				friendlyName: 'Quest Action Instruction Link',
				description: 'The url that will be embedded in the action message.',
			},
			message: {
				type: 'string',
				friendlyName: 'Quest Action Verification Text',
				description: 'The second message that appears that tells the user to verify the action.',
			},
		}
	}

	getUserDataSchema(): any {
		return {}
	}

	async isGoalComplete(data: IsGoalCompleteSchema): Promise<boolean> {
		if (!data || !data.walletAddress || !data.orgId || !data.configData) {
			throw new Error('Wallet address, org id, and event name are required');
		}

		const configData = data.configData as ConfigSchema<HasCustomEventDataSchema>;
		const configuredEventName = configData.eventName?.value;

		if (!configuredEventName) {
			throw new Error('Event name is required');
		}

		const url = `/dev/goal/custom-event?address=${data.walletAddress}&eventName=${configuredEventName}&orgId=${data.orgId}`;
		const signedRequest = getURL(url, 'GET', undefined, GOAL_API);
		const response = await fetch(`https://${GOAL_API}${url}`, signedRequest);
		const isComplete = await response.json();
		console.log(`Wallet address ${data.walletAddress}` +
			`has completed custom event ${configuredEventName}: ${isComplete}`
		);
		return isComplete != null;
	}

	static GOAL_TYPE_NAME = 'HasCustomEvent';
	static FRIENDLY_NAME = 'Has Custom Event';
}
