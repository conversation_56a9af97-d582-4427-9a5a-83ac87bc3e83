import { service } from '@loopback/core';
import {IsolationLevel, repository} from '@loopback/repository';
import {Journey} from '../../../models/journey.model';
import {JourneyRepository} from '../../../repositories';
import { RaleonUserService } from '../../raleon-user.service';

export abstract class GoalType<
	ConfigData extends GoalConfigData = GoalConfigData
> {
	constructor(
		@service(RaleonUserService)
		protected raleonUserService: RaleonUserService,
		@repository(JourneyRepository)
		protected journeyRepository: JourneyRepository
	) {}

	abstract getGoalTypeName(): string;
	abstract getFriendlyName(): string;
	abstract getConfigDataSchema(): ConfigSchema<ConfigData>;
	abstract getContentConfigSchema() : ContentConfigSchema;
	abstract getUserDataSchema(): any;
	abstract isGoalComplete(data: IsGoalCompleteSchema): Promise<boolean | IsGoalCompleteObjectResult>;

	async isComplete(data: IsGoalCompleteSchema): Promise<boolean | IsGoalCompleteObjectResult> {
		const cache = await this.isCompletionCached(data)
		if (cache) {
			console.log('Cache hit for ', data.goalId);
			return cache;
		}

		//console.log('cache miss for ', data.goalId);

		if (data.walletAddress) {
			const result = await this.isGoalComplete(data);

			if (result && typeof result == 'boolean') {
				this.cacheCompletion(data);
			} else if (this.isIsGoalCompleteObjectResult(result)) {
				if (result.isComplete) {
					this.cacheCompletionObjectResult(data, result);
				}
			}

			return result;
		}

		//TODO: update this to use all addresses associated with the user
		//and call this.checkCompletion() for each
		const raleonUser = await this.raleonUserService.getIdentitiesFromUser(
			data.raleonUserId
		);
		const walletAddress = raleonUser.raleonUserIdentities.find(
			x => x.identityType === 'address'
		)?.identityValue;

		const result = await this.isGoalComplete({...data, walletAddress});

		if (result && typeof result == 'boolean') {
			this.cacheCompletion(data);
		} else if (this.isIsGoalCompleteObjectResult(result)) {
			if (result.isComplete) {
				this.cacheCompletionObjectResult(data, result);
			}
		}

		return result;
	}

	protected async isCompletionCached(data: IsGoalCompleteSchema): Promise<boolean | IsGoalCompleteObjectResult> {
		const journey = await this.raleonUserService.findOrCreateJourney(data.questId, data.raleonUserId);

		if (!(data.goalId in (journey?.goalCompletionCache || {}))) {
			return false;
		}

		const { cacheTimeMs, complete } = journey?.goalCompletionCache?.[data.goalId]!;
		if ((Date.now() - cacheTimeMs) > GoalType.TTL) {
			return false;
		}

		return complete;
	}

	protected async cacheCompletion(data: IsGoalCompleteSchema): Promise<void> {
		const journey = await this.raleonUserService.findOrCreateJourney(data.questId, data.raleonUserId);

		journey!.goalCompletionCache = journey?.goalCompletionCache || {};
		journey!.goalCompletionCache[data.goalId] = {
			...(journey?.goalCompletionCache[data.goalId] || {}),
			cacheTimeMs: Date.now(),
			complete: true
		};

		await this.journeyRepository.update(journey!);
	}

	protected async cacheCompletionObjectResult(
		data: IsGoalCompleteSchema,
		toCache: IsGoalCompleteObjectResult
	): Promise<void> {
		const journey = await this.raleonUserService.findOrCreateJourney(data.questId, data.raleonUserId);

		journey!.goalCompletionCache = journey?.goalCompletionCache || {};
		journey!.goalCompletionCache[data.goalId] = {
			...(journey?.goalCompletionCache[data.goalId] || {}),
			cacheTimeMs: Date.now(),
			complete: toCache,
		};

		await this.journeyRepository.update(journey!);
	}

	private isIsGoalCompleteObjectResult(result: any): result is IsGoalCompleteObjectResult {
		return result && result.isComplete !== undefined && result.totalCompleted !== undefined;
	}

	static TTL = 7 * 24 * 60 * 60 * 1000;
}

export interface GoalConfigData {};

export type ConfigSchema<ConfigData extends GoalConfigData> = {
	[configKey in keyof ConfigData]: {
		type: 'string'|'number'|'boolean',
		friendlyName: string,
		actionDisplayName?: string,
		description?: string,
		icon?: string,
		value?: any,
	}
}

export interface ContentConfigSchema {
	buttonText?: {};
	buttonUrl?: {};
	message?: {};
	header?: {};
}

export interface IsGoalCompleteSchema {
	raleonUserId: number;
	walletAddress?: string;
	orgId: string | number;
	goalId: number;
	questId: number;
	configData: ConfigSchema<GoalConfigData>;
	userData: any;
}

export interface IsGoalCompleteObjectResult {
	isComplete: boolean;
	totalCompleted: number;
	totalRequired: number;
}
