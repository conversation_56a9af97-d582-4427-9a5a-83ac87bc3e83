import {BindingScope, injectable} from '@loopback/core';
import {GoalRepository} from '../../../repositories';
import {ConfigSchema, ContentConfigSchema, GoalConfigData, GoalType} from './goal-type.abstract';


export interface FollowOnTwitterDataSchema extends GoalConfigData {
	orgTwitterHandle: any;
}
@injectable({scope: BindingScope.SINGLETON})
export class FollowOnTwitter extends GoalType<FollowOnTwitterDataSchema> {

	getGoalTypeName(): string {
		return FollowOnTwitter.GOAL_TYPE_NAME;
	}

	getFriendlyName(): string {
		return FollowOnTwitter.FRIENDLY_NAME;
	}

	getConfigDataSchema(): ConfigSchema<FollowOnTwitterDataSchema> {
		return {
			orgTwitterHandle: {
				type: 'string',
				friendlyName: 'Twitter Handle',
				actionDisplayName: 'Follow',
				icon: 'twitter.svg',
			}
		};
	}

	getContentConfigSchema(): ContentConfigSchema {
		return {
			buttonText: FollowOnTwitter.FRIENDLY_NAME,
			buttonUrl: 'custom event button url',
			message: 'Some custom event message',
		}
	}

	getUserDataSchema(): any {
		return {
			twitterHandle: 'string'
		}
	}

	async isGoalComplete(data: any): Promise<boolean> {
		//call out to twitter to see if this user is following the org
		throw new Error('Method not implemented.');
	}

	static GOAL_TYPE_NAME = 'FollowOnTwitter';
	static FRIENDLY_NAME = 'Follow On Twitter';
}
