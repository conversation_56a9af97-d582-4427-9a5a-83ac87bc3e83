import {UserService} from '@loopback/authentication';
import {inject} from '@loopback/context';
import {repository} from '@loopback/repository';
import {HttpErrors} from '@loopback/rest';
import {securityId, UserProfile} from '@loopback/security';
import {SendEmailResponse} from 'aws-sdk/clients/ses';
import _ from 'lodash';
import {v4 as uuidv4} from 'uuid';
import {NewUserRequest} from '../controllers';
import {PasswordHasherBindings} from '../keys';
import {IDENTITY_TYPES, User, UserWithPassword} from '../models';
import {Credentials, UserIdentityRepository, UserRepository} from '../repositories';
import {subtractDates} from '../utils';
import {sanitizeFields} from '../utils';
import {EmailService} from './email.service';
import {PasswordHasher} from './hash.password.bcryptjs';
import {AccessTokenService} from './oauth/token-service';
import {service} from '@loopback/core';
import {OAuth2Client, TokenPayload} from 'google-auth-library';

export class UserManagementService implements UserService<User, Credentials> {
  constructor(
    @repository(UserRepository)
    public userRepository: UserRepository,
	@repository(UserIdentityRepository)
	public userIdentityRepository: UserIdentityRepository,
    @inject(PasswordHasherBindings.PASSWORD_HASHER)
    public passwordHasher: PasswordHasher,
    @inject('services.EmailService')
    public emailService: EmailService,
        @service(AccessTokenService)
        public accessTokenService: AccessTokenService
  ) {
    this.googleClient = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);
  }

  private googleClient: OAuth2Client;

  async verifyToken(token: string): Promise<User> {
	const userIdentity = await this.userIdentityRepository.findOne({
		where: {
			identityType: IDENTITY_TYPES.SELF_SERVICE,
			identityValue: token,
		}
	});

	if (!userIdentity) {
		throw new HttpErrors.Unauthorized('Invalid token');
	}

	const user = await this.userRepository.findById(userIdentity.userId);
	const userData = await this.accessTokenService.verifyUserToken(token);
	if (!user || !userData ||
		userData.id !== user.id ||
		userData.organizationId !== user.organizationId
	) {
		throw new HttpErrors.Unauthorized('Invalid token');
	}

	return user;
  }

  async verifyCredentials(credentials: Credentials): Promise<User> {
    const {email, password} = credentials;
    const invalidCredentialsError = 'Invalid email or password.';

    if (!email) {
      throw new HttpErrors.Unauthorized(invalidCredentialsError);
    }
    const foundUser = await this.userRepository.findOne({
		where: { or: [{ email, isSecondaryAccount: false }, { email, isSecondaryAccount: null as any }] },
    });
    if (!foundUser) {
      throw new HttpErrors.Unauthorized(invalidCredentialsError);
    }

    const credentialsFound = await this.userRepository.findCredentials(
      foundUser.id,
    );
    if (!credentialsFound) {
      throw new HttpErrors.Unauthorized(invalidCredentialsError);
    }

    const passwordMatched = await this.passwordHasher.comparePassword(
      password,
      credentialsFound.password,
    );

    if (!passwordMatched) {
      throw new HttpErrors.Unauthorized(invalidCredentialsError);
    }

    return foundUser;
  }

  convertToUserProfile(user: User): UserProfile {
    // since first name and lastName are optional, no error is thrown if not provided
    let userName = '';
    if (user.firstName) userName = `${user.firstName}`;
    if (user.lastName)
      userName = user.firstName
        ? `${userName} ${user.lastName}`
        : `${user.lastName}`;
    return {
      [securityId]: user.id!.toString(),
      name: userName,
      id: user.id,
      roles: user.roles,
    };
  }

  async createUser(userWithPassword: NewUserRequest): Promise<User> {
    sanitizeFields(userWithPassword as any, [
      'email',
      'firstName',
      'lastName',
    ]);
    const password = await this.passwordHasher.hashPassword(
      userWithPassword.password,
    );
	(userWithPassword as any).isSecondaryAccount = (userWithPassword as any).isSecondaryAccount || false;
    userWithPassword.password = password;
    const user = await this.userRepository.create(
      _.omit(userWithPassword, 'password'),
    );
    await this.userRepository.userCredentials(user.id).create({password});
    return user;
  }

  async createNewUser(user: UserWithPassword): Promise<User> {
    sanitizeFields(user as any, ['email', 'firstName', 'lastName']);
    const password = await this.passwordHasher.hashPassword(user.password);
    user.password = password;

    const newUser = await this.userRepository.create(
      _.omit(user, 'password'),
    );

    await this.userRepository.userCredentials(newUser.id).create({password});
    return newUser;
  }

  async requestPasswordReset(email: string): Promise<SendEmailResponse> {
    const noAccountFoundError =
      'No account associated with the provided email address.';
    const foundUser = await this.userRepository.findOne({
		where: { or: [{ email, isSecondaryAccount: false }, { email, isSecondaryAccount: null as any }] },
    });

    if (!foundUser) {
      throw new HttpErrors.NotFound(noAccountFoundError);
    }

    const user = await this.updateResetRequestLimit(foundUser);

    try {
      await this.userRepository.updateById(user.id, user);
    } catch (e) {
      return e;
    }
    return this.emailService.sendResetPasswordMail(user);
  }

  /**
   * Checks user reset timestamp if its same day increase count
   * otherwise set current date as timestamp and start counting
   * For first time reset request set reset count to 1 and assign same day timestamp
   * @param user
   */
  async updateResetRequestLimit(user: User): Promise<User> {
    const resetTimestampDate = new Date(user.resetTimestamp);

    const difference = await subtractDates(resetTimestampDate);

    if (difference === 0) {
      user.resetCount = user.resetCount + 1;

      if (user.resetCount > +(process.env.PASSWORD_RESET_EMAIL_LIMIT ?? 2)) {
        throw new HttpErrors.TooManyRequests(
          'Account has reached daily limit for sending password-reset requests',
        );
      }
    } else {
      user.resetTimestamp = new Date().toLocaleDateString();
      user.resetCount = 1;
    }
    // TODO: For generating unique reset key there are other options besides the proposed solution below.
    user.resetKey = uuidv4();
    user.resetKeyTimestamp = new Date().toLocaleDateString();

    return user;
  }

  /**
   * Ensures reset key is only valid for a day
   * @param user
   */
  async validateResetKeyLifeSpan(user: User): Promise<User> {
    const resetKeyLifeSpan = new Date(user.resetKeyTimestamp);
    const difference = await subtractDates(resetKeyLifeSpan);

    user.resetKey = '';
    user.resetKeyTimestamp = '';

    if (difference !== 0) {
      throw new HttpErrors.BadRequest('The provided reset key has expired.');
    }

    return user;
  }

  async verifyGoogleToken(idToken: string): Promise<TokenPayload> {
    try {
      const ticket = await this.googleClient.verifyIdToken({
        idToken,
        audience: process.env.GOOGLE_CLIENT_ID,
      });
      const payload = ticket.getPayload();
      if (!payload) {
        throw new HttpErrors.Unauthorized('Invalid Google token');
      }
      return payload;
    } catch (err) {
      throw new HttpErrors.Unauthorized('Invalid Google token');
    }
  }
}
