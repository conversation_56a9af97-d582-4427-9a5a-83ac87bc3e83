import {injectable, BindingScope} from '@loopback/core';
import {repository} from '@loopback/repository';
import {TranslationStringRepository} from '../repositories';
import {TranslationString} from '../models';

@injectable({scope: BindingScope.TRANSIENT})
export class TranslationService {
    static translationCache = new Map<string, TranslationString>();

    constructor(
    // Add @repository to inject repositories
    	@repository(TranslationStringRepository)
        private repository: TranslationStringRepository,
    ) {}

    async getTranslation(
        language: string,
        key: string,
		orgId: number,
        replacements: { [key: string]: string|undefined } = {},
        fallbackValue = '',
    ): Promise<string> {
        const translation = await this.getTranslationValue(language, key, orgId, fallbackValue);

        return TranslationService.replacePlaceholders(translation, replacements);
    }

    async populateCache(): Promise<void> {
        const translations = await this.repository.find();
        translations.forEach((translation) => {
            TranslationService.translationCache.set(translation.id!, translation);
        });
    }

    private async getTranslationValue(
        language: string,
        key: string,
		orgId: number,
        value: string | undefined
    ): Promise<string | undefined> {
		const cachedTranslation = TranslationService.getCachedTranslation(language, key, orgId);
		if (cachedTranslation) {
			return cachedTranslation;
		}

		const keys = [
			`${language}_${key}_org_${orgId}`,
			`${language}_${key}`,
		];

		if (language.includes('-')) {
			keys.push(`${language.split('-')[0]}_${key}_org_${orgId}`);
			keys.push(`${language.split('-')[0]}_${key}`);
		}

		const translations = await this.repository.find({
			where: {
				id: {inq: keys},
			},
		});

		if (translations.length > 0) {
			const best =
				translations.find((t) => t.id === `${language}_${key}_org_${orgId}`) ||
				translations.find((t) => t.id === `${language}_${key}`) ||
				translations.find((t) => t.id === `${language.split('-')[0]}_${key}_org_${orgId}`) ||
				translations.find((t) => t.id === `${language.split('-')[0]}_${key}`);

			return best?.value;
		}

		return value;
    }

    static getCachedTranslation(
        language: string,
        key: string,
		orgId: number,
        replacements: { [key: string]: string|undefined } = {},
        fallbackValue = ''
    ): string {
        let translation =
			TranslationService.translationCache.get(`${language}_${key}_org_${orgId}`) ||
			TranslationService.translationCache.get(`${language}_${key}`);

		if (!translation && language.includes('-')) {
			translation =
				TranslationService.translationCache.get(`${language.split('-')[0]}_${key}_org_${orgId}`) ||
				TranslationService.translationCache.get(`${language.split('-')[0]}_${key}`);
		}

		if (!translation) {
			translation = { value: fallbackValue } as any;
		}

        return this.replacePlaceholders(translation!.value, replacements);
    }

    private static replacePlaceholders(
        value: string | undefined,
        replacements: { [key: string]: string|undefined }
    ): string {
        if (value == undefined || value == null) {
            return '';
        }

        return value.replace(
            /{{\s*(?!currency_value:\d+)([^}\s]+)\s*}}/g,
            (match, placeholder) => {
				if (replacements[placeholder] == undefined || replacements[placeholder] == null) {
					return '';
				}
                return replacements[placeholder]!;
            }
        );
    }
}
