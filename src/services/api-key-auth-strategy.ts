import {inject} from '@loopback/core';
import {HttpErrors, Request} from '@loopback/rest';
import {AuthenticationStrategy} from '@loopback/authentication';
import {UserProfile, securityId} from '@loopback/security';
import {ApiKeyRepository} from '../repositories/api-key.repository';
import {repository} from '@loopback/repository';

export class ApiKeyAuthenticationStrategy implements AuthenticationStrategy {
  name = 'api-key';

  constructor(
    @repository(ApiKeyRepository)
    private apiKeyRepository: ApiKeyRepository,
  ) {}

  async authenticate(request: Request): Promise<UserProfile | undefined> {
    const apiKey = this.extractCredentials(request);
    const foundKey = await this.apiKeyRepository.findOne({
      where: {key: apiKey},
    });

    if (!foundKey) {
      throw new HttpErrors.Unauthorized('Invalid API key');
    }

    // Update last used timestamp
    await this.apiKeyRepository.updateById(foundKey.id, {
      lastUsedAt: new Date(),
    });

    const userProfile: UserProfile = {
      [securityId]: foundKey.id!.toString(),
      name: foundKey.name,
      scopes: foundKey.scopes || [],
    };

    return userProfile;
  }

  extractCredentials(request: Request): string {
    if (!request.headers.authorization) {
      throw new HttpErrors.Unauthorized('Authorization header not found.');
    }

    const authHeaderValue = request.headers.authorization;

    if (!authHeaderValue.startsWith('ApiKey ')) {
      throw new HttpErrors.Unauthorized(
        'Authorization header is not of type "ApiKey".',
      );
    }

    return authHeaderValue.split(' ')[1];
  }
}
