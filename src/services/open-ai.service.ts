/// <reference lib="dom" />
import {injectable, /* inject, */ BindingScope} from '@loopback/core';
import OpenAI, {toFile} from 'openai';
const openai = new OpenAI({apiKey: '***********************************************************************************************************************************************************************'});

@injectable({scope: BindingScope.TRANSIENT})
export class OpenAiService {
	async getO3Completion(prompt: string) : Promise<string> {
		const completion = await openai.chat.completions.create({
			model: 'o3-mini',
			max_completion_tokens: 16384,
			messages: [
				{
					role: 'system',
					content: prompt
				}
			],
			response_format: {type: "json_object"}
		}) as any;
		// Log the completion for debugging purposes
		console.log(completion.choices[0].message.content);

		// Return the raw content as a string
		if (completion.choices[0].message.content) {
			return completion.choices[0].message.content;
		} else {
			throw new Error('No content received from OpenAI');
		}
	}

	async getImageDescription(prompt: string, imageUrl: string) : Promise<string> {
		const completion = await openai.chat.completions.create({
			model: 'gpt-4o-mini',
			max_tokens: 1000,
			messages: [
				{
					role: 'user',
					content: [{
						"type": "text", "text": prompt},
						{
							"type": "image_url",
							"image_url": {
								"url": imageUrl
							}
						}
					]
				}
			],
			response_format: {type: "json_object"}
		}) as any;
		// Log the completion for debugging purposes
		console.log(completion.choices[0].message.content);

		// Return the raw content as a string
		if (completion.choices[0].message.content) {
			return completion.choices[0].message.content;
		} else {
			throw new Error('No content received from OpenAI');
		}
	}

	async getImageInfo(imageUrl: string) : Promise<string> {
		const prompt = `Analyze this image and extract all readable text, focusing specifically on:
1. Larger, prominent text (headings, titles, main messages)
2. Button text and clickable elements
3. Navigation text and menu items
4. Important UI labels and form fields

IGNORE:
- Small product labels or SKU numbers
- Copyright text and legal disclaimers
- Watermarks and branding unless they are primary content
- Very small or hard-to-read text

Return a JSON object with the following structure:
{
  "mainText": ["array of main headings and prominent text"],
  "buttonText": ["array of button text and clickable elements"],
  "navigationText": ["array of navigation and menu text"],
  "otherText": ["array of other significant readable text"],
  "summary": "Brief description of what type of content/interface this appears to be"
}

If no significant text is found, return empty arrays but always include the summary.`;

		const completion = await openai.chat.completions.create({
			model: 'gpt-4o-mini',
			max_tokens: 1000,
			messages: [
				{
					role: 'user',
					content: [{
						"type": "text", "text": prompt},
						{
							"type": "image_url",
							"image_url": {
								"url": imageUrl
							}
						}
					]
				}
			],
			response_format: {type: "json_object"}
		}) as any;
		// Log the completion for debugging purposes
		console.log(completion.choices[0].message.content);

		// Return the raw content as a string
		if (completion.choices[0].message.content) {
			return completion.choices[0].message.content;
		} else {
			throw new Error('No content received from OpenAI');
		}
	}

	async getCompletion(
    messages: Array<{ role: string; content: string | Array<{ type: string; text?: string; image_url?: { url: string } }> }>,
    max_tokens?: number,
    model?: string,
    json = true,
    tools?: Array<any>
  ): Promise<any> {
    try {
      const request: any = {
        model: model || 'gpt-4-vision-preview',
        max_tokens: max_tokens || 4096,
        messages: messages.map(msg => ({
          role: msg.role,
          content: Array.isArray(msg.content) ? msg.content : msg.content
        }))
      };

      if (tools) {
        request.tools = tools;
      }
      if (json) {
        request.response_format = {type: "json_object"};
      }

      const completion = await openai.chat.completions.create(request);
      console.log(completion.choices[0].message.content);

      return {
        content: completion.choices[0].message.content,
        usage: completion.usage,
        model_version: completion.model
      };

    } catch (error) {
      console.error('OpenAI API Error:', error);
      throw new Error('Failed to get response from OpenAI');
    }
  }

    async generateImage(prompt: string, options: {
        model?: 'gpt-image-1';
        quality?: 'standard' | 'high';
        style?: 'vivid' | 'natural';
        size?: '1024x1024' | '1024x1536' | '1536x1024';
        background?: 'auto' | 'transparent' | 'opaque' | null;
        n?: number;
    } = {}): Promise<{data: Array<{url: string, revised_prompt?: string}>, usage: {total_tokens: number, prompt_tokens: number, output_tokens: number}}> {
        try {
            const useGPTImage = options.model === 'gpt-image-1';

            const response = await openai.images.generate({
                model: options.model || 'gpt-image-1',
                prompt,
                n: options.n || 1,
                // quality: options.quality || 'high',
                size: options.size || '1024x1024',
                style: !useGPTImage ? (options.style || 'vivid') : undefined,
                background: options.background
            } as any);

            // For image generation, we can estimate token usage based on prompt length
            // OpenAI generally counts 1 token per ~4 characters of text
            const promptTokens = (response as any).usage?.input_tokens || Math.ceil(prompt.length / 4);
			const outputTokens = (response as any).usage?.output_tokens || 0;
			const totalTokens = (response as any).usage?.total_tokens || promptTokens + outputTokens;

            return {
                data: response?.data?.map(img => ({
                    url: `data:image/png;base64,${img.b64_json}`,
                    revised_prompt: img.revised_prompt
                })) || [],
                usage: {
                    total_tokens: totalTokens,
					output_tokens: outputTokens,
                    prompt_tokens: promptTokens
                }
            };
        } catch (error) {
            console.error('Image generation error:', error);
            throw new Error('Failed to generate image');
        }
    }

    async editImage(
        images: string[], // Data URLs
        mask: string | undefined, // Data URL
        prompt: string,
        options: {
            model?: 'gpt-image-1';
            quality?: 'standard' | 'high';
            size?: '1024x1024' | '1024x1536' | '1536x1024';
            n?: number;
            imageEditing?: {
                quality?: 'standard' | 'high';
                [key: string]: any;
            };
        } = {}
    ): Promise<{data: Array<{url: string}>, usage: {total_tokens: number, prompt_tokens: number, output_tokens: number}}> {
        try {
			if (options.model === 'gpt-image-1') {
                if (images.length > 16) {
                    throw new Error('GPT-Image-1 supports up to 16 images at a time');
                }
            }

            // Combine all quality sources with priority
            const quality = options.imageEditing?.quality || options.quality || 'high';

            const dataUrlToBlob = async (
                dataUrl: string,
                ): Promise<{ blob: Blob; mimeType: string }> => {
                const match = dataUrl.match(/^data:([^;]+);base64,(.+)$/);
                if (!match) throw new Error('Invalid data URL format');

                const [, mimeType, base64Data] = match;
                const buffer = Buffer.from(base64Data, 'base64'); // fast native decode
                const blob = new Blob([buffer], { type: mimeType });
                return { blob, mimeType };
            };

            // Convert data URLs to OpenAI FileLike objects and extract MIME type
            const imageFiles = await Promise.all(images.map(async (dataUrl, i) => {
                const { blob, mimeType } = await dataUrlToBlob(dataUrl);
                return await toFile(blob, `image${i}.png`, { type: mimeType });
            }));

            // Convert mask if provided
            let maskFile: any = undefined;
            if (mask) {
                const { blob, mimeType } = await dataUrlToBlob(mask);
                maskFile = await toFile(blob, 'mask.png', { type: mimeType });
            }

            // Process all images in a single request
            const response = await openai.images.edit({
                image: imageFiles as any,
                mask: maskFile,
                model: options.model || 'gpt-image-1',
                // quality: 'high',
                prompt,
                n: options.n || 1,
                size: options.size || '1024x1024' as any
            } as any) as any;

            // Convert response to data URLs
            const allData = response?.data?.map((img: any) => {
                if (!img.b64_json) {
                    throw new Error('Invalid response format from OpenAI');
                }
                return { url: `data:image/png;base64,${img.b64_json}` };
            }) || [];

            // For image editing, we can estimate token usage based on prompt length
            // OpenAI generally counts 1 token per ~4 characters of text
            const promptTokens = (response as any).usage?.input_tokens || Math.ceil(prompt.length / 4);
			const outputTokens = (response as any).usage?.output_tokens || 0;
            const totalTokens = (response as any).usage?.total_tokens || promptTokens + outputTokens;

            return {
                data: allData,
                usage: {
                    total_tokens: totalTokens,
					output_tokens: outputTokens,
                    prompt_tokens: promptTokens
                }
            };
        } catch (error) {
            console.error('Image editing error:', error);
            throw new Error('Failed to edit image');
        }
    }
}
