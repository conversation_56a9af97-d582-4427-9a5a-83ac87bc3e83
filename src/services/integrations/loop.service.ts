import {BindingScope, inject, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import { OrganizationOrganizationKeysController, DEFAULT_SECRET_KEY_NAME } from '../../controllers/organization-organization-keys.controller';
import {OrganizationIntegrationDetailsRepository} from '../../repositories/organization-integration-details.repository';
import {OrganizationKeysRepository} from '../../repositories';
import fetch from 'node-fetch';

const LOOP_API_BASE_URL = 'https://api.loopsubscriptions.com/admin/2023-10';
const LOOP_INTEGRATION_ID = 8;
const LOOP_API_KEY_NAME = 'Loop-API-Key';

@injectable()
export class LoopIntegration {

	constructor(
		@repository(OrganizationIntegrationDetailsRepository)
		protected organizationIntegrationDetailsRepository: OrganizationIntegrationDetailsRepository,
		@repository(OrganizationKeysRepository)
		protected organizationKeysRepository: OrganizationKeysRepository,
		@inject('controllers.OrganizationOrganizationKeysController')
		private organizationKeysController: OrganizationOrganizationKeysController,
	) {}

	async enableLoop(orgId: number): Promise<any> {
		const orgIntegrationDetails = await this.organizationIntegrationDetailsRepository.findOne({
			where: {
				orgId: orgId,
				integrationId: LOOP_INTEGRATION_ID,
			}
		});

		if (!orgIntegrationDetails) {
			await this.organizationIntegrationDetailsRepository.create({
				orgId: orgId,
				integrationId: LOOP_INTEGRATION_ID,
				enabled: true,
			});
		} else {
			await this.organizationIntegrationDetailsRepository.updateById(orgIntegrationDetails.id, {
				enabled: true,
			});
		}

		return {
			success: true,
		};
	}

	async disableLoop(orgId: number): Promise<any> {
		const orgIntegrationDetails = await this.organizationIntegrationDetailsRepository.findOne({
			where: {
				orgId: orgId,
				integrationId: LOOP_INTEGRATION_ID,
			}
		});

		if (!orgIntegrationDetails) {
			await this.organizationIntegrationDetailsRepository.create({
				orgId: orgId,
				integrationId: LOOP_INTEGRATION_ID,
				enabled: false,
			});
		} else {
			await this.organizationIntegrationDetailsRepository.updateById(orgIntegrationDetails.id, {
				enabled: false,
			});
		}

		return {
			success: true,
		};
	}

	async saveLoopKeys(orgId: number, data: any): Promise<any> {
		if (!data.apiKey) {
			return {
				success: false,
				message: 'Missing API Key'
			};
		}

		// Delete any existing keys first
		const orgIntegrationKeys = await this.organizationKeysRepository.find({
			where: {
				organizationId: orgId,
				key: LOOP_API_KEY_NAME,
			}
		});

		if (orgIntegrationKeys && orgIntegrationKeys.length > 0) {
			for (let key of orgIntegrationKeys) {
				await this.organizationKeysRepository.deleteById(key.id);
			}
		}

		// Create new encrypted key using the controller
		await this.organizationKeysController.create({
			key: LOOP_API_KEY_NAME,
			value: data.apiKey,
			secretKeyId: DEFAULT_SECRET_KEY_NAME
		}, orgId);

		return {
			success: true,
		};
	}

	async deleteLoopKeys(orgId: number): Promise<any> {
		const orgIntegrationKeys = await this.organizationKeysRepository.find({
			where: {
				organizationId: orgId,
				key: LOOP_API_KEY_NAME,
			}
		});

		if (orgIntegrationKeys && orgIntegrationKeys.length > 0) {
			for (let key of orgIntegrationKeys) {
				await this.organizationKeysRepository.deleteById(key.id);
			}
		}

		return {
			success: true,
		};
	}

	async isLoopConnected(orgId: number): Promise<boolean> {
		const orgIntegrationKeys = await this.organizationKeysRepository.find({
			where: {
				and: [
					{ organizationId: orgId, },
					{ key: LOOP_API_KEY_NAME },
				],
			}
		});

		const keysInstalled = orgIntegrationKeys && orgIntegrationKeys.length === 1;
		if (!keysInstalled) return false;

		const loopApiKey = await this.organizationKeysController.findKey(LOOP_API_KEY_NAME, orgId);
		const webhookListeners = await this.getWebhookListeners({ apiKey: loopApiKey[0].value });
		return keysInstalled && webhookListeners?.data?.length > 0;
	}

    async createWebhookListener(orgId: number, data: any, url: string): Promise<any> {
        const existingListeners = await this.getWebhookListeners(data);
        if (existingListeners?.data?.length === 0) {
            const newListener = await this.createWebhookListeners(orgId, data, url);
            if (newListener) {
                return {
                    status: 200,
                    message: 'Webhook listener registered.',
                }
            } else {
                return {
                    status: 500,
                    message: 'Failed to register webhook listener.',
                }
            }
        } else {
            return {
                status: 200,
                message: 'Webhook listener already registered.',
            }
        }
    }

    async getWebhookListeners(data: any): Promise<any> {
        const response = await fetch(`${LOOP_API_BASE_URL}/webhook`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Loop-Token': data.apiKey,
            }
        });

        if (!response.ok) {
            return [];
        }

        return response.json();
    }

    async createWebhookListeners(orgId: number, data: any, url: string): Promise<any> {
        const response = await fetch(`${LOOP_API_BASE_URL}/webhook`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Loop-Token': data.apiKey,
            },
            body: JSON.stringify({
                topic: 'order/processed',
                address: `${url}?name=loop`,
            })
        });

        if (!response.ok) {
            return null;
        }

        return response.json();
    }

    async deleteWebhookListeners(orgId: number, data: any): Promise<any> {
        const existingListeners = await this.getWebhookListeners(data);
        if (existingListeners?.data?.length) {
            for (let listener of existingListeners?.data) {
				if (!listener?.address?.endsWith('?name=loop')) {
					continue;
				}

                await fetch(`${LOOP_API_BASE_URL}/webhook/${listener.id}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Loop-Token': data.apiKey,
                    }
                });
            }
            return {
                status: 200,
                message: 'Webhook listeners deleted.',
            }
        } else {
            return {
                status: 200,
                message: 'No webhook listeners found.',
            }
        }
    }
}
