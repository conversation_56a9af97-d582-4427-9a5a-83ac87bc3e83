import {BindingScope, inject, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import { OrganizationOrganizationKeysController, DEFAULT_SECRET_KEY_NAME } from '../../controllers/organization-organization-keys.controller';
import {OrganizationIntegrationDetailsRepository} from '../../repositories/organization-integration-details.repository';
import {OrganizationKeysRepository} from '../../repositories';


const SKIO_INTEGRATION_ID = 4;
const SKIO_API_KEY_NAME = 'Skio-API-Key';
const SKIO_WEBHOOK_TOKEN_NAME = 'Skio-Webhook-Token';

@injectable()
export class SkioIntegration {

	constructor(
		@repository(OrganizationIntegrationDetailsRepository)
		protected organizationIntegrationDetailsRepository: OrganizationIntegrationDetailsRepository,
		@repository(OrganizationKeysRepository)
		protected organizationKeysRepository: OrganizationKeysRepository,
		@inject('controllers.OrganizationOrganizationKeysController')
		private organizationKeysController: OrganizationOrganizationKeysController,
	) {}

	async enableSkio(orgId: number): Promise<any> {
		const orgIntegrationDetails = await this.organizationIntegrationDetailsRepository.findOne({
			where: {
				orgId: orgId,
				integrationId: SKIO_INTEGRATION_ID,
			}
		});

		if (!orgIntegrationDetails) {
			await this.organizationIntegrationDetailsRepository.create({
				orgId: orgId,
				integrationId: SKIO_INTEGRATION_ID,
				enabled: true,
				connectedDate: new Date().toUTCString(),
			});
		} else {
			await this.organizationIntegrationDetailsRepository.updateById(orgIntegrationDetails.id, {
				enabled: true,
			});
		}

		return {
			success: true,
		};
	}

	async disableSkio(orgId: number): Promise<any> {
		const orgIntegrationDetails = await this.organizationIntegrationDetailsRepository.findOne({
			where: {
				orgId: orgId,
				integrationId: SKIO_INTEGRATION_ID,
			}
		});

		if (!orgIntegrationDetails) {
			await this.organizationIntegrationDetailsRepository.create({
				orgId: orgId,
				integrationId: SKIO_INTEGRATION_ID,
				enabled: false,
			});
		} else {
			await this.organizationIntegrationDetailsRepository.updateById(orgIntegrationDetails.id, {
				enabled: false,
			});
		}

		return {
			success: true,
		};
	}

	async saveSkioKeys(orgId: number, data: any): Promise<any> {
		//Verify body includes enabled
		if (!data.apiKey || !data.webhookToken) {
			return {
				success: false,
				message: 'Missing apiKey or webhookToken'
			};
		}

		const orgIntegrationKeys = await this.organizationKeysRepository.find({
			where: {
				organizationId: orgId,
				or: [
					{key: SKIO_API_KEY_NAME},
					{key: SKIO_WEBHOOK_TOKEN_NAME},
				],
			}
		});

		if (orgIntegrationKeys && orgIntegrationKeys.length > 0) {
			for (let key of orgIntegrationKeys) {
				if (key.key === SKIO_API_KEY_NAME) {
					await this.organizationKeysRepository.updateById(key.id, {
						value: data.apiKey
					});
				}

				if (key.key === SKIO_WEBHOOK_TOKEN_NAME) {
					await this.organizationKeysRepository.updateById(key.id, {
						value: data.webhookToken
					});
				}
			}
		}

		if (!orgIntegrationKeys || orgIntegrationKeys.length === 0) {
			await this.organizationKeysController.create({
				key: SKIO_API_KEY_NAME,
				value: data.apiKey,
				secretKeyId: DEFAULT_SECRET_KEY_NAME
			}, orgId);

			await this.organizationKeysController.create({
				key: SKIO_WEBHOOK_TOKEN_NAME,
				value: data.webhookToken,
				secretKeyId: DEFAULT_SECRET_KEY_NAME
			}, orgId);
		}

		return {
			success: true,
		};
	}

	async deleteSkioKeys(orgId: number): Promise<any> {
		const orgIntegrationKeys = await this.organizationKeysRepository.find({
			where: {
				organizationId: orgId,
				or: [
					{key: SKIO_API_KEY_NAME},
					{key: SKIO_WEBHOOK_TOKEN_NAME},
				],
			}
		});

		if (orgIntegrationKeys && orgIntegrationKeys.length > 0) {
			for (let key of orgIntegrationKeys) {
				await this.organizationKeysRepository.deleteById(key.id);
			}
		}

		return {
			success: true,
		};
	}

	async isSkioConnected(orgId: number): Promise<boolean> {
		const orgIntegrationKeys = await this.organizationKeysRepository.find({
			where: {
				and: [
					{ organizationId: orgId, },
					{ or: [
						{key: SKIO_API_KEY_NAME},
						{key: SKIO_WEBHOOK_TOKEN_NAME},
					]},
				],
			}
		});

		return orgIntegrationKeys && orgIntegrationKeys.length === 2;
	}
}
