import {BindingScope, inject, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import { OrganizationOrganizationKeysController, DEFAULT_SECRET_KEY_NAME } from '../../controllers/organization-organization-keys.controller';
import {OrganizationIntegrationDetailsRepository} from '../../repositories/organization-integration-details.repository';
import {OrganizationKeysRepository} from '../../repositories';
import fetch from 'node-fetch';

const PRIVE_INTEGRATION_ID = 6;
const PRIVE_API_KEY_NAME = 'Prive-API-Key';

@injectable()
export class PriveIntegration {

	constructor(
		@repository(OrganizationIntegrationDetailsRepository)
		protected organizationIntegrationDetailsRepository: OrganizationIntegrationDetailsRepository,
		@repository(OrganizationKeysRepository)
		protected organizationKeysRepository: OrganizationKeysRepository,
		@inject('controllers.OrganizationOrganizationKeysController')
		private organizationKeysController: OrganizationOrganizationKeysController,
	) {}

	async enablePrive(orgId: number): Promise<any> {
		const orgIntegrationDetails = await this.organizationIntegrationDetailsRepository.findOne({
			where: {
				orgId: orgId,
				integrationId: PRIVE_INTEGRATION_ID,
			}
		});

		if (!orgIntegrationDetails) {
			await this.organizationIntegrationDetailsRepository.create({
				orgId: orgId,
				integrationId: PRIVE_INTEGRATION_ID,
				enabled: true,
				connectedDate: new Date().toUTCString(),
			});
		} else {
			await this.organizationIntegrationDetailsRepository.updateById(orgIntegrationDetails.id, {
				enabled: true,
			});
		}

		return {
			success: true,
		};
	}

	async disablePrive(orgId: number): Promise<any> {
		const orgIntegrationDetails = await this.organizationIntegrationDetailsRepository.findOne({
			where: {
				orgId: orgId,
				integrationId: PRIVE_INTEGRATION_ID,
			}
		});

		if (!orgIntegrationDetails) {
			await this.organizationIntegrationDetailsRepository.create({
				orgId: orgId,
				integrationId: PRIVE_INTEGRATION_ID,
				enabled: false,
			});
		} else {
			await this.organizationIntegrationDetailsRepository.updateById(orgIntegrationDetails.id, {
				enabled: false,
			});
		}

		return {
			success: true,
		};
	}

	async savePriveKeys(orgId: number, data: any): Promise<any> {
		if (!data.apiKey) {
			return {
				success: false,
				message: 'Missing API Key'
			};
		}

		const orgIntegrationKeys = await this.organizationKeysRepository.find({
			where: {
				organizationId: orgId,
				key: PRIVE_API_KEY_NAME,
			}
		});

		if (orgIntegrationKeys && orgIntegrationKeys.length > 0) {
			for (let key of orgIntegrationKeys) {
				if (key.key === PRIVE_API_KEY_NAME) {
					await this.organizationKeysRepository.updateById(key.id, {
						value: data.apiKey
					});
				}
			}
		}

		if (!orgIntegrationKeys || orgIntegrationKeys.length === 0) {
			await this.organizationKeysController.create({
				key: PRIVE_API_KEY_NAME,
				value: data.apiKey,
				secretKeyId: DEFAULT_SECRET_KEY_NAME
			}, orgId);
		}

		return {
			success: true,
		};
	}

	async deletePriveKeys(orgId: number): Promise<any> {
		const orgIntegrationKeys = await this.organizationKeysRepository.find({
			where: {
				organizationId: orgId,
				key: PRIVE_API_KEY_NAME,
			}
		});

		if (orgIntegrationKeys && orgIntegrationKeys.length > 0) {
			for (let key of orgIntegrationKeys) {
				await this.organizationKeysRepository.deleteById(key.id);
			}
		}

		return {
			success: true,
		};
	}

	async isPriveConnected(orgId: number): Promise<boolean> {
		const orgIntegrationKeys = await this.organizationKeysRepository.find({
			where: {
				and: [
					{ organizationId: orgId, },
					{ key: PRIVE_API_KEY_NAME },
				],
			}
		});

		const keysInstalled = orgIntegrationKeys && orgIntegrationKeys.length === 1;
		if (!keysInstalled) return false;

		const priveApiKey = await this.organizationKeysController.findKey(PRIVE_API_KEY_NAME, orgId);
		const webhookListeners = await this.getWebhookListeners({ apiKey: priveApiKey[0].value });
		return keysInstalled && webhookListeners && webhookListeners.webhooks.length >= 2;
	}

	async createWebhookListener(orgId: number, data: any, url: string): Promise<any> {
		const existingListeners = await this.getWebhookListeners(data);
		if (!existingListeners || existingListeners?.webhooks?.length === 0) {
			const newListeners = await this.createWebhookListeners(orgId, data, url);
			if (newListeners && newListeners.length) {
				return {
					status: 200,
					message: 'Webhook listeners registered.',
				}
			} else {
				return {
					status: 500,
					message: 'Failed to register webhook listener.',
				}
			}
		} else {
			return {
				status: 200,
				message: 'Webhook listeners already registered.',
			}
		}
	}

	async getWebhookListeners(data: any): Promise<any> {
		const response = await fetch('https://subs.api.tryprive.com/api/v1/webhooks?page=1&limit=100', {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				'Authorization': `Bearer ${data.apiKey}`,
			}
		})

		return await response.json();
	}

	async createWebhookListeners(orgId: number, data: any, url: string): Promise<any> {
		const topics = ['subscriptions/created', 'subscriptions/status/updated']; //TODO: validate this update topic is correct
		const results = [];
		for (let topic of topics) {
			const result = await fetch('https://subs.api.tryprive.com/api/v1/webhooks', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'Authorization': `Bearer ${data.apiKey}`,
				},
				body: JSON.stringify({
					address: `${url}?name=prive`,
					topic,
				})
			});
			results.push(await result.json());
		}
		return results;
	}

	async deleteWebhookListeners(orgId: number, data: any): Promise<any> {
		const existingListeners = await this.getWebhookListeners(data);
		if (existingListeners && existingListeners.length) {
			for (let listener of existingListeners) {
				await fetch(`https://subs.api.tryprive.com/api/v1/webhooks/${listener.id}`, {
					method: 'DELETE',
					headers: {
						'Content-Type': 'application/json',
						'Authorization': `Bearer ${data.apiKey}`,
					}
				})
			}
			return {
				status: 200,
				message: 'Webhook listeners deleted.',
			}
		} else {
			return {
				status: 200,
				message: 'No webhook listeners found.',
			}
		}
	}
}
