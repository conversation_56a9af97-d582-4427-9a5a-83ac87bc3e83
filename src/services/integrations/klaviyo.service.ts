import {injectable, BindingScope, inject} from '@loopback/core';
import {IntegrationRepository, OrganizationKeysRepository} from '../../repositories';
import {repository} from '@loopback/repository';
import axios, {AxiosRequestConfig} from 'axios';
import {HttpErrors} from '@loopback/rest';
import {OrganizationOrganizationKeysController} from '../../controllers';
import {MetricSegment} from '../../models';

interface CreateTemplateParams {
  name: string;
  html: string;
  editorType?: 'USER_DRAGGABLE' | 'CODE';
}

interface CreateCampaignParams {
  name: string;
  templateId: string;
  subject: string;
  previewText: string;
  listId: string;
  sendTime: string;
}

interface UpdateCampaignParams {
  name?: string;
  subject?: string;
  previewText?: string;
  sendTime?: string;
}

interface UpdateTemplateParams {
  name?: string;
  html?: string;
}

interface AssignTemplateParams {
  campaignId: string;
  messageId: string;
  templateId: string;
}

interface GetImagesParams {
  pageSize?: number;
  pageCursor?: string;
  nameFilter?: string;
  fields?: string[];
}

const KLAVIYO_INTEGRATION_KEY = 'Klaviyo-API-Key';

@injectable({scope: BindingScope.TRANSIENT})
export class KlaviyoService {
	private apiBaseUrl: string = 'https://a.klaviyo.com/api/';
	constructor(
		@repository(OrganizationKeysRepository)
		public organizationKeysRepository: OrganizationKeysRepository,
		@repository(IntegrationRepository)
		public integrationRepository: IntegrationRepository,
		@inject('controllers.OrganizationOrganizationKeysController')
		private organizationKeysController: OrganizationOrganizationKeysController,
	) {
	}

	/**
	 * Create a new segment in Klaviyo
	 * @param orgId The organization ID
	 * @param segmentData The data for the segment to be created
	 */
	async createSegment(orgId: number, segmentData: any): Promise<any> {
		const apiKey = await this.getKlaviyoApiKey(orgId);
		const klaviyoSegmentData = await this.prepareKlaviyoSegmentData(orgId, segmentData, apiKey);

		console.log('Segment Data:', JSON.stringify(klaviyoSegmentData, null, 2));
		const url = `${this.apiBaseUrl}segments/`;
		try {
			const response = await axios.post(url, klaviyoSegmentData, {
				headers: this.getHeaders(apiKey),
			});
			console.log('Klaviyo segment created:', response.data);
			return response.data.data.id;
		} catch (error: any) {
			console.error('Error creating Klaviyo segment for orgId:', orgId, error.response?.data || error.message);
			const errorMessage = error.response?.data?.errors?.[0]?.detail ||
							   error.response?.data?.message ||
							   error.message ||
							   'Failed to create segment in Klaviyo';
			const errorToThrow = new HttpErrors.InternalServerError(errorMessage);
			errorToThrow.isScopeError = error?.response?.data?.errors?.[0]?.code === 'permission_denied' && error?.response?.data?.errors?.[0]?.detail?.includes?.('scope');

			throw errorToThrow;
		}
	}


	/**
	 * Update a segment in Klaviyo
	 * @param orgId The organization ID
	 * @param segmentData The data for the segment to be created
	 */
	async updateSegment(orgId: number, klayvioSegmentId: string, segmentData: any): Promise<any> {
		const apiKey = await this.getKlaviyoApiKey(orgId);
		const klaviyoSegmentData = await this.prepareKlaviyoSegmentData(orgId, segmentData, apiKey);

		console.log('Segment Data:', JSON.stringify(klaviyoSegmentData, null, 2));
		const url = `${this.apiBaseUrl}segments/${klayvioSegmentId}`;
		try {
			const response = await axios.patch(url, {
				...klaviyoSegmentData,
				data: {
					id: klayvioSegmentId,
					...klaviyoSegmentData.data,
				}
			}, {
				headers: this.getHeaders(apiKey),
			});
			console.log('Klaviyo segment created:', response.data);
			return response.data.data.id;
		} catch (error: any) {
			console.error('Error creating Klaviyo segment:', error.response?.data || error.message);
			const errorMessage = error.response?.data?.errors?.[0]?.detail ||
							   error.response?.data?.message ||
							   error.message ||
							   'Failed to update segment in Klaviyo';
			throw new HttpErrors.InternalServerError(errorMessage);
		}
	}


	private async getKlaviyoApiKey(orgId: number): Promise<string> {
		const keys = await this.organizationKeysController.findKey(KLAVIYO_INTEGRATION_KEY, orgId);

		if (!keys || keys.length === 0) {
			throw new HttpErrors.BadRequest('Klaviyo API key not found for this organization.');
		}

		const apiKey = keys[0].value;

		if (!apiKey) {
			throw new HttpErrors.BadRequest('Klaviyo API key is empty.');
		}

		return apiKey;
	}

	private getHeaders(apiKey: string): AxiosRequestConfig['headers'] {
		return {
			'Content-Type': 'application/json',
			Authorization: `Klaviyo-API-Key ${apiKey}`,
			Revision: '2024-10-15',
		};
	}

	private async prepareKlaviyoSegmentData(orgId: number, data: any, apiKey: string): Promise<any> {
		const fieldMappings = await this.getFieldMappings();

		const positiveConditionGroups = await this.buildKlaviyoConditionsAsGroups(data.positive || [], fieldMappings, false, apiKey);

		const negativeConditionGroups = await this.buildKlaviyoConditionsAsGroups(
			data.negative || [],
			fieldMappings,
			true,
			apiKey
		);

		const combinedConditionGroups = [
			...positiveConditionGroups,
			...negativeConditionGroups,
			// Always include this condition group
			{
				conditions: [
					{
						type: "profile-property",
						property: "properties['Raleon User Id']",
						filter: {
							type: "existence",
							operator: "is-set"
						}
					}
				]
			}
		];

		return {
			data: {
				type: 'segment',
				attributes: {
					name: data.name,
					is_starred: false,
					definition: {
						condition_groups: combinedConditionGroups,
					},
				},
			},
		};
	}

	async getFieldMappings(): Promise<Record<string, string>> {
		const klaviyoIntegration = await this.integrationRepository.findOne({where: {name: 'Klaviyo'}});
		if (!klaviyoIntegration || !klaviyoIntegration.fieldMappings) {
			throw new HttpErrors.NotFound('Klaviyo integration field mappings not found.');
		}
		return JSON.parse(klaviyoIntegration.fieldMappings);
	}

	private async buildKlaviyoConditionsAsGroups(
		signals: MetricSegment[],
		fieldMappings: Record<string, string>,
		negate = false,
		apiKey: string
	): Promise<any> {
		const conditionGroups: {conditions: any[]}[] = [];

		for (const signal of signals) {
			if (!signal.query) {
				console.warn('Signal query is missing or empty. Skipping:', signal);
				continue;
			}

			try {
				const query = JSON.parse(signal.query);
				console.log('Signal:', signal);
				const hasIntegrationId = signal.integrationId !== null && signal.integrationId !== undefined;
				console.log('Has integration ID:', hasIntegrationId);
				await this.processQueryConditions(query, fieldMappings, negate, conditionGroups, hasIntegrationId, apiKey);
			} catch (error) {
				console.error('Error parsing signal query:', error, signal.query);
			}
		}
		console.log('Condition Groups:', JSON.stringify(conditionGroups, null, 2));
		return conditionGroups;
	}

	private async processQueryConditions(
		query: any,
		fieldMappings: Record<string, string>,
		negate: boolean,
		conditionGroups: {conditions: any[]}[],
		hasIntegrationId: boolean,
		apiKey: string
	) {

		if (!query.conditions || !Array.isArray(query.conditions)) {
			console.warn('No valid conditions found in query. Skipping:', JSON.stringify(query, null, 2));
			return;
		}

		for (const condition of query.conditions) {
			// Check if this is a nested OR condition
			if (condition.operator === 'or' && condition.conditions && Array.isArray(condition.conditions)) {
				// Handle nested OR: create a single group with all OR conditions
				const orGroup: {conditions: any[]} = {conditions: []};

				for (const orCondition of condition.conditions) {
					const klaviyoCondition = await this.convertToKlaviyoCondition(orCondition, fieldMappings, negate, hasIntegrationId, apiKey);
					orGroup.conditions.push(klaviyoCondition);
				}

				if (orGroup.conditions.length > 0) {
					conditionGroups.push(orGroup);
				}
			} else {
				// Handle regular conditions (including top-level OR)
				const klaviyoCondition = await this.convertToKlaviyoCondition(condition, fieldMappings, negate, hasIntegrationId, apiKey);

				if (query.operator === 'or') {
					// For top-level OR, add all conditions to the same group
					if (conditionGroups.length === 0) {
						conditionGroups.push({conditions: []});
					}
					conditionGroups[conditionGroups.length - 1].conditions.push(klaviyoCondition);
				} else {
					// For AND conditions, create separate groups
					conditionGroups.push({conditions: [klaviyoCondition]});
				}
			}
		}
	}

	private async convertToKlaviyoCondition(
		condition: any,
		fieldMappings: Record<string, string>,
		negate = false,
		hasIntegrationId = false,
		apiKey: string
	): Promise<any> {
		// Handle nested condition objects (like OR groups) - but this should now be handled in processQueryConditions
		if (condition.operator && condition.conditions && Array.isArray(condition.conditions)) {
			// This shouldn't happen anymore with the updated processQueryConditions, but keep as fallback
			console.warn('Nested condition detected in convertToKlaviyoCondition - this should be handled in processQueryConditions');
			if (condition.conditions.length > 0) {
				return await this.convertToKlaviyoCondition(
					condition.conditions[0],
					fieldMappings,
					negate,
					hasIntegrationId,
					apiKey
				);
			}
			throw new Error('Empty nested condition array');
		}

		const operatorMapping: Record<string, string> = {
			'>': 'greater-than',
			'>=': 'greater-than-or-equal',
			'<': 'less-than',
			'<=': 'less-than-or-equal',
			'=': 'equals',
			'!=': 'not-equals',
			'IS': 'not-set', //Assuming when using IS the value is NULL
			'IS NOT': 'is-set',
		};

		let operator = operatorMapping[condition.operator];
		if (!operator) {
			throw new Error(`Unsupported operator: ${condition.operator}`);
		}

		if (negate) {
			operator = this.getNegatedOperator(operator);
		}
		if (hasIntegrationId) {
			console.log('Has integration ID');

			// Handle profile-marketing-consent signal type
			if (condition.field === 'can_receive_marketing') {
				return {
					type: 'profile-marketing-consent',
					consent: {
						channel: 'email',
						can_receive_marketing: !negate ? condition.value === 'true' : condition.value != 'true',
						consent_status: {
							subscription: 'any'
						}
					}
				};
			}

			console.log('Condition:', condition);
			let transformCondition = await this.transformCondition(condition, operator, apiKey);
			console.log('Transformed Condition:', transformCondition);
			return transformCondition;
		}

		const mappedField = fieldMappings[condition.field.toLowerCase()] || condition.field;
		const filterType = this.getFilterType(condition.value);

		return {
			type: 'profile-property',
			property: `properties['${mappedField}']`,
			filter: {
				type: filterType,
				operator: operator,
				...(filterType !== 'existence' && {
					value: filterType === 'numeric' ? parseInt(condition.value, 10) : condition.value,
				  }),
			},
		};
	}

	private async transformCondition(condition: any, operator: string, apiKey: string): Promise<any> {
		// Handle metric-based conditions
		const metricName = condition.field;
		try {
			const response = await axios.get(`${this.apiBaseUrl}metrics/`, {
				headers: this.getHeaders(apiKey),
			});

			const metrics = response.data.data;
			const matchingMetric = metrics.find((metric: any) => metric.attributes.name === metricName);

			if (!matchingMetric) {
				throw new Error(`Metric not found: ${metricName}`);
			}

			return {
				type: "profile-metric",
				metric_id: matchingMetric.id,
				measurement: "count",
				measurement_filter: {
					type: "numeric",
					operator: operator,
					value: parseInt(condition.value, 10),
				},
				timeframe_filter: {
					type: "date",
					operator: "in-the-last",
					unit: "day",
					quantity: condition.days || 30,
				},
				metric_filters: null,
			};
		} catch (error: any) {
			console.error('Error fetching metrics from Klaviyo:', error.response?.data || error.message);
			throw new Error('Failed to retrieve metric ID from Klaviyo');
		}
	}

	private getNegatedOperator(operator: string): string {
		const negatedOperatorMapping: Record<string, string> = {
			equals: 'not-equals',
			'not-equals': 'equals',
			'greater-than': 'less-than-or-equal',
			'greater-than-or-equal': 'less-than',
			'less-than': 'greater-than-or-equal',
			'less-than-or-equal': 'greater-than',
			'is-set': 'not-set',
			'not-set': 'is-set',
		};
		return negatedOperatorMapping[operator] || operator;
	}

	private getFilterType(value: any): string {
		if(value === 'NULL') {
			return 'existence';
		}
		if (!isNaN(value) && value !== null && value !== '') {
			return 'numeric';
		}
		return 'string';
	}

	/**
	 * Get all segments from Klaviyo
	 * @param orgId The organization ID
	 * @returns Array of segments
	 */
	async getSegments(orgId: number): Promise<any[]> {
		const apiKey = await this.getKlaviyoApiKey(orgId);

		try {
			const segments: any[] = [];
			const pageSize = 100;
			let nextUrl: string | null = `https://a.klaviyo.com/api/segments/`; // ?page[size]=${pageSize}`;

			while (nextUrl) {
				const response: any = await axios.get(nextUrl, {
					headers: this.getHeaders(apiKey)
				});

				segments.push(...response.data.data);

				// Get the next page URL from the response links
				nextUrl = response.data.links?.next || null;
			}

			return segments;
		} catch (error: any) {
			console.error('Error getting segments:', error.response?.data || error.message);
			throw new HttpErrors.InternalServerError('Failed to get segments from Klaviyo');
		}
	}

	async checkSegmentExists(orgId: number, segmentName: string): Promise<string | null> {
		const segments = await this.getSegments(orgId);
		const matchingSegment = segments.find((segment: any) =>
			segment.attributes.name === segmentName
		);
		return matchingSegment ? matchingSegment.id : null;
	}

	private async getProfilesForMetric(
		metricName: string,
		apiKey: string
	): Promise<string[]> {
		try {
			// Get all segments
			const response = await axios.get<{
				data: Array<{
					id: string;
					attributes: {
						name: string;
					};
				}>;
			}>(
				`${this.apiBaseUrl}segments/`,
				{ headers: this.getHeaders(apiKey) }
			);

			// Find the segment with matching name
			const segments = response.data.data;
			const matchingSegment = segments.find((segment: any) =>
				segment.attributes.name === metricName
			);

			if (!matchingSegment) {
				throw new Error(`No segment found for metric: ${metricName}`);
			}

			// Get members of the segment
			const customerIds = new Set<string>();
			const pageSize = 100;
			let nextCursor: string | null = null;

			do {
				const params: any = {
					'page[size]': pageSize,
					'fields[profile]': 'properties'
				};

				if (nextCursor) {
					params['page[cursor]'] = nextCursor;
				}

				const membersResponse = await axios.get(
					`${this.apiBaseUrl}segments/${matchingSegment.id}/profiles`,
					{
						headers: this.getHeaders(apiKey),
						params
					}
				);

				const profiles = membersResponse.data.data;
				for (const profile of profiles) {
					const customerId = profile.attributes.properties?.['Raleon Identity Value'];
					if (customerId) {
						customerIds.add(customerId);
					}
				}

				// Get the next cursor from the links
				nextCursor = membersResponse.data.links?.next
					? new URL(membersResponse.data.links.next).searchParams.get('page[cursor]')
					: null;

			} while (nextCursor);

			return Array.from(customerIds);
		} catch (error: any) {
			console.error('Error getting profiles for metric:', error.response?.data || error.message);
			throw new Error('Failed to get profiles for metric from Klaviyo');
		}
	}

	/**
	 * Get customer IDs for profiles that match a specific metric criteria
	 * @param orgId Organization ID
	 * @param metricName Name of the Klaviyo metric
	 * @param operator Comparison operator
	 * @param value Value to compare against
	 * @param days Optional time window in days (default 30)
	 */
	async getMetricCustomers(
		orgId: number,
		metricName: string,
		operator: string,
		value: number,
		days: number = 30
	): Promise<string[]> {
		const apiKey = await this.getKlaviyoApiKey(orgId);

		try {
			// Create or get segment with the specified criteria
			// const condition = {
			// 	field: metricName,
			// 	operator: operator,
			// 	value: value.toString(),
			// 	days: days
			// };
			// await this.transformCondition(condition, this.convertToKlaviyoOperator(operator));

			// Get profiles from the segment
			return await this.getProfilesForMetric(metricName, apiKey);
		} catch (error: any) {
			console.error('Error getting metric customers from Klaviyo:', error.response?.data || error.message);
			throw new Error('Failed to get metric customers from Klaviyo');
		}
	}

	/**
	 * Check if Klaviyo profiles have been synced with Raleon Identity Value
	 * @param orgId The organization ID
	 * @returns Object indicating if profiles are synced
	 */
	async checkProfileSync(orgId: number): Promise<{synced: boolean}> {
		try {
			const apiKey = await this.getKlaviyoApiKey(orgId);

			// Get profiles from Klaviyo and check if they have Raleon Identity Value
			const response = await axios.get(`${this.apiBaseUrl}profiles/`, {
				headers: this.getHeaders(apiKey),
				params: {
					'page[size]': 10,  // Check first 10 profiles
					'sort': 'created',  // Get oldest profiles first
					'fields[profile]': 'properties'
				}
			});

			const profiles = response.data.data || [];
			if (profiles.length === 0) {
				return { synced: true }; // No profiles to check
			}

			// Check if profiles have Raleon Identity Value
			const hasRaleonIdentity = profiles.some((profile: any) =>
				profile.attributes.properties && profile.attributes.properties['Raleon Identity Value']
			);

			return { synced: hasRaleonIdentity };
		} catch (error) {
			console.error('Error checking Klaviyo profile sync:', error);
			throw new HttpErrors.InternalServerError('Failed to check Klaviyo profile sync');
		}
	}

	private convertToKlaviyoOperator(operator: string): string {
		const operatorMapping: Record<string, string> = {
			'>': 'greater-than',
			'>=': 'greater-than-or-equal',
			'<': 'less-than',
			'<=': 'less-than-or-equal',
			'=': 'equals',
			'!=': 'not-equals',
		};
		return operatorMapping[operator] || 'equals';
	}

	async getUniversalContentFooter(orgId: number): Promise<any> {
		try {
			const apiKey = await this.getKlaviyoApiKey(orgId);
			const universalContentAPI = 'https://a.klaviyo.com/api/template-universal-content';
			const universalContentResponse = await axios.get(universalContentAPI, {
				headers: this.getHeaders(apiKey)
			});

			//iterate through and look for footer
			for(const template of universalContentResponse.data.data) {
				if(template.attributes.name.toLowerCase() === 'footer') {
					return {
						content: template.attributes.definition.data.content,
						styles: template.attributes.definition.data.styles
					}
				}
			}
			return {};
		} catch (error: any) {
			console.error('Error getting universal content footer:', error.response?.data || error.message);
			return {};
		}
	}

	/**
	 * Create a new email template in Klaviyo
	 * @param orgId The organization ID
	 * @param params Template parameters (name and HTML content)
	 * @returns Template ID
	 */
	async createTemplate(orgId: number, params: CreateTemplateParams): Promise<string> {
		const apiKey = await this.getKlaviyoApiKey(orgId);
		const url = `https://a.klaviyo.com/api/templates`;

		try {
			const response = await axios.post(url, {
				data: {
					type: "template",
					attributes: {
						name: params.name,
						editor_type: params.editorType || 'USER_DRAGGABLE',
						html: params.html
					}
				}
			}, {
				headers: this.getHeaders(apiKey)
			});

			console.log('Klaviyo template created:', response.data);
			return response.data.data.id;
		} catch (error: any) {
			console.error('Error creating Klaviyo template:', error.response?.data || error.message);
			const errorMessage = error.response?.data?.errors?.[0]?.detail ||
							   error.response?.data?.message ||
							   error.message ||
							   'Failed to create template in Klaviyo';
			throw new HttpErrors.InternalServerError(errorMessage);
		}
	}

	/**
	 * Create a new email campaign in Klaviyo
	 * @param orgId The organization ID
	 * @param params Campaign parameters
	 * @returns Campaign
	 */
	async createCampaign(orgId: number, params: CreateCampaignParams): Promise<any> {
		const apiKey = await this.getKlaviyoApiKey(orgId);
		const url = `https://a.klaviyo.com/api/campaigns`;

		try {
			const response = await axios.post(url, {
				data: {
					type: "campaign",
					attributes: {
						name: params.name,
						audiences: {
							included: [params.listId]
						},
						send_strategy: {
							options_static: {
								datetime: new Date(params.sendTime).toISOString(),
								"is_local": true,
								"send_past_recipients_immediately": false
							},
							method: 'static',
						},
						"send_options": {
						  "use_smart_sending": true
						},
						"campaign-messages": {
							data: [
								{
									type: 'campaign-message',
									attributes: {
										channel: 'email',
										content: {
											subject: params.subject,
											preview_text: params.previewText,
										},
										"render_options": {
											"shorten_links": true,
											"add_org_prefix": true,
											"add_info_link": true,
											"add_opt_out_language": false
										},
									}
								}
							]
						}
					}
				}
			}, {
				headers: this.getHeaders(apiKey)
			});

			console.log('Klaviyo campaign created:', response.data);
			// Return both campaign ID and message ID
			return {
				campaignId: response.data.data.id,
				messageId: response.data.data.relationships['campaign-messages'].data[0].id
			};
		} catch (error: any) {
			console.error('Error creating Klaviyo campaign:', error.response?.data || error.message);
			const errorMessage = error.response?.data?.errors?.[0]?.detail ||
							   error.response?.data?.message ||
							   error.message ||
							   'Failed to create campaign in Klaviyo';
			throw new HttpErrors.InternalServerError(errorMessage);
		}
	}

	/**
	 * Assign a template to a campaign message
	 * @param orgId The organization ID
	 * @param params Parameters containing campaign ID, message ID, and template ID
	 */
	async assignTemplateToCampaign(orgId: number, params: AssignTemplateParams): Promise<void> {
		const apiKey = await this.getKlaviyoApiKey(orgId);
		const url = `https://a.klaviyo.com/api/campaign-message-assign-template`;

		try {
			await axios.post(url, {
				data: {
					type: "campaign-message",
					id: params.messageId,
					relationships: {
						template: {
							data: {
								type: 'template',
								id: params.templateId
							}
						}
					}
				}
			}, {
				headers: this.getHeaders(apiKey)
			});

			console.log('Template assigned to campaign message successfully');
		} catch (error: any) {
			console.error('Error assigning template to campaign:', error.response?.data || error.message);
			const errorMessage = error.response?.data?.errors?.[0]?.detail ||
							   error.response?.data?.message ||
							   error.message ||
							   'Failed to assign template to campaign in Klaviyo';
			throw new HttpErrors.InternalServerError(errorMessage);
		}
	}

	/**
	 * Schedule a campaign to be sent
	 * @param orgId The organization ID
	 * @param campaignId The ID of the campaign to send
	 */
	async scheduleCampaign(orgId: number, campaignId: string): Promise<void> {
		const apiKey = await this.getKlaviyoApiKey(orgId);
		const url = `https://a.klaviyo.com/api/campaign-send-jobs`;

		try {
			await axios.post(url, {
				data: {
					type: "campaign-send-job",
					id: campaignId
				}
			}, {
				headers: this.getHeaders(apiKey)
			});

			console.log('Campaign scheduled successfully');
		} catch (error: any) {
			console.error('Error scheduling campaign:', error.response?.data || error.message);
			const errorMessage = error.response?.data?.errors?.[0]?.detail ||
							   error.response?.data?.message ||
							   error.message ||
							   'Failed to schedule campaign in Klaviyo';
			throw new HttpErrors.InternalServerError(errorMessage);
		}
	}

	/**
	 * Update an existing email template in Klaviyo
	 * @param orgId The organization ID
	 * @param templateId The ID of the template to update
	 * @param params Template update parameters (name and/or HTML content)
	 * @returns Updated template ID
	 */
	async updateTemplate(orgId: number, templateId: string, params: UpdateTemplateParams): Promise<string> {
		const apiKey = await this.getKlaviyoApiKey(orgId);
		const url = `https://a.klaviyo.com/api/templates/${templateId}`;

		try {
			const response = await axios.patch(url, {
				data: {
					type: "template",
					id: templateId,
					attributes: {
						...(params.name && { name: params.name }),
						...(params.html && { html: params.html }),
					}
				}
			}, {
				headers: this.getHeaders(apiKey)
			});

			console.log('Klaviyo template updated:', response.data);
			return response.data.data.id;
		} catch (error: any) {
			console.error('Error updating Klaviyo template:', error.response?.data || error.message);
			const errorMessage = error.response?.data?.errors?.[0]?.detail ||
							   error.response?.data?.message ||
							   error.message ||
							   'Failed to update template in Klaviyo';
			throw new HttpErrors.InternalServerError(errorMessage);
		}
	}

	/**
	 * Update an existing email campaign in Klaviyo
	 * @param orgId The organization ID
	 * @param campaignId The ID of the campaign to update
	 * @param params Campaign update parameters
	 * @returns Updated campaign data
	 */
	async updateCampaign(orgId: number, campaignId: string, params: UpdateCampaignParams): Promise<any> {
		const apiKey = await this.getKlaviyoApiKey(orgId);
		const url = `https://a.klaviyo.com/api/campaigns/${campaignId}`;

		try {
			const response = await axios.patch(url, {
				data: {
					type: "campaign",
					id: campaignId,
					attributes: {
						...(params.name && { name: params.name }),
						...(params.sendTime && {
							send_strategy: {
								options_static: {
									datetime: new Date(params.sendTime).toISOString(),
									is_local: true,
									send_past_recipients_immediately: false
								},
								method: 'static',
							}
						})
					}
				}
			}, {
				headers: this.getHeaders(apiKey)
			});

			// If subject or preview text need to be updated, we need to update the campaign message
			if (params.subject || params.previewText) {
				const messageId = response.data.data.relationships['campaign-messages'].data[0].id;
				await this.updateCampaignMessage(orgId, messageId, {
					subject: params.subject,
					previewText: params.previewText
				});
			}

			console.log('Klaviyo campaign updated:', response.data);
			return response.data;
		} catch (error: any) {
			console.error('Error updating Klaviyo campaign:', error.response?.data || error.message);
			const errorMessage = error.response?.data?.errors?.[0]?.detail ||
							   error.response?.data?.message ||
							   error.message ||
							   'Failed to update campaign in Klaviyo';
			throw new HttpErrors.InternalServerError(errorMessage);
		}
	}

	/**
	 * Update a campaign message with new subject and/or preview text
	 * @param orgId The organization ID
	 * @param messageId The ID of the campaign message to update
	 * @param params The subject and/or preview text to update
	 */
	private async updateCampaignMessage(orgId: number, messageId: string, params: { subject?: string, previewText?: string }): Promise<void> {
		const apiKey = await this.getKlaviyoApiKey(orgId);
		const url = `https://a.klaviyo.com/api/campaign-messages/${messageId}`;

		try {
			await axios.patch(url, {
				data: {
					type: "campaign-message",
					id: messageId,
					attributes: {
						content: {
							...(params.subject && { subject: params.subject }),
							...(params.previewText && { preview_text: params.previewText })
						}
					}
				}
			}, {
				headers: this.getHeaders(apiKey)
			});

			console.log('Campaign message updated successfully');
		} catch (error: any) {
			console.error('Error updating campaign message:', error.response?.data || error.message);
			throw new HttpErrors.InternalServerError('Failed to update campaign message in Klaviyo');
		}
	}

	/**
	 * Retrieve all images from a Klaviyo account with pagination and filtering
	 * @param orgId The organization ID
	 * @param params Optional parameters for pagination and filtering
	 * @returns Images data with pagination info
	 */
	async getImages(orgId: number, params: GetImagesParams = {}): Promise<any> {
		const apiKey = await this.getKlaviyoApiKey(orgId);
		let url = `${this.apiBaseUrl}images`;

		// Build query parameters
		const queryParams = new URLSearchParams();

		// Add pagination parameters
		if (params.pageSize) {
			queryParams.append('page[size]', params.pageSize.toString());
		}
		if (params.pageCursor) {
			queryParams.append('page[cursor]', params.pageCursor);
		}

		// Add name filter if provided
		if (params.nameFilter) {
			const filterValue = `"${params.nameFilter}"`; // Wrap the value in quotes
			queryParams.append('filter', `contains(name,${filterValue})`);
		}

		// add image fields
		queryParams.append('fields[image]', 'image_url,name,size');

		// Append query parameters to URL if any exist
		const queryString = queryParams.toString();
		if (queryString) {
			url += `?${queryString}`;
		}

		try {
			const response = await axios.get(url, {
				headers: this.getHeaders(apiKey)
			});

			console.log('Klaviyo images retrieved successfully');
			return response.data;
		} catch (error: any) {
			console.error('Error retrieving Klaviyo images:', error.response?.data || error.message);
			const errorMessage = error.response?.data?.errors?.[0]?.detail ||
							   error.response?.data?.message ||
							   error.message ||
							   'Failed to retrieve images from Klaviyo';
			throw new HttpErrors.InternalServerError(errorMessage);
		}
	}
}
