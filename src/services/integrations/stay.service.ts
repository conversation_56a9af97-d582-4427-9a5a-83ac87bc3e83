import {BindingScope, inject, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import { OrganizationOrganizationKeysController, DEFAULT_SECRET_KEY_NAME } from '../../controllers/organization-organization-keys.controller';
import {OrganizationIntegrationDetailsRepository} from '../../repositories/organization-integration-details.repository';
import {OrganizationKeysRepository} from '../../repositories';
import fetch from 'node-fetch';

const STAY_INTEGRATION_ID = 5;
const STAY_API_KEY_NAME = 'Stay-API-Key';

@injectable()
export class StayIntegration {

	constructor(
		@repository(OrganizationIntegrationDetailsRepository)
		protected organizationIntegrationDetailsRepository: OrganizationIntegrationDetailsRepository,
		@repository(OrganizationKeysRepository)
		protected organizationKeysRepository: OrganizationKeysRepository,
		@inject('controllers.OrganizationOrganizationKeysController')
		private organizationKeysController: OrganizationOrganizationKeysController,
	) {}

	async enableStay(orgId: number): Promise<any> {
		const orgIntegrationDetails = await this.organizationIntegrationDetailsRepository.findOne({
			where: {
				orgId: orgId,
				integrationId: STAY_INTEGRATION_ID,
			}
		});

		if (!orgIntegrationDetails) {
			await this.organizationIntegrationDetailsRepository.create({
				orgId: orgId,
				integrationId: STAY_INTEGRATION_ID,
				enabled: true,
			});
		} else {
			await this.organizationIntegrationDetailsRepository.updateById(orgIntegrationDetails.id, {
				enabled: true,
			});
		}

		return {
			success: true,
		};
	}

	async disableStay(orgId: number): Promise<any> {
		const orgIntegrationDetails = await this.organizationIntegrationDetailsRepository.findOne({
			where: {
				orgId: orgId,
				integrationId: STAY_INTEGRATION_ID,
			}
		});

		if (!orgIntegrationDetails) {
			await this.organizationIntegrationDetailsRepository.create({
				orgId: orgId,
				integrationId: STAY_INTEGRATION_ID,
				enabled: false,
			});
		} else {
			await this.organizationIntegrationDetailsRepository.updateById(orgIntegrationDetails.id, {
				enabled: false,
			});
		}

		return {
			success: true,
		};
	}

	async saveStayKeys(orgId: number, data: any): Promise<any> {
		if (!data.apiKey) {
			return {
				success: false,
				message: 'Missing API Key'
			};
		}

		const orgIntegrationKeys = await this.organizationKeysRepository.find({
			where: {
				organizationId: orgId,
				key: STAY_API_KEY_NAME,
			}
		});

		if (orgIntegrationKeys && orgIntegrationKeys.length > 0) {
			for (let key of orgIntegrationKeys) {
				if (key.key === STAY_API_KEY_NAME) {
					await this.organizationKeysRepository.updateById(key.id, {
						value: data.apiKey
					});
				}
			}
		}

		if (!orgIntegrationKeys || orgIntegrationKeys.length === 0) {
			await this.organizationKeysController.create({
				key: STAY_API_KEY_NAME,
				value: data.apiKey,
				secretKeyId: DEFAULT_SECRET_KEY_NAME
			}, orgId);
		}

		return {
			success: true,
		};
	}

	async deleteStayKeys(orgId: number): Promise<any> {
		const orgIntegrationKeys = await this.organizationKeysRepository.find({
			where: {
				organizationId: orgId,
				key: STAY_API_KEY_NAME,
			}
		});

		if (orgIntegrationKeys && orgIntegrationKeys.length > 0) {
			for (let key of orgIntegrationKeys) {
				await this.organizationKeysRepository.deleteById(key.id);
			}
		}

		return {
			success: true,
		};
	}

	async isStayConnected(orgId: number): Promise<boolean> {
		const orgIntegrationKeys = await this.organizationKeysRepository.find({
			where: {
				and: [
					{ organizationId: orgId, },
					{ key: STAY_API_KEY_NAME },
				],
			}
		});

		const keysInstalled = orgIntegrationKeys && orgIntegrationKeys.length === 1;
		if (!keysInstalled) return false;

		const stayApiKey = await this.organizationKeysController.findKey(STAY_API_KEY_NAME, orgId);
		const webhookListeners = await this.getWebhookListeners({ apiKey: stayApiKey[0].value });
		return keysInstalled && webhookListeners && webhookListeners.length > 0;
	}

	async createWebhookListener(orgId: number, data: any, url: string): Promise<any> {
		const existingListeners = await this.getWebhookListeners(data);
		if (!existingListeners || existingListeners.length === 0) {
			const newListeners = await this.createWebhookListeners(orgId, data, url);
			if (newListeners && newListeners.length) {
				return {
					status: 200,
					message: 'Webhook listeners registered.',
				}
			} else {
				return {
					status: 500,
					message: 'Failed to register webhook listener.',
				}
			}
		} else {
			return {
				status: 200,
				message: 'Webhook listeners already registered.',
			}
		}
	}

	async getWebhookListeners(data: any): Promise<any> {
		const response = await fetch('https://api.retextion.com/api/v2/webhooks/', {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				'X-RETEXTION-ACCESS-TOKEN': data.apiKey,
			}
		})

		return response.json();
	}

	async createWebhookListeners(orgId: number, data: any, url: string): Promise<any> {
		const topics = ['SUBSCRIPTION', 'SUBSCRIPTION_UPDATE'];
		const results = [];
		for (let topic of topics) {
			const result = await fetch('https://api.retextion.com/api/v2/webhooks/', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'X-RETEXTION-ACCESS-TOKEN': data.apiKey,
				},
				body: JSON.stringify({
					event: topic,
					url: `${url}?name=stay`,
				})
			});
			results.push(await result.json());
		}
		return results;
	}

	async deleteWebhookListeners(orgId: number, data: any): Promise<any> {
		const existingListeners = await this.getWebhookListeners(data);
		if (existingListeners && existingListeners.length) {
			for (let listener of existingListeners) {
				await fetch(`https://api.retextion.com/api/v2/webhooks/${listener.id}`, {
					method: 'DELETE',
					headers: {
						'Content-Type': 'application/json',
						'X-RETEXTION-ACCESS-TOKEN': data.apiKey,
					}
				})
			}
			return {
				status: 200,
				message: 'Webhook listeners deleted.',
			}
		} else {
			return {
				status: 200,
				message: 'No webhook listeners found.',
			}
		}
	}
}
