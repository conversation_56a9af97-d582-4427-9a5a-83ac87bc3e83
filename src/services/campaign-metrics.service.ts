import {injectable, /* inject, */ BindingScope, service} from '@loopback/core';
import {repository} from '@loopback/repository';
import {
	CampaignMetric,
	CampaignMetricsContainer,
	CampaignWithRelations,
	JourneyWithRelations,
	QuestWithRelations,
} from '../models';
import {
	CampaignRepository,
	GoalRepository,
	QuestRepository,
} from '../repositories';
import {HttpErrors} from '@loopback/rest';

const fetch = require('node-fetch')
const aws4 = require('aws4')

const CONVERSION_API = 'bcie42aco8.execute-api.us-east-1.amazonaws.com';
const AWS_ACCESS_KEY = process.env.API_ACCESS_KEY;
const AWS_SECRET_KEY = process.env.API_SECRET_KEY;

export function getURL(path: string, method: string, body?: any, host?: string, stringify = true) {
	const opts = {
		host: host || CONVERSION_API,
		path: path,
		region: 'us-east-1',
		service: 'execute-api',
		mode: 'cors',
		body: body != undefined ? (stringify ? JSON.stringify(body) : body) : undefined,
		headers: {
			'Content-Type': 'application/json',
		},
		method: method
	}
	return aws4.sign(opts, {accessKeyId: AWS_ACCESS_KEY, secretAccessKey: AWS_SECRET_KEY});
}

@injectable({scope: BindingScope.TRANSIENT})
export class CampaignMetricsService {
	constructor(
		@repository(CampaignRepository)
		private campaignRepository: CampaignRepository,
		@repository(QuestRepository)
		private questRepository: QuestRepository,
		@repository(GoalRepository)
		private goalRepository: GoalRepository,
	) { }

	async getMetricsForCampaigns(
		campaignIds: Array<number>
	): Promise<Array<CampaignMetricsContainer>> {
		const campaigns = await this.campaignRepository.find({
			where: {
				id: {
					inq: campaignIds,
				},
			},
			include: [
				{
					relation: 'quests',
					scope: {
						include: [
							'goals',
							{
								relation: 'journeys',
								scope: {
									include: [
										{
											relation: 'raleonUser',
											scope: {
												include: [
													{
														relation: 'raleonUserIdentities',
													},
												],
											},
										},
									],
								},
							},
						],
					},
				},
			],
		});

		if (campaignIds.length !== campaigns.length) {
			throw new Error('Campaign not found');
		}

		return campaigns.map((x) => {
			const metrics = this.generateMetricsForCampaign(x);
			return new CampaignMetricsContainer({
				campaignId: x.id || -1,
				metrics,
			});
		});
	}

	async getMetricsForCampaignsEventStream(
		campaignIds: Array<number>
	): Promise<Array<CampaignMetricsContainer>> {
		const campaigns = await this.campaignRepository.find({
			where: {
				id: {
					inq: campaignIds,
				},
			},
			include: [
				{
					relation: 'quests',
					scope: {
						include: [
							'goals',
							{
								relation: 'journeys',
								scope: {
									include: [
										{
											relation: 'raleonUser',
											scope: {
												include: [
													{
														relation: 'raleonUserIdentities',
													},
												],
											},
										},
									],
								},
							},
						],
					},
				},
			],
		});

		if (campaignIds.length !== campaigns.length) {
			throw new Error('Campaign not found');
		}

		return Promise.all(campaigns.map(async (x) => {
			let metrics: any = [];
			if (x.type == 'NativeQuest') {
				metrics = await this.generateMetricsForNativeQuest(x.quests?.[0]);
				return new CampaignMetricsContainer({
					campaignId: x.id || -1,
					metrics,
				});
			} else {

				metrics = await this.generateMetricsForActionPrompt(x.quests?.[0]);
				return new CampaignMetricsContainer({
					campaignId: x.id || -1,
					metrics,
				});
			}
		}));
	}

	async getMetricsForQuests(
		questIds: Array<number>,
	): Promise<Array<CampaignMetricsContainer>> {
		const quests = await this.questRepository.find({
			where: {
				id: {
					inq: questIds,
				},
			},
			include: [
				'goals',
				{
					relation: 'journeys',
					scope: {
						include: [
							{
								relation: 'raleonUser',
								scope: {
									include: [
										{
											relation: 'raleonUserIdentities',
										},
									],
								},
							},
						],
					},
				},
			],
		});

		if (questIds.length !== quests.length) {
			throw new Error('Quest not found');
		}

		return quests.map(x => {
			const metrics = this.generateMetricsForQuest(x);
			return new CampaignMetricsContainer({
				campaignId: x.campaignId || -1,
				questId: x.id || -1,
				metrics,
			});
		});
	}

	async getMetricsForNativeQuest(
		campaignId: number,
	): Promise<Array<CampaignMetricsContainer>> {
		//TODO: this could be more than one quest in the future
		const quest = (await this.getQuestWithGoalsAndJourneys(campaignId))?.[0];

		if (!quest) {
			throw new Error('Quest not found');
		}

		const metrics = await this.generateMetricsForNativeQuest(quest);
		return [
			new CampaignMetricsContainer({
				campaignId: quest.campaignId || -1,
				questId: quest.id || -1,
				metrics,
			}),
		];
	}

	async getMetricsForActionPrompt(
		campaignId: number
	): Promise<Array<CampaignMetricsContainer>> {
		//TODO: this could be more than one quest in the future
		const quest = (await this.getQuestWithGoalsAndJourneys(campaignId))?.[0];

		if (!quest) {
			throw new Error('Quest not found');
		}

		const metrics = await this.generateMetricsForActionPrompt(quest);
		return [
			new CampaignMetricsContainer({
				campaignId: quest.campaignId || -1,
				questId: quest.id || -1,
				metrics,
			}),
		];
	}

	private async generateMetricsForActionPrompt(quest: QuestWithRelations): Promise<Array<CampaignMetric>> {
		const campaignMetrics = [
			'CAMPAIGN_METRIC_TOTAL_VIEWS',
			'CAMPAIGN_METRIC_TOTAL_UNIQUE_VIEWS',
			'CAMPAIGN_METRIC_QUEST_COMPLETED',
		];

		const [
			totalViews,
			uniqueViews,
			questComplete,
		] = await Promise.all(campaignMetrics.map(async (metric) =>
			this.getCampaignMetric(metric, quest.campaignId)
		));

		const totalviewsMetric = new CampaignMetric();
		totalviewsMetric.metricName = 'total_views';
		totalviewsMetric.metricValue = totalViews?.Items?.[0]?.data || '0';

		const uniqueViewsMetric = new CampaignMetric();
		uniqueViewsMetric.metricName = 'unique_views';
		uniqueViewsMetric.metricValue = uniqueViews?.Items?.[0]?.data || '0';

		const questCompleted = new CampaignMetric();
		questCompleted.metricName = 'quest_completed';
		questCompleted.metricValue = questComplete?.Items?.[0]?.data || '0';

		const conversionRate = new CampaignMetric();
		conversionRate.metricName = 'conversion_rate';
		if (parseInt(questCompleted.metricValue) > 0 && parseInt(uniqueViewsMetric.metricValue) > 0) {
			conversionRate.metricValue = (
				(parseInt(questCompleted.metricValue)/ parseInt(uniqueViewsMetric.metricValue)) * 100
			).toString();
		} else {
			conversionRate.metricValue = '0';
		}

		const usersMetric = new CampaignMetric();
		usersMetric.metricName = 'journeys';
		usersMetric.metricValue = quest.journeys;

		return [
			totalviewsMetric,
			uniqueViewsMetric,
			questCompleted,
			conversionRate,
			usersMetric,
		];
	}

	private async generateMetricsForNativeQuest(quest: QuestWithRelations): Promise<Array<CampaignMetric>> {
		const campaignMetrics = [
			'CAMPAIGN_METRIC_TOTAL_VIEWS',
			'CAMPAIGN_METRIC_TOTAL_UNIQUE_VIEWS',
			'CAMPAIGN_METRIC_QUEST_COMPLETED',
			'CAMPAIGN_METRIC_QUEST_ACTIONS_COMPLETED',
			'CAMPAIGN_METRIC_QUEST_ACTIONS_COMPLETED_WALLETS',
			'CAMPAIGN_METRIC_ENGAGEMENT',
			'CAMPAIGN_METRIC_STARTED_BUT_NOT_COMPLETED',
		]

		const [
			totalViews,
			uniqueViews,
			questComplete,
			questActionsComplete,
			questActionsCompleteWallets,
			questEngagement,
			startedButNotCompleted,
		] = await Promise.all(campaignMetrics.map(async (metric) =>
			this.getCampaignMetric(metric, quest.campaignId)
		));

		const totalviewsMetric = new CampaignMetric();
		totalviewsMetric.metricName = 'total_views';
		totalviewsMetric.metricValue = totalViews?.Items?.[0]?.data || '0';

		const uniqueViewsMetric = new CampaignMetric();
		uniqueViewsMetric.metricName = 'unique_views';
		uniqueViewsMetric.metricValue = uniqueViews?.Items?.[0]?.data || '0';

		const questCompleted = new CampaignMetric();
		questCompleted.metricName = 'quest_completed';
		questCompleted.metricValue = questComplete?.Items?.[0]?.data || '0';

		const questActionsCompleted = new CampaignMetric();
		questActionsCompleted.metricName = 'quest_actions_completed';
		questActionsCompleted.metricValue = questActionsComplete?.Items?.[0]?.data || '0';

		const engagementPercentage = new CampaignMetric();
		engagementPercentage.metricName = 'engagement_percentage';
		const engagementData = questEngagement?.Items?.[0]?.data || '0';
		if (parseInt(engagementData) > 0 && parseInt(uniqueViewsMetric.metricValue) > 0) {
			engagementPercentage.metricValue = (
				(parseInt(engagementData) / parseInt(uniqueViewsMetric.metricValue)) * 100
			).toString();
		} else {
			engagementPercentage.metricValue = 'N/A';
		}

		const startedButNotCompletedPct = new CampaignMetric();
		startedButNotCompletedPct.metricName = 'started_but_not_completed';
		const startedButNotCompletedData = startedButNotCompleted?.Items?.[0]?.data || '0';
		if (parseInt(startedButNotCompletedData) > 0 && parseInt(uniqueViewsMetric.metricValue) > 0) {
			startedButNotCompletedPct.metricValue = (
				(parseInt(startedButNotCompletedData) / parseInt(uniqueViewsMetric.metricValue)) * 100
			).toString();
		} else {
			startedButNotCompletedPct.metricValue = '0';
		}

		const conversionRate = new CampaignMetric();
		conversionRate.metricName = 'conversion_rate';
		if (parseInt(questCompleted.metricValue) > 0 && parseInt(uniqueViewsMetric.metricValue) > 0) {
			conversionRate.metricValue = (
				(parseInt(questCompleted.metricValue)/ parseInt(uniqueViewsMetric.metricValue)) * 100
			).toString();
		} else {
			conversionRate.metricValue = '0';
		}

		const questActionsCompletedWallets = questActionsCompleteWallets?.Items?.[0]?.data || [];
		const actionsCompletedAddresses = [];
		for (const completedAction of questActionsCompletedWallets) {
			const actionCompletedWallets = new CampaignMetric();
			const evt = completedAction.eventType;
			const actionId = evt.substring(evt.indexOf('action-') + 7, evt.indexOf('-completed'))

			const arrayString = completedAction.addresses.substring(1, completedAction.addresses.length - 1);
			const formattedValues = arrayString ? arrayString.split(',').map((x: string) => x.trim()) : [];

			actionCompletedWallets.metricName = `addresses_${actionId}_completed`;
			actionCompletedWallets.metricValue = formattedValues;
			actionsCompletedAddresses.push(actionCompletedWallets);
		}

		const usersMetric = new CampaignMetric();
		usersMetric.metricName = 'journeys';
		usersMetric.metricValue = quest.journeys;

		return [
			totalviewsMetric,
			uniqueViewsMetric,
			questActionsCompleted,
			questCompleted,
			engagementPercentage,
			startedButNotCompletedPct,
			conversionRate,
			...actionsCompletedAddresses,
			usersMetric,
		];
	}

	private generateMetricsForCampaign(
		campaign: CampaignWithRelations,
	): Array<CampaignMetric> {

		const metrics = campaign.quests.reduce(
			(acc, quest: QuestWithRelations) => acc.concat(this.generateMetrics(quest, quest.journeys || [])),
			[] as Array<CampaignMetric>,
		);

		return metrics;
	}

	private generateMetricsForQuest(
		quest: QuestWithRelations,
	): Array<CampaignMetric> {
		return this.generateMetrics(quest, quest.journeys || []);
	}

	private generateMetrics(
		quest: QuestWithRelations,
		journeys: Array<JourneyWithRelations>
	): Array<CampaignMetric> {
		const uniqueViews = journeys.length;
		const totalViews = journeys.reduce(
			(acc, journey) => acc + (journey.startCount || 0),
			0,
		);

		const goals = quest.goals || [];

		const conversions = journeys.filter(
			journey =>
				(journey.status === 'completed' && !goals.length) ||
				(goals.length && goals.every(x => journey.goalCompletionCache?.[x.id!]?.complete))
		).length;


		const totalviewsMetric = new CampaignMetric();
		totalviewsMetric.metricName = 'total_views';
		totalviewsMetric.metricValue = totalViews.toString();

		const uniqueViewsMetric = new CampaignMetric();
		uniqueViewsMetric.metricName = 'unique_views';
		uniqueViewsMetric.metricValue = uniqueViews.toString();

		const conversionMetric = new CampaignMetric();
		conversionMetric.metricName = 'conversion_rate';
		conversionMetric.metricValue =
			uniqueViews === 0
				? '0'
				: ((conversions / uniqueViews) * 100.0).toString();

		const usersMetric = new CampaignMetric();
		usersMetric.metricName = 'journeys';
		usersMetric.metricValue = journeys;

		return [
			totalviewsMetric,
			uniqueViewsMetric,
			conversionMetric,
			usersMetric,
		];
	}

	 async getCampaignMetric(metricName: string, campaignId: number): Promise<any> {
		const url = `/dev/metric?network=NONE&address=campaign-${campaignId}&metric-name=${metricName}&end-date=latest`;
		const signedRequest = getURL(url, 'GET', undefined, CONVERSION_API);
		const response = await fetch(`https://${CONVERSION_API}${url}`, signedRequest);
		return await response.json();
	}

	async getQuestWithGoalsAndJourneys(campaignId: number) {
		const quests = await this.questRepository.find({
			where: { campaignId },
			include: [
				'goals',
				'rewards',
				{
					relation: 'journeys',
					scope: {
						include: [
							{
								relation: 'raleonUser',
								scope: {
									include: [
										{
											relation: 'raleonUserIdentities',
										},
									],
								},
							},
						],
					},
				},
			],
		});

		if (!quests.length) {
			throw new HttpErrors.NotFound('No quests found for this campaign');
		}

		return quests;
	}
}
