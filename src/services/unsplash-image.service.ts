import {injectable} from '@loopback/core';
import { createApi } from 'unsplash-js';

@injectable()
export class UnsplashImageService {

	unsplashApi: any;

	constructor() {
		if (!process.env.UNSPLASH_ACCESS_KEY) {
			throw new Error('Unsplash access key not found');
		}
		this.unsplashApi = createApi({
			accessKey: process.env.UNSPLASH_ACCESS_KEY,
		});
	}

	async getPhotos(query: string, page: number, perPage: number, orientation: string) {
		const response = await this.unsplashApi.search.getPhotos({
			query: query,
			page: page,
			perPage: perPage,
			orientation: orientation,
		});

		if (response.errors) {
			throw new Error(response.errors[0]);
		}

		return response.response.results;
	}

	async trackDownload(downloadLocation: string) {
		const response = await this.unsplashApi.photos.trackDownload({ downloadLocation });

		if (response.errors) {
			throw new Error(response.errors[0]);
		}

		return response.response;
	}


}
