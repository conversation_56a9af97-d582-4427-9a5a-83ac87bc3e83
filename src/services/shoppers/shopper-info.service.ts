import {BindingScope, injectable, service} from '@loopback/core';
import {ShopifyApiInvoker} from '../shopify/shopify-api-invoker.service';
import {RaleonUserIdentity} from '../../models';
import {
	Filter,
	repository,
} from '@loopback/repository';
import {LoyaltyCurrencyBalanceRepository, LoyaltyCurrencyRepository, LoyaltyProgramRepository, RaleonUserIdentityRepository} from '../../repositories';

@injectable({scope: BindingScope.TRANSIENT})
export class ShopperInfo {

	constructor(
		@service(ShopifyApiInvoker)
		private shopifyApiInvoker: ShopifyApiInvoker,
		@repository(RaleonUserIdentityRepository)
		private raleonUserIdentityRepository: RaleonUserIdentityRepository,
		@repository(LoyaltyProgramRepository) protected loyaltyProgramRepository: LoyaltyProgramRepository,
		@repository(LoyaltyCurrencyRepository) protected loyaltyCurrencyRepository: LoyaltyCurrencyRepository,
		@repository(LoyaltyCurrencyBalanceRepository) protected loyaltyCurrencyBalanceRepository: LoyaltyCurrencyBalanceRepository

	) {}

	async getShopperInfo(orgId: number, page: number, limit: number, search?: string, customerIds?: Array<string>) : Promise<any> {
		if (!orgId) {
			throw new Error('Organization ID is required');
		}
		let shopifyIds = [];
		let response: any = null;
		if(search) {
			const path = `/shopper-info?query=${search}`;
			response = await this.shopifyApiInvoker.invokeAdminApi(orgId, path, 'GET');
			console.log('RESPONSE FROM SEARCH', response);
		}

		let filter: Filter<RaleonUserIdentity> = {
			where: {
				orgId: orgId,
				identityType: 'customer_id'
			},
			include: [
				{
					relation: 'raleonUser',
					scope: {
						include: [
							{
								relation: 'loyaltyCurrencyBalance',
								scope: {
									include: [
										{
											relation: 'loyaltyCurrencyTxLogs'
										}
									]
								}
							},
							{
								relation: 'loyaltyRewardLogs'
							}
						]
					}
				}
			],
			limit: limit,
			skip: (page - 1) * limit
		};

		//If searching we need to limit the search to the customers returned from shopify
		if(search) {
			filter.where = {
				...filter.where,
				identityValue: {
					inq: response.customers.map((customer: {id: number;}) => customer.id.toString())
				}
			};
		} else if (customerIds) {
			filter.where = {
				...filter.where,
				identityValue: {
					inq: customerIds
				}
			};
		}

		let ralIUsers =  await this.raleonUserIdentityRepository.find(filter);

		if(ralIUsers.length === 0) {
			return [];
		}

		//Lets build a list of ids to fetch from shopify, each item in the list will have identityValue which is the shopify customer id
		//pull out all the shopify customer ids
		if(!search) {
			shopifyIds = ralIUsers.map((ralIUser) => ralIUser.identityValue);
			const path = `/shopper-info?ids=${shopifyIds.filter(x => !x.includes('+')).join(',')}`;
			response = await this.shopifyApiInvoker.invokeAdminApi(orgId, path, 'GET');
		}


		//combine into final data model
		/*
		{
			id: 1,
			first_name: 'John',
			last_name: 'Doe',
			orders_count: 5,
			total_spent: 100,
			loyaltyPoints: 100,
			email: <EMAIL>,
			created_at: '2021-01-01',
			points_log: []
		}
		*/
		let shopifyCustomers = response?.customers;
		if(!shopifyCustomers) {
			return [];
		}

		//Grab Currency Id in case we need to create a new loyalty currency balance record
		let programs = await this.loyaltyProgramRepository.find({where: {orgId: orgId}});
		let program = programs[0];
		let currency = await this.loyaltyCurrencyRepository.find({where: {loyaltyProgramId: program.id}});

		const finalData = await Promise.all(ralIUsers.map(async (ralIUser) => {
			const shopifyData = shopifyCustomers.find((shopifyUser: {id: number;}) => shopifyUser?.id.toString() === ralIUser.identityValue);
			const raleonUser = ralIUser.raleonUser;
			if(raleonUser && !raleonUser.loyaltyCurrencyBalance) {
				let newRecord = await this.loyaltyCurrencyBalanceRepository.create({
					loyaltyCurrencyId: currency[0]?.id,
					raleonUserId: raleonUser.id,
				});
				raleonUser.loyaltyCurrencyBalance = newRecord;
				console.log('Created new loyalty currency balance record', newRecord);
			}
			else if(!raleonUser) {
				console.log('No raleon user found for ralIUser');
				return null;
			}
			return {
				id: ralIUser.id,
				raleon_user_id: raleonUser.id,
				first_name: shopifyData?.first_name || 'Unknown',
				shopify_id: shopifyData?.id || '',
				last_name: shopifyData?.last_name || 'Unknown',
				loyalty_segment: ralIUser.loyaltySegment == 'Unclassified' ? 'Very Loyal' : ralIUser.loyaltySegment,
				loyalty_score: ralIUser.loyaltyScore,
				orders_count: shopifyData?.orders_count || 0,
				total_spent: shopifyData?.total_spent || 0,
				loyalty_points: raleonUser.loyaltyCurrencyBalance?.balance,
				email: shopifyData?.email || '',
				created_at: shopifyData?.created_at || '',
				loyalty_currency_id: raleonUser.loyaltyCurrencyBalance?.loyaltyCurrencyId,
				loyalty_currency_balance_id: raleonUser.loyaltyCurrencyBalance?.id,
				points_log: raleonUser.loyaltyCurrencyBalance?.loyaltyCurrencyTxLogs,
				rewards_log: raleonUser.loyaltyRewardLogs
			};
		}));

		const filteredFinalData = finalData.filter(data => data !== null && data !== undefined);

		return {
			shoppers: filteredFinalData,
			pagination: {
				page: page,
				limit: limit,
				total: ralIUsers.length,
				nextPage: ralIUsers.length >= limit ? page + 1 : null
			}
		};
	}
}
