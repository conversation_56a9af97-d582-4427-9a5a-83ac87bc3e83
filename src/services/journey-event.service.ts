import {injectable, /* inject, */ BindingScope, service, inject} from '@loopback/core';
import {repository} from '@loopback/repository';
import {SecurityBindings} from '@loopback/security';
import {getURL} from '../controllers';
import {Journey, JourneyEvent, Quest, RaleonUser} from '../models';
import {JourneyRepository, QuestRepository, RaleonUserIdentityRepository, RaleonUserRepository, UserRepository} from '../repositories';
import {RaleonUserService} from './raleon-user.service';

const fetch = require('node-fetch')
const UTM_API = 'j0v36abmdj.execute-api.us-east-1.amazonaws.com';

@injectable({scope: BindingScope.TRANSIENT})
export class JourneyEventService {
  constructor(
    @repository(JourneyRepository)
    private journeyRepository: JourneyRepository,
    @repository(QuestRepository)
    private questRepository: QuestRepository,
    @service(RaleonUserService)
    private userService: RaleonUserService,
    @repository(UserRepository)
    private userRepository: UserRepository,
  ) {}

  /*
   * Add service methods here
   */
  async registerEvent(journeyEvent: JourneyEvent): Promise<Journey> {
    this.forwardEvent(journeyEvent);

    const identities = [
      {
        identityValue: journeyEvent.raleon_id,
        identityType: 'raleon_id',
      }
    ];
    if (journeyEvent.address && journeyEvent.address !== 'anonymous') {
      identities.push({
        identityValue: journeyEvent.address.toLowerCase(),
        identityType: 'address',
      });
    }

    const user = await this.userService.getUserFromIdentities(identities);

    const campaignId = this.getCampaignId(journeyEvent.event_type);
    const quest = await this.getQuest(campaignId, user);

	let completedEvent = `campaign-${campaignId}-quest-${quest.id}-completed`

    // determine status, TODO: de-hardcode clicked
	let status = journeyEvent.status;
	if( status != 'completed' && status != 'running' ) {
		status = journeyEvent.event_type.endsWith('clicked') || journeyEvent.event_type == completedEvent ? 'completed' : 'running';
	}

	const journey = await this.userService.findOrCreateJourney(quest.id!, user.id!, status);

    // Skip updating status if status already completed (don't revert to earlier status)
    if (journey!.status !== 'completed') {
      journey!.status = status;
    }

    // increment start count if viewed
    if (journeyEvent.event_type.endsWith('viewed')) {
      journey!.startCount = (journey!.startCount || 0) + 1;
    }

    if (
      (journey!.startCount || 0) >= 2 &&
      journey!.status !== 'completed' &&
      journeyEvent.event_type.endsWith('dismissed')
    ) {
      journey!.status = 'ignored';
    }

    this.journeyRepository.updateById(journey!.id, journey!);

    return journey!;
  }

  private async getQuest(campaignId: number, user: RaleonUser): Promise<Quest> {
    // find existing quest
    const quests = await this.questRepository.find({
      where: {
        campaignId: Number(campaignId),
      }
    });
    if (quests.length > 1) {
      throw new Error('Multiple quests found for campaign id. Not supported yet');
    }

    const quest = quests[0];
    if (!quest) {
      throw new Error('Campaign was missing Quest');
    }

    return quest;
  }

  private async forwardEvent(journeyEvent: JourneyEvent): Promise<void> {
    try {
      const url = `/api/event`;
      const signedRequest = getURL(url, 'POST', journeyEvent, UTM_API);
      console.log(signedRequest);
      const response = await fetch(`https://${UTM_API}${url}`, signedRequest);
      console.log(response);
    } catch (err) {
      console.log("Error: " + JSON.stringify(err));
    }
  }

  private getCampaignId(eventType: string): number {
    const campaignIdMatch = eventType.match(/^campaign-(\d+)/);
    if (!campaignIdMatch) {
      throw new Error('Invalid event type - missing campaign id');
    }
    const campaignId = Number(campaignIdMatch[1]);
    if (!campaignId || Number.isNaN(campaignId)) {
      throw new Error('Invalid event type - campaign id is not valid');
    }

    return campaignId;
  }
}
