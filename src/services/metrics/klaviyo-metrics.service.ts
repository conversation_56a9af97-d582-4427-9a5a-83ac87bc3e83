import {BindingScope, injectable, service} from '@loopback/core';
import {LLMRouterService} from '../chat/llm-router.service';

@injectable({scope: BindingScope.TRANSIENT})
export class KlaviyoMetricsService {
  constructor(
    @service(LLMRouterService)
    private llmRouter: LLMRouterService,
  ) {}

  async getTop5SubjectLines(metricsData: any) {
    try {
      if (!metricsData?.body?.data) {
        return "No campaign data available";
      }

      const campaigns = Array.isArray(metricsData.body.data) ?
        metricsData.body.data : [metricsData.body.data];

      // Filter and sort campaigns by open rate
      const filteredCampaigns = campaigns
        .filter((campaign: any) => {
          const openRate = campaign.metrics?.stats_open_rate?.value;
          return openRate && !isNaN(parseFloat(openRate));
        })
        .sort((a: any, b: any) => {
          const aOpenRate = parseFloat(a.metrics.stats_open_rate.value);
          const bOpenRate = parseFloat(b.metrics.stats_open_rate.value);
          return bOpenRate - aOpenRate;
        })
        .slice(0, 5);

      if (filteredCampaigns.length === 0) {
        return "No campaigns with open rates found";
      }

      return filteredCampaigns.map((c: any, index: number) => {
        const messages = JSON.parse(c.metrics.messages?.value || '[]');
        const subject = messages[0]?.attributes?.content?.subject || 'No Subject';
        return `${index + 1}. "${subject}" (${c.metrics.stats_open_rate.value} open rate)`;
      }).join('\n');

    } catch (error) {
      console.error('Error fetching top subject lines:', error);
      return `Error: Failed to fetch top subject lines - ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }

  async getKlaviyoCampaignSummary(metricsData: any): Promise<string> {
    try {
      if (!metricsData?.body.data) {
        return "No campaign data available";
      }

      const campaigns = Array.isArray(metricsData.body.data) ?
        metricsData.body.data : [metricsData.body.data];

      // Group campaigns by month
      const campaignsByMonth = new Map();

      campaigns.forEach((campaign: any) => {
        const date = new Date(campaign.metrics.send_time?.value);
        const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

        if (!campaignsByMonth.has(monthKey)) {
          campaignsByMonth.set(monthKey, {
            Promotion: { count: 0, openRates: [], clickRates: [], revenues: []},
            Education: { count: 0, openRates: [], clickRates: [], revenues: []},
            Awareness: { count: 0, openRates: [], clickRates: [], revenues: []}
          });
        }

        const monthData = campaignsByMonth.get(monthKey);
        const type = campaign.metrics.campaign_type?.value || 'Promotion';
        const typeData = monthData[type] || monthData['Promotion'];

        // Parse metrics
        const openRate = (parseFloat(campaign.metrics.stats_open_rate?.value || '0'));
        const clickRate = (parseFloat(campaign.metrics.stats_click_rate?.value || '0'));
        const revenue = parseFloat(campaign.metrics.stats_revenue_per_recipient?.value || '0') *
          parseFloat(campaign.metrics.stats_recipients?.value || '0');

        // Update metrics
        typeData.count++;
        if (!isNaN(openRate)) typeData.openRates.push(openRate);
        if (!isNaN(clickRate)) typeData.clickRates.push(clickRate);
        if (!isNaN(revenue)) typeData.revenues.push(revenue);
      });

      // Calculate averages and format response
      const monthlyAnalysis = Array.from(campaignsByMonth.entries()).map(([month, types]) => {
        const formattedTypes: Record<string, any> = {};
        let totalCampaigns = 0;

        for (const [type, data] of Object.entries(types as Record<string, any>)) {
          const avgOpenRate = data.openRates.length
            ? ((data.openRates.reduce((a: number, b: number) => a + b, 0) / data.openRates.length) * 100).toFixed(2) + '%'
            : '0%';

          const avgClickRate = data.clickRates.length
            ? ((data.clickRates.reduce((a: number, b: number) => a + b, 0) / data.clickRates.length) * 100).toFixed(2) + '%'
            : '0%';

          const avgRevenue = data.revenues.length
            ? '$' + ((data.revenues.reduce((a: number, b: number) => a + b, 0) / data.revenues.length)).toFixed(2)
            : '$0.00';

          formattedTypes[type] = {
            count: data.count,
            avgOpenRate,
            avgClickRate,
            avgRevenue
          };

          totalCampaigns += data.count;
        }

        return {
          month,
          types: formattedTypes,
          totalCampaigns
        };
      });

      return JSON.stringify({
        monthlyAnalysis: monthlyAnalysis.sort((a, b) => b.month.localeCompare(a.month)), // Sort by month descending
        totalCampaigns: monthlyAnalysis.reduce((sum, month) => sum + month.totalCampaigns, 0)
      }, null, 2);

    } catch (error) {
      console.error('Error analyzing campaign metrics:', error);
      return 'Error: Failed to analyze campaign metrics';
    }
  }

  async getKlaviyoCampaignsLast12Months(metricsData: any): Promise<string> {
    try {
      if (!metricsData?.body.data) {
        return "No campaign data available";
      }

      const campaigns = Array.isArray(metricsData.body.data) ?
        metricsData.body.data : [metricsData.body.data];

      // Get date 12 months ago
      const date12MonthsAgo = new Date();
      date12MonthsAgo.setMonth(date12MonthsAgo.getMonth() - 12);

      // Filter and format campaigns that have open rates
      const filteredCampaigns = campaigns
        .filter((campaign: any) => {
          const sendDate = new Date(campaign.metrics.send_time?.value);
          const hasOpenRate = campaign.metrics.stats_open_rate?.value &&
            !isNaN(parseFloat(campaign.metrics.stats_open_rate.value));
          return sendDate >= date12MonthsAgo && hasOpenRate;
        })
        .map((campaign: any) => ({
          name: campaign.metrics.name?.value || 'Untitled',
          type: campaign.metrics.campaign_type?.value || 'Unknown',
          sendDate: new Date(campaign.metrics.send_time.value).toLocaleDateString(),
          subject: campaign.metrics.messages?.value ?
            JSON.parse(campaign.metrics.messages.value)[0]?.attributes?.content?.subject || 'No Subject' : 'No Subject',
          previewText: campaign.metrics.messages?.value ?
            JSON.parse(campaign.metrics.messages.value)[0]?.attributes?.content?.preview_text || 'No Preview' : 'No Preview',
          openRate: (parseFloat(campaign.metrics.stats_open_rate.value) * 100).toFixed(2) + '%',
          clickRate: (parseFloat(campaign.metrics.stats_click_rate?.value || '0') * 100).toFixed(2) + '%',
          revenue: '$' + (parseFloat(campaign.metrics.stats_revenue_per_recipient?.value || '0') *
            parseFloat(campaign.metrics.stats_recipients?.value || '0')).toFixed(2)
        }));

      if (filteredCampaigns.length === 0) {
        return "No campaigns with open rates found in the last 12 months";
      }

      return filteredCampaigns
        .map((c:any) => `Campaign: ${c.name}\nType: ${c.type}\nDate: ${c.sendDate}\nSubject: ${c.subject}\nPreview: ${c.previewText}\nOpen Rate: ${c.openRate}\nClick Rate: ${c.clickRate}\nRevenue: ${c.revenue}\n`)
        .join('\n---\n');
    } catch (error) {
      console.error('Error fetching campaign history:', error);
      return `Error: Failed to fetch campaign history - ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }

  async getKlaviyoCampaigns12MonthAgo(metricsData: any): Promise<string> {
    try {
      // Get date range for exactly 12 months ago
      const now = new Date();
      const targetMonth = new Date(now.getFullYear() - 1, now.getMonth(), 1);
      const targetMonthEnd = new Date(now.getFullYear() - 1, now.getMonth() + 1, 0);

      if (!metricsData?.body.data) {
        return `No campaign data available for ${targetMonth.toLocaleString('default', { month: 'long', year: 'numeric' })}`;
      }

      const campaigns = Array.isArray(metricsData.body.data) ?
        metricsData.body.data : [metricsData.body.data];

      // Filter campaigns from exactly 12 months ago AND with open rates
      const exactMonthCampaigns = campaigns
        .filter((campaign: any) => {
          const sendDate = new Date(campaign.metrics.send_time?.value);
          return sendDate >= targetMonth &&
            sendDate <= targetMonthEnd &&
            campaign.metrics.stats_open_rate?.value &&
            !isNaN(parseFloat(campaign.metrics.stats_open_rate.value));
        })
        .map((campaign: any) => ({
          type: campaign.metrics.campaign_type?.value || 'Unknown',
          sendDate: new Date(campaign.metrics.send_time.value).toLocaleDateString(),
          subject: campaign.metrics.messages?.value ?
            JSON.parse(campaign.metrics.messages.value)[0]?.attributes?.content?.subject || 'No Subject' : 'No Subject',
          previewText: campaign.metrics.messages?.value ?
            JSON.parse(campaign.metrics.messages.value)[0]?.attributes?.content?.preview_text || 'No Preview' : 'No Preview',
          openRate: (parseFloat(campaign.metrics.stats_open_rate.value) * 100).toFixed(2) + '%',
          revenue: '$' + (parseFloat(campaign.metrics.stats_revenue_per_recipient?.value || '0') *
            parseFloat(campaign.metrics.stats_recipients?.value || '0')).toFixed(2)
        }));

      if (exactMonthCampaigns.length === 0) {
        return `No campaigns with open rates found for ${targetMonth.toLocaleString('default', { month: 'long', year: 'numeric' })}`;
      }

      const monthYear = targetMonth.toLocaleString('default', { month: 'long', year: 'numeric' });
      return `Campaigns from ${monthYear}:\n` + exactMonthCampaigns
        .map((c:any) => `- ${c.sendDate}: "${c.subject}" (${c.openRate} open rate, Revenue: ${c.revenue}), Type: ${c.type}\nPreview: ${c.previewText}`)
        .join('\n');

    } catch (error) {
      console.error('Error fetching campaigns from 12 months ago:', error);
      return `Error: Failed to fetch historical campaigns - ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }

  async getKlaviyoCampaignsLastWeek(metricsData: any): Promise<string> {
    try {
      if (!metricsData?.body.data) {
        return "No campaign data available for the last week";
      }

      const campaigns = Array.isArray(metricsData.body.data) ?
        metricsData.body.data : [metricsData.body.data];

      // Get start (Monday) and end (Sunday) of the last complete week
      const today = new Date();
      const currentDay = today.getDay(); // 0 = Sunday, 1 = Monday, etc.

      // Calculate days to subtract to get to last Sunday (end of last week)
      const daysToLastSunday = currentDay === 0 ? 7 : currentDay;

      // Calculate the last week's Sunday (end date)
      const lastSunday = new Date(today);
      lastSunday.setDate(today.getDate() - daysToLastSunday);
      lastSunday.setHours(23, 59, 59, 999);

      // Calculate the last week's Monday (start date)
      const lastMonday = new Date(lastSunday);
      lastMonday.setDate(lastSunday.getDate() - 6);
      lastMonday.setHours(0, 0, 0, 0);

      // Filter and format campaigns from the last week that have open rates
      const filteredCampaigns = campaigns
        .filter((campaign: any) => {
          const sendDate = new Date(campaign.metrics.send_time?.value);
          const hasOpenRate = campaign.metrics.stats_open_rate?.value &&
            !isNaN(parseFloat(campaign.metrics.stats_open_rate.value));
          return sendDate >= lastMonday && sendDate <= lastSunday && hasOpenRate;
        })
        .map((campaign: any) => ({
          name: campaign.metrics.name?.value || 'Untitled',
          type: campaign.metrics.campaign_type?.value || 'Unknown',
          sendDate: new Date(campaign.metrics.send_time.value).toLocaleDateString(),
          subject: campaign.metrics.messages?.value ?
            JSON.parse(campaign.metrics.messages.value)[0]?.attributes?.content?.subject || 'No Subject' : 'No Subject',
          description: campaign.metrics.campaign_description?.value || 'No description',
          openRate: (parseFloat(campaign.metrics.stats_open_rate.value) * 100).toFixed(2) + '%',
          clickRate: (parseFloat(campaign.metrics.stats_click_rate?.value || '0') * 100).toFixed(2) + '%',
          revenue: '$' + (parseFloat(campaign.metrics.stats_revenue_per_recipient?.value || '0') *
            parseFloat(campaign.metrics.stats_recipients?.value || '0')).toFixed(2)
        }));

      if (filteredCampaigns.length === 0) {
        return `No campaigns with open rates found for the week of ${lastMonday.toLocaleDateString()} to ${lastSunday.toLocaleDateString()}`;
      }

      const weekRange = `${lastMonday.toLocaleDateString()} to ${lastSunday.toLocaleDateString()}`;
      return `Campaigns from week of ${weekRange}:\n` +
        filteredCampaigns
          .map((c:any) => `Campaign: ${c.name}\nDescription: ${c.description}\nType: ${c.type}\nDate: ${c.sendDate}\nSubject: ${c.subject}\nOpen Rate: ${c.openRate}\nClick Rate: ${c.clickRate}\nRevenue: ${c.revenue}\n`)
          .join('\n---\n');
    } catch (error) {
      console.error('Error fetching last week campaign history:', error);
      return `Error: Failed to fetch last week campaign history - ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }

  async getKlaviyoCampaignsLastMonth(metricsData: any): Promise<string> {
    try {
      if (!metricsData?.body.data) {
        return "No campaign data available for the last month";
      }

      const campaigns = Array.isArray(metricsData.body.data) ?
        metricsData.body.data : [metricsData.body.data];

      // Get start (first day) and end (last day) of the last complete month
      const today = new Date();

      // Last month's last day (last day of previous month)
      const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0);
      lastMonthEnd.setHours(23, 59, 59, 999);

      // Last month's first day
      const lastMonthStart = new Date(lastMonthEnd.getFullYear(), lastMonthEnd.getMonth(), 1);
      lastMonthStart.setHours(0, 0, 0, 0);

      // Filter and format campaigns from the last month that have open rates
      const filteredCampaigns = campaigns
        .filter((campaign: any) => {
          const sendDate = new Date(campaign.metrics.send_time?.value);
          const hasOpenRate = campaign.metrics.stats_open_rate?.value &&
            !isNaN(parseFloat(campaign.metrics.stats_open_rate.value));
          return sendDate >= lastMonthStart && sendDate <= lastMonthEnd && hasOpenRate;
        })
        .map((campaign: any) => ({
          name: campaign.metrics.name?.value || 'Untitled',
          type: campaign.metrics.campaign_type?.value || 'Unknown',
          sendDate: new Date(campaign.metrics.send_time.value).toLocaleDateString(),
          subject: campaign.metrics.messages?.value ?
            JSON.parse(campaign.metrics.messages.value)[0]?.attributes?.content?.subject || 'No Subject' : 'No Subject',
          description: campaign.metrics.campaign_description?.value || 'No description',
          openRate: (parseFloat(campaign.metrics.stats_open_rate.value) * 100).toFixed(2) + '%',
          clickRate: (parseFloat(campaign.metrics.stats_click_rate?.value || '0') * 100).toFixed(2) + '%',
          revenue: '$' + (parseFloat(campaign.metrics.stats_revenue_per_recipient?.value || '0') *
            parseFloat(campaign.metrics.stats_recipients?.value || '0')).toFixed(2)
        }));

      if (filteredCampaigns.length === 0) {
        return `No campaigns with open rates found for ${lastMonthStart.toLocaleString('default', { month: 'long', year: 'numeric' })}`;
      }

      const monthName = lastMonthStart.toLocaleString('default', { month: 'long', year: 'numeric' });
      return `Campaigns from ${monthName}:\n` +
        filteredCampaigns
          .map((c:any) => `Campaign: ${c.name}\nDescription: ${c.description}\nType: ${c.type}\nDate: ${c.sendDate}\nSubject: ${c.subject}\nOpen Rate: ${c.openRate}\nClick Rate: ${c.clickRate}\nRevenue: ${c.revenue}\n`)
          .join('\n---\n');
    } catch (error) {
      console.error('Error fetching last month campaign history:', error);
      return `Error: Failed to fetch last month campaign history - ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }
}
