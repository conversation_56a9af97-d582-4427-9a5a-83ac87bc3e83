import {inject} from '@loopback/core';
import {HttpErrors, Request, RestBindings} from '@loopback/rest';
import { AuthenticationStrategy } from '@loopback/authentication';
import {AccessTokenService, UserData} from './token-service';
import {UserProfile, securityId} from '@loopback/security';

export interface RequestWithRaleonUserIdentity extends Request {
	raleonUserIdentity?: any;
}

export class UserAccessTokenAuthenticationStrategy implements AuthenticationStrategy {
	name = 'user-access-token';

	constructor(
		@inject(RestBindings.Http.REQUEST)
		private req: RequestWithRaleonUserIdentity,
		@inject('services.AccessTokenService')
		protected accessTokenService: AccessTokenService,
	) {}

	authenticate(request: Request): Promise<UserProfile | undefined> {
        return new Promise((resolve, reject) => {
            const token = request.headers.authorization?.replace('Bearer ', '');
            if (!token) {
                return reject(new HttpErrors.Unauthorized(`Authorization header not found.`));
            }
            this.accessTokenService.verifyUserToken(token)
                .then((user) => {
                    const raleonUser: any = {
						[securityId]: user.id,
						id: user.id,
                        organizationId: user.organizationId,
						roles: ['admin']
                    };
                    resolve(raleonUser);
                })
                .catch(err => reject(new HttpErrors.Unauthorized(`Invalid access token: ${err.message}`)));
        });
	}
}
