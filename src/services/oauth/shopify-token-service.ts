import {inject, service} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HttpErrors} from '@loopback/rest';
import {promisify} from 'util';
import {<PERSON>yalty<PERSON><PERSON>ram, <PERSON>leonUser, RaleonUserIdentity, RaleonUserIdentityWithRelations} from '../../models';
import {OrganizationRepository, RaleonUserIdentityRepository, RaleonUserRepository} from '../../repositories';
import {ShopifyApiInvoker} from '../shopify/shopify-api-invoker.service';
import {LoyaltyProgramController, LoyaltyRedemptionController} from '../../controllers';
import {DevDbDataSource} from '../../datasources';
import {TierService} from '../tier.service';
import {LoyaltyEventPublisher} from '../event-stream/loyalty-event-publisher.service';
import NodeCache from 'node-cache';
const jwt = require('jsonwebtoken');
const verifyAsync = promisify(jwt.verify);


export interface UserAccessTokenService {
	verifyToken(token: string): Promise<RaleonUserIdentity>;
}

export class ShopifyTokenService implements UserAccessTokenService {
	private tokenCache = new NodeCache({
		stdTTL: 300, // 5 minutes
		checkperiod: 60, // Check for expired keys every 60 seconds
		maxKeys: 1000, // Limit to 1000 tokens max
		deleteOnExpire: true,
		useClones: false // Better performance for our use case
	});
	private pendingVerifications = new Map<string, Promise<RaleonUserIdentity>>();
	
	constructor(
		@repository(RaleonUserRepository)
		public raleonUserRepository: RaleonUserRepository,
		@repository(RaleonUserIdentityRepository)
		public raleonUserIdentityRepository: RaleonUserIdentityRepository,
		@repository(OrganizationRepository)
		public organizationRepository: OrganizationRepository,
		@inject('services.ShopifyApiInvoker')
		private shopifyApiInvoker: ShopifyApiInvoker,
		@inject('controllers.LoyaltyRedemptionController')
		private loyaltyRedemptionController: LoyaltyRedemptionController,
		@inject('controllers.LoyaltyProgramController')
		private loyaltyProgramController: LoyaltyProgramController,
		@service(LoyaltyEventPublisher)
		private loyaltyEventPublisher: LoyaltyEventPublisher,
		@service(TierService)
		private tierService: TierService,

		@inject('datasources.dev_db') private devDbDataSource: DevDbDataSource
	) { 
		// Add event listeners for monitoring
		this.tokenCache.on('expired', (key, value) => {
			console.log(`[Token Cache] Expired key: ${key.substring(0, 20)}...`);
		});
		
		this.tokenCache.on('del', (key, value) => {
			console.log(`[Token Cache] Deleted key: ${key.substring(0, 20)}...`);
		});
	}

	// Clean up on service shutdown
	destroy() {
		this.tokenCache.close();
		this.pendingVerifications.clear();
	}

	async verifyToken(token: string): Promise<RaleonUserIdentity> {
		// Check cache first - NodeCache handles expiry automatically
		const cachedUser = this.tokenCache.get<RaleonUserIdentity>(token);
		if (cachedUser) {
			return cachedUser;
		}

		// Check if already verifying this token to prevent concurrent requests
		const pending = this.pendingVerifications.get(token);
		if (pending) {
			return pending;
		}

		// Create the verification promise and store it to prevent duplicates
		const verificationPromise = this.performTokenVerification(token);
		this.pendingVerifications.set(token, verificationPromise);

		try {
			const result = await verificationPromise;
			// Cache the result - NodeCache handles TTL automatically
			this.tokenCache.set(token, result);
			return result;
		} finally {
			// Always clean up the pending verification
			this.pendingVerifications.delete(token);
		}
	}

	private async performTokenVerification(token: string): Promise<RaleonUserIdentity> {
		const secret = process.env.SHOPIFY_API_SECRET;
		if (!secret) {
			throw new Error('Secret not found');
		}

		const maxRetries = 3; // Reduced from 5
		let attempt = 0;
		let noCustomerId = false;

		while (attempt < maxRetries) {
			try {
				const decoded = await verifyAsync(token, secret);
				const customerId = decoded.customerId || decoded.sub?.split?.('/')?.pop?.();
				const shopDomain = decoded.shopDomain || decoded.dest;
				const isRaleonApi = decoded.raleonApi == true;

				if (!customerId) {
					noCustomerId = true;
					throw new HttpErrors.Unauthorized('Invalid token');
				}

				let orgId: number | undefined;

				let user: any = await this.raleonUserIdentityRepository.findOne({
					where: {
						identityType: 'customer_id',
						identityValue: customerId
					},
					include: [{relation: 'raleonUser'}]
				});

				if (user && !user.firstLoginDate && !isRaleonApi) {
					const currentDate = new Date().toISOString();
					const sql = `
						UPDATE raleonuseridentity
						SET firstlogindate = $1
						WHERE id = $2 AND firstlogindate IS NULL
					`;
					const params = [currentDate, user.id];

					const result = await this.devDbDataSource.execute(sql, params);
					orgId = await this.getOrgId(shopDomain);
					if (result.count > 0) {
						this.grantNewUserWelcomeBonusesAndVipTiers(orgId!, customerId, user?.raleonUser, user!).catch(e => {
							console.error('Welcome bonus grant failed', e);
						});
					} else {
						console.log('Another process has already set the firstLoginDate.');
					}
					await this.loyaltyEventPublisher.publishJoinedLoyalty(customerId, orgId!)
				}
				if (!user) {
					const currentDate = new Date().toISOString();
					const orgId = await this.getOrgId(shopDomain);
					const raleonUser = await this.raleonUserRepository.create({});
					user = await this.raleonUserIdentityRepository.create({
						identityType: 'customer_id',
						identityValue: customerId,
						raleonUserId: raleonUser.id,
						orgId: orgId,
						loyaltySegment: 'New Users',
						firstLoginDate: isRaleonApi ? undefined : currentDate
					});

					const body = JSON.stringify({
						metafield: {
							namespace: "raleonInfo",
							key: "member_since",
							value: currentDate,
							object: "Customer",
							objectId: customerId
						}
					});

					if (orgId) {
						this.shopifyApiInvoker.invokeAdminApi(
							orgId,
							'/raleon-data/metafields',
							'POST',
							body
						).catch(e => console.error('Error emitting member_since meta update', e));;
					} else {
						throw new HttpErrors.Unauthorized('Organization not found');
					}

					this.grantNewUserWelcomeBonusesAndVipTiers(orgId, customerId, raleonUser!, {
						...user,
						raleonUser
					} as RaleonUserIdentityWithRelations).catch(e => {
						console.error('Welcome bonus grant failed', e);
					});

					if (!isRaleonApi) {
						await this.loyaltyEventPublisher.publishJoinedLoyalty(customerId, orgId);
					}
				}
				return user;
			} catch (err) {
				// Only log once per token to prevent log spam
				if (attempt === 0) {
					console.log(`Auth Error: ${err.message}`);
				}
				
				if (noCustomerId || err.message?.includes('Invalid token') || err.message?.includes('jwt malformed')) {
					throw new HttpErrors.Unauthorized(`No customer id found in token`);
				}
				
				attempt++;
				if (attempt < maxRetries) {
					// Exponential backoff: 500ms, 1s, 2s instead of fixed 200ms
					const delay = Math.min(500 * Math.pow(2, attempt - 1), 2000);
					console.log(`Retrying due to duplicate issue: Attempt ${attempt} (delay: ${delay}ms)`);
					await new Promise(resolve => setTimeout(resolve, delay));
				}
			}
		}
		throw new HttpErrors.Unauthorized(`Failed to verify token (and create user) after ${maxRetries} attempts`);
	}

	private async getOrgId(shopDomain: string) {
		const org = await this.organizationRepository.findOne({
			where: {
				externalDomain: shopDomain
			}
		});
		if (!org) throw new HttpErrors.Unauthorized('Organization not found');
		return org.id;
	}

	private async grantNewUserWelcomeBonusesAndVipTiers(orgId: number, customerId: string, raleonUser: RaleonUser, newUser: RaleonUserIdentityWithRelations) {
		try {
			const active: LoyaltyProgram[] = await this.loyaltyProgramController.findLive(raleonUser.id!, customerId as any, orgId);
			if (!active || active.length === 0) {
				console.warn('No active loyalty programs found for new user', raleonUser.id);
				return;
			}
			const activeCampaigns = active.map(x => x.loyaltyCampaigns).reduce((a, b) => a.concat(b), []).filter(x => x);
			const activeEarns = activeCampaigns.map(x => x.loyaltyEarns).reduce((a, b) => a.concat(b), []).filter(x => x);
			const welcomeBonusConditions = activeEarns.map(x => x.earnConditions).reduce((a, b) => a.concat(b), []).filter(x => x && x.type === 'welcome-bonus');

			const earnData = welcomeBonusConditions.map(c => ({
				earnId: c.loyaltyEarnId
			}));
			for (const earn of earnData) {
				await this.loyaltyRedemptionController.grantToCustomer(
					earn,
					raleonUser.id!,
					orgId,
					this.devDbDataSource,
					true
				);
			}

			await this.tierService.reassignUsersForAllTiers(orgId, [newUser]);
		} catch (e) {
			console.error('Welcome bonus grant failed', e);
		}
	}

}

export interface UserData {
	id: number,
	organizationId: number
}
