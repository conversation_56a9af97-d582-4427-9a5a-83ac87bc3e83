import {inject} from '@loopback/core';
import {HttpErrors, Request, RestBindings} from '@loopback/rest';
import {AuthenticationStrategy} from '@loopback/authentication';
import {repository} from '@loopback/repository';
import {AccessTokenService} from './token-service';
import {UserProfile, securityId} from '@loopback/security';
import {ShopifyTokenService} from './shopify-token-service';

export interface RequestWithRaleonUserIdentity extends Request {
	raleonUserIdentity?: any;
}

export class ShopifyCustomerAuthenticationStrategy implements AuthenticationStrategy {
	name = 'shopify-customer-access-token';

	constructor(
		@inject(RestBindings.Http.REQUEST)
		private req: RequestWithRaleonUserIdentity,
		@inject('services.ShopifyTokenService')
		protected shopifyTokenService: ShopifyTokenService,
	) {}

	authenticate(request: Request): Promise<UserProfile | undefined> {
		return new Promise((resolve, reject) => {
			const token = request.headers.authorization?.replace('Bearer ', '');
			if (!token) {
				return reject(new HttpErrors.Unauthorized(`Authorization header not found.`));
			}
			this.shopifyTokenService.verifyToken(token)
				.then((user) => {
					const raleonUser: UserProfile = {
						[securityId]: user.identityValue,
					};
					resolve(raleonUser);
				})
				.catch(err => reject(new HttpErrors.Unauthorized(`Invalid access token: ${err.message}`)));
		});
	}
}
