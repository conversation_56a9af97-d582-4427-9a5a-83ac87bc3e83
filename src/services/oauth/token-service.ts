import {inject} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HttpErrors} from '@loopback/rest';
import {promisify} from 'util';
import {RaleonUserIdentityRepository} from '../../repositories';
import {TokenServiceBindings} from '@loopback/authentication-jwt';
const jwt = require('jsonwebtoken');
const signAsync = promisify(jwt.sign);
const verifyAsync = promisify(jwt.verify);

export class AccessTokenService {
	constructor(
		@inject(TokenServiceBindings.TOKEN_SECRET)
		private jwtSecret: string,
		@repository(RaleonUserIdentityRepository)
		public raleonUserIdentityRepository: RaleonUserIdentityRepository,
	) { }

	async generateUserToken(user: UserData): Promise<string> {
		return await signAsync(
			JSON.stringify(user),
			process.env.RALEON_USER_TOKEN_SECRET
		);
	}

	async verifyUserToken(token: string): Promise<UserData> {
		try {
			return await verifyAsync(
				token,
				process.env.RALEON_USER_TOKEN_SECRET
			);
		} catch (err) {
			throw new HttpErrors.Unauthorized(`Error verifying token : ${err}`);
		}
	}
}

export interface UserData {
	id: number,
	organizationId: number
}
