import {inject, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import {LoyaltyEventRepository} from '../../repositories';
import {EventStreamController} from '../../controllers';
import {LoyaltyEvent, RewardCoupon} from '../../models';
import {getURL} from '../../controllers/ecommerce-metric.controller';

const fetch = require('node-fetch');
const EVENT_STREAM_API = process.env.EVENT_STREAM_API! || "s393yf9cql.execute-api.us-east-1.amazonaws.com";

@injectable()
export class LoyaltyEventPublisher {

	constructor(
		@repository(LoyaltyEventRepository)
		protected loyaltyEventRepository: LoyaltyEventRepository,
	) { }

	async publishReferralSharedEvent(customerId: string, orgId: number, friendEmail?: any): Promise<any> {
		let data: any = {};
		if (friendEmail) {
			data = { friendEmail };
		}
		return await this.publishEvent(EventStreamEvent.REFERRAL_SHARED, customerId, orgId, data);
	}

	async publishReferralCompletedEvent(customerId: string, orgId: number): Promise<any> {
		return await this.publishEvent(EventStreamEvent.REFERRAL_COMPLETED, customerId, orgId, {});
	}

	async publishVipTierUpdatedEvent(customerId: string, orgId: number, vipTier: string, pointsToNextTier?: number): Promise<any> {
		return await this.publishEvent(EventStreamEvent.VIP_TIER_UPDATED, customerId, orgId, {vipTier, pointsToNextTier});
	}

	async publishReferralReceivedEvent(customerId: string, orgId: number, email: string, referralLink: string): Promise<any> {
		return await this.publishEvent(EventStreamEvent.REFERRAL_RECEIVED, customerId, orgId, { email: encodeURIComponent(email), referralLink });
	}

	async publishBirthdayEvent(customerId: string, orgId: number, birthday: string): Promise<any> {
		const data = { birthday };
		return await this.publishEvent(EventStreamEvent.CUSTOMER_BIRTHDAY, customerId, orgId, data);
	}

	async publishBirthdayRewardGrantedEvent(customerId: string, orgId: number, rewardName: string, birthday: string, couponId?: string): Promise<any> {
		const data = {
			rewardName,
			birthday,
			couponId,
		 };
		return await this.publishEvent(EventStreamEvent.BIRTHDAY_REWARD_GRANTED, customerId, orgId, data);
	}

	async publishRewardGrantedEvent(customerId: string, orgId: number, rewardName: string, expirationDate: string, couponId: string): Promise<any> {
		const data = {
			rewardName,
			expirationDate,
			couponId,
		};
		return await this.publishEvent(EventStreamEvent.REWARD_GRANTED, customerId, orgId, data);
	}

	async publishJoinedLoyalty(customerId: string, orgId: number): Promise<any> {
		return await this.publishEvent(EventStreamEvent.JOINED_LOYALTY, customerId, orgId, {});
	}

	async bulkPublishOfferReceivedEvent(events: any[], orgId: number): Promise<any> {
		await this.publishEvent(EventStreamEvent.OFFER_RECEIVED, '', orgId, events);
	}

	async publishEvent(eventName: string, customerId: string, orgId: number, data: any): Promise<any> {
		const event = await this.loyaltyEventRepository.findOne({
			where: {
				name: eventName,
			},
		});

		if (!event) {
			throw new Error('Event type not supported');
		}

		if (!event.dataStructure) {
			return await this.sendEvent(event, customerId, orgId, data);
		}

		const validEventData = this.validateData(event, data);
		if (!validEventData) {
			throw new Error('Invalid data');
		}

		return await this.sendEvent(event, customerId, orgId, data);
	}

	private validateData(event: LoyaltyEvent, data: any): boolean {
		if (!event.dataStructure) {
			return true;
		}

		return this.validateObject(event.dataStructure, data);
	}

	private validateObject(structure: any, data: any): boolean {
		for (let key in structure) {
			if (structure[key].required && !(key in data)) {
				console.error(`Missing required key: ${key}`);
				return false;
			}

			if (key in data) {
				if (typeof data[key] !== structure[key].type && structure[key].required) {
					console.error(`Invalid type for key: ${key}`);
					return false;
				}

				if (structure[key].type === 'object' && structure[key].properties) {
					if (!this.validateObject(structure[key].properties, data[key])) {
						console.error(`Invalid properties for key: ${JSON.stringify(data[key])}`);
						return false;
					}
				}
			}
		}

		return true;
	}

	private async sendEvent(event: LoyaltyEvent, customerId: string, orgId: number, data?: any) {
		const params: any = {
			eventName: event.name,
			friendlyName: event.friendlyName,
			orgId: orgId,
			customerId: customerId,
			timestamp: new Date().toISOString(),
		};

		if (data) {
			params.data = data;
		}
		console.log(`sending event params: ${JSON.stringify(params)}`);
		console.log(`sending event to https://${EVENT_STREAM_API}/prod/event`);

		const signedRequest = getURL('/prod/event', 'POST', params, EVENT_STREAM_API);

		console.log(`signedRequest: ${JSON.stringify(signedRequest)}`);
		const response = await fetch(`https://${EVENT_STREAM_API}/prod/event`, signedRequest);
		return await response.json();
	}
}

export enum EventStreamEvent {
	REFERRAL_SHARED = 'referral_shared',
	REFERRAL_COMPLETED = 'referral_completed',
	REFERRAL_RECEIVED = 'referral_received',
	BALANCE_CHANGE = 'point_balance_change',
	NEXT_REWARD_UPDATE = 'next_reward_update',
	CUSTOMER_BIRTHDAY = 'customer_birthday',
	VIP_TIER_UPDATED = 'vip_tier_updated',
	OFFER_RECEIVED = 'offer_received',
	JOINED_LOYALTY = 'raleon_joined_loyalty',
	REWARD_EXPIRING = 'reward_expiring',
	BIRTHDAY_REWARD_GRANTED = 'birthday_reward_granted',
	REWARD_GRANTED = 'reward_granted',
}
