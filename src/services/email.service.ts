import {bind, BindingScope} from '@loopback/core';
import {SES} from 'aws-sdk';
import {SendEmailRequest, SendEmailResponse, SendRawEmailRequest, SendRawEmailResponse} from 'aws-sdk/clients/ses';
import {User} from '../models';
import { getWelcomeEmailTemplate } from '../email-templates/raleon-welcome';
import { getResetPasswordTemplate } from '../email-templates/raleon-reset-password';

@bind({scope: BindingScope.TRANSIENT})
export class EmailService {
	async sendResetPasswordMail(user: User): Promise<SendEmailResponse> {
		const SES_CONFIG = {
			accessKeyId: process.env.SES_IAM_ACCESS_KEY,
			secretAccessKey: process.env.SES_IAM_SECRET_KEY,
			region: process.env.REGION,
		};
		const ses = new SES(SES_CONFIG);
		const encodedEmail = encodeURIComponent(user.email);
		const encodedResetKey = encodeURIComponent(user.resetKey!);

		const params: SendEmailRequest = {
			Source: process.env.SENDER!,
			Destination: {
				ToAddresses: [
					user.email
				]
			},
			Message: {
				Subject: {
					Data: '[Raleon] Reset Password Request',
					Charset: 'UTF-8'
				},
				Body: {
					Html: {
						Charset: 'UTF-8',
						Data: getResetPasswordTemplate(`${process.env.APPLICATION_URL}/reset-password?email=${encodedEmail}&resetKey=${encodedResetKey}`),
					},
				}
			}
		}

		return ses.sendEmail(params)
			.promise()
			.then(data => {
				return data;
			})
			.catch(err => {
				console.log(err, err.stack);
				return err;
			});
	}

	async sendSegmentExportEmail(
		userEmail: string,
		csvData: string,
		fileName: string,
		emailSubject: string
	  ): Promise<SendRawEmailResponse> {
		const SES_CONFIG = {
		  accessKeyId: process.env.SES_IAM_ACCESS_KEY,
		  secretAccessKey: process.env.SES_IAM_SECRET_KEY,
		  region: process.env.REGION,
		};
		const ses = new SES(SES_CONFIG);

		const boundary = "----=_Part_0_123456789.1234567890";

		const rawEmail = [
		  `From: <EMAIL>`,
		  `To: ${userEmail}`,
		  `Subject: ${emailSubject}`,
		  "MIME-Version: 1.0",
		  `Content-Type: multipart/mixed; boundary="${boundary}"`,
		  "",
		  `--${boundary}`,
		  "Content-Type: text/html; charset=UTF-8",
		  "Content-Transfer-Encoding: 7bit",
		  "",
		  "Attached is the CSV file containing the exported segment data.",
		  "",
		  `--${boundary}`,
		  "Content-Type: text/csv",
		  `Content-Disposition: attachment; filename="${fileName}"`,
		  "Content-Transfer-Encoding: base64",
		  "",
		  Buffer.from(csvData).toString('base64'),
		  "",
		  `--${boundary}--`
		].join('\r\n');

		const params: SendRawEmailRequest = {
		  RawMessage: {
			Data: Buffer.from(rawEmail)
		  }
		};

		return ses.sendRawEmail(params).promise();
	  }

	async sendEmailWithAttachment(
		userEmail: string,
		csvData: string,
	  ): Promise<SendRawEmailResponse> {
		const SES_CONFIG = {
		  accessKeyId: process.env.SES_IAM_ACCESS_KEY,
		  secretAccessKey: process.env.SES_IAM_SECRET_KEY,
		  region: process.env.REGION,
		};
		const ses = new SES(SES_CONFIG);

		const boundary = "----=_Part_0_123456789.1234567890";

		const rawEmail = [
		  `From: <EMAIL>`,
		  `To: ${userEmail}`,
		  "Subject:Your [Raleon] Shoppers Export is Ready",
		  "MIME-Version: 1.0",
		  `Content-Type: multipart/mixed; boundary="${boundary}"`,
		  "",
		  `--${boundary}`,
		  "Content-Type: text/html; charset=UTF-8",
		  "Content-Transfer-Encoding: 7bit",
		  "",
		  "Attached is the CSV file containing the exported shoppers data.",
		  "",
		  `--${boundary}`,
		  "Content-Type: text/csv",
		  "Content-Disposition: attachment; filename=\"raleon_export_shoppers.csv\"",
		  "Content-Transfer-Encoding: base64",
		  "",
		  Buffer.from(csvData).toString('base64'),
		  "",
		  `--${boundary}--`
		].join('\r\n');

		const params: SendRawEmailRequest = {
		  RawMessage: {
			Data: Buffer.from(rawEmail)
		  }
		};

		return ses.sendRawEmail(params).promise();
	}

	async sendNewUserEmail(email: string, inviteCode: string, isSecondaryAccount = false): Promise<SendEmailResponse> {
		const SES_CONFIG = {
			accessKeyId: process.env.SES_IAM_ACCESS_KEY,
			secretAccessKey: process.env.SES_IAM_SECRET_KEY,
			region: process.env.REGION,
		};
		const ses = new SES(SES_CONFIG);
		const encodedEmail = encodeURIComponent(email);
		const encodedInviteCode = encodeURIComponent(inviteCode);

		const params: SendEmailRequest = {
			Source: process.env.SENDER!,
			Destination: { ToAddresses: [email] },
			Message: {
				Subject: {
					Data: 'Welcome to Raleon!',
					Charset: 'UTF-8'
				},
				Body: {
					Html: {
						Charset: 'UTF-8',
						Data: getWelcomeEmailTemplate(`${process.env.APPLICATION_URL}/invite?email=${encodedEmail}&invite-code=${encodedInviteCode}&isSecondaryAccount=${isSecondaryAccount}`),
					},
				}
			}
		}

		return ses.sendEmail(params)
			.promise()
			.then(data => {
				return data;
			})
			.catch(err => {
				console.log(err, err.stack);
				return err;
			});

	}
}
