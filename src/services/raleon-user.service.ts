import {injectable, /* inject, */ BindingScope} from '@loopback/core';
import {repository} from '@loopback/repository';
import {JourneyWithRelations, RaleonUser} from '../models';
import {
  JourneyRepository,
  RaleonUserIdentityRepository,
  RaleonUserRepository,
} from '../repositories';

@injectable({scope: BindingScope.TRANSIENT})
export class RaleonUserService {
  constructor(
    // Add @repository to inject repositories
    @repository(RaleonUserRepository)
    private repository: RaleonUserRepository,
    @repository(RaleonUserIdentityRepository)
    private raleonUserIdentityRepository: RaleonUserIdentityRepository,
    @repository(JourneyRepository)
    private journeyRepository: JourneyRepository,
  ) {}

  async getIdentitiesFromUser(
	raleonUserId: number
  ): Promise<RaleonUser> {
	return this.repository.findById(raleonUserId, { include: ["raleonUserIdentities"]});
  }

  async getAddressIdentityFromUser(
	raleonUserId: number
  ): Promise<string | undefined> {
	const user = await this.repository.findById(raleonUserId, { include: ["raleonUserIdentities"]});
	return user.raleonUserIdentities.find(x => x.identityType === 'address')?.identityValue;
  }

  async findOrCreateJourney(
	questId: number,
	raleonUserId: number,
	status?: string
  ): Promise<JourneyWithRelations | null> {
	const existing = await this.journeyRepository.findOne({
		where: {
		  raleonUserId,
		  questId,
		}
	});

	if (existing) {
		return existing;
	}

	try {
		const journey = await this.journeyRepository.create({
			raleonUserId,
			status,
			questId,
			questUserKey: `${questId}-${raleonUserId}`,
			startCount: 1
		}) as JourneyWithRelations;

		return journey;
	} catch (e) {
		return this.journeyRepository.findOne({
			where: {
			  raleonUserId,
			  questId,
			}
		});
	}
  }

  /*
   * Add service methods here
   */
  async getUserFromIdentities(
    identities: Array<{identityValue: string; identityType: string}>,
    raleonUserId?: number
  ): Promise<RaleonUser> {
    // filter out empty identities
    identities = identities.filter(identity => identity.identityValue);

    // find identities
    const identityObjects = await this.raleonUserIdentityRepository.find({
      where: {
        or: identities,
      },
      include: [
        {
          relation: 'raleonUser',
        },
      ],
    });

    const users = identityObjects.map(identity => identity.raleonUser);
    // include provided user if id specified
    if (raleonUserId) {
      const user = await this.repository.findById(raleonUserId);
      users.push(user);
    }
    const userIds = users.map(user => user.id);

    // find or create user
    const user =
      identityObjects.length === 0
        ? await this.repository.create({})
        : new Set(userIds).size > 1
          ? await this.mergeUsers(users)
          : users[0];

    // create missing identities
    const missingIdentities = identities.filter(
      identity =>
        !identityObjects.find(
          identityObject =>
            identityObject.identityValue === identity.identityValue &&
            identityObject.identityType === identity.identityType,
        ),
    );
    await this.raleonUserIdentityRepository.createAll(
      missingIdentities.map(identity => ({
        ...identity,
        raleonUserId: user.id,
      })),
    );

    return user;
  }

  private async mergeUsers(users: Array<RaleonUser>): Promise<RaleonUser> {
    // find all identities
    const identities = await this.raleonUserIdentityRepository.find({
      where: {
        raleonUserId: {
          inq: users.map(user => user.id || 0),
        },
      },
    });

    // create new user
    const newUser = users[0];

    // Repoint all identities to new user
    await this.raleonUserIdentityRepository.updateAll(
      {
        raleonUserId: newUser.id,
      },
      {
        raleonUserId: {
          inq: users.map(user => user.id || 0),
        },
      },
    );

    // mark old users as replaced
    await this.repository.updateAll(
      {
        replacedByUserId: newUser.id,
      },
      {
        or: [
          {
            id: {
              inq: users.map(user => user.id || 0),
            },
          },
          {
            replacedByUserId: {
              inq: users.map(user => user.id || 0),
            },
          },
        ]
      },
    );

    await this.updateJourneys(users.map(user => user.id || 0), newUser.id || 0);


    return newUser;
  }

  private async updateJourneys(oldUserIds: Array<number>, newUserId: number) {
     // Repoint all journeys to new user
     await this.journeyRepository.updateAll(
      {
        raleonUserId: newUserId,
      },
      {
        raleonUserId: {
          inq: oldUserIds
        },
      },
    );

    const journeys = await this.journeyRepository.find({
      where: {
        raleonUserId: newUserId,
      },
      include: [
        {
          relation: 'quest',
        },
      ],
    });

    // TODO: this may need to be more deliberate, especially once steps get involved
    // delete duplicate journeys pointing to same quest and user
    const duplicateJourneys = journeys
		.sort((a,b) => {
			if (a.status === 'completed') {
				return b.status === 'completed' ? 0 : 1;
			}

			if (a.status === 'ignored') {
				return b.status === 'completed'
					? -1
					: b.status === 'ignored' ? 0 : 1;
			}

			return b.status === 'ignored' || b.status === 'completed' ? -1 : 0;
		})
		.filter((journey, index, journeys) =>
			journeys.findIndex(
			otherJourney =>
				otherJourney.questId === journey.questId &&
				otherJourney.id !== journey.id,
			) > index
		);

	if (!duplicateJourneys.length) {
		return;
	}

    const nonDuplicateJourneys = journeys.filter(journey => !duplicateJourneys.includes(journey));
    if (nonDuplicateJourneys.length > 1) {
    //   throw new Error('Journey deduplication failed');
		duplicateJourneys.push(...nonDuplicateJourneys.slice(1));
    }
    const remainingJourney = nonDuplicateJourneys[0];

    const startCount = duplicateJourneys.reduce((acc, journey) => acc + (journey.startCount || 0), 0);

    await this.journeyRepository.deleteAll({
      id: {
        inq: duplicateJourneys.map(journey => journey.id || 0),
      },
    });

    // update start count on remaining journey
    await this.journeyRepository.updateAll(
      {
        startCount: startCount + (remainingJourney.startCount || 0),
      },
      {
        id: remainingJourney.id,
      },
    );
  }
}
