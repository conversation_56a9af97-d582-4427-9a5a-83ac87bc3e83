import {BindingScope, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import {PromptTemplateRepository} from '../repositories';
import {PromptTemplate} from '../models';
import {HttpErrors} from '@loopback/rest';

@injectable({scope: BindingScope.TRANSIENT})
export class PromptTemplateService {
  constructor(
    @repository(PromptTemplateRepository)
    private promptTemplateRepository: PromptTemplateRepository,
  ) {}

  async create(promptTemplate: Omit<PromptTemplate, 'id'>): Promise<PromptTemplate> {
    return this.promptTemplateRepository.create(promptTemplate);
  }

  async findById(id: number): Promise<PromptTemplate> {
    const template = await this.promptTemplateRepository.findById(id);
    if (!template) {
      throw new HttpErrors.NotFound('Prompt template not found');
    }
    return template;
  }

  async find(): Promise<PromptTemplate[]> {
    return this.promptTemplateRepository.find({
      order: ['category ASC', 'name ASC'],
    });
  }

  async updateById(id: number, promptTemplate: Partial<PromptTemplate>): Promise<void> {
    await this.promptTemplateRepository.updateById(id, {
      ...promptTemplate,
      updatedAt: new Date(),
    });
  }

  async deleteById(id: number): Promise<void> {
    await this.promptTemplateRepository.deleteById(id);
  }
}