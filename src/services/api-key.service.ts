import {BindingScope, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import {ApiKeyRepository} from '../repositories/api-key.repository';
import {Api<PERSON><PERSON>} from '../models/api-key.model';
import {v4 as uuidv4} from 'uuid';

@injectable({scope: BindingScope.TRANSIENT})
export class ApiKeyService {
  constructor(
    @repository(ApiKeyRepository)
    private apiKeyRepository: ApiKeyRepository,
  ) {}

  async generateApiKey(name: string, scopes: string[] = []): Promise<ApiKey> {
    const key = uuidv4();
    const newApiKey = await this.apiKeyRepository.create({
      key,
      name,
      scopes,
      createdAt: new Date(),
    });
    return newApiKey;
  }

  async revokeApiKey(id: number): Promise<void> {
    await this.apiKeyRepository.deleteById(id);
  }

  async listApiKeys(): Promise<ApiKey[]> {
    return this.apiKeyRepository.find();
  }
}
