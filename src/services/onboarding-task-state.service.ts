import {injectable, /* inject, */ BindingScope} from '@loopback/core';
import {repository, Count} from '@loopback/repository';
import {OnboardingState} from '../models';
import {OnboardingTaskRepository, OnboardingStateRepository} from '../repositories';
import {HttpErrors} from '@loopback/rest';

@injectable({scope: BindingScope.TRANSIENT})
export class OnboardingTaskStateService {
  constructor(
    @repository(OnboardingTaskRepository)
    public onboardingTaskRepository : OnboardingTaskRepository,
	@repository(OnboardingStateRepository)
	public onboardingStateRepository: OnboardingStateRepository
  ) {}

  async updateState(
    onboardingState: Partial<OnboardingState>,
	type: string,
	orgId: number,
  ): Promise<Count> {
	const task = await this.onboardingTaskRepository.findOne({
		where: {
			type
		}
	});

	if (!task || !task.id) {
		console.warn('No matcing task found');
		return { count : 0 };
	}

	if (await this.onboardingStateRepository.findOne({ where: { orgId, taskId: task!.id } })) {
		return this.onboardingStateRepository.updateAll(
			{
				...onboardingState,
				orgId,
				taskId: task!.id,
				timestamp: new Date()
			},
			{
				orgId,
				taskId: task!.id,
			}
		)

	} else {
		await this.onboardingStateRepository.create(
			{
				...onboardingState,
				orgId,
				taskId: task!.id,
				timestamp: new Date()
			}
		);

		return {
			count: 1
		}
	}

  }
}
