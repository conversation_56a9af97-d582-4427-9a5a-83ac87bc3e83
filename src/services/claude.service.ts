import {BindingScope, injectable} from '@loopback/core';
import fetch from 'node-fetch';

@injectable({scope: BindingScope.TRANSIENT})
export class ClaudeService {
  async getCompletion(
    messages: Array<{
      role: string;
      content: Array<{
        type: string;
        text?: string;
        image_url?: { url: string };
        source?: {
          type: 'base64';
          media_type: string;
          data: string;
        };
      }>;
    }>,
    max_tokens?: number,
    model?: string,
    tools?: Array<any>
  ): Promise<string> {
    try {
      const request: any = {
        model: model || 'claude-3-5-sonnet-20241022',
        max_tokens: max_tokens || 4096,
        messages: messages,
        temperature: 0.7
      };

      if (tools) {
        request.tools = tools;
      }

      const response = await fetch('https://api.anthropic.com/v1/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': '************************************************************************************************************',
          'anthropic-version': '2023-06-01'
        },
        body: JSON.stringify(request)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Claude API error: ${errorData.error?.message || 'Unknown error'}`);
      }

      const data = await response.json();
      return data.content[0].text;
    } catch (error) {
      console.error('Claude API Error:', error);
      throw new Error('Failed to get response from Claude');
    }
  }

}
