import {repository} from '@loopback/repository';
import {EarnEffectRepository, FeatureSettingRepository, LoyaltyCurrencyBalanceRepository, LoyaltyCurrencyRepository, LoyaltyCurrencyTxLogRepository, LoyaltyEarnRepository, LoyaltyProgramRepository, RaleonUserIdentityRepository, VipTierRepository, LoyaltyCampaignRepository, LoyaltyRedemptionShopItemRepository, CurrencyRepository} from '../../repositories';
import {EarnConditionWithRelations, EarnEffectWithRelations, LoyaltyCampaignWithRelations, LoyaltyCurrency, LoyaltyCurrencyBalance, LoyaltyCurrencyBalanceWithRelations, LoyaltyProgram, LoyaltyCurrencyTxLog, LoyaltyEarnWithRelations, RaleonUserIdentityWithRelations, RaleonUserIdentity} from '../../models';
import {inject, injectable, service} from '@loopback/core';
import {LoyaltyDetailsService} from './loyalty-details.service';
import {LoyaltyTrailingTwelveMonthPointsService} from './loyalty-trailing-twelve-month-points.service';
import {LoyaltyEventPublisher, EventStreamEvent} from '../event-stream/loyalty-event-publisher.service';
import {ShopifyApiInvoker} from '../shopify/shopify-api-invoker.service';
import {TierService} from '../tier.service';
import {HttpErrors} from '@loopback/rest';
import {convertCurrencyPlaceholdersToValues} from '../../controllers';

@injectable()
export class LoyaltyBalanceManager {

	constructor(
		@repository(LoyaltyCurrencyBalanceRepository)
		public loyaltyCurrencyBalanceRepository: LoyaltyCurrencyBalanceRepository,
		@repository(LoyaltyCurrencyTxLogRepository)
		public loyaltyCurrencyTxLogRepository: LoyaltyCurrencyTxLogRepository,
		@repository(LoyaltyCurrencyRepository)
		public loyaltyCurrencyRepository: LoyaltyCurrencyRepository,
		@repository(LoyaltyProgramRepository)
		public loyaltyProgramRepository: LoyaltyProgramRepository,
		@repository(RaleonUserIdentityRepository)
		private raleonUserIdentityRepository: RaleonUserIdentityRepository,
		@service(LoyaltyDetailsService)
		private loyaltyDetailsService: LoyaltyDetailsService,
		@service(LoyaltyTrailingTwelveMonthPointsService)
		private loyaltyTtmPointsService: LoyaltyTrailingTwelveMonthPointsService,
		@service(LoyaltyEventPublisher)
		private loyaltyEventPublisher: LoyaltyEventPublisher,
		@repository(LoyaltyEarnRepository)
		private loyaltyEarnRepository: LoyaltyEarnRepository,
		@repository(EarnEffectRepository)
		private earnEffectRepository: EarnEffectRepository,
		@service(TierService)
		private tierService: TierService,
		@inject('services.ShopifyApiInvoker')
		private shopifyApiInvoker: ShopifyApiInvoker,
		@repository(LoyaltyCampaignRepository)
		private loyaltyCampaignRepository: LoyaltyCampaignRepository,
		@repository(LoyaltyRedemptionShopItemRepository)
		private loyaltyRedemptionShopItemRepository: LoyaltyRedemptionShopItemRepository,
		@repository(CurrencyRepository)
		private currencyRepository: CurrencyRepository
	) {}

	async updateBalanceAndLog(
		id: typeof LoyaltyCurrency.prototype.id,
		request: any,
		overrideTtmBalance?: number
	): Promise<any> {
		console.log('UPDATING BALANCE RECORD FOR RALEON USER ID: ', request.raleonUserId);

		// Find or create balance record
		let existingBalanceRecord = await this.loyaltyCurrencyBalanceRepository.findOne({
			where: {
				loyaltyCurrencyId: id,
				raleonUserId: request.raleonUserId
			},
		});

		if (!existingBalanceRecord) {
			try {
				existingBalanceRecord = await this.loyaltyCurrencyBalanceRepository.create({
					loyaltyCurrencyId: id,
					raleonUserId: request.raleonUserId,
				});
			} catch (e) {
				await new Promise(resolve => setTimeout(resolve, 200));
				existingBalanceRecord = await this.loyaltyCurrencyBalanceRepository.findOne({
					where: {
						loyaltyCurrencyId: id,
						raleonUserId: request.raleonUserId
					}
				});
			}
		}

		if (!existingBalanceRecord) {
			console.error(`Failed to create LoyaltyCurrencyBalance record for loyaltyCurrencyId: ${id} and raleonUserId: ${request.raleonUserId}`);
			return;
		}

		existingBalanceRecord!.balance = (existingBalanceRecord!.balance ?? 0) + request.balanceChange;
		if (overrideTtmBalance !== undefined) {
			existingBalanceRecord!.trailingTwelveMonthGrantTotal = overrideTtmBalance;
		}
		await this.loyaltyCurrencyBalanceRepository.updateById(existingBalanceRecord!.id, existingBalanceRecord!);

		// Create a LoyaltyCurrencyTxLog record
		await this.loyaltyCurrencyTxLogRepository.create({
			date: new Date().toISOString(),
			info: request.info || 'Points',
			amount: request.balanceChange,
			loyaltyCurrencyBalanceId: existingBalanceRecord!.id,
			orderId: request.orderId,
			itemId: request.itemId,
			earnEffectId: request.earnEffectId
		});

		try {
			if(!request.skipRewards) {
				await this.grantPointsBalanceTriggerRewards(existingBalanceRecord, request.raleonUserId);
			}
		} catch (e) {
			console.error('Error granting points balance trigger rewards', e);
		}

		//get org id
		const currency = await this.loyaltyCurrencyRepository.findOne({ where: { id: id } });
		const program = await this.loyaltyProgramRepository.findOne({ where: { id: currency!.loyaltyProgramId } });
		const customerId = await this.raleonUserIdentityRepository.findOne({ where: { raleonUserId: request.raleonUserId, orgId: program?.orgId } });

		if (customerId && existingBalanceRecord) {
			this.emitEventsAndUpdateMetadata(customerId, program!, request, existingBalanceRecord)
				.catch(e => console.error('Error emitting events and updating metadata', e));
		}
		console.log(`Balance updated for raleonUserId: ${request.raleonUserId}`);
		return {
			info: request.info,
			balanceChange: request.balanceChange,
			raleonUserId: request.raleonUserId,
		};
	}

	private async emitEventsAndUpdateMetadata(customerId: RaleonUserIdentity, program: LoyaltyProgram, request: any, existingBalanceRecord: LoyaltyCurrencyBalance & LoyaltyCurrencyBalanceWithRelations) {
		const identityValue = customerId?.identityValue;

		let earnName = request.earnName;
		const earnEffect = await this.earnEffectRepository.findOne({ where: { id: request.earnEffectId } });
		const earn = await this.loyaltyEarnRepository.findOne({
			where: { id: earnEffect!.loyaltyEarnId },
			include: ['earnConditions']
		});

		if (!earnName && request.earnEffectId) {
			earnName = earn!.name;
		}

		if (earn && earn?.earnConditions?.[0] && earn.earnConditions[0].type == 'birthday-bonus' && customerId.birthday) {
			let rewardName = earnName;
			if (earnEffect!.type == 'points') {
				rewardName = `${request.balanceChange} ${(existingBalanceRecord as any).loyaltyCurrency?.name || 'points'}`;
			}
			const birthday = new Date(customerId?.birthday?.toISOString()!);
			const month = birthday.getMonth() + 1;
			const day = birthday.getDate();
			const year = birthday.getFullYear();
			await this.loyaltyEventPublisher.publishBirthdayRewardGrantedEvent(
				identityValue!,
				customerId.orgId!,
				rewardName,
				`${month}/${day}/${year}`,
			);
		} else {
			console.log(`PUBLISHING BALANCE CHANGE EVENT`);
			const balanceChangeEvent = await this.loyaltyEventPublisher.publishEvent(
				EventStreamEvent.BALANCE_CHANGE,
				customerId.identityValue,
				program?.orgId!,
				{
					balanceChange: request.balanceChange,
					totalPoints: existingBalanceRecord!.balance,
					earnName: earnName,
				}
			);
			console.log(`SENT BALANCE CHANGE EVENT: ${JSON.stringify(balanceChangeEvent)}`);
		}



		await this.calculateAndEmitNextReward(existingBalanceRecord, program, identityValue);

		const body = JSON.stringify({
			metafield: {
				namespace: "raleonInfo",
				key: "loyalty_points",
				value: existingBalanceRecord!.balance?.toString(),
				object: "Customer",
				objectId: identityValue
			}
		});

		if (customerId?.orgId) {
			let res = await this.shopifyApiInvoker.invokeAdminApi(
				customerId?.orgId,
				'/raleon-data/metafields',
				'POST',
				body
			);
		}
	}


	private async calculateAndEmitNextReward(existingBalanceRecord: LoyaltyCurrencyBalance, program: LoyaltyProgram, identityValue: string) {
		const campaign = await this.loyaltyCampaignRepository.findOne({
			where: {
				loyaltyProgramId: program?.id,
				active: true,
				evergreen: true
			}
		});

		const shopItems = await this.loyaltyRedemptionShopItemRepository.find({
			where: {
				loyaltyCampaignId: campaign?.id,
				active: true
			}
		});

		const orgCurrency: any = await this.currencyRepository.findOne({
			where: {
				organizationId: program.orgId
			},
			include: [
				{
					relation: 'supportedCurrencies'
				}
			]
		});

		const balance = existingBalanceRecord.balance || 0;
		const nextReward = shopItems.sort(
			(a, b) => a.price - b.price
		).find(x => x.price > balance);
		const deficit = nextReward ? nextReward.price - balance : 0;

		console.log(`PUBLISHING NEXT REWARD EVENT`);
		const nextRewardEventSent = await this.loyaltyEventPublisher.publishEvent(
			EventStreamEvent.NEXT_REWARD_UPDATE,
			identityValue,
			program?.orgId!,
			{
				nextReward: nextReward
					? convertCurrencyPlaceholdersToValues(nextReward.name, orgCurrency)
					: '',
				deficit
			}
		);

		console.log(`Next Reward Event Sent: ${JSON.stringify(nextRewardEventSent)}`);
	}

	async processRefund(raleonUserId: number, orderId: number, originalAmount: number, refundAmount: number, kind: string): Promise<any> {
		if (!orderId) {
			console.warn('No order id found in refund');

			return;
		}

		const transactions = await this.loyaltyCurrencyTxLogRepository.find({
			where: {
				orderId: orderId.toString()
			}
		});
		if (!transactions || transactions.length === 0) {
			console.warn(`No transactions found for order ${orderId}`);
			return;
		}

		const earnEffects = await this.earnEffectRepository.find({
			where: {
				id: { inq: transactions.filter(x => x.earnEffectId).map(x => x.earnEffectId) }
			}
		});

		const filteredTransactions = transactions.filter(x =>
			earnEffects.find(y => y.pointsPerDollar && y.id === x.earnEffectId)
		);

		const currencyBalanceIds = filteredTransactions.map(x => x.loyaltyCurrencyBalanceId);
		const currencyBalances = await this.loyaltyCurrencyBalanceRepository.find({
			where: {
				id: {
					inq: currencyBalanceIds
				}
			}
		});
		const currencies = currencyBalances.map(x => x.loyaltyCurrencyId);
		const currencySet = new Set(currencies);

		if (currencySet.size > 1) {
			console.error('Multiple possible point currencies found for refund');
			throw new Error('Multiple possible point currencies found for refund');
		}

		// const originalAmount = transactions.reduce((acc, x) => acc + (x. || 0), 0);
		var originalPoints = 0;
		var refundPoints = 0;
		if(kind == 'void') {
			originalPoints = filteredTransactions.reduce((acc, x) => acc + (x.amount || 0), 0);
			refundPoints = originalPoints
		}
		else {
			originalPoints = filteredTransactions.reduce((acc, x) => acc + (x.amount || 0), 0);
			const refundRatio = Math.min(refundAmount / originalAmount, 1);
			refundPoints = Math.floor(refundRatio * originalPoints);
		}
		if (!currencies[0]) {
			console.warn(`No currency, refund may be for order prior to Raleon (Order ${orderId}), skipping`);
			return;
		}

		if (!refundPoints) {
			console.warn(`No points to refund for order ${orderId}`);
		}

		if (!currencies[0]) {
			console.warn(`No currency, refund may be for order prior to Raleon (Order ${orderId}), skipping`);

			return;
		}

		if (!refundPoints) {
			console.warn(`No points to refund for order ${orderId}`);
		}

		return this.updateBalanceAndLog(currencies[0], {
			info: `Points subtracted for refund`,
			balanceChange: -refundPoints,
			raleonUserId: raleonUserId,
			orderId: orderId,
			skipRewards: false
		});
	}


	private async grantPointsBalanceTriggerRewards(currencyBalanceRecord: LoyaltyCurrencyBalance, raleonUserId: number) {
		// find all ways to earn with condition type points-balance
		const identities = await this.raleonUserIdentityRepository.find({
			where: {
				raleonUserId: raleonUserId
			},
			include: [
				{
					relation: 'raleonUser',
					scope: {
						include: [
							{
								relation: 'loyaltyCurrencyBalance',
								scope: {
									include: [
										{
											relation: 'loyaltyCurrencyTxLogs'
										}
									]
								}
							},
						]
					}
				}
			]
		});

		await this.loyaltyTtmPointsService.calculateTtmCurrencyGrants(currencyBalanceRecord, true, true);

		const identitiesByOrgId = new Map<number, Array<RaleonUserIdentityWithRelations>>();
		for (const identity of identities) {
			if (!identitiesByOrgId.has(identity.orgId!)) {
				identitiesByOrgId.set(identity.orgId!, []);
			}

			identitiesByOrgId.get(identity.orgId!)!.push(identity);
		}

		for (const [orgId, orgIdentities] of identitiesByOrgId) {
			await this.tierService.reassignUsersForAllTiers(orgId, orgIdentities);
		}
	}
}

