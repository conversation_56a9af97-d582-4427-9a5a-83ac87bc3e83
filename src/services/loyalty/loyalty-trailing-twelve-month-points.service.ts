import {repository} from '@loopback/repository';
import {LoyaltyCurrencyBalanceRepository, LoyaltyCurrencyTxLogRepository} from '../../repositories';
import {injectable} from '@loopback/core';
import {LoyaltyCurrencyBalance} from '../../models';

@injectable()
export class LoyaltyTrailingTwelveMonthPointsService {

	constructor(
		@repository(LoyaltyCurrencyBalanceRepository)
		public loyaltyCurrencyBalanceRepository: LoyaltyCurrencyBalanceRepository,
		@repository(LoyaltyCurrencyTxLogRepository)
		public loyaltyCurrencyTxLogRepository: LoyaltyCurrencyTxLogRepository,
	) {}

	async calculateTtmCurrencyGrants(existingBalanceRecord: LoyaltyCurrencyBalance, updateBalanceRecord = false, ignoreCachedTtmTotal = false) {
		const oneYearAgo = new Date();
		oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);

		if (!ignoreCachedTtmTotal && existingBalanceRecord.earliestTtmGrantDate && new Date(existingBalanceRecord.earliestTtmGrantDate) > oneYearAgo) {
			return existingBalanceRecord.trailingTwelveMonthGrantTotal ?? 0;
		}

		const transactions = await this.loyaltyCurrencyTxLogRepository.find({
			where: {

				loyaltyCurrencyBalanceId: existingBalanceRecord!.id,
				date: {
					gte: oneYearAgo.toISOString(),
				},
				amount: {
					gt: 0
				}
			}
		});

		const earliestTransactionDate = transactions.reduce((earliest, transaction) => {
			const date = new Date(transaction.date);
			return date < earliest ? date : earliest;
		}, new Date());

		const result = transactions
			.filter(x => (x.amount || 0) > 0)
			.reduce((sum, transaction) => sum + (transaction.amount || 0), 0);

		if (updateBalanceRecord) {
			existingBalanceRecord!.trailingTwelveMonthGrantTotal = result;
			existingBalanceRecord!.earliestTtmGrantDate = earliestTransactionDate.toISOString();
			await this.loyaltyCurrencyBalanceRepository.updateById(existingBalanceRecord!.id, existingBalanceRecord!);
		}

		return result;
	}
}
