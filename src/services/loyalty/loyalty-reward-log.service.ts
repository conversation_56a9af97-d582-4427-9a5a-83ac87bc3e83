import {repository} from '@loopback/repository';
import {LoyaltyCurrencyBalanceRepository, LoyaltyCurrencyTxLogRepository} from '../../repositories';
import {InventoryCoupon, InventoryCouponWithRelations, LoyaltyCurrency, LoyaltyCurrencyTxLog} from '../../models';
import {injectable} from '@loopback/core';
import {LoyaltyRewardLogRepository} from '../../repositories/loyalty-reward-log.repository';

@injectable()
export class LoyaltyRewardLogService {

	constructor(
		@repository(LoyaltyRewardLogRepository)
		public loyaltyRewardLogRepository: LoyaltyRewardLogRepository,
	) {}

	async createLogEntryForCoupon(coupon: Partial<InventoryCouponWithRelations>, granted?: boolean, orderId?: string, itemId?: string, earnEffectId?: number) {
		return this.createLogEntryForCoupons([coupon], granted, orderId, itemId, earnEffectId);
	}

	async createLogEntryForCoupons(coupons: Array<Partial<InventoryCouponWithRelations>>, granted?: boolean, orderId?: string, itemId?: string, earnEffectId?: number) {
		await this.loyaltyRewardLogRepository.createAll(coupons.map(coupon => ({
			date: new Date().toISOString(),
			redeemed: !granted,
			granted: granted || false,
			inventoryCouponId: coupon.id,
			raleonUserId: coupon.raleonUserId,
			loyaltyRewardDefinitionId: coupon.loyaltyRewardDefinitionId,
			orderId,
			itemId,
			earnEffectId
		})));
	}
}
