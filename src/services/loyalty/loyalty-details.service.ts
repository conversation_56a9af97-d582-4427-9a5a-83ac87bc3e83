import {repository} from '@loopback/repository';
import {CurrencyRepository, FeatureSettingRepository, LoyaltyCampaignRepository, LoyaltyCurrencyBalanceRepository, LoyaltyCurrencyRepository, LoyaltyCurrencyTxLogRepository, LoyaltyProgramRepository, LoyaltyRewardLogRepository, OrganizationRepository, RaleonUserEarnLogRepository, RaleonUserIdentityRepository, VipTierRepository} from '../../repositories';
import {LoyaltyDetailsController} from '../../controllers';
import {inject, injectable, service} from '@loopback/core';
import {RaleonUserIdentity, RaleonUserIdentityWithRelations, LoyaltyProgramWithRelations, LoyaltyCampaignWithRelations, EarnConditionWithRelations, LoyaltyEarnWithRelations, RewardCouponWithRelations, EarnEffectWithRelations, VipTier, VipTierWithRelations, UserIdentity} from '../../models';
import {ShopifyApiInvoker} from '../shopify/shopify-api-invoker.service';
import {LoyaltyBalanceManager} from './loyalty-balance-manager.service';
import {LoyaltyTrailingTwelveMonthPointsService} from './loyalty-trailing-twelve-month-points.service';
import {filter, last} from 'cheerio/lib/api/traversing';
import {SegmentService} from '../shopify/segment.service';
import {FeatureService} from '../feature.service';
const NodeCache = require( "node-cache" );
const orderCache = new NodeCache({ stdTTL: 900, checkperiod: 120 });

interface VipTierData {
	tier: VipTierWithRelations;
	tierCampaign: LoyaltyCampaignWithRelations;
	entryWtes: Array<LoyaltyEarnWithRelations>;
	entryConditions: Array<VipTierEntryCondition>,
	entryCoupons: Array<RewardCouponWithRelations>;
}

interface VipTierEntryCondition {
	condition: EarnConditionWithRelations;
	conditionValue: number;
	amountType: string;
	previousCondition: EarnConditionWithRelations|null;
	previousConditionValue: number;
	passed: boolean;
	remainingValue: number;
	remainingPercent: number;
	progressedValue: number;
	progressedPercent: number;

	nextCondition: EarnConditionWithRelations|null;
	nextConditionValue: number;
}

export interface VipState {
    currentTier: null|VipTierData;
    nextTier: null|VipTierData,
	allTiers: Array<VipTierData>;
}

@injectable()
export class LoyaltyDetailsService {

	constructor(
		@repository(RaleonUserIdentityRepository)
		private raleonUserIdentityRepository: RaleonUserIdentityRepository,
		@repository(OrganizationRepository)
		private organizationRepository: OrganizationRepository,
		@repository(LoyaltyProgramRepository)
		private loyaltyProgramRepository: LoyaltyProgramRepository,
		@repository(LoyaltyCampaignRepository)
		private loyaltyCampaignRepository: LoyaltyCampaignRepository,
		@repository(LoyaltyRewardLogRepository)
		private loyaltyRewardLogRepository: LoyaltyRewardLogRepository,
		@repository(LoyaltyCurrencyBalanceRepository)
		private loyaltyCurrencyBalanceRepository: LoyaltyCurrencyBalanceRepository,
		@repository(RaleonUserEarnLogRepository)
		private raleonUserEarnLogRepository: RaleonUserEarnLogRepository,
		@service(ShopifyApiInvoker)
		private shopifyApiInvoker: ShopifyApiInvoker,
		@service(LoyaltyTrailingTwelveMonthPointsService)
		private loyaltyTtmPointsService: LoyaltyTrailingTwelveMonthPointsService,
		@repository(VipTierRepository)
		private vipTierRepository: VipTierRepository,
		@repository(LoyaltyCurrencyTxLogRepository)
		private loyaltyCurrencyTxLogRepository: LoyaltyCurrencyTxLogRepository,
		@repository(FeatureSettingRepository)
		private featureSettingRepository: FeatureSettingRepository,
		@repository(CurrencyRepository)
		private currencyRepository: CurrencyRepository,
		@service(SegmentService)
		private segmentService: SegmentService,
		// @service(FeatureService)
		// private featureService: FeatureService,
	) {}

	async getLoyaltyDetails(orgId: number, raleonUserId: number, preview = false, filterForLoyaltyUi = true, filterForAdminUi = false, includeTtmGrantTotal = false): Promise<{
		programs: Array<LoyaltyProgramWithRelations>,
		campaigns: Array<LoyaltyCampaignWithRelations>,
		balances: any,
		vipState: any,
		logs: any,
		birthday: Date|undefined
	}> {
		// if (await this.featureService.isFeatureAvailable('loyalty-app', orgId) === false) {
		// 	return {
		// 		programs: [],
		// 		campaigns: [],
		// 		balances: [],
		// 		vipState: null,
		// 		logs: []
		// 	}
		// }

		// const freeProductEnabled = await this.featureService.isFeatureEnabled('reward-free-products', orgId);

		const startTime = Date.now();
		console.log(`get loyalty details for orgId: ${orgId}: ${raleonUserId}`)
		const userIdentity = await this.raleonUserIdentityRepository.findOne({
			where: {
				raleonUserId: raleonUserId,
				identityType: 'customer_id',
				orgId: orgId
			},
			include: [
				{
					relation: 'vipTier'
				}
			]
		});

		console.log(`User Identity Fetch Time: ${Date.now() - startTime}ms`);

		if (!userIdentity && !preview) {
			throw new Error('No user found');
		}

		const currentOrg = await this.organizationRepository.findById(orgId);
		const loyaltySegment = userIdentity?.loyaltySegment || 'not loyal';

		console.log(`Organization Fetch Time: ${Date.now() - startTime}ms`);

		let programs = await this.loyaltyProgramRepository.find({
			where: {
				orgId: orgId,
			},
			include: [
				{
					relation: 'loyaltyCurrencies',
				}
			]
		});

		//Go through and gather campaigns for each program that is active
		let campaigns: any[] = [];
		for (let program of programs) {
			if (program.active || preview) {
				let whereFilter: any = {
					loyaltyProgramId: program.id,
				};
				if (!preview) {
					whereFilter.active = true;
				}
				let programCampaigns = await this.loyaltyCampaignRepository.find({
					where: whereFilter,
					include: [
						{
							relation: 'loyaltyEarns',
							scope: {
								order: ['priority ASC'],
								include: [
									{
										relation: 'earnEffects',
										scope: {
											include: [
												{
													relation: 'loyaltyCurrency',
													scope: {
														fields: {
															conversionToUSD: false
														}
													}
												},
												{
													relation: 'loyaltyRewardDefinition',
													scope: {
														include: [
															{
																relation: 'rewardCoupon',
															}
														]
													}
												}
											]
										}
									},
									{ relation: 'earnConditions' }
								],
								where: {
									active: {inq: [null, true]},
									recommendationState: {inq: ['NONE', 'APPROVED_RECOMMENDATION']}
								}
							}
						},
						{
							relation: 'loyaltyRedemptionShopItems',
							scope: {
								order: ['priority ASC'],
								include: [
									{
										relation: 'loyaltyRewardDefinition',
										scope: {
											fields: {
												startingInventory: false,
												redeemed: false,
												granted: false,
											},
											include: [{relation: 'rewardCoupon', }]
										}
									}
								],
								where: {
									active: true,
									recommendationState: {inq: ['NONE', 'APPROVED_RECOMMENDATION']}
								}
							}
						},
						{
							relation: 'staticEffects',
							scope: {
								include: [
									{
										relation: 'loyaltyRewardDefinition',
										scope: {
											include: ['rewardCoupon'],
										},
								}],
								where: {
									active: true
								}
							},
						}
					]
				});
				campaigns = campaigns.concat(programCampaigns);
			}
		}
		if (!preview) {
			campaigns = await this.filterCampaignsForLoyalty(loyaltySegment!, campaigns, userIdentity);
		}

		campaigns = campaigns.map((c: any) => ({
			...c,
			loyaltyEarns: c.loyaltyEarns?.filter((e: any) => e.earnConditions && e.earnConditions.every((condition: any) => condition.type !== 'referred-customer-bonus')),
		}));

		const orgLanguage = currentOrg?.language || 'en';

		this.sortCampaignsLoyaltyFirst(campaigns, userIdentity);
		await this.filterUserRedemptionsAndWTE(campaigns, raleonUserId, userIdentity);
		let balances = await this.createLoyaltyCurrencyBalances(raleonUserId, programs, includeTtmGrantTotal);
		console.log(`Programs Fetch Time: ${Date.now() - startTime}ms`);

		//Grab all loyalty reward logs for the user for each loyaltyrewarddefinitionid
		//Then count the number of redeems and granted for each reward definition
		//Then add that to the loyaltyrewarddefinition object
		let rewardLogs = await this.loyaltyRewardLogRepository.find({
			where: {
				raleonUserId: raleonUserId
			}
		});
		let shopifyOrders: any = [];
		for (let campaign of campaigns) {
			//iterate through each loyaltyearn and add the condition string
			if (campaign.loyaltyEarns && campaign.loyaltyEarns.length > 0) {
				for (let i = 0; i < campaign.loyaltyEarns.length; i++) {
					if (campaign.loyaltyEarns[i].isRecommendation || campaign.loyaltyEarns[i].ignoreRecommendation) continue;
					let earnConditionGroups = LoyaltyDetailsController.groupEarnConditions(campaign.loyaltyEarns[i].earnConditions);
					if (earnConditionGroups['nth-purchase'] || (earnConditionGroups['timed-purchase'] && campaign.evergreen === false)) {
						if (shopifyOrders.length === 0) {
							shopifyOrders = await this.fetchShopifyOrders(orgId, userIdentity);
						}
						let conditionKey = 'nth-purchase' in earnConditionGroups ? 'nth-purchase' : 'timed-purchase';
						const evaluationResult = LoyaltyDetailsController.evaluatePurchaseConditions(campaign, shopifyOrders, earnConditionGroups[conditionKey], conditionKey, orgId, orgLanguage);
						if (evaluationResult.shouldRemoveEarn) {
							campaign.loyaltyEarns.splice(i, 1);
							i--;
							continue;
						}
						campaign.loyaltyEarns[i].progress = evaluationResult.progress;
						campaign.loyaltyEarns[i].daysLeft = evaluationResult.daysLeft;
					}

					if (earnConditionGroups['referrer-bonus']) {
						for (let j = 0; j < campaign.loyaltyEarns[i].earnEffects?.length; j++) {
							const [rewardName, rewardDescription] = LoyaltyDetailsController.generateReferralRewardText(campaign.loyaltyEarns[i].earnEffects?.[j], orgId, orgLanguage);
							campaign.loyaltyEarns[i].earnEffects[j].name = rewardDescription;
							campaign.loyaltyEarns[i].earnEffects[j].description = rewardDescription;
						}
					}
					let orgCurrency = await this.currencyRepository.findOne({
						where: { organizationId: orgId },
						include: [ { relation: 'supportedCurrencies' } ]
					});
					let conditionStrings = LoyaltyDetailsController.generateConditionStrings(campaign, earnConditionGroups, orgId, orgLanguage, orgCurrency!, currentOrg?.externalDomain);
					let rewardStrings = [] as Array<string>;
					let pillStrings = LoyaltyDetailsController.generatePillStrings(campaign.loyaltyEarns[i], orgId, orgLanguage, orgCurrency!);

					campaign.loyaltyEarns[i] = {
						...campaign.loyaltyEarns[i],
						instructions: conditionStrings,
						rewardStrings: rewardStrings,
						pillStrings: pillStrings
					}
				}
			}

			campaign.staticEffects = (campaign.staticEffects || []).map((effect: any) => {
				const startDate = new Date(campaign.startdate);
				const endDate = new Date(campaign.enddate);
				const currentDate = new Date();
				const timeDiff = endDate.getTime() - currentDate.getTime();
				const daysDifference = Math.ceil(timeDiff / (1000 * 3600 * 24));

				return {
					...effect,
					daysLeft: campaign.evergreen ? undefined : `${daysDifference} day${daysDifference > 1 ? 's' : ''} left`
				}
			});

			if (!campaign.loyaltyRedemptionShopItems) continue;
			for (let redemption of campaign.loyaltyRedemptionShopItems) {
				if (redemption.isRecommendation || redemption.ignoreRecommendation) continue;
				let relevantLogs = rewardLogs.filter((log: any) => log.loyaltyRewardDefinitionId === redemption.loyaltyRewardDefinitionId);
				let timeToExpiration = LoyaltyDetailsController.calculateTimeToExpiration(campaign.enddate, new Date());
				if (campaign.evergreen) {
					timeToExpiration = {
						timeVal: 0,
						timeLabel: 'Ongoing'
					}
				}
				if (relevantLogs.length > 0) {
					let numUserRedeemed = relevantLogs.filter((log: any) => log.redeemed).length;
					let numUserGranted = relevantLogs.filter((log: any) => log.granted).length;
					redemption.loyaltyRewardDefinition = {
						...redemption.loyaltyRewardDefinition,
						numUserRedeemed: numUserRedeemed,
						numUserGranted: numUserGranted,
						timeVal: timeToExpiration.timeVal,
						timeLabel: timeToExpiration.timeLabel,
						redemptionTimeLeftString: !campaign.evergreen ? `${timeToExpiration.timeVal} ${timeToExpiration.timeLabel} to redeem` : ''
					}

				}
				else {
					redemption.loyaltyRewardDefinition = {
						...redemption.loyaltyRewardDefinition,
						numUserRedeemed: 0,
						numUserGranted: 0,
						timeVal: timeToExpiration.timeVal,
						timeLabel: timeToExpiration.timeLabel,
						redemptionTimeLeftString: !campaign.evergreen ? `${timeToExpiration.timeVal} ${timeToExpiration.timeLabel} to redeem` : ''
					}
				}
			}
		}

		const vipState = await this.processVipState(userIdentity!, campaigns, balances, orgLanguage, currentOrg?.externalDomain!, orgId, preview);

		let userPointsLogs: any[] = [];
		if(preview) {
			userPointsLogs.push({
				info: "Fake transaction for preview mode",
				date: new Date(),
				amount: 100,
			})

			userPointsLogs.push({
				info: "Fake transaction for preview mode",
				date: new Date(),
				amount: 500,
			})

			userPointsLogs.push({
				info: "Fake transaction for preview mode",
				date: new Date(),
				amount: -100,
			})
		}
		else {
			userPointsLogs = await this.loyaltyCurrencyTxLogRepository.find({
				where: {
					loyaltyCurrencyBalanceId: balances[0]?.id
				}
			});
		}

		return {
			programs: programs,
			campaigns: campaigns,
			balances: balances,
			vipState,
			logs: userPointsLogs,
			birthday: userIdentity?.birthday
		};
	}

	private async filterCampaignsForLoyalty(loyaltySegment: string, campaigns: any[], raleonUserIdentity?: (RaleonUserIdentity & RaleonUserIdentityWithRelations | null)): Promise<any[]> {
		const segments = campaigns.map((c: any) => c.loyaltySegmentType).filter(x => x);
		const segmentMembership = await Promise.all(segments.map((segment: any) =>
			this.segmentService
				.isCustomerInSegment(raleonUserIdentity!.orgId!, raleonUserIdentity!.identityValue, segment)
				.then((isMember: boolean) => ({ segment, isMember }))
		));


		return campaigns.filter((campaign: any) => {
			const isUserVipTier = (raleonUserIdentity?.vipTier?.loyaltyCampaignId === campaign.id);
			if (isUserVipTier) {
				return true;
			}

			if (!campaign.live) return false;

			if (campaign.loyaltySegmentType) {
				const segment = segmentMembership.find((x: any) => x.segment === campaign.loyaltySegmentType);
				if (!segment) return false;
				if (!segment.isMember) return false;
			}
			if (!campaign.loyaltySegment) return true;
			//include unclassified users in very loyal campaigns
			if (campaign.loyaltySegment.toUpperCase() === 'VERY LOYAL' && loyaltySegment?.toUpperCase() === 'UNCLASSIFIED') return true;
			return campaign.loyaltySegment === loyaltySegment || campaign.loyaltySegment.toUpperCase() === 'EVERYONE';
		});
	}

	private sortCampaignsLoyaltyFirst(campaigns: any[], raleonUserIdentity?: (RaleonUserIdentity & RaleonUserIdentityWithRelations | null)): void {
		campaigns.sort((a: any, b: any) => {
			const isAUserVipTier = (raleonUserIdentity?.vipTier?.loyaltyCampaignId === a.id);
			const isBUserVipTier = (raleonUserIdentity?.vipTier?.loyaltyCampaignId === b.id);

			if (isAUserVipTier && !isBUserVipTier) return -1;
			if (!isAUserVipTier && isBUserVipTier) return 1;

			if (a.loyaltySegment && !b.loyaltySegment) return -1;
			if (!a.loyaltySegment && b.loyaltySegment) return 1;
			return 0;
		});
	}

	private async filterUserRedemptionsAndWTE(campaigns: any[], raleonUserId: number, raleonUserIdentity: any): Promise<void> {
		const userLogs = await this.loyaltyRewardLogRepository.find({
			where: {
				raleonUserId: raleonUserId
			}
		});

		const logsByRewardDef = userLogs.reduce((acc, log) => {
			if (!acc.has(log.loyaltyRewardDefinitionId)) {
				acc.set(log.loyaltyRewardDefinitionId, []);
			}
			const logs = acc.get(log.loyaltyRewardDefinitionId!);
			if (logs) {
				logs.push(log);
			}
			return acc;
		}, new Map<number, any[]>());

		for (let campaign of campaigns) {
			campaign.loyaltyRedemptionShopItems = campaign?.loyaltyRedemptionShopItems?.filter((item: any) => {
				const rewardDefinition = item.loyaltyRewardDefinition;
				if (!rewardDefinition) return false;
				const logs = logsByRewardDef.get(rewardDefinition.id);
				const rewardLogCount = logs?.length || 0;
				return rewardLogCount < rewardDefinition.maxUserRedemptions || rewardDefinition.maxUserRedemptions === -1;
			});

			if (!campaign?.loyaltyEarns || !campaign.loyaltyEarns?.length) continue;

			campaign.loyaltyEarns = await Promise.all(campaign?.loyaltyEarns?.map(async (earn: any) => {
				let completedCount = 0;
				const userEarnLog = await this.raleonUserEarnLogRepository.find({
					where: {
						raleonUserId: raleonUserId,
						loyaltyEarnId: earn.id,
						completed: true
					}
				});

				const now = new Date();
				const startOfYear = new Date(now.getFullYear(), 0, 1);
				const startOfNextYear = new Date(now.getFullYear() + 1, 0, 1);

				//Lets grab the first earnCondition and check its type
				if (earn.earnConditions && earn.earnConditions.length > 0) {
					const condition = earn.earnConditions[0];
					const singleEarnConditionTypes = [
						'specific-product-purchase',
						'collection-product-purchase',
						'nth-purchase',
						'timed-purchase',
						'welcome-bonus',
						'follow-on-instagram',
						'follow-on-tiktok',
						'follow-on-facebook',
						'follow-facebook-group',
						'follow-on-youtube',
						'follow-on-custom',
						'referred-customer-bonus',
					];

					if (singleEarnConditionTypes.includes(condition.type)) {
						if (userEarnLog.length > 0) {
							earn.completed = true;
							return earn;
						}
					}

					if(condition.type === 'welcome-bonus') {
						earn.completed = true;
						return earn;
					}

					if(condition.type === 'product-review' || condition.type === 'product-photo-review') {
						//number of reviews
						let userEarnLogCount = userEarnLog.length;
						if(condition.amount <= userEarnLogCount) {
							earn.completed = true;
							return earn;
						}
					}

					if (condition.type === 'birthday-bonus') {
						// Find any logs that occurred this year and if there is one, then the birthday bonus has been completed
						const birthdayLogs = userEarnLog.filter(log => {
							const logDate = log.createdDate ? new Date(log.createdDate) : null;
							return logDate && logDate >= startOfYear && logDate < startOfNextYear;
						});
						if (birthdayLogs.length > 0) {
							earn.completed = true;
							return earn;
						}
						if(raleonUserIdentity?.birthday) {
							earn.completed = true;
							return earn;
						}
					}
				}

				if (!earn.earnEffects || !earn.earnEffects.length) return earn;

				for (const earnEffect of earn.earnEffects) {
					const rewardDefinition = earnEffect.loyaltyRewardDefinition;
					if (rewardDefinition) {
						const logs = logsByRewardDef.get(rewardDefinition.id);
						const grantCount = logs?.length || 0;
						const completed = grantCount >= rewardDefinition.maxUserGrants;
						if (completed) {
							completedCount++;
						}
					}
				}

				earn.completed = completedCount === earn.earnEffects.length;
				return earn;
			}));
		}


	}


	private async createLoyaltyCurrencyBalances(raleonUserId: number, programs: LoyaltyProgramWithRelations[], includeTtmGrantTotal = false): Promise<Array<any>> {
		let currentBalances = new Array();
		for (let program of programs) {
			if (program.loyaltyCurrencies?.length) {
				for (let currency of program?.loyaltyCurrencies) {
					let existingBalances = await this.loyaltyCurrencyBalanceRepository.find({
						where: {
							raleonUserId: raleonUserId,
							loyaltyCurrencyId: currency.id
						}
					});

					if (existingBalances?.length === 0) {
						const newBalance = {
							raleonUserId: raleonUserId,
							loyaltyCurrencyId: currency.id,
						};

						try {
							await this.loyaltyCurrencyBalanceRepository.create(newBalance);
							existingBalances = await this.loyaltyCurrencyBalanceRepository.find({
								where: {
									raleonUserId: raleonUserId,
									loyaltyCurrencyId: currency.id
								}
							});
						} catch (error) {
							if (error.code === '23505') {
								// Handle unique constraint violation by re-querying for the balance
								existingBalances = await this.loyaltyCurrencyBalanceRepository.find({
									where: {
										raleonUserId: raleonUserId,
										loyaltyCurrencyId: currency.id
									}
								});
							} else {
								throw error;
							}
						}
					}

					const balance = {
						id: existingBalances[0].id,
						loyaltyCurrencyId: currency.id,
						balance: existingBalances[0].balance,
						ttmCurrencyGrants: undefined
					};

					if (includeTtmGrantTotal) {
						balance['ttmCurrencyGrants'] = await this.loyaltyTtmPointsService.calculateTtmCurrencyGrants(existingBalances[0], true) as any;
					}


					currentBalances.push(balance);
				}
			}
		}
		return currentBalances;
	}


	private async fetchShopifyOrders(orgId: number, userIdentity: any): Promise<any[]> {
		if (userIdentity) {
			const cachedData = orderCache.get(`order-info-${orgId}-${userIdentity.identityValue}`);
			if (cachedData) {
				console.log("Using cached order data")
				return JSON.parse(cachedData);
			}
			try {
				let customerId = userIdentity.identityValue;
				console.log(`getting orders MIKE`);
				const response = await this.shopifyApiInvoker.invokeAdminApi(
					orgId,
					'/get-orders?customerId=' + customerId,
					'GET',
				);
				console.log(`mike orders response: ${JSON.stringify(response)}`);

				orderCache.set(`order-info-${orgId}-${userIdentity.identityValue}`, JSON.stringify(response.orders), 5 * 60);
				return response.orders;
			} catch (error) {
				console.error('Error fetching orders from Shopify:', error);
				return [];
			}
		}
		return [];
	}


	private async processVipState(
		identity: RaleonUserIdentity,
		campaigns: LoyaltyCampaignWithRelations[],
		balances: Array<{loyaltyCurrencyId: number, balance: number, ttmCurrencyGrants?: number}>,
		orgLanguage: string,
		externalDomain: string,
		orgId: number,
		preview: boolean = false
	): Promise<VipState|undefined> {

        // TODO: this will need to be refactored to support multiple entry condition types

        // const foundationalCampaign = campaigns?.find((x: any) => x.evergreen && x.active && x.loyaltySegment === 'Everyone');
        // const currentVipTierCampaign = campaigns?.find((x: any) => x.loyaltySegment === 'vip' && x.active === true);

		// const featureStates = await this.featureService.getFeatureStates(identity?.orgId!);

		// const vipFeatureSetting = featureStates.features.find((x: any) => x.featureId === 'vip');

		const vipFeatureSetting = await this.featureSettingRepository.findOne({
			where: {
				organizationId: orgId || identity?.orgId,
				name: 'vip'
			}
		});
		// const vipLiveAndEnabled = vipFeatureSetting?.enabled && vipFeatureSetting?.live;

		let orgCurrency = await this.currencyRepository.findOne({
			where: { organizationId: orgId || identity?.orgId },
			include: [ { relation: 'supportedCurrencies' } ]
		});

		let previewVipTierId = 0;
		if(preview) {
			let tier = campaigns?.find((x: any) => x.loyaltySegment === 'vip' && x.active === true);
			if(!tier || !tier.id) {
				return {
					currentTier: null,
					nextTier: null,
					allTiers: [],
				};
			}
			previewVipTierId = tier?.id;
		}

		if (!identity && !preview || (!vipFeatureSetting?.enabled)) {
			return {
				currentTier: null,
				nextTier: null,
				allTiers: []
			};
		}

		const officialVipTierId = identity?.vipTierId || previewVipTierId;

		const vipTierIds = campaigns
			.map(x => x?.loyaltyEarns || [])
			.flat()
			.map(earn => earn?.earnEffects || [])
			.flat()
			.filter(effect =>
				(effect as EarnEffectWithRelations)?.loyaltyRewardDefinition?.rewardCoupon?.amountType === 'vip-tier'
			).map(effect =>
				(effect as EarnEffectWithRelations)?.loyaltyRewardDefinition?.rewardCoupon?.amount
			).filter(x => x);

		const vipTiers = await this.vipTierRepository.find({
			where: {
				id: { inq: vipTierIds }
			},
			include: [{
				relation: 'loyaltyCampaign',
				scope: {
					include: [
						{
							relation: 'loyaltyEarns',
							scope: {
								order: ['priority ASC'],
								include: [
									{
										relation: 'earnEffects',
										scope: {
											include: [
												{
													relation: 'loyaltyCurrency',
													scope: {
														fields: {
															conversionToUSD: false
														}
													}
												},
												{
													relation: 'loyaltyRewardDefinition',
													scope: {
														include: [
															{
																relation: 'rewardCoupon',
															}
														]
													}
												}
											]
										}
									},
									{ relation: 'earnConditions' }
								],
								where: {
									active: {inq: [null, true]},
									recommendationState: {inq: ['NONE', 'APPROVED_RECOMMENDATION']}
								}
							}
						},
						{
							relation: 'loyaltyRedemptionShopItems',
							scope: {
								order: ['priority ASC'],
								include: [
									{
										relation: 'loyaltyRewardDefinition',
										scope: {
											fields: {
												startingInventory: false,
												redeemed: false,
												granted: false,
											},
											include: [{relation: 'rewardCoupon', }]
										}
									}
								],
								where: {
									active: true,
									recommendationState: {inq: ['NONE', 'APPROVED_RECOMMENDATION']}
								}
							}
						},
						{
							relation: 'staticEffects',
							scope: {
								include: [
									{
										relation: 'loyaltyRewardDefinition',
										scope: {
											include: ['rewardCoupon'],
										},
								}],
								where: {
									active: true
								}
							},
						}
					]
				}
			}]
		});

        const vipEntryWtes: Array<LoyaltyEarnWithRelations> = campaigns
            .map((x: any) => x.loyaltyEarns || [])
            .flat()
            .filter((x: any) => x?.earnConditions?.some?.((y: any) => y?.type === 'vip-entry'));

		let currentTierData: VipTierData|null = null;
		let nextTierData: VipTierData|null = null;
		let lastIterationTierData: VipTierData;
		const allTiers: Array<VipTierData> = vipTiers
			.sort((tierA, tierB) => {
				// TODO: just add ranking field to VipTier
				const conditionA = vipEntryWtes.find(earn =>
					earn?.earnEffects?.some?.(effect =>
						(effect as EarnEffectWithRelations)?.loyaltyRewardDefinition?.rewardCoupon?.amountType === 'vip-tier' &&
						(effect as EarnEffectWithRelations)?.loyaltyRewardDefinition?.rewardCoupon?.amount === tierA.id
				))?.earnConditions[0]?.amount;
				const conditionB = vipEntryWtes.find(earn =>
					earn?.earnEffects?.some?.(effect =>
						(effect as EarnEffectWithRelations)?.loyaltyRewardDefinition?.rewardCoupon?.amountType === 'vip-tier' &&
						(effect as EarnEffectWithRelations)?.loyaltyRewardDefinition?.rewardCoupon?.amount === tierB.id
				))?.earnConditions?.[0]?.amount;

				return (conditionA || 0) - (conditionB || 0);
			})
			.filter(tier => tier.loyaltyCampaign?.active)
			.map((tier, index, array) => {
				const previousTier = array[index - 1];
				const nextTier = array[index + 1];

				const tierCampaign = campaigns.find(x => x?.id === tier?.loyaltyCampaignId) || tier.loyaltyCampaign;
				let entryWtes = vipEntryWtes.filter(earn =>
					(earn?.earnEffects || [])
						.some(effect =>
							(effect as EarnEffectWithRelations)?.loyaltyRewardDefinition?.rewardCoupon?.amountType === 'vip-tier' &&
							(effect as EarnEffectWithRelations)?.loyaltyRewardDefinition?.rewardCoupon?.amount === tier.id
				));

				const entryCoupons = entryWtes.map(earn =>
					(earn?.earnEffects || [])
						.find(effect => (effect as EarnEffectWithRelations)?.loyaltyRewardDefinition?.rewardCoupon?.amountType === 'vip-tier'))
						.map(effect => (effect as EarnEffectWithRelations)?.loyaltyRewardDefinition?.rewardCoupon);

				const entryConditions = entryWtes.map(earn => {
					const amountType = 'points-ttm';
					const condition = earn?.earnConditions?.find?.(condition => condition?.variable === amountType) as EarnConditionWithRelations;
					const conditionValue = condition?.amount || 0;
					const previousCondition = lastIterationTierData?.entryConditions?.[0]?.condition;
					const previousConditionValue = previousCondition?.amount || 0;

					const lastIterationMatchingCondition = lastIterationTierData?.entryConditions?.find(x => x.amountType === amountType);
					if (lastIterationMatchingCondition) {
						lastIterationMatchingCondition.nextCondition = condition;
						lastIterationMatchingCondition.nextConditionValue = conditionValue;
					}

					const currentValue = balances?.[0]?.ttmCurrencyGrants || 0;
					const passed = currentValue >= conditionValue;
					const tierSpread = conditionValue - previousConditionValue;
					const progressedValue = passed ? tierSpread : currentValue - previousConditionValue;
					const progressedPercent = passed ? 100 : (progressedValue / tierSpread) * 100;
					const remainingPercent = 100 - progressedPercent;
					const remainingValue = passed ? 0 : conditionValue - currentValue;

					return {
						condition,
						amountType,
						conditionValue,
						previousConditionValue,
						previousCondition,
						passed,
						progressedPercent,
						progressedValue,
						remainingPercent,
						remainingValue
					} as VipTierEntryCondition;
				});

				if (tierCampaign?.loyaltyEarns?.length) {
					tierCampaign.loyaltyEarns = tierCampaign.loyaltyEarns.map((earn, index) => {
						if (tierCampaign.loyaltyEarns?.[index]?.earnConditions?.length === 0) return earn;
						let earnConditionGroups = LoyaltyDetailsController.groupEarnConditions(tierCampaign.loyaltyEarns?.[index]?.earnConditions);
						let conditionStrings = LoyaltyDetailsController.generateConditionStrings(
							tierCampaign,
							earnConditionGroups,
							identity?.orgId!,
							orgLanguage,
							orgCurrency!,
							externalDomain
						);

						return {
							...earn,
							instructions: conditionStrings
						} as any;
					});
				}

				const thisIterationTierData = {
					tier,
					tierCampaign,
					entryWtes,
					entryConditions,
					entryCoupons
				} as VipTierData;

				if (entryConditions.some(x => x.passed)) {
					currentTierData = thisIterationTierData;
				}

				if (!nextTierData && entryConditions.every(x => !x.passed)) {
					nextTierData = thisIterationTierData;
				}

				lastIterationTierData = thisIterationTierData;

				return thisIterationTierData;
			});

		if (officialVipTierId) {
			currentTierData = allTiers.find(x => x.tier?.id === officialVipTierId) || currentTierData!;
		}

		const previewingAndEnabled = preview && vipFeatureSetting?.enabled;
		const notPreviewingAndLive = !preview && vipFeatureSetting?.live;

		if (previewingAndEnabled) {
			return {
				currentTier: currentTierData,
				nextTier: nextTierData,
				allTiers
			};
		} else if (notPreviewingAndLive) {
			return {
				currentTier: currentTierData,
				nextTier: nextTierData,
				allTiers
			};
		} else {
			return {
				currentTier: null,
				nextTier: null,
				allTiers: []
			};
		}
	}
}
