import {repository} from '@loopback/repository';
import {HttpErrors} from '@loopback/rest';
import {DevDbDataSource} from '../../datasources';
import {LoyaltyEarnRepository, LoyaltyCampaignRepository, LoyaltyRewardDefinitionRepository, RaleonUserEarnLogRepository, InventoryCouponRepository, LoyaltyRewardLogRepository, RaleonUserIdentityRepository, CustomerOfferRepository, LoyaltyEventEmailRepository, CurrencyRepository} from '../../repositories';
import {LoyaltyBalanceManager} from './loyalty-balance-manager.service';
import {LoyaltyRewardLogService} from './loyalty-reward-log.service';
import {injectable, service} from '@loopback/core';
import {CustomerOfferWithRelations, EarnEffect, EarnEffectWithRelations, LoyaltyRewardDefinition, LoyaltyRewardDefinitionWithRelations, RaleonUserIdentity} from '../../models';
import {LoyaltyMembersService} from './loyalty-members.service';
import {InventoryCoupon, InventoryCouponWithRelations} from '../../models/loyalty/inventory-coupon.model';
import {EventStreamEvent, LoyaltyEventPublisher} from '../event-stream/loyalty-event-publisher.service';
import {convertCurrencyPlaceholdersToValues} from '../../controllers/admin-ui.controller';
import {MemcachedLockService} from '../mem-cached.service';

@injectable()
export class LoyaltyRedemptionService {

	public CLIENT_GRANTABLE_TYPES = ['follow-on-instagram', 'follow-on-tiktok', 'follow-on-facebook', 'follow-facebook-group', 'follow-on-youtube', 'follow-on-custom', 'birthday-bonus', 'click-to-redeem'];

	constructor(
		@repository(LoyaltyEarnRepository)
		public loyaltyEarnRepository: LoyaltyEarnRepository,
		@repository(LoyaltyCampaignRepository)
		public loyaltyCampaignRepository: LoyaltyCampaignRepository,
		@repository(LoyaltyRewardDefinitionRepository)
		public loyaltyRewardDefinitionRepository: LoyaltyRewardDefinitionRepository,
		@service(LoyaltyBalanceManager)
		public loyaltyBalanceManager: LoyaltyBalanceManager,
		@repository(RaleonUserEarnLogRepository)
		public raleonUserEarnLogRepository: RaleonUserEarnLogRepository,
		@service(LoyaltyRewardLogService)
		public loyaltyRewardLogService: LoyaltyRewardLogService,
		@repository(InventoryCouponRepository)
		public inventoryCouponRepository: InventoryCouponRepository,
		@repository(LoyaltyEventEmailRepository)
		public loyaltyEmailEventRepository: LoyaltyEventEmailRepository,
		@repository(LoyaltyRewardLogRepository)
		public loyaltyRewardLogRepository: LoyaltyRewardLogRepository,
		@service(LoyaltyMembersService)
		public loyaltyMembersService: LoyaltyMembersService,
		@repository(RaleonUserIdentityRepository)
		public raleonUserIdentityRepository: RaleonUserIdentityRepository,
		@service(LoyaltyEventPublisher)
		public loyaltyEventPublisher: LoyaltyEventPublisher,
		@repository(CustomerOfferRepository)
		private customerOfferRepository: CustomerOfferRepository,
		@repository(CurrencyRepository)
		private currencyRepository: CurrencyRepository,
		@service(MemcachedLockService)
    	private lockService: MemcachedLockService,
	) {}

	async grantBirthdayReward(
		earnId: number,
		raleonUserId: number,
		orgId: number,
		dataSource: DevDbDataSource
	) {
		const lockKey = this.lockService.createLockKey('birthday_reward', raleonUserId, earnId);
    	const lockTTL = 30;

		try {
			const locked = await this.lockService.acquire(lockKey, lockTTL);
			if (!locked) {
			  console.log('Failed to acquire lock, another process might be handling this reward');
			  return;
			}

			let granted = await this.validateNotGrantedThisYear(earnId, raleonUserId);
			if (granted) {
				return;
			}
			const earn = (await this.loyaltyEarnRepository.find({
				where: {
					id: earnId,
				},
				include: ['earnConditions', 'earnEffects']
			}))?.[0];

			if (!earn) {
				throw new HttpErrors.NotFound('Loyalty Earn not found');
			}

			const program = await this.loyaltyCampaignRepository.loyaltyProgram(earn?.loyaltyCampaignId);

			if (program.orgId != orgId) {
				throw new HttpErrors.UnprocessableEntity('Invalid earn. Organization does not match.');
			}

			if (earn.earnConditions) {
				const earnCondition = earn.earnConditions[0];
				if (!this.CLIENT_GRANTABLE_TYPES.includes(earnCondition.type)) {
					throw new HttpErrors.UnprocessableEntity('Invalid earn condition type');
				}
			}

			if (earn.earnEffects) {
				let granted = false;
				for (let effect of earn.earnEffects) {
					if (effect.type == 'points') {
						await this.loyaltyBalanceManager.updateBalanceAndLog(effect.loyaltyCurrencyId!, {
							raleonUserId,
							balanceChange: effect.points!,
							info: `Granted ${effect.points} points for ${earn.name}`,
							earnEffectId: effect.id,
							earnName: earn.name,
						});
						granted = true;
					}
					else {
						await this.grantReward(
							effect.loyaltyRewardDefinitionId!,
							orgId,
							raleonUserId,
							dataSource,
							undefined,
							undefined,
							effect.id,
							earn.earnConditions?.[0]?.type
						);
						granted = true;
					}
				}

				if (granted) {
					await this.raleonUserEarnLogRepository.create({
						raleonUserId,
						loyaltyEarnId: earn.id,
						completed: true
					});
				}
			}
		} finally {
			await this.lockService.release(lockKey);
		}

	}

	private async validateNotGrantedThisYear(earnId: number, raleonUserId: number) {
		console.log('Validating not granted', earnId, raleonUserId);
		const now = new Date();
		const startOfYear = new Date(now.getFullYear(), 0, 1);
		const startOfNextYear = new Date(now.getFullYear() + 1, 0, 1);

		const userLogs = await this.raleonUserEarnLogRepository.find({
			where: {
				raleonUserId: raleonUserId,
				loyaltyEarnId: earnId,
				completed: true,
				createdDate: {
					gte: startOfYear.toISOString(),
					lt: startOfNextYear.toISOString(),
        		}
			}
		});

		if (userLogs?.length) {
			return true;
		}
	}

	async grantReward(
		rewardDefinitionId: number,
		orgId: number,
		raleonUserId: number,
		dataSource: any,
		orderId?: string,
		itemId?: string,
		earnEffectId?: number,
		earnType?: string
	) {
		let rewardDefinition = (await this.loyaltyRewardDefinitionRepository.find({
			where: { id: rewardDefinitionId },
			include: [ { relation: 'rewardCoupon' } ]
		}))?.[0];
		//TODO: set daysToRedeem here and add it to the inventory coupon record below
		await this.validateGrant(rewardDefinition, raleonUserId);

		await this.incrementLoyaltyReward(rewardDefinition.id!, 'granted', dataSource);

		this.bulkExecuteGrants(rewardDefinition, orgId, [raleonUserId], dataSource, orderId, itemId, earnEffectId, earnType);
	}

	private async bulkExecuteGrants(
		rewardDefinition: LoyaltyRewardDefinitionWithRelations,
		orgId: number,
		raleonUserIds: Array<number>,
		dataSource: any,
		orderId?: string,
		itemId?: string,
		earnEffectId?: number,
		earnType?: string,
	): Promise<Array<InventoryCoupon>> {

		let expirationDays = (rewardDefinition as any).rewardCoupon?.expiresInDays;
		let currentDate = new Date();
		let expirationDate = new Date(currentDate.setDate(currentDate.getDate() + expirationDays));

		const coupons = await this.inventoryCouponRepository.createAll(raleonUserIds.map(raleonUserId => ({
			name: (rewardDefinition as any).rewardCoupon?.name,
			rewardCouponId: rewardDefinition.rewardCouponId,
			raleonUserId,
			expiration: expirationDate.toISOString(),
			orgId,
			used: false,
			hiddenFromLoyaltyUi: rewardDefinition.rewardCoupon?.hiddenFromLoyaltyUi,
			loyaltyGiveawayId: rewardDefinition.rewardCoupon?.loyaltyGiveawayId,
			loyaltyRewardDefinitionId: rewardDefinition.id,
		})));

		await this.loyaltyRewardLogService.createLogEntryForCoupons(coupons, true, orderId, itemId, earnEffectId);

		const couponEventData = coupons.map(coupon => ({
			couponId: coupon.uuid,
			rewardName: rewardDefinition.rewardCoupon?.name,
			expirationDate: expirationDate.toISOString(),
			type: earnType,
			raleonUserId: coupon.raleonUserId,
		}));
		await this.publishRewardGrantedEvents(orgId, raleonUserIds, couponEventData);
		return coupons;
	}

	async publishRewardGrantedEvents(orgId: number, raleonUserIds: number[], coupons: any[]) {
		const ruis = await this.raleonUserIdentityRepository.find({
			where: {
				raleonUserId: { inq: raleonUserIds },
				orgId,
			}
		});

		const currencies = await this.currencyRepository.find({
			where: {
				organizationId: orgId
			},
			include: [ { relation: 'supportedCurrencies' } ]
		});

		const orgCurrencies = new Map(currencies.map(currency => [currency.organizationId, currency]));

		const customerMap = new Map<number, RaleonUserIdentity>(
			ruis.map(rui => [rui.raleonUserId, rui])
		);

		for (const coupon of coupons) {
			const rui = customerMap.get(coupon.raleonUserId);
			const rewardName = coupon.rewardName.includes('{{currency_value') ?
				convertCurrencyPlaceholdersToValues(coupon.rewardName, orgCurrencies.get(coupon.orgid)!) :
				coupon.rewardName;

			if (coupon.type === 'birthday-bonus') {
				if (!rui?.birthday) continue;

				const birthday = new Date(rui?.birthday?.toISOString()!);
				const month = birthday.getMonth() + 1;
				const day = birthday.getDate();
				const year = birthday.getFullYear();

				await this.loyaltyEventPublisher.publishBirthdayRewardGrantedEvent(
					rui?.identityValue!,
					orgId,
					rewardName,
					`${month}/${day}/${year}`,
					coupon.couponId
				);
			} else {
				const expiration = new Date(coupon.expirationDate);
				const expMonth = expiration.getMonth() + 1;
				const expDay = expiration.getDate();
				const expYear = expiration.getFullYear();

				await this.loyaltyEventPublisher.publishRewardGrantedEvent(
					rui?.identityValue!,
					orgId,
					rewardName,
					`${expMonth}/${expDay}/${expYear}`,
					coupon.couponId
				);
			}
		}
	}


	async validateGrant(
		rewardDefinition: LoyaltyRewardDefinition,
		raleonUserId: number,
	) {
		if (!rewardDefinition) {
			throw new HttpErrors.NotFound('No loyalty reward definition found');
		}

		const userLogs = await this.loyaltyRewardLogRepository.find({
			where: {
				raleonUserId: raleonUserId,
				loyaltyRewardDefinitionId: rewardDefinition.id,
			}
		});

		const maxUserGrantSet =
			rewardDefinition.maxUserGrants !== null &&
			rewardDefinition.maxUserGrants !== undefined;

		if (maxUserGrantSet && (userLogs?.length || 0) >= rewardDefinition.maxUserGrants!) {
			throw new HttpErrors.UnprocessableEntity('Reward has been granted the maximum number of times');
		}
	}


	async incrementLoyaltyReward(id: number, type: string, dataSource: DevDbDataSource, count = 1){
		const sql = `
			UPDATE loyaltyrewarddefinition
			SET ${type} = ${type} + ${count}
			WHERE id = ${id};
		`;

		try {
			const result = await dataSource.execute(sql);
			console.log(result);
		} catch (err) {
			console.error(err);
		}
	}

	async grantAutoRedemptionReward(autoRedemptionEarnId: number, dataSource: any, overrideTargetRaleonUserIdentityIds?: Array<number>) {
		return this.bulkGrantAutoRedemptionRewards([autoRedemptionEarnId], dataSource, overrideTargetRaleonUserIdentityIds);
	}

	async bulkGrantAutoRedemptionRewards(autoRedemptionEarnIds: Array<number>, dataSource: any, overrideTargetRaleonUserIdentityIds?: Array<number>): Promise<void> {
		const earns = await this.loyaltyEarnRepository.find({
			where: {
				id: { inq: autoRedemptionEarnIds }
			},
			include: [
				{
					relation: 'earnEffects',
					scope: {
						include: [
							{
								relation: 'loyaltyRewardDefinition',
								scope: {
									include: [
										{
											relation: 'rewardCoupon'
										}
									]
								}
							}
						]
					}
				}
			]
		});

		console.log('Auto-granting rewards for earn ids: ' + autoRedemptionEarnIds.join(', '));

		const earnMemberIdentitiesMap = new Map<number, Array<RaleonUserIdentity>>();

		if (!overrideTargetRaleonUserIdentityIds || !Array.isArray(overrideTargetRaleonUserIdentityIds) || !overrideTargetRaleonUserIdentityIds.length) {
			const campaignIds = Array.from(new Set(earns.map(e => e.loyaltyCampaignId)));
			const campaignMemberIdentitiesMap = new Map<number, Array<RaleonUserIdentity>>();
			for (let campaignId of campaignIds) {
				const identities = await this.loyaltyMembersService.getCampaignMemberIdentities(campaignId);
				campaignMemberIdentitiesMap.set(campaignId, identities);
			}

			for (let earn of earns) {
				const campaignId = earn.loyaltyCampaignId;
				const identities = campaignMemberIdentitiesMap.get(campaignId);
				earnMemberIdentitiesMap.set(earn.id!, identities || []);
			}
		} else {
			const identities = await this.raleonUserIdentityRepository.find({
				where: {
					id: { inq: overrideTargetRaleonUserIdentityIds }
				},
				include: ['raleonUser']
			});

			for (let earn of earns) {
				earnMemberIdentitiesMap.set(earn.id!, identities);
			}
		}

		console.log(`Selected raleon user identitites for auto-redemption:`);
		try {
			console.log(JSON.stringify(
				Array.from(new Set(
					Array.from(earnMemberIdentitiesMap.values()).flat().map(x => x.id)
				))
			));
		} catch(e) {}

		for (let earn of earns) {
			console.log(`Processing earn: ${earn.id}`);

			const identities = earnMemberIdentitiesMap.get(earn.id!);
			if (!identities || !identities.length) {
				console.warn('No identities to auto-grant to for earn:', earn.id);
				continue;
			}

			const orgId = identities[0].orgId;
			if (!identities.every(i => i.orgId === orgId)) {
				throw new Error('Identities were from multiple orgs for earn: ' + earn.id);
			}

			console.log(`Auto-granting reward to ${identities.length} identities for earn: ${earn.id}`);

			await this.incrementLoyaltyReward(
				earn.earnEffects![0].loyaltyRewardDefinitionId!,
				'granted',
				dataSource,
				identities.length
			);

			await this.bulkExecuteGrants(
				(earn.earnEffects![0] as EarnEffectWithRelations).loyaltyRewardDefinition!,
				orgId!,
				identities.map(i => i.raleonUserId),
				dataSource,
				undefined,
				undefined,
				earn.earnEffects![0].id
			);
		}

		console.log('Auto-redemption rewards granted');
	}

	async bulkGrantOffers(offers: Array<CustomerOfferWithRelations>, orgId: number, dataSource: any): Promise<Array<InventoryCoupon>> {
		// const unsentOffers = offers.filter(o => !o.inventoryCouponId);

		const offersWithRewardsAndUsers = offers.filter(o => o.loyaltyRewardDefinition && o.raleonUser);
		const coupons = await this.inventoryCouponRepository.find({
			where: {
				raleonUserId: { inq: offersWithRewardsAndUsers.map(o => o.raleonUserId) },
				loyaltyRewardDefinitionId: { inq: offersWithRewardsAndUsers.map(o => o.loyaltyRewardDefinition.id) }
			}
		});

		const offersWithoutCoupons = offersWithRewardsAndUsers.filter(o =>
			//filter out offers that have already been granted a coupon
			!coupons.some(c =>
				c.raleonUserId === o.raleonUser!.id &&
				c.loyaltyRewardDefinitionId === o.loyaltyRewardDefinition!.id
			)
		);

		const rewards = offersWithoutCoupons.filter(o => o.loyaltyRewardDefinition).map(o => o.loyaltyRewardDefinition);
		const rewardIdSet = new Set(rewards.map(r => r.id));
		const rewardIds = Array.from(rewardIdSet);

		const allCoupons = [];

		for (const rewardId of rewardIds) {
			const reward = rewards.find(r => r.id === rewardId);
			const raleonUserIds = offersWithoutCoupons.filter(o => o.loyaltyRewardDefinition?.id === rewardId).map(o => o.raleonUserId);
			const coupons = await this.bulkExecuteGrants(reward!, orgId, raleonUserIds, dataSource, undefined, undefined, undefined);
			allCoupons.push(...coupons);
		}

		return allCoupons;
	}
}
