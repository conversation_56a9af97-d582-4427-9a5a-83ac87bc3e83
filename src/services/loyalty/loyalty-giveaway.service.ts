import {injectable, service} from '@loopback/core';
import {repository} from '@loopback/repository';
import {EarnEffectRepository, InventoryCouponRepository, LoyaltyEarnRepository, LoyaltyGiveawayRepository, LoyaltyRewardDefinitionRepository, RaleonUserIdentityRepository, RewardCouponRepository} from '../../repositories';
import {EarnEffectWithRelations, InventoryCoupon, LoyaltyEarnWithRelations, RaleonUser, RaleonUserIdentity} from '../../models';
import {LoyaltyRedemptionService} from './loyalty-redemption.service';
import AWS from 'aws-sdk';

@injectable()
export class LoyaltyGiveawayService {
	constructor(
		@repository(LoyaltyGiveawayRepository)
		private loyaltyGiveawayRepository: LoyaltyGiveawayRepository,
		@repository(RewardCouponRepository)
		private rewardCouponRepository: RewardCouponRepository,
		@repository(LoyaltyRewardDefinitionRepository)
		private loyaltyRewardDefinitionRepository: LoyaltyRewardDefinitionRepository,
		@repository(EarnEffectRepository)
		private earnEffectRepository: EarnEffectRepository,
		@repository(LoyaltyEarnRepository)
		private loyaltyEarnRepository: LoyaltyEarnRepository,
		@service(LoyaltyRedemptionService)
		private loyaltyRedemptionService: LoyaltyRedemptionService,
		@repository(InventoryCouponRepository)
		private inventoryCouponRepository: InventoryCouponRepository,
		@repository(RaleonUserIdentityRepository)
		private raleonUserIdentityRepository: RaleonUserIdentityRepository
	) {}

	async areGiveawayWTEsActive(giveawayId: number): Promise<boolean> {
		const giveawayWTEs = await this.findGiveawayWTEs(giveawayId);

		if (!giveawayWTEs.length) {
			return false;
		}

		if (giveawayWTEs.some(wte => !wte.active) && giveawayWTEs.some(wte => wte.active)) {
			throw new Error('Inconsistent WTE active states. Update with setGiveawayWTEsActive first.');
		}

		return giveawayWTEs.every(wte => wte.active);
	}

	async setGiveawayWTEsActive(giveawayId: number, active: boolean): Promise<void> {
		const giveawayWTEs = await this.findGiveawayWTEs(giveawayId);

		return this.updateActive(giveawayWTEs, active);
	}

	async startGiveaway(giveawayId: number): Promise<void> {
		// TODO: when we have a separate giveaway.launched property
		const giveaway = await this.loyaltyGiveawayRepository.findById(giveawayId, {
			include: ['loyaltyProgram']
		});
		if (!giveaway.launched) {
			throw new Error('Giveaway has not been launched and cant be started');
		}

		return this.setGiveawayWTEsActive(giveawayId, true);
	}

	async endGiveaway(giveawayId: number, dataSource: any): Promise<string> {
		const giveaway = await this.loyaltyGiveawayRepository.findById(giveawayId, {
			include: ['loyaltyProgram']
		});

		try {
			return await this.getCsvFromBucket(giveaway.loyaltyProgram!.orgId!, giveawayId);
		} catch (e) {
			console.log('No CSV found in bucket, continuing');
		}

		if (!giveaway.launched) {
			throw new Error('Giveaway has not been launched and cant be ended');
		}

		if (giveaway.endDate && new Date(giveaway.endDate) > new Date()) {
			throw new Error('Giveaway has not ended yet');
		}

		// TOOD: update giveaway endDate and/or status to mark it as ended if needed

		const giveawayWTEs = await this.findGiveawayWTEs(giveawayId);

		console.log('Disabling giveaway WTEs...');
		await this.updateActive(giveawayWTEs);

		console.log('Auto-granting eligible giveaway entries...');
		await this.autoGrantGiveawayEntries(giveawayWTEs, dataSource);

		console.log('Giveaway complete, generating CSV...');
		const csvContent = await this.generateCsv(giveawayId);

		console.log('Uploading CSV to bucket...');

		const result = await this.uploadCsvToBucket(giveaway.loyaltyProgram!.orgId!, giveawayId, csvContent);

		console.log('CSV uploaded to bucket, returning CSV content');

		return result;
	}

	async findGiveawayWTEs(giveawayId: number): Promise<Array<LoyaltyEarnWithRelations>> {
		const rewardCoupons = await this.rewardCouponRepository.find({
			where: {
				loyaltyGiveawayId: giveawayId
			}
		});

		const rewardDefinitions = await this.loyaltyRewardDefinitionRepository.find({
			where: {
				rewardCouponId: {
					inq: rewardCoupons.map(coupon => coupon.id!)
				}
			},
		});

		const earnEffects = await this.earnEffectRepository.find({
			where: {
				loyaltyRewardDefinitionId: {
					inq: rewardDefinitions.map(reward => reward.id!)
				}
			}
		});

		return this.loyaltyEarnRepository.find({
			where: {
				id: {
					inq: earnEffects.map(effect => effect.loyaltyEarnId!)
				}
			},
			include: [
				{
					relation: 'earnEffects',
					scope: {
						include: [
							{
								relation: 'loyaltyRewardDefinition',
								scope: {
									include: [
										{
											relation: 'rewardCoupon'
										}
									]
								}
							}
						]
					}
				},
				{
					relation: 'earnConditions',
				}
			]
		});
	}

	private async updateActive(giveawayWTEs: Array<LoyaltyEarnWithRelations>, active = false): Promise<any> {
		if (!giveawayWTEs.length) {
			return;
		}

		return this.loyaltyEarnRepository.updateAll({
			active
		}, {
			id: {
				inq: giveawayWTEs.map(wte => wte.id!)
			}
		});
	}

	private async autoGrantGiveawayEntries(giveawayWTEs: Array<LoyaltyEarnWithRelations>, dataSource: any): Promise<void> {
		// grant auto-redeem WTEs
		const autoRedeemWTEs = giveawayWTEs.filter(wte =>
			wte.active &&
			wte.earnConditions.some(x => x.type === 'auto-redeem') &&
			wte.earnEffects.some(x => (x as EarnEffectWithRelations).loyaltyRewardDefinition?.rewardCoupon?.amountType === 'giveaway-entry')
		);
		await this.loyaltyRedemptionService.bulkGrantAutoRedemptionRewards(autoRedeemWTEs.map(x => x.id!), dataSource);
	}

	private async findGiveawayEntries(giveawayId: number): Promise<Map<RaleonUserIdentity, Array<InventoryCoupon>>> {
		const allCoupons = await this.inventoryCouponRepository.find({
			where: {
				loyaltyGiveawayId: giveawayId,
			},
			include: ['rewardCoupon']
		});

		const filteredCoupons = allCoupons.filter(x => x.rewardCoupon?.amountType === 'giveaway-entry');

		const identityEntries = new Map<number, Array<InventoryCoupon>>();
		for (const filteredCoupon of filteredCoupons) {
			const count = filteredCoupon.rewardCoupon?.amount || 1;
			const userId = filteredCoupon.raleonUserId;

			if (!userId) {
				console.error(`InventoryCoupon ${filteredCoupon.id} is missing a RaleonUserId`);
				continue;
			}

			if (!identityEntries.has(userId)) {
				identityEntries.set(userId, []);
			}

			for (let i = 0; i < count; i++) {
				identityEntries.get(userId)!.push(filteredCoupon);
			}
		}

		const entries = new Map<RaleonUserIdentity, Array<InventoryCoupon>>();
		for (const userId of identityEntries.keys()) {
			const identity = await this.raleonUserIdentityRepository.findOne({
				where: {
					raleonUserId: userId,
					identityType: 'customer_id'
				}
			});
			if (!identity) {
				console.error(`RaleonUserId ${userId} is missing a customer_id identity`);
				continue;
			}

			entries.set(identity, identityEntries.get(userId)!);
		}
		return entries;
	}

	private async generateCsv(giveawayId: number): Promise<string> {
		const entries = await this.findGiveawayEntries(giveawayId);

		return `Customer_ID, Entry_Count\n${
			Array.from(entries.entries()).map(([identity, coupons]) =>
				`${identity.identityValue}, ${coupons.length}`
			).join('\n')
		}`;
	}

	private async uploadCsvToBucket(orgId: number, giveawayId: number, csvContent: string): Promise<string> {
		const AWS_ACCESS_KEY = process.env.API_ACCESS_KEY;
		const AWS_SECRET_KEY = process.env.API_SECRET_KEY;
		const filename = `org-${orgId}-giveaway-${giveawayId}.csv`;
		const s3 = new AWS.S3({
			accessKeyId: AWS_ACCESS_KEY,
			secretAccessKey: AWS_SECRET_KEY,
			region: 'us-east-1'
		});
		s3.putObject({
			Bucket: 'raleon-giveaway-csvs',
			Key: filename,
			Body: csvContent,
			ContentType: 'text/csv'
		}, (err: any, data: any) => {
			if (err) {
				console.error(err);
			}
		});

		return csvContent;
	}

	private async getCsvFromBucket(orgId: number, giveawayId: number): Promise<string> {
		const AWS_ACCESS_KEY = process.env.API_ACCESS_KEY;
		const AWS_SECRET_KEY = process.env.API_SECRET_KEY;
		const filename = `org-${orgId}-giveaway-${giveawayId}.csv`;
		const s3 = new AWS.S3({
			accessKeyId: AWS_ACCESS_KEY,
			secretAccessKey: AWS_SECRET_KEY,
			region: 'us-east-1'
		});
		const params = {
			Bucket: 'raleon-giveaway-csvs',
			Key: filename
		};
		const stream = s3.getObject(params).createReadStream();

		let csvContent = '';
		for await (const chunk of stream) {
			csvContent += chunk;
		}

		return csvContent;
	}
}
