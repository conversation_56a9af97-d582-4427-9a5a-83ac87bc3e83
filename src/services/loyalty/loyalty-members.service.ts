import {injectable, service} from '@loopback/core';
import {FeatureSettingRepository, LoyaltyCampaignRepository, RaleonUserIdentityRepository, RaleonUserRepository} from '../../repositories';
import {repository} from '@loopback/repository';
import {RaleonUser, RaleonUserIdentity} from '../../models';
import {SegmentService} from '../shopify/segment.service';
import {FeatureService} from '../feature.service';

@injectable()
export class LoyaltyMembersService {
	constructor(
		@repository(LoyaltyCampaignRepository)
		private loyaltyCampaignRepository: LoyaltyCampaignRepository,
		@repository(RaleonUserRepository)
		private raleonUserRepository: RaleonUserRepository,
		@repository(RaleonUserIdentityRepository)
		private raleonUserIdentityRepository: RaleonUserIdentityRepository,
		@service(SegmentService)
		private segmentService: SegmentService,
		// @service(FeatureService)
		// private featureService: FeatureService
		@repository(FeatureSettingRepository)
		private featureSettingRepository: FeatureSettingRepository
	){}

	async getCampaignMemberIdentities(campaignId: number, raleonUserId?: number): Promise<Array<RaleonUserIdentity>> {
		const campaign = await this.loyaltyCampaignRepository.findById(campaignId, {
			include: ['loyaltyProgram', 'vipTier']
		});
		const orgId = campaign.loyaltyProgram.orgId;
		const segment = campaign.loyaltySegment;

		const baseWhere: any = {
			orgId
		};

		if (raleonUserId) {
			baseWhere.raleonUserId = raleonUserId;
		}

		if (campaign.loyaltySegmentType) {
			return this.findShopifySegmentMemberIdentities(orgId, campaign.loyaltySegmentType, raleonUserId);
		} else if (segment) {
			switch (segment.toLowerCase()) {
				case 'everyone':
					return this.findAllMemberIdentities(orgId, raleonUserId);
				case 'vip':
					const vipFeatureSetting = await this.featureSettingRepository.findOne({
						where: {
							organizationId: orgId,
							name: 'vip'
						}
					});
					const vipFeatureLiveAndEnabled = vipFeatureSetting?.enabled && vipFeatureSetting?.live;

					if (!vipFeatureLiveAndEnabled) {
						console.warn('VIP membership was checked, but the VIP feature is not enabled/live/permitted');
						return [];
					}

					const vipTierId = campaign.vipTier?.id;
					if (!vipTierId) throw new Error('VIP Tier Campaign was missing VIP Tier');

					return this.getVipTierMemberIdentities(vipTierId, raleonUserId);
				default:
					return this.getSegmentMemberIdentities(orgId, segment, raleonUserId);
			}
		} else {
			return this.findAllMemberIdentities(orgId, raleonUserId);
		}
	}

	private async findAllMemberIdentities(orgId: number, raleonUserId?: number): Promise<Array<RaleonUserIdentity>> {
		console.log('Finding all member identities');
		const where: any = { orgId };
		if (raleonUserId) {
			where.raleonUserId = raleonUserId;
		}
		return this.raleonUserIdentityRepository.find({
			where,
			include: ['raleonUser']
		});
	}

	private async getSegmentMemberIdentities(orgId: number, segment: string, raleonUserId?: number): Promise<Array<RaleonUserIdentity>> {
		const where: any = {
			orgId,
			loyaltySegment: segment
		};
		if (raleonUserId) {
			where.raleonUserId = raleonUserId;
		}
		return this.raleonUserIdentityRepository.find({
			where,
			include: ['raleonUser']
		});
	}

	private async getVipTierMemberIdentities(vipTierId: number, raleonUserId?: number): Promise<Array<RaleonUserIdentity>> {
		const where: any = { vipTierId };
		if (raleonUserId) {
			where.raleonUserId = raleonUserId;
		}
		return this.raleonUserIdentityRepository.find({
			where,
			include: ['raleonUser']
		});
	}

	private async findShopifySegmentMemberIdentities(orgId: number, segment: string, raleonUserId?: number): Promise<Array<RaleonUserIdentity>> {
		console.log('Finding Shopify Segment Members');
		const customerIds = await this.segmentService.getSegmentCustomers(orgId, segment);
		const where: any = {
			orgId
		};
		if (raleonUserId) {
			where.raleonUserId = raleonUserId;
		} else {
			where.identityValue = {
				inq: customerIds
			}
		}
		return this.raleonUserIdentityRepository.find({
			where,
			include: ['raleonUser']
		});
	}
}
