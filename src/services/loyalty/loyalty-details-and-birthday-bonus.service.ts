import {inject, injectable, service} from '@loopback/core';
import {<PERSON>arn<PERSON>ondition, LoyaltyCampaign, LoyaltyEarn, RaleonUserEarnLog, RaleonUserIdentity} from '../../models';
import {LoyaltyCampaignController, LoyaltyDetailsController} from '../../controllers';
import {LoyaltyRedemptionService} from './loyalty-redemption.service';
import {LoyaltyDetailsService} from './loyalty-details.service';
import {repository} from '@loopback/repository';
import {LoyaltyEventEmailRepository, RaleonUserEarnLogRepository, RaleonUserIdentityRepository} from '../../repositories';
import {LoyaltyEventPublisher} from '../event-stream/loyalty-event-publisher.service';

@injectable()
export class LoyaltyDetailsAndBirthdayBonusService {
	constructor(
		@service(LoyaltyRedemptionService)
		private loyaltyRedemptionService: LoyaltyRedemptionService,
		@inject('datasources.dev_db')
		private devDbDataSource: any,
		@service(LoyaltyDetailsService)
		private loyaltyDetailsService: LoyaltyDetailsService,
		@repository(RaleonUserIdentityRepository)
		private raleonUserIdentityRepository: RaleonUserIdentityRepository,
		@repository('LoyaltyEventEmailRepository')
		private loyaltyEventEmailRepository: LoyaltyEventEmailRepository,
		@service(LoyaltyEventPublisher)
		private loyaltyEventPublisher: LoyaltyEventPublisher,
		@inject('controllers.LoyaltyCampaignController')
		private loyaltyCampaignController: LoyaltyCampaignController,
		@repository(RaleonUserEarnLogRepository)
		private raleonUserEarnLogRepository: RaleonUserEarnLogRepository,
	) { }

	async getLoyaltyDetailsAndGrantBirthdayBonusIfApplicable(
		orgId: number,
		raleonUserId: number,
		preview: boolean = false,
		filterForLoyaltyUi?: boolean,
		filterForAdminUi?: boolean,
		includeTtmGrantTotal?: boolean
	) {
		console.log(`Fetching loyalty details for user ${raleonUserId} in org ${orgId}`);
		const result = await this.loyaltyDetailsService.getLoyaltyDetails(orgId, raleonUserId, preview, filterForLoyaltyUi, filterForAdminUi, includeTtmGrantTotal);
		const campaigns = result.campaigns;
		const vipCampaigns = result.vipState?.allTiers.map((tier: any) => tier.tierCampaign);
		const allCampaigns = [...campaigns, ...vipCampaigns];

		const birthdayBonusIds = allCampaigns
		.flatMap(campaign => campaign?.loyaltyEarns
			?.filter((earn : LoyaltyEarn) => earn?.earnConditions?.some( (condition : EarnCondition) => condition.type === 'birthday-bonus'))
			?.map((earn : LoyaltyEarn) => earn.id)
		)
		.filter(Boolean);

		const now = new Date();
		const startOfYear = new Date(now.getFullYear(), 0, 1);
		const endOfYear = new Date(now.getFullYear() + 1, 0, 1);

		const existingLogs = await this.raleonUserEarnLogRepository.find({
			where: {
				raleonUserId: raleonUserId,
				loyaltyEarnId: { inq: birthdayBonusIds },
				completed: true,
				createdDate: {
					gte: startOfYear.toISOString(),
					lt: endOfYear.toISOString()
				}
			}
		});

		if(existingLogs.length > 0) {
			console.log(`Birthday bonus already granted for user ${raleonUserId} in year ${now.getFullYear()}`);
			return result;
		}



		//await this.grantBirthdayRewardIfApplicable(campaigns, orgId, raleonUserId);
		process.nextTick(() => {
			this.grantBirthdayRewardIfApplicable(campaigns, orgId, raleonUserId);
		});
		console.log(`Done Fetching loyalty details for user ${raleonUserId} in org ${orgId}`);
		return result;
	}

	async grantBirthdayRewardIfApplicable(
		campaigns: Array<LoyaltyCampaign>,
		orgId: number,
		raleonUserId: number,
	) {
		const userIdentity = await this.raleonUserIdentityRepository.findOne({
			where: {
				raleonUserId: raleonUserId,
				identityType: 'customer_id',
				orgId: orgId,
			}
		});
		// const birthdayBonusCampaign = campaigns.find(x => x.loyaltyEarns.some(y => y.earnConditions.some(z => z.type == 'birthday-bonus')));
		const birthdayBonuses = campaigns
			.flatMap(campaign => campaign?.loyaltyEarns?.filter(x => x?.earnConditions?.some(y => y.type == 'birthday-bonus')))
			.filter(x => x); // This will have all loyaltyEarns with birthday-bonus across all campaigns

		if (birthdayBonuses.length > 0 && userIdentity?.birthday) {
			const today = new Date();
			const birthday = new Date(userIdentity.birthday);

			if (today.getMonth() === birthday.getMonth()) {
				for (const bonus of birthdayBonuses) {
					console.log(`Granting birthday reward for campaign ${bonus.id}`);
					await this.loyaltyRedemptionService.grantBirthdayReward(bonus.id!, raleonUserId, orgId, this.devDbDataSource);
				}
			}
		}
	}

	async grantBirthdayRewardsAndSendEmails() {
		const today = new Date();
		const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
		const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
		firstDayOfMonth.setHours(0, 0, 0, 0);
		lastDayOfMonth.setHours(23, 59, 59, 999);

		const query = `SELECT *
			FROM raleonuseridentity
			WHERE EXTRACT(MONTH FROM birthday) = EXTRACT(MONTH FROM CURRENT_DATE)
		`;

		const users = await this.devDbDataSource.execute(query);

		if (!users || !users.length) {
			console.log(`No users found with birthdays this month`);
			return;
		}

		const orgUsers = new Map<number, any[]>();

		for (const user of users) {
			if (!orgUsers.has(user.orgid!)) {
				orgUsers.set(user.orgid!, []);
			}
			orgUsers.get(user.orgid!)!.push(user);
		}

		for (const [orgId, users] of orgUsers) {
			const campaigns = await this.loyaltyCampaignController.getActiveLoyaltyCampaigns(orgId);
			if (!campaigns || !campaigns.length) {
				console.log(`No active campaigns found for org ${orgId}`);
				continue;
			}

			const birthdayBonuses = campaigns
				.flatMap(campaign => campaign?.loyaltyEarns?.filter(x => x?.earnConditions?.some(y => y.type == 'birthday-bonus')))
				.filter(x => x); // This will have all loyaltyEarns with birthday-bonus across all campaigns

			if (birthdayBonuses.length > 0) {
				for (const user of users) {
					if (user?.birthday) {
						for (const bonus of birthdayBonuses) {
							await this.loyaltyRedemptionService.grantBirthdayReward(bonus.id!, user.raleonuserid, orgId, this.devDbDataSource);
						}
					}
				}
			}
		}
	}
}
