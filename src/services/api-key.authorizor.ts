import {AuthorizationContext, AuthorizationDecision, AuthorizationMetadata} from '@loopback/authorization';
import {securityId, UserProfile} from '@loopback/security';

export async function scopeAuthorization(
  authorizationCtx: AuthorizationContext,
  metadata: AuthorizationMetadata,
): Promise<AuthorizationDecision> {
  const user = authorizationCtx.principals[0] as UserProfile;
  const requiredScopes = metadata.scopes || [];

  if (!user || !user.scopes) {
    return AuthorizationDecision.DENY;
  }

  const userScopes = user.scopes as string[];

  if (requiredScopes.every(scope => userScopes.includes(scope))) {
    return AuthorizationDecision.ALLOW;
  }

  return AuthorizationDecision.DENY;
}
