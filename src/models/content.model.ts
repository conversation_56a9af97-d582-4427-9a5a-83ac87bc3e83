import {belongsTo, Entity, model, property} from '@loopback/repository';
import {Goal} from './goal.model';
import {Quest} from './quest.model';
import {Reward} from './reward.model';

@model({settings: {strict: false}})
export class Content extends Entity {
	@property({
		type: 'number',
		id: true,
		generated: true,
	})
	id?: number;

	@property({
		type: 'string',
	})
	message?: string;

	@property({
		type: 'string',
	})
	header?: string;

	@property({
		type: 'string',
	})
	headerImageUrl?: string;

	@property({
		type: 'string',
	})
	closeMessage?: string;

	@property({
		type: 'string',
	})
	buttonText?: string;

	@property({
		type: 'string',
	})
	buttonUrl?: string;

	@property({
		type: 'object',
	})
	css?: object;

	@property({
		type: 'string',
	})
	externalId?: string;

	@belongsTo(() => Quest)
	questId?: number;

	@belongsTo(() => Reward)
	rewardId?: number;

	@belongsTo(() => Goal)
	goalId?: number;

	// Define well-known properties here

	// Indexer property to allow additional data
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	[prop: string]: any;

	constructor(data?: Partial<Content>) {
		super(data);
	}
}

export interface ContentRelations {
	// describe navigational properties here
}

export type ContentWithRelations = Content & ContentRelations;
