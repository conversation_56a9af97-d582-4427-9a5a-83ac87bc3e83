import {Entity, model, property, belongsTo, hasMany} from '@loopback/repository';
import {Organization} from './organization.model';
import {OrganizationSegmentDetails} from './organization-segment-details.model';

@model({settings: {strict: false}})
export class OrganizationSegment extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @belongsTo(() => Organization)
  orgId: number;

  @property({
    type: 'string',
    required: false,
  })
  name?: string;

  @property({
    type: 'string',
    required: false,
  })
  description?: string;

  @property({
    type: 'string',
    required: false,
  })
  externalId?: string;

  @property({
    type: 'date',
  })
  externalSyncDate?: Date;

  @property({
    type: 'string',
    required: false,
  })
  variableOverride?: string;

  @property({
    type: 'boolean',
    required: false,
    default: false,
  })
  archived?: boolean;

  @property({
    type: 'date',
    required: false,
  })
  archivedDate?: Date;

  @hasMany(() => OrganizationSegmentDetails, {keyFrom: 'id', keyTo: 'orgSegmentId'})
  organizationSegmentDetails: OrganizationSegmentDetails[];

  constructor(data?: Partial<OrganizationSegment>) {
    super(data);
  }
}

export interface OrganizationSegmentRelations {
	organizationSegmentDetails?: OrganizationSegmentDetails[];
}

export type OrganizationSegmentWithRelations = OrganizationSegment & OrganizationSegmentRelations;
