import {Model, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class UniqueWallets extends Model {
  @property({
    type: 'string',
    id: true,
    generated: false,
    required: true,
  })
  address: string;

  @property({
    type: 'string',
    required: true,
  })
  network: string;


  constructor(data?: Partial<UniqueWallets>) {
    super(data);
  }
}

export interface UniqueWalletsRelations {
  // describe navigational properties here
}

export type UniqueWalletsWithRelations = UniqueWallets & UniqueWalletsRelations;
