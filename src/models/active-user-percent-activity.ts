import {Model, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class ActiveUserPercentActivity extends Model {
  @property({
    type: 'string',
    id: true,
    generated: false,
    required: true,
  })
  address: string;

  @property({
    type: 'string',
    required: true,
  })
  network: string;


  constructor(data?: Partial<ActiveUserPercentActivity>) {
    super(data);
  }
}

export interface ActiveUserPercentActivityRelations {
  // describe navigational properties here
}

export type ActiveUserPercentActivityWithRelations = ActiveUserPercentActivity & ActiveUserPercentActivityRelations;
