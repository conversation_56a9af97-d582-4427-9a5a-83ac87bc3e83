import {Model, model, property} from '@loopback/repository';

@model()
export class Journey<PERSON>vent extends Model {
  @property({
    type: 'string',
  })
  address?: string;

  @property({
    type: 'string',
  })
  network?: string;

  @property({
    type: 'string',
  })
  org_id?: string;

  @property({
    type: 'string',
  })
  status?: string;

  @property({
    type: 'string',
  })
  data_type?: string;

  @property({
    type: 'string',
    required: true,
  })
  raleon_id: string;

  @property({
    type: 'string',
  })
  data?: string;

  @property({
    type: 'string',
    required: true,
  })
  event_type: string;

  @property({
    type: 'string',
    required: false,
  })
  application_id?: string;

  [prop: string]: any;

  constructor(data?: Partial<JourneyEvent>) {
    super(data);
  }
}

export interface JourneyEventRelations {
  // describe navigational properties here
}

export type JourneyEventWithRelations = JourneyEvent & JourneyEventRelations;
