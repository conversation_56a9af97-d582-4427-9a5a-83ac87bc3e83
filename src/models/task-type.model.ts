import {Entity, model, property} from '@loopback/repository';

@model()
export class TaskType extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: false,
  })
  id?: number;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
    required: true,
  })
  taskType: string;

  @property({
    type: 'string',
  })
  description?: string;


  constructor(data?: Partial<TaskType>) {
    super(data);
  }
}

export interface TaskTypeRelations {
  // describe navigational properties here
}

export type TaskTypeWithRelations = TaskType & TaskTypeRelations;
