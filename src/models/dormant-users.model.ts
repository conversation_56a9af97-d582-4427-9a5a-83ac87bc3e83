import {Model, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class DormantUsers extends Model {
  @property({
    type: 'string',
    id: true,
    generated: false,
    required: true,
  })
  address: string;

  @property({
    type: 'string',
    required: true,
  })
  network: string;

  @property({
    type: 'string',
    required: false,
  })
  startDate: string;

  @property({
    type: 'string',
    required: false,
  })
  endDate: string;


  constructor(data?: Partial<DormantUsers>) {
    super(data);
  }
}

export interface DormantUsersRelations {
  // describe navigational properties here
}

export type DormantUserseWithRelations = DormantUsers & DormantUsersRelations;
