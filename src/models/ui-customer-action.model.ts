import {Entity, model, property, hasMany} from '@loopback/repository';
import {UiCustomerActionCondition} from './ui-customer-action-condition.model';
import {UiCustomerReward} from './ui-customer-reward.model';
import {UiActionRewardJunction} from './ui-action-reward-junction.model';

@model()
export class UiCustomerAction extends Entity {
	@property({
		type: 'number',
		id: true
	})
	id?: number;

	@property({
		type: 'string',
		required: true,
	})
	type: string;

	@property({
		type: 'string',
		required: true,
	})
	name: string;

	@property({
		type: 'string',
		required: true,
	})
	description: string;

	@property({
		type: 'boolean',
		default: true,
	})
	chainable?: boolean;

	@property({
		type: 'string',
		default: "orders/create",
	})
	triggeredEvent?: string;

	@property({
		type: 'string',
		default: "test-image",
	})
	imageSlotKey?: string;

	@property({
		type: 'string',
		default: 1,
	})
	maxRewardsForThisAction?: number;

	@property({
		type: 'boolean',
		default: false,
	})
	enabled: boolean;

	@property({
		type: 'string',
	})
	integrationCategory?: string;

	@property({
		type: 'boolean',
		default: false,
	})
	includeInAI?: boolean;

	@property({
		type: 'boolean',
		default: false,
	})
	includeInAICampaigns?: boolean;

	@property({
		type: 'boolean',
		default: false,
	})
	hideFromWteConfig?: boolean;

	@hasMany(() => UiCustomerActionCondition)
	uiCustomerActionConditions: UiCustomerActionCondition[];

	@hasMany(() => UiCustomerReward, {
		through: {
			model: () => UiActionRewardJunction,
			keyFrom: 'uiCustomerActionId',
			keyTo: 'uiCustomerRewardId'
		}
	})
	uiCustomerRewards: UiCustomerReward[];

	constructor(data?: Partial<UiCustomerAction>) {
		super(data);
	}
}

export interface UiCustomerActionRelations {
	// describe navigational properties here
}

export type UiCustomerActionWithRelations = UiCustomerAction & UiCustomerActionRelations;
