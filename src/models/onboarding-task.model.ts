import {Entity, model, property} from '@loopback/repository';

@model()
export class OnboardingTask extends Entity {
  @property({
    type: 'number',
    id: true,
  })
  id?: number;

  @property({
    type: 'string',
    required: true,
  })
  taskName: string;

  @property({
    type: 'string',
  })
  taskDescription?: string;

  @property({
    type: 'string',
  })
  taskCta?: string;

  @property({
    type: 'string',
  })
  taskPendingName?: string;

  @property({
    type: 'string',
  })
  taskPendingDescription?: string;

  @property({
    type: 'number',
    required: true,
  })
  priority: number;

  @property({
    type: 'string',
    required: true,
  })
  type: string;

  @property({
    type: 'string',
  })
  userAction: string;


  constructor(data?: Partial<OnboardingTask>) {
    super(data);
  }
}

export interface OnboardingTaskRelations {
  // describe navigational properties here
}

export type OnboardingTaskWithRelations = OnboardingTask & OnboardingTaskRelations;
