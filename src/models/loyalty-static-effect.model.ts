import {Entity, model, property, belongsTo} from '@loopback/repository';
import {LoyaltyCampaign} from './loyalty/loyalty-campaign.model';
import {LoyaltyRewardDefinition} from './loyalty/loyalty-reward-definition.model';

@model()
export class LoyaltyStaticEffect extends Entity {
	@property({
		type: 'number',
		id: true,
		generated: true,
	})
	id?: number;

	@property({
		type: 'string',
		required: true,
	})
	name: string;

	@property({
		type: 'boolean',
		default: false
	})
	active: boolean;

	@property({
		type: 'boolean',
		default: false
	})
	archived: boolean;

	@property({
		type: 'string'
	})
	imageURL?: string;

	@property({
		type: 'string'
	})
	description?: string;

	@property({
		type: 'number',
		default: 999
	})
	priority?: number;

	@property({
		type: 'string',
		required: false
	})
	externalLink?: string;

	@property({
		type: 'string',
		required: false
	})
	type?: string;

	@belongsTo(() => LoyaltyCampaign)
	loyaltyCampaignId: number;

	@belongsTo(() => LoyaltyRewardDefinition)
	loyaltyRewardDefinitionId: number;

	constructor(data?: Partial<LoyaltyStaticEffect>) {
		super(data);
	}
}

export interface LoyaltyStaticEffectRelations {
	// describe navigational properties here
	loyaltyCampaign: LoyaltyCampaign;
	loyaltyRewardDefinition: LoyaltyRewardDefinition;
}

export type LoyaltyStaticEffectWithRelations = LoyaltyStaticEffect & LoyaltyStaticEffectRelations;
