import {Entity, model, property, hasMany, belongsTo, hasOne} from '@loopback/repository';
import {RaleonUserIdentity, RaleonUserIdentityWithRelations} from './raleon-user-identity.model';
import {Organization} from './organization.model';
import {InventoryCoupon} from './loyalty/inventory-coupon.model';
import {LoyaltyCurrencyBalance} from './loyalty/loyalty-currency-balance.model';
import {LoyaltyRewardLog} from './loyalty/loyalty-reward-log.model';
import {RaleonUserKeyValueStore} from './raleon-user-key-value-store.model';

@model()
export class RaleonUser extends Entity {
	@property({
		type: 'number',
		id: true,
		generated: true,
	})
	id?: number;

	@property({
		type: 'string',
	})
	name?: string;

	@hasMany(() => RaleonUserIdentity)
	raleonUserIdentities: RaleonUserIdentity[];

	@hasOne(() => LoyaltyCurrencyBalance, {keyTo: 'raleonUserId', name: 'loyaltyCurrencyBalance'})
	loyaltyCurrencyBalance: LoyaltyCurrencyBalance;

	@hasMany(() => LoyaltyRewardLog, {keyTo: 'raleonUserId', name: 'loyaltyRewardLogs'})
	loyaltyRewardLogs: LoyaltyRewardLog[];

	@property({
		type: 'number',
	})
	// TODO: would prefer this be a @belongsTo(() => RaleonUser)
	// but causes issues with the OpenAPI schema
	// (raleonUserIdentities stops working weirdly, and then controller validators break)
	replacedByUserId?: number;

  @hasMany(() => RaleonUserKeyValueStore)
  raleonUserKeyValueStores: RaleonUserKeyValueStore[];

	constructor(data?: Partial<RaleonUser>) {
		super(data);
	}
}

export interface RaleonUserRelations {
	// describe navigational properties here
	raleonUserIdentities: RaleonUserIdentityWithRelations[];
}

export type RaleonUserWithRelations = RaleonUser & RaleonUserRelations;
