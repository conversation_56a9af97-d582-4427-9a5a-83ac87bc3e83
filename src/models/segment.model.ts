import {Entity, model, property, hasMany} from '@loopback/repository';
import {CampaignSegment} from './campaign-segment.model';
import {Campaign} from './campaign.model';

@model()
export class Segment extends Entity {
  @property({
    type: 'number',
    generated: true,
    id: true,
  })
  id?: number;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
    required: true,
  })
  network: string;

  @property({
    type: 'number',
    required: true,
  })
  orgid: number;

  @property({
    type: 'array',
    itemType: 'any',
    required: true,
  })
  queries: any[];

  @property({
    type: 'string',
    itemType: 'any',
    required: false,
  })
  queryid: string;

  @property({
    type: 'string',
    required: false,
  })
  description?: string;

  @property({
    type: 'string',
    required: false,
  })
  status?: string;

  @property({
    type: 'number',
    required: false
  })
  addressCount?: number;

  @property({
    type: 'string',
    required: false
  })
  viewname?: string;

  @property({
    type: 'string',
    required: false
  })
  creatorId?: string;

  @property({
	type: 'boolean',
	required: false
  })
  metricsEnabled?: boolean;

  @hasMany(() => Campaign, {through: {model: () => CampaignSegment}})
  campaigns?: Campaign[];

  constructor(data?: Partial<Segment>) {
    super(data);
  }
}

export interface SegmentRelations {
  // describe navigational properties here
}

export type SegmentWithRelations = Segment & SegmentRelations;
