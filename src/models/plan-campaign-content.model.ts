import {Entity, model, property} from '@loopback/repository';

@model()
export class PlanCampaignContent extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
    required: true,
  })
  template: string;

  @property({
    type: 'string',
  })
  visual?: string;

  @property({
    type: 'number',
  })
  plannerCampaignId?: number;

  @property({
    type: 'string',
  })
  subjectLine?: string;

  @property({
    type: 'string',
  })
  previewText?: string;

  @property({
    type: 'string',
  })
  emailBriefText?: string;

  @property({
    type: 'string',
  })
  emailJSON?: string;

  constructor(data?: Partial<PlanCampaignContent>) {
    super(data);
  }
}

export interface PlanCampaignContentRelations {
  // describe navigational properties here
}

export type PlanCampaignContentWithRelations = PlanCampaignContent & PlanCampaignContentRelations;
