import {Entity, model, property, belongsTo} from '@loopback/repository';
import {Organization} from './organization.model';
import {OnboardingTask} from './onboarding-task.model';

@model()
export class OnboardingState extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;
  @property({
    type: 'string',
  })
  state?: string;

  @property({
    type: 'date',
  })
  timestamp: Date;

  @belongsTo(() => Organization)
  orgId: number;

  @belongsTo(() => OnboardingTask)
  taskId: number;

  constructor(data?: Partial<OnboardingState>) {
    super(data);
  }
}

export interface OnboardingStateRelations {
  // describe navigational properties here
  task: OnboardingTask;
}

export type OnboardingStateWithRelations = OnboardingState & OnboardingStateRelations;
