import {Entity, model, property, hasOne} from '@loopback/repository';
import {AvailableExtensions} from './available-extensions.model';

@model()
export class Extensions extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
    required: true,
  })
  type: string;

  @property({
    type: 'boolean',
    default: false,
  })
  enabled?: boolean;

  @property({
    type: 'number',
  })
  organizationId?: number;

  @property({
	type: 'string'
  })
  externalId?: string;

  @property({
	type: 'number'
  })
  availableExtensionsId?: number;


  constructor(data?: Partial<Extensions>) {
    super(data);
  }
}

export interface ExtensionsRelations {
  // describe navigational properties here
}

export type ExtensionsWithRelations = Extensions & ExtensionsRelations;
