import {belongsTo, Entity, model, property} from '@loopback/repository';
import {MetricSegment} from './metric-segment.model';
import {OrganizationSegment} from './organization-segment.model';

@model({settings: {strict: false}})
export class OrganizationSegmentDetails extends Entity {

  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @belongsTo(() => MetricSegment)
  metricSegmentId: number;

  @belongsTo(() => OrganizationSegment)
  orgSegmentId: number;

  @property({
    type: 'boolean',
    required: true,
  })
  include: boolean;

  @property({
	type: 'string',
	required: false,
  })
  queryOverride: string;

  constructor(data?: Partial<OrganizationSegmentDetails>) {
    super(data);
  }
}

export interface OrganizationSegmentDetailsRelations {
	metricSegment?: MetricSegment;
	organizationSegment?: OrganizationSegment;
}

export type OrganizationSegmentDetailsWithRelations = OrganizationSegmentDetails & OrganizationSegmentDetailsRelations;
