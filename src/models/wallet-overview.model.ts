import {Model, model, property} from '@loopback/repository';

@model()
export class WalletOverview extends Model {
  @property({
    type: 'array',
    itemType: 'string',
    required: true,
  })
  addresses: string[];

  @property({
    type: 'string',
    required: true,
  })
  network: string;


  @property({
    type: 'boolean',
  })
  includeNetWorth?: boolean;

  @property({
    type: 'boolean',
  })
  includeEthCount?: boolean;

  @property({
    type: 'boolean',
  })
  includeNftCount?: boolean;

  @property({
    type: 'string',
  })
  includeDataFromEventType?: string;


  constructor(data?: Partial<WalletOverview>) {
    super(data);
  }
}

export interface WalletOverviewRelations {
  // describe navigational properties here
}

export type WalletOverviewWithRelations = WalletOverview & WalletOverviewRelations;
