import {Model, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class CommonTokensHeld extends Model {
  @property({
    type: 'string',
    id: true,
    generated: false,
    required: true,
  })
  address: string;

  @property({
    type: 'string',
    required: true,
  })
  network: string;


  constructor(data?: Partial<CommonTokensHeld>) {
    super(data);
  }
}

export interface CommonTokensHeldRelations {
  // describe navigational properties here
}

export type CommonTokensHeldWithRelations = CommonTokensHeld & CommonTokensHeldRelations;
