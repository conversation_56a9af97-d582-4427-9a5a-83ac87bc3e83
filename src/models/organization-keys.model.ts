import {Entity, model, property} from '@loopback/repository';

@model()
export class OrganizationKeys extends Entity {
	@property({
		type: 'number',
		id: true,
		generated: true,
	})
	id?: number;

	@property({
		type: 'string',
		required: true,
	})
	key: string;

	@property({
		type: 'string',
		required: true,
	})
	value: string;

	@property({
		type: 'string',
		required: true,
	})
	secretKeyId: string;

	@property({
		type: 'date',
	})
	created_at?: string;

	@property({
		type: 'number',
	})
	organizationId?: number;

	constructor(data?: Partial<OrganizationKeys>) {
		super(data);
	}
}

export interface OrganizationKeysRelations {
	// describe navigational properties here
}

export type OrganizationKeysWithRelations = OrganizationKeys & OrganizationKeysRelations;
