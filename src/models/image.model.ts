import {Entity, model, property, hasOne, belongsTo} from '@loopback/repository';
import {Campaign} from './campaign.model';

@model()
export class Image extends Entity {
	@property({
		type: 'number',
		id: true,
		generated: true,
	})
	id?: number;

	@property({
		type: 'number',
		required: true,
	})
	orgId: number;

	@property({
		type: 'string',
		required: true,
	})
	url: string;

	@property({
		type: 'string',
		required: true,
	})
	friendlyname: string;

	@property({
		type: 'number',
	})
	width?: number;

	@property({
		type: 'number',
	})
	height?: number;

	@property({
		type: 'string',
	})
	imageType?: string;

	@property({
		type: 'string',
	})
	assetType?: string;

	@property({
		type: 'string',
	})
	contentType?: string;

	@property({
		type: 'string',
	})
	description?: string;

	@belongsTo(() => Campaign)
	campaignId?: number;

	constructor(data?: Partial<Image>) {
		super(data);
	}
}

export interface ImageRelations {
	// describe navigational properties here
}

export type ImageWithRelations = Image & ImageRelations;
