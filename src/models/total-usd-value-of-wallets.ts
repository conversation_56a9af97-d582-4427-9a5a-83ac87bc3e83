import {Model, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class TotalUSDValueOfWallets extends Model {
  @property({
    type: 'string',
    id: true,
    generated: false,
    required: true,
  })
  address: string;

  @property({
    type: 'string',
    required: true,
  })
  network: string;


  constructor(data?: Partial<TotalUSDValueOfWallets>) {
    super(data);
  }
}

export interface TotalUSDValueOfWalletsRelations {
  // describe navigational properties here
}

export type TotalUSDValueOfWalletsWithRelations = TotalUSDValueOfWallets & TotalUSDValueOfWalletsRelations;
