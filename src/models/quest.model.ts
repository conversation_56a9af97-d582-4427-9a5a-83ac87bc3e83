import {belongsTo, Entity, hasOne, model, property, hasMany} from '@loopback/repository';
import {Content} from './content.model';
import {Campaign, CampaignWithRelations} from './campaign.model';
import {Journey, JourneyWithRelations} from './journey.model';
import {Goal} from './goal.model';
import {Reward} from './reward.model';

@model()
export class Quest extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
    required: false,
  })
  chatGraph: string;

  @property({
	type: 'boolean',
	default: false,
  })
  isLoginQuest?: boolean;

  @belongsTo(() => Campaign)
  campaignId: number;

  @hasOne(() => Content)
  content?: Content;

  @hasMany(() => Journey)
  journeys: Journey[];

  @hasMany(() => Goal)
  goals: Goal[];

  @hasMany(() => Reward)
  rewards: Reward[];

  constructor(data?: Partial<Quest>) {
    super(data);
  }
}

export interface QuestRelations {
  // describe navigational properties here
  journeys: Array<JourneyWithRelations>;
  campaign: CampaignWithRelations;
}

export type QuestWithRelations = Quest & QuestRelations;
