import {Entity, belongsTo, hasOne, model, property} from '@loopback/repository';
import {User} from './user-management/user.model';

@model()
export class UserIdentity extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
    required: true,
  })
  identityType: string;

  @property({
    type: 'string',
    required: true,
  })
  identityValue: string;

  @property({
	type: 'string',
  })
  sessionToken?: string;

  @belongsTo(() => User)
  userId: number;

  constructor(data?: Partial<UserIdentity>) {
    super(data);
  }
}

export interface UserIdentityRelations {
  // describe navigational properties here
}

export type UserIdentityWithRelations = UserIdentity & UserIdentityRelations;

export const IDENTITY_TYPES = {
        SELF_SERVICE: 'self-service',
        GOOGLE: 'google'
}
