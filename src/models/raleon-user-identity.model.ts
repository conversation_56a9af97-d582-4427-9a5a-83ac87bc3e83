import {Entity, model, property, belongsTo, hasMany,} from '@loopback/repository';
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, RaleonUserWithRelations} from './raleon-user.model';
import {UserProfile} from '@loopback/security';
import {Organization} from './organization.model';
import {integer} from 'aws-sdk/clients/cloudfront';
import {VipTier, VipTierWithRelations} from './vip-tier.model';
import {RaleonUserIdentityAttributes} from './raleon-user-identity-attributes.model';

@model({
  settings: {
    indexes: {
      orgId_index: {
        keys: {
          orgId: -1
        }
      },
      idx_raleonuseridentity_type_value: {
        keys: {
          identityType: 1,
          identityValue: 1,
          id: 1
        }
      },
      identityValue_index: {
        keys: {
          identityValue: 1
        }
      },
      idx_raleonuseridentity_userid_type: {
        keys: {
          raleonUserId: 1,
          identityType: 1,
          id: 1
        }
      }
    },
    mysql: {
      uniqueKeys: {
        unique_identity_value: {
          columns: ['identityValue']
        }
      }
    }
  }
})

export class RaleonUserIdentity extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id: number;

  @property({
    type: 'date',
    defaultFn: 'now',
  })
  createdDate?: string;

  @property({
    type: 'string',
    required: true,
  })
  identityValue: string;

  @property({
    type: 'string',
    required: true,
  })
  identityType: string;

  @property({
    type: 'string',
  })
  loyaltySegment?: string;

  @property({
    type: 'number',
  })
  loyaltyScore?: number;

  @property({
    type: 'number',
  })
  discountScore?: number;

  @property({
    type: 'number',
  })
  refundPropensity?: number;

  @property({
    type: 'number',
  })
  ltv?: number;

  @property({
    type: 'number',
  })
  aov?: number;

  @property({
    type: 'number',
  })
  ltvDistribution?: number;

  @property({
    type: 'number',
  })
  revenue?: number;

  @property({
    type: 'number',
  })
  churnRisk?: number;

  @property({
    type: 'number',
  })
  daysSinceLastSubscription?: number;

  @property({
    type: 'number',
  })
  engagement30days?: number;

  @property({
    type: 'number',
  })
  engagement60days?: number;

  @property({
    type: 'number',
  })
  engagement90days?: number;

  @property({
    type: 'number',
  })
  replenishmentScore?: number;

  @property({
	type: 'string',
  })
  replenishmentProduct?: string;

  @property({
	type: 'number',
  })
  rebuypropensity?: number;

  @property({
	type: 'string',
  })
  rebuyproduct?: string;

  @property({
	type: 'number',
  })
  subscriptionpropensity?: number;

  @property({
	type: 'number',
  })
  winbackScore?: number;

  @property({
    type: 'number',
  })
  totalOrders?: number;

  @property({
    type: 'number',
  })
  totalRefunds?: number;

  @property({
	type: 'number',
  })
  email_unique_opensl30?: number;

  @property({
	type: 'number',
  })
  email_unique_opensl60?: number;

  @property({
	type: 'number',
  })
  email_unique_opensl90?: number;

  @property({
	type: 'number',
  })
  email_total_opensl30?: number;

  @property({
	type: 'number',
  })
  email_total_opensl60?: number;

  @property({
	type: 'number',
  })
  email_total_opensl90?: number;

  @property({
	type: 'number',
  })
  has_abandoned_checkout?: number;

  @property({
    type: 'date',
  })
  birthday?: Date;

  @property({
    type: 'string',
  })
  referralCode?: string;

  @property({
    type: 'string',
  })
  signupReferrer?: string;

  @property({
    type: 'boolean',
  })
  referralWelcomeBonusComplete?: boolean;

  @property({
    type: 'boolean',
  })
  referralComplete?: boolean;

  @property({
    type: 'date',
  })
  firstLoginDate?: Date;

  @property({
    type: 'date',
  })
  metricsUpdated?: Date;

  @belongsTo(() => RaleonUser)
  raleonUserId: number;

  @belongsTo(() => Organization, {
    name: 'organization',
  })
  orgId?: number;

  @belongsTo(() => VipTier)
  vipTierId?: number;

  @property({
	type: 'boolean',
  })
  unsubscribed?: boolean;

  @property({
	type: 'string',
  })
  lastEmailSent?: Date;

  @property({
	type: 'boolean',
	default: false,
  })
  isDemoUser?: boolean;

  @hasMany(() => RaleonUserIdentityAttributes)
  raleonUserIdentityAttributes: RaleonUserIdentityAttributes[];

  constructor(data?: Partial<RaleonUserIdentity>) {
    super(data);
  }
}

export interface RaleonUserIdentityRelations {
  // describe navigational properties here
  raleonUser: RaleonUserWithRelations;
  vipTier: VipTierWithRelations;
}

export type RaleonUserIdentityWithRelations = RaleonUserIdentity & RaleonUserIdentityRelations;
