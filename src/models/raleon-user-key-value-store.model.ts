import {Entity, model, property} from '@loopback/repository';

@model()
export class RaleonUserKeyValueStore extends Entity {
  @property({
    type: 'string',
    required: true,
  })
  key: string;

  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
    required: true,
  })
  value: string;

  @property({
    type: 'number',
  })
  raleonUserId: number;

  constructor(data?: Partial<RaleonUserKeyValueStore>) {
    super(data);
  }
}

export interface RaleonUserKeyValueStoreRelations {
  // describe navigational properties here
}

export type RaleonUserKeyValueStoreWithRelations = RaleonUserKeyValueStore & RaleonUserKeyValueStoreRelations;
