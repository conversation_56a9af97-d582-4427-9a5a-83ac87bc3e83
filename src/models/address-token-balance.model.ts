import {Model, model, property} from '@loopback/repository';

@model()
export class AddressTokenBalance extends Model {
  @property({
    type: 'string',
    id: true,
    generated: false,
    required: true,
  })
  address: string;

  @property({
    type: 'array',
    itemType: 'string',
    required: true,
  })
  token_tickers: string[];

  @property({
    type: 'array',
    itemType: 'number',
    required: true,
  })
  usd_values: number[];


  constructor(data?: Partial<AddressTokenBalance>) {
    super(data);
  }
}

export interface AddressTokenBalanceRelations {
  // describe navigational properties here
}

export type AddressTokenBalanceWithRelations = AddressTokenBalance & AddressTokenBalanceRelations;
