import {Entity, model, property} from '@loopback/repository';

@model()
export class UiShopItemCondition extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
    required: true,
  })
  label: string;

  @property({
    type: 'number',
    required: true,
  })
  defaultAmount: number;

  @property({
    type: 'string',
    required: true,
  })
  fieldOnDataModel: string;


  constructor(data?: Partial<UiShopItemCondition>) {
    super(data);
  }
}

export interface UiShopItemConditionRelations {
  // describe navigational properties here
}

export type UiShopItemConditionWithRelations = UiShopItemCondition & UiShopItemConditionRelations;
