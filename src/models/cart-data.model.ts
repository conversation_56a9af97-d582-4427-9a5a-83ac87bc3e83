import {Entity, model, property} from '@loopback/repository';

@model({
  settings: {    indexes: {
      checkoutid_index: {
        keys: {checkoutId: 1},
		options: {unique: true},
      },
	  customerid_index: {
		keys: {customerId: 1},
	  },
	  orgid_index: {
		keys: {orgId: 1},
	  },
    },
  },
})
export class CartData extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
    required: true,
  })
  checkoutId: string;

  @property({
    type: 'string',
    required: true,
  })
  customerId: string;

  @property({
    type: 'date',
    required: true,
  })
  lastUpdateTime: Date;

  @property({
    type: 'number',
    required: true,
  })
  orgId?: number;

  @property({
	type: 'number',
	required: true,
  })
  raleonUserId: number;

  @property({
    type: 'string',
    required: true,
    default: 'active',
    jsonSchema: {
      enum: ['active', 'abandoned', 'completed'],
    },
  })
  status?: string;

  constructor(data?: Partial<CartData>) {
    super(data);
  }
}

export interface CartDataRelations {
  // describe navigational properties here
}

export type CartDataWithRelations = CartData & CartDataRelations;
