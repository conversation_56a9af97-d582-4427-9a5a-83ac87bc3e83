import {Model, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class ActiveUserInteractionCount extends Model {
  @property({
    type: 'string',
    id: true,
    generated: false,
    required: true,
  })
  address: string;

  @property({
    type: 'string',
    required: true,
  })
  network: string;


  constructor(data?: Partial<ActiveUserInteractionCount>) {
    super(data);
  }
}

export interface ActiveUserInteractionCountRelations {
  // describe navigational properties here
}

export type ActiveUserInteractionCountWithRelations = ActiveUserInteractionCount & ActiveUserInteractionCountRelations;
