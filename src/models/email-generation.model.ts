import {Entity, model, property} from '@loopback/repository';

@model()
export class EmailGeneration extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'number',
    required: true,
  })
  taskId: number;

  @property({
    type: 'string',
    required: true,
  })
  status: 'pending' | 'processing' | 'completed' | 'failed';

  @property({
    type: 'string',
    required: true,
  })
  startTime: string;

  @property({
    type: 'object',
  })
  design?: object;

  @property({
    type: 'string',
  })
  operationType?: 'email' | 'klaviyo_campaign' | 'klaviyo_resync' | 'klaviyo_campaign_html_only';

  @property({
    type: 'string',
  })
  step?: string;

  @property({
    type: 'object',
  })
  data?: object;

  @property({
    type: 'string',
  })
  error?: string;

  constructor(data?: Partial<EmailGeneration>) {
    super(data);
  }
}

export interface EmailGenerationRelations {
  // describe navigational properties here
}

export type EmailGenerationWithRelations = EmailGeneration & EmailGenerationRelations;
