import {Entity, model, property} from '@loopback/repository';

@model({
  settings: {
    indexes: {
      uniqueOrganizationKey: {
        keys: {
          organizationId: 1,
          key: 1,
        },
        options: {
          unique: true, // Enforce uniqueness
        },
      },
    },
  },
})
export class OrganizationSettings extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
    required: true,
  })
  key: string;

  @property({
    type: 'string',
    required: true,
  })
  value: string;

  @property({
    type: 'number',
  })
  organizationId?: number;

  constructor(data?: Partial<OrganizationSettings>) {
    super(data);
  }
}

export interface OrganizationSettingsRelations {
  // describe navigational properties here
}

export type OrganizationSettingsWithRelations = OrganizationSettings & OrganizationSettingsRelations;
