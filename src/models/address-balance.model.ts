import {Model, model, property} from '@loopback/repository';

@model()
export class AddressBalance extends Model {
  @property({
    type: 'string',
    id: true,
    generated: false,
    required: true,
  })
  address: string;

  @property({
    type: 'number',
    required: true,
  })
  total: number;

  constructor(data?: Partial<AddressBalance>) {
    super(data);
  }
}

export interface AddressBalanceRelations {
  // describe navigational properties here
}

export type AddressBalanceWithRelations = AddressBalance & AddressBalanceRelations;
