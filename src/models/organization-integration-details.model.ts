import {Entity, model, property, belongsTo} from '@loopback/repository';
import {Integration} from './integration.model';

@model()
export class OrganizationIntegrationDetails extends Entity {
	@property({
		type: 'number',
		id: true,
		generated: true,
	})
	id?: number;

	@property({
		type: 'boolean',
		default: false,
	})
	enabled?: boolean;

	@property({
		type: 'string',
	})
	accessToken?: string;

	@property({
		type: 'number',
		required: true,
	})
	orgId: number;

	@property({
		type: 'string',
		required: false,
	})
	connectedDate: string;

	@belongsTo(() => Integration)
	integrationId: number;

	constructor(data?: Partial<OrganizationIntegrationDetails>) {
		super(data);
	}
}

export interface OrganizationIntegrationDetailsRelations {
	// describe navigational properties here
}

export type OrganizationIntegrationDetailsWithRelations = OrganizationIntegrationDetails & OrganizationIntegrationDetailsRelations;
