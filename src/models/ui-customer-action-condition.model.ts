import {Entity, model, property} from '@loopback/repository';

@model()
export class UiCustomerActionCondition extends Entity {
	@property({
		type: 'number',
		id: true,
		//generated: true,
	})
	id?: number;

	@property({
		type: 'string',
		required: true,
	})
	label: string;

	@property({
		type: 'number',
		default: 0,
	})
	defaultAmount?: number;

	@property({
		type: 'string',
		required: true,
	})
	operatorLabel: string;

	@property({
		type: 'string',
		default: "=",
	})
	operatorComparison?: string;

	@property({
		type: 'string',
		required: true,
	})
	variable: string;

	@property({
		type: 'number',
	})
	uiCustomerActionId?: number;

	@property({
		type: 'string'
	})
	defaultTextValue?: string;

	@property({
		type: 'string',
	})
	placeholderText?: string;

	@property({
		type: 'string',
	})
	propertyType?: string;


	constructor(data?: Partial<UiCustomerActionCondition>) {
		super(data);
	}
}

export interface UiCustomerActionConditionRelations {
	// describe navigational properties here
}

export type UiCustomerActionConditionWithRelations = UiCustomerActionCondition & UiCustomerActionConditionRelations;
