import {Entity, model, property} from '@loopback/repository';

@model()
export class CustomMetric extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
    required: true,
  })
  uuid: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'number',
  })
  organizationId?: number;


  constructor(data?: Partial<CustomMetric>) {
    super(data);
  }
}

export interface CustomMetricRelations {
  // describe navigational properties here
}

export type CustomMetricWithRelations = CustomMetric & CustomMetricRelations;
