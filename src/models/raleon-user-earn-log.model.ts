import {Entity, belongsTo, model, property} from '@loopback/repository';
import {LoyaltyEarn} from './loyalty/loyalty-earn.model';
import {RaleonUser} from './raleon-user.model';

@model(
	{
		settings: {
		  indexes: {
			idx_raleonuserearnlog_lookup: {
			  keys: {
				raleonUserId: 1,
				loyaltyEarnId: 1,
				completed: 1,
				id: 1
			  }
			}
		  }
		}
	  }
)
export class RaleonUserEarnLog extends Entity {
	@property({
		type: 'number',
		id: true,
		generated: true,
	})
	id?: number;

	@property({
		type: 'boolean',
		required: false,
		default: false
	})
	completed?: boolean;

	@property({
		type: 'date',
		defaultFn: 'now',
	})
	createdDate?: string;

	@belongsTo(() => LoyaltyEarn)
	loyaltyEarnId: number;

	@belongsTo(() => RaleonUser)
	raleonUserId: number;

	constructor(data?: Partial<RaleonUserEarnLog>) {
		super(data);
	}
}

export interface RaleonUserEarnLogRelations {
	// describe navigational properties here
}

export type RaleonUserEarnLogWithRelations = RaleonUserEarnLog & RaleonUserEarnLogRelations;
