import {Entity, model, property} from '@loopback/repository';

@model()
export class PlannerCampaignImage extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'number',
    required: false,
  })
  imageId?: number;

  @property({
    type: 'number',
  })
  plannerCampaignId?: number;

  @property({
    type: 'string',
	required: false,
  })
  url?: string;

  @property({
    type: 'boolean',
	required: false,
  })
  isDraft?: boolean;

  constructor(data?: Partial<PlannerCampaignImage>) {
    super(data);
  }
}

export interface PlannerCampaignImageRelations {
  // describe navigational properties here
}

export type PlannerCampaignImageWithRelations = PlannerCampaignImage & PlannerCampaignImageRelations;
