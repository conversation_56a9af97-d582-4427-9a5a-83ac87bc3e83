import {Entity, model, property, belongsTo} from '@loopback/repository';
import {Plan} from './plan.model';

@model()
export class PlanRevenuePricing extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'number',
    required: true,
  })
  revenueUsdMin: number;

  @property({
    type: 'number',
    required: false,
  })
  revenueUsdMax?: number;

  @property({
    type: 'number',
    required: true,
  })
  price: number;

  @belongsTo(() => Plan)
  planId: number;

  constructor(data?: Partial<PlanRevenuePricing>) {
    super(data);
  }
}

export interface PlanRevenuePricingRelations {
  // describe navigational properties here
}

export type PlanRevenuePricingWithRelations = PlanRevenuePricing & PlanRevenuePricingRelations;
