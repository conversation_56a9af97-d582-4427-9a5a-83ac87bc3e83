import {Model, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class HighestValueWallet extends Model {
  @property({
    type: 'string',
    id: true,
    generated: false,
    required: true,
  })
  address: string;

  @property({
    type: 'string',
    required: true,
  })
  network: string;


  constructor(data?: Partial<HighestValueWallet>) {
    super(data);
  }
}

export interface HighestValueWalletRelations {
  // describe navigational properties here
}

export type HighestValueWalletWithRelations = HighestValueWallet & HighestValueWalletRelations;
