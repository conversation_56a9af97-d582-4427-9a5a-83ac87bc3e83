import {Model, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class ActiveUsers extends Model {
  @property({
    type: 'string',
    id: true,
    generated: false,
    required: true,
  })
  address: string;

  @property({
    type: 'string',
    required: true,
  })
  network: string;


  constructor(data?: Partial<ActiveUsers>) {
    super(data);
  }
}

export interface ActiveUsersRelations {
  // describe navigational properties here
}

export type ActiveUserseWithRelations = ActiveUsers & ActiveUsersRelations;
