import {Model, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class NewUserActivities extends Model {
  @property({
    type: 'string',
    id: true,
    generated: false,
    required: true,
  })
  address: string;

  @property({
    type: 'string',
    required: true,
  })
  network: string;


  constructor(data?: Partial<NewUserActivities>) {
    super(data);
  }
}

export interface NewUserActivitiesRelations {
  // describe navigational properties here
}

export type NewUserActivitiesWithRelations = NewUserActivities & NewUserActivitiesRelations;
