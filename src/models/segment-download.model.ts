import {Model, model, property} from '@loopback/repository';

@model()
export class SegmentDownload extends Model {
  @property({
    type: 'string',
    required: true,
  })
  audience: string;

  @property({
    type: 'boolean',
    default: false,
  })
  includeNetWorth?: boolean;

  @property({
    type: 'boolean',
    default: false,
  })
  includeNftCount?: boolean;

  @property({
    type: 'boolean',
    default: false,
  })
  includeEthCount?: boolean;

  @property({
    type: 'string',
  })
  includeDataFromEventType?: string;

  @property({
    type: 'string',
  })
  queryExecutionId?: string;


  constructor(data?: Partial<SegmentDownload>) {
    super(data);
  }
}

export interface SegmentDownloadRelations {
  // describe navigational properties here
}

export type SegmentDownloadWithRelations = SegmentDownload & SegmentDownloadRelations;
