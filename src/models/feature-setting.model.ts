import {Entity, model, property, belongsTo} from '@loopback/repository';
import {Organization} from './organization.model';

@model({settings: {strict: false}})
export class FeatureSetting extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'boolean',
    required: true,
  })
  enabled: boolean;

  @property({
    type: 'boolean',
    required: true,
  })
  live: boolean;

  @belongsTo(() => Organization)
  organizationId: number;

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<FeatureSetting>) {
    super(data);
  }
}

export interface FeatureSettingRelations {
  // describe navigational properties here
}

export type FeatureSettingWithRelations = FeatureSetting & FeatureSettingRelations;
