import {belongsTo, Entity, model, property} from '@loopback/repository';
import {Feature} from './feature.model';

@model()
export class Metric extends Entity {
	@property({
		type: 'number',
		id: true,
		generated: true,
	})
	id?: number;

	@property({
		type: 'string',
		required: true,
	})
	name: string;

	@property({
		type: 'number',
		required: false,
	})
	metricSegmentId?: number;

	@property({
		type: 'string',
		required: false,
	})
	type?: string;

	@property({
		type: 'string',
		required: false,
	})
	query?: string;

	@property({
		type: 'string',
		required: false,
	})
	variables?: string;

	@property({
		type: 'string',
		required: false,
	})
	catalog?: string;

	@property({
		type: 'string',
		required: false,
	})
	dataStructure?: string;

	@property({
		type: 'string',
		required: false,
	})
	defaultRunFrequency?: string;

	@property({
		type: 'string',
		required: false,
	})
	fieldMappings?: string;

	@property({
		type: 'number',
		required: false,
	})
	priority?: number;

	@property({
		type: 'boolean',
		required: false,
		default: false,
	})
	isDefault: boolean;

	@property({
		type: 'boolean',
		required: false,
		default: false,
	})
	includeInPrompt: boolean;

	@property({
		type: 'string',
		required: false,
	})
	description: string;

	@belongsTo(() => Feature)
  	featureId?: string;

	constructor(data?: Partial<Metric>) {
		super(data);
	}
}
export interface MetricRelations {
	// describe navigational properties here
  }

  export type MetricWithRelations = Metric & MetricRelations;
