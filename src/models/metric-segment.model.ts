import {Entity, model, property, hasOne, hasMany, belongsTo} from '@loopback/repository';
import {OrganizationMetric} from './organization-metric.model';
import {Metric} from './metric.model';
import {Integration} from './integration.model';

@model()
export class MetricSegment extends Entity {
	@property({
		type: 'number',
		id: true,
		generated: true,
	})
	id?: number;

	@property({
		type: 'string',
		required: false,
	})
	key: string;

	@property({
		type: 'string',
		required: false,
	})
	segmentKey: string;

	@property({
		type: 'string',
		required: true,
	})
	name: string;

	@property({
		type: 'string',
		required: false,
	})
	description?: string;

	@property({
		type: 'string',
		required: false,
	})
	type?: string;

	@property({
		type: 'string',
		required: true,
	})
	query?: string;

	@property({
		type: 'string',
		required: false,
	})
	variables?: string;

	@property({
		type: 'string',
		required: false,
	})
	catalog?: string;

	@property({
		type: 'string',
		required: false,
	})
	dataStructure?: string;

	@property({
		type: 'boolean',
		required: false,
		default: false,
	})
	isSignal: boolean;

	@property({
		type: 'boolean',
		required: false,
		default: false,
	})
	isDefault: boolean;

	@belongsTo(() => Integration)
	integrationId: number;

	@hasMany(() => Metric, {keyTo: 'id', keyFrom: 'metricSegmentId'})
	metrics: Metric[];

	constructor(data?: Partial<MetricSegment>) {
		super(data);
	}
}

export interface MetricSegmentRelations {
	// describe navigational properties here
}

export type MetricSegmentWithRelations = MetricSegment & MetricSegmentRelations;
