import {Model, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class AtRiskActivities extends Model {
  @property({
    type: 'string',
    id: true,
    generated: false,
    required: true,
  })
  address: string;

  @property({
    type: 'string',
    required: true,
  })
  network: string;


  constructor(data?: Partial<AtRiskActivities>) {
    super(data);
  }
}

export interface AtRiskActivitiesRelations {
  // describe navigational properties here
}

export type AtRiskActivitiesWithRelations = AtRiskActivities & AtRiskActivitiesRelations;
