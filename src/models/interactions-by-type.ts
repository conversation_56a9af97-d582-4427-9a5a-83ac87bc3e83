import {Model, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class InteractionsByType extends Model {
  @property({
    type: 'string',
    id: true,
    generated: false,
    required: true,
  })
  address: string;

  @property({
    type: 'string',
    required: true,
  })
  network: string;


  constructor(data?: Partial<InteractionsByType>) {
    super(data);
  }
}

export interface InteractionsByTypeRelations {
  // describe navigational properties here
}

export type InteractionsByTypeWithRelations = InteractionsByType & InteractionsByTypeRelations;
