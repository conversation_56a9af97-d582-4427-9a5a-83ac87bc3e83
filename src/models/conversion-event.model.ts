import {Entity, model, property} from '@loopback/repository';

@model()
export class ConversionEvent extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
    required: false,
  })
  friendlyname: string;

  @property({
    type: 'string',
    required: false,
  })
  description: string;

  @property({
    type: 'string',
    required: true,
  })
  conversiontype: string;

  @property({
    type: 'string',
    required: true,
  })
  sourcetype: string;

  @property({
    type: 'date',
    required: true,
  })
  starttime: string;

  @property({
    type: 'date',
    required: true,
  })
  endtime: string;

  @property({
    type: 'number',
    required: true,
  })
  attributionminutes: number;

  @property({
    type: 'number',
    required: true,
  })
  orgid: number;

  @property({
    type: 'object',
    required: true,
  })
  conversiondata: object;

  @property({
    type: 'object',
    required: true,
  })
  sourcedata: object;

  @property({
    type: 'number',
  })
  attributionCampaignId?: number;

  constructor(data?: Partial<ConversionEvent>) {
    super(data);
  }
}

export interface ConversionEventRelations {
  // describe navigational properties here
}

export type ConversionEventWithRelations = ConversionEvent & ConversionEventRelations;
