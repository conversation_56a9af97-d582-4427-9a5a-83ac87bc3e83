import {Entity, model, property, belongsTo} from '@loopback/repository';
import {Project} from './project.model';

@model({settings: {strict: false}})
export class DataConnections extends Entity {
  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  type?: string;

  @property({
    type: 'string',
  })
  address?: string;

  @property({
    type: 'date',
  })
  startDate?: string;

  @property({
    type: 'number',
  })
  projectId?: number;

  @belongsTo(() => Project, {name: 'project'}, {keyTo: 'uuid'})
  project_uuid: string;

  @property({
	type: 'string',
  })
  network?: string;

  @property({
	type: 'boolean',
  })
  aggregate?: string;
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<DataConnections>) {
    super(data);
  }
}

export interface DataConnectionsRelations {
  // describe navigational properties here
}

export type DataConnectionsWithRelations = DataConnections & DataConnectionsRelations;
