import {Entity, belongsTo, hasMany, model, property} from '@loopback/repository';
import {DataConnections} from './data-connections.model';
import {bool} from 'aws-sdk/clients/signer';
import {Organization} from './organization.model';

@model()
export class Project extends Entity {
  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'number',
    generated: true,
	id: true
  })
  id?: number;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'date',
  })
  activationDate?: string;

  @property({
    type: 'string',
	required: true
  })
  uuid: string;

  @belongsTo(() => Organization, {name: 'organization'})
  organizationId: number;

  @property({
    type: 'number',
  })
  dataConnectionsId?: number;

  @property({
    type: 'boolean',
	default: false
  })
  isAdmin?: boolean;

  @hasMany(() => DataConnections, { keyFrom: 'uuid', keyTo: 'project_uuid' })
  dataConnections: DataConnections[];

  constructor(data?: Partial<Project>) {
    super(data);
  }
}

export interface ProjectRelations {
  // describe navigational properties here
}

export type ProjectWithRelations = Project & ProjectRelations;

