import {Entity, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class Invite extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
    required: true,
  })
  email: string;

  @property({
    type: 'number',
    required: true,
  })
  orgId: number;

  @property({
    type: 'date',
    required: false,
  })
  dateSent?: string;

  @property({
    type: 'string',
    required: false,
  })
  inviteCode?: string;

  @property({
    type: 'boolean',
    required: false,
  })
  accepted?: boolean;

  @property({
    type: 'date',
    required: false,
  })
  expirationDate?: string;

  @property({
    type: 'array',
    itemType: 'string',
    required: true
  })
  roles: string[];

  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<Invite>) {
    super(data);
  }
}

export interface InviteRelations {
  // describe navigational properties here
}

export type InviteWithRelations = Invite & InviteRelations;
