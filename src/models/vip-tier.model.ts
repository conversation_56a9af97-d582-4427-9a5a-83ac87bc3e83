import {Entity, model, property, hasOne, belongsTo} from '@loopback/repository';
import {LoyaltyCampaign} from './loyalty/loyalty-campaign.model';

@model()
export class VipTier extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
  })
  description?: string;
  
  @property({
	type: 'string'
  })
  imageURL?: string;

  @belongsTo(() => LoyaltyCampaign)
  loyaltyCampaignId: number;

  constructor(data?: Partial<VipTier>) {
    super(data);
  }
}

export interface VipTierRelations {
  // describe navigational properties here
  loyaltyCampaign: LoyaltyCampaign;
}

export type VipTierWithRelations = VipTier & VipTierRelations;
