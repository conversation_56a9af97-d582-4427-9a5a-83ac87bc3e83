import {Entity, model, property} from '@loopback/repository';

@model()
export class PromptLog extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
    required: true,
  })
  prompt: string;

  @property({
    type: 'number',
    required: true,
  })
  orgId: number;

  @property({
    type: 'string',
    required: true,
  })
  type: string;

  @property({
    type: 'date',
    required: true,
  })
  date: string;


  constructor(data?: Partial<PromptLog>) {
    super(data);
  }
}

export interface PromptLogRelations {
  // describe navigational properties here
}

export type PromptLogWithRelations = PromptLog & PromptLogRelations;
