import {Model, model, property} from '@loopback/repository';

@model()
export class SelfServiceSignup extends Model {
  @property({
    type: 'string',
    required: true,
  })
  organizationName: string;

  @property({
    type: 'string',
    required: true,
  })
  email: string;

  @property({
    type: 'string',
    required: true,
  })
  password: string;

  @property({
    type: 'string',
    required: false,
  })
  firstName?: string;

  @property({
    type: 'string',
    required: false,
  })
  lastName?: string;

  @property({
    type: 'number',
    required: false,
  })
  orgId?: number;

  @property({
    type: 'number',
    required: false,
  })
  userId?: number;

  @property({
    type: 'boolean',
    required: false,
  })
  isSignup?: boolean;

  constructor(data?: Partial<SelfServiceSignup>) {
    super(data);
  }
}

export interface SelfServiceSignupRelations {
  // describe navigational properties here
}

export type SelfServiceSignupWithRelations = SelfServiceSignup & SelfServiceSignupRelations;



@model()
export class SelfServiceAdditionalSignup extends Model {
  @property({
    type: 'string',
    required: true,
  })
  organizationName: string;

  constructor(data?: Partial<SelfServiceSignup>) {
    super(data);
  }
}

export interface SelfServiceAdditionalSignupRelations {
  // describe navigational properties here
}

export type SelfServiceAdditionalSignupWithRelations = SelfServiceAdditionalSignup & SelfServiceAdditionalSignupRelations;
