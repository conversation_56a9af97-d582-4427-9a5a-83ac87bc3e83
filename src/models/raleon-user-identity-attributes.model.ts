import {Entity, model, property, hasOne, belongsTo} from '@loopback/repository';
import {RaleonUserIdentity} from './raleon-user-identity.model';

@model()
export class RaleonUserIdentityAttributes extends Entity {
  @property({
	type: 'number',
	id: true,
	generated: true,
  })
  id?: number;

  @property({
	type: 'string',
	required: true,
  })
  key: string;

  @property({
	type: 'string',
  })
  value?: string;

  @belongsTo(() => RaleonUserIdentity)
  raleonUserIdentityId: number;

  constructor(data?: Partial<RaleonUserIdentity>) {
	super(data);
  }
}

export interface RaleonUserIdentityAttributesRelations {
  // describe navigational properties here
  raleonUserIdentity: RaleonUserIdentity;
}

export type RaleonUserIdentityAttributesWithRelations = RaleonUserIdentityAttributes & RaleonUserIdentityAttributesRelations;
