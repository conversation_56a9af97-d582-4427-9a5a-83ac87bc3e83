import {belongsTo, Entity, model, property} from '@loopback/repository';
import {Conversation} from './conversation.model';

export enum MessageRole {
	USER = 'user',
	ASSISTANT = 'assistant',
	SYSTEM = 'system',
	TOOL = 'tool',
}

export enum MessageStatus {
	STREAMING = 'streaming',
	COMPLETED = 'completed',
	FAILED = 'failed',
}

@model()
export class Message extends Entity {
	@property({
		type: 'number',
		id: true,
		generated: true,
	})
	id?: number;

	@property({
		type: 'string',
		required: true,
		jsonSchema: {
			enum: Object.values(MessageRole),
		},
	})
	role: MessageRole;

	@property({
		type: 'string',
		required: false,
	})
	content: string;

	@property({
		type: 'string',
		required: true,
		jsonSchema: {
			enum: Object.values(MessageStatus),
		},
	})
	status: MessageStatus;

	@property({
		type: 'date',
		required: true,
	})
	createdAt: Date;

	@property({
		type: 'date',
		required: true,
	})
	updatedAt: Date;

	@belongsTo(() => Conversation)
	conversationId: number;

	@property({
		type: 'string',
		required: false,
		jsonSchema: {
			description: 'The LLM system that processed this message (e.g. OpenAI, Anthropic)',
		},
	})
	llmSystem?: string;

	@property({
		type: 'string',
		required: false,
		jsonSchema: {
			description: 'The LLM provider that processed this message',
		},
	})
	llmProvider?: string;

	@property({
		type: 'string',
		required: false,
		jsonSchema: {
			description: 'The specific model used to process this message',
		},
	})
	llmModel?: string;

	@property({
		type: 'object',
		required: false,
		jsonSchema: {
			description: 'Additional metadata about the LLM request/response',
		},
	})
	llmMetadata?: object;

	@property({
		type: 'boolean',
		required: false,
		default: false,
		jsonSchema: {
			description: 'Whether the response was delivered via streaming',
		},
	})
	isStreamed?: boolean;

	@property({
		type: 'number',
		required: false,
		postgresql: {
			dataType: 'bigint'
		},
		jsonSchema: {
			description: 'Timestamp when the request was initiated (milliseconds since epoch)',
		},
	})
	requestStartedAt?: number;

	@property({
		type: 'number',
		required: false,
		postgresql: {
			dataType: 'bigint'
		},
		jsonSchema: {
			description: 'Time from request start to first chunk received (milliseconds)',
		},
	})
	timeToFirstChunk?: number;

	@property({
		type: 'number',
		required: false,
		postgresql: {
			dataType: 'bigint'
		},
		jsonSchema: {
			description: 'Total time from request start to completion (milliseconds)',
		},
	})
	totalCompletionTime?: number;

	@property({
		type: 'number',
		required: false,
		jsonSchema: {
			description: 'Number of chunks received for streamed responses',
		},
	})
	numberOfChunks?: number;

	@property({
		type: 'number',
		required: false,
		postgresql: {
			dataType: 'double precision'
		},
		jsonSchema: {
			description: 'Cost of this message in USD',
		},
	})
	cost?: number;

	@property({
		type: 'object',
		required: false,
		jsonSchema: {
			description: 'Token usage statistics for this message',
			properties: {
				promptTokens: { type: 'number' },
				completionTokens: { type: 'number' },
				totalTokens: { type: 'number' }
			}
		},
	})
	tokenUsage?: {
		promptTokens: number;
		completionTokens: number;
		totalTokens: number;
	};

	@property({
		type: 'string',
		required: false,
		postgresql: {
			dataType: 'text'
		},
		jsonSchema: {
			description: 'Tool calls made during message generation, stored as serialized JSON',
		},
	})
	toolCalls?: string;

	@property({
		type: 'string',
		required: false,
		postgresql: {
			dataType: 'text'
		},
		jsonSchema: {
			description: 'Results from tool calls, stored as serialized JSON',
		},
	})
	toolResults?: string;

	constructor(data?: Partial<Message>) {
		super(data);
	}
}

export interface MessageRelations {
	conversation: Conversation;
}

export type MessageWithRelations = Message & MessageRelations;
