import {Entity, model, property, hasMany} from '@loopback/repository';
import {UiRewardRestriction} from './ui-reward-restriction.model';
import {UiRewardLimit} from './ui-reward-limit.model';

@model()
export class UiCustomerReward extends Entity {
	@property({
		type: 'number',
		id: true,
		//generated: true,
	})
	id?: number;

	@property({
		type: 'string',
		required: true,
	})
	type: string;

	@property({
		type: 'string',
		required: true,
	})
	title: string;

	@property({
		type: 'string',
		required: true,
	})
	subtitle: string;

	@property({
		type: 'string',
		required: true,
	})
	label: string;

	@property({
		type: 'boolean',
		default: false,
	})
	hasRestrictions?: boolean;

	@property({
		type: 'boolean',
		default: false,
	})
	hasLimits?: boolean;

	@property({
		type: 'string',
		required: true,
	})
	typeDataSchema: string;

	@hasMany(() => UiRewardRestriction)
	uiRewardRestrictions: UiRewardRestriction[];

	@hasMany(() => UiRewardLimit)
	uiRewardLimits: UiRewardLimit[];

	@property({
		type: 'string',
		default: "test-image",
	})
	imageSlotKey?: string;

	@property({
		type: 'boolean',
		default: false,
	})
	canShowInShop?: boolean;

	@property({
		type: 'boolean',
		default: false,
	})
	canShowInPerks?: boolean;

	@property({
		type: 'boolean',
		default: false,
	})
	hideFromWteConfig?: boolean;

	@property({
		type: 'boolean',
		default: false,
	})
	showInGiveawayConfig?: boolean;

	@property({
		type: 'boolean',
		default: false,
	})
	enabled?: boolean;

	@property({
		type: 'boolean',
		default: false,
	})
	includeInAI?: boolean;

	@property({
		type: 'boolean',
		default: false,
	})
	includeInAICampaigns?: boolean;

	constructor(data?: Partial<UiCustomerReward>) {
		super(data);
	}
}

export interface UiCustomerRewardRelations {
	// describe navigational properties here
}

export type UiCustomerRewardWithRelations = UiCustomerReward & UiCustomerRewardRelations;
