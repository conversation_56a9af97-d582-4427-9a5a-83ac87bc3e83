import {Model, model, property} from '@loopback/repository';

@model()
export class AddressLastTransactionDate extends Model {
  @property({
    type: 'string',
    id: true,
    generated: false,
    required: true,
  })
  address: string;

  @property({
    type: 'string',
    required: true,
  })
  network: string;


  constructor(data?: Partial<AddressLastTransactionDate>) {
    super(data);
  }
}

export interface AddressLastTransactionDateRelations {
  // describe navigational properties here
}

export type AddressLastTransactionDateWithRelations = AddressLastTransactionDate & AddressLastTransactionDateRelations;
