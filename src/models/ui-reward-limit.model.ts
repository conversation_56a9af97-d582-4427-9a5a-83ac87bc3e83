import {Entity, model, property} from '@loopback/repository';

@model()
export class UiRewardLimit extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
    required: true,
  })
  fieldOnDefinition: string;

  @property({
    type: 'number',
  })
  uiCustomerRewardId?: number;

  constructor(data?: Partial<UiRewardLimit>) {
    super(data);
  }
}

export interface UiRewardLimitRelations {
  // describe navigational properties here
}

export type UiRewardLimitWithRelations = UiRewardLimit & UiRewardLimitRelations;
