import {Entity, model, property, hasMany} from '@loopback/repository';
import {PlannerCampaign} from './planner-campaign.model';

@model()
export class PlannerPlanVersion extends Entity {
	@property({
		type: 'number',
		id: true,
		generated: true,
	})
	id?: number;

	@property({
		type: 'string',
	})
	prompt?: string;

	@property({
		type: 'boolean',
		default: false,
	})
	active?: boolean;

	@property({
		type: 'string',
	})
	description?: string;

	@property({
		type: 'number',
	})
	organizationPlannerPlanId?: number;

	@property({
		type: 'string'
	})
	userPrompt?: string;

	@property({
		type: 'string',
	})
	dataSummary?: string;

	@hasMany(() => PlannerCampaign)
	plannerCampaigns: PlannerCampaign[];

	constructor(data?: Partial<PlannerPlanVersion>) {
		super(data);
	}
}

export interface PlannerPlanVersionRelations {
	// describe navigational properties here
}

export type PlannerPlanVersionWithRelations = PlannerPlanVersion & PlannerPlanVersionRelations;
