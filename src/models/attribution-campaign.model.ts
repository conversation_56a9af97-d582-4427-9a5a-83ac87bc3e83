import {Entity, model, property, hasOne} from '@loopback/repository';
import {ConversionEvent} from './conversion-event.model';

@model()
export class AttributionCampaign extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
    required: true,
  })
  utm_campaign: string;

  @property({
    type: 'string',
    required: false,
  })
  orgid: string;

  @hasOne(() => ConversionEvent)
  conversionEvent: ConversionEvent;

  constructor(data?: Partial<AttributionCampaign>) {
    super(data);
  }
}

export interface AttributionCampaignRelations {
  // describe navigational properties here
}

export type AttributionCampaignWithRelations = AttributionCampaign & AttributionCampaignRelations;
