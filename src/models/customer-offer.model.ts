import {Entity, model, property, hasOne, belongsTo} from '@loopback/repository';
import {LoyaltyRewardDefinition} from './loyalty/loyalty-reward-definition.model';
import {RaleonUser} from './raleon-user.model';

@model()
export class CustomerOffer extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'number',
  })
  offerId?: number;

  @hasOne(() => LoyaltyRewardDefinition)
  loyaltyRewardDefinition: LoyaltyRewardDefinition;

  @belongsTo(() => RaleonUser)
  raleonUserId: number;

  constructor(data?: Partial<CustomerOffer>) {
    super(data);
  }
}

export interface CustomerOfferRelations {
  // describe navigational properties here
  raleonUser: RaleonUser;
}

export type CustomerOfferWithRelations = CustomerOffer & CustomerOfferRelations;
