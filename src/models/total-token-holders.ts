import {Model, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class TotalTokenHolders extends Model {
  @property({
    type: 'string',
    id: true,
    generated: false,
    required: true,
  })
  address: string;

  @property({
    type: 'string',
    required: true,
  })
  network: string;


  constructor(data?: Partial<TotalTokenHolders>) {
    super(data);
  }
}

export interface TotalTokenHoldersRelations {
  // describe navigational properties here
}

export type TotalTokenHoldersWithRelations = TotalTokenHolders & TotalTokenHoldersRelations;
