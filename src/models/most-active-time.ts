import {Model, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class MostActiveTime extends Model {
  @property({
    type: 'string',
    id: true,
    generated: false,
    required: true,
  })
  address: string;

  @property({
    type: 'string',
    required: true,
  })
  network: string;


  constructor(data?: Partial<MostActiveTime>) {
    super(data);
  }
}

export interface MostActiveTimeRelations {
  // describe navigational properties here
}

export type MostActiveTimeWithRelations = MostActiveTime & MostActiveTimeRelations;
