import {Model, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class AtRiskUsers extends Model {
  @property({
    type: 'string',
    id: true,
    generated: false,
    required: true,
  })
  address: string;

  @property({
    type: 'string',
    required: true,
  })
  network: string;


  constructor(data?: Partial<AtRiskUsers>) {
    super(data);
  }
}

export interface AtRiskUsersRelations {
  // describe navigational properties here
}

export type AtRiskUserseWithRelations = AtRiskUsers & AtRiskUsersRelations;
