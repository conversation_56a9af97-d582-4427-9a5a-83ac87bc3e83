import {Entity, model, property, belongsTo} from '@loopback/repository';
import {RewardTransaction} from '../services/quests/reward-types/reward-transaction.abstract';
import {RewardUserData} from '../services/quests/reward-types/reward-type.abstract';
import {Quest, QuestWithRelations} from './quest.model';
import {<PERSON>leon<PERSON><PERSON>, RaleonUserWithRelations} from './raleon-user.model';
import {IsGoalCompleteObjectResult} from '../services/quests/goal-types/goal-type.abstract';

@model()
export class Journey extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
	type:'string',
	index: {
		unique: true
	}
  })
  questUserKey: string;

  @property({
    type: 'string',
  })
  orgId?: string;

  @property({
    type: 'string',
  })
  status?: string;

  @property({
    type: 'number',
  })
  startCount?: number;

  @belongsTo(() => Quest)
  questId: number;

  @belongsTo(() => RaleonUser)
  raleonUserId: number;

  @property({
	type: 'object'
  })
  goalUserData?: { [goalTypeName: string]: any }; // JSON map of goal type name to user-data object for that goal type

  @property({
	type: 'object'
  })
  goalCompletionCache?: { [goalId: number]: { cacheTimeMs: number, complete: true | IsGoalCompleteObjectResult } }

  @property({
	type: 'object'
  })
  rewardUserData?: { [rewardTypeName: string]: RewardUserData }; // JSON map of reward type name to user-data object for that reward type

  @property({
	type: 'object'
  })
  rewardTransactionStates?: { [rewardTypeName: string]: RewardTransaction|undefined }; // JSON map of reward type name to transaction state for that reward type

  constructor(data?: Partial<Journey>) {
    super(data);
  }
}

export interface JourneyRelations {
  // describe navigational properties here
  quest: QuestWithRelations;
  raleonUser: RaleonUserWithRelations;
}

export type JourneyWithRelations = Journey & JourneyRelations;
