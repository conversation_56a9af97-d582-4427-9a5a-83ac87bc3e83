import {Entity, hasMany, model, property, hasOne, belongsTo} from '@loopback/repository';
import {Project} from './project.model';
import {RaleonUser} from './raleon-user.model';
import {OrganizationSettings} from './organization-settings.model';
import {Currency} from './currency.model';
import {OrganizationKeys} from './organization-keys.model';
import {Extensions} from './extensions.model';
import {OrganizationPlannerPlan} from './organization-planner-plan.model';

interface ExternalPlanDetails {
    displayName: string;
    partnerDevelopment: boolean;
    shopifyPlus: boolean;
}
@model()
export class Organization extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'date',
    defaultFn: 'now',
  })
  createdDate?: string;

  @property({
    type: 'date',
    required: false,
  })
  uninstalledDate?: string;

  @property({
    type: 'date',
    required: false,
  })
  shopifyConnectedDate?: string;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'boolean',
    required: false,
  })
  dev: boolean;

  @property({
    type: 'boolean',
    required: false,
  })
  beta: boolean;

  @property({
    type: 'boolean',
    required: false,
  })
  attribution: boolean;

  @property({
    type: 'string',
    required: false,
  })
  interactionData: string;

  @property({
    type: 'string',
    required: false,
  })
  plan?: string;

  @property({
    type: 'boolean',
  })
  selfService: boolean;

  @property({
    type: 'boolean'
  })
  selfServiceUpgradeRequested: boolean;

  @property({
    type: 'string',
  })
  externalDomain?: string;

  @property({
    type: 'object',
  })
  externalPlanDetails?: ExternalPlanDetails;

  @property({
    type: 'string',
  })
  chosenAdventure?: string;

  @property({
    type: 'boolean',
  })
  snippetVerified?: boolean;

  @property({
    type: 'string',
  })
  targetUsers?: string;

  @property({
    type: 'string',
  })
  profilePicUrl?: string;

  @property({
    type: 'string',
  })
  questAlbumArtUrl?: string;

  @property({
    type: 'string',
  })
  questBotName?: string;

  @property({
    type: 'string',
  })
  questLaunchText?: string;

  @property({
    type: 'string',
  })
  questLaunchAvatarUrl?: string;

  @property({
    type: 'string',
  })
  questBrandColor?: string;

  @property({
    type: 'boolean',
  })
  onboardingComplete?: boolean;

  @property({
    type: 'boolean',
  })
  historicalDataComplete?: boolean;

  @property({
    type: 'string',
  })
  branding?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  sampleLanguage?: string;

  @property({
    type: 'string',
    default: 'en'
  })
  language?: string;

  @property({
    type: 'boolean',
    default: false
  })
  isDemoOrg?: boolean;

  @property({
    type: 'string',
    required: false,
  })
  orgType?: string;

  @hasMany(() => Project, {keyTo: 'id', keyFrom: 'organizationId'})
  projects: Project[];

  @hasMany(() => RaleonUser, {keyTo: 'id', keyFrom: 'orgId'})
  raleonUsers: RaleonUser[];

  @hasMany(() => OrganizationSettings)
  organizationSettings: OrganizationSettings[];

  @hasOne(() => Currency)
  primaryCurrency: Currency;

  @hasMany(() => OrganizationKeys)
  organizationKeys: OrganizationKeys[];

  @hasMany(() => Extensions)
  extensions: Extensions[];

  @hasMany(() => OrganizationPlannerPlan)
  organizationPlannerPlans: OrganizationPlannerPlan[];

  @belongsTo(() => Organization)
  parentOrgId: number;

  constructor(data?: Partial<Organization>) {
    super(data);
  }
}

export interface OrganizationRelations {
	// describe navigational properties here
}

export type OrganizationWithRelations = Organization & OrganizationRelations;
