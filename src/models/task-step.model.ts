import {Entity, model, property, belongsTo} from '@loopback/repository';
import {TaskType} from './task-type.model';
import {Task} from './task.model';

@model()
export class TaskStep extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
    required: true,
  })
  description: string;

  @property({
    type: 'string',
    required: true,
  })
  status: string;

  @property({
    type: 'string',
  })
  data?: string;

  @property({
    type: 'number',
    required: true,
  })
  position: number;

  @belongsTo(() => TaskType)
  taskTypeId: number;

  @belongsTo(() => Task)
  taskId: number;

  constructor(data?: Partial<TaskStep>) {
    super(data);
  }
}

export interface TaskStepRelations {
  // describe navigational properties here
  taskType: TaskType;
}

export type TaskStepWithRelations = TaskStep & TaskStepRelations;
