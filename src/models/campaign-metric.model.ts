import {Model, model, property} from '@loopback/repository';

@model()
export class CampaignMetric extends Model {
  @property({
    type: 'string',
    required: true,
  })
  metricName: string;

  @property({
    type: 'any',
    required: true,
  })
  metricValue: any;


  constructor(data?: Partial<CampaignMetric>) {
    super(data);
  }
}

export interface CampaignMetricRelations {
  // describe navigational properties here
}

export type CampaignMetricWithRelations = CampaignMetric & CampaignMetricRelations;
