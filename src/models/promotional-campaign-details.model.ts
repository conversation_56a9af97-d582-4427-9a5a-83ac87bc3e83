import {Entity, model, property, belongsTo} from '@loopback/repository';
import {PromotionalCampaign} from './promotional-campaign.model';

@model({settings: {strict: false}})
export class PromotionalCampaignDetails extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  externalId?: string;

  @property({
    type: 'string',
  })
  functionId?: string;

  @property({
    type: 'string',
  })
  productVariantId?: string;

  @property({
    type: 'number',
  })
  splitPercentage?: number;

  @property({
    type: 'string',
    jsonSchema: {
      enum: ['basic', 'choose_gift']
    },
    default: 'basic'
  })
  mode?: string;

  @property({
    type: 'string'
  })
  giftOptions?: string;

  @property({
    type: 'string',
  })
  externalData?: string;

  @belongsTo(() => PromotionalCampaign)
  promotionalCampaignId: number;

  [prop: string]: any;

  constructor(data?: Partial<PromotionalCampaignDetails>) {
    super(data);
  }
}

export interface PromotionalCampaignDetailsRelations {
  // describe navigational properties here
}

export type PromotionalCampaignDetailsWithRelations = PromotionalCampaignDetails & PromotionalCampaignDetailsRelations;
