import {Model, model, property} from '@loopback/repository';
import {CampaignMetric} from './campaign-metric.model';

@model()
export class CampaignMetricsContainer extends Model {
  @property({
    type: 'number',
    required: true,
  })
  campaignId: number;

  @property({
    type: 'number',
  })
  questId?: number;

  @property({
    type: 'array',
    itemType: 'object',
    required: true,
  })
  metrics: CampaignMetric[];


  constructor(data?: Partial<CampaignMetricsContainer>) {
    super(data);
  }
}

export interface CampaignMetricsContainerRelations {
  // describe navigational properties here
}

export type CampaignMetricsContainerWithRelations = CampaignMetricsContainer & CampaignMetricsContainerRelations;
