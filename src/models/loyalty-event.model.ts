import {Entity, model, property} from '@loopback/repository';

@model()
export class LoyaltyEvent extends Entity {
  @property({
    type: 'number',
    id: true,
  })
  id: number;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
    required: true,
  })
  friendlyName: string;

  @property({
     type: 'object',
  })
  dataStructure?: object;

  @property({
	type: 'string',
  })
  description?: string;

  constructor(data?: Partial<LoyaltyEvent>) {
    super(data);
  }
}

export interface LoyaltyEventRelations {
  // describe navigational properties here
}

export type LoyaltyEventWithRelations = LoyaltyEvent & LoyaltyEventRelations;
