import {Entity, model, property} from '@loopback/repository';

@model()
export class Async<PERSON>ob extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
    required: true,
  })
  jobId: string;

  @property({
    type: 'string',
    required: true,
    jsonSchema: {
      enum: ['running', 'completed', 'failed']
    }
  })
  status: string;

  @property({
    type: 'number',
    default: 0
  })
  progressPercentage: number;

  @property({
    type: 'string',
  })
  statusMessage?: string;

  @property({
    type: 'string',
  })
  jobType?: string;

  @property({
    type: 'number',
  })
  planId?: number;

  @property({
    type: 'string',
  })
  error?: string;

  @property({
    type: 'object',
  })
  ideas?: object[];

  @property({
    type: 'object',
  })
  data?: object;

  @property({
    type: 'number',
    required: true,
  })
  organizationId: number;

  @property({
    type: 'date',
    default: () => new Date(),
  })
  createdAt?: Date;

  @property({
    type: 'date',
    default: () => new Date(),
  })
  updatedAt?: Date;

  constructor(data?: Partial<AsyncJob>) {
    super(data);
  }
}

export interface AsyncJobRelations {
  // describe navigational properties here
}

export type AsyncJobWithRelations = AsyncJob & AsyncJobRelations;
