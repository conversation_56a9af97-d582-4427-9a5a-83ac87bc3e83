import {Entity, model, property} from '@loopback/repository';

@model()
export class AvailableExtensions extends Entity {
  @property({
    type: 'number',
    id: true
  })
  id?: number;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
    required: true,
  })
  description: string;

  @property({
    type: 'string',
    required: true,
  })
  endpoint: string;

  @property({
	type: 'string'
  })
  functionId?: string;

  constructor(data?: Partial<AvailableExtensions>) {
    super(data);
  }
}

export interface AvailableExtensionsRelations {
  // describe navigational properties here
}

export type AvailableExtensionsWithRelations = AvailableExtensions & AvailableExtensionsRelations;
