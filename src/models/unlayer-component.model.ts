import {Entity, model, property} from '@loopback/repository';

@model()
export class UnlayerComponent extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
	type: 'string',
  })
  editableFields?: string;

  @property({
	type: 'number',
  })
  orgId?: number;

  @property({
	type: 'number',
  })
  overrideId?: number;

  @property({
    type: 'string',
    required: true,
  })
  json: string;

  @property({
    type: 'string',
    default: 'unlayer',
  })
  type: string;

  @property({
    type: 'boolean',
    default: true,
  })
  active: boolean;

  constructor(data?: Partial<UnlayerComponent>) {
    super(data);
  }
}
