import {Model, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class LifetimeTransaction extends Model {
  @property({
    type: 'string',
    id: true,
    generated: false,
    required: true,
  })
  address_network_metric: string;

  @property({
    type: 'string',
    required: true,
  })
  address: string;

  @property({
    type: 'string',
    required: true,
  })
  date_processed: string;

  @property({
    type: 'string',
    required: true,
  })
  transaction_count: string;

  @property({
    type: 'string',
    required: true,
  })
  metric: string;

  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<LifetimeTransaction>) {
    super(data);
  }
}

export interface LifetimeTransactionRelations {
  // describe navigational properties here
}

export type LifetimeTransactionWithRelations = LifetimeTransaction & LifetimeTransactionRelations;
