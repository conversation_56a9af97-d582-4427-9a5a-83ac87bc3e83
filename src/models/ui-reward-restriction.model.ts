import {Entity, model, property} from '@loopback/repository';

@model()
export class UiRewardRestriction extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
    default: "number",
  })
  inputType?: string;

  @property({
    type: 'string',
    required: true,
  })
  fieldOnDefinition: string;

  @property({
    type: 'number',
  })
  uiCustomerRewardId?: number;

  @property({
	type: 'number'
  })
  defaultAmount?: number;

  @property({
	type: 'number'
  })
  minimumAmount?: number;

  @property({
	type: 'boolean',
	default: false,
  })
  required?: boolean;

  @property({
	type: 'string'
  })
  definitionModel?: string;

  constructor(data?: Partial<UiRewardRestriction>) {
    super(data);
  }
}

export interface UiRewardRestrictionRelations {
  // describe navigational properties here
}

export type UiRewardRestrictionWithRelations = UiRewardRestriction & UiRewardRestrictionRelations;
