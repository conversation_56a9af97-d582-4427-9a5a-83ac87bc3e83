import {Model, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class MetricProof extends Model {
  @property({
    type: 'array',
    id: false,
    generated: false,
    required: false,
    itemType: 'string'
  })
  walletIds: string;

  constructor(data?: Partial<MetricProof>) {
    super(data);
  }
}

export interface MetricProofRelations {
  // describe navigational properties here
}

export type MetricProofWithRelations = MetricProof & MetricProofRelations;
