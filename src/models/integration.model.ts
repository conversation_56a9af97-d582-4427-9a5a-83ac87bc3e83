import {Entity, model, property} from '@loopback/repository';

@model()
export class Integration extends Entity {
	@property({
		type: 'number',
		id: true,
		//generated: true,
	})
	id?: number;

	@property({
		type: 'string',
		required: true,
	})
	name: string;

	@property({
		type: 'string',
		required: true,
	})
	description: string;

	@property({
		type: 'string',
		jsonSchema: {
			enum: ['product-review', 'email', 'product-photo-review']
		}
	})
	category: string;

	@property({
		type: 'string',
		required: false,
	})
	exchangeApiUrl: string;


	@property({
		type: 'string',
		required: false,
	})
	oAuthURL: string;

	@property({
		type: 'string',
		required: false,
	})
	imageURL: string;

	@property({
		type: 'string',
		required: false,
	})
	docURL: string;

	@property({
		type: 'string',
		required: false,
	})
	docURLText: string;

	@property({
		type: 'string',
		required: false,
	})
	enableDisableURL: string;


	@property({
		type: 'boolean',
		required: false,
	})
	showConnectButton: boolean;

	@property({
		type: 'boolean',
		required: false,
	})
	canBeActivated: boolean;

	@property({
		type: 'string',
		required: false,
	})
	customComponent?: string;

	@property({
		type: 'string',
		required: false,
	})
	errorMessage?: string;

	@property({
		type: 'string',
		required: false,
	})
	fieldMappings?: string;

	@property({
		type: 'number',
		required: false,
	})
	sortOrder?: number;

	constructor(data?: Partial<Integration>) {
		super(data);
	}
}

export interface IntegrationRelations {
	// describe navigational properties here
}

export type IntegrationWithRelations = Integration & IntegrationRelations;
