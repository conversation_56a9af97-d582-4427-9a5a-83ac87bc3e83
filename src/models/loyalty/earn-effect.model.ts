import {Entity, model, property, belongsTo} from '@loopback/repository';
import {LoyaltyCurrency} from './loyalty-currency.model';
import {LoyaltyRewardDefinition, LoyaltyRewardDefinitionWithRelations} from './loyalty-reward-definition.model';

@model()
export class EarnEffect extends Entity {
	@property({
		type: 'number',
		id: true,
		generated: true,
	})
	id?: number;

	@property({
		type: 'number',
	        postgresql: {
	          dataType: 'double precision',
	        },
		default: 0,
	})
	points?: number;

	@property({
		type: 'number',
	        postgresql: {
	          dataType: 'double precision',
	        },
		default: 0,
	})
	pointsPerDollar?: number;

	@belongsTo(() => LoyaltyCurrency)
	loyaltyCurrencyId?: number;

	@property({
		type: 'number',
	})
	loyaltyEarnId?: number;

	@property({
		type: 'string'
	})
	type: string;

	@property({
		type: 'string'
	})
	name: string;

	@property({
		type: 'string'
	})
	description: string;

	@property({
		type: 'string',
	})
	imageURL: string;

	@property({
		type: 'boolean',
		default: true,
	})
	includeTaxes: boolean;

	@property({
		type: 'boolean',
		default: true,
	})
	includeShipping: boolean;

	@belongsTo(() => LoyaltyRewardDefinition)
	loyaltyRewardDefinitionId?: number;

	constructor(data?: Partial<EarnEffect>) {
		super(data);
	}
}

export interface EarnEffectRelations {
	// describe navigational properties here
	loyaltyRewardDefinition?: LoyaltyRewardDefinitionWithRelations;
}

export type EarnEffectWithRelations = EarnEffect & EarnEffectRelations;
