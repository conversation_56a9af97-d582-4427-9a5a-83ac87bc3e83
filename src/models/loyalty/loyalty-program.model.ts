import {Entity, hasMany, model, property} from '@loopback/repository';
import {LoyaltyCurrency} from './loyalty-currency.model';
import {LoyaltyCampaign} from './loyalty-campaign.model';
import {LoyaltyRewardDefinition} from './loyalty-reward-definition.model';

@model()
export class LoyaltyProgram extends Entity {
	@property({
		type: 'string',
		required: true,
	})
	name: string;

	@property({
		type: 'number',
		id: true,
		generated: true,
	})
	id?: number;

	@property({
		type: 'boolean',
		required: true,
	})
	active: boolean;

	@property({
		type: 'date'
	})
	activationDate?: string;

	@property({
		type: 'boolean',
		default: true,
	})
	launcherActive?: boolean;

	@property({
		type: 'boolean',
		default: true,
	})
	notificationsActive?: boolean;

	@property({
		type: 'boolean',
		default: false,
	})
	referralsActive?: boolean;

	@property({
		type: 'number',
		required: true,
	})
	orgId: number;

	@property({
		type: 'string',
		required: false,
	})
	exclusionTags?: string;

	@property({
		type: 'string',
		required: false,
	})
	orderExclusionTags?: string;

	@property({
		type: 'string',
		required: false,
	})
	excludedProductIds?: string;

	@hasMany(() => LoyaltyCurrency)
	loyaltyCurrencies: LoyaltyCurrency[];

	@hasMany(() => LoyaltyCampaign)
	loyaltyCampaigns: LoyaltyCampaign[];

	@hasMany(() => LoyaltyRewardDefinition)
	loyaltyRewardDefinitions: LoyaltyRewardDefinition[];

	constructor(data?: Partial<LoyaltyProgram>) {
		super(data);
	}
}

export interface LoyaltyProgramRelations {
	// describe navigational properties here
}

export type LoyaltyProgramWithRelations = LoyaltyProgram & LoyaltyProgramRelations;
