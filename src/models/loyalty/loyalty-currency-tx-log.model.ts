import {Entity, model, property} from '@loopback/repository';

@model({
  settings: {
    indexes: {
      currencyBalanceId: {
        keys: {
          loyaltyCurrencyBalanceId: -1
        }
      },
      orderId_index: {
        keys: {
          orderId: 1
        }
      }
    }
  }
})

export class LoyaltyCurrencyTxLog extends Entity {
	@property({
		type: 'number',
		id: true,
		generated: true,
	})
	id?: number;

	@property({
		type: 'date',
		required: true,
	})
	date: string;

	@property({
		type: 'string',
		required: true,
	})
	info: string;

	@property({
		type: 'number',
		default: 0,
	})
	amount?: number;

	@property({
		type: 'number',
	})
	loyaltyCurrencyBalanceId?: number;

	@property({
		type: 'boolean',
	})
	customerViewed?: boolean;

	@property({
		type: 'string',
		required: false,
	})
	orderId?: string;

	@property({
		type: 'string',
		required: false,
	})
	itemId?: string;

	@property({
		type: 'number',
		required: false,
	})
	earnEffectId?: number;

	constructor(data?: Partial<LoyaltyCurrencyTxLog>) {
		super(data);
	}
}

export interface LoyaltyCurrencyTxLogRelations {
	// describe navigational properties here
}

export type LoyaltyCurrencyTxLogWithRelations = LoyaltyCurrencyTxLog & LoyaltyCurrencyTxLogRelations;
