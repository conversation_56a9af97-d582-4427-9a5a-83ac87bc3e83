import {Entity, model, property, belongsTo} from '@loopback/repository';
import {LoyaltyRewardDefinition} from './loyalty-reward-definition.model';

@model({
	settings: {
		strict: false
	}
})
export class LoyaltyRedemptionShopItem extends Entity {
	@property({
		type: 'number',
		id: true,
		generated: true,
	})
	id?: number;

	@property({
		type: 'string',
		required: true,
	})
	name: string;

	@property({
		type: 'boolean',
		default: false
	})
	active: boolean;

	@property({
		type: 'boolean',
		default: false
	})
	archived: boolean;

	@property({
		type: 'number',
		postgresql: {
			dataType: 'double precision',
		},
		required: true,
	})
	price: number;

	@property({
		type: 'number',
	})
	loyaltyCampaignId?: number;

	@property({
		type: 'string'
	})
	imageURL: string;

	@property({
		type: 'string'
	})
	description: string;

	@property({
		type: 'boolean',
		default: false
	})
	recommendedByAI?: boolean;

	@property({
		type: 'number',
		default: 999
	})
	priority: number;

	@property({
		type: 'string',
		jsonSchema: {
			enum: ['NONE', 'RECOMMENDED', 'IGNORED', 'APPROVED_RECOMMENDATION']
		},
		default: 'NONE'
	})
	recommendationState?: string;


	@belongsTo(() => LoyaltyRewardDefinition)
	loyaltyRewardDefinitionId: number;

    isRecommendation: boolean = false;
    ignoreRecommendation: boolean = false;

	constructor(data?: Partial<LoyaltyRedemptionShopItem>) {
		super(data);

		Object.defineProperty(this, 'isRecommendation', {
			get() {
				return this.recommendedByAI && this.recommendationState === 'RECOMMENDED';
			}
		});

		Object.defineProperty(this, 'ignoreRecommendation', {
			get() {
				return this.recommendedByAI && this.recommendationState === 'IGNORED';
			}
		});
	}
}

export interface LoyaltyRedemptionShopItemRelations {
	// describe navigational properties here
}

export type LoyaltyRedemptionShopItemWithRelations = LoyaltyRedemptionShopItem & LoyaltyRedemptionShopItemRelations;
