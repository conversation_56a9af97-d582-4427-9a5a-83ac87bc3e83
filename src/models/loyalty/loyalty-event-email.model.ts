import {Entity, model, property, hasMany, hasOne, belongsTo} from '@loopback/repository';
import {LoyaltyEvent} from '../loyalty-event.model';

@model()
export class LoyaltyEventEmail extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'number',
    required: true,
  })
  orgId: number;

  @belongsTo(() => LoyaltyEvent)
  loyaltyEventId: number;

  @property({
    type: 'boolean',
    required: true,
	default: false,
  })
  active: boolean;

  @property({
    type: 'string',
    default: false,
  })
  emailSubject?: string;

  @property({
    type: 'string',
    default: false,
  })
  fromName?: string;

  @property({
    type: 'string',
  })
  branding?: string;

  @property({
	type: 'boolean',
	default: false
  })
  smartSend: boolean;

  [prop: string]: any;

  constructor(data?: Partial<LoyaltyEventEmail>) {
    super(data);
  }
}

export interface LoyaltyEventEmailRelations {
  // describe navigational properties here
}

export type LoyaltyEventEmailWithRelations = LoyaltyEventEmail & LoyaltyEventEmailRelations;
