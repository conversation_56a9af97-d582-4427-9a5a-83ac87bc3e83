import {Entity, model, property, hasOne, belongsTo} from '@loopback/repository';
import {RewardCoupon, RewardCouponWithRelations} from './reward-coupon.model';

@model()
export class LoyaltyRewardDefinition extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'number',
    required: true,
  })
  daysToRedeem: number;

  @property({
    type: 'boolean',
    required: true,
  })
  redeemable: boolean;

  @property({
    type: 'boolean',
    default: false,
  })
  grantable?: boolean;

  @property({
    type: 'number',
    default: 0,
  })
  redeemed?: number;

  @property({
    type: 'number',
    default: 0,
  })
  granted?: number;

  @property({
    type: 'number',
    default: -1,
  })
  startingInventory?: number;

  @property({
    type: 'number',
    default: 3,
  })
  maxUserGrants?: number;

  @property({
    type: 'number',
    default: 3,
  })
  maxUserRedemptions?: number;

  @property({
    type: 'number',
    postgresql: {
      dataType: 'double precision',
    },
    default: 100,
  })
  price?: number;

  @property({
    type: 'number',
  })
  loyaltyProgramId?: number;

  @belongsTo(() => RewardCoupon)
  rewardCouponId: number;

  @property({
    type: 'number',
  })
  loyaltyCampaignId?: number;

  @property({
    type: 'number',
  })
  customerOfferId?: number;

  constructor(data?: Partial<LoyaltyRewardDefinition>) {
    super(data);
  }
}

export interface LoyaltyRewardDefinitionRelations {
  // describe navigational properties here
  rewardCoupon?: RewardCouponWithRelations;
}

export type LoyaltyRewardDefinitionWithRelations = LoyaltyRewardDefinition & LoyaltyRewardDefinitionRelations;
