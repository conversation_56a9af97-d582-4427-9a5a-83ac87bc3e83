import {Entity, model, property} from '@loopback/repository';

@model()
export class EarnCondition extends Entity {
	@property({
		type: 'number',
		id: true,
		generated: true,
	})
	id?: number;

	@property({
		type: 'string',
		required: true,
	})
	type: string;

	@property({
		type: 'string',
	})
	variable?: string;

	@property({
		type: 'string',
	})
	operator?: string;

	@property({
		type: 'number',
		postgresql: {
			dataType: 'double precision',
		},
	})
	amount?: number;

	@property({
		type: 'string',
	})
	triggeredEvent?: string;

	@property({
		type: 'number',
	})
	loyaltyEarnId?: number;

	@property({
		type: 'string'
	})
	textValue?: string;

	@property({
		type: 'string',
	})
	handle?: string;

	constructor(data?: Partial<EarnCondition>) {
		super(data);
	}
}

export interface EarnConditionRelations {
	// describe navigational properties here
}

export type EarnConditionWithRelations = EarnCondition & EarnConditionRelations;
