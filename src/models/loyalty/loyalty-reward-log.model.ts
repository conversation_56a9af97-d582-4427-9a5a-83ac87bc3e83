import {Entity, belongsTo, model, property} from '@loopback/repository';
import {LoyaltyRewardDefinition} from './loyalty-reward-definition.model';
import {RewardCoupon} from './reward-coupon.model';
import {RaleonUser} from '../raleon-user.model';
import {InventoryCoupon} from './inventory-coupon.model';

@model()
export class LoyaltyRewardLog extends Entity {
	@property({
		type: 'number',
		id: true,
		generated: true,
	})
	id?: number;

	@property({
		type: 'date',
		required: true,
	})
	date: string;

	@property({
		type: 'boolean',
	})
	granted: boolean;

	@property({
		type: 'boolean',
	})
	redeemed: boolean;

	@property({
		type: 'boolean',
	})
	customerViewed?: boolean;

	@property({
		type: 'string',
		required: false,
	})
	orderId?: string;

	@property({
		type: 'string',
		required: false,
	})
	itemId?: string;

	@property({
		type: 'number',
		required: false,
	})
	earnEffectId?: number;

	@belongsTo(() => LoyaltyRewardDefinition)
	loyaltyRewardDefinitionId: number;

	@belongsTo(() => InventoryCoupon)
	inventoryCouponId: number;

	@belongsTo(() => RaleonUser)
	raleonUserId: number;

	constructor(data?: Partial<LoyaltyRewardLog>) {
		super(data);
	}
}

export interface LoyaltyRewardLogRelations {
	// describe navigational properties here
}

export type LoyaltyRewardLogWithRelations = LoyaltyRewardLog & LoyaltyRewardLogRelations;
