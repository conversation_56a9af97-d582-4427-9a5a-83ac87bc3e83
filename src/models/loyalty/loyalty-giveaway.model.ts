import {Entity, model, property, belongsTo} from '@loopback/repository';
import {LoyaltyProgram} from './loyalty-program.model';

@model()
export class LoyaltyGiveaway extends Entity {
	@property({
		type: 'number',
		id: true,
		generated: true,
	})
	id?: number;

	@property({
		type: 'string',
		required: true,
	})
	name: string;

	@property({
		type: 'string',
		required: true,
	})
	startDate: string;

	@property({
		type: 'string',
		required: true,
	})
	endDate: string;

	@property({
		type: 'boolean',
		default: false,
	})
	launched: boolean;

	@belongsTo(() => LoyaltyProgram)
	loyaltyProgramId: number;

	constructor(data?: Partial<LoyaltyGiveaway>) {
		super(data);
	}
}

export interface LoyaltyGiveawayRelations {
  // describe navigational properties here
  loyaltyProgram: LoyaltyProgram
}

export type LoyaltyGiveawayWithRelations = LoyaltyGiveaway & LoyaltyGiveawayRelations;
