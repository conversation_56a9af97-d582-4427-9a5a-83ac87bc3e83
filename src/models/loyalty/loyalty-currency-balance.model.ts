import {Entity, model, property, hasMany, belongsTo} from '@loopback/repository';
import {LoyaltyCurrencyTxLog} from './loyalty-currency-tx-log.model';
import {LoyaltyCurrency} from './loyalty-currency.model';
import {RaleonUser} from '../raleon-user.model';

@model({
  settings: {
    indexes: {
      uniqueBalanceIndex: {
        keys: {
          loyaltyCurrencyId: 1,
          raleonUserId: 1,
        },
        options: {
          unique: true,
        },
      },
    },
  },
})
export class LoyaltyCurrencyBalance extends Entity {
	@property({
		type: 'number',
		id: true,
		generated: true,
	})
	id?: number;

	@property({
		type: 'number',
	        postgresql: {
	          dataType: 'double precision',
	        },
		default: 0,
	})
	balance?: number;

	@property({
		type: 'date',
		defaultFn: 'now',
	})
	updatedDate?: string;

	@property({
		type: 'number',
	        postgresql: {
	          dataType: 'double precision',
	        },
		default: 0,
		required: false
	})
	trailingTwelveMonthGrantTotal?: number;

	@property({
		type: 'date',
		required: false,
	})
	earliestTtmGrantDate?: string;

	@hasMany(() => LoyaltyCurrencyTxLog)
	loyaltyCurrencyTxLogs: LoyaltyCurrencyTxLog[];

	@belongsTo(() => LoyaltyCurrency)
	loyaltyCurrencyId: number;

	@belongsTo(() => RaleonUser)
	raleonUserId: number;

	constructor(data?: Partial<LoyaltyCurrencyBalance>) {
		super(data);
	}
}

export interface LoyaltyCurrencyBalanceRelations {
	// describe navigational properties here
}

export type LoyaltyCurrencyBalanceWithRelations = LoyaltyCurrencyBalance & LoyaltyCurrencyBalanceRelations;
