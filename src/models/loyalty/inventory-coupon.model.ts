import {Entity, model, property, belongsTo} from '@loopback/repository';
import {RewardCoupon} from './reward-coupon.model';
import {LoyaltyRewardDefinition} from './loyalty-reward-definition.model';
import {LoyaltyGiveaway} from './loyalty-giveaway.model';
import {v4 as uuidv4} from 'uuid';

@model({
	settings: {
		indexes: {
			uuid_idx: {
			keys: {
				uuid: 1
			},
			options: {
				unique: true
			}
			}
		}
	}
})
export class InventoryCoupon extends Entity {
	@property({
		type: 'number',
		id: true,
		generated: true,
	})
	id?: number;

	@property({
		type: 'string',
		required: true,
	})
	name: string;

	@property({
		type: 'number',
		required: true,
	})
	orgId: number;

	@property({
		type: 'date',
	})
	expiration?: string;

	@property({
		type: 'boolean',
		required: true,
	})
	used: boolean;

	@property({
		type: 'date',
	})
	usedDate?: string;

	@property({
		type: 'string'
	})
	externalId?: string;

	@property({
		type: 'boolean',
		default: false
	})
	hiddenFromLoyaltyUi: boolean;

	@belongsTo(() => RewardCoupon)
	rewardCouponId: number;

	@belongsTo(() => LoyaltyRewardDefinition)
	loyaltyRewardDefinitionId?: number;

	@property({
		type: 'number',
	})
	raleonUserId?: number;

	@belongsTo(() => LoyaltyGiveaway)
	loyaltyGiveawayId?: number;

	@property({
		type: 'string',
		required: false,
		default: () => uuidv4(),
	  })
	uuid?: string;

	constructor(data?: Partial<InventoryCoupon>) {
		super(data);
		if (!this.uuid) {
			this.uuid = uuidv4();
		}
	}
}

export interface InventoryCouponRelations {
	// describe navigational properties here
	rewardCoupon: RewardCoupon;
	loyaltyRewardDefinition: LoyaltyRewardDefinition;
}

export type InventoryCouponWithRelations = InventoryCoupon & InventoryCouponRelations;
