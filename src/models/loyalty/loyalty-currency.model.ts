import {Entity, belongsTo, model, property, hasMany} from '@loopback/repository';
import {LoyaltyProgram} from './loyalty-program.model';
import {LoyaltyCurrencyBalance} from './loyalty-currency-balance.model';

@model()
export class LoyaltyCurrency extends Entity {
	@property({
		type: 'number',
		id: true,
		generated: true,
	})
	id?: number;

	@property({
		type: 'string',
		required: true,
	})
	name: string;

	@property({
		type: 'string',
		required: false,
		default: 'pts'
	})
	abbreviatedName?: string;

	@property({
		type: 'number',
		postgresql: {
			dataType: 'double precision',
		},
		required: true,
	})
	conversionToUSD: number;

	@belongsTo(() => LoyaltyProgram)
	loyaltyProgramId: number;

	@hasMany(() => LoyaltyCurrencyBalance)
	loyaltyCurrencyBalances: LoyaltyCurrencyBalance[];

	constructor(data?: Partial<LoyaltyCurrency>) {
		super(data);
	}
}

export interface LoyaltyCurrencyRelations {
	// describe navigational properties here
}

export type LoyaltyCurrencyWithRelations = LoyaltyCurrency & LoyaltyCurrencyRelations;
