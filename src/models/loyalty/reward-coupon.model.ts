import {Entity, model, property, belongsTo} from '@loopback/repository';
import {LoyaltyGiveaway} from './loyalty-giveaway.model';

@model()
export class RewardCoupon extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'number',
    required: true,
	postgresql: {
	  dataType: 'double precision',
	},
  })
  amount: number;

  @property({
    type: 'string',
    required: true,
  })
  amountType: string;

  @property({
    type: 'number',
  })
  expiresInDays?: number;

  @property({
    type: 'number',
	postgresql: {
	  dataType: 'double precision',
	},
  })
  minimumOrderTotal?: number;

  @property({
    type: 'number',
	postgresql: {
	  dataType: 'double precision',
	},
  })
  maximumDiscount?: number;

  @property({
    type: 'boolean',
    default: false
  })
  hiddenFromLoyaltyUi: boolean;

  @property({
    type: 'boolean',
    default: false
  })
  hiddenFromAdminUi: boolean;

  @property({
    type: 'string',
    required: false,
  })
  imageURL: string;

  @property({
    type: 'string',
    required: false,
  })
  externalId?: string; //shopify collection id

  @property({
    type: 'string',
    required: false,
  })
  secondaryExternalId?: string; //shopify product variant id

  @property({
    type: 'string',
    required: false,
  })
  externalName?: string; //shopify collection name

  @property({
	type: 'string',
	required: false,
  })
  externalLink?: string; //shopify collection handle (slug)

  @property({
	type: 'boolean',
	required: false,
  })
  appliesOnSubscriptions?: boolean;

  @property({
	type: 'boolean',
	required: false,
  })
  combinesWithOrders?: boolean;

  @property({
	type: 'boolean',
	required: false,
  })
  combinesWithProducts?: boolean;

  @property({
	type: 'boolean',
	required: false,
  })
  combinesWithShipping?: boolean;

  @belongsTo(() => LoyaltyGiveaway)
  loyaltyGiveawayId?: number;

  constructor(data?: Partial<RewardCoupon>) {
    super(data);
  }
}

export interface RewardCouponRelations {
  // describe navigational properties here
}

export type RewardCouponWithRelations = RewardCoupon & RewardCouponRelations;

export const REWARD_COUPON_AMOUNT_TYPES = {
  DOLLAR: '$' || 'dollar',
  PERCENT: '%' || 'percent'
}
