import {Entity, model, property, hasMany, hasOne} from '@loopback/repository';
import {CustomerOffer} from './customer-offer.model';

@model()
export class Offer extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
    required: true,
  })
  type: string;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'number',
    required: true,
  })
  orgId: number;

  @property({
    type: 'boolean',
    default: false,
  })
  generated?: boolean;

  @property({
    type: 'string',
  })
  discountType?: string;

  @property({
    type: 'boolean',
    default: false,
  })
  archived?: boolean;

  @property({
	type: 'string',
	required: true,
  })
  collectionId: string;

  @property({
    type: 'string',
  })
  segmentType?: string;

  @property({
    type: 'string',
  })
  segment?: string;

  @hasMany(() => CustomerOffer)
  customerOffers: CustomerOffer[];

  [prop: string]: any;

  constructor(data?: Partial<Offer>) {
    super(data);
  }
}

export interface OfferRelations {
  // describe navigational properties here
}

export type OfferWithRelations = Offer & OfferRelations;
