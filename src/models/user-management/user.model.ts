import {belongsTo, Entity, hasOne, model, property} from '@loopback/repository';
import {Organization} from '../organization.model';
import {UserCredentials} from './user-credentials.model';

@model()
export class User extends Entity {
  @property({
    type: 'number',
    generated: true,
    id: true,
  })
  id?: number;

  @property({
    type: 'string',
    required: true,
  })
  email: string;

  @property({
    type: 'boolean',
  })
  isSecondaryAccount: boolean;

  @property({
    type: 'string',
  })
  firstName?: string;

  @property({
    type: 'string',
  })
  lastName?: string;

  @property({
    type: 'string',
    required: false,
  })
  avatarColors: string;

  @hasOne(() => UserCredentials)
  userCredentials: UserCredentials;

  @property({
    type: 'string',
  })
  resetKey?: string;

  @property({
    type: 'number',
  })
  resetCount: number;

  @property({
    type: 'string',
  })
  resetTimestamp: string;

  @property({
    type: 'string',
  })
  resetKeyTimestamp: string;

  @belongsTo(() => Organization)
  organizationId: number;

  @property({
    type: 'array',
    itemType: 'string',
  })
  roles?: string[];

//   @property({
// 	type: 'boolean',
//   })
//   emailVerified: boolean;

  constructor(data?: Partial<User>) {
    super(data);
  }
}

@model()
export class UserWithPassword extends User {
  @property({
    type: 'string',
    required: true,
  })
  password: string;

  constructor(data?: Partial<UserWithPassword>) {
    super(data);
  }
}



export interface UserRelations {
  organization: Organization;
}

export type UserWithRelations = User & UserRelations;
