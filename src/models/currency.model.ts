import {Entity, model, property, belongsTo} from '@loopback/repository';
import {SupportedCurrencies} from './supported-currencies.model';

@model()
export class Currency extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'number',
  })
  organizationId?: number;

  @belongsTo(() => SupportedCurrencies)
  supportedCurrenciesId: number;

  constructor(data?: Partial<Currency>) {
    super(data);
  }
}

export interface CurrencyRelations {
  // describe navigational properties here
}

export type CurrencyWithRelations = Currency & CurrencyRelations;
