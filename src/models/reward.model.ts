import {Entity, model, property, belongsTo, hasOne} from '@loopback/repository';
import {Quest} from './quest.model';
import {Content} from './content.model';
import {RewardConfigData} from '../services/quests/reward-types/reward-type.abstract';

@model()
export class Reward extends Entity {
	@property({
		type: 'number',
		id: true,
		generated: true,
	})
	id?: number;

	@property({
		type: 'string',
	})
	externalId?: string;

	@property({
		type: 'string',
		required: true
	})
	type: string;

	@property({
		type: 'string',
		required: false
	})
	receiptType?: string;

	@property({
		type: 'object',
		required: true,
	})
	configData: RewardConfigData;

	@hasOne(() => Content)
	content?: Content;

	@belongsTo(() => Quest)
	questId?: number;

	constructor(data?: Partial<Reward>) {
		super(data);
	}
}

export interface RewardRelations {
	// describe navigational properties here
}

export type RewardWithRelations = Reward & RewardRelations;
