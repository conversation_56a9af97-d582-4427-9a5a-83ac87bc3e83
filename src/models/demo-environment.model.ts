import {Entity, model, property} from '@loopback/repository';

@model()
export class DemoEnvironment extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  storeUrl?: string;

  @property({
    type: 'number',
  })
  aov?: number;

  @property({
    type: 'number',
  })
  ltv?: number;

  @property({
    type: 'string',
  })
  defaultEmail?: string;

  @property({
    type: 'string',
  })
  defaultPassword?: string;

  @property({
    type: 'number',
  })
  orgId?: number;

  @property({
	type: 'number'
  })
  numberOfUsers?: number;


  constructor(data?: Partial<DemoEnvironment>) {
    super(data);
  }
}

export interface DemoEnvironmentRelations {
  // describe navigational properties here
}

export type DemoEnvironmentWithRelations = DemoEnvironment & DemoEnvironmentRelations;
