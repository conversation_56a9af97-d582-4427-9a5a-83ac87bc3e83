import {Entity, belongsTo, model, property} from '@loopback/repository';
import {Organization} from './organization.model';
import {MetricSegment} from './metric-segment.model';

@model({settings: {strict: false}})
export class OrganizationMetricSegment extends Entity {
	@property({
		type: 'number',
		id: true,
		generated: true,
	})
	id?: number;

	@belongsTo(() => Organization)
	orgId: number;

	@belongsTo(() => MetricSegment, {name: 'metricSegment'})
	metricSegmentId: number

	constructor(data?: Partial<OrganizationMetricSegment>) {
		super(data);
	}
}

export interface OrganizationMetricSegmentRelations {
	metricSegment: MetricSegment;
}

export type OrganizationMetricSegmentWithRelations = OrganizationMetricSegment & OrganizationMetricSegmentRelations;
