import {Entity, model, property, hasOne} from '@loopback/repository';
import {UiCustomerAction} from './ui-customer-action.model';
import {UiCustomerReward} from './ui-customer-reward.model';

@model()
export class UiActionRewardJunction extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'number',
  })
  uiCustomerActionId?: number;

  @property({
    type: 'number',
  })
  uiCustomerRewardId?: number;

  constructor(data?: Partial<UiActionRewardJunction>) {
    super(data);
  }
}

export interface UiActionRewardJunctionRelations {
  // describe navigational properties here
}

export type UiActionRewardJunctionWithRelations = UiActionRewardJunction & UiActionRewardJunctionRelations;
