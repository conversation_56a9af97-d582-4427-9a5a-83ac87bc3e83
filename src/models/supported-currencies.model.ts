import {Entity, model, property} from '@loopback/repository';

@model()
export class SupportedCurrencies extends Entity {
	@property({
		type: 'number',
		id: true
	})
	id?: number;

	@property({
		type: 'string',
		required: true,
	})
	name: string;

	@property({
		type: 'string',
	})
	prefix?: string;

	@property({
		type: 'string',
	})
	postfix?: string;

	@property({
		type: 'number',
	        postgresql: {
	          dataType: 'double precision',
	        },
		required: true,
	})
	conversionToUSD?: number;


	constructor(data?: Partial<SupportedCurrencies>) {
		super(data);
	}
}

export interface SupportedCurrenciesRelations {
	// describe navigational properties here
}

export type SupportedCurrenciesWithRelations = SupportedCurrencies & SupportedCurrenciesRelations;
