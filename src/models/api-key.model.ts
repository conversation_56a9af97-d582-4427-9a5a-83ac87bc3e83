import {Entity, model, property} from '@loopback/repository';

@model()
export class <PERSON><PERSON><PERSON>ey extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
    required: true,
    index: {unique: true},
  })
  key: string;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'date',
    required: true,
  })
  createdAt: Date;

  @property({
    type: 'date',
  })
  lastUsedAt?: Date;

  @property({
    type: 'array',
    itemType: 'string',
  })
  scopes?: string[];

  constructor(data?: Partial<ApiKey>) {
    super(data);
  }
}
