import {Model, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class TokenTransferSize extends Model {
  @property({
    type: 'string',
    id: true,
    generated: false,
    required: true,
  })
  address: string;

  @property({
    type: 'string',
    required: true,
  })
  network: string;


  constructor(data?: Partial<TokenTransferSize>) {
    super(data);
  }
}

export interface TokenTransferSizeRelations {
  // describe navigational properties here
}

export type TokenTransferSizeWithRelations = TokenTransferSize & TokenTransferSizeRelations;
