import { Content } from "../models";

const popupId = 'raleon-popup';

export const renderTemplate = (
	content: Content,
	applyClickActions: boolean = true
): string => {
	if (!content) {
		return '';
	}

	const css = content?.css as any || {};
	let newContent: any = addDefaultStyles(content);
	const containerStyles = newContent.css!.container || '';
	const headerStyles = newContent.css.header || '';
	const headerImageStyles = newContent.css.headerImage || '';
	const messageStyles = newContent.css.message || '';
	const buttonStyles = newContent.css.button || '';
	const closeButtonStyles = newContent.css.closeMessage || '';

	return `
		<div id="${popupId}" style="${transformStyles(containerStyles)}">

			${content.header ? `
				<h3 id="${popupId}-header" style="${transformStyles(headerStyles)}">
					${content.header}
				</h3>` : ''
			}

			${content.headerImageUrl ? `
				<img src="${content.headerImageUrl}" style="${transformStyles(headerImageStyles)}" />`
				: ''
			}

			${content.message ? `
				<p id="${popupId}-message" style="${transformStyles(messageStyles)}">
					${content.message}
				</p>` : ''
			}

			${content.buttonText ? `
				<button
					id="${popupId}-button"
					style="${transformStyles(buttonStyles)}"
					onclick="${content.buttonUrl ? `window.open('${content.buttonUrl}', '_blank')` : ''}"
				>
					${content.buttonText}
				</button>` : ''
			}

			${content.closeMessage ? `
				<div style="text-align: center;">
					<a id="${popupId}-close"
						style="${transformStyles(closeButtonStyles)}"
						${applyClickActions ? `onclick="document.getElementById('${popupId}').remove()` : ''} ">
						${content.closeMessage}
					</a>
				</div> ` : ''
			}
		</div>
	`;
}

function transformStyles(styles: any): string {
	if (!styles) return '';
	return Object.keys(styles)
		.map((key: string) => `${key}: ${styles[key]}`)
		.join('; ');
}

function addDefaultStyles(content: Content) {
	if (!content.css) content.css = {};

	if (!(content.css as any).container) {
		(content.css as any).container = {
			"display": "flex",
			"flex-direction": "column",
			"font-family": "Trebuchet MS",
			"align-items": "center",
			"gap": "0.5rem",
			"border-radius": "30px",
			"padding": "16px",
			"width": "fit-content",
			"max-width": "380px",
			"height": "fit-content",
			"maxHeight": "300px",
			"z-index": "999",
			"position": "fixed",
			"bottom": "1em",
			"left": "3em",
			"background-color": "rgb(30,19,50)",
			"background-image": "radial-gradient(rgba(255, 255, 255, 0.05) 10%, transparent 20%)",
			"background-position": "0 0, 50px 50px",
			"background-size": "20px 20px"
		};
	}

	if (!(content.css as any).header) {
		(content.css as any).header = {
			// "color": "#FFF",
			"font-size": "18px",
			"font-weight": "600"
		};
	}

	if (!(content.css as any).headerImage) {
		(content.css as any).headerImage = {
			"text-align": "center",
			// "color": "#FFF",
			"margin-bottom": "0",
			"font-size": "18px",
			"max-height": "9em",
		};
	}

	if (!(content.css as any).message) {
		(content.css as any).message = {
			"text-align": "center",
			"font-size": "15px",
			// "color": "#FFF",
			"font-weight": "400",
			"margin-bottom": "0.5em"
		};
	}

	if (!(content.css as any).button) {
		(content.css as any).button = {
			"min-width": "100%",
			// "color": "white",
			"font-size": "18px",
			"border-radius": "0.3em",
			"border": "none",
			"height": "48px",
			"background": "linear-gradient(261.82deg, rgb(90, 22, 201) -5.43%, rgb(42, 38, 63) 109.81%)",
			"cursor": "pointer"
		};
	}

	if (!(content.css as any).closeMessage) {
		(content.css as any).closeMessage = {
			// "color": "#554EC3",
			"font-size": "14px",
			"cursor": "pointer",
			"text-align": "center"
		};
	}
	return content;
}
