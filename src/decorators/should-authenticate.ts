import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';

export function shouldAuthenticate(options: any, httpMethod: string) {
	if (options.hasOwnProperty('unauthenticatedMethods') && options.unauthenticatedMethods !== '{}') {
		const unauthenticatedMethods: any = Object
			.keys(options.unauthenticatedMethods)
			.reduce((prev: any, curr: any) => {
				prev[curr.toLowerCase()] = options.unauthenticatedMethods[curr];
				return prev;
			}, {});

		return function (target: any, fnName: string, descriptor: PropertyDescriptor) {
			const containsMethod = unauthenticatedMethods.hasOwnProperty(httpMethod.toLowerCase())
			const match = containsMethod && unauthenticatedMethods[httpMethod.toLowerCase()].includes(fnName);
			if (match) {
				authenticate.skip()(target, fnName, descriptor);
				authorize.skip()(target, fnName, descriptor);
			}
		}
	}
	return function (t: any, p: string, d: PropertyDescriptor) {};
}
