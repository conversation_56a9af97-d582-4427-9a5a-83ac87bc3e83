import * as aws4 from 'aws4';
import * as AWS from 'aws-sdk';

const DATA_API = 'bcie42aco8.execute-api.us-east-1.amazonaws.com'
const UTM_API = 'j0v36abmdj.execute-api.us-east-1.amazonaws.com'

const AWS_ACCESS_KEY = process.env.API_ACCESS_KEY;
const AWS_SECRET_KEY = process.env.API_SECRET_KEY;

export function getClusterPersona(clusterId: number) {
	switch (clusterId) {
		case 0:
			return 'Web3 Casual'
		case 1:
			return 'Trader'
		case 2:
			return 'NFT Expert'
		case 3:
			return 'Web3 Power User'
		default:
			return 'Web3 Casual';
	}
}

export function getClusterPersonaEmoji(persona: number) {
	switch (persona) {
		case 0:
			return '🤠 '
		case 1:
			return '😁 '
		case 2:
			return '🤑 '
		case 3:
			return '🫡 '
		default:
			return '🤠 ';
	}
}

export function getURL(path: string, method: string, body?: any, host?: string) {
	const opts = {
		host: host || DATA_API,
		path: path,
		region: 'us-east-1',
		service: 'execute-api',
		mode: 'cors',
		body: body != undefined ? JSON.stringify(body) : undefined,
		headers: {
			'Content-Type': 'application/json',
		},
		method: method
	}
	return aws4.sign(opts, {accessKeyId: AWS_ACCESS_KEY, secretAccessKey: AWS_SECRET_KEY});
}

export function formatNumberWithCommas(num: number): string {
	return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

/**
 * Capitalizes the first letter of a string and converts the rest to lowercase
 * @param str The string to format
 * @returns The formatted string with first letter capitalized and rest lowercase
 */
export function capitalizeFirstLetter(str: string): string {
        if (!str) return '';
        return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

export function sanitizeString(value: string): string {
  if (!value) return '';
  return value
    .replace(/<script.*?>.*?<\/script>/gi, '')
    .replace(/<[^>]*>/g, '')
    .trim();
}

export function sanitizeFields<T extends object>(
  obj: T,
  fields: string[],
): void {
  for (const field of fields) {
    if (field in obj) {
      const val = (obj as any)[field];
      if (typeof val === 'string') {
        (obj as any)[field] = sanitizeString(val);
      }
    }
  }
}
