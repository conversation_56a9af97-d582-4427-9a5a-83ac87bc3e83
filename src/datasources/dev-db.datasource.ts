import {inject, lifeCycleObserver, LifeCycleObserver} from '@loopback/core';
import {juggler} from '@loopback/repository';

const config = {
  name: 'dev_db',
  connector: 'postgresql',
  host: 'raleon-webapp-development.ctskkm7eoqqi.us-east-1.rds.amazonaws.com',
  port: 5432,
  user: 'postgres',
  password: '%9w)6pKbjTfZA4u&',
  database: process.env.DATABASE_NAME || 'raleon-test',
  // Add connection pool configuration to prevent exhaustion
  max: process.env.NODE_ENV === 'production' ? 100 : 15, // Max connections in pool
  min: process.env.NODE_ENV === 'production' ? 5 : 2,   // Min connections in pool
  acquire: 60000,     // Max time to get connection (60s)
  idle: 10000,        // Max time connection can be idle (10s)
  evict: 1000,        // Check for idle connections every 1s
  handleDisconnects: true,
  // Add query timeout to prevent hanging queries
  requestTimeout: 30000, // 30 second query timeout
  // Connection validation
  validate: true,
  // Retry configuration
  retry: {
    max: 3,
    timeout: 5000,
    interval: 1000,
    backoff: 'exponential'
  }
};

// Observe application's life cycle to disconnect the datasource when
// application is stopped. This allows the application to be shut down
// gracefully. The `stop()` method is inherited from `juggler.DataSource`.
// Learn more at https://loopback.io/doc/en/lb4/Life-cycle.html
@lifeCycleObserver('datasource')
export class DevDbDataSource extends juggler.DataSource
  implements LifeCycleObserver {
  static dataSourceName = 'dev_db';
  static readonly defaultConfig = config;

  constructor(
    @inject('datasources.config.dev_db', {optional: true})
    dsConfig: object = config,
  ) {
    super(dsConfig);
  }
}
