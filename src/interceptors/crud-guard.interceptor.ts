import {User} from '@loopback/authentication-jwt';
import {Binding, Context, ControllerClass, CoreBindings, globalInterceptor, Interceptor, InvocationContext, InvocationResult, MetadataInspector, ParameterDecoratorFactory, Provider, ValueOrPromise} from '@loopback/core';
import {Where, Entity, EntityCrudRepository, Condition, Inclusion, FilterExcludingWhere, MODEL_PROPERTIES_KEY, Count, EntityNotFoundError, repository} from '@loopback/repository';
import {SecurityBindings} from '@loopback/security';
import {RaleonWebappApplication} from '../application';
import {RaleonUserIdentityRepository, RaleonUserRepository, UserRepository} from '../repositories';

import {ClassDecoratorFactory, MethodDecoratorFactory} from '@loopback/metadata';
import {RestBindings, RequestContext, ResponseModelOrSpec} from '@loopback/rest';
import {globalBindingRequestRepositoryClasses} from '../global-binding-request-container';
import {AUTHENTICATION_METADATA_CLASS_KEY, AUTHENTICATION_METADATA_KEY} from '@loopback/authentication';

export interface GuardOutputMetadata {
	plural: boolean;
	filterOnly?: boolean;
}

@globalInterceptor('auth', {tags: {name: 'crud-guard'}})
export class CrudGuardInterceptor implements Provider<Interceptor> {
	constructor(
		@repository(RaleonUserIdentityRepository) public raleonUserIdentityRepository: RaleonUserIdentityRepository,
	) {}
  value() {
    return this.intercept.bind(this);
  }

  static async checkControllerConfig(controllerClass: any, methodName: string, context: Context): Promise<void> {
	if (methodName === 'constructor') {
		return;
	}

	if ('OPENAPI_FORM' in controllerClass) {
		return;
	}

	// const guardClassSkipMetadata = MetadataInspector.getClassMetadata<object>(
	// 	'guard-skip-class',
	// 	controllerClass
	// );

	// if (guardClassSkipMetadata) {
	// 	return;
	// }

	const guard = MetadataInspector.getClassMetadata<GuardStrategy<any, any>>(
		'guard-config',
		controllerClass
	);

	if (!guard) {
		return GuardStrategy.configWasInvalid(controllerClass, '');
	}

	await guard.performConfigChecks(controllerClass, methodName, context);
  }

  async intercept(
    invocationCtx: InvocationContext,
    next: () => ValueOrPromise<InvocationResult>,
  ) {
    try {
		await this.handleRequest(invocationCtx);
	} catch (e) {
		if ('code' in e && e.code == 'ENTITY_NOT_FOUND') {
			console.warn('guard target not found, check guard config as it could be a config issue rather than a real 404, returning 404 to client');
			const reqCtx = await invocationCtx.get(RestBindings.Http.CONTEXT) as RequestContext;
			const res = reqCtx.response;
			res.statusCode = 404;
			res.setHeader('Content-Type', 'application/json; charset=utf-8');
			res.end('', 'utf-8');

			return;
		}
		if (e instanceof NotFoundError || e instanceof GuardMismatchError) {
			const reqCtx = await invocationCtx.get(RestBindings.Http.CONTEXT) as RequestContext;
			const res = reqCtx.response;
			res.statusCode = e.statusCode;
			res.setHeader('Content-Type', 'application/json; charset=utf-8');
			res.end('', 'utf-8');

			return;
		} else {
			const reqCtx = await invocationCtx.get(RestBindings.Http.CONTEXT) as RequestContext;
			const res = reqCtx.response;
			res.statusCode = 500;
			res.setHeader('Content-Type', 'application/json; charset=utf-8');
			res.end('', 'utf-8');

			return;
		}
	}

	const result = await next();

    try {
		return await this.handleResponse(invocationCtx, result);
	} catch (e) {
		if ('code' in e && e.code == 'ENTITY_NOT_FOUND') {
			console.warn('guard target not found, check guard config as it could be a config issue rather than a real 404, returning 404 to client');
			const reqCtx = await invocationCtx.get(RestBindings.Http.CONTEXT) as RequestContext;
			const res = reqCtx.response;
			res.statusCode = 404;
			res.setHeader('Content-Type', 'application/json; charset=utf-8');
			res.end('', 'utf-8');

			return;
		}
		if (e instanceof NotFoundError || e instanceof GuardMismatchError) {
			const reqCtx = await invocationCtx.get(RestBindings.Http.CONTEXT) as RequestContext;
			const res = reqCtx.response;
			res.statusCode = e.statusCode;
			res.setHeader('Content-Type', 'application/json; charset=utf-8');
			res.end('', 'utf-8');

			return;
		} else {
			const reqCtx = await invocationCtx.get(RestBindings.Http.CONTEXT) as RequestContext;
			const res = reqCtx.response;
			res.statusCode = 500;
			res.setHeader('Content-Type', 'application/json; charset=utf-8');
			res.end('', 'utf-8');

			return;
		}
	}

	return result;
  }

  private async handleRequest(invocationCtx: InvocationContext): Promise<void> {
	const controllerClass = await invocationCtx.get(CoreBindings.CONTROLLER_CLASS);
	if ('OPENAPI_FORM' in controllerClass) {
		return;
	}

	const guard = MetadataInspector.getClassMetadata<GuardStrategy<any, any>>(
		'guard-config',
		controllerClass
	);

	if (!guard) {
		return GuardStrategy.configWasInvalid(controllerClass, '');
	}

	await guard.performConfigChecks(controllerClass, invocationCtx.methodName, invocationCtx);

	await this.applyUserSugars(invocationCtx, controllerClass);
	await this.injectFilters(invocationCtx, controllerClass, guard);


	const guardSkipMetadata = MetadataInspector.getMethodMetadata<object>(
		'guard-skip',
		controllerClass.prototype,
		invocationCtx.methodName
	);
	const guardReadMetadata = MetadataInspector.getMethodMetadata<GuardOutputMetadata>(
		'guard-reads',
		controllerClass.prototype,
		invocationCtx.methodName
	);
	const guardAutoCountMetadata = MetadataInspector.getMethodMetadata<GuardOutputMetadata>(
		'guard-auto-count',
		controllerClass.prototype,
		invocationCtx.methodName
	);


	if (guardSkipMetadata) {
		return;
	}

	const modelArrayIndexDecorations =
		MetadataInspector.getAllParameterMetadata<EntityClass<any>>(
			'guard-model-array',
			controllerClass.prototype,
			invocationCtx.methodName
	);
	if (modelArrayIndexDecorations && modelArrayIndexDecorations.some(x => x)) {
		throw new Error(`@modelArrayForGuard() is not supported yet

		*** GUARD CONFIG ERROR *** - ${controllerClass.name}.${invocationCtx.methodName}
		`);
	}
	const modelArrayIndex = modelArrayIndexDecorations?.map((x,i) => x ? i : undefined)?.find(x => Number.isFinite(x)) as number;
	const modelArrayType = modelArrayIndexDecorations?.find(x => x);
	const modelArray = invocationCtx.args[modelArrayIndex];

	const modelIndexDecorations =
		MetadataInspector.getAllParameterMetadata<EntityClass<any>>(
			'guard-model',
			controllerClass.prototype,
			invocationCtx.methodName
		);


	const modelIndex = modelIndexDecorations?.map((x,i) => x ? i : undefined)?.find(x => Number.isFinite(x)) as number;
	const modelType = modelIndexDecorations?.find(x => x);
	const model = invocationCtx.args[modelIndex];
	if (model && typeof model !== 'object') {
		throw new Error(`Parameter decorated with @modelForGuard() was not an object at runtime

		Check decorator position, and potentially move non-guard decorated parameters after the @modelForGuard() parameter.

		*** GUARD CONFIG ERROR *** - ${controllerClass.name}.${invocationCtx.methodName}
		`);
	}

	const modelIdIndexDecorations =
		MetadataInspector.getAllParameterMetadata<EntityClass<any>>(
			'guard-model-id',
			controllerClass.prototype,
			invocationCtx.methodName
		);

	const modelIdIndex = modelIdIndexDecorations?.map((x,i) => x ? i : undefined)?.find(x => Number.isFinite(x)) as number
	const modelId = invocationCtx.args[modelIdIndex];
	if (modelId && typeof modelId !== 'string' && typeof modelId !== 'number') {
		throw new Error(`Parameter decorated with @modelIdForGuard() was not an id at runtime

		Check decorator position, and potentially move non-guard decorated parameters after the @modelIdForGuard() parameter.

		*** GUARD CONFIG ERROR *** - ${controllerClass.name}.${invocationCtx.methodName}
		`);
	}
	const modelIdType = modelIdIndexDecorations?.find(x => x);

	if (guardReadMetadata || guardAutoCountMetadata) {
		return;
	}

	await guard.assertMutationAllowed(model, modelType!, modelId, modelIdType!, invocationCtx, true);
  }

  private async applyUserSugars(invocationCtx: InvocationContext, controllerClass: ControllerClass<any>): Promise<void> {
	const appendOrgIdToParams =
	MetadataInspector.getAllParameterMetadata<object>(
		'guard-inject-org-id',
		controllerClass.prototype,
		invocationCtx.methodName
	);

	const appendRaleonUserOrgIdToParams =
	MetadataInspector.getAllParameterMetadata<object>(
		'guard-inject-raleon-user-org-id',
		controllerClass.prototype,
		invocationCtx.methodName
	);

	const appendUserIdToParams =
	MetadataInspector.getAllParameterMetadata<object>(
		'guard-inject-user-id',
		controllerClass.prototype,
		invocationCtx.methodName
	);

	const appendRaleonUserIdToParams =
	MetadataInspector.getAllParameterMetadata<object>(
		'guard-inject-raleon-user-id',
		controllerClass.prototype,
		invocationCtx.methodName
	);

	const authenticateDecoration = MetadataInspector.getMethodMetadata<any>(
		AUTHENTICATION_METADATA_KEY,
		controllerClass.prototype,
		invocationCtx.methodName
	) || MetadataInspector.getClassMetadata<any>(
		AUTHENTICATION_METADATA_CLASS_KEY,
		controllerClass
	);

	if (appendOrgIdToParams || appendUserIdToParams || appendRaleonUserOrgIdToParams || appendRaleonUserIdToParams) {
		if (!authenticateDecoration) {
			throw new Error(`Controller method with guarding requested is not decorated with @authenticate()

				- In order for user id / user org id sugars to work, the user must be logged in, in order to determine their id/orgid.
				- Please either:
				  - Decorate the method with @authenticate()
				  - Remove @injectUserId() and @injectUserOrgId()

			*** SUGAR CONFIG ERROR *** - ${controllerClass.name}.${invocationCtx.methodName}
			`);
		}

		if (appendOrgIdToParams) {
			for (let i = 0; i < appendOrgIdToParams.length; i++) {
				if (!appendOrgIdToParams[i]) {
					continue;
				}

				invocationCtx.args[i] = await GuardStrategy.prototype.getUserOrgId(invocationCtx);
			}
		}

		if (appendRaleonUserOrgIdToParams) {
			for (let i = 0; i < appendRaleonUserOrgIdToParams.length; i++) {
				if (!appendRaleonUserOrgIdToParams[i]) {
					continue;
				}

				invocationCtx.args[i] = await GuardStrategy.prototype.getRaleonUserOrgId(invocationCtx);
			}
		}

		if (appendUserIdToParams) {
			for (let i = 0; i < appendUserIdToParams.length; i++) {
				if (!appendUserIdToParams[i]) {
					continue;
				}

				invocationCtx.args[i] = await GuardStrategy.prototype.getUserId(invocationCtx);
			}
		}

		if (appendRaleonUserIdToParams) {
			for (let i = 0; i < appendRaleonUserIdToParams.length; i++) {
				if (!appendRaleonUserIdToParams[i]) {
					continue;
				}
				invocationCtx.args[i] = await GuardStrategy.prototype.getRaleonUserId(invocationCtx);
			}
		}
	}
  }

  private async injectFilters(invocationCtx: InvocationContext, controllerClass: ControllerClass<any>, guard: GuardStrategy<any>): Promise<void> {
	const appendGuardedFilterToParams =
		MetadataInspector.getAllParameterMetadata<object>(
			'guard-inject-guarded-filter',
			controllerClass.prototype,
			invocationCtx.methodName
		);
	const appendGuardedWhereToParams =
		MetadataInspector.getAllParameterMetadata<object>(
			'guard-inject-guarded-where',
			controllerClass.prototype,
			invocationCtx.methodName
		);


	if (appendGuardedFilterToParams) {
		for (let i = 0; i < appendGuardedFilterToParams?.length; i++) {
			if (!appendGuardedFilterToParams[i]) {
				continue;
			}

			if (!invocationCtx.args[i]) {
				invocationCtx.args[i] = {};
			}

			const filter = invocationCtx.args[i];
			if (filter && typeof filter !== 'object') {
				console.warn('Filter param decorated with injectGuardedFilter() was not a filter');
				continue;
			}

			if (!filter.where) {
				filter.where = {};
			}

			filter.where = {
				and: [
					filter.where,
					await guard.getReadFilter(invocationCtx)
				]
			};
		}
	}
	if (appendGuardedWhereToParams) {
		for (let i = 0; i < appendGuardedWhereToParams?.length; i++) {
			if (!appendGuardedWhereToParams[i]) {
				continue;
			}

			if (!invocationCtx.args[i]) {
				invocationCtx.args[i] = {};
			}

			const where = invocationCtx.args[i];
			if (where && typeof where !== 'object') {
				console.warn('Where param decorated with injectGuardedWhere() was not a Where');
				continue;
			}

			invocationCtx.args[i] = {
				and: [
					where,
					await guard.getReadFilter(invocationCtx)
				]
			};
		}
	}

  }

  private async handleResponse(invocationCtx: InvocationContext, result: any): Promise<any> {
	const controllerClass = await invocationCtx.get(CoreBindings.CONTROLLER_CLASS);
	if ('OPENAPI_FORM' in controllerClass) {
		return result;
	}

	const guardClassSkipMetadata = MetadataInspector.getClassMetadata<object>(
		'guard-skip-class',
		controllerClass
	);
	const guardSkipMetadata = MetadataInspector.getMethodMetadata<object>(
		'guard-skip',
		controllerClass.prototype,
		invocationCtx.methodName
	);

	if (guardClassSkipMetadata || guardSkipMetadata) {
		return result;
	}

	const guard = MetadataInspector.getClassMetadata<GuardStrategy<any, any>>(
		'guard-config',
		controllerClass
	);

	if (!guard) {
		return GuardStrategy.configWasInvalid(controllerClass, invocationCtx.methodName);
	}

	const guardReadMetadata = MetadataInspector.getMethodMetadata<GuardOutputMetadata>(
		'guard-reads',
		controllerClass.prototype,
		invocationCtx.methodName
	);
	const guardAutoCountMetadata = MetadataInspector.getMethodMetadata<GuardOutputMetadata>(
		'guard-auto-count',
		controllerClass.prototype,
		invocationCtx.methodName
	);

	if (guardReadMetadata && !guardReadMetadata.filterOnly || guardAutoCountMetadata) {
		if (guardReadMetadata?.plural || guardAutoCountMetadata) {
			const all = result as Array<Entity>;;
			const allowed = await Promise.all(all.map(x => guard.shouldAllowRead(x, invocationCtx).then(y => y ? x : undefined)));
			const filtered = allowed.filter(x => x);

			if (guardAutoCountMetadata) {
				return { count: filtered.length } as Count;
			}

			return filtered;

		} else {
			await guard.assertReadAllowed(result, invocationCtx);
		}
	}

	return result;
  }
}

export function skipGuardCheck(spec: object = {}): MethodDecorator {
	return MethodDecoratorFactory.createDecorator<object>(
		'guard-skip',
		spec,
	);
}

export function guardStrategy(spec: GuardStrategy<any, any>): ClassDecorator {
	return ClassDecoratorFactory.createDecorator<GuardStrategy<any, any>>(
		'guard-config',
		spec,
	);
}

export function modelForGuard<Z extends Entity>(spec: EntityClass<Z>): ParameterDecorator {
	return ParameterDecoratorFactory.createDecorator<EntityClass<Z>>(
		'guard-model',
		spec
	);
}

export function modelArrayForGuard<Z extends Entity>(spec: EntityClass<Z>): ParameterDecorator {
	return ParameterDecoratorFactory.createDecorator<EntityClass<Z>>(
		'guard-model-array',
		spec
	);
}

export function modelIdForGuard<Z extends Entity>(spec: EntityClass<Z>): ParameterDecorator {
	return ParameterDecoratorFactory.createDecorator<EntityClass<Z>>(
		'guard-model-id',
		spec
	);
}

export function restrictReadsWithGuard(spec: GuardOutputMetadata): MethodDecorator {
	return MethodDecoratorFactory.createDecorator<GuardOutputMetadata>(
		'guard-reads',
		spec,
	);
}

export function guardAutoCount(spec: object = {}): MethodDecorator {
	return MethodDecoratorFactory.createDecorator<object>(
		'guard-auto-count',
		spec,
	);
}
export function injectGuardedWhere(spec: object = {}): ParameterDecorator {
	return ParameterDecoratorFactory.createDecorator<object>(
		'guard-inject-guarded-where',
		spec
	);
}

export function injectGuardedFilter(spec: object = {}): ParameterDecorator {
	return ParameterDecoratorFactory.createDecorator<object>(
		'guard-inject-guarded-filter',
		spec
	);
}

export function injectUserOrgId(spec: object = {}): ParameterDecorator {
	return ParameterDecoratorFactory.createDecorator<object>(
		'guard-inject-org-id',
		spec
	);
}

export function injectRaleonUserOrgId(spec: object = {}): ParameterDecorator {
	return ParameterDecoratorFactory.createDecorator<object>(
		'guard-inject-raleon-user-org-id',
		spec
	);
}

export function injectUserId(spec: object = {}): ParameterDecorator {
	return ParameterDecoratorFactory.createDecorator<object>(
		'guard-inject-user-id',
		spec
	);
}

export function injectRaleonUserId(spec: object = {}): ParameterDecorator {
	return ParameterDecoratorFactory.createDecorator<object>(
		'guard-inject-raleon-user-id',
		spec
	);
}

class NotFoundError extends Error {
	statusCode: number

	constructor(message: string) {
	  super(message)
	  this.statusCode = 404;
	}
  }

class GuardMismatchError extends Error {
	statusCode: number

	constructor(message: string) {
		super(message)
		this.statusCode = 403;
	}
}

type RepoClass<T extends Entity, IdName extends keyof T> = { new(...args: any[]): EntityCrudRepository<T, IdName, any> };
type EntityClass<T extends Entity> = { new(...args: any[]): T };

export abstract class GuardStrategy<T extends Entity, IdName extends keyof T = any> {
	private userRepository: UserRepository;
	private raleonUserRepository: RaleonUserRepository
	private raleonUserIdentityRepository: RaleonUserIdentityRepository
	protected repositoryCache = new Map<RepoClass<any, any>, EntityCrudRepository<any, any, any>>();

	protected async requestRepositoryBinding(repoClass: RepoClass<T, IdName>): Promise<void> {
		if (!repoClass) {
			return;
		}

		globalBindingRequestRepositoryClasses.add(repoClass);
	}

	protected static async getRepositoryBinding(invocationCtx: Context, repoClass: RepoClass<any, any>): Promise<Binding> {
		const app = await invocationCtx.get(CoreBindings.APPLICATION_INSTANCE) as RaleonWebappApplication;
		return app.repositoryBindingRegistry.get(repoClass);
	}

	constructor(
		protected repositoryClass: RepoClass<T, IdName>,
	) {
		this.requestRepositoryBinding(repositoryClass);
	}

	protected static async getRepositoryInstance<C extends EntityCrudRepository<any, any, any>>(
		context: Context,
		cl: { new(...args: any[]): C }
	): Promise<C> {
		const binding = await this.getRepositoryBinding(context, cl);
		if (!binding){
			throw new Error('Missing orgIdGuard repository binding');
		}

		return await context.get(binding.key) as C;
	}

	async getUser(context: Context): Promise<User> {
		try {
			return context.get(SecurityBindings.USER) as any as User;
		} catch (e) {
			throw new GuardMismatchError('User could not be determined, ensure controller method is decorated with @authenticate')
		}
	}
	async getUserId(context: Context): Promise<number> {
		try {
			const user = await this.getUser(context);

			return Number(user.id)
		} catch (e) {
			throw new GuardMismatchError('User id could not be determined, ensure controller method is decorated with @authenticate')
		}
	}
	async getUserOrgId(context: Context): Promise<number> {
		const instance = this.userRepository || await GuardStrategy.getRepositoryInstance(context, UserRepository);
		if (!this.userRepository) {
			this.userRepository = instance;
		}

		try {
			const user = await this.getUser(context);

			return instance.findOrganization(user);
		} catch (e) {
			throw new GuardMismatchError('User org id could not be determined, ensure controller method is decorated with @authenticate')
		}
	}

	async getRaleonUserOrgId(context: Context): Promise<number | undefined> {
		const repo = await context.get<RaleonUserIdentityRepository>('repositories.RaleonUserIdentityRepository');
		let currentUser = await context.get(SecurityBindings.USER) as any;
		let symbols = Object.getOwnPropertySymbols(currentUser);
		let securityId = currentUser[symbols[0]];
		let user = await repo.findOne(
			{
				where: {
					identityValue: securityId,
					identityType: 'customer_id'
				},
				include: ['raleonUser']
			}
		)

		if(!user) {
			return -1;
		}

		return user.orgId;
	}

	async getRaleonUserId(context: Context): Promise<number | null> {
		const repo = await context.get<RaleonUserIdentityRepository>('repositories.RaleonUserIdentityRepository');
		let currentUser = await context.get(SecurityBindings.USER) as any;
		let symbols = Object.getOwnPropertySymbols(currentUser);
		let securityId = currentUser[symbols[0]];
		let user = await repo.findOne({where: {
			identityValue: securityId,
			identityType: 'customer_id'
		}});

		if(!user) {
			return -1;
		}
		return user.raleonUserId;
	}

	async performConfigChecks(controllerClass: any, methodName: string, context: Context): Promise<void> {

		const appendOrgIdToParams =
		MetadataInspector.getAllParameterMetadata<object>(
			'guard-inject-org-id',
			controllerClass.prototype,
			methodName
		);

		const appendRaleonUserOrgIdToParams =
		MetadataInspector.getAllParameterMetadata<object>(
			'guard-inject-raleon-user-org-id',
			controllerClass.prototype,
			methodName
		);

		const appendUserIdToParams =
		MetadataInspector.getAllParameterMetadata<object>(
			'guard-inject-user-id',
			controllerClass.prototype,
			methodName
		);

		const appendRaleonUserIdToParams =
		MetadataInspector.getAllParameterMetadata<object>(
			'guard-inject-raleon-user-id',
			controllerClass.prototype,
			methodName
		);

		const authenticateDecoration = MetadataInspector.getMethodMetadata<any>(
			AUTHENTICATION_METADATA_KEY,
			controllerClass.prototype,
			methodName
		) || MetadataInspector.getClassMetadata<any>(
			AUTHENTICATION_METADATA_CLASS_KEY,
			controllerClass
		);

		if (appendOrgIdToParams || appendUserIdToParams || appendRaleonUserOrgIdToParams || appendRaleonUserIdToParams) {
			if (!authenticateDecoration) {
				throw new Error(`Controller method with guarding requested is not decorated with @authenticate()

					- In order for guarding to work, the user must be logged in, in order to determine their orgid.
					- Please either:
					  - Decorate the method with @authenticate()
					  - Decorate the method with @skipGuardCheck()
						- (and remove @injectGuardedWhere, @injectGuardedFilter, @modelForGuard, @modelArrayForGuard, @modelIdForGuard)

				*** GUARD CONFIG ERROR *** - ${controllerClass.name}.${methodName}
				`);
			}
		}

		const guardSkipMetadata = MetadataInspector.getMethodMetadata<object>(
			'guard-skip',
			controllerClass.prototype,
			methodName
		);
		const guardReadMetadata = MetadataInspector.getMethodMetadata<GuardOutputMetadata>(
			'guard-reads',
			controllerClass.prototype,
			methodName
		);
		const guardAutoCountMetadata = MetadataInspector.getMethodMetadata<GuardOutputMetadata>(
			'guard-auto-count',
			controllerClass.prototype,
			methodName
		);

		const modelArrayIndexDecorations =
			MetadataInspector.getAllParameterMetadata<object>(
				'guard-model-array',
				controllerClass.prototype,
				methodName
		);
		const modelIndexDecorations =
			MetadataInspector.getAllParameterMetadata<object>(
				'guard-model',
				controllerClass.prototype,
				methodName
			);
		const modelIdIndexDecorations =
			MetadataInspector.getAllParameterMetadata<object>(
				'guard-model-id',
				controllerClass.prototype,
				methodName
			);
		const appendWhereToParams =
			MetadataInspector.getAllParameterMetadata<object>(
				'guard-inject-guarded-where',
				controllerClass.prototype,
				methodName
			);

		const appendFilterToParams =
			MetadataInspector.getAllParameterMetadata<object>(
				'guard-inject-guarded-filter',
				controllerClass.prototype,
				methodName
			);

		if (guardSkipMetadata) {
			if (modelArrayIndexDecorations || modelIndexDecorations || modelIdIndexDecorations || appendWhereToParams || appendFilterToParams) {
				throw new Error(`Controller method with guarding skipped is also decorated with guarding decorators

					Please either:
					- Remove @skipGuardCheck(), and ensure the other appropriate guarding decorators are properly applied
					- Remove all guarding decorators

					(guarding decorators are @injectGuardedWhere, @injectGuardedFilter, @modelForGuard, @modelArrayForGuard, @modelIdForGuard)

				*** GUARD CONFIG ERROR *** - ${controllerClass.name}.${methodName}
				`);
			}

			return;
		}

		if (!authenticateDecoration) {
			throw new Error(`Controller method with guarding requested is not decorated with @authenticate()

				- In order for guarding to work, the user must be logged in, in order to determine their orgid.
				- Please either:
				  - Decorate the method with @authenticate()
				  - Decorate the method with @skipGuardCheck()
					- (and remove @injectGuardedWhere, @injectGuardedFilter, @modelForGuard, @modelArrayForGuard, @modelIdForGuard)

			*** GUARD CONFIG ERROR *** - ${controllerClass.name}.${methodName}
			`);
		}

		if (modelArrayIndexDecorations!?.filter(x => x)?.length > 1) {
			throw new Error(`Only one argument can be decorated with @modelArrayForGuard()

			*** GUARD CONFIG ERROR *** - ${controllerClass.name}.${methodName}
			`);
		}

		if (modelArrayIndexDecorations && modelArrayIndexDecorations.some(x => x)) {
			throw new Error(`@modelArrayForGuard() is not supported yet

			*** GUARD CONFIG ERROR *** - ${controllerClass.name}.${methodName}
			`);
		}

		if (context && 'args' in context) {
			const invocationCtx = context as InvocationContext;
			const modelArrayIndex = modelArrayIndexDecorations?.map((x,i) => x ? i : undefined).find(x => Number.isFinite(x)) as number;
			const modelArray = invocationCtx.args[modelArrayIndex];
			if (modelArray && !Array.isArray(modelArray)) {
				throw new Error(`Argument decorated with @modelArrayForGuard() was not an Array

					@modelForGuard() should be used instead in cases where only a singular Model is being accepted/mutated

				*** GUARD CONFIG ERROR *** - ${controllerClass.name}.${methodName}
				`);
			}
		}

		if (modelIndexDecorations!?.filter(x => x)?.length > 1) {
			throw new Error(`Only one argument can be decorated with @modelForGuard()

			*** GUARD CONFIG ERROR *** - ${controllerClass.name}.${methodName}
			`);
		}
		if (context && 'args' in context) {
			const invocationCtx = context as InvocationContext;
			const modelIndex = modelIndexDecorations?.map((x,i) => x ? i : undefined).find(x => Number.isFinite(x)) as number;
			const model = invocationCtx.args[modelIndex];

			if (model && Array.isArray(model)) {
				throw new Error(`Argument decorated with the singular @modelForGuard() was plural / an Array

					@modelArrayForGuard() must be used instead

				*** GUARD CONFIG ERROR *** - ${controllerClass.name}.${invocationCtx.methodName}
				`)
			}

		}


		if (modelIdIndexDecorations!?.filter(x => x)?.length > 1) {
			throw new Error(`Only one argument can be decorated with @modelIdForGuard()

			*** GUARD CONFIG ERROR *** - ${controllerClass.name}.${methodName}
			`);
		}

		const modelIndex = modelIndexDecorations?.map((x,i) => x ? i : undefined).find(x => Number.isFinite(x)) as number;
		const modelType = modelIndexDecorations?.find(x => x) as EntityClass<any>;
		const modelIdIndex = modelIdIndexDecorations?.map((x,i) => x ? i : undefined).find(x => Number.isFinite(x)) as number
		const modelIdType = modelIdIndexDecorations?.find(x => x) as EntityClass<any>;
		if (!await this.checkModelTypes(context, modelType, modelIdType)) {
			throw new Error(`Model type mismatch for guarding

			- The payload type passed to @modelForGuard() or id type passed to @modelIdForGuard() did not match the guarding strategy
			- Ensure that the right payloads / ids are being decorated
			- Ensure that the type specified in the decorations matches the actual types being processed via the method args

		   *** GUARD CONFIG ERROR *** - ${controllerClass.name}.${methodName}`)
		}

		if (guardReadMetadata?.filterOnly && !appendFilterToParams && !appendWhereToParams) {
			throw new Error(`filterOnly: true was specified, but no filter was injected or used.

			 - Use @injectGuardedFilter() or @injectGuardedWhere on an existing or new method parameter
			 - Pass the filter/where to the repository / db call
			 - OR remove filterOnly: true from @restrictReadsWithGuard()

			*** GUARD CONFIG ERROR *** - ${controllerClass.name}.${methodName}
			`)
		}

		const responseArr = MetadataInspector.getMethodMetadata('openapi-v3:methods:response', controllerClass.prototype, methodName) as any;
		const response = responseArr?.[0] as ResponseModelOrSpec;
		if (response && ('content' in response || 'type' in response || 'responseModelOrSpec' in response)) {
			const isArrayReturnType = 'responseModelOrSpec' in response && response.responseModelOrSpec?.type === 'array' ||
				'content' in response && response.content['application/json']?.schema?.type === 'array' ||
				'type' in response && response.type === 'array';

			if (guardReadMetadata && guardReadMetadata.plural !== isArrayReturnType) {
				throw new Error(`@restrictReadsWithGuard() plural: true/false option did not match Response

				- @restrictReadsWithGuard({ plural: true }) should be used when the Controller method return type is an Array of Models
				- @restrictReadsWithGuard({ plural: false }) should be used when the Controller method return type is a singular Model

			   *** GUARD CONFIG ERROR *** - ${controllerClass.name}.${methodName}
			   `)
			}
		}

		if (
			!guardReadMetadata &&
			!guardAutoCountMetadata &&
			(!modelIndexDecorations || !modelIndexDecorations.filter(x => x).length) &&
			(!modelIdIndexDecorations || !modelIdIndexDecorations.filter(x => x).length)
		) {
			return GuardStrategy.configWasInvalid(controllerClass, methodName);
		}

	}

	static configWasInvalid(controllerClass: any, methodName: string) {
		if (!methodName) {
			throw new Error(`Missing Guard decorations on controller: ${controllerClass.name}

			The controller class *requires*:
				- @guardStrategy(new GuardStrategy<Model>())

			See https://stackoverflowteams.com/c/raleon/questions/13 for more info

		*** GUARD CONFIG ERROR *** - ${controllerClass.name}
		`);
		}
		throw new Error(`Missing Guard decorations on method: ${controllerClass.name}.${methodName}

				All controller methods *require* at least one of the following:

						- @skipGuardCheck()
						- @restrictReadsWithGuard()
						- @guardAutoCount() - COUNT query support see https://stackoverflowteams.com/c/raleon/questions/23
						- OR one or more of the following on method parameters:
						  - @modelForGuard() for creates and updates
						  - @modelIdForGuard() for updates and deletes
						  - @modelArrayForGuard()

				See https://stackoverflowteams.com/c/raleon/questions/13 for more info

			*** GUARD CONFIG ERROR *** - ${controllerClass.name}.${methodName}
			`);
	  }

	async checkModelTypes<Y extends Entity, Z extends Entity>(context: Context, dataType: EntityClass<Y>, idType: EntityClass<Z>, data?: Y, id?: Z): Promise<boolean> {
		const repo = await this.getRepository(context);
		if (dataType && dataType !== repo.entityClass as any) {
			return false
		}

		if (idType && idType !== repo.entityClass as any) {
			return false;
		}

		return true;
	}

	abstract getCurrentGuardId(invocationCtx: InvocationContext): Promise<number>;

	protected async getRepository(context: Context): Promise<EntityCrudRepository<T, any, any>> {
		return this.getRepositoryFromClass(this.repositoryClass, context);
	}

	protected async getRepositoryFromClass<Z extends Entity>(cls: RepoClass<Z, keyof Z>, context: Context): Promise<EntityCrudRepository<Z, any, any>> {
		const cached =  this.repositoryCache.get(cls);
		if (cached) {
			return cached;
		}

		const instance = await GuardStrategy.getRepositoryInstance(context, cls)
		if (!instance) {
			throw new Error('Missing repository instance');
		}

		this.repositoryCache.set(cls, instance);

		return instance;
	}

	setRepository(repository: EntityCrudRepository<T, any, any>): void {
		this.repositoryCache.set(this.repositoryClass, repository);
	}


	async shouldAllowMutation(data: T | Omit<T, IdName> | Partial<T> | undefined, dataType: EntityClass<any>, id: string | number | undefined, idType: EntityClass<any>, invocationCtx: InvocationContext): Promise<boolean> {
		if (!data) {
			if (id) {
				return this.shouldAllowOperationWithId(id, idType, invocationCtx);
			} else {
				throw new Error('Must have id parameter if no data is provided');
			}
		}

		if (!id && await this.hasNoId(data, invocationCtx)) {
			return this.shouldAllowOperationWithPayload(data as Omit<T, IdName>, dataType, invocationCtx);
		} else {
			return this.shouldAllowOperation(data as Partial<T>, dataType, id as number|string, idType, invocationCtx);
		}
	}

	protected abstract hasNoId(data: T | Partial<T> | Omit<T, IdName>, invocationCtx: InvocationContext): Promise<boolean>;
	abstract getReadFilter(invocationCtx: InvocationContext): Promise<Where<T | Omit<T, IdName>>>;
	abstract shouldAllowRead(data: T, invocationCtx: InvocationContext): Promise<boolean>;
	abstract shouldAllowOperation(data: Partial<T>, dataType: EntityClass<any>, id: string|number, idType: EntityClass<any>, invocationCtx: InvocationContext): Promise<boolean>;
	abstract shouldAllowOperationWithId(id: string|number, idType: EntityClass<any>, invocationCtx: InvocationContext): Promise<boolean>;
	abstract shouldAllowOperationWithPayload(data: T | Omit<T, IdName>, dataType: EntityClass<any>, invocationCtx: InvocationContext): Promise<boolean>;

	abstract correctPayload(data: Partial<T>, invocationCtx: InvocationContext): Promise<void>;

	async assertMutationAllowed(data: T | Partial<T> | Omit<T, IdName> | undefined, dataType: EntityClass<any>, id: number | string | undefined, idType: EntityClass<any>, invocationCtx: InvocationContext, populateOrgIdInPayload: boolean): Promise<void> {
		if (populateOrgIdInPayload) {
			await this.correctPayload(data as T, invocationCtx);
		}

		if (!await this.shouldAllowMutation(data, dataType, id, idType, invocationCtx)) {
			throw new GuardMismatchError('Unable to mutate data, organization mismatch');
		}
	}

	async assertReadAllowed(data: T, invocationCtx: InvocationContext): Promise<void> {
		if (!await this.shouldAllowRead(data, invocationCtx)) {
			console.error('Organization mismatch, throwing NotFoundError');
			throw new NotFoundError('Unable to read data, organization mismatch');
		}
	}
	async assertOperationAllowed(data: Partial<T>, dataType: EntityClass<any>, id: string|number, idType: EntityClass<any>, invocationCtx: InvocationContext, populateOrgIdInPayload: boolean): Promise<void> {
		if (data && populateOrgIdInPayload) {
			await this.correctPayload(data, invocationCtx);
		}

		if (!await this.shouldAllowOperation(data, dataType, id, idType, invocationCtx)) {
			throw new GuardMismatchError('Unable to operate on payload/id, organization mismatch');
		}
	}
	async assertIdOperationAllowed(id: string|number, idType: EntityClass<any>, invocationCtx: InvocationContext): Promise<void> {
		if (!await this.shouldAllowOperationWithId(id, idType, invocationCtx)) {
			throw new GuardMismatchError('Unable to operate on id, organization mismatch');
		}
	}
	async assertPayloadOperationAllowed(data: T | Omit<T, IdName>, dataType: EntityClass<any>, invocationCtx: InvocationContext, populateOrgIdInPayload: boolean): Promise<void> {
		if (populateOrgIdInPayload) {
			await this.correctPayload(data as T, invocationCtx);
		}

		if (!await this.shouldAllowOperationWithPayload(data, dataType, invocationCtx)) {
			throw new GuardMismatchError('Unable to create data, organization mismatch');
		}
	}

	protected async getModelIdPropertyName<Z extends Entity>(repoClass: RepoClass<Z, keyof Z>, invocationCtx: InvocationContext): Promise<keyof Z> {
		const repoInstance = await this.getRepositoryFromClass(repoClass, invocationCtx);

		return this.getModelIdPropertyNameFromRepoInstance(repoInstance);
	}

	protected getModelIdPropertyNameForPrimaryEntityCached(): keyof T {
		const cachedRepoInstance = this.repositoryCache.get(this.repositoryClass) as EntityCrudRepository<T, any, any>;

		return this.getModelIdPropertyNameFromRepoInstance(cachedRepoInstance);
	}

	protected getModelIdPropertyNameFromRepoInstance<Z extends Entity>(repoInstance: EntityCrudRepository<Z, any, any>): keyof Z {
		const entityClass = repoInstance.entityClass;
		const idPropertyKey = Object.getOwnPropertyNames(entityClass.prototype).find(x => {
			const metadata = MetadataInspector.getPropertyMetadata(MODEL_PROPERTIES_KEY, entityClass.prototype, x);
			if (metadata && metadata.id) {
				return true;
			}
		});

		return idPropertyKey as keyof Z;
	}
}

export class GuardSkipStrategy extends GuardStrategy<any, 'id'> {

	constructor() {
		super(undefined as any);
	}

	async shouldAllowOperationWithPayload(data: any, dataType: EntityClass<any>, invocationCtx: InvocationContext): Promise<boolean> { return false; }
	async getReadFilter(invocationCtx: InvocationContext): Promise<Where<any>> { return {}; }
	async shouldAllowRead(data: any, invocationCtx: InvocationContext): Promise<boolean> { return false; }
	async shouldAllowOperation(data: any, dataType: EntityClass<any>, id: string|number, idType: EntityClass<any>, invocationCtx: InvocationContext): Promise<boolean> { return false; }
	async shouldAllowOperationWithId(id: string|number, idType: EntityClass<any>, invocationCtx: InvocationContext): Promise<boolean> { return false; }
	async correctPayload(data: Partial<any>, invocationCtx: InvocationContext): Promise<void> {}

	protected async hasNoId(data: any): Promise<boolean> {
		return true;
	}

	async getCurrentGuardId(invocationCtx: InvocationContext): Promise<number> {
		return 0;
	}

	async checkModelTypes<Y extends Entity, Z extends Entity>(context: Context, dataType: EntityClass<Y>, idType: EntityClass<Z>, data?: Y | undefined, id?: Z | undefined): Promise<boolean> {
		return true;
	}
}


export interface GuardPropertyStrategyOptions<T extends Entity, IdName extends keyof T> {
	guardModelPropertyName: keyof T,
	repositoryClass: RepoClass<T, any>
}

export abstract class GuardPropertyStrategy<T extends Entity> extends GuardStrategy<T> {
	constructor(
		private options: GuardPropertyStrategyOptions<T, any>
	) {
		super(options.repositoryClass);
	}

	async shouldAllowOperationWithPayload(data: T, dataType: EntityClass<any>, invocationCtx: InvocationContext): Promise<boolean> {
		const currentGuardId = await this.getCurrentGuardId(invocationCtx);
		return Promise.resolve(data[this.options.guardModelPropertyName] as any == currentGuardId);
	}

	async shouldAllowOperationWithId(id: string | number, idType: EntityClass<any>, invocationCtx: InvocationContext): Promise<boolean> {
		const currentGuardId = await this.getCurrentGuardId(invocationCtx);
		const repo = await this.getRepository(invocationCtx);
		const existing = await repo.findById(id);

		return existing[this.options.guardModelPropertyName] == currentGuardId;
	}

	async shouldAllowOperation(data: T, dataType: EntityClass<any>, id: string|number, idType: EntityClass<any>, invocationCtx: InvocationContext): Promise<boolean> {
		const currentGuardId = await this.getCurrentGuardId(invocationCtx);
		const repo = await this.getRepository(invocationCtx);
		const modelIdPropertyName = await this.getModelIdPropertyName(this.options.repositoryClass, invocationCtx);
		if ((data as any)[modelIdPropertyName]) {
			if (id && id != (data as any)[modelIdPropertyName]) {
				throw new Error('Id mismatch in update payload. Id on partial model payload did not match id parameter.');
			}

			id = (data as any)[modelIdPropertyName];
		}
		const existing = await repo.findById(id);

		return ((data[this.options.guardModelPropertyName] as any) == currentGuardId && existing[this.options.guardModelPropertyName] == currentGuardId);
	}

	async getReadFilter(invocationCtx: InvocationContext): Promise<Where<T>> {
		const currentGuardId = await this.getCurrentGuardId(invocationCtx);
		return 		{
			[this.options.guardModelPropertyName]: currentGuardId,
		} as Condition<T>;
	}

	async shouldAllowRead(data: T, invocationCtx: InvocationContext): Promise<boolean> {
		const currentGuardId = await this.getCurrentGuardId(invocationCtx);
		return data[this.options.guardModelPropertyName] as any == currentGuardId;
	}


	async correctPayload(data: Partial<T>, invocationCtx: InvocationContext): Promise<void> {
		if (!data) {
			return;
		}

		const currentGuardId = await this.getCurrentGuardId(invocationCtx);
		if (data[this.options.guardModelPropertyName] && data[this.options.guardModelPropertyName] !== currentGuardId as any) {
			throw new GuardMismatchError(`guard id (${this.options.guardModelPropertyName as string}) was provided in payload but did not match the current applicable value`);
		}

		data[this.options.guardModelPropertyName] = currentGuardId as any;
	}

	protected async hasNoId(data: T | Omit<T, any> | Partial<T>, invocationCtx: InvocationContext): Promise<boolean> {
		const modelIdPropertyName = await this.getModelIdPropertyName(this.repositoryClass, invocationCtx);

		return !(modelIdPropertyName in data);
	}
}


export interface OrgGuardPropertyStrategyOptions<T extends Entity, IdName extends keyof T> {
	orgIdModelPropertyName: keyof T,
	repositoryClass: RepoClass<T, any>
}

export class OrgGuardPropertyStrategy<T extends Entity> extends GuardPropertyStrategy<T> {
	constructor(
		options: OrgGuardPropertyStrategyOptions<T, any>
	) {
		super({
			...options,
			guardModelPropertyName: options.orgIdModelPropertyName
		});
	}

	getCurrentGuardId(invocationCtx: InvocationContext): Promise<number> {
		return this.getUserOrgId(invocationCtx);
	}
}

export interface UserGuardPropertyStrategyOptions<T extends Entity, IdName extends keyof T> {
	userIdModelPropertyName: keyof T,
	repositoryClass: RepoClass<T, any>
}
export class UserGuardPropertyStrategy<T extends Entity> extends GuardPropertyStrategy<T> {
	constructor(options: UserGuardPropertyStrategyOptions<T, any>) {
		super({
			...options,
			guardModelPropertyName: options.userIdModelPropertyName
		});
	}

	getCurrentGuardId(invocationCtx: InvocationContext): Promise<number> {
		return this.getUserId(invocationCtx);
	}
}


export interface GuardMultiHopPropertyStrategyOptions<
	T extends Entity,
	IdNameT extends keyof T,
	FirstHop extends Entity,
	IdNameFirstHop extends keyof FirstHop,
	LastHop extends Entity,
	IdNameLastHop extends keyof LastHop
> {
	repositoryClass: RepoClass<T, IdNameT>,
	firstHopIdPropertyName: keyof T,
	firstHopRepositoryClass: RepoClass<FirstHop, IdNameFirstHop>
	inclusionChainAfterFirstHop: Inclusion | undefined,
	lastHopGuardPropertyName: keyof LastHop,
}

export abstract class GuardMultiHopPropertyStrategy<
	T extends Entity,
	FirstHop extends Entity,
	LastHop extends Entity
> extends GuardStrategy<T, any> {
	constructor(
		protected options: GuardMultiHopPropertyStrategyOptions<T, any, FirstHop, any, LastHop, any>
	) {
		super(options.repositoryClass);
	}

	protected async getFirstHopRepository(context: Context): Promise<EntityCrudRepository<FirstHop, any, any>> {
		return this.getRepositoryFromClass(this.options.firstHopRepositoryClass, context);
	}

	setFirstHopRepository(repository: EntityCrudRepository<FirstHop, any, any>): void {
		this.repositoryCache.set(this.options.firstHopRepositoryClass, repository);
	}

	async checkModelTypes<Y extends Entity, Z extends Entity>(context: Context, dataType: EntityClass<Y>, idType: EntityClass<Z>, data?: Y, id?: Z): Promise<boolean> {
		const [repo, firstHopRepo] = await Promise.all([
			this.getRepository(context),
			this.getFirstHopRepository(context)
		]);

		if (dataType && dataType !== repo.entityClass as any && dataType !== firstHopRepo.entityClass as any) {
			return false
		}

		if (idType && idType !== repo.entityClass as any && idType !== firstHopRepo.entityClass as any) {
			return false;
		}

		return true;
	}

	private async getRelatedFromData(data: T|FirstHop, dataType: EntityClass<any>, context: Context): Promise<FirstHop> {
		const [repo, firstHopRepo] = await Promise.all([
			this.getRepository(context),
			this.getFirstHopRepository(context)
		]);

		if (dataType !== repo.entityClass && dataType !== firstHopRepo.entityClass) {
			throw new Error('Unresolvable model payload type');
		}

		const firstHopModelIdPropertyName = await this.getModelIdPropertyNameFromRepoInstance(firstHopRepo);
		const id = dataType === repo.entityClass
			? (data as T)[this.options.firstHopIdPropertyName]
			: (data as FirstHop)[firstHopModelIdPropertyName];

		return id
			? await firstHopRepo.findById(id, this.generateInclusionFilter())
			: undefined;
	}

	private async getRelatedFromId(id: string|number, idType: EntityClass<any>, context: Context): Promise<FirstHop> {
		const [repo, firstHopRepo] = await Promise.all([
			this.getRepository(context),
			this.getFirstHopRepository(context)
		]);

		if (idType !== repo.entityClass && idType !== firstHopRepo.entityClass) {
			throw new Error('Unresolvable model id type');
		}

		if (idType === repo.entityClass) {
			const tObj = await repo.findById(id);
			const relatedId = tObj[this.options.firstHopIdPropertyName];

			return await firstHopRepo.findById(relatedId, this.generateInclusionFilter());

		} else {
			return firstHopRepo.findById(id, this.generateInclusionFilter());
		}
	}

	async shouldAllowOperationWithPayload(data: T, dataType: EntityClass<any>, invocationCtx: InvocationContext): Promise<boolean> {
		const currentGuardId = await this.getCurrentGuardId(invocationCtx);

		try {
			const related = await this.getRelatedFromData(data, dataType, invocationCtx);
			if (!related) {
				return true;
			}

			const relatedGuardId = this.resolveToGuardId(related);

			return Promise.resolve(relatedGuardId == currentGuardId);
		} catch (e) {
			if (e.code == 'ENTITY_NOT_FOUND' || e instanceof EntityNotFoundError) {
				return false;
			}

			throw e;
		}
	}

	async shouldAllowOperationWithId(id: string | number, idType: EntityClass<any>, invocationCtx: InvocationContext): Promise<boolean> {
		const currentGuardId = await this.getCurrentGuardId(invocationCtx);

		try {
			const related = await this.getRelatedFromId(id, idType, invocationCtx);
			if (!related) {
				return true;
			}

			const relatedGuardId = this.resolveToGuardId(related);

			return relatedGuardId == currentGuardId;
		} catch (e) {
			if (e instanceof EntityNotFoundError || e.code == 'ENTITY_NOT_FOUND') {
				return false;
			}

			throw e;
		}
	}

	async shouldAllowOperation(data: T|Partial<T>, dataType: EntityClass<any>, id: keyof T & (number|string), idType: EntityClass<any>, invocationCtx: InvocationContext): Promise<boolean> {
		// const currentGuardId = await this.getCurrentGuardId(invocationCtx);
		// const repo = await this.getRepository(invocationCtx);
		// const firstHopRepo = await this.getFirstHopRepository(invocationCtx);

		// try {
			const results = await Promise.all([
				this.shouldAllowOperationWithId(id, idType, invocationCtx),
				this.shouldAllowOperationWithPayload(data as T, dataType, invocationCtx),
			]);

			return results[0] && results[1];

			// const updateRelated = data[this.options.firstHopIdPropertyName]
			// 	? await firstHopRepo.findById(data[this.options.firstHopIdPropertyName], this.generateInclusionFilter())
			// 	: undefined;
			// const modelIdPropertyName = await this.getModelIdPropertyName(this.options.repositoryClass, invocationCtx);
			// if ((data as any)[modelIdPropertyName]) {
			// 	if (id && id != (data as any)[modelIdPropertyName]) {
			// 		throw new Error('Id mismatch in update payload. Id on partial model payload did not match id parameter.');
			// 	}

			// 	id = (data as any)[modelIdPropertyName];
			// }
			// const existing = await repo.findById(id);
			// const existingRelated = existing[this.options.firstHopIdPropertyName]
			// 	? await firstHopRepo.findById(existing[this.options.firstHopIdPropertyName], this.generateInclusionFilter())
			// 	: undefined;

			// if (!updateRelated && !existingRelated) {
			// 	return true;
			// }

			// const existingRelatedGuardId = this.resolveToGuardId(existingRelated);
			// if (existingRelatedGuardId !== currentGuardId) {
			// 	return false;
			// }

			// if (updateRelated) {
			// 	const updateRelatedGuardId = this.resolveToGuardId(updateRelated);
			// 	if (updateRelatedGuardId !== currentGuardId) {
			// 		return false;
			// 	}
			// }

			// return true;
		// } catch (e) {
		// 	if (e instanceof EntityNotFoundError || e.code == 'ENTITY_NOT_FOUND') {
		// 		return false;
		// 	}

		// 	throw e;
		// }
	}

	async getReadFilter(invocationCtx: InvocationContext): Promise<Where<T>> {
		// throw new Error(`Guard Read filters not supported for GuardIds that live on related models.
		// Must use @restrictReadsWithGuard() post-query-filtering instead`);
		// return {};

		// generate filter based on related object GuardId
		const currentGuardId = await this.getCurrentGuardId(invocationCtx);
		const firstHopRepo = await this.getFirstHopRepository(invocationCtx);
		const filter = this.generateInclusionFilter();
		const related = await firstHopRepo.find(filter as any);

		// find relateds that resolve to a matching Guard id
		const relatedWithMatch = related.filter(r => this.resolveToGuardId(r) == currentGuardId);

		// generate filter restricted to those related objects
		const relatedIds = relatedWithMatch.map(r => r.id);
		return { [this.options.firstHopIdPropertyName]: { inq: relatedIds } } as Where<T>;
	}

	async shouldAllowRead(data: T, invocationCtx: InvocationContext): Promise<boolean> {
		const currentGuardId = await this.getCurrentGuardId(invocationCtx);
		const firstHopRepo = await this.getFirstHopRepository(invocationCtx);

		try {
			const related = data[this.options.firstHopIdPropertyName]
				? await firstHopRepo.findById(data[this.options.firstHopIdPropertyName], this.generateInclusionFilter())
				: undefined;
			if (!related) {
				return true;
			}

			const relatedGuardId = this.resolveToGuardId(related);

			return relatedGuardId == currentGuardId;
		} catch (e) {
			if (e instanceof EntityNotFoundError || e.code == 'ENTITY_NOT_FOUND') {
				return false;
			}

			throw e;
		}
	}

	async correctPayload(data: Partial<T>, invocationCtx: InvocationContext): Promise<void> {
	}

	protected generateInclusionFilter(inclusion = this.options.inclusionChainAfterFirstHop): FilterExcludingWhere<any>|undefined {
		return inclusion
			?  { include: [inclusion] }
			: undefined;
	}

	protected resolveToGuardId(intermediate: Entity): number|string {
		const inclusionChain = this.options.inclusionChainAfterFirstHop;
		let nested = inclusionChain;
		while (nested) {
			intermediate = intermediate[nested.relation as keyof Entity] as any as Entity;

			nested = nested.scope?.include?.[0] as Inclusion;
		}

		return (intermediate as LastHop)[this.options.lastHopGuardPropertyName] as any;
	}

	protected async hasNoId(data: T | Omit<T, any> | Partial<T>, invocationCtx: InvocationContext): Promise<boolean> {
		const modelIdPropertyName = await this.getModelIdPropertyName(this.repositoryClass, invocationCtx);

		return !(modelIdPropertyName in data);
	}
}


export interface OrgGuardMultiHopPropertyStrategyOptions<
	T extends Entity,
	IdNameT extends keyof T,
	FirstHop extends Entity,
	IdNameFirstHop extends keyof FirstHop,
	LastHop extends Entity,
	IdNameLastHop extends keyof LastHop
> {
	repositoryClass: RepoClass<T, IdNameT>,
	firstHopIdPropertyName: keyof T,
	firstHopRepositoryClass: RepoClass<FirstHop, IdNameFirstHop>
	inclusionChainAfterFirstHop: Inclusion | undefined,
	lastHopOrgIdPropertyName: keyof LastHop,
}

export class OrgGuardMultiHopPropertyStrategy<
	T extends Entity,
	FirstHop extends Entity,
	LastHop extends Entity
> extends GuardMultiHopPropertyStrategy<T, FirstHop, LastHop> {
	constructor(
		options: OrgGuardMultiHopPropertyStrategyOptions<T, any, FirstHop, any, LastHop, any>
	) {
		super({
			...options,
			lastHopGuardPropertyName: options.lastHopOrgIdPropertyName
		});
	}
	getCurrentGuardId(invocationCtx: InvocationContext): Promise<number> {
		return this.getUserOrgId(invocationCtx);
	}
}


export interface OrgGuardSingleHopPropertyStrategyOptions<
	T extends Entity,
	IdNameT extends keyof T,
	U extends Entity,
	IdNameU extends keyof U
> {
	repositoryClass: RepoClass<T, IdNameT>,
	relatedIdPropertyName: keyof T,
	relatedRepositoryClass: RepoClass<U, IdNameU>
	relatedOrgIdPropertyName: keyof U,
}

export class OrgGuardSingleHopPropertyStrategy<
	T extends Entity,
	U extends Entity
> extends OrgGuardMultiHopPropertyStrategy<T, U, U> {
	constructor(
		options: OrgGuardSingleHopPropertyStrategyOptions<T, any, U, any>
	) {
		super({
			...options,
			firstHopIdPropertyName: options.relatedIdPropertyName,
			firstHopRepositoryClass: options.relatedRepositoryClass,
			inclusionChainAfterFirstHop: undefined,
			lastHopOrgIdPropertyName: options.relatedOrgIdPropertyName
		});
	}
}



// export interface GuardMultiHopReadMutateByIdOnlyStrategyOptions<
// 	T extends Entity,
// 	IdNameT extends keyof T,
// 	FirstHop extends Entity,
// 	IdNameFirstHop extends keyof FirstHop,
// 	LastHop extends Entity,
// 	IdNameLastHop extends keyof LastHop,
// 	MutationBasisType extends Entity,
// 	IdNameMutationType extends keyof MutationBasisType
// > {
// 	repositoryClass: RepoClass<T, IdNameT>,
// 	firstHopIdPropertyName: keyof T,
// 	firstHopRepositoryClass: RepoClass<FirstHop, IdNameFirstHop>,
// 	inclusionChainAfterFirstHop: Inclusion | undefined,
// 	lastHopGuardPropertyName: keyof LastHop,
// 	mutationBasisRepositoryClass: RepoClass<MutationBasisType, IdNameMutationType>,
// 	mutationBasisGuardPropertyName: keyof MutationBasisType
// }

// export abstract class GuardMultiHopReadMutateByIdOnlyStrategy<
// 	T extends Entity,
// 	FirstHop extends Entity,
// 	LastHop extends Entity,
// 	MutationBasisType extends Entity
// > extends GuardMultiHopPropertyStrategy<T, FirstHop, LastHop> {
// 	constructor(
// 		protected options: GuardMultiHopReadMutateByIdOnlyStrategyOptions<
// 			T, any,
// 			FirstHop, any,
// 			LastHop, any,
// 			MutationBasisType, any
// 		>
// 	) {
// 		super(options);
// 	}

// 	async performConfigChecks(controllerClass: any, methodName: string, context: Context): Promise<void> {
// 		await super.performConfigChecks(controllerClass, methodName, context);


// 		const appendFilterToParams =
// 			MetadataInspector.getAllParameterMetadata<object>(
// 				'guard-inject-guarded-filter',
// 				controllerClass.prototype,
// 				methodName
// 		);
// 		const appendWhereToParams =
// 				MetadataInspector.getAllParameterMetadata<object>(
// 					'guard-inject-guarded-where',
// 					controllerClass.prototype,
// 					methodName
// 				);

// 		if ((appendFilterToParams!?.filter(x => x)?.length || appendWhereToParams!?.filter(x => x)?.length)) {
// 			throw new Error(`Invalid decorator usage

// 				- When using a Mutate-By-Id-Only strategy, the @injectGuardedFilter() and @injectGuardedWhere() decorators must not be used
// 				- For reads, the post-query filtering applied via @restrictReadsWithGuard() must be used
// 				- For PATCH/updateAll, manual filtering must be implemented

// 			*** GUARD CONFIG ERROR *** - ${controllerClass.name}.${methodName}
// 			`);
// 		}

// 		const modelIndexDecorations =
// 		MetadataInspector.getAllParameterMetadata<EntityClass<any>>(
// 			'guard-model',
// 			controllerClass.prototype,
// 			methodName
// 		);


// 		if (modelIndexDecorations!?.filter(x => x)?.length) {
// 			throw new Error(`Invalid decorator usage

// 				- When using a Mutate-By-Id-Only strategy, the @modelForGuard() and @modelArrayForGuard() decorators must not be used
// 				- Instead use @modelIdForGuard() on the *parent object id* parameter
// 				- If that does not make sense, the wrong strategy may be employed for this controller


// 				Example:

// 					Model > FirstHopModel > LastHopModel (or just Model > RelatedModel)


// 					Example params: (

// 						@modelIdForGuard()
// 						parentId: string|number // id of LastHopModel/RelatedModel

// 						@requestBody()
// 						child: Model
// 					)


// 			*** GUARD CONFIG ERROR *** - ${controllerClass.name}.${methodName}
// 			`);
// 		}

// 	}


// 	protected async getMutationBasisRepository(invocationCtx: InvocationContext): Promise<EntityCrudRepository<MutationBasisType, any, any>> {
// 		return this.getRepositoryFromClass(this.options.mutationBasisRepositoryClass, invocationCtx);
// 	}

// 	setMutationBasisRepository(repository: EntityCrudRepository<MutationBasisType, any, any>): void {
// 		this.repositoryCache.set(this.options.mutationBasisRepositoryClass, repository);
// 	}

// 	async shouldAllowOperationWithPayload(data: T, dataType: EntityClass<any>, invocationCtx: InvocationContext): Promise<boolean> {
// 		throw new Error(`Do not use shouldAllowCreate/assertCreateAllowed / @restrictCreatesToGuard with GuardIdMultiHopReadNoHopMutateRelatedPropertyGuard

// 			Use shouldAllowMutation / assertMutationAllowed / @restrictMutationsToGuard AND ONLY @modelIdForGuard
// 			**Do Not Use** @modelForGuard or @modelArrayForGuard
// 		`);
// 	}

// 	async shouldAllowOperationWithId(id: string | number, idType: EntityClass<any>, invocationCtx: InvocationContext): Promise<boolean> {
// 		throw new Error(`Do not use shouldAllowDelete/assertDeleteAllowed / @restrictDeletesToGuard with GuardIdMultiHopReadNoHopMutateRelatedPropertyGuard

// 			Use shouldAllowMutation / assertMutationAllowed / @restrictMutationsToGuard AND ONLY @modelIdForGuard
// 			**Do Not Use** @modelForGuard or @modelArrayForGuard
// 		`);
// 	}

// 	async shouldAllowOperation(data: T|Partial<T>, dataType: EntityClass<any>, id: number|string, idType: EntityClass<any>, invocationCtx: InvocationContext): Promise<boolean> {
// 		throw new Error(`Do not use shouldAllowUpdate/assertUpdateAllowed / @restrictUpdatesToGuard with GuardIdMultiHopReadNoHopMutateRelatedPropertyGuard

// 			Use shouldAllowMutation / assertMutationAllowed / @restrictMutationsToGuard AND ONLY @modelIdForGuard
// 			**Do Not Use** @modelForGuard or @modelArrayForGuard
// 		`);
// 	}

// 	async getReadFilter(invocationCtx: InvocationContext): Promise<Where<T>> {
// 		throw new Error(`Guard guard Read filters not supported for GuardIds that live on related models.
// 		Must use @restrictReadsWithGuard() post-query-filtering instead`);
// 		return {};
// 	}

// 	async shouldAllowMutation(data: T | Omit<T, any> | Partial<T> | undefined, dataType: EntityClass<any>, id: string | number | undefined, idType: EntityClass<any>, invocationCtx: InvocationContext): Promise<boolean> {
// 		if (data) {
// 			throw new Error(`**Do Not Use** @modelForGuard or @modelArrayForGuard with GuardIdMultiHopReadNoHopMutateRelatedPropertyGuard

// 			ONLY use @modelIdForGuard. Must be placed on the LastHop model's id

// 			Model > FirstHopModel > LastHopModel


// 			Example params: (

// 				@modelIdForGuard()
// 				parentId: string|number // id of LastHopModel

// 				@requestBody()
// 				child: Model
// 			)

// 		`);
// 		}

// 		const currentGuardId = await this.getCurrentGuardId(invocationCtx);
// 		const mutationBasisRepository = await this.getMutationBasisRepository(invocationCtx);

// 		try {
// 			const mutationBasisModel = await mutationBasisRepository.findById(id);

// 			return mutationBasisModel[this.options.mutationBasisGuardPropertyName] === currentGuardId;
// 		} catch (e) {
// 			if (e instanceof EntityNotFoundError || e.code === 'ENTITY_NOT_FOUND') {
// 				return false;
// 			}

// 			throw e;
// 		}
// 	}

// 	async shouldAllowRead(data: T, invocationCtx: InvocationContext): Promise<boolean> {
// 		const currentGuardId = await this.getCurrentGuardId(invocationCtx);
// 		const firstHopRepo = await this.getFirstHopRepository(invocationCtx);

// 		try {
// 			const related = (data as any)[this.options.firstHopIdPropertyName]
// 				? await firstHopRepo.findById((data as any)[this.options.firstHopIdPropertyName], this.generateInclusionFilter())
// 				: undefined;
// 			if (!related) {
// 				return true;
// 			}

// 			const relatedGuardId = this.resolveToGuardId(related);

// 			return relatedGuardId == currentGuardId;
// 		} catch (e) {
// 			if (e instanceof EntityNotFoundError || e.code === 'ENTITY_NOT_FOUND') {
// 				return false;
// 			}

// 			throw e;
// 		}
// 	}
// }
// export interface OrgGuardMultiHopReadMutateByIdOnlyStrategyOptions<
// 	T extends Entity,
// 	IdNameT extends keyof T,
// 	FirstHop extends Entity,
// 	IdNameFirstHop extends keyof FirstHop,
// 	LastHop extends Entity,
// 	IdNameLastHop extends keyof LastHop,
// 	MutationBasisType extends Entity,
// 	IdNameMutationType extends keyof MutationBasisType
// > {
// 	repositoryClass: RepoClass<T, IdNameT>,
// 	firstHopIdPropertyName: keyof T,
// 	firstHopRepositoryClass: RepoClass<FirstHop, IdNameFirstHop>,
// 	inclusionChainAfterFirstHop: Inclusion | undefined,
// 	lastHopOrgIdPropertyName: keyof LastHop,
// 	mutationBasisRepositoryClass: RepoClass<MutationBasisType, IdNameMutationType>,
// 	mutationBasisOrgIdPropertyName: keyof MutationBasisType
// }

// export class OrgGuardMultiHopReadMutateByIdOnlyStrategy<
// 	T extends Entity,
// 	FirstHop extends Entity,
// 	LastHop extends Entity,
// 	MutationBasisType extends Entity
// > extends GuardMultiHopReadMutateByIdOnlyStrategy<T, FirstHop, LastHop, MutationBasisType> {
// 	constructor(
// 		options: OrgGuardMultiHopReadMutateByIdOnlyStrategyOptions<
// 			T, any,
// 			FirstHop, any,
// 			LastHop, any,
// 			MutationBasisType, any
// 		>
// 	) {
// 		super({
// 			...options,
// 			lastHopGuardPropertyName: options.lastHopOrgIdPropertyName,
// 			mutationBasisGuardPropertyName: options.mutationBasisOrgIdPropertyName
// 		});
// 	}

// 	getCurrentGuardId(invocationCtx: InvocationContext): Promise<number> {
// 		return this.getUserOrgId(invocationCtx);
// 	}
// }

// export interface OrgGuardSingleHopReadMutateByIdOnlyStrategyOptions<
// 	T extends Entity,
// 	IdNameT extends keyof T,
// 	U extends Entity,
// 	IdNameU extends keyof U,
// 	MutationBasisType extends Entity,
// 	IdNameMutationType extends keyof MutationBasisType
// > {
// 	repositoryClass: RepoClass<T, IdNameT>,
// 	relatedIdPropertyName: keyof T,
// 	relatedRepositoryClass: RepoClass<U, IdNameU>
// 	relatedOrgIdPropertyName: keyof U,

// 	mutationBasisRepositoryClass: RepoClass<MutationBasisType, IdNameMutationType>,
// 	mutationBasisOrgIdPropertyName: keyof MutationBasisType
// }

// export class OrgGuardSingleHopReadMutateByIdOnlyStrategy<
// 	T extends Entity,
// 	U extends Entity,
// 	MutationBasisType extends Entity
// > extends OrgGuardMultiHopReadMutateByIdOnlyStrategy<T, U, U, MutationBasisType> {
// 	constructor(
// 		options: OrgGuardSingleHopReadMutateByIdOnlyStrategyOptions<T, any, U, any, MutationBasisType, any>
// 	) {
// 		super({
// 			...options,
// 			firstHopIdPropertyName: options.relatedIdPropertyName,
// 			firstHopRepositoryClass: options.relatedRepositoryClass,
// 			inclusionChainAfterFirstHop: undefined,
// 			lastHopOrgIdPropertyName: options.relatedOrgIdPropertyName
// 		});
// 	}
// }





// export interface GuardMultiHopReadMutateByMultiHopIdOnlyStrategyOptions<
// 	T extends Entity,
// 	IdNameT extends keyof T,
// 	FirstHop extends Entity,
// 	IdNameFirstHop extends keyof FirstHop,
// 	LastHop extends Entity,
// 	IdNameLastHop extends keyof LastHop,
// 	MutationBasisType extends Entity,
// 	IdNameMutationType extends keyof MutationBasisType,
// 	MutationTargetType extends Entity,
// 	IdNameMutationTargetType extends keyof MutationTargetType
// > {
// 	repositoryClass: RepoClass<T, IdNameT>,
// 	firstHopIdPropertyName: keyof T,
// 	firstHopRepositoryClass: RepoClass<FirstHop, IdNameFirstHop>,
// 	inclusionChainAfterFirstHop: Inclusion | undefined,
// 	lastHopGuardPropertyName: keyof LastHop,
// 	mutationBasisRepositoryClass: RepoClass<MutationBasisType, IdNameMutationType>,
// 	mutationTargetGuardPropertyName: keyof MutationTargetType,
// 	inclusionChainForMutations: Inclusion | undefined,
// }

// export abstract class GuardMultiHopReadMutateByMultiHopIdOnlyStrategy<
// 	T extends Entity,
// 	FirstHop extends Entity,
// 	LastHop extends Entity,
// 	MutationBasisType extends Entity,
// 	MutationTargetType extends Entity
// > extends GuardMultiHopReadMutateByIdOnlyStrategy<T, FirstHop, LastHop, MutationBasisType> {
// 	constructor(
// 		protected options2: GuardMultiHopReadMutateByMultiHopIdOnlyStrategyOptions<
// 			T, any,
// 			FirstHop, any,
// 			LastHop, any,
// 			MutationBasisType, any,
// 			MutationTargetType, any
// 		>
// 	) {
// 		super({
// 			...options2,
// 			mutationBasisGuardPropertyName: undefined as any
// 		});
// 	}


// 	async shouldAllowMutation(data: T | Omit<T, any> | Partial<T> | undefined, dataType: EntityClass<any>, id: string | number | undefined, idType: EntityClass<any>, invocationCtx: InvocationContext): Promise<boolean> {
// 		if (data) {
// 			throw new Error(`**Do Not Use** @modelForGuard or @modelArrayForGuard with GuardIdMultiHopReadNoHopMutateRelatedPropertyGuard

// 			ONLY use @modelIdForGuard. Must be placed on the LastHop model's id

// 			Model > FirstHopModel > LastHopModel


// 			Example params: (

// 				@modelIdForGuard()
// 				parentId: string|number // id of LastHopModel

// 				@requestBody()
// 				child: Model
// 			)

// 		`);
// 		}

// 		const currentGuardId = await this.getCurrentGuardId(invocationCtx);
// 		const mutationBasisRepository = await this.getMutationBasisRepository(invocationCtx);
// 		const mutationTargetModel = await mutationBasisRepository.findById(id, this.generateInclusionFilter(this.options2.inclusionChainForMutations));

// 		return mutationTargetModel[this.options2.mutationTargetGuardPropertyName] === currentGuardId;
// 	}
// }




// export interface OrgGuardMultiHopReadMutateByMultiHopIdOnlyStrategyOptions<
// 	T extends Entity,
// 	IdNameT extends keyof T,
// 	FirstHop extends Entity,
// 	IdNameFirstHop extends keyof FirstHop,
// 	LastHop extends Entity,
// 	IdNameLastHop extends keyof LastHop,
// 	MutationBasisType extends Entity,
// 	IdNameMutationType extends keyof MutationBasisType,
// 	MutationTargetType extends Entity,
// 	IdNameMutationTargetType extends keyof MutationTargetType
// > {
// 	repositoryClass: RepoClass<T, IdNameT>,
// 	firstHopIdPropertyName: keyof T,
// 	firstHopRepositoryClass: RepoClass<FirstHop, IdNameFirstHop>,
// 	inclusionChainAfterFirstHop: Inclusion | undefined,
// 	lastHopOrgIdPropertyName: keyof LastHop,
// 	mutationBasisRepositoryClass: RepoClass<MutationBasisType, IdNameMutationType>,
// 	mutationTargetOrgIdPropertyName: keyof MutationTargetType,
// 	inclusionChainForMutations: Inclusion | undefined,
// }

// export class OrgGuardMultiHopReadMutateByMultiHopIdOnlyStrategy<
// 	T extends Entity,
// 	FirstHop extends Entity,
// 	LastHop extends Entity,
// 	MutationBasisType extends Entity,
// 	MutationTargetType extends Entity
// > extends GuardMultiHopReadMutateByMultiHopIdOnlyStrategy<T, FirstHop, LastHop, MutationBasisType, MutationTargetType> {
// 	constructor(
// 		options2: OrgGuardMultiHopReadMutateByMultiHopIdOnlyStrategyOptions<
// 			T, any,
// 			FirstHop, any,
// 			LastHop, any,
// 			MutationBasisType, any,
// 			MutationTargetType, any
// 		>
// 	) {
// 		super({
// 			...options2,
// 			mutationTargetGuardPropertyName: options2.mutationTargetOrgIdPropertyName,
// 			lastHopGuardPropertyName: options2.lastHopOrgIdPropertyName
// 		});
// 	}


// 	getCurrentGuardId(invocationCtx: InvocationContext): Promise<number> {
// 		return this.getUserOrgId(invocationCtx);
// 	}
// }
