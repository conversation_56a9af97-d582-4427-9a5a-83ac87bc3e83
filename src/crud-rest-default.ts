import {Count, CountSchema, CrudRepositoryImpl, DataObject, Entity, EntityCrudRepository, Filter, Where} from '@loopback/repository';
import {api, del, get, getFilterSchemaFor, getJsonSchema, getModelSchemaRef, getWhereSchemaFor, JsonSchemaOptions, jsonToSchemaObject, MediaTypeObject, param, ParameterObject, patch, post, put, requestBody, ResponsesObject, SchemaObject} from '@loopback/rest';
import {CrudRestController, CrudRestControllerCtor, CrudRestControllerOptions} from '@loopback/rest-crud';
import assert from 'assert';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from './services/basic.authorizor';
import {shouldAuthenticate} from './decorators/should-authenticate';
import {injectGuardedWhere, guardAutoCount, GuardStrategy, guardStrategy, restrictReadsWithGuard, skipGuardCheck, GuardSkipStrategy} from './interceptors/crud-guard.interceptor';
import {modelForGuard, modelIdForGuard} from './interceptors/crud-guard.interceptor';

export function defineCrudRestController<
  T extends Entity,
  IdType,
  IdName extends keyof T,
  Relations extends object = {}
>(
  modelCtor: typeof Entity & {prototype: T & {[key in IdName]: IdType}},
  options: CrudRestControllerOptions & {
	skipGuardCheckOnAllRequests?: true,
	guardStrategy?: GuardStrategy<T>,
	restrictCreatesWithGuard?: boolean,
	restrictReadsWithGuard?: boolean,
	restrictUpdatesWithGuard?: boolean,
	restrictDeletesWithGuard?: boolean,
	restrictMutationsWithGuard?: boolean
  }
): CrudRestControllerCtor<T, IdType, IdName, Relations> {
  const modelName = modelCtor.name;
  const idPathParam: ParameterObject = {
	 name: 'id',
	 in: 'path',
	 schema: getIdSchema(modelCtor),
  };

  @api({basePath: `/api/v1/${options.basePath}`, paths: {}})
  @authenticate('jwt')
  @authorize({
	 allowedRoles: ['admin', 'support', 'customer'],
	 voters: [basicAuthorization],
  })
  class CrudRestControllerImpl implements CrudRestController<T, IdType, IdName> {
		constructor(
			public readonly repository: EntityCrudRepository<T, IdType, Relations>
		) {
		}

		@post('/', {
			...response.model(200, `${modelName} instance created`, modelCtor),
		})
		@shouldAuthenticate(options, 'POST')
		async create(
			@body(modelCtor, {
				title: `New${modelName}`,
				exclude: modelCtor.getIdProperties() as (keyof T)[],
			})
			data: Omit<T, IdName>,
		): Promise<T> {
			return this.repository.create(data as DataObject<T>);
		}

		@get('/', {
			...response.array(200, `Array of ${modelName} instances`, modelCtor, {
			includeRelations: true,
			}),
		})
		@shouldAuthenticate(options, 'GET')
		async find(
			@param.query.object('filter', getFilterSchemaFor(modelCtor))
			filter?: Filter<T>,
		): Promise<(T & Relations)[]> {
			return this.repository.find(filter);
		}

		@get('/{id}', {
			...response.model(200, `${modelName} instance`, modelCtor, {
			includeRelations: true,
			}),
		})
		@shouldAuthenticate(options, 'GET')
		async findById(
			@param(idPathParam) id: IdType,
			@param.query.object('filter', getFilterSchemaFor(modelCtor))
			filter?: Filter<T>,
		): Promise<T & Relations> {
			return this.repository.findById(id, filter);
		}

		@get('/count', {
			...response(200, `${modelName} count`, {schema: CountSchema}),
		})
		@shouldAuthenticate(options, 'GET')
		async count(
			@param.query.object('where', getWhereSchemaFor(modelCtor))
			where?: Where<T>,
		): Promise<Count|T[]> {
			if (options.guardStrategy) {
				return this.repository.find({ where });
			}  else {
				return this.repository.count(where);
			}
		}

		@patch('/', {
			...response(200, `Count of ${modelName} models updated`, {
				schema: CountSchema,
			}),
		})
		@shouldAuthenticate(options, 'PATCH')
		async updateAll(
			@body(modelCtor, {partial: true}) data: Partial<T>,
			@param.query.object('where', getWhereSchemaFor(modelCtor))
			where?: Where<T>,
		): Promise<Count> {
			return this.repository.updateAll(
			// FIXME(bajtos) Improve repository API to support this use case
			// with no explicit type-casts required
			data as DataObject<T>,
			where,
			);
		}

		@patch('/{id}', {
			responses: {
			'204': {description: `${modelName} was updated`},
			},
		})
		@shouldAuthenticate(options, 'PATCH')
		async updateById(
			@param(idPathParam) id: IdType,
			@body(modelCtor, {partial: true}) data: Partial<T>,
		): Promise<void> {
			await this.repository.updateById(
			id,
			// FIXME(bajtos) Improve repository API to support this use case
			// with no explicit type-casts required
			data as DataObject<T>,
			);
		}

		@put('/{id}', {
			responses: {
			'204': {description: `${modelName} was updated`},
			},
		})
		@shouldAuthenticate(options, 'PUT')
		async replaceById(
			@param(idPathParam) id: IdType,
			@body(modelCtor) data: T,
		): Promise<void> {
			await this.repository.replaceById(id, data);
		}

		@del('/{id}', {
			responses: {
			'204': {description: `${modelName} was deleted`},
			},
		})
		@shouldAuthenticate(options, 'DELETE')
		async deleteById(@param(idPathParam) id: IdType): Promise<void> {
			await this.repository.deleteById(id);
		}
  }


  if (options.guardStrategy) {
	guardStrategy(options.guardStrategy as GuardStrategy<any, any>)(CrudRestControllerImpl);

	if (options.restrictCreatesWithGuard || (options.restrictMutationsWithGuard && options.restrictCreatesWithGuard !== false)) {
		modelForGuard(modelCtor)(CrudRestControllerImpl.prototype, 'create', 0);
	}
	if (options.restrictReadsWithGuard) {
		restrictReadsWithGuard({
			plural: true
		})(CrudRestControllerImpl.prototype, 'find', Object.getOwnPropertyDescriptor(CrudRestControllerImpl.prototype, 'find') as TypedPropertyDescriptor<any>);

		restrictReadsWithGuard({
			plural: false
		})(CrudRestControllerImpl.prototype, 'findById', Object.getOwnPropertyDescriptor(CrudRestControllerImpl.prototype, 'findById') as TypedPropertyDescriptor<any>);

		guardAutoCount()(CrudRestControllerImpl.prototype, 'count', Object.getOwnPropertyDescriptor(CrudRestControllerImpl.prototype, 'count') as TypedPropertyDescriptor<any>)
	}
	if (options.restrictUpdatesWithGuard || (options.restrictMutationsWithGuard && options.restrictUpdatesWithGuard !== false)) {
		modelForGuard(modelCtor)(CrudRestControllerImpl.prototype, 'updateAll', 0);
		injectGuardedWhere()(CrudRestControllerImpl.prototype, 'updateAll', 1);

		modelIdForGuard(modelCtor)(CrudRestControllerImpl.prototype, 'updateById', 0);
		modelForGuard(modelCtor)(CrudRestControllerImpl.prototype, 'updateById', 1);

		modelIdForGuard(modelCtor)(CrudRestControllerImpl.prototype, 'replaceById', 0);
		modelForGuard(modelCtor)(CrudRestControllerImpl.prototype, 'replaceById', 1);
	}
	if (options.restrictDeletesWithGuard || (options.restrictMutationsWithGuard && options.restrictDeletesWithGuard !== false)) {
		modelIdForGuard(modelCtor)(CrudRestControllerImpl.prototype, 'deleteById', 0);
	}
  }

  if (options.skipGuardCheckOnAllRequests) {
	guardStrategy(new GuardSkipStrategy())(CrudRestControllerImpl);

	[
		CrudRepositoryImpl.prototype.create, // POST
		CrudRepositoryImpl.prototype.find, // GET
		CrudRepositoryImpl.prototype.findById, // GET {id}
		CrudRepositoryImpl.prototype.count, // count
		CrudRepositoryImpl.prototype.updateAll, // PATCH
		CrudRepositoryImpl.prototype.updateById, // PATCH
		CrudRepositoryImpl.prototype.replaceById, // PUT
		CrudRepositoryImpl.prototype.deleteById // DELETE
	].forEach(method => {
		skipGuardCheck()(CrudRestControllerImpl.prototype, method.name, Object.getOwnPropertyDescriptor(CrudRestControllerImpl.prototype, method.name) as TypedPropertyDescriptor<any>);
	});
  }

  const controllerName = modelName + 'Controller';
  const defineNamedController = new Function(
	 'controllerClass',
	 `return class ${controllerName} extends controllerClass {}`,
  );
  const controller = defineNamedController(CrudRestControllerImpl);
  assert.equal(controller.name, controllerName);
  return controller;
}

function getIdSchema<T extends Entity>(
	modelCtor: typeof Entity & {prototype: T},
): SchemaObject {
	const idProp = modelCtor.getIdProperties()[0];
	const modelSchema = jsonToSchemaObject(
	getJsonSchema(modelCtor),
	) as SchemaObject;
	return (modelSchema.properties ?? {})[idProp] as SchemaObject;
}

 // Temporary implementation of a short-hand version of `@requestBody`
 // See https://github.com/strongloop/loopback-next/issues/3493
function body<T extends Entity>(
	modelCtor: Function & {prototype: T},
	options?: JsonSchemaOptions<T>,
) {
	return requestBody({
	content: {
		'application/json': {
			schema: getModelSchemaRef(modelCtor, options),
		},
	},
	});
}

function response(
	statusCode: number,
	description: string,
	payload: MediaTypeObject,
): {responses: ResponsesObject} {
	return {
		responses: {
			[`${statusCode}`]: {
				description,
				content: {
					'application/json': payload,
				},
			},
		},
	};
}

namespace response {
	export function model<T extends Entity>(
		statusCode: number,
		description: string,
		modelCtor: Function & {prototype: T},
		options?: JsonSchemaOptions<T>,
	) {
		return response(statusCode, description, {
			schema: getModelSchemaRef(modelCtor, options),
		});
	}

	export function array<T extends Entity>(
		statusCode: number,
		description: string,
		modelCtor: Function & {prototype: T},
		options?: JsonSchemaOptions<T>,
	) {
		return response(statusCode, description, {
			schema: {
			type: 'array',
			items: getModelSchemaRef(modelCtor, options),
			},
		});
	}
}
