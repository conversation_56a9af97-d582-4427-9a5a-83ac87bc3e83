import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {CampaignSegment, CampaignSegmentRelations, Quest, Segment, Campaign} from '../models';
import {CampaignRepository} from './campaign.repository';
import {SegmentRepository} from './segment.repository';

export class CampaignSegmentRepository extends DefaultCrudRepository<
  CampaignSegment,
  typeof CampaignSegment.prototype.id,
  CampaignSegmentRelations
> {

  public readonly campaign: BelongsToAccessor<Campaign, typeof CampaignSegment.prototype.id>;

  public readonly segment: BelongsToAccessor<Segment, typeof CampaignSegment.prototype.id>;

  constructor(
		@inject('datasources.dev_db') dataSource: DevDbDataSource,
		@repository.getter('CampaignRepository')
		protected campaignRepositoryGetter: Getter<CampaignRepository>,
		@repository.getter('SegmentRepository')
		protected segmentRepositoryGetter: Getter<SegmentRepository>,
  ) {
    super(CampaignSegment, dataSource);
    this.segment = this.createBelongsToAccessorFor('segment', segmentRepositoryGetter);
    this.registerInclusionResolver('segment', this.segment.inclusionResolver);
    this.campaign = this.createBelongsToAccessorFor('campaign', campaignRepositoryGetter);
    this.registerInclusionResolver('campaign', this.campaign.inclusionResolver);
  }
}
