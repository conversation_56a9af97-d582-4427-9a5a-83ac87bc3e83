import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {UnlayerComponent} from '../models';

export class UnlayerComponentRepository extends DefaultCrudRepository<
  UnlayerComponent,
  typeof UnlayerComponent.prototype.id
> {
  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
  ) {
    super(UnlayerComponent, dataSource);
  }
}
