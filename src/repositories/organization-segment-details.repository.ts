import {Getter, inject} from '@loopback/core';
import {BelongsToAccessor, DefaultCrudRepository, repository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {MetricSegment, OrganizationSegment} from '../models';
import {OrganizationSegmentDetails, OrganizationSegmentDetailsRelations} from '../models/organization-segment-details.model';
import {MetricSegmentRepository} from './metric-segment.repository';

export class OrganizationSegmentDetailsRepository extends DefaultCrudRepository<
  OrganizationSegmentDetails,
  typeof OrganizationSegmentDetails.prototype.id,
  OrganizationSegmentDetailsRelations
> {
	public readonly metricSegment: BelongsToAccessor<MetricSegment, typeof OrganizationSegment.prototype.id>;
  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
	@repository.getter('MetricSegmentRepository')
    protected metricSegmentRepositoryGetter: Getter<MetricSegmentRepository>,
  ) {
    super(OrganizationSegmentDetails, dataSource);
	this.metricSegment = this.createBelongsToAccessorFor('metricSegment', metricSegmentRepositoryGetter);
	this.registerInclusionResolver('metricSegment', this.metricSegment.inclusionResolver);
  }
}
