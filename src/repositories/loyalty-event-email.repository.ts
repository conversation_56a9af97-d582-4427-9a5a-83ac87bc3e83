import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasOneRepositoryFactory, BelongsToAccessor} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {LoyaltyEvent} from '../models';
import {LoyaltyEventEmail, LoyaltyEventEmailRelations} from '../models/loyalty/loyalty-event-email.model';
import {LoyaltyEventRepository} from './loyalty-event.repository';

export class LoyaltyEventEmailRepository extends DefaultCrudRepository<
  LoyaltyEventEmail,
  typeof LoyaltyEventEmail.prototype.id,
  LoyaltyEventEmailRelations
> {

  public readonly loyaltyEvent: BelongsToAccessor<LoyaltyEvent, typeof LoyaltyEventEmail.prototype.id>;

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
	@repository.getter('LoyaltyEventRepository') protected loyaltyEventRepositoryGetter: Getter<LoyaltyEventRepository>,
  ) {
    super(LoyaltyEventEmail, dataSource);
    this.loyaltyEvent = this.createBelongsToAccessorFor('loyaltyEvent', loyaltyEventRepositoryGetter,);
    this.registerInclusionResolver('loyaltyEvent', this.loyaltyEvent.inclusionResolver);
  }
}
