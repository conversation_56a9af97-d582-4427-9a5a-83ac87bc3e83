import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {EarnEffect, EarnEffectRelations, LoyaltyCurrency, LoyaltyRewardDefinition} from '../models';
import {LoyaltyCurrencyRepository} from './loyalty-currency.repository';
import {LoyaltyRewardDefinitionRepository} from './loyalty-reward-definition.repository';

export class EarnEffectRepository extends DefaultCrudRepository<
  EarnEffect,
  typeof EarnEffect.prototype.id,
  EarnEffectRelations
> {

  public readonly loyaltyCurrency: BelongsToAccessor<LoyaltyCurrency, typeof EarnEffect.prototype.id>;

  public readonly loyaltyRewardDefinition: BelongsToAccessor<LoyaltyRewardDefinition, typeof EarnEffect.prototype.id>;

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource, @repository.getter('LoyaltyCurrencyRepository') protected loyaltyCurrencyRepositoryGetter: Getter<LoyaltyCurrencyRepository>, @repository.getter('LoyaltyRewardDefinitionRepository') protected loyaltyRewardDefinitionRepositoryGetter: Getter<LoyaltyRewardDefinitionRepository>,
  ) {
    super(EarnEffect, dataSource);
    this.loyaltyRewardDefinition = this.createBelongsToAccessorFor('loyaltyRewardDefinition', loyaltyRewardDefinitionRepositoryGetter,);
    this.registerInclusionResolver('loyaltyRewardDefinition', this.loyaltyRewardDefinition.inclusionResolver);
    this.loyaltyCurrency = this.createBelongsToAccessorFor('loyaltyCurrency', loyaltyCurrencyRepositoryGetter,);
    this.registerInclusionResolver('loyaltyCurrency', this.loyaltyCurrency.inclusionResolver);
  }
}
