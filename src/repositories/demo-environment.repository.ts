import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {DemoEnvironment, DemoEnvironmentRelations} from '../models';

export class DemoEnvironmentRepository extends DefaultCrudRepository<
  DemoEnvironment,
  typeof DemoEnvironment.prototype.id,
  DemoEnvironmentRelations
> {
  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
  ) {
    super(DemoEnvironment, dataSource);
  }
}
