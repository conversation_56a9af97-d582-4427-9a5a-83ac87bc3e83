import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {Feature, FeatureRelations} from '../models';

export class FeatureRepository extends DefaultCrudRepository<
  Feature,
  typeof Feature.prototype.id,
  FeatureRelations
> {
  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
  ) {
    super(Feature, dataSource);
  }
}
