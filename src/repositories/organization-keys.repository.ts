import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {OrganizationKeys, OrganizationKeysRelations} from '../models';

export class OrganizationKeysRepository extends DefaultCrudRepository<
  OrganizationKeys,
  typeof OrganizationKeys.prototype.id,
  OrganizationKeysRelations
> {
  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
  ) {
    super(OrganizationKeys, dataSource);
  }
}
