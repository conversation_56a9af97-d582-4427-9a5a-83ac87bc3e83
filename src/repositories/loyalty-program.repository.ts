import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {LoyaltyProgram, LoyaltyCurrency, LoyaltyProgramRelations, LoyaltyCampaign, LoyaltyRewardDefinition} from '../models';
import {LoyaltyCurrencyRepository} from './loyalty-currency.repository';
import {LoyaltyCampaignRepository} from './loyalty-campaign.repository';
import {LoyaltyRewardDefinitionRepository} from './loyalty-reward-definition.repository';

export class LoyaltyProgramRepository extends DefaultCrudRepository<
	LoyaltyProgram,
	typeof LoyaltyProgram.prototype.id,
	LoyaltyProgramRelations
> {

	public readonly loyaltyCurrencies: HasManyRepositoryFactory<LoyaltyCurrency, typeof LoyaltyProgram.prototype.id>;

	public readonly loyaltyCampaigns: HasManyRepositoryFactory<LoyaltyCampaign, typeof LoyaltyProgram.prototype.id>;

	public readonly loyaltyRewardDefinitions: HasManyRepositoryFactory<LoyaltyRewardDefinition, typeof LoyaltyProgram.prototype.id>;

	constructor(
		@inject('datasources.dev_db') dataSource: DevDbDataSource,
		@repository.getter('LoyaltyCurrencyRepository') protected loyaltyCurrencyRepositoryGetter: Getter<LoyaltyCurrencyRepository>, @repository.getter('LoyaltyCampaignRepository') protected loyaltyCampaignRepositoryGetter: Getter<LoyaltyCampaignRepository>, @repository.getter('LoyaltyRewardDefinitionRepository') protected loyaltyRewardDefinitionRepositoryGetter: Getter<LoyaltyRewardDefinitionRepository>,
	) {
		super(LoyaltyProgram, dataSource);
		this.loyaltyRewardDefinitions = this.createHasManyRepositoryFactoryFor('loyaltyRewardDefinitions', loyaltyRewardDefinitionRepositoryGetter,);
		this.registerInclusionResolver('loyaltyRewardDefinitions', this.loyaltyRewardDefinitions.inclusionResolver);
		this.loyaltyCampaigns = this.createHasManyRepositoryFactoryFor('loyaltyCampaigns', loyaltyCampaignRepositoryGetter,);
		this.registerInclusionResolver('loyaltyCampaigns', this.loyaltyCampaigns.inclusionResolver);
		this.loyaltyCurrencies = this.createHasManyRepositoryFactoryFor('loyaltyCurrencies', loyaltyCurrencyRepositoryGetter,);
		this.registerInclusionResolver('loyaltyCurrencies', this.loyaltyCurrencies.inclusionResolver);
	}

	/*async cascadeDeleteProgram(programId: number) {
		const currencies = await this.loyaltyCurrencies(programId).find();
	}*/
}
