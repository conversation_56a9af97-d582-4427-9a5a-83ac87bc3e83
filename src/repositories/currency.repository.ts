import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {Currency, CurrencyRelations, SupportedCurrencies} from '../models';
import {SupportedCurrenciesRepository} from './supported-currencies.repository';

export class CurrencyRepository extends DefaultCrudRepository<
  Currency,
  typeof Currency.prototype.id,
  CurrencyRelations
> {

  public readonly supportedCurrencies: BelongsToAccessor<SupportedCurrencies, typeof Currency.prototype.id>;

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource, @repository.getter('SupportedCurrenciesRepository') protected supportedCurrenciesRepositoryGetter: Getter<SupportedCurrenciesRepository>,
  ) {
    super(Currency, dataSource);
    this.supportedCurrencies = this.createBelongsToAccessorFor('supportedCurrencies', supportedCurrenciesRepositoryGetter,);
    this.registerInclusionResolver('supportedCurrencies', this.supportedCurrencies.inclusionResolver);
  }
}
