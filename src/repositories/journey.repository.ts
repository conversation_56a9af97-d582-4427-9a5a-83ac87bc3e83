import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {Journey, JourneyRelations, Quest, RaleonUser} from '../models';
import {QuestRepository} from './quest.repository';
import {RaleonUserRepository} from './raleon-user.repository';

export class JourneyRepository extends DefaultCrudRepository<
  Journey,
  typeof Journey.prototype.id,
  JourneyRelations
> {

  public readonly quest: BelongsToAccessor<Quest, typeof Journey.prototype.id>;

  public readonly raleonUser: BelongsToAccessor<RaleonUser, typeof Journey.prototype.id>;

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource, @repository.getter('QuestRepository') protected questRepositoryGetter: Getter<QuestRepository>, @repository.getter('RaleonUserRepository') protected raleonUserRepositoryGetter: Getter<RaleonUserRepository>,
  ) {
    super(Journey, dataSource);
    this.raleonUser = this.createBelongsToAccessorFor('raleonUser', raleonUserRepositoryGetter,);
    this.registerInclusionResolver('raleonUser', this.raleonUser.inclusionResolver);
    this.quest = this.createBelongsToAccessorFor('quest', questRepositoryGetter,);
    this.registerInclusionResolver('quest', this.quest.inclusionResolver);
  }
}
