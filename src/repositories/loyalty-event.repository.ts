import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {LoyaltyEvent, LoyaltyEventRelations} from '../models';

export class LoyaltyEventRepository extends DefaultCrudRepository<
  LoyaltyEvent,
  typeof LoyaltyEvent.prototype.id,
  LoyaltyEventRelations
> {
  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
  ) {
    super(LoyaltyEvent, dataSource);
  }
}
