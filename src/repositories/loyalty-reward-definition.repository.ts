import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasOneRepositoryFactory, BelongsToAccessor} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {LoyaltyRewardDefinition, LoyaltyRewardDefinitionRelations, RewardCoupon} from '../models';
import {RewardCouponRepository} from './reward-coupon.repository';

export class LoyaltyRewardDefinitionRepository extends DefaultCrudRepository<
	LoyaltyRewardDefinition,
	typeof LoyaltyRewardDefinition.prototype.id,
	LoyaltyRewardDefinitionRelations
> {

	public readonly rewardCoupon: BelongsToAccessor<RewardCoupon, typeof LoyaltyRewardDefinition.prototype.id>;

	constructor(
		@inject('datasources.dev_db') dataSource: DevDbDataSource,
		@repository.getter('RewardCouponRepository')
		protected rewardCouponRepositoryGetter: Getter<RewardCouponRepository>,
	) {
		super(LoyaltyRewardDefinition, dataSource);
		this.rewardCoupon = this.createBelongsToAccessorFor('rewardCoupon', rewardCouponRepositoryGetter,);
		this.registerInclusionResolver('rewardCoupon', this.rewardCoupon.inclusionResolver);
	}
}
