import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {LoyaltyCurrencyTxLog, LoyaltyCurrencyTxLogRelations} from '../models';

export class LoyaltyCurrencyTxLogRepository extends DefaultCrudRepository<
  LoyaltyCurrencyTxLog,
  typeof LoyaltyCurrencyTxLog.prototype.id,
  LoyaltyCurrencyTxLogRelations
> {
  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
  ) {
    super(LoyaltyCurrencyTxLog, dataSource);
  }
}
