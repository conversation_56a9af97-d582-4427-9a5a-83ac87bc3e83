import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {Metric, MetricRelations} from '../models';

export class MetricRepository extends DefaultCrudRepository<
  Metric,
  typeof Metric.prototype.id,
  MetricRelations
> {
  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
  ) {
    super(Metric, dataSource);
  }
}
