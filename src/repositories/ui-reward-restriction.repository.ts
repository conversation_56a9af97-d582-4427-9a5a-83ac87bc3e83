import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {UiRewardRestriction, UiRewardRestrictionRelations} from '../models';

export class UiRewardRestrictionRepository extends DefaultCrudRepository<
  UiRewardRestriction,
  typeof UiRewardRestriction.prototype.id,
  UiRewardRestrictionRelations
> {
  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
  ) {
    super(UiRewardRestriction, dataSource);
  }
}
