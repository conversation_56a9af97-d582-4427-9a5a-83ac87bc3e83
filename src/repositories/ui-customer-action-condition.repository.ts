import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {UiCustomerActionCondition, UiCustomerActionConditionRelations} from '../models';

export class UiCustomerActionConditionRepository extends DefaultCrudRepository<
  UiCustomerActionCondition,
  typeof UiCustomerActionCondition.prototype.id,
  UiCustomerActionConditionRelations
> {
  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
  ) {
    super(UiCustomerActionCondition, dataSource);
  }
}
