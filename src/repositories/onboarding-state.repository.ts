import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {OnboardingState, OnboardingStateRelations, Organization, OnboardingTask} from '../models';
import {OrganizationRepository} from './organization.repository';
import {OnboardingTaskRepository} from './onboarding-task.repository';

export class OnboardingStateRepository extends DefaultCrudRepository<
  OnboardingState,
  typeof OnboardingState.prototype.id,
  OnboardingStateRelations
> {

  public readonly org: BelongsToAccessor<Organization, typeof OnboardingState.prototype.id>;

  public readonly task: BelongsToAccessor<OnboardingTask, typeof OnboardingState.prototype.id>;

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource, @repository.getter('OrganizationRepository') protected organizationRepositoryGetter: Getter<OrganizationRepository>, @repository.getter('OnboardingTaskRepository') protected onboardingTaskRepositoryGetter: Getter<OnboardingTaskRepository>,
  ) {
    super(OnboardingState, dataSource);
    this.task = this.createBelongsToAccessorFor('task', onboardingTaskRepositoryGetter,);
    this.registerInclusionResolver('task', this.task.inclusionResolver);
    this.org = this.createBelongsToAccessorFor('org', organizationRepositoryGetter,);
    this.registerInclusionResolver('org', this.org.inclusionResolver);
  }
}
