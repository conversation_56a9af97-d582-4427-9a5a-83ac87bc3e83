import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {CartData, CartDataRelations} from '../models';

export class CartDataRepository extends DefaultCrudRepository<
  CartData,
  typeof CartData.prototype.id,
  CartDataRelations
> {
  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
  ) {
    super(CartData, dataSource);
  }
}
