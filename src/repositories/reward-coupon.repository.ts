import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {RewardCoupon, RewardCouponRelations, LoyaltyGiveaway} from '../models';
import {LoyaltyGiveawayRepository} from './loyalty-giveaway.repository';

export class RewardCouponRepository extends DefaultCrudRepository<
  RewardCoupon,
  typeof RewardCoupon.prototype.id,
  RewardCouponRelations
> {

  public readonly loyaltyGiveaway: BelongsToAccessor<LoyaltyGiveaway, typeof RewardCoupon.prototype.id>;

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource, @repository.getter('LoyaltyGiveawayRepository') protected loyaltyGiveawayRepositoryGetter: Getter<LoyaltyGiveawayRepository>,
  ) {
    super(RewardCoupon, dataSource);
    this.loyaltyGiveaway = this.createBelongsToAccessorFor('loyaltyGiveaway', loyaltyGiveawayRepositoryGetter,);
    this.registerInclusionResolver('loyaltyGiveaway', this.loyaltyGiveaway.inclusionResolver);
  }
}
