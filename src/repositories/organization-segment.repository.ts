import {Getter, inject} from '@loopback/core';
import {BelongsToAccessor, DefaultCrudRepository, HasManyRepositoryFactory, repository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {OrganizationSegment, OrganizationSegmentRelations} from '../models';
import {OrganizationSegmentDetails} from '../models/organization-segment-details.model';
import {OrganizationSegmentDetailsRepository} from './organization-segment-details.repository';

export class OrganizationSegmentRepository extends DefaultCrudRepository<
	OrganizationSegment,
	typeof OrganizationSegment.prototype.id,
	OrganizationSegmentRelations
> {
	public readonly organizationSegmentDetails: HasManyRepositoryFactory<OrganizationSegmentDetails, typeof OrganizationSegment.prototype.id>;
	constructor(
		@inject('datasources.dev_db') dataSource: DevDbDataSource,
		@repository.getter('OrganizationSegmentDetailsRepository')
		protected organizationSegmentDetailsRepositoryGetter: Getter<OrganizationSegmentDetailsRepository>,
	) {
		super(OrganizationSegment, dataSource);
		this.organizationSegmentDetails = this.createHasManyRepositoryFactoryFor('organizationSegmentDetails', organizationSegmentDetailsRepositoryGetter,);
    	this.registerInclusionResolver('organizationSegmentDetails', this.organizationSegmentDetails.inclusionResolver);
	}
}
