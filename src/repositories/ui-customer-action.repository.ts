import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory, HasManyThroughRepositoryFactory} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {UiCustomerAction, UiCustomerActionRelations, UiCustomerActionCondition, UiCustomerReward, UiActionRewardJunction} from '../models';
import {UiCustomerActionConditionRepository} from './ui-customer-action-condition.repository';
import {UiActionRewardJunctionRepository} from './ui-action-reward-junction.repository';
import {UiCustomerRewardRepository} from './ui-customer-reward.repository';

export class UiCustomerActionRepository extends DefaultCrudRepository<
  UiCustomerAction,
  typeof UiCustomerAction.prototype.id,
  UiCustomerActionRelations
> {

  public readonly uiCustomerActionConditions: HasManyRepositoryFactory<UiCustomerActionCondition, typeof UiCustomerAction.prototype.id>;

  public readonly uiCustomerRewards: HasManyThroughRepositoryFactory<UiCustomerReward, typeof UiCustomerReward.prototype.id,
          UiActionRewardJunction,
          typeof UiCustomerAction.prototype.id
        >;

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource, @repository.getter('UiCustomerActionConditionRepository') protected uiCustomerActionConditionRepositoryGetter: Getter<UiCustomerActionConditionRepository>, @repository.getter('UiActionRewardJunctionRepository') protected uiActionRewardJunctionRepositoryGetter: Getter<UiActionRewardJunctionRepository>, @repository.getter('UiCustomerRewardRepository') protected uiCustomerRewardRepositoryGetter: Getter<UiCustomerRewardRepository>,
  ) {
    super(UiCustomerAction, dataSource);
    this.uiCustomerRewards = this.createHasManyThroughRepositoryFactoryFor('uiCustomerRewards', uiCustomerRewardRepositoryGetter, uiActionRewardJunctionRepositoryGetter,);
    this.registerInclusionResolver('uiCustomerRewards', this.uiCustomerRewards.inclusionResolver);
    this.uiCustomerActionConditions = this.createHasManyRepositoryFactoryFor('uiCustomerActionConditions', uiCustomerActionConditionRepositoryGetter,);
    this.registerInclusionResolver('uiCustomerActionConditions', this.uiCustomerActionConditions.inclusionResolver);
  }
}
