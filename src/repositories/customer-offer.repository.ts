import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasOneRepositoryFactory, BelongsToAccessor} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {CustomerOffer, CustomerOfferRelations, LoyaltyRewardDefinition, RaleonUser} from '../models';
import {LoyaltyRewardDefinitionRepository} from './loyalty-reward-definition.repository';
import {InventoryCouponRepository} from './inventory-coupon.repository';
import {RaleonUserRepository} from './raleon-user.repository';

export class CustomerOfferRepository extends DefaultCrudRepository<
  CustomerOffer,
  typeof CustomerOffer.prototype.id,
  CustomerOfferRelations
> {

  public readonly loyaltyRewardDefinition: HasOneRepositoryFactory<LoyaltyRewardDefinition, typeof CustomerOffer.prototype.id>;

  public readonly raleonUser: BelongsToAccessor<RaleonUser, typeof CustomerOffer.prototype.id>;

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource, @repository.getter('LoyaltyRewardDefinitionRepository') protected loyaltyRewardDefinitionRepositoryGetter: Getter<LoyaltyRewardDefinitionRepository>, @repository.getter('InventoryCouponRepository') protected inventoryCouponRepositoryGetter: Getter<InventoryCouponRepository>, @repository.getter('RaleonUserRepository') protected raleonUserRepositoryGetter: Getter<RaleonUserRepository>,
  ) {
    super(CustomerOffer, dataSource);
    this.raleonUser = this.createBelongsToAccessorFor('raleonUser', raleonUserRepositoryGetter,);
    this.registerInclusionResolver('raleonUser', this.raleonUser.inclusionResolver);
    this.loyaltyRewardDefinition = this.createHasOneRepositoryFactoryFor('loyaltyRewardDefinition', loyaltyRewardDefinitionRepositoryGetter);
	this.registerInclusionResolver('loyaltyRewardDefinition', this.loyaltyRewardDefinition.inclusionResolver);
  }
}
