import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {OrganizationSettings, OrganizationSettingsRelations} from '../models';

export class OrganizationSettingsRepository extends DefaultCrudRepository<
  OrganizationSettings,
  typeof OrganizationSettings.prototype.id,
  OrganizationSettingsRelations
> {
  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
  ) {
    super(OrganizationSettings, dataSource);
  }
}
