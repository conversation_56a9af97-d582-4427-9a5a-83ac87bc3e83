import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {Plan, PlanRelations, PlanRevenuePricing} from '../models';
import {PlanRevenuePricingRepository} from './plan-revenue-pricing.repository';

export class PlanRepository extends DefaultCrudRepository<
  Plan,
  typeof Plan.prototype.id,
  PlanRelations
> {

  public readonly planRevenuePricings: HasManyRepositoryFactory<PlanRevenuePricing, typeof Plan.prototype.id>;

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource, @repository.getter('PlanRevenuePricingRepository') protected planRevenuePricingRepositoryGetter: Getter<PlanRevenuePricingRepository>,
  ) {
    super(Plan, dataSource);
    this.planRevenuePricings = this.createHasManyRepositoryFactoryFor('planRevenuePricings', planRevenuePricingRepositoryGetter,);
    this.registerInclusionResolver('planRevenuePricings', this.planRevenuePricings.inclusionResolver);
  }
}
