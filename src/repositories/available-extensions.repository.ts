import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {AvailableExtensions, AvailableExtensionsRelations} from '../models';

export class AvailableExtensionsRepository extends DefaultCrudRepository<
  AvailableExtensions,
  typeof AvailableExtensions.prototype.id,
  AvailableExtensionsRelations
> {
  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
  ) {
    super(AvailableExtensions, dataSource);
  }
}
