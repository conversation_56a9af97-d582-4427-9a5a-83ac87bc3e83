import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasOneRepositoryFactory, BelongsToAccessor} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {VipTier, VipTierRelations, LoyaltyCampaign} from '../models';
import {LoyaltyCampaignRepository} from './loyalty-campaign.repository';

export class VipTierRepository extends DefaultCrudRepository<
  VipTier,
  typeof VipTier.prototype.id,
  VipTierRelations
> {

  public readonly loyaltyCampaign: BelongsToAccessor<LoyaltyCampaign, typeof VipTier.prototype.id>;

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource, @repository.getter('LoyaltyCampaignRepository') protected loyaltyCampaignRepositoryGetter: Getter<LoyaltyCampaignRepository>,
  ) {
    super(VipTier, dataSource);
    this.loyaltyCampaign = this.createBelongsToAccessorFor('loyaltyCampaign', loyaltyCampaignRepositoryGetter,);
    this.registerInclusionResolver('loyaltyCampaign', this.loyaltyCampaign.inclusionResolver);
  }
}
