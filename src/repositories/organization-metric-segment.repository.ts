import {Getter, inject} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {OrganizationMetricSegment, OrganizationMetricSegmentRelations, MetricSegment} from '../models';
import {MetricSegmentRepository} from './metric-segment.repository';

export class OrganizationMetricSegmentRepository extends DefaultCrudRepository<
  OrganizationMetricSegment,
  typeof OrganizationMetricSegment.prototype.id,
  OrganizationMetricSegmentRelations
> {
  public readonly metricSegment: BelongsToAccessor<
    MetricSegment,
    typeof OrganizationMetricSegment.prototype.id
  >;

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
    @repository.getter('MetricSegmentRepository')
		protected metricSegmentRepositoryGetter: Getter<MetricSegmentRepository>,
  ) {
    super(OrganizationMetricSegment, dataSource);

    this.metricSegment = this.createBelongsToAccessorFor(
      'metricSegment',
      metricSegmentRepositoryGetter
    );

    this.registerInclusionResolver('metricSegment', this.metricSegment.inclusionResolver);
  }
}
