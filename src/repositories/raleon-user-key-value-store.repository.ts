import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {RaleonUserKeyValueStore, RaleonUserKeyValueStoreRelations} from '../models';

export class RaleonUserKeyValueStoreRepository extends DefaultCrudRepository<
  RaleonUserKeyValueStore,
  typeof RaleonUserKeyValueStore.prototype.id,
  RaleonUserKeyValueStoreRelations
> {
  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
  ) {
    super(RaleonUserKeyValueStore, dataSource);
  }
}
