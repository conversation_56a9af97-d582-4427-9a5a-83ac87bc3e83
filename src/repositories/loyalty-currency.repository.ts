import {inject, Getter} from '@loopback/core';
import {BelongsToAccessor, DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {LoyaltyCurrency, LoyaltyCurrencyRelations, LoyaltyProgram, LoyaltyCurrencyBalance} from '../models';
import {LoyaltyProgramRepository} from './loyalty-program.repository';
import {LoyaltyCurrencyBalanceRepository} from './loyalty-currency-balance.repository';

export class LoyaltyCurrencyRepository extends DefaultCrudRepository<
	LoyaltyCurrency,
	typeof LoyaltyCurrency.prototype.id,
	LoyaltyCurrencyRelations
> {

	public readonly loyaltyProgram: BelongsToAccessor<
		LoyaltyProgram,
		number
	>;

	public readonly loyaltyCurrencyBalances: HasManyRepositoryFactory<LoyaltyCurrencyBalance, typeof LoyaltyCurrency.prototype.id>;

	constructor(
		@inject('datasources.dev_db') dataSource: DevDbDataSource,
		@repository.getter('LoyaltyProgramRepository')
		protected loyaltyProgramRepositoryGetter: Getter<LoyaltyProgramRepository>, @repository.getter('LoyaltyCurrencyBalanceRepository') protected loyaltyCurrencyBalanceRepositoryGetter: Getter<LoyaltyCurrencyBalanceRepository>,
	) {
		super(LoyaltyCurrency, dataSource);
		this.loyaltyCurrencyBalances = this.createHasManyRepositoryFactoryFor('loyaltyCurrencyBalances', loyaltyCurrencyBalanceRepositoryGetter,);
		this.registerInclusionResolver('loyaltyCurrencyBalances', this.loyaltyCurrencyBalances.inclusionResolver);
		this.loyaltyProgram = this.createBelongsToAccessorFor('loyaltyProgram', loyaltyProgramRepositoryGetter,);
		this.registerInclusionResolver('loyaltyProgram', this.loyaltyProgram.inclusionResolver);
	}
}
