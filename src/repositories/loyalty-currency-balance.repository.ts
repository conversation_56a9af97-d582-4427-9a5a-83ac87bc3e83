import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory, BelongsToAccessor} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {LoyaltyCurrencyBalance, LoyaltyCurrencyBalanceRelations, LoyaltyCurrencyTxLog, LoyaltyCurrency, RaleonUser} from '../models';
import {LoyaltyCurrencyTxLogRepository} from './loyalty-currency-tx-log.repository';
import {LoyaltyCurrencyRepository} from './loyalty-currency.repository';
import {RaleonUserRepository} from './raleon-user.repository';

export class LoyaltyCurrencyBalanceRepository extends DefaultCrudRepository<
  LoyaltyCurrencyBalance,
  typeof LoyaltyCurrencyBalance.prototype.id,
  LoyaltyCurrencyBalanceRelations
> {

  public readonly loyaltyCurrencyTxLogs: HasManyRepositoryFactory<LoyaltyCurrencyTxLog, typeof LoyaltyCurrencyBalance.prototype.id>;

  public readonly loyaltyCurrency: BelongsToAccessor<LoyaltyCurrency, typeof LoyaltyCurrencyBalance.prototype.id>;

  public readonly raleonUser: BelongsToAccessor<RaleonUser, typeof LoyaltyCurrencyBalance.prototype.id>;

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource, @repository.getter('LoyaltyCurrencyTxLogRepository') protected loyaltyCurrencyTxLogRepositoryGetter: Getter<LoyaltyCurrencyTxLogRepository>, @repository.getter('LoyaltyCurrencyRepository') protected loyaltyCurrencyRepositoryGetter: Getter<LoyaltyCurrencyRepository>, @repository.getter('RaleonUserRepository') protected raleonUserRepositoryGetter: Getter<RaleonUserRepository>,
  ) {
    super(LoyaltyCurrencyBalance, dataSource);
    this.raleonUser = this.createBelongsToAccessorFor('raleonUser', raleonUserRepositoryGetter,);
    this.loyaltyCurrency = this.createBelongsToAccessorFor('loyaltyCurrency', loyaltyCurrencyRepositoryGetter,);
    this.registerInclusionResolver('loyaltyCurrency', this.loyaltyCurrency.inclusionResolver);
    this.loyaltyCurrencyTxLogs = this.createHasManyRepositoryFactoryFor('loyaltyCurrencyTxLogs', loyaltyCurrencyTxLogRepositoryGetter,);
    this.registerInclusionResolver('loyaltyCurrencyTxLogs', this.loyaltyCurrencyTxLogs.inclusionResolver);
  }
}
