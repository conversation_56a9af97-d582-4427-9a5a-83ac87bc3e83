import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {InventoryCoupon, InventoryCouponRelations, LoyaltyRewardDefinition, RewardCoupon, LoyaltyGiveaway} from '../models';
import {RewardCouponRepository} from './reward-coupon.repository';
import {LoyaltyRewardDefinitionRepository} from './loyalty-reward-definition.repository';
import {LoyaltyGiveawayRepository} from './loyalty-giveaway.repository';

export class InventoryCouponRepository extends DefaultCrudRepository<
	InventoryCoupon,
	typeof InventoryCoupon.prototype.id,
	InventoryCouponRelations
> {

	public readonly rewardCoupon: BelongsToAccessor<RewardCoupon, typeof InventoryCoupon.prototype.id>;
	public readonly loyaltyRewardDefinition: BelongsToAccessor<LoyaltyRewardDefinition, typeof InventoryCoupon.prototype.id>;

  public readonly loyaltyGiveaway: BelongsToAccessor<LoyaltyGiveaway, typeof InventoryCoupon.prototype.id>;

	constructor(
		@inject('datasources.dev_db')
		dataSource: DevDbDataSource,
		@repository.getter('RewardCouponRepository')
		protected rewardCouponRepositoryGetter: Getter<RewardCouponRepository>,
		@repository.getter('LoyaltyRewardDefinitionRepository')
		protected loyaltyRewardDefinitionRepositoryGetter: Getter<LoyaltyRewardDefinitionRepository>, @repository.getter('LoyaltyGiveawayRepository') protected loyaltyGiveawayRepositoryGetter: Getter<LoyaltyGiveawayRepository>,
	) {
		super(InventoryCoupon, dataSource);
    this.loyaltyGiveaway = this.createBelongsToAccessorFor('loyaltyGiveaway', loyaltyGiveawayRepositoryGetter,);
    this.registerInclusionResolver('loyaltyGiveaway', this.loyaltyGiveaway.inclusionResolver);
		this.rewardCoupon = this.createBelongsToAccessorFor('rewardCoupon', rewardCouponRepositoryGetter,);
		this.registerInclusionResolver('rewardCoupon', this.rewardCoupon.inclusionResolver);
		this.loyaltyRewardDefinition = this.createBelongsToAccessorFor('loyaltyRewardDefinition', loyaltyRewardDefinitionRepositoryGetter,);
		this.registerInclusionResolver('loyaltyRewardDefinition', this.loyaltyRewardDefinition.inclusionResolver);
	}
}
