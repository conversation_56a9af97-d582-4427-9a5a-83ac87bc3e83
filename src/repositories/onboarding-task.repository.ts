import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {OnboardingTask, OnboardingTaskRelations} from '../models';

export class OnboardingTaskRepository extends DefaultCrudRepository<
  OnboardingTask,
  typeof OnboardingTask.prototype.id,
  OnboardingTaskRelations
> {
  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
  ) {
    super(OnboardingTask, dataSource);
  }
}
