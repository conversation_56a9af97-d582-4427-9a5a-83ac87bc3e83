import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory, HasOneRepositoryFactory} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {Offer, OfferRelations, CustomerOffer} from '../models';
import {CustomerOfferRepository} from './customer-offer.repository';

export class OfferRepository extends DefaultCrudRepository<
  Offer,
  typeof Offer.prototype.id,
  OfferRelations
> {

  public readonly customerOffers: HasManyRepositoryFactory<CustomerOffer, typeof Offer.prototype.id>;

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
	@repository.getter('CustomerOfferRepository')
	protected customerOfferRepositoryGetter: Getter<CustomerOfferRepository>,
  ) {
    super(Offer, dataSource);
    this.customerOffers = this.createHasManyRepositoryFactoryFor('customerOffers', customerOfferRepositoryGetter,);
    this.registerInclusionResolver('customerOffers', this.customerOffers.inclusionResolver);
  }
}
