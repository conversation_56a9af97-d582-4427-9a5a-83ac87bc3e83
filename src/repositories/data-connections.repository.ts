import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository,BelongsToAccessor, repository } from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {DataConnections, DataConnectionsRelations, Project} from '../models';
import {ProjectRepository} from './project.repository';

export class DataConnectionsRepository extends DefaultCrudRepository<
  DataConnections,
  typeof DataConnections.prototype.project_uuid,
  DataConnectionsRelations
> {
	public readonly project: BelongsToAccessor<Project, typeof DataConnections.prototype.project_uuid>;
  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
	@repository.getter('ProjectRepository') protected projectRepositoryGetter: Getter<ProjectRepository>,
  ) {
	super(DataConnections, dataSource);
	this.project = this.createBelongsToAccessorFor('project', projectRepositoryGetter);
    this.registerInclusionResolver('project', this.project.inclusionResolver);
  }
}
