import {Getter, inject} from '@loopback/core';
import {BelongsToAccessor, DefaultCrudRepository, HasOneRepositoryFactory, repository, HasManyThroughRepositoryFactory, HasManyRepositoryFactory} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {Content, Quest, Campaign, QuestRelations, Journey, Goal, Reward} from '../models';
import {ContentRepository} from './content.repository';
import {CampaignRepository} from './campaign.repository';
import {JourneyRepository} from './journey.repository';
import {GoalRepository} from './goal.repository';
import {RewardRepository} from './reward.repository';

export class QuestRepository extends DefaultCrudRepository<
	Quest,
	typeof Quest.prototype.id,
	QuestRelations
> {

	public readonly campaign: BelongsToAccessor<
		Campaign,
		typeof Quest.prototype.id
	>;

	public readonly content: HasOneRepositoryFactory<
		Content,
		typeof Quest.prototype.id
	>;

	public readonly journeys: HasManyRepositoryFactory<Journey, typeof Quest.prototype.id>;

	public readonly goals: HasManyRepositoryFactory<Goal, typeof Quest.prototype.id>;

	public readonly rewards: HasManyRepositoryFactory<Reward, typeof Quest.prototype.id>;

	constructor(
		@inject('datasources.dev_db')
		dataSource: DevDbDataSource,
		@repository.getter('CampaignRepository')
		protected campaignRepositoryGetter: Getter<CampaignRepository>,
		@repository.getter('ContentRepository')
		protected contentRepositoryGetter: Getter<ContentRepository>,
		@repository.getter('JourneyRepository')
		protected journeyRepositoryGetter: Getter<JourneyRepository>,
		@repository.getter('GoalRepository')
		protected goalRepositoryGetter: Getter<GoalRepository>,
		@repository.getter('RewardRepository')
		protected rewardRepositoryGetter: Getter<RewardRepository>,
	) {
		super(Quest, dataSource);
		this.rewards = this.createHasManyRepositoryFactoryFor('rewards', rewardRepositoryGetter,);
		this.registerInclusionResolver('rewards', this.rewards.inclusionResolver);
		this.goals = this.createHasManyRepositoryFactoryFor('goals', goalRepositoryGetter,);
		this.registerInclusionResolver('goals', this.goals.inclusionResolver);
		this.journeys = this.createHasManyRepositoryFactoryFor('journeys', journeyRepositoryGetter,);
		this.registerInclusionResolver('journeys', this.journeys.inclusionResolver);
		this.content = this.createHasOneRepositoryFactoryFor('content', contentRepositoryGetter);
		this.registerInclusionResolver('content', this.content.inclusionResolver);
		this.campaign = this.createBelongsToAccessorFor('campaign', campaignRepositoryGetter);
		this.registerInclusionResolver('campaign', this.campaign.inclusionResolver);
	}

	async cascadeDelete(questId: number) {
		await this.content(questId).delete();
		const goals = await this.goals(questId).find();
		const goalRepo = await this.goalRepositoryGetter();
		for (const goal of goals) {
			await goalRepo.cascadeDelete(goal.id!);
		}
		await this.journeys(questId).delete();
		await this.deleteById(questId);
	}
}
