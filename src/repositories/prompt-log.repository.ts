import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {PromptLog, PromptLogRelations} from '../models';

export class PromptLogRepository extends DefaultCrudRepository<
  PromptLog,
  typeof PromptLog.prototype.id,
  PromptLogRelations
> {
  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
  ) {
    super(PromptLog, dataSource);
  }
}
