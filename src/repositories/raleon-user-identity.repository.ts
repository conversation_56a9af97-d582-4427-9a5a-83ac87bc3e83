import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor, HasManyRepositoryFactory} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {RaleonUserIdentity, RaleonUserIdentityRelations, RaleonUser, Organization, VipTier, RaleonUserIdentityAttributes} from '../models';
import {RaleonUserRepository} from './raleon-user.repository';
import {OrganizationRepository} from './organization.repository';
import {VipTierRepository} from './vip-tier.repository';
import {RaleonUserIdentityAttributesRepository} from './raleon-user-identity-attributes.repository';

export class RaleonUserIdentityRepository extends DefaultCrudRepository<
  RaleonUserIdentity,
  typeof RaleonUserIdentity.prototype.id,
  RaleonUserIdentityRelations
> {

  public readonly raleonUser: BelongsToAccessor<RaleonUser, typeof RaleonUserIdentity.prototype.id>;
  public readonly organization: BelongsToAccessor<Organization, typeof RaleonUserIdentity.prototype.id>;
  public readonly raleonUserIdentityAttributes: HasManyRepositoryFactory<RaleonUserIdentityAttributes, typeof RaleonUserIdentity.prototype.id>;
  public readonly vipTier: BelongsToAccessor<VipTier, typeof RaleonUserIdentity.prototype.id>;

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
	@repository.getter('RaleonUserRepository')
	protected raleonUserRepositoryGetter: Getter<RaleonUserRepository>,
	@repository.getter('OrganizationRepository')
	protected organizationRepositoryGetter: Getter<OrganizationRepository>,
	@repository.getter('VipTierRepository')
	protected vipTierRepositoryGetter: Getter<VipTierRepository>,
	@repository.getter('RaleonUserIdentityAttributesRepository')
	protected raleonUserIdentityAttributesRepositoryGetter: Getter<RaleonUserIdentityAttributesRepository>,
  ) {
    super(RaleonUserIdentity, dataSource);
    this.vipTier = this.createBelongsToAccessorFor('vipTier', vipTierRepositoryGetter,);
    this.registerInclusionResolver('vipTier', this.vipTier.inclusionResolver);
    this.organization = this.createBelongsToAccessorFor('organization', organizationRepositoryGetter,);
    this.registerInclusionResolver('organization', this.organization.inclusionResolver);
    this.raleonUser = this.createBelongsToAccessorFor('raleonUser', raleonUserRepositoryGetter,);
    this.registerInclusionResolver('raleonUser', this.raleonUser.inclusionResolver);
	this.raleonUserIdentityAttributes = this.createHasManyRepositoryFactoryFor('raleonUserIdentityAttributes', raleonUserIdentityAttributesRepositoryGetter,);
    this.registerInclusionResolver('raleonUserIdentityAttributes', this.raleonUserIdentityAttributes.inclusionResolver);
  }
}
