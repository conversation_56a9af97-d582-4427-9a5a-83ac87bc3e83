import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {Content, ContentRelations, Goal, Quest, Reward} from '../models';
import {GoalRepository} from './goal.repository';
import {QuestRepository} from './quest.repository';
import {RewardRepository} from './reward.repository';

export class ContentRepository extends DefaultCrudRepository<
  Content,
  typeof Content.prototype.id,
  ContentRelations
> {

  public readonly quest: BelongsToAccessor<Quest, typeof Content.prototype.id>;
  public readonly reward: BelongsToAccessor<Reward, typeof Content.prototype.id>;
  public readonly goal: BelongsToAccessor<Goal, typeof Content.prototype.id>;

  constructor(
    @inject('datasources.dev_db')
	dataSource: DevDbDataSource,
	@repository.getter('QuestRepository')
	protected questRepositoryGetter: Getter<QuestRepository>,
	@repository.getter('RewardRepository')
	protected rewardRepositoryGetter: Getter<RewardRepository>,
	@repository.getter('GoalRepository')
	protected goalRepositoryGetter: Getter<GoalRepository>,
  ) {
    super(Content, dataSource);
    this.quest = this.createBelongsToAccessorFor('quest', questRepositoryGetter,);
    this.registerInclusionResolver('quest', this.quest.inclusionResolver);

	this.reward = this.createBelongsToAccessorFor('reward', rewardRepositoryGetter,);
	this.registerInclusionResolver('reward', this.reward.inclusionResolver);

	this.goal = this.createBelongsToAccessorFor('goal', goalRepositoryGetter,);
	this.registerInclusionResolver('goal', this.goal.inclusionResolver);
  }
}
