import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor, HasManyRepositoryFactory, HasOneRepositoryFactory} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {LoyaltyCampaign, LoyaltyCampaignRelations, LoyaltyProgram, LoyaltyEarn, LoyaltyRedemptionShopItem, LoyaltyStaticEffect, VipTier} from '../models';
import {LoyaltyProgramRepository} from './loyalty-program.repository';
import {LoyaltyEarnRepository} from './loyalty-earn.repository';
import {LoyaltyRedemptionShopItemRepository} from './loyalty-redemption-shop-item.repository';
import {LoyaltyStaticEffectRepository} from './loyalty-static-effect.repository';
import {VipTierRepository} from './vip-tier.repository';

export class LoyaltyCampaignRepository extends DefaultCrudRepository<
  LoyaltyCampaign,
  typeof LoyaltyCampaign.prototype.id,
  LoyaltyCampaignRelations
> {

  public readonly loyaltyProgram: BelongsToAccessor<LoyaltyProgram, typeof LoyaltyCampaign.prototype.id>;

  public readonly loyaltyEarns: HasManyRepositoryFactory<LoyaltyEarn, typeof LoyaltyCampaign.prototype.id>;

  public readonly loyaltyRedemptionShopItems: HasManyRepositoryFactory<LoyaltyRedemptionShopItem, typeof LoyaltyCampaign.prototype.id>;

  public readonly vipTier: HasOneRepositoryFactory<VipTier, typeof LoyaltyCampaign.prototype.id>;

  public readonly staticEffects: HasManyRepositoryFactory<LoyaltyStaticEffect, typeof LoyaltyCampaign.prototype.id>;

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
	@repository.getter('LoyaltyProgramRepository')
	protected loyaltyProgramRepositoryGetter: Getter<LoyaltyProgramRepository>,
	@repository.getter('LoyaltyEarnRepository')
	protected loyaltyEarnRepositoryGetter: Getter<LoyaltyEarnRepository>,
	@repository.getter('LoyaltyRedemptionShopItemRepository')
	protected loyaltyRedemptionShopItemRepositoryGetter: Getter<LoyaltyRedemptionShopItemRepository>,
	@repository.getter('LoyaltyStaticEffectRepository')
	protected loyaltyStaticEffectRepositoryGetter: Getter<LoyaltyStaticEffectRepository>,
	@repository.getter('VipTierRepository')
	protected vipTierRepositoryGetter: Getter<VipTierRepository>,
  ) {
    super(LoyaltyCampaign, dataSource);
    this.staticEffects = this.createHasManyRepositoryFactoryFor('staticEffects', loyaltyStaticEffectRepositoryGetter,);
    this.registerInclusionResolver('staticEffects', this.staticEffects.inclusionResolver);
    this.vipTier = this.createHasOneRepositoryFactoryFor('vipTier', vipTierRepositoryGetter);
    this.registerInclusionResolver('vipTier', this.vipTier.inclusionResolver);
    this.loyaltyRedemptionShopItems = this.createHasManyRepositoryFactoryFor('loyaltyRedemptionShopItems', loyaltyRedemptionShopItemRepositoryGetter,);
    this.registerInclusionResolver('loyaltyRedemptionShopItems', this.loyaltyRedemptionShopItems.inclusionResolver);
    this.loyaltyEarns = this.createHasManyRepositoryFactoryFor('loyaltyEarns', loyaltyEarnRepositoryGetter,);
    this.registerInclusionResolver('loyaltyEarns', this.loyaltyEarns.inclusionResolver);
    this.loyaltyProgram = this.createBelongsToAccessorFor('loyaltyProgram', loyaltyProgramRepositoryGetter,);
    this.registerInclusionResolver('loyaltyProgram', this.loyaltyProgram.inclusionResolver);
  }

  definePersistedModel(entityClass: typeof LoyaltyCampaign) {
    const modelClass = super.definePersistedModel(entityClass);
    modelClass.observe('before save', async ctx => {
		if (ctx.instance && ctx.instance.__unknownProperties) {
			ctx.instance.__unknownProperties = [];
		}
    });
    return modelClass;
  }

  async cascadeDelete(id: number): Promise<any> {
	const loyaltyCampaign = await this.findById(id);
	const staticEffects = await this.staticEffects(loyaltyCampaign.id).find({
		include: [{ relation: 'loyaltyRewardDefinition' }]
	});
	const staticEffectRepo = await this.loyaltyStaticEffectRepositoryGetter();
	for (const staticEffect of staticEffects) {
		await staticEffectRepo.cascadeDelete(staticEffect.id!);
	}

	const shopItemRepo = await this.loyaltyRedemptionShopItemRepositoryGetter();

	const shopItems = await shopItemRepo.find({
		where: { loyaltyCampaignId: loyaltyCampaign.id },
		include: [{ relation: 'loyaltyRewardDefinition', scope: { include: [{relation: 'rewardCoupon'}] }}]
	});

	for (const shopItem of shopItems) {
		await shopItemRepo.cascadeDelete(shopItem.id!);
	}

	const earnRepo = await this.loyaltyEarnRepositoryGetter();

	const earns = await earnRepo.find({
		where: { loyaltyCampaignId: loyaltyCampaign.id },
	});

	for (const earn of earns) {
		await earnRepo.cascadeDelete(earn.id!);
	}

	await this.deleteById(id);

	return {
		success: true,
		message: 'Loyalty Campaign deleted successfully',
	}
  }
}
