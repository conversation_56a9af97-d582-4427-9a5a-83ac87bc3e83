import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasOneRepositoryFactory} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {Extensions, ExtensionsRelations, AvailableExtensions} from '../models';
import {AvailableExtensionsRepository} from './available-extensions.repository';

export class ExtensionsRepository extends DefaultCrudRepository<
  Extensions,
  typeof Extensions.prototype.id,
  ExtensionsRelations
> {

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource, @repository.getter('AvailableExtensionsRepository') protected availableExtensionsRepositoryGetter: Getter<AvailableExtensionsRepository>,
  ) {
    super(Extensions, dataSource);
  }
}
