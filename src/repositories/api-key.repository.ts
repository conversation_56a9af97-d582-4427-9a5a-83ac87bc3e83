import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {ApiKey} from '../models/api-key.model';

export class ApiKeyRepository extends DefaultCrudRepository<
  ApiKey,
  typeof ApiKey.prototype.id
> {
  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
  ) {
    super(ApiKey, dataSource);
  }
}
