import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {OrganizationPlan, OrganizationPlanRelations, Plan, Organization} from '../models';
import {PlanRepository} from './plan.repository';
import {OrganizationRepository} from './organization.repository';

export class OrganizationPlanRepository extends DefaultCrudRepository<
  OrganizationPlan,
  typeof OrganizationPlan.prototype.id,
  OrganizationPlanRelations
> {

  public readonly plan: BelongsToAccessor<Plan, typeof OrganizationPlan.prototype.id>;

  public readonly organization: BelongsToAccessor<Organization, typeof OrganizationPlan.prototype.id>;

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource, @repository.getter('PlanRepository') protected planRepositoryGetter: Getter<PlanRepository>, @repository.getter('OrganizationRepository') protected organizationRepositoryGetter: Getter<OrganizationRepository>,
  ) {
    super(OrganizationPlan, dataSource);
    this.organization = this.createBelongsToAccessorFor('organization', organizationRepositoryGetter,);
    this.registerInclusionResolver('organization', this.organization.inclusionResolver);
    this.plan = this.createBelongsToAccessorFor('plan', planRepositoryGetter,);
    this.registerInclusionResolver('plan', this.plan.inclusionResolver);
  }
}
