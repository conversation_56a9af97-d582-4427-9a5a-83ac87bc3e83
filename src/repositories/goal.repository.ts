import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasOneRepositoryFactory, BelongsToAccessor} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {Goal, GoalRelations, Content, Quest} from '../models';
import {ContentRepository} from './content.repository';
import {QuestRepository} from './quest.repository';

export class GoalRepository extends DefaultCrudRepository<
	Goal,
	typeof Goal.prototype.id,
	GoalRelations
> {

	public readonly content: HasOneRepositoryFactory<Content, typeof Goal.prototype.id>;

	public readonly quest: BelongsToAccessor<Quest, typeof Goal.prototype.id>;

	constructor(
		@inject('datasources.dev_db')
		dataSource: DevDbDataSource,
		@repository.getter('ContentRepository')
		protected contentRepositoryGetter: Getter<ContentRepository>,
		@repository.getter('QuestRepository')
		protected questRepositoryGetter: Getter<QuestRepository>,
	) {
		super(Goal, dataSource);
		this.quest = this.createBelongsToAccessorFor('quest', questRepositoryGetter,);
		this.registerInclusionResolver('quest', this.quest.inclusionResolver);
		this.content = this.createHasOneRepositoryFactoryFor('content', contentRepositoryGetter);
		this.registerInclusionResolver('content', this.content.inclusionResolver);
	}

	async cascadeDelete(goalId: number) {
		await this.content(goalId).delete();
		await this.deleteById(goalId);
	}
}
