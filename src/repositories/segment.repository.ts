import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyThroughRepositoryFactory} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {Segment, SegmentRelations, Quest, CampaignSegment, Campaign} from '../models';
import {CampaignRepository} from './campaign.repository';
import {CampaignSegmentRepository} from './campaign-segment.repository';

export class SegmentRepository extends DefaultCrudRepository<
  Segment,
  typeof Segment.prototype.id,
  SegmentRelations
> {

  public readonly campaigns: HasManyThroughRepositoryFactory<Campaign, typeof Campaign.prototype.id,
    CampaignSegment,
    typeof Segment.prototype.id
  >;

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
    @repository.getter('CampaignSegmentRepository')
    protected campaignSegmentRepositoryGetter: Getter<CampaignSegmentRepository>,
    @repository.getter('CampaignRepository')
    protected campaignRepositoryGetter: Getter<CampaignRepository>,
  ) {
    super(Segment, dataSource);
    this.campaigns = this.createHasManyThroughRepositoryFactoryFor('campaigns', campaignRepositoryGetter, campaignSegmentRepositoryGetter,);
    this.registerInclusionResolver('campaigns', this.campaigns.inclusionResolver);
  }
}
