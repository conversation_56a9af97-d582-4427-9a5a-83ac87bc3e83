import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {Message, MessageRelations, Conversation} from '../models';
import {ConversationRepository} from './conversation.repository';

export class MessageRepository extends DefaultCrudRepository<
	Message,
	typeof Message.prototype.id,
	MessageRelations
> {

	public readonly conversation: BelongsToAccessor<Conversation, typeof Message.prototype.id>;

	constructor(
		@inject('datasources.dev_db') dataSource: DevDbDataSource, @repository.getter('ConversationRepository') protected conversationRepositoryGetter: Getter<ConversationRepository>,
	) {
		super(Message, dataSource);
		this.conversation = this.createBelongsToAccessorFor('conversation', conversationRepositoryGetter,);
		this.registerInclusionResolver('conversation', this.conversation.inclusionResolver);
	}
}
