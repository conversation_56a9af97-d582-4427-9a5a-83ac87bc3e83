import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {TaskType, TaskTypeRelations} from '../models';

export class TaskTypeRepository extends DefaultCrudRepository<
  TaskType,
  typeof TaskType.prototype.id,
  TaskTypeRelations
> {
  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
  ) {
    super(TaskType, dataSource);
  }
}
