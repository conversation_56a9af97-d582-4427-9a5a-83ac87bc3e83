import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {AsyncJob, AsyncJobRelations} from '../models';

export class AsyncJobRepository extends DefaultCrudRepository<
  AsyncJob,
  typeof AsyncJob.prototype.id,
  AsyncJobRelations
> {
  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
  ) {
    super(AsyncJob, dataSource);
  }
}
