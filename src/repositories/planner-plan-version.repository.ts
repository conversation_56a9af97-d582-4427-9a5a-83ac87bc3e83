import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {PlannerPlanVersion, PlannerPlanVersionRelations, PlannerCampaign} from '../models';
import {PlannerCampaignRepository} from './planner-campaign.repository';

export class PlannerPlanVersionRepository extends DefaultCrudRepository<
  PlannerPlanVersion,
  typeof PlannerPlanVersion.prototype.id,
  PlannerPlanVersionRelations
> {

  public readonly plannerCampaigns: HasManyRepositoryFactory<PlannerCampaign, typeof PlannerPlanVersion.prototype.id>;

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource, @repository.getter('PlannerCampaignRepository') protected plannerCampaignRepositoryGetter: Getter<PlannerCampaignRepository>,
  ) {
    super(PlannerPlanVersion, dataSource);
    this.plannerCampaigns = this.createHasManyRepositoryFactoryFor('plannerCampaigns', plannerCampaignRepositoryGetter,);
    this.registerInclusionResolver('plannerCampaigns', this.plannerCampaigns.inclusionResolver);
  }
}
