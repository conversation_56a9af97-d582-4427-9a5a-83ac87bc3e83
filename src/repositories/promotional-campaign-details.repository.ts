import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {PromotionalCampaignDetails, PromotionalCampaignDetailsRelations} from '../models';

export class PromotionalCampaignDetailsRepository extends DefaultCrudRepository<
  PromotionalCampaignDetails,
  typeof PromotionalCampaignDetails.prototype.id,
  PromotionalCampaignDetailsRelations
> {
  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
  ) {
    super(PromotionalCampaignDetails, dataSource);
  }
}
