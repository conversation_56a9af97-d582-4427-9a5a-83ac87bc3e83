import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {TranslationString, TranslationStringRelations} from '../models';

export class TranslationStringRepository extends DefaultCrudRepository<
  TranslationString,
  typeof TranslationString.prototype.id,
  TranslationStringRelations
> {
  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
  ) {
    super(TranslationString, dataSource);
  }
}
