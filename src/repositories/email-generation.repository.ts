import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {EmailGeneration, EmailGenerationRelations} from '../models/email-generation.model';

export class EmailGenerationRepository extends DefaultCrudRepository<
  EmailGeneration,
  typeof EmailGeneration.prototype.id,
  EmailGenerationRelations
> {
  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
  ) {
    super(EmailGeneration, dataSource);
  }
}
