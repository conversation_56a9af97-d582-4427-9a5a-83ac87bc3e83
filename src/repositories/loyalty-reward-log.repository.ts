import {BelongsToAccessor, DefaultCrudRepository, repository} from '@loopback/repository';
import {InventoryCoupon, LoyaltyRewardDefinition, LoyaltyRewardLog, LoyaltyRewardLogRelations, RaleonUser} from '../models';
import {Getter, inject} from '@loopback/core';
import {DevDbDataSource} from '../datasources';
import {LoyaltyRewardDefinitionRepository} from './loyalty-reward-definition.repository';
import {RaleonUserRepository} from './raleon-user.repository';
import {InventoryCouponRepository} from './inventory-coupon.repository';


export class LoyaltyRewardLogRepository extends DefaultCrudRepository<
	LoyaltyRewardLog,
	typeof LoyaltyRewardLog.prototype.id,
	LoyaltyRewardLogRelations
> {

	public readonly loyaltyRewardDefinition: BelongsToAccessor<LoyaltyRewardDefinition, typeof LoyaltyRewardLog.prototype.id>;
	public readonly inventoryCoupon: BelongsToAccessor<InventoryCoupon, typeof LoyaltyRewardLog.prototype.id>
	public readonly raleonUser: BelongsToAccessor<RaleonUser, typeof LoyaltyRewardLog.prototype.id>

	constructor(
		@inject('datasources.dev_db') dataSource: DevDbDataSource,
		@repository.getter('LoyaltyRewardDefinitionRepository')
		protected loyaltyRewardDefinitionRepositoryGetter: Getter<LoyaltyRewardDefinitionRepository>,
		@repository.getter('InventoryCouponRepository')
		protected inventoryCouponRepositoryGetter: Getter<InventoryCouponRepository>,
		@repository.getter('RaleonUserRepository')
		protected raleonUserRepositoryGetter: Getter<RaleonUserRepository>
	) {
		super(LoyaltyRewardLog, dataSource);

		this.loyaltyRewardDefinition = this.createBelongsToAccessorFor('loyaltyRewardDefinition', loyaltyRewardDefinitionRepositoryGetter,);
		this.registerInclusionResolver('loyaltyRewardDefinition', this.loyaltyRewardDefinition.inclusionResolver);

		this.inventoryCoupon = this.createBelongsToAccessorFor('inventoryCoupon', inventoryCouponRepositoryGetter);
		this.registerInclusionResolver('inventoryCoupon', this.inventoryCoupon.inclusionResolver);

		this.raleonUser = this.createBelongsToAccessorFor('raleonUser', raleonUserRepositoryGetter)
		this.registerInclusionResolver('raleonUser', this.raleonUser.inclusionResolver);
	}

}
