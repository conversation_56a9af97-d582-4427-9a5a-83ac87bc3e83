import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {Invite, InviteRelations} from '../models';

export class InviteRepository extends DefaultCrudRepository<
  Invite,
  typeof Invite.prototype.id,
  InviteRelations
> {
  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
  ) {
    super(Invite, dataSource);
  }
}
