import {Getter, inject} from '@loopback/core';
import {BelongsToAccessor, DefaultCrudRepository, repository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {LoyaltyEarn, RaleonUser, RaleonUserEarnLog, RaleonUserEarnLogRelations} from '../models';
import {LoyaltyEarnRepository} from './loyalty-earn.repository';
import {RaleonUserRepository} from './raleon-user.repository';

export class RaleonUserEarnLogRepository extends DefaultCrudRepository<
	RaleonUserEarnLog,
	typeof RaleonUserEarnLog.prototype.id,
	RaleonUserEarnLogRelations
> {

	public readonly loyaltyEarn: BelongsToAccessor<LoyaltyEarn, typeof RaleonUserEarnLog.prototype.id>;
	public readonly raleonUser: BelongsToAccessor<RaleonUser, typeof RaleonUserEarnLog.prototype.id>;

	constructor(
		@inject('datasources.dev_db') dataSource: DevDbDataSource,
		@repository.getter('LoyaltyEarnRepository')
		protected loyaltyEarnRepositoryGetter: Getter<LoyaltyEarnRepository>,
		@repository.getter('RaleonUserRepository')
		protected raleonUserRepositoryGetter: Getter<RaleonUserRepository>,
	) {
		super(RaleonUserEarnLog, dataSource);

		this.loyaltyEarn = this.createBelongsToAccessorFor('loyaltyEarn', loyaltyEarnRepositoryGetter,);
		this.registerInclusionResolver('loyaltyEarn', this.loyaltyEarn.inclusionResolver);

		this.raleonUser = this.createBelongsToAccessorFor('raleonUser', raleonUserRepositoryGetter,);
		this.registerInclusionResolver('raleonUser', this.raleonUser.inclusionResolver);
	}
}
