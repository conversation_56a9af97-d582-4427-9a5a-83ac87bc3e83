import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasOneRepositoryFactory} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {AttributionCampaign, AttributionCampaignRelations, ConversionEvent} from '../models';
import {ConversionEventRepository} from './conversion-event.repository';

export class AttributionCampaignRepository extends DefaultCrudRepository<
  AttributionCampaign,
  typeof AttributionCampaign.prototype.id,
  AttributionCampaignRelations
> {

  public readonly conversionEvent: HasOneRepositoryFactory<ConversionEvent, typeof AttributionCampaign.prototype.id>;

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource, @repository.getter('ConversionEventRepository') protected conversionEventRepositoryGetter: Getter<ConversionEventRepository>,
  ) {
    super(AttributionCampaign, dataSource);
    this.conversionEvent = this.createHasOneRepositoryFactoryFor('conversionEvent', conversionEventRepositoryGetter);
    this.registerInclusionResolver('conversionEvent', this.conversionEvent.inclusionResolver);
  }
}
