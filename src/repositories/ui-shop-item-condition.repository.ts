import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {UiShopItemCondition, UiShopItemConditionRelations} from '../models';

export class UiShopItemConditionRepository extends DefaultCrudRepository<
  UiShopItemCondition,
  typeof UiShopItemCondition.prototype.id,
  UiShopItemConditionRelations
> {
  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
  ) {
    super(UiShopItemCondition, dataSource);
  }
}
