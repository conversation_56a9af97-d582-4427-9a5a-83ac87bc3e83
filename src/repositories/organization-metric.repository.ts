import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {OrganizationMetric, OrganizationMetricRelations} from '../models';

export class OrganizationMetricRepository extends DefaultCrudRepository<
  OrganizationMetric,
  typeof OrganizationMetric.prototype.id,
  OrganizationMetricRelations
> {
  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
  ) {
    super(OrganizationMetric, dataSource);
  }
}
