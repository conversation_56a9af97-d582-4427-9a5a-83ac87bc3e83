import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory, BelongsToAccessor, HasOneRepositoryFactory} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {RaleonUser, RaleonUserRelations, RaleonUserIdentity, Organization, InventoryCoupon, LoyaltyCurrencyBalance, LoyaltyRewardLog, RaleonUserKeyValueStore} from '../models';
import {RaleonUserIdentityRepository} from './raleon-user-identity.repository';
import {OrganizationRepository} from './organization.repository';
import {InventoryCouponRepository} from './inventory-coupon.repository';
import {LoyaltyCurrencyBalanceRepository} from './loyalty-currency-balance.repository';
import {LoyaltyRewardLogRepository} from './loyalty-reward-log.repository';
import {RaleonUserKeyValueStoreRepository} from './raleon-user-key-value-store.repository';

export class RaleonUserRepository extends DefaultCrudRepository<
	RaleonUser,
	typeof RaleonUser.prototype.id,
	RaleonUserRelations
> {

	public readonly raleonUserIdentities: HasManyRepositoryFactory<RaleonUserIdentity, typeof RaleonUser.prototype.id>;
	public readonly raleonUserCurrencyBalance: HasOneRepositoryFactory<LoyaltyCurrencyBalance, typeof RaleonUser.prototype.id>;
	public readonly raleonRewardLogs: HasManyRepositoryFactory<LoyaltyRewardLog, typeof RaleonUser.prototype.id>;

  public readonly raleonUserKeyValueStores: HasManyRepositoryFactory<RaleonUserKeyValueStore, typeof RaleonUser.prototype.id>;
	// public readonly organization: BelongsToAccessor<Organization, typeof RaleonUser.prototype.id>;

	constructor(
		@inject('datasources.dev_db') dataSource: DevDbDataSource,
		@repository.getter('RaleonUserIdentityRepository')
		protected raleonUserIdentityRepositoryGetter: Getter<RaleonUserIdentityRepository>,
		@repository.getter('RaleonUserRepository')
		protected raleonUserRepositoryGetter: Getter<RaleonUserRepository>,
		@repository.getter('OrganizationRepository')
		protected organizationRepositoryGetter: Getter<OrganizationRepository>,
		@repository.getter('InventoryCouponRepository')
		protected inventoryCouponRepositoryGetter: Getter<InventoryCouponRepository>,
		@repository.getter('LoyaltyCurrencyBalanceRepository')
		protected loyaltyCurrencyBalanceRepositoryGetter: Getter<LoyaltyCurrencyBalanceRepository>,
		@repository.getter('LoyaltyRewardLogRepository')
		protected loyaltyRewardLogRepositoryGetter: Getter<LoyaltyRewardLogRepository>, @repository.getter('RaleonUserKeyValueStoreRepository') protected raleonUserKeyValueStoreRepositoryGetter: Getter<RaleonUserKeyValueStoreRepository>,
	) {
		super(RaleonUser, dataSource);
    this.raleonUserKeyValueStores = this.createHasManyRepositoryFactoryFor('raleonUserKeyValueStores', raleonUserKeyValueStoreRepositoryGetter,);
    this.registerInclusionResolver('raleonUserKeyValueStores', this.raleonUserKeyValueStores.inclusionResolver);
		this.raleonUserIdentities = this.createHasManyRepositoryFactoryFor('raleonUserIdentities', raleonUserIdentityRepositoryGetter,);
		this.registerInclusionResolver('raleonUserIdentities', this.raleonUserIdentities.inclusionResolver);
		this.raleonUserCurrencyBalance = this.createHasOneRepositoryFactoryFor('loyaltyCurrencyBalance', loyaltyCurrencyBalanceRepositoryGetter);
		this.registerInclusionResolver('loyaltyCurrencyBalance', this.raleonUserCurrencyBalance.inclusionResolver);
		this.raleonRewardLogs = this.createHasManyRepositoryFactoryFor('loyaltyRewardLogs', loyaltyRewardLogRepositoryGetter);
		this.registerInclusionResolver('loyaltyRewardLogs', this.raleonRewardLogs.inclusionResolver);
	}
}
