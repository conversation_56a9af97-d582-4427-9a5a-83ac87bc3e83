import {Getter, inject} from '@loopback/core';
import {BelongsToAccessor, DefaultCrudRepository, HasManyRepositoryFactory, repository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {DataConnections, Organization, Project, ProjectRelations} from '../models';
import {DataConnectionsRepository} from './data-connections.repository';
import {OrganizationRepository} from './organization.repository';

export class ProjectRepository extends DefaultCrudRepository<
  Project,
  typeof Project.prototype.id,
  ProjectRelations
> {

  public readonly dataConnections: HasManyRepositoryFactory<DataConnections, typeof Project.prototype.uuid>;
  public readonly organization: BelongsToAccessor<Organization, typeof Project.prototype.id>;

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
	@repository.getter('DataConnectionsRepository') protected dataConnectionsRepositoryGetter: Getter<DataConnectionsRepository>,
	@repository.getter('OrganizationRepository') protected organizationRepositoryGetter: Getter<OrganizationRepository>,
  ) {
    super(Project, dataSource);
    this.dataConnections = this.createHasManyRepositoryFactoryFor('dataConnections', dataConnectionsRepositoryGetter,);
    this.registerInclusionResolver('dataConnections', this.dataConnections.inclusionResolver);
	this.organization = this.createBelongsToAccessorFor('organization', organizationRepositoryGetter);
    this.registerInclusionResolver('organization', this.organization.inclusionResolver);
  }
}
