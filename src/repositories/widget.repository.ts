import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {Widget, WidgetRelations} from '../models';

export class WidgetRepository extends DefaultCrudRepository<
  Widget,
  typeof Widget.prototype.id,
  WidgetRelations
> {
  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
  ) {
    super(Widget, dataSource);
  }
}
