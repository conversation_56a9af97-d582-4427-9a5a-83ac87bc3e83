import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {UiRewardLimit, UiRewardLimitRelations} from '../models';

export class UiRewardLimitRepository extends DefaultCrudRepository<
  UiRewardLimit,
  typeof UiRewardLimit.prototype.id,
  UiRewardLimitRelations
> {
  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
  ) {
    super(UiRewardLimit, dataSource);
  }
}
