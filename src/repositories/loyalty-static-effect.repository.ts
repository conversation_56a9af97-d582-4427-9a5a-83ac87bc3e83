import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {LoyaltyStaticEffect, LoyaltyStaticEffectRelations, LoyaltyCampaign, LoyaltyRewardDefinition} from '../models';
import {LoyaltyCampaignRepository} from './loyalty-campaign.repository';
import {LoyaltyRewardDefinitionRepository} from './loyalty-reward-definition.repository';
import {RewardCouponRepository} from './reward-coupon.repository';

export class LoyaltyStaticEffectRepository extends DefaultCrudRepository<
  LoyaltyStaticEffect,
  typeof LoyaltyStaticEffect.prototype.id,
  LoyaltyStaticEffectRelations
> {

  public readonly loyaltyCampaign: BelongsToAccessor<LoyaltyCampaign, typeof LoyaltyStaticEffect.prototype.id>;

  public readonly loyaltyRewardDefinition: BelongsToAccessor<LoyaltyRewardDefinition, typeof LoyaltyStaticEffect.prototype.id>;

  constructor(
    @inject('datasources.dev_db')
	dataSource: DevDbDataSource,
	@repository.getter('LoyaltyCampaignRepository')
	protected loyaltyCampaignRepositoryGetter: Getter<LoyaltyCampaignRepository>,
	@repository.getter('LoyaltyRewardDefinitionRepository')
	protected loyaltyRewardDefinitionRepositoryGetter: Getter<LoyaltyRewardDefinitionRepository>,
	@repository.getter('RewardCouponRepository')
	protected rewardCouponRepositoryGetter: Getter<RewardCouponRepository>,
  ) {
    super(LoyaltyStaticEffect, dataSource);
    this.loyaltyRewardDefinition = this.createBelongsToAccessorFor('loyaltyRewardDefinition', loyaltyRewardDefinitionRepositoryGetter,);
    this.registerInclusionResolver('loyaltyRewardDefinition', this.loyaltyRewardDefinition.inclusionResolver);
    this.loyaltyCampaign = this.createBelongsToAccessorFor('loyaltyCampaign', loyaltyCampaignRepositoryGetter,);
    this.registerInclusionResolver('loyaltyCampaign', this.loyaltyCampaign.inclusionResolver);
  }

  async cascadeDelete(id: number): Promise<any> {
	const rewardDef = await this.loyaltyRewardDefinition(id);
	const coupon = rewardDef.rewardCouponId;
	if (coupon) {
		await (await this.rewardCouponRepositoryGetter()).deleteById(coupon);
	}

	await (await this.loyaltyRewardDefinitionRepositoryGetter()).deleteById(rewardDef.id);
	await this.deleteById(id);

	return {
		success: true,
		message: `Loyalty Static Effect ${id} deleted successfully`
	};
  }
}
