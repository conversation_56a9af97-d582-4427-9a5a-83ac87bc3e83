import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor, HasOneRepositoryFactory} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {Reward, RewardRelations, Quest, Content} from '../models';
import {QuestRepository} from './quest.repository';
import {ContentRepository} from './content.repository';

export class RewardRepository extends DefaultCrudRepository<
  Reward,
  typeof Reward.prototype.id,
  RewardRelations
> {

  public readonly quest: BelongsToAccessor<Quest, typeof Reward.prototype.id>;

  public readonly content: HasOneRepositoryFactory<Content, typeof Reward.prototype.id>;

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource, @repository.getter('QuestRepository') protected questRepositoryGetter: Getter<QuestRepository>, @repository.getter('ContentRepository') protected contentRepositoryGetter: Getter<ContentRepository>,
  ) {
    super(Reward, dataSource);
    this.content = this.createHasOneRepositoryFactoryFor('content', contentRepositoryGetter);
    this.registerInclusionResolver('content', this.content.inclusionResolver);
    this.quest = this.createBelongsToAccessorFor('quest', questRepositoryGetter,);
    this.registerInclusionResolver('quest', this.quest.inclusionResolver);
  }
}
