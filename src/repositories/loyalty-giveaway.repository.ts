import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {LoyaltyGiveaway, LoyaltyGiveawayRelations} from '../models';
import {LoyaltyProgramRepository} from './loyalty-program.repository';
import {LoyaltyProgram} from '../models/loyalty/loyalty-program.model';

export class LoyaltyGiveawayRepository extends DefaultCrudRepository<
  LoyaltyGiveaway,
  typeof LoyaltyGiveaway.prototype.id,
  LoyaltyGiveawayRelations
> {

  public readonly loyaltyProgram: BelongsToAccessor<LoyaltyProgram, typeof LoyaltyGiveaway.prototype.id>;

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource, @repository.getter('LoyaltyProgramRepository') protected loyaltyProgramRepositoryGetter: Getter<LoyaltyProgramRepository>,
  ) {
    super(LoyaltyGiveaway, dataSource);
    this.loyaltyProgram = this.createBelongsToAccessorFor('loyaltyProgram', loyaltyProgramRepositoryGetter,);
    this.registerInclusionResolver('loyaltyProgram', this.loyaltyProgram.inclusionResolver);
  }
}
