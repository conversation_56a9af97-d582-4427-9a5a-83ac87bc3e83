import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory, BelongsToAccessor} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {LoyaltyEarn, LoyaltyEarnRelations, EarnEffect, LoyaltyCampaign, EarnCondition} from '../models';
import {EarnEffectRepository} from './earn-effect.repository';
import {LoyaltyCampaignRepository} from './loyalty-campaign.repository';
import {EarnConditionRepository} from './earn-condition.repository';
import {LoyaltyRewardDefinitionRepository} from './loyalty-reward-definition.repository';
import {RewardCouponRepository} from './reward-coupon.repository';

export class LoyaltyEarnRepository extends DefaultCrudRepository<
  LoyaltyEarn,
  typeof LoyaltyEarn.prototype.id,
  LoyaltyEarnRelations
> {

  public readonly earnEffects: HasManyRepositoryFactory<EarnEffect, typeof LoyaltyEarn.prototype.id>;

  public readonly loyaltyCampaign: BelongsToAccessor<LoyaltyCampaign, typeof LoyaltyEarn.prototype.id>;

  public readonly earnConditions: HasManyRepositoryFactory<EarnCondition, typeof LoyaltyEarn.prototype.id>;

  constructor(
    @inject('datasources.dev_db')
	dataSource: DevDbDataSource,
	@repository.getter('EarnEffectRepository')
	protected earnEffectRepositoryGetter: Getter<EarnEffectRepository>,
	@repository.getter('LoyaltyCampaignRepository')
	protected loyaltyCampaignRepositoryGetter: Getter<LoyaltyCampaignRepository>,
	@repository.getter('EarnConditionRepository')
	protected earnConditionRepositoryGetter: Getter<EarnConditionRepository>,
	@repository.getter('LoyaltyRewardDefinitionRepository')
	protected loyaltyRewardDefinitionRepositoryGetter: Getter<LoyaltyRewardDefinitionRepository>,
	@repository.getter('RewardCouponRepository')
	protected rewardCouponRepositoryGetter: Getter<RewardCouponRepository>,
  ) {
    super(LoyaltyEarn, dataSource);
    this.earnConditions = this.createHasManyRepositoryFactoryFor('earnConditions', earnConditionRepositoryGetter,);
    this.registerInclusionResolver('earnConditions', this.earnConditions.inclusionResolver);
    this.loyaltyCampaign = this.createBelongsToAccessorFor('loyaltyCampaign', loyaltyCampaignRepositoryGetter,);
    this.registerInclusionResolver('loyaltyCampaign', this.loyaltyCampaign.inclusionResolver);
    this.earnEffects = this.createHasManyRepositoryFactoryFor('earnEffects', earnEffectRepositoryGetter,);
    this.registerInclusionResolver('earnEffects', this.earnEffects.inclusionResolver);
  }


  definePersistedModel(entityClass: typeof LoyaltyEarn) {
    const modelClass = super.definePersistedModel(entityClass);
    modelClass.observe('before save', async ctx => {
		if (ctx.instance && ctx.instance.__unknownProperties) {
			ctx.instance.__unknownProperties = [];
		}
    });
    return modelClass;
  }

  async cascadeDelete(id: number): Promise<any> {
	//delete all earn conditions
	const conditionCount = await (await this.earnConditionRepositoryGetter()).deleteAll({loyaltyEarnId: id});

	const earnEffectRepository = await this.earnEffectRepositoryGetter();
	const earnEffects = await earnEffectRepository.find(
		{
			where: {loyaltyEarnId: id},
			include: [{relation: 'loyaltyRewardDefinition', scope: {fields: ['rewardCouponId']}}]
		},
	);

	const rewardDefinitionIds = earnEffects.map((earnEffect) => earnEffect.loyaltyRewardDefinitionId);
	const couponIds = earnEffects.map((earnEffect) => earnEffect.loyaltyRewardDefinition?.rewardCouponId);

	//delete all reward coupons
	const couponCount = await (await this.rewardCouponRepositoryGetter()).deleteAll({id: {inq: couponIds}});
	//delete all reward definitions
	const rewardCount = await (await this.loyaltyRewardDefinitionRepositoryGetter()).deleteAll({id: {inq: rewardDefinitionIds}});

	//delete all earn effects
	const effectCount = await earnEffectRepository.deleteAll({loyaltyEarnId: id});
	//delete loyalty earn
	await this.deleteById(id);

	return {
		success: true,
		message: `Loyalty earn ${id} deleted successfully`
	}
  }
}
