import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {LoyaltyRedemptionShopItem, LoyaltyRedemptionShopItemRelations, LoyaltyRewardDefinition} from '../models';
import {LoyaltyRewardDefinitionRepository} from './loyalty-reward-definition.repository';
import {RewardCouponRepository} from './reward-coupon.repository';

export class LoyaltyRedemptionShopItemRepository extends DefaultCrudRepository<
  LoyaltyRedemptionShopItem,
  typeof LoyaltyRedemptionShopItem.prototype.id,
  LoyaltyRedemptionShopItemRelations
> {

  public readonly loyaltyRewardDefinition: BelongsToAccessor<LoyaltyRewardDefinition, typeof LoyaltyRedemptionShopItem.prototype.id>;

  constructor(
    @inject('datasources.dev_db')
	dataSource: DevDbDataSource,
	@repository.getter('LoyaltyRewardDefinitionRepository')
	protected loyaltyRewardDefinitionRepositoryGetter: Getter<LoyaltyRewardDefinitionRepository>,
	@repository.getter('RewardCouponRepository')
	protected rewardCouponRepositoryGetter: Getter<RewardCouponRepository>,
  ) {
    super(LoyaltyRedemptionShopItem, dataSource);
    this.loyaltyRewardDefinition = this.createBelongsToAccessorFor('loyaltyRewardDefinition', loyaltyRewardDefinitionRepositoryGetter,);
    this.registerInclusionResolver('loyaltyRewardDefinition', this.loyaltyRewardDefinition.inclusionResolver);
  }

  definePersistedModel(entityClass: typeof LoyaltyRedemptionShopItem) {
    const modelClass = super.definePersistedModel(entityClass);
    modelClass.observe('before save', async ctx => {
		if (ctx.instance && ctx.instance.__unknownProperties) {
			ctx.instance.__unknownProperties = [];
		}
    });
    return modelClass;
  }

  async cascadeDelete(id: number): Promise<any> {
	const coupon = (await this.loyaltyRewardDefinition(id)).rewardCouponId;
	if (coupon) {
		await (await this.rewardCouponRepositoryGetter()).deleteById(coupon);
	}

	await (await this.loyaltyRewardDefinitionRepositoryGetter()).deleteById(id);
	await this.deleteById(id);

	return {
		success: true,
		message: `Loyalty earn ${id} deleted successfully`
	};
  }
}
