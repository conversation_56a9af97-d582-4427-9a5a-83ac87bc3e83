import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {ConversionEvent, ConversionEventRelations} from '../models';

export class ConversionEventRepository extends DefaultCrudRepository<
  ConversionEvent,
  typeof ConversionEvent.prototype.id,
  ConversionEventRelations
> {
  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
  ) {
    super(ConversionEvent, dataSource);
  }
}
