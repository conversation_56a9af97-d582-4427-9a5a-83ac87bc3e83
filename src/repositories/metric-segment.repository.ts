import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {MetricSegment, MetricSegmentRelations} from '../models';

export class MetricSegmentRepository extends DefaultCrudRepository<
  MetricSegment,
  typeof MetricSegment.prototype.id,
  MetricSegmentRelations
> {
  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
  ) {
    super(MetricSegment, dataSource);
  }
}
