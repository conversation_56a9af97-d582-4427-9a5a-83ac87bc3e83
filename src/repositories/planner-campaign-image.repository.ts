import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {PlannerCampaignImage, PlannerCampaignImageRelations} from '../models';

export class PlannerCampaignImageRepository extends DefaultCrudRepository<
  PlannerCampaignImage,
  typeof PlannerCampaignImage.prototype.id,
  PlannerCampaignImageRelations
> {
  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
  ) {
    super(PlannerCampaignImage, dataSource);
  }
}
