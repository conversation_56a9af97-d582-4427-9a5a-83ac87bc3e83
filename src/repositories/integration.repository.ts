import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {Integration, IntegrationRelations} from '../models';

export class IntegrationRepository extends DefaultCrudRepository<
  Integration,
  typeof Integration.prototype.id,
  IntegrationRelations
> {
  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
  ) {
    super(Integration, dataSource);
  }
}
