import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {PlanFeature, PlanFeatureRelations, Plan, Feature} from '../models';
import {PlanRepository} from './plan.repository';
import {FeatureRepository} from './feature.repository';

export class PlanFeatureRepository extends DefaultCrudRepository<
  PlanFeature,
  typeof PlanFeature.prototype.id,
  PlanFeatureRelations
> {

  public readonly plan: BelongsToAccessor<Plan, typeof PlanFeature.prototype.id>;

  public readonly feature: BelongsToAccessor<Feature, typeof PlanFeature.prototype.id>;

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource, @repository.getter('PlanRepository') protected planRepositoryGetter: Getter<PlanRepository>, @repository.getter('FeatureRepository') protected featureRepositoryGetter: Getter<FeatureRepository>,
  ) {
    super(PlanFeature, dataSource);
    this.feature = this.createBelongsToAccessorFor('feature', featureRepositoryGetter,);
    this.registerInclusionResolver('feature', this.feature.inclusionResolver);
    this.plan = this.createBelongsToAccessorFor('plan', planRepositoryGetter,);
    this.registerInclusionResolver('plan', this.plan.inclusionResolver);
  }
}
