import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {UiCustomerReward, UiCustomerRewardRelations, UiRewardRestriction, UiRewardLimit} from '../models';
import {UiRewardRestrictionRepository} from './ui-reward-restriction.repository';
import {UiRewardLimitRepository} from './ui-reward-limit.repository';

export class UiCustomerRewardRepository extends DefaultCrudRepository<
  UiCustomerReward,
  typeof UiCustomerReward.prototype.id,
  UiCustomerRewardRelations
> {

  public readonly uiRewardRestrictions: HasManyRepositoryFactory<UiRewardRestriction, typeof UiCustomerReward.prototype.id>;

  public readonly uiRewardLimits: HasManyRepositoryFactory<UiRewardLimit, typeof UiCustomerReward.prototype.id>;

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource, @repository.getter('UiRewardRestrictionRepository') protected uiRewardRestrictionRepositoryGetter: Getter<UiRewardRestrictionRepository>, @repository.getter('UiRewardLimitRepository') protected uiRewardLimitRepositoryGetter: Getter<UiRewardLimitRepository>,
  ) {
    super(UiCustomerReward, dataSource);
    this.uiRewardLimits = this.createHasManyRepositoryFactoryFor('uiRewardLimits', uiRewardLimitRepositoryGetter,);
    this.registerInclusionResolver('uiRewardLimits', this.uiRewardLimits.inclusionResolver);
    this.uiRewardRestrictions = this.createHasManyRepositoryFactoryFor('uiRewardRestrictions', uiRewardRestrictionRepositoryGetter,);
    this.registerInclusionResolver('uiRewardRestrictions', this.uiRewardRestrictions.inclusionResolver);
  }
}
