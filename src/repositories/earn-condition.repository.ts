import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {EarnCondition, EarnConditionRelations} from '../models';

export class EarnConditionRepository extends DefaultCrudRepository<
  EarnCondition,
  typeof EarnCondition.prototype.id,
  EarnConditionRelations
> {
  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
  ) {
    super(EarnCondition, dataSource);
  }
}
