import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasOneRepositoryFactory} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {UiActionRewardJunction, UiActionRewardJunctionRelations, UiCustomerAction, UiCustomerReward} from '../models';
import {UiCustomerActionRepository} from './ui-customer-action.repository';
import {UiCustomerRewardRepository} from './ui-customer-reward.repository';

export class UiActionRewardJunctionRepository extends DefaultCrudRepository<
  UiActionRewardJunction,
  typeof UiActionRewardJunction.prototype.id,
  UiActionRewardJunctionRelations
> {

//   public readonly uiCustomerAction: HasOneRepositoryFactory<UiCustomerAction, typeof UiActionRewardJunction.prototype.id>;

//   public readonly uiCustomerReward: HasOneRepositoryFactory<UiCustomerReward, typeof UiActionRewardJunction.prototype.id>;

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource, @repository.getter('UiCustomerActionRepository') protected uiCustomerActionRepositoryGetter: Getter<UiCustomerActionRepository>, @repository.getter('UiCustomerRewardRepository') protected uiCustomerRewardRepositoryGetter: Getter<UiCustomerRewardRepository>,
  ) {
    super(UiActionRewardJunction, dataSource);
    // this.uiCustomerReward = this.createHasOneRepositoryFactoryFor('uiCustomerReward', uiCustomerRewardRepositoryGetter);
    // this.registerInclusionResolver('uiCustomerReward', this.uiCustomerReward.inclusionResolver);
    // this.uiCustomerAction = this.createHasOneRepositoryFactoryFor('uiCustomerAction', uiCustomerActionRepositoryGetter);
    // this.registerInclusionResolver('uiCustomerAction', this.uiCustomerAction.inclusionResolver);
  }
}
