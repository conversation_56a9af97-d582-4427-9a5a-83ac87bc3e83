import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {PromptTemplate} from '../models';

export class PromptTemplateRepository extends DefaultCrudRepository<
  PromptTemplate,
  typeof PromptTemplate.prototype.id
> {
  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
  ) {
    super(PromptTemplate, dataSource);
  }
}
