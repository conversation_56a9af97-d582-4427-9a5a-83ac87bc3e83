import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {SupportedCurrencies, SupportedCurrenciesRelations} from '../models';

export class SupportedCurrenciesRepository extends DefaultCrudRepository<
  SupportedCurrencies,
  typeof SupportedCurrencies.prototype.id,
  SupportedCurrenciesRelations
> {
  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
  ) {
    super(SupportedCurrencies, dataSource);
  }
}
