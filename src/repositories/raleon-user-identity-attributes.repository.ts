import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasOneRepositoryFactory, BelongsToAccessor} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {RaleonUserIdentityAttributes, RaleonUserIdentityAttributesRelations, RaleonUserIdentity} from '../models';
import {RaleonUserIdentityRepository} from './raleon-user-identity.repository';

export class RaleonUserIdentityAttributesRepository extends DefaultCrudRepository<
RaleonUserIdentityAttributes,
  typeof RaleonUserIdentityAttributes.prototype.id,
  RaleonUserIdentityAttributesRelations
> {

  public readonly raleonUserIdentity: BelongsToAccessor<RaleonUserIdentity, typeof RaleonUserIdentityAttributes.prototype.id>;

  constructor(
	@inject('datasources.dev_db') dataSource: DevDbDataSource, @repository.getter('RaleonUserIdentityRepository') protected raleonUserIdentityRepositoryGetter: Getter<RaleonUserIdentityRepository>,
  ) {
	super(RaleonUserIdentityAttributes, dataSource);
	this.raleonUserIdentity = this.createBelongsToAccessorFor('raleonUserIdentity', raleonUserIdentityRepositoryGetter,);
	this.registerInclusionResolver('raleonUserIdentity', this.raleonUserIdentity.inclusionResolver);
  }
}
