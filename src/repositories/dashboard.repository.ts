import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {Dashboard, DashboardRelations, Widget} from '../models';
import {WidgetRepository} from './widget.repository';

export class DashboardRepository extends DefaultCrudRepository<
  Dashboard,
  typeof Dashboard.prototype.id,
  DashboardRelations
> {

  public readonly widgets: HasManyRepositoryFactory<Widget, typeof Dashboard.prototype.id>;

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource, @repository.getter('WidgetRepository') protected widgetRepositoryGetter: Getter<WidgetRepository>,
  ) {
    super(Dashboard, dataSource);
    this.widgets = this.createHasManyRepositoryFactoryFor('widgets', widgetRepositoryGetter,);
    this.registerInclusionResolver('widgets', this.widgets.inclusionResolver);
  }
}
