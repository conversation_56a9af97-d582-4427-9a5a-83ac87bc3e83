import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {CustomMetric, CustomMetricRelations} from '../models';

export class CustomMetricRepository extends DefaultCrudRepository<
  CustomMetric,
  typeof CustomMetric.prototype.id,
  CustomMetricRelations
> {
  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
  ) {
    super(CustomMetric, dataSource);
  }
}
