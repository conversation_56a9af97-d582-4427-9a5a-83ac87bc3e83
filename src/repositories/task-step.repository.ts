import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {TaskStep, TaskStepRelations, TaskType, Task} from '../models';
import {TaskTypeRepository} from './task-type.repository';
import {TaskRepository} from './task.repository';

export class TaskStepRepository extends DefaultCrudRepository<
  TaskStep,
  typeof TaskStep.prototype.id,
  TaskStepRelations
> {

  public readonly taskType: BelongsToAccessor<TaskType, typeof TaskStep.prototype.id>;

  public readonly task: BelongsToAccessor<Task, typeof TaskStep.prototype.id>;

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource, @repository.getter('TaskTypeRepository') protected taskTypeRepositoryGetter: Getter<TaskTypeRepository>, @repository.getter('TaskRepository') protected taskRepositoryGetter: Getter<TaskRepository>,
  ) {
    super(TaskStep, dataSource);
    this.task = this.createBelongsToAccessorFor('task', taskRepositoryGetter,);
    this.registerInclusionResolver('task', this.task.inclusionResolver);
    this.taskType = this.createBelongsToAccessorFor('taskType', taskTypeRepositoryGetter,);
    this.registerInclusionResolver('taskType', this.taskType.inclusionResolver);
  }
}
