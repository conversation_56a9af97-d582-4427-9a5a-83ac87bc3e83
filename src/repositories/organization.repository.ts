import {Getter, inject} from '@loopback/core';
import {DefaultCrudRepository, HasManyRepositoryFactory, repository, HasOneRepositoryFactory, BelongsToAccessor, DataObject} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {Organization, OrganizationRelations, Project, RaleonUser, OrganizationSettings, Currency, OrganizationKeys, Extensions, OrganizationPlannerPlan} from '../models';
import {ProjectRepository} from './project.repository';
import {RaleonUserRepository} from './raleon-user.repository';
import {OrganizationSettingsRepository} from './organization-settings.repository';
import {CurrencyRepository} from './currency.repository';
import {OrganizationKeysRepository} from './organization-keys.repository';
import {ExtensionsRepository} from './extensions.repository';
import {OrganizationPlannerPlanRepository} from './organization-planner-plan.repository';
const hubspot = require('@hubspot/api-client');

export class OrganizationRepository extends DefaultCrudRepository<
  Organization,
  typeof Organization.prototype.id,
  OrganizationRelations
> {
  public readonly projects: HasManyRepositoryFactory<Project, typeof Organization.prototype.id>;

  public readonly raleonUsers: HasManyRepositoryFactory<RaleonUser, typeof Organization.prototype.id>;

  public readonly organizationSettings: HasManyRepositoryFactory<OrganizationSettings, typeof Organization.prototype.id>;

  public readonly primaryCurrency: HasOneRepositoryFactory<Currency, typeof Organization.prototype.id>;

  public readonly organizationKeys: HasManyRepositoryFactory<OrganizationKeys, typeof Organization.prototype.id>;

  public readonly extensions: HasManyRepositoryFactory<Extensions, typeof Organization.prototype.id>;

  public readonly organizationPlannerPlans: HasManyRepositoryFactory<OrganizationPlannerPlan, typeof Organization.prototype.id>;

  public readonly parentOrg: BelongsToAccessor<Organization, typeof Organization.prototype.id>;

  private hubspotClient = new hubspot.Client({
    accessToken: process.env.HUBSPOT_ACCESS_TOKEN || '********************************************',
    numberOfApiCallRetries: 3,
  });

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
	@repository.getter('ProjectsRepository')
	protected projectsRepositoryGetter: Getter<ProjectRepository>,
	@repository.getter('RaleonUserRepository')
	protected raleonUserRepositoryGetter: Getter<RaleonUserRepository>,
	@repository.getter('OrganizationSettingsRepository')
	protected organizationSettingsRepositoryGetter: Getter<OrganizationSettingsRepository>,
	@repository.getter('CurrencyRepository')
	protected currencyRepositoryGetter: Getter<CurrencyRepository>,
	@repository.getter('OrganizationKeysRepository')
	protected organizationKeysRepositoryGetter: Getter<OrganizationKeysRepository>,
	@repository.getter('ExtensionsRepository')
	protected extensionsRepositoryGetter: Getter<ExtensionsRepository>, @repository.getter('OrganizationPlannerPlanRepository') protected organizationPlannerPlanRepositoryGetter: Getter<OrganizationPlannerPlanRepository>, @repository.getter('OrganizationRepository') protected organizationRepositoryGetter: Getter<OrganizationRepository>,
  ) {
    super(Organization, dataSource);
    this.parentOrg = this.createBelongsToAccessorFor('parentOrg', organizationRepositoryGetter,);
    this.registerInclusionResolver('parentOrg', this.parentOrg.inclusionResolver);
    this.organizationPlannerPlans = this.createHasManyRepositoryFactoryFor('organizationPlannerPlans', organizationPlannerPlanRepositoryGetter,);
    this.registerInclusionResolver('organizationPlannerPlans', this.organizationPlannerPlans.inclusionResolver);
    this.extensions = this.createHasManyRepositoryFactoryFor('extensions', extensionsRepositoryGetter,);
    this.registerInclusionResolver('extensions', this.extensions.inclusionResolver);
    this.organizationKeys = this.createHasManyRepositoryFactoryFor('organizationKeys', organizationKeysRepositoryGetter,);
    this.registerInclusionResolver('organizationKeys', this.organizationKeys.inclusionResolver);
    this.primaryCurrency = this.createHasOneRepositoryFactoryFor('primaryCurrency', currencyRepositoryGetter);
    this.registerInclusionResolver('primaryCurrency', this.primaryCurrency.inclusionResolver);
    this.organizationSettings = this.createHasManyRepositoryFactoryFor('organizationSettings', organizationSettingsRepositoryGetter,);
    this.registerInclusionResolver('organizationSettings', this.organizationSettings.inclusionResolver);
    this.raleonUsers = this.createHasManyRepositoryFactoryFor('raleonUsers', raleonUserRepositoryGetter,);
    this.registerInclusionResolver('raleonUsers', this.raleonUsers.inclusionResolver);
	this.projects = this.createHasManyRepositoryFactoryFor('projects', projectsRepositoryGetter,);
    this.registerInclusionResolver('projects', this.projects.inclusionResolver);
  }

  async updateById(id: typeof Organization.prototype.id, data: DataObject<Organization>): Promise<void> {
    const current = await this.findById(id);
    await super.updateById(id, data);

    if (data.name && data.name !== current.name) {
      await this.updateHubspotCompany(current.externalDomain, data.name as string);
    }
  }

  async replaceById(id: typeof Organization.prototype.id, data: Organization): Promise<void> {
    const current = await this.findById(id);
    await super.replaceById(id, data);

    if (data.name && data.name !== current.name) {
      await this.updateHubspotCompany(data.externalDomain || current.externalDomain, data.name);
    }
  }

  private async updateHubspotCompany(domain?: string, name?: string) {
    if (!domain || !name) return;
    try {
      const searchRequest = {
        filterGroups: [{filters: [{propertyName: 'domain', operator: 'EQ', value: domain}]}],
        properties: ['name', 'domain'],
      };

      const res = await this.hubspotClient.crm.companies.searchApi.doSearch(searchRequest);
      if (res.results.length > 0) {
        await this.hubspotClient.crm.companies.basicApi.update(res.results[0].id, {
          properties: {name},
        });
      }
    } catch (e) {
      console.error('Failed to update Hubspot company name:', e);
    }
  }
}
