import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {PlanCampaignContent, PlanCampaignContentRelations} from '../models';

export class PlanCampaignContentRepository extends DefaultCrudRepository<
  PlanCampaignContent,
  typeof PlanCampaignContent.prototype.id,
  PlanCampaignContentRelations
> {
  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource,
  ) {
    super(PlanCampaignContent, dataSource);
  }
}
