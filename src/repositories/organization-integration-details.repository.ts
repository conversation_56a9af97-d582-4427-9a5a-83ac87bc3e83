import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {OrganizationIntegrationDetails, OrganizationIntegrationDetailsRelations, Integration} from '../models';
import {IntegrationRepository} from './integration.repository';

export class OrganizationIntegrationDetailsRepository extends DefaultCrudRepository<
  OrganizationIntegrationDetails,
  typeof OrganizationIntegrationDetails.prototype.id,
  OrganizationIntegrationDetailsRelations
> {

  public readonly integration: BelongsToAccessor<Integration, typeof OrganizationIntegrationDetails.prototype.id>;

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource, @repository.getter('IntegrationRepository') protected integrationRepositoryGetter: Getter<IntegrationRepository>,
  ) {
    super(OrganizationIntegrationDetails, dataSource);
    this.integration = this.createBelongsToAccessorFor('integration', integrationRepositoryGetter,);
    this.registerInclusionResolver('integration', this.integration.inclusionResolver);
  }
}
