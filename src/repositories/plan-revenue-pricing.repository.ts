import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {PlanRevenuePricing, PlanRevenuePricingRelations, Plan} from '../models';
import {PlanRepository} from './plan.repository';

export class PlanRevenuePricingRepository extends DefaultCrudRepository<
  PlanRevenuePricing,
  typeof PlanRevenuePricing.prototype.id,
  PlanRevenuePricingRelations
> {

  public readonly plan: BelongsToAccessor<Plan, typeof PlanRevenuePricing.prototype.id>;

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource, @repository.getter('PlanRepository') protected planRepositoryGetter: Getter<PlanRepository>,
  ) {
    super(PlanRevenuePricing, dataSource);
    this.plan = this.createBelongsToAccessorFor('plan', planRepositoryGetter,);
    this.registerInclusionResolver('plan', this.plan.inclusionResolver);
  }
}
