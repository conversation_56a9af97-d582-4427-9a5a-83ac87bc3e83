import {Getter, inject} from '@loopback/core';
import {
	BelongsToAccessor,
  DefaultCrudRepository, HasOneRepositoryFactory,
  juggler,
  repository
} from '@loopback/repository';
import { HttpErrors } from '@loopback/rest';
import { SecurityBindings } from '@loopback/security';
import {Organization, User, UserCredentials, UserWithRelations} from '../models';
import {UserCredentialsRepository} from './user-credentials.repository';
import { User as AuthedUser } from '@loopback/authentication-jwt';
import {OrganizationRepository} from './organization.repository';

export type Credentials = {
  email: string;
  password: string;
};

export class UserRepository extends DefaultCrudRepository<
  User,
  typeof User.prototype.id,
  UserWithRelations
> {

  public readonly userCredentials: HasOneRepositoryFactory<
    UserCredentials,
    typeof User.prototype.id
  >;

  public readonly organization: BelongsToAccessor<
    Organization,
    typeof User.prototype.id
  >;

  constructor(
    @inject('datasources.dev_db') dataSource: juggler.DataSource,
    @repository.getter('UserCredentialsRepository')
    protected userCredentialsRepositoryGetter: Getter<UserCredentialsRepository>,
    @repository.getter('OrganizationRepository')
    protected organizationRepositoryGetter: Getter<OrganizationRepository>,
  ) {
    super(User, dataSource);
    this.userCredentials = this.createHasOneRepositoryFactoryFor(
      'userCredentials',
      userCredentialsRepositoryGetter,
    );
    this.organization = this.createBelongsToAccessorFor(
      'organization',
      organizationRepositoryGetter,
    );
	this.registerInclusionResolver('organization', this.organization.inclusionResolver);
  }

  async findCredentials(
    userId: typeof User.prototype.id,
  ): Promise<UserCredentials | undefined> {
    try {
      return await this.userCredentials(userId).get();
    } catch (err) {
      if (err.code === 'ENTITY_NOT_FOUND') {
        return undefined;
      }
      throw err;
    }
  }

  async findOrganization(authedUser: AuthedUser): Promise<number> {
	const user = await this.findById(parseInt(authedUser.id));
	if (!user.organizationId) {
		throw new HttpErrors.Unauthorized('User does not belong to an organization');
	}
	return user.organizationId;
  }
}
