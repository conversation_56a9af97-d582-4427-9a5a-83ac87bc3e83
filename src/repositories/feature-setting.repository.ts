import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {FeatureSetting, FeatureSettingRelations, Organization} from '../models';
import {OrganizationRepository} from './organization.repository';

export class FeatureSettingRepository extends DefaultCrudRepository<
  FeatureSetting,
  typeof FeatureSetting.prototype.id,
  FeatureSettingRelations
> {

  public readonly organization: BelongsToAccessor<Organization, typeof FeatureSetting.prototype.id>;

  constructor(
    @inject('datasources.dev_db') dataSource: DevDbDataSource, @repository.getter('OrganizationRepository') protected organizationRepositoryGetter: Getter<OrganizationRepository>,
  ) {
    super(FeatureSetting, dataSource);
    this.organization = this.createBelongsToAccessorFor('organization', organizationRepositoryGetter,);
    this.registerInclusionResolver('organization', this.organization.inclusionResolver);
  }
}
