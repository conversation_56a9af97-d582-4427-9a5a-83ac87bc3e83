# Controllers

This directory contains source files for the controllers exported by this app.

To add a new empty controller, type in `lb4 controller [<name>]` from the
command-line of your application's root directory.
For more information, please visit
[Controller generator](http://loopback.io/doc/en/lb4/Controller-generator.html).

When adding a new controller, set the basePath of the controller to `/api/v{versionNumber}`
by using the `@api({basePath: '/api/v1'})` annotation on the class declaration.
All resources within the controller's api will get this prefix added to them.

# Important

To ensure the generated controller endpoints are secured with authentication, do the following:
- Open the generated `{name}.rest-config.ts` file and add two properties to the config object.
  1. repository - this should be set to the corresponding repository class for this controller.
  2. unauthenticatedMethods - this is an array of HTTP Method Types ('GET', 'PUT'...) for generated endpoints that should remain unauthenticated.

