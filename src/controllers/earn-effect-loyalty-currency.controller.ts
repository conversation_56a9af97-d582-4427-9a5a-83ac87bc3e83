import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
  api
} from '@loopback/rest';
import {
  EarnEffect,
  LoyaltyCurrency,
} from '../models';
import {EarnEffectRepository} from '../repositories';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../services';
import {skipGuardCheck} from '../interceptors/crud-guard.interceptor';
import {guardStrategy, GuardSkipStrategy} from '../interceptors/crud-guard.interceptor';

@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class EarnEffectLoyaltyCurrencyController {
  constructor(
    @repository(EarnEffectRepository)
    public earnEffectRepository: EarnEffectRepository,
  ) { }

  @skipGuardCheck()
  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
  })
  @get('/earn-effects/{id}/loyalty-currency', {
    responses: {
      '200': {
        description: 'LoyaltyCurrency belonging to EarnEffect',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LoyaltyCurrency)},
          },
        },
      },
    },
  })
  async getLoyaltyCurrency(
    @param.path.number('id') id: typeof EarnEffect.prototype.id,
  ): Promise<LoyaltyCurrency> {
    return this.earnEffectRepository.loyaltyCurrency(id);
  }
}
