// Uncomment these imports to begin using these cool features!

import {authenticate} from '@loopback/authentication';
import {User} from '@loopback/authentication-jwt';
import {authorize} from '@loopback/authorization';
import {inject} from '@loopback/core';
import {api, post, requestBody} from '@loopback/rest';
import {SecurityBindings} from '@loopback/security';
import {GuardSkipStrategy, guardStrategy, skipGuardCheck} from '../interceptors';
import {basicAuthorization} from '../services/basic.authorizor';
const fetch = require('node-fetch')
const aws4 = require('aws4')

const AWS_ACCESS_KEY = process.env.API_ACCESS_KEY;
const AWS_SECRET_KEY = process.env.API_SECRET_KEY;
const DATA_INGESTION_API = 'arp0p4h0lj.execute-api.us-east-1.amazonaws.com'

function getURL(path: string, method: string, body?: any, host?: string) {
	const opts = {
	  host: host || DATA_INGESTION_API,
	  path: path,
	  region: 'us-east-1',
	  service: 'execute-api',
	  mode: 'cors',
	  body: body != undefined ? JSON.stringify(body) : undefined,
	  headers: {
		'Content-Type': 'application/json',
	  },
	  method: method
	}
	return aws4.sign(opts, {accessKeyId: AWS_ACCESS_KEY, secretAccessKey: AWS_SECRET_KEY});
  }

@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class DataIngestionController {
	constructor(
		@inject(SecurityBindings.USER, {optional: true})
		private user: User,
	) {}

  	@authenticate('jwt')
	@authorize({
		allowedRoles: ['raleon-support'],
		voters: [basicAuthorization],
	})
	@post('/balances')
	@skipGuardCheck()
	async create(
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						properties: {
							viewName: {
								type: 'string',
							},
							projectId: {
								type: 'string',
							},
						},
					},
				},
			},
		})
		request: any,
	): Promise<any> {
		const path = '/v1/balances';
		const signedRequest = getURL(path, 'POST', request);
		const response = await fetch(`https://${DATA_INGESTION_API}${path}`, signedRequest);
		const data = await response.text();

		if (!data.includes('Success')) {
			throw new Error('Error Prioritizing Balance Ingestion');
		}
		return {
			statusCode: 200,
			message: 'Success',
		};
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['raleon-support'],
		voters: [basicAuthorization],
	})
	@post('/transactions')
	@skipGuardCheck()
	async ingestTransactions(
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						properties: {
							viewName: {
								type: 'string',
							},
							projectId: {
								type: 'string',
							},
						},
					},
				},
			},
		})
		request: any,
	): Promise<any> {
		const path = '/v1/transactions';
		const signedRequest = getURL(path, 'POST', request);
		const response = await fetch(`https://${DATA_INGESTION_API}${path}`, signedRequest);
		const data = await response.text();

		if (!data.includes('Success')) {
			throw new Error('Error Prioritizing Transaction Ingestion');
		}
		return {
			statusCode: 200,
			message: 'Success',
		};
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['raleon-support'],
		voters: [basicAuthorization],
	})
	@post('/nfts')
	@skipGuardCheck()
	async ingestNFTs(
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						properties: {
							viewName: {
								type: 'string',
							},
							projectId: {
								type: 'string',
							},
						},
					},
				},
			},
		})
		request: any,
	): Promise<any> {
		const path = '/v1/nfts';
		const signedRequest = getURL(path, 'POST', request);
		const response = await fetch(`https://${DATA_INGESTION_API}${path}`, signedRequest);
		const data = await response.text();

		if (!data.includes('Success')) {
			throw new Error('Error Prioritizing NFT Ingestion');
		}
		return {
			statusCode: 200,
			message: 'Success',
		};
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['raleon-support'],
		voters: [basicAuthorization],
	})
	@post('/smart-contracts')
	@skipGuardCheck()
	async ingestSmartContracts(
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						properties: {
							viewName: {
								type: 'string',
							},
							projectId: {
								type: 'string',
							},
						},
					},
				},
			},
		})
		request: any,
	): Promise<any> {
		const path = '/v1/smart-contracts';
		const signedRequest = getURL(path, 'POST', request);
		const response = await fetch(`https://${DATA_INGESTION_API}${path}`, signedRequest);
		const data = await response.text();

		if (!data.includes('Success')) {
			throw new Error('Error Prioritizing Smart Contract Ingestion');
		}
		return {
			statusCode: 200,
			message: 'Success',
		};
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['raleon-support'],
		voters: [basicAuthorization],
	})
	@post('/tokens')
	@skipGuardCheck()
	async ingestTokens(
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						properties: {
							viewName: {
								type: 'string',
							},
							projectId: {
								type: 'string',
							},
						},
					},
				},
			},
		})
		request: any,
	): Promise<any> {
		const path = '/v1/tokens';
		const signedRequest = getURL(path, 'POST', request);
		const response = await fetch(`https://${DATA_INGESTION_API}${path}`, signedRequest);
		const data = await response.text();

		if (!data.includes('Success')) {
			throw new Error('Error Prioritizing Token Ingestion');
		}
		return {
			statusCode: 200,
			message: 'Success',
		};
	}
}
