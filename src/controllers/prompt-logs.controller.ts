import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {inject} from '@loopback/core';
import {
	Count,
	CountSchema,
	Filter,
	repository,
	Where,
} from '@loopback/repository';
import {
	api,
	del,
	get,
	getModelSchemaRef,
	HttpErrors,
	param,
	patch,
	post,
	put,
	requestBody,
	response,
} from '@loopback/rest';
import {SecurityBindings, UserProfile} from '@loopback/security';
import * as AWS from 'aws-sdk';
import {PromptLog} from '../models';
import {PromptLogRepository} from '../repositories';
import {GuardSkipStrategy, guardStrategy, skipGuardCheck} from '../interceptors';
import {basicAuthorization} from '../services';

// Configure AWS S3
// Ensure AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, and AWS_REGION are set in environment variables
const s3 = new AWS.S3({
	accessKeyId: process.env.AWS_ACCESS_KEY_ID,
	secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
	region: process.env.AWS_REGION || 'us-east-1',
});

const S3_BUCKET_NAME = 'raleon-prompt-logs';

@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class PromptLogsController {
	constructor(
		@repository(PromptLogRepository)
		public promptLogRepository: PromptLogRepository
	) { }

	// --- Private Helper for S3 ---
	private async fetchFromS3(key: string): Promise<any> {
		try {
			const params = {
				Bucket: S3_BUCKET_NAME,
				Key: key,
			};
			const data = await s3.getObject(params).promise();
			if (!data.Body) {
				throw new HttpErrors.NotFound(`S3 object content not found for key: ${key}`);
			}
			// Assuming the body is JSON data stored as a string
			return JSON.parse(data.Body.toString('utf-8'));
		} catch (error) {
			console.error(`Error fetching from S3 (key: ${key}):`, error);
			if (error.code === 'NoSuchKey') {
				throw new HttpErrors.NotFound(`S3 object not found for key: ${key}`);
			}
			// Rethrow as a generic server error for other S3 issues
			throw new HttpErrors.InternalServerError(`Failed to fetch prompt content from storage.`);
		}
	}

	// --- Endpoints ---

	@get('/prompt-logs')
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['raleon-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@response(200, {
		description: 'Array of PromptLog model instances',
		content: {
			'application/json': {
				schema: {
					type: 'object',
					properties: {
						total: {type: 'number'},
						data: {
							type: 'array',
							items: getModelSchemaRef(PromptLog, {includeRelations: true}),
						},
					},
				},
			},
		},
	})
	async find(
		@param.filter(PromptLog) filter?: Filter<PromptLog>,
	): Promise<{total: number; data: PromptLog[]}> {
		// Ensure default ordering if not provided
		const effectiveFilter: Filter<PromptLog> = {
			...filter,
			order: filter?.order ?? ['date DESC'],
		};
		const logs = await this.promptLogRepository.find(effectiveFilter);
		const count = await this.promptLogRepository.count(effectiveFilter.where);
		return {total: count.count, data: logs};
	}

	@get('/prompt-logs/organization/{orgId}')
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['raleon-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@response(200, {
		description: 'Array of PromptLog model instances for a specific organization',
		content: {
			'application/json': {
				schema: {
					type: 'object',
					properties: {
						total: {type: 'number'},
						data: {
							type: 'array',
							items: getModelSchemaRef(PromptLog, {includeRelations: true}),
						},
					},
				},
			},
		},
	})
	async findByOrganization(
		@param.path.number('orgId') orgId: number,
		@param.filter(PromptLog) filter?: Filter<PromptLog>,
	): Promise<{total: number; data: PromptLog[]}> {
		const whereClause: Where<PromptLog> = {
			...filter?.where,
			orgId: orgId, // Force filtering by orgId (Corrected property name)
		};
		// Ensure default ordering if not provided
		const effectiveFilter: Filter<PromptLog> = {
			...filter,
			where: whereClause,
			order: filter?.order ?? ['date DESC'],
		};
		const logs = await this.promptLogRepository.find(effectiveFilter);
		const count = await this.promptLogRepository.count(whereClause);
		return {total: count.count, data: logs};
	}

	@get('/prompt-logs/{id}/content')
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['raleon-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@response(200, {
		description: 'Get the S3 content for a specific PromptLog',
		content: {
			'application/json': {
				schema: {
					type: 'object', // Assuming the S3 content is always JSON
				},
			},
		},
	})
	async getContentById(
		@param.path.number('id') id: number,
	): Promise<any> {
		const promptLog = await this.promptLogRepository.findById(id);

		if (!promptLog.prompt || typeof promptLog.prompt !== 'string' || !promptLog.prompt.startsWith('s3://')) {
			throw new HttpErrors.BadRequest(`PromptLog with ID ${id} does not contain a valid S3 key in the 'prompt' field.`);
		}

		// Extract the key from the s3:// URI format (e.g., s3://bucket-name/path/to/object.json)
		// We only need the part after the bucket name.
		const s3Uri = new URL(promptLog.prompt);
		if (s3Uri.hostname !== S3_BUCKET_NAME) {
			console.warn(`PromptLog ${id} S3 URI bucket (${s3Uri.hostname}) does not match expected bucket (${S3_BUCKET_NAME}). Proceeding anyway.`);
		}
		const s3Key = s3Uri.pathname.substring(1); // Remove leading '/'

		if (!s3Key) {
			throw new HttpErrors.InternalServerError(`Could not extract S3 key from prompt field: ${promptLog.prompt}`);
		}

		return this.fetchFromS3(s3Key);
	}
}
