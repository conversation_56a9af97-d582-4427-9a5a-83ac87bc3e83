import {inject, service} from '@loopback/core';
import {repository} from '@loopback/repository';
import {param, get, response, api, post, getModelSchemaRef, requestBody, HttpErrors, } from '@loopback/rest';
import {LoyaltyProgramRepository, OrganizationRepository, RaleonUserIdentityRepository, RaleonUserRepository} from '../repositories';
import {RaleonUserService} from '../services';
import {GuardSkipStrategy, guardStrategy, injectRaleonUserId, injectRaleonUserOrgId, skipGuardCheck} from '../interceptors';
import {authenticate} from '@loopback/authentication';
import {DevDbDataSource} from '../datasources';
import {RaleonUserIdentity} from '../models/raleon-user-identity.model';
import {ShopifyApiInvoker} from '../services/shopify/shopify-api-invoker.service';
import {uuid} from 'uuidv4';
import {LoyaltyProgramController} from './loyalty/loyalty-program.controller';
import {LoyaltyProgram, RaleonUserKeyValueStore} from '../models';
import {LoyaltyRedemptionController} from './loyalty/loyalty-redemption.controller';
import {LoyaltyEventPublisher} from '../services/event-stream/loyalty-event-publisher.service';

@guardStrategy(new GuardSkipStrategy())
@api({basePath: '/api/v1'})
export class RaleonUserController {
	constructor(
		@repository(RaleonUserRepository)
		public raleonUserRepository: RaleonUserRepository,
		@repository(RaleonUserIdentityRepository)
		private raleonUserIdentityRepository: RaleonUserIdentityRepository,
		@service(RaleonUserService)
		public raleonUserService: RaleonUserService,
		@service(ShopifyApiInvoker)
		private shopifyApiInvoker: ShopifyApiInvoker,
		@service(LoyaltyEventPublisher)
		private loyaltyEventPublisher: LoyaltyEventPublisher,
		@repository(OrganizationRepository)
		private organizationRepository: OrganizationRepository,
		@inject('controllers.LoyaltyProgramController')
		private loyaltyProgramController: LoyaltyProgramController,
		@inject('controllers.LoyaltyRedemptionController')
		private loyaltyRedemptionController: LoyaltyRedemptionController,

		@inject('datasources.dev_db') private devDbDataSource: DevDbDataSource
	) { }


	@skipGuardCheck()
	@get('/raleon-users/from-customer/{customerId}')
	@response(200, {
		description: 'Raleon User Id',
		content: {'application/json': {schema: {type: 'object', properties: {raleonUserId: {type: 'number'}}}}},
	})
	async getRaleonUserFromCustomerId(
		@param.path.number('customerId') customerId: number,
	): Promise<Record<string, number>> {
		const user = await this.raleonUserIdentityRepository.findOne({
			where: {
				identityType: 'customer_id',
				identityValue: customerId.toString(),
			}
		});
		if (!user) {
			throw new HttpErrors.NotFound('No user found');
		}
		return {raleonUserId: user.raleonUserId};
	}


	@skipGuardCheck()
	@authenticate('shopify-customer-access-token')
	@post('/raleon-users/set-key-value', {
		responses: {
			'200': {
				description: 'Returns the result of Setting the key-value',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async setKeyValue(
		@requestBody({
			content: {
				'application/json': {
					schema: {
						properties: {
							key: {
								type: 'string',
								description: 'The metafield key'
							},
							value: {
								type: 'string',
								description: 'The metafield value'
							}
						},
						required: ['key', 'value']
					}
				},
			},
		}) keyValueData: any,
		@injectRaleonUserId() raleonUserId: number,
		@injectRaleonUserOrgId() orgId: number,
	) {
		const userIdentity = await this.raleonUserIdentityRepository.findOne({
			where: {
				raleonUserId: raleonUserId,
				identityType: 'customer_id'
			}
		});
		if (!userIdentity) {
			throw new Error('User not found');
		}

		console.log('Setting key value', keyValueData.key, keyValueData.value, userIdentity.raleonUserId, orgId)
		let result = await this.raleonUserRepository.raleonUserKeyValueStores(userIdentity.raleonUserId).find({
			where: {
				key: keyValueData.key
			}
		});
		console.log('Result', result)

		if (result && result.length > 0) {
			await this.raleonUserRepository.raleonUserKeyValueStores(userIdentity.raleonUserId).patch(
				{
					key: keyValueData.key,
					raleonUserId: userIdentity.raleonUserId
				},
				{
					value: keyValueData.value
				}

			);
			console.log('Patched')
			return result;
		}

		return this.raleonUserRepository.raleonUserKeyValueStores(userIdentity.raleonUserId).create({
			key: keyValueData.key,
			value: keyValueData.value
		});
	}

	@skipGuardCheck()
	@authenticate('shopify-customer-access-token')
	@get('/raleon-users/key-value/{key}', {
		responses: {
			'200': {
				description: 'Returns the result of Setting the key-value',
				content: {
					'application/json': {
						schema: getModelSchemaRef(RaleonUserKeyValueStore),
					},
				},
			},
		},
	})
	async getKeyValue(
		@param.path.string('key') key: string,
		@injectRaleonUserId() raleonUserId: number,
	) {
		const userIdentity = await this.raleonUserIdentityRepository.findOne({
			where: {
				raleonUserId: raleonUserId,
				identityType: 'customer_id'
			}
		});
		if (!userIdentity) {
			throw new Error('User not found');
		}

		console.log('Getting key value', key, userIdentity.raleonUserId)
		let result = await this.raleonUserRepository.raleonUserKeyValueStores(userIdentity.raleonUserId).find({
			where: {
				key: key,
				raleonUserId: userIdentity.raleonUserId
			}
		});

		return result && result.length > 0 ? result[0] : [];
	}

	@skipGuardCheck()
	@authenticate('shopify-customer-access-token')
	@post('/raleon-users/update-birthday', {
		responses: {
			'200': {
				description: 'Returns the result of the redemption',
				content: {
					'application/json': {
						schema: getModelSchemaRef(RaleonUserIdentity),
					},
				},
			},
		},
	})
	async redeem(
		@requestBody({
			content: {
				'application/json': {
					schema: {
						properties: {
							birthday: {
								type: 'string',
								description: 'Birthday of the user in the format YYYY-MM-DD'
							}
						},
						required: ['birthday']
					}
				},
			},
		}) birthdayData: any,
		@injectRaleonUserId() raleonUserId: number,
	) {
		const userIdentity = await this.raleonUserIdentityRepository.findOne({
			where: {
				raleonUserId: raleonUserId,
				identityType: 'customer_id'
			}
		});
		if (!userIdentity) {
			throw new Error('User not found');
		}
		const [year, month, day] = birthdayData.birthday.split('-').map((part: string) => parseInt(part, 10));
		const birthdayDate = new Date(year, month - 1, day);
		userIdentity.birthday = birthdayDate;
		await this.raleonUserIdentityRepository.updateById(userIdentity.id, userIdentity);
		await this.loyaltyEventPublisher.publishBirthdayEvent(
			userIdentity.identityValue,
			userIdentity.orgId!,
			birthdayData.birthday,
		);
		return {message: 'Birthday updated successfully'};
	}

	@skipGuardCheck()
	@authenticate('shopify-customer-access-token')
	@post('/raleon-users/set-referrer/{code}', {
		responses: {
			'200': {
				description: 'Returns the result of the referral',
				content: {
					'application/json': {},
				},
			},
		},
	})
	async setReferrer(
		@param.path.string('code') code: string,
		@injectRaleonUserOrgId() orgId: number,
		@injectRaleonUserId() raleonUserId: number,
	) {
		const userIdentity = await this.raleonUserIdentityRepository.findOne({
			where: {
				raleonUserId: raleonUserId,
				identityType: 'customer_id'
			}
		});
		if (!userIdentity) {
			throw new HttpErrors.NotFound('User not found');
		}

		if (userIdentity.referralCode == code) {
			throw new HttpErrors.Forbidden('Users may not use thier own referral code');
		}

		if (userIdentity.signupReferrer == code) {
			return;
		}

		if (userIdentity.referralWelcomeBonusComplete) {
			throw new HttpErrors.Forbidden('Referral bonus already complete');
		}

		const orders = await this.fetchShopifyOrders(orgId, userIdentity);
		const nonCancelledOrders = orders.filter((order: any) => !order.cancelled_at);
		if (nonCancelledOrders?.length > 0) {
			throw new HttpErrors.Forbidden('Only new customers can use a referral code');
		}

		if (userIdentity.signupReferrer) {
			throw new HttpErrors.Forbidden('Referral already assigned');
		}

		const referringIdentity = await this.raleonUserIdentityRepository.findOne({
			where: {
				referralCode: code,
				identityType: 'customer_id'
			}
		});
		if (!referringIdentity) {
			throw new HttpErrors.NotFound('Referring user not found');
		}

		if (referringIdentity.raleonUserId == userIdentity.raleonUserId || referringIdentity.identityValue == userIdentity.identityValue) {
			throw new HttpErrors.Forbidden('Referring user cannot be the same as the referred user');
		}

		const livePrograms: LoyaltyProgram[] = await this.loyaltyProgramController.findLive(raleonUserId, userIdentity.identityValue as any as number, orgId);
		const programs = livePrograms.filter(x => x.referralsActive);
		if (!programs.length) {
			throw new HttpErrors.Forbidden('No referral programs are active');
		}

		this.grantReferralWelcomeBonus(orgId, userIdentity, programs).catch();

		userIdentity.signupReferrer = code;
		userIdentity.referralWelcomeBonusComplete = true;
		await this.raleonUserIdentityRepository.updateById(userIdentity.id, userIdentity);

		return {message: 'Referral updated successfully'};
	}

	@skipGuardCheck()
	@post('/customers/{customerId}/unsubscribe', {
		responses: {
			'200': {
				description: 'If unsubscribe was successful',
				content: {
					'application/json': {},
				},
			},
		},
	})
	async unsubscribe(
		@param.path.string('customerId') customerId: string,
	) {
		const userIdentity = await this.raleonUserIdentityRepository.findOne({
			where: {
				identityValue: customerId,
				identityType: 'customer_id'
			}
		});
		if (!userIdentity) {
			throw new HttpErrors.NotFound('User not found');
		}
		userIdentity.unsubscribed = true;
		await this.raleonUserIdentityRepository.updateById(userIdentity.id, userIdentity);
		return { statusCode: 200, body: `Successfully unsubscribed customer ${customerId}` };
	}

	private async grantReferralWelcomeBonus(orgId: number, referredIdentity: RaleonUserIdentity, programs: LoyaltyProgram[]) {
		const referralCampaigns = programs.map(x => x.loyaltyCampaigns).reduce((a, b) => a.concat(b), []).filter(x => x);
		const referralEarns = referralCampaigns.map(x => x.loyaltyEarns).reduce((a, b) => a.concat(b), []).filter(x => x);
		const referredCustomerBonusConditions = referralEarns.map(x => x.earnConditions).reduce((a, b) => a.concat(b), []).filter(x => x && x.type === 'referred-customer-bonus');

		const earnData = referredCustomerBonusConditions.map(c => ({
			earnId: c.loyaltyEarnId
		}));

		return Promise.all(earnData.map(async earn => this.loyaltyRedemptionController.grantToCustomer(earn, referredIdentity.raleonUserId!, orgId, this.devDbDataSource, true)));
	}

	private async fetchShopifyOrders(orgId: number, userIdentity: any): Promise<any[]> {
		if (userIdentity) {
			try {
				let customerId = userIdentity.identityValue;
				const response = await this.shopifyApiInvoker.invokeAdminApi(
					orgId,
					'/get-orders?customerId=' + customerId,
					'GET',
				);
				return response.orders;
			} catch (error) {
				console.error('Error fetching orders from Shopify:', error);
				return [];
			}
		}
		return [];
	}


	@skipGuardCheck()
	@authenticate('shopify-customer-access-token')
	@post('/raleon-users/trigger-referral-email', {
		responses: {
			'200': {
				description: 'Returns the users referral link',
				content: {
					'application/json': {},
				},
			},
		},
	})
	async sendReferralEmail(
		@requestBody({
			content: {
				'application/json': {
					schema: {
						properties: {
							email: {
								type: 'string',
								description: 'email of the friend to refer'
							},
							referralLink: {
								type: 'string',
								description: 'referral link to include in the email'
							}
						},
						required: ['email']
					}
				},
			},
		}) referralInfo: any,
		@injectRaleonUserOrgId() orgId: number,
		@injectRaleonUserId() raleonUserId: number,
	) {
		const userIdentity = await this.raleonUserIdentityRepository.findOne({
			where: {
				raleonUserId: raleonUserId,
				identityType: 'customer_id'
			}
		});
		if (!userIdentity) {
			throw new HttpErrors.NotFound('User not found');
		}

		const org = await this.organizationRepository.findById(orgId);
		if (!org) {
			throw new HttpErrors.NotFound('Organization not found');
		}

		await this.loyaltyEventPublisher.publishReferralReceivedEvent(
			userIdentity?.identityValue!,
			orgId,
			referralInfo.email,
			referralInfo.referralLink,
		);
	}

	@skipGuardCheck()
	@authenticate('shopify-customer-access-token')
	@post('/raleon-user', {
		responses: {
			'200': {
				description: 'Returns the users referral link',
				content: {
					'application/json': {},
				},
			},
		},
	})
	async createRaleonUserAndIdentity(
		@injectRaleonUserId() raleonUserId: number,
	) {
		//the shopify-customer-access-token authentication will create the user if one does not exist
		//code below is just a validation that it did create the user
		const userIdentity = await this.raleonUserIdentityRepository.findOne({
			where: {
				raleonUserId: raleonUserId,
				identityType: 'customer_id'
			}
		});
		if (!userIdentity) {
			throw new HttpErrors.NotFound('User not found');
		}
		return userIdentity;
	}

	@skipGuardCheck()
	@authenticate('shopify-customer-access-token')
	@get('/raleon-users/own-referral-link', {
		responses: {
			'200': {
				description: 'Returns the users referral link',
				content: {
					'application/json': {},
				},
			},
		},
	})
	async getOwnReferralLink(
		@injectRaleonUserOrgId() orgId: number,
		@injectRaleonUserId() raleonUserId: number,
	) {
		const userIdentity = await this.raleonUserIdentityRepository.findOne({
			where: {
				raleonUserId: raleonUserId,
				identityType: 'customer_id'
			}
		});
		if (!userIdentity) {
			throw new HttpErrors.NotFound('User not found');
		}

		const org = await this.organizationRepository.findById(orgId);
		if (!org) {
			throw new HttpErrors.NotFound('Organization not found');
		}

		const livePrograms = await this.loyaltyProgramController.findLive(raleonUserId, userIdentity.identityValue as any as number, orgId);
		const programs = livePrograms.filter(x => x.referralsActive);
		if (!programs.length) {
			throw new HttpErrors.NotFound('No referral programs are active');
		}

		if (!userIdentity.referralCode) {
			userIdentity.referralCode = uuid();
			await this.raleonUserIdentityRepository.updateById(userIdentity.id, userIdentity);
		}

		const storeInfo = await this.shopifyApiInvoker.invokeAdminApi(
			orgId,
			'/get-shop-info',
			'GET',
		);

		if (!storeInfo) {
			return `${org.externalDomain?.startsWith('http') ? '' : 'https://'}${org.externalDomain}?referral_code=${userIdentity.referralCode}`;
		}

		const url = storeInfo.shop.primaryDomain.url;
		return `${url?.startsWith('http') ? '' : 'https://'}${url}?referral_code=${userIdentity.referralCode}`;
	}

	// @skipGuardCheck()
	// @authenticate('shopify-customer-access-token')
	// @get('/raleon-users/own-referral-code', {
	// 	responses: {
	// 		'200': {
	// 			description: 'Returns the users referral code',
	// 			content: {
	// 				'application/json': {},
	// 			},
	// 		},
	// 	},
	// })
	// async getOwnReferralCode(
	// 	@injectRaleonUserOrgId() orgId: number,
	// 	@injectRaleonUserId() raleonUserId: number,
	// ) {
	// 	const userIdentity = await this.raleonUserIdentityRepository.findOne({
	// 		where: {
	// 			raleonUserId: raleonUserId,
	// 			identityType: 'customer_id'
	// 		}
	// 	});
	// 	if (!userIdentity) {
	// 		throw new HttpErrors.NotFound('User not found');
	// 	}

	// 	return userIdentity.referralCode;
	// }

	// @post('/raleon-users')
	// @response(200, {
	// 	description: 'RaleonUser model instance',
	// 	content: {'application/json': {schema: getModelSchemaRef(RaleonUser)}},
	// })
	// async create(
	// 	@requestBody({
	// 		content: {
	// 			'application/json': {
	// 				schema: getModelSchemaRef(RaleonUser, {
	// 					title: 'NewRaleonUser',
	// 					exclude: ['id'],
	// 				}),
	// 			},
	// 		},
	// 	})
	// 	raleonUser: Omit<RaleonUser, 'id'>,
	// ): Promise<RaleonUser> {
	// 	return this.raleonUserRepository.create(raleonUser);
	// }

	// @post('/raleon-users/associate')
	// @response(200, {
	// 	description: 'RaleonUser identity association instance',
	// 	content: {'application/json': {schema: getModelSchemaRef(RaleonUser)}},
	// })
	// async associateIdentities(
	// 	@requestBody({
	// 		content: {
	// 			'application/json': {
	// 				schema: {
	// 					type: 'array',
	// 					items: getModelSchemaRef(RaleonUserIdentity, {
	// 						title: 'NewRaleonUserIdentity',
	// 						exclude: ['id'],
	// 					}),
	// 				},
	// 			},
	// 		},
	// 	})
	// 	identitiesToAssociate: Array<Omit<RaleonUserIdentity, 'id'>>,
	// 	@param.query.number('raleonUserId', {required: false}) raleonUserId?: number,
	// ): Promise<RaleonUser> {
	// 	return this.raleonUserService.getUserFromIdentities(identitiesToAssociate, raleonUserId);
	// }

	// @get('/raleon-users/count')
	// @response(200, {
	// 	description: 'RaleonUser model count',
	// 	content: {'application/json': {schema: CountSchema}},
	// })
	// async count(
	// 	@param.where(RaleonUser) where?: Where<RaleonUser>,
	// ): Promise<Count> {
	// 	return this.raleonUserRepository.count(where);
	// }

	// @get('/raleon-users')
	// @response(200, {
	// 	description: 'Array of RaleonUser model instances',
	// 	content: {
	// 		'application/json': {
	// 			schema: {
	// 				type: 'array',
	// 				items: getModelSchemaRef(RaleonUser, {includeRelations: true}),
	// 			},
	// 		},
	// 	},
	// })
	// async find(
	// 	@param.filter(RaleonUser) filter?: Filter<RaleonUser>,
	// ): Promise<RaleonUser[]> {
	// 	return this.raleonUserRepository.find(filter);
	// }

	// @patch('/raleon-users')
	// @response(200, {
	// 	description: 'RaleonUser PATCH success count',
	// 	content: {'application/json': {schema: CountSchema}},
	// })
	// async updateAll(
	// 	@requestBody({
	// 		content: {
	// 			'application/json': {
	// 				schema: getModelSchemaRef(RaleonUser, {partial: true}),
	// 			},
	// 		},
	// 	})
	// 	raleonUser: RaleonUser,
	// 	@param.where(RaleonUser) where?: Where<RaleonUser>,
	// ): Promise<Count> {
	// 	return this.raleonUserRepository.updateAll(raleonUser, where);
	// }

	// @get('/raleon-users/{id}')
	// @response(200, {
	// 	description: 'RaleonUser model instance',
	// 	content: {
	// 		'application/json': {
	// 			schema: getModelSchemaRef(RaleonUser, {includeRelations: true}),
	// 		},
	// 	},
	// })
	// async findById(
	// 	@param.path.number('id') id: number,
	// 	@param.filter(RaleonUser, {exclude: 'where'}) filter?: FilterExcludingWhere<RaleonUser>
	// ): Promise<RaleonUser> {
	// 	return this.raleonUserRepository.findById(id, filter);
	// }

	// @patch('/raleon-users/{id}')
	// @response(204, {
	// 	description: 'RaleonUser PATCH success',
	// })
	// async updateById(
	// 	@param.path.number('id') id: number,
	// 	@requestBody({
	// 		content: {
	// 			'application/json': {
	// 				schema: getModelSchemaRef(RaleonUser, {partial: true}),
	// 			},
	// 		},
	// 	})
	// 	raleonUser: RaleonUser,
	// ): Promise<void> {
	// 	await this.raleonUserRepository.updateById(id, raleonUser);
	// }

	// @put('/raleon-users/{id}')
	// @response(204, {
	// 	description: 'RaleonUser PUT success',
	// })
	// async replaceById(
	// 	@param.path.number('id') id: number,
	// 	@requestBody() raleonUser: RaleonUser,
	// ): Promise<void> {
	// 	await this.raleonUserRepository.replaceById(id, raleonUser);
	// }

	// @del('/raleon-users/{id}')
	// @response(204, {
	// 	description: 'RaleonUser DELETE success',
	// })
	// async deleteById(@param.path.number('id') id: number): Promise<void> {
	// 	await this.raleonUserRepository.deleteById(id);
	// }
}
