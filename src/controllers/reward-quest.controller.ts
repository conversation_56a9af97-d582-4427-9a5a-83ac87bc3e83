import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {GuardMultiHopPropertyStrategy, guardStrategy, OrgGuardMultiHopPropertyStrategy, skipGuardCheck} from '../interceptors';
import {
  Reward,
  Quest,
  Campaign,
} from '../models';
import {QuestRepository, RewardRepository} from '../repositories';

@guardStrategy(new OrgGuardMultiHopPropertyStrategy<Reward, Quest, Campaign>({
	repositoryClass: RewardRepository,
	firstHopIdPropertyName: 'questId',
	firstHopRepositoryClass: QuestRepository,
	inclusionChainAfterFirstHop: {relation: "campaign"},
	lastHopOrgIdPropertyName: 'orgId',
}))
export class RewardQuestController {
  constructor(
    @repository(RewardRepository)
    public rewardRepository: RewardRepository,
  ) { }

  @get('/rewards/{id}/quest', {
    responses: {
      '200': {
        description: 'Quest belonging to <PERSON><PERSON>',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Quest)},
          },
        },
      },
    },
  })
  @skipGuardCheck() //TODO: update
  async getQuest(
    @param.path.number('id') id: typeof Reward.prototype.id,
  ): Promise<Quest> {
    return this.rewardRepository.quest(id);
  }
}
