import {
	Count,
	CountSchema,
	Filter,
	FilterExcludingWhere,
	repository,
	Where,
} from '@loopback/repository';
import {
	post,
	param,
	get,
	getModelSchemaRef,
	patch,
	put,
	del,
	requestBody,
	response,
	api,
} from '@loopback/rest';
import {SupportedCurrencies, Currency} from '../models';
import {CurrencyRepository, SupportedCurrenciesRepository} from '../repositories';
import {GuardSkipStrategy, guardStrategy, injectUserOrgId, skipGuardCheck} from '../interceptors';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../services';

@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())

export class CurrencyController {
	constructor(
		@repository(CurrencyRepository)
		public currencyRepository: CurrencyRepository,
		@repository(SupportedCurrenciesRepository)
		public supportedCurrenciesRepository: SupportedCurrenciesRepository,
	) { }

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/organization/primary-currency')
	@response(200, {
		description: 'Returns the primary currency for the organization',
		content: {
			'application/json': {
				schema: {
					type: 'object',
					items: getModelSchemaRef(SupportedCurrencies),
				},
			},
		},
	})
	async find(
		@injectUserOrgId() orgId: number,
	): Promise<SupportedCurrencies> {
		//Lets first check to see if there is one already set
		const currency = await this.currencyRepository.findOne({
			where: {organizationId: orgId},
			include: [
				{relation: 'supportedCurrencies'}
			]
		});
		if (currency) {
			return (currency as any).supportedCurrencies;
		}

		//If not, lets create one
		await this.currencyRepository.create({
			organizationId: orgId,
			supportedCurrenciesId: 1
		});

		//Now lets return the default currency
		return this.supportedCurrenciesRepository.findById(1);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/organization/primary-currency')
	@response(200, {
		description: 'Create or update a currency record',
		content: {'application/json': {schema: getModelSchemaRef(Currency)}},
	})
	async createOrUpdateCurrency(
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(SupportedCurrencies, {partial: true}),
				},
			},
		})
		currencyData: SupportedCurrencies,
		@injectUserOrgId() orgId: number,
	): Promise<Currency> {
		const existingCurrency = await this.currencyRepository.findOne({
			where: {organizationId: orgId}
		});

		if (existingCurrency) {
			// Update existing record
			await this.currencyRepository.updateById(existingCurrency.id, {
				supportedCurrenciesId: currencyData.id
			});
			return this.currencyRepository.findById(existingCurrency.id);
		} else {
			// Create new record
			const createdCurrency = await this.currencyRepository.create({
				organizationId: orgId,
				supportedCurrenciesId: currencyData.id
			});
			return createdCurrency;
		}
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/currency/supported-currencies')
	@response(200, {
		description: 'Array of Supported Currency model instances',
		content: {
			'application/json': {
				schema: {
					type: 'array',
					items: getModelSchemaRef(SupportedCurrencies, {includeRelations: true}),
				},
			},
		},
	})
	async getAllSupportedCurrencies(): Promise<SupportedCurrencies[]> {
		return this.supportedCurrenciesRepository.find();
	}
}
