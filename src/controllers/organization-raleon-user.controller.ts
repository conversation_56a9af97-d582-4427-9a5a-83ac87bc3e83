import {
	Count,
	CountSchema,
	Filter,
	repository,
	Where,
} from '@loopback/repository';
import {
	del,
	get,
	getModelSchemaRef,
	getWhereSchemaFor,
	param,
	patch,
	post,
	requestBody,
	api
} from '@loopback/rest';
import {
	LoyaltyRewardDefinition,
	Organization,
	RaleonUser,
	RaleonUserIdentity,
} from '../models';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../services/basic.authorizor';
import {CurrencyRepository, LoyaltyCurrencyBalanceRepository, LoyaltyCurrencyRepository, LoyaltyProgramRepository, LoyaltyRewardDefinitionRepository, OrganizationRepository, RaleonUserIdentityRepository, RaleonUserRepository} from '../repositories';
import {injectGuardedFilter, injectUserOrgId, guardStrategy} from '../interceptors/crud-guard.interceptor';
import {OrgGuardPropertyStrategy} from '../interceptors/crud-guard.interceptor';
import {restrictReadsWithGuard} from '../interceptors/crud-guard.interceptor';
import {skipGuardCheck} from '../interceptors/crud-guard.interceptor';
import {modelForGuard} from '../interceptors/crud-guard.interceptor';
import {modelIdForGuard} from '../interceptors/crud-guard.interceptor';
import {service} from '@loopback/core';
import {ShopifyApiInvoker} from '../services/shopify/shopify-api-invoker.service';
import {ShopperInfo} from '../services/shoppers/shopper-info.service';
import {LoyaltyBalanceManager} from '../services/loyalty/loyalty-balance-manager.service';
import {convertCurrencyPlaceholdersToValues} from './admin-ui.controller';

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardPropertyStrategy<Organization>({
	orgIdModelPropertyName: 'id',
	repositoryClass: OrganizationRepository
}))
export class OrganizationRaleonUserController {
	constructor(
		@repository(OrganizationRepository) protected organizationRepository: OrganizationRepository,
		@repository(RaleonUserIdentityRepository) protected raleonUserIdentityRepository: RaleonUserIdentityRepository,
		@repository(LoyaltyRewardDefinitionRepository) protected loyaltyRewardDefinitionRepository: LoyaltyRewardDefinitionRepository,
		@repository(LoyaltyCurrencyRepository) protected loyaltyCurrencyRepository: LoyaltyCurrencyRepository,
		@repository(LoyaltyCurrencyBalanceRepository) protected loyaltyCurrencyBalanceRepository: LoyaltyCurrencyBalanceRepository,
		@repository(CurrencyRepository) protected currencyRepository: CurrencyRepository,
		@repository(LoyaltyProgramRepository) protected loyaltyProgramRepository: LoyaltyProgramRepository,
		@repository(RaleonUserRepository) protected raleonUserRepository: RaleonUserRepository,
		@service(ShopifyApiInvoker) protected shopifyApiInvoker: ShopifyApiInvoker,
		@service(ShopperInfo) protected shopperInfo: ShopperInfo,
		@service(LoyaltyBalanceManager) protected loyaltyBalanceManager: LoyaltyBalanceManager
	) { }

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/organizations/{id}/raleon-users', {
		responses: {
			'200': {
				description: 'Array of Organization has many RaleonUser',
				content: {
					'application/json': {
						schema: {type: 'array', items: getModelSchemaRef(RaleonUser)},
					},
				},
			},
		},
	})
	async find(
		@modelIdForGuard(Organization)
		@param.path.number('id') id: number,
		@param.query.object('filter') filter?: Filter<RaleonUser>,
	): Promise<RaleonUser[]> {
		return this.organizationRepository.raleonUsers(id).find(filter);
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/organizations/shoppers/count', {
		responses: {
			'200': {
				description: 'Array of Organization has many RaleonUser',
				content: {
					'application/json': {
						schema: {type: 'array', items: getModelSchemaRef(RaleonUser)},
					},
				},
			},
		},
	})
	async getShopperCount(
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		let countData = await this.raleonUserIdentityRepository.count({orgId: orgId, identityType: 'customer_id'});
		return countData;
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/organizations/shoppers', {
		responses: {
			'200': {
				description: 'Array of Organization has many RaleonUser',
				content: {
					'application/json': {
						schema: {type: 'array', items: getModelSchemaRef(RaleonUser)},
					},
				},
			},
		},
	})
	async getShoppers(
		@param.query.number('page') page: number,
		@param.query.number('limit') limit: number,
		@param.query.string('search') search: string,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		return this.shopperInfo.getShopperInfo(orgId, page, limit, search);
	}

	//! This api should be extended to return all things for a specific customer based on raleonuseridentity id
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/organizations/shopper-info', {
		responses: {
			'200': {
				description: 'Specific shopper info',
				content: {
					'application/json': {
						schema: {type: 'object', items: getModelSchemaRef(RaleonUser)},
					},
				},
			},
		},
	})
	async getShopperInfo(
		@param.query.number('id') id: number,
		@injectUserOrgId() orgId: number,
	): Promise<any> {

		let shopifyIds = [];
		let response: any = null;
		let filter: Filter<RaleonUserIdentity> = {
			where: {
				id: id,
				orgId: orgId,
				identityType: 'customer_id'
			},
			include: [
				{
					relation: 'vipTier'
				},
				{
					relation: 'raleonUser',
					scope: {
						include: [
							{
								relation: 'loyaltyCurrencyBalance',
								scope: {
									include: [
										{
											relation: 'loyaltyCurrencyTxLogs',
											scope: { order: ['date DESC'] }
										}
									]
								}
							},
							{
								relation: 'loyaltyRewardLogs',
								scope: {
									order: ['date DESC'],
									include: [
										{
											relation: 'inventoryCoupon',
											scope: {
												include: [
													{
														relation: 'rewardCoupon'
													}
												]
											}
										}
									]
								}
							}
						]
					}
				}
			]
		};

		let ralIUsers =  await this.raleonUserIdentityRepository.find(filter);
		if(ralIUsers.length === 0) {
			return {};
		}

		shopifyIds = ralIUsers.map((ralIUser) => ralIUser.identityValue);
		const path = `/shopper-info?ids=${shopifyIds.join(',')}`;
		response = await this.shopifyApiInvoker.invokeAdminApi(orgId, path, 'GET');

		let shopifyCustomers = response?.customers;
		if(!shopifyCustomers) {
			return {};
		}

		//Lets go through and get all loyaltyrewarddefinition and their corresponding coupon so we can log out rewards properly
		//Each loyaltyRewardLog has a rewardDefinitionId, we need to get the coupon for that rewardDefinitionId
		//We will then add the coupon to the rewardLog object, gather the ids of the rewardDefinitions and get the coupons
		// let rewardDefinitionIds: number[] = [];
		// ralIUsers.forEach((ralIUser) => {
		// 	ralIUser.raleonUser.loyaltyRewardLogs.forEach((rewardLog) => {
		// 		rewardDefinitionIds.push(rewardLog.loyaltyRewardDefinitionId);
		// 	});
		// });

		// let rewardDefinitions = [];
		// let rewardDefinitionFilter: Filter<LoyaltyRewardDefinition> = {
		// 	where: {
		// 		id: {
		// 			inq: rewardDefinitionIds
		// 		}
		// 	},
		// 	include: [
		// 		{
		// 			relation: 'inventoryCoupon'
		// 		}
		// 	]
		// };

		// try {
		// 	rewardDefinitions = await this.loyaltyRewardDefinitionRepository.find(rewardDefinitionFilter);
		// } catch(e) {
		// 	console.log('Error getting reward definitions', e);
		// }

		//Grab Currency Id in case we need to create a new loyalty currency balance record
		let programs = await this.loyaltyProgramRepository.find({where: {orgId: orgId}});
		let program = programs[0];
		let currency = await this.loyaltyCurrencyRepository.find({where: {loyaltyProgramId: program.id}});
		const orgCurrency = await this.currencyRepository.findOne({
			where: { organizationId: orgId },
			include: [ { relation: 'supportedCurrencies' } ]
		});



		const finalData = await Promise.all(ralIUsers.map(async (ralIUser) => {
			const shopifyData = shopifyCustomers.find((shopifyUser: {id: number;}) => shopifyUser.id.toString() === ralIUser.identityValue);
			const raleonUser = ralIUser.raleonUser;

			if(!raleonUser.loyaltyCurrencyBalance) {
				let newRecord = await this.loyaltyCurrencyBalanceRepository.create({
					loyaltyCurrencyId: currency[0]?.id,
					raleonUserId: raleonUser.id,
				});
				raleonUser.loyaltyCurrencyBalance = newRecord;
				console.log('Created new loyalty currency balance record', newRecord);
			}

			let updatedRewardLogs = [];
			if (raleonUser?.loyaltyRewardLogs?.length) {
				updatedRewardLogs = raleonUser.loyaltyRewardLogs.map((log : any) => {
					if (log.inventoryCoupon && log.inventoryCoupon.rewardCoupon) {
					  log.inventoryCoupon.rewardCoupon.name = convertCurrencyPlaceholdersToValues(
						log.inventoryCoupon.rewardCoupon.name,
						orgCurrency!
					  );
					}
					return log;
				});
			}

			return {
				id: raleonUser.id,
				shopify_id: ralIUser.identityValue,
				first_name: shopifyData.first_name,
				last_name: shopifyData.last_name,
				loyalty_score: ralIUser.loyaltyScore,
				loyalty_tier: ralIUser.vipTier?.name || 'None',
				orders_count: shopifyData.orders_count,
				total_spent: shopifyData.total_spent,
				loyalty_points: raleonUser.loyaltyCurrencyBalance?.balance,
				email: shopifyData.email,
				created_at: shopifyData.created_at,
				currency_balance_id: raleonUser.loyaltyCurrencyBalance?.id,
				currency_id: raleonUser.loyaltyCurrencyBalance?.loyaltyCurrencyId,
				points_log: raleonUser.loyaltyCurrencyBalance?.loyaltyCurrencyTxLogs,
				rewards_log: updatedRewardLogs,
				birthday: ralIUser.birthday,
			};
		}));


		return finalData && finalData.length > 0 ? finalData[0] : {};
	}


	//! This api should be extended to return all things for a specific customer based on raleonuseridentity id
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/organizations/shopper-info/{id}/birthday-change', {
		responses: {
			'200': {
				description: 'Specific shopper info',
				content: {
					'application/json': {
						schema: {type: 'object', items: getModelSchemaRef(RaleonUser)},
					},
				},
			},
		},
	})
	async updateShopperBirthday(
		@param.path.number('id') id: number,
		@requestBody({
			content: {
				'application/json': {
					schema: {}
				},
			},
		}) payload: {birthday: string},

		@injectUserOrgId() orgId: number,
	): Promise<any> {
		const ralIUsers =  await this.raleonUserIdentityRepository.find({
			where: {
				id: id,
				orgId: orgId,
				identityType: 'customer_id'
			},
		});
		if(ralIUsers.length === 0) {
			return {};
		}

		ralIUsers[0].birthday = new Date(payload.birthday);

		return this.raleonUserIdentityRepository.update(ralIUsers[0]);
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})

	@post('/organizations/import-init-shopper', {
		responses: {
			'200': {
				description: 'Import Initiated',
				content: {
					'application/json': {
						schema: {type: 'object', items: getModelSchemaRef(RaleonUser)},
					},
				},
			},
		},
	})
	async importInitShopper(
		@requestBody({
			content: {
				'application/json': {
					schema: {}
				},
			},
		}) payload: {shoppers: {points: number, shopify_id: string, description: string}[]},
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		const program = await this.loyaltyProgramRepository.findOne(
			{
				where:
				{
					orgId: orgId
				},
				include: [
					{
						relation: 'loyaltyCurrencies'
					}
				]
			},
		);

		let currencyId = program?.loyaltyCurrencies[0].id;
		let successfulCount = 0;
		let unsuccessfulCount = 0;
		//remove any null shopify ids
		payload.shoppers = payload.shoppers.filter((shopper) => shopper.shopify_id !== null);
		let missingShopperIds = payload.shoppers.map((shopper) => shopper.shopify_id).join(',');
		const validMissingShoppers = await this.shopifyApiInvoker.invokeAdminApi(
			orgId,
			`/shopper-info?ids=${missingShopperIds}`,
			'GET',
		);

		if(!validMissingShoppers || !validMissingShoppers.customers) {
			return {successfulCount: 0, unsuccessfulCount: payload.shoppers.length};
		}


		for(let i = 0; i < validMissingShoppers.customers.length; i++) {
			let curShopper = validMissingShoppers.customers[i];
			//find the shopper in the original payload
			let shopperData = payload.shoppers.find((shopper) => {
				// Log the current comparison
				return shopper.shopify_id.toString() === curShopper.id.toString();
			});

			if (!shopperData) {
				console.log(`Shopper not found for ID: ${curShopper.Id}`);
				continue; // Skip to the next iteration if not found
			}
			try{
				let finalIdentity: any = await this.raleonUserIdentityRepository.findOne({
					where: {
						orgId: orgId,
						identityType: 'customer_id',
						identityValue: shopperData?.shopify_id
					}
				});

				if(!finalIdentity) {
					let raleonUser = await this.raleonUserRepository.create({});
					let newUser = {
						orgId: orgId,
						identityType: 'customer_id',
						identityValue: shopperData?.shopify_id,
						raleonUserId: raleonUser.id
					}
					finalIdentity = await this.raleonUserIdentityRepository.create(newUser);
				}



				if(finalIdentity) {
					successfulCount++;
					await this.loyaltyBalanceManager.updateBalanceAndLog(currencyId, {
						raleonUserId: finalIdentity.raleonUserId,
						balanceChange: shopperData?.points,
						info: shopperData?.description
					})
					successfulCount++;
				}
			} catch(e) {
				unsuccessfulCount++;
			}

		}
		return {successfulCount: successfulCount, unsuccessfulCount: unsuccessfulCount};
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/organizations/{id}/raleon-users', {
		responses: {
			'200': {
				description: 'Organization model instance',
				content: {'application/json': {schema: getModelSchemaRef(RaleonUser)}},
			},
		},
	})
	async create(
		@modelIdForGuard(Organization)
		@param.path.number('id') id: typeof Organization.prototype.id,
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(RaleonUser, {
						title: 'NewRaleonUserInOrganization',
						exclude: ['id'],
					}),
				},
			},
		}) raleonUser: Omit<RaleonUser, 'id'>,
	): Promise<RaleonUser> {
		return this.organizationRepository.raleonUsers(id).create(raleonUser);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@patch('/organizations/{id}/raleon-users', {
		responses: {
			'200': {
				description: 'Organization.RaleonUser PATCH success count',
				content: {'application/json': {schema: CountSchema}},
			},
		},
	})
	async patch(
		@modelIdForGuard(Organization)
		@param.path.number('id') id: number,
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(RaleonUser, {partial: true}),
				},
			},
		})
		raleonUser: Partial<RaleonUser>,
		@param.query.object('where', getWhereSchemaFor(RaleonUser)) where?: Where<RaleonUser>,
	): Promise<Count> {
		return this.organizationRepository.raleonUsers(id).patch(raleonUser, where);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@del('/organizations/{id}/raleon-users', {
		responses: {
			'200': {
				description: 'Organization.RaleonUser DELETE success count',
				content: {'application/json': {schema: CountSchema}},
			},
		},
	})
	async delete(
		@modelIdForGuard(Organization)
		@param.path.number('id') id: number,
		@param.query.object('where', getWhereSchemaFor(RaleonUser)) where?: Where<RaleonUser>,
	): Promise<Count> {
		return this.organizationRepository.raleonUsers(id).delete(where);
	}
}
