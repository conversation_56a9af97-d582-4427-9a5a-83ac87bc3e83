import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
	api,
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
	OrganizationPlannerPlan,
  PlannerCampaign,
  PlannerCampaignImage,
  PlannerPlanVersion,
} from '../models';
import {PlannerCampaignRepository, PlannerPlanVersionRepository} from '../repositories';
import {GuardSkipStrategy, guardStrategy, modelIdForGuard, OrgGuardMultiHopPropertyStrategy, OrgGuardPropertyStrategy, skipGuardCheck} from '../interceptors';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../services/basic.authorizor';


@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
// @guardStrategy(new OrgGuardMultiHopPropertyStrategy<PlannerCampaign, PlannerPlanVersion, OrganizationPlannerPlan>({
// 	repositoryClass: PlannerCampaignRepository,
// 	firstHopIdPropertyName: 'plannerPlanVersionId',
// 	firstHopRepositoryClass: PlannerPlanVersionRepository,
// 	inclusionChainAfterFirstHop: {relation: "organizationPlannerPlan"},
// 	lastHopOrgIdPropertyName: 'organizationId'
// }))
export class PlannerCampaignPlannerCampaignImageController {
  constructor(
    @repository(PlannerCampaignRepository) protected plannerCampaignRepository: PlannerCampaignRepository,
  ) { }

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer', 'raleon-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
  @get('/planner-campaigns/{id}/planner-campaign-images', {
    responses: {
      '200': {
        description: 'Array of PlannerCampaign has many PlannerCampaignImage',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(PlannerCampaignImage)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id')
	id: number,
    @param.query.object('filter') filter?: Filter<PlannerCampaignImage>,
  ): Promise<PlannerCampaignImage[]> {
    return this.plannerCampaignRepository.plannerCampaignImages(id).find(filter);
  }

  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer', 'raleon-admin'],
	  voters: [basicAuthorization],
  })
  @skipGuardCheck()
  @post('/planner-campaigns/{id}/planner-campaign-images', {
    responses: {
      '200': {
        description: 'PlannerCampaign model instance',
        content: {'application/json': {schema: getModelSchemaRef(PlannerCampaignImage)}},
      },
    },
  })
  async create(
    @param.path.number('id')
	id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PlannerCampaignImage, {
            title: 'NewPlannerCampaignImageInPlannerCampaign',
            exclude: ['id'],
            optional: ['plannerCampaignId']
          }),
        },
      },
    }) plannerCampaignImage: Omit<PlannerCampaignImage, 'id'>,
  ): Promise<PlannerCampaignImage> {
    return this.plannerCampaignRepository.plannerCampaignImages(id).create(plannerCampaignImage);
  }

  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer', 'raleon-admin'],
	  voters: [basicAuthorization],
  })
  @skipGuardCheck()
  @patch('/planner-campaigns/{id}/planner-campaign-images', {
    responses: {
      '200': {
        description: 'PlannerCampaign.PlannerCampaignImage PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id')
	id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PlannerCampaignImage, {partial: true}),
        },
      },
    })
    plannerCampaignImage: Partial<PlannerCampaignImage>,
    @param.query.object('where', getWhereSchemaFor(PlannerCampaignImage)) where?: Where<PlannerCampaignImage>,
  ): Promise<Count> {
    return this.plannerCampaignRepository.plannerCampaignImages(id).patch(plannerCampaignImage, where);
  }

  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer', 'raleon-admin'],
	  voters: [basicAuthorization],
  })
  @skipGuardCheck()
  @del('/planner-campaigns/{id}/planner-campaign-images', {
    responses: {
      '200': {
        description: 'PlannerCampaign.PlannerCampaignImage DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id')
	id: number,
    @param.query.object('where', getWhereSchemaFor(PlannerCampaignImage)) where?: Where<PlannerCampaignImage>,
  ): Promise<Count> {
    return this.plannerCampaignRepository.plannerCampaignImages(id).delete(where);
  }
}
