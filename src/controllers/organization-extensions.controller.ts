import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
	api,
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  Organization,
  Extensions,
} from '../models';
import {ExtensionsRepository, OrganizationRepository} from '../repositories';
import {GuardSkipStrategy, guardStrategy, OrgGuardPropertyStrategy, restrictReadsWithGuard} from '../interceptors';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../services';
@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardPropertyStrategy<Extensions>({
	orgIdModelPropertyName: 'organizationId',
	repositoryClass: ExtensionsRepository
}))

export class OrganizationExtensionsController {
  constructor(
    @repository(OrganizationRepository) protected organizationRepository: OrganizationRepository,
  ) { }

  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  @restrictReadsWithGuard({ plural: true })
  @get('/organizations/{id}/extensions', {
    responses: {
      '200': {
        description: 'Array of Organization has many Extensions',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Extensions)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<Extensions>,
  ): Promise<Extensions[]> {
    return this.organizationRepository.extensions(id).find(filter);
  }

  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  @restrictReadsWithGuard({ plural: true })
  @post('/organizations/{id}/extensions', {
    responses: {
      '200': {
        description: 'Organization model instance',
        content: {'application/json': {schema: getModelSchemaRef(Extensions)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof Organization.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Extensions, {
            title: 'NewExtensionsInOrganization',
            exclude: ['id'],
            optional: ['organizationId']
          }),
        },
      },
    }) extensions: Omit<Extensions, 'id'>,
  ): Promise<Extensions> {
    return this.organizationRepository.extensions(id).create(extensions);
  }

  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  @restrictReadsWithGuard({ plural: true })
  @patch('/organizations/{id}/extensions', {
    responses: {
      '200': {
        description: 'Organization.Extensions PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Extensions, {partial: true}),
        },
      },
    })
    extensions: Partial<Extensions>,
    @param.query.object('where', getWhereSchemaFor(Extensions)) where?: Where<Extensions>,
  ): Promise<Count> {
    return this.organizationRepository.extensions(id).patch(extensions, where);
  }

  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  @restrictReadsWithGuard({ plural: true })
  @del('/organizations/{id}/extensions', {
    responses: {
      '200': {
        description: 'Organization.Extensions DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(Extensions)) where?: Where<Extensions>,
  ): Promise<Count> {
    return this.organizationRepository.extensions(id).delete(where);
  }
}
