import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
  api,
} from '@loopback/rest';
import {OnboardingState, OnboardingTask} from '../models';
import {OnboardingStateRepository, OnboardingTaskRepository} from '../repositories';
import {GuardSkipStrategy, guardStrategy, injectUserOrgId, modelIdForGuard, skipGuardCheck} from '../interceptors';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization, OnboardingTaskStateService} from '../services';
import {service} from '@loopback/core';


@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class OnboardingTaskController {
  constructor(
    @repository(OnboardingTaskRepository)
    public onboardingTaskRepository : OnboardingTaskRepository,
	@repository(OnboardingStateRepository)
	public onboardingStateRepository: OnboardingStateRepository,
	@service(OnboardingTaskStateService)
	public onboardingTaskStateService: OnboardingTaskStateService
  ) {}

  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  @get('/onboarding-tasks')
  @response(200, {
    description: 'Array of OnboardingTask model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(OnboardingTask, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(OnboardingTask) filter?: Filter<OnboardingTask>,
  ): Promise<OnboardingTask[]> {
    return this.onboardingTaskRepository.find(filter);
  }

  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  @get('/onboarding-tasks/states')
  @response(200, {
    description: 'Array of OnboardingTask model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(OnboardingTask, {includeRelations: true}),
        },
      },
    },
  })
  async findStates(
	@injectUserOrgId() orgId: number,
  ): Promise<OnboardingTask[]> {
    const [tasks, states] = await Promise.all([
		this.onboardingTaskRepository.find(),
		this.onboardingStateRepository.find({ where: { orgId }})
	]);

	return tasks.map(x => ({
		...x,
		state: states.find(y => y.taskId === x.id)
	}) as any as OnboardingTask);
  }

  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  @patch('/onboarding-tasks/{type}/states')
  @response(204, {
    description: 'OnboardingState PATCH success',
  })
  @skipGuardCheck()
  async updateState(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(OnboardingState, {partial: true}),
        },
      },
    })
    onboardingState: OnboardingState,
	@param.path.string('type') type: string,
	@injectUserOrgId() orgId: number,
  ): Promise<Count> {
	return this.onboardingTaskStateService.updateState(onboardingState, type, orgId);
  }
}
