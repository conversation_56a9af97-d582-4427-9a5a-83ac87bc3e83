import {
	Count,
	CountSchema,
	Filter,
	FilterExcludingWhere,
	repository,
	Where,
} from '@loopback/repository';
import {
	api,
	post,
	param,
	get,
	getModelSchemaRef,
	patch,
	put,
	del,
	requestBody,
	response,
} from '@loopback/rest';
import {ConversionEvent} from '../models';
import {ConversionEventRepository} from '../repositories';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {injectUserOrgId, GuardSkipStrategy, guardStrategy, skipGuardCheck} from '../interceptors';
import {basicAuthorization} from '../services';
import {getURL} from '../utils/utils';
const fetch = require('node-fetch');

const DATA_API = 'bcie42aco8.execute-api.us-east-1.amazonaws.com';

@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class ConversionEventController {
	constructor(
		@repository(ConversionEventRepository)
		public conversionEventRepository: ConversionEventRepository,
	) { }

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/conversion-events')
	@response(200, {
		description: 'ConversionEvent model instance',
		content: {'application/json': {schema: getModelSchemaRef(ConversionEvent)}},
	})
	async create(
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(ConversionEvent, {
						title: 'NewConversionEvent',
						exclude: ['id'],
					}),
				},
			},
		})
		conversionEvent: Omit<ConversionEvent, 'id'>,
	): Promise<ConversionEvent> {
		return this.conversionEventRepository.create(conversionEvent);
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/conversion-events/count')
	@response(200, {
		description: 'ConversionEvent model count',
		content: {'application/json': {schema: CountSchema}},
	})
	async count(
		@param.where(ConversionEvent) where?: Where<ConversionEvent>,
	): Promise<Count> {
		return this.conversionEventRepository.count(where);
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/conversion-events')
	@response(200, {
		description: 'Array of ConversionEvent model instances',
		content: {
			'application/json': {
				schema: {
					type: 'array',
					items: getModelSchemaRef(ConversionEvent, {includeRelations: true}),
				},
			},
		},
	})
	async find(
		@param.filter(ConversionEvent) filter?: Filter<ConversionEvent>,
	): Promise<ConversionEvent[]> {
		return this.conversionEventRepository.find(filter);
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@patch('/conversion-events')
	@response(200, {
		description: 'ConversionEvent PATCH success count',
		content: {'application/json': {schema: CountSchema}},
	})
	async updateAll(
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(ConversionEvent, {partial: true}),
				},
			},
		})
		conversionEvent: ConversionEvent,
		@param.where(ConversionEvent) where?: Where<ConversionEvent>,
	): Promise<Count> {
		return this.conversionEventRepository.updateAll(conversionEvent, where);
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/conversion-events/{id}')
	@response(200, {
		description: 'ConversionEvent model instance',
		content: {
			'application/json': {
				schema: getModelSchemaRef(ConversionEvent, {includeRelations: true}),
			},
		},
	})
	async findById(
		@param.path.number('id') id: number,
		@param.filter(ConversionEvent, {exclude: 'where'}) filter?: FilterExcludingWhere<ConversionEvent>
	): Promise<ConversionEvent> {
		return this.conversionEventRepository.findById(id, filter);
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@patch('/conversion-events/{id}')
	@response(204, {
		description: 'ConversionEvent PATCH success',
	})
	async updateById(
		@param.path.number('id') id: number,
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(ConversionEvent, {partial: true}),
				},
			},
		})
		conversionEvent: ConversionEvent,
	): Promise<void> {
		await this.conversionEventRepository.updateById(id, conversionEvent);
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@put('/conversion-events/{id}')
	@response(204, {
		description: 'ConversionEvent PUT success',
	})
	async replaceById(
		@param.path.number('id') id: number,
		@requestBody() conversionEvent: ConversionEvent,
	): Promise<void> {
		await this.conversionEventRepository.replaceById(id, conversionEvent);
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@del('/conversion-events/{id}')
	@response(204, {
		description: 'ConversionEvent DELETE success',
	})
	async deleteById(@param.path.number('id') id: number): Promise<void> {
		await this.conversionEventRepository.deleteById(id);
	}


	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/conversion-events/{id}/conversion-event-logs')
	@response(200, {
		description: 'Array of ConversionEventLog model instances',
		content: {
			'application/json': {
				schema: {
					type: 'array',
					items: {
						'type': 'object'
					},
				}
			},
		},
	})
	async findConversionEventLogs(
		@param.path.number('id') id: number,
		@param.filter(ConversionEvent) filter?: Filter<ConversionEvent>,
	): Promise<any> {
		let conversionInfo = await this.conversionEventRepository.findById(id);

		let response: any = {};
		try {
			const url = `/dev/conversion/log`;
			let payload = {
				type: conversionInfo.conversiontype,
				source_type: conversionInfo.sourcetype,
				start_time: conversionInfo.starttime,
				end_time: conversionInfo.endtime,
				attribution_minutes: conversionInfo.attributionminutes,
				org_id: conversionInfo.orgid,
				data : conversionInfo.conversiondata,
				source: conversionInfo.sourcedata

			}
			const signedRequest = getURL(url, 'POST', payload);
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		return data;
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/conversion-events/{id}/conversion-event-info')
	@response(200, {
		description: 'Array of ConversionEventLog model instances',
		content: {
			'application/json': {
				schema: {
					type: 'object',
					properties: {
						"total_users": {
							"type": "number"
						},
						"total_conversions": {
							"type": "number"
						},
						"total_converted": {
							"type": "number"
						},
						"percent_conversions": {
							"type": "number"
						}
					}

				}
			},
		},
	})
	async findConversionEventInfo(
		@param.path.number('id') id: number,
		@param.filter(ConversionEvent) filter?: Filter<ConversionEvent>,
	): Promise<any> {
		let conversionInfo = await this.conversionEventRepository.findById(id);

		let response: any = {};
		try {
			const url = `/dev/conversion/info`;
			let payload = {
				type: conversionInfo.conversiontype,
				source_type: conversionInfo.sourcetype,
				start_time: conversionInfo.starttime,
				end_time: conversionInfo.endtime,
				attribution_minutes: conversionInfo.attributionminutes,
				org_id: conversionInfo.orgid,
				data : conversionInfo.conversiondata,
				source: conversionInfo.sourcedata

			}
			const signedRequest = getURL(url, 'POST', payload);
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		return data;
	}


	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/conversion-events/{id}/conversion-event-custom/{custom_type}')
	@response(200, {
		description: 'Output will depend on custom event',
		content: {
			'application/json': {
				schema: {}
			},
		},
	})
	async calculateCustomConversion(
		@param.path.number('id') id: number,
		@param.path.string('custom_type') custom_type: string,
	): Promise<any> {
		let conversionInfo = await this.conversionEventRepository.findById(id);

		let response: any = {};
		try {
			const url = `/dev/conversion/custom`;
			let payload = {
				type: conversionInfo.conversiontype,
				source_type: conversionInfo.sourcetype,
				start_time: conversionInfo.starttime,
				end_time: conversionInfo.endtime,
				attribution_minutes: conversionInfo.attributionminutes,
				custom_type: custom_type,
				org_id: conversionInfo.orgid,
				data : conversionInfo.conversiondata,
				source: conversionInfo.sourcedata

			}
			const signedRequest = getURL(url, 'POST', payload);
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		return data;
	}

}
