import {inject, service} from '@loopback/core';
import {repository} from '@loopback/repository';
import {
	post,
	Request,
	RestBindings,
	HttpErrors,
	api,
} from '@loopback/rest';
import {RaleonUserIdentityRepository, RaleonUserRepository, UserIdentityRepository} from '../repositories';
import {AccessTokenService} from '../services/oauth/token-service';
import {GuardSkipStrategy, guardStrategy, skipGuardCheck} from '../interceptors';
import {UserManagementService} from '../services';
import { v4 as uuidv4 } from 'uuid';
import {IDENTITY_TYPES} from '../models/user-identity.model';

@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class TokenController {
	constructor(
		@repository(RaleonUserRepository)
		protected raleonUserRepository: RaleonUserRepository,
		@repository(RaleonUserIdentityRepository)
		protected raleonUserIdentityRepository: RaleonUserIdentityRepository,
		@repository(UserIdentityRepository)
		protected userIdentityRepository: UserIdentityRepository,
		@inject('services.AccessTokenService')
		public tokenService: AccessTokenService,
		@service(UserManagementService)
		private userManagementService: UserManagementService
	) { }

	@post('/create-session', {
		responses: {
			'200': {
				description: 'Session Token',
				content: {},
			},
		},
	})
	@skipGuardCheck()
	async createSession(
		@inject(RestBindings.Http.REQUEST) request: Request,
	) {
		const accessToken = request.headers?.authorization?.split(' ')?.[1];
		if (!accessToken) {
			throw new HttpErrors.Unauthorized('Invalid Access Token');
		}

		const userIdentity = await this.userIdentityRepository.findOne({
			where: {
				identityType: IDENTITY_TYPES.SELF_SERVICE,
				identityValue: accessToken
			}
		})

		if (!userIdentity) {
			throw new HttpErrors.Unauthorized('Invalid Access Token');
		}

		await this.userManagementService.verifyToken(accessToken);
		if (userIdentity.sessionToken) {
			return { sessionToken: userIdentity.sessionToken };
		}

		let sessionToken = uuidv4();
		await this.userIdentityRepository.updateById(
			userIdentity.id,
			{ sessionToken }
		);

		return { sessionToken };
	}
}

export interface Customer {
	id: string;
	// firstName: string;
	// lastName: string;
	// email: string;
}

export interface CustomerWithHash extends Customer {
	digest: string;
}
