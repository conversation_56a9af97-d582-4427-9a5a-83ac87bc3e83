import {
	Count,
	CountSchema,
	Filter,
	repository,
	Where,
} from '@loopback/repository';
import {
	del,
	get,
	getModelSchemaRef,
	getWhereSchemaFor,
	param,
	patch,
	post,
	api,
	requestBody,
} from '@loopback/rest';
import {
	Organization,
	OrganizationKeys,
} from '../models';
import {OrganizationKeysRepository, OrganizationRepository} from '../repositories';
import {injectUserOrgId, OrgGuardPropertyStrategy, restrictReadsWithGuard, skipGuardCheck} from '../interceptors/crud-guard.interceptor';
import {guardStrategy} from '../interceptors/crud-guard.interceptor';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../services/basic.authorizor';
import { SecretsManager } from 'aws-sdk';
import crypto from 'crypto';

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardPropertyStrategy<OrganizationKeys>({
	orgIdModelPropertyName: 'organizationId',
	repositoryClass: OrganizationKeysRepository
}))

export class OrganizationOrganizationKeysController {
	constructor(
		@repository(OrganizationRepository) protected organizationRepository: OrganizationRepository,
	) { }

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/organizations/organization-keys', {
		responses: {
			'200': {
				description: 'Array of Organization has many OrganizationKeys',
				content: {
					'application/json': {
						schema: {type: 'array', items: getModelSchemaRef(OrganizationKeys)},
					},
				},
			},
		},
	})
	async find(
		@injectUserOrgId() orgId: number,
	): Promise<OrganizationKeys[]> {
		let secrets = await this.getSecrets();
		let keys = await this.organizationRepository.organizationKeys(orgId).find();
		keys.forEach((key: OrganizationKeys) => {
			if(key.secretKeyId && secrets[key.secretKeyId]) {
				key.value = this.decrypt(key.value, secrets[key.secretKeyId]);
			}
		});
		return keys;
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/organizations/organization-keys/{key}', {
		responses: {
			'200': {
				description: 'Returns one key with the given key',
				content: {
					'application/json': {
						schema: {type: 'array', items: getModelSchemaRef(OrganizationKeys)},
					},
				},
			},
		},
	})
	async findKey(
		@param.path.string('key') key: string,
		@injectUserOrgId() orgId: number,
	): Promise<OrganizationKeys[]> {
		let secrets = await this.getSecrets();
		let keys = await this.organizationRepository.organizationKeys(orgId).find(
			{ where: {key: key} }
		);
		keys.forEach((key: OrganizationKeys) => {
			if(key.secretKeyId && secrets[key.secretKeyId]) {
				key.value = this.decrypt(key.value, secrets[key.secretKeyId]);
			}
		});
		return keys;
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()

	@post('/organizations/organization-keys', {
		responses: {
			'200': {
				description: 'Organization model instance',
				content: {'application/json': {schema: getModelSchemaRef(OrganizationKeys)}},
			},
		},
	})
	async create(
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(OrganizationKeys, {
						title: 'NewOrganizationKeysInOrganization',
						exclude: ['id','organizationId','created_at']
					}),
				},
			},
		}) organizationKeys: any,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		let secrets = await this.getSecrets();

		//Check to see if we already have a key with this name
		let existingKey = await this.organizationRepository.organizationKeys(orgId).find({where: {key: organizationKeys.key}});
		if(existingKey && existingKey.length > 0) {
			return {
				statusCode: 400,
				error: 'Key with this name already exists'
			}
		}

		if(organizationKeys.secretKeyId && secrets[organizationKeys.secretKeyId]) {
			organizationKeys.value = this.encrypt(organizationKeys.value, secrets[organizationKeys.secretKeyId]);
		} else {
			return {
				statusCode: 400,
				error: 'Invalid secretKeyId, or secretKeyId not found in secrets manager: use default secretKeyId if you want to use default secret key.'
			}
		}

		organizationKeys.created_at = new Date().toISOString();
		return this.organizationRepository.organizationKeys(orgId).create(organizationKeys);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/organizations/organization-keys', {
		responses: {
			'200': {
				description: 'Organization model instance',
				content: {'application/json': {schema: getModelSchemaRef(OrganizationKeys)}},
			},
		},
	})
	async update(
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(OrganizationKeys, {
						title: 'NewOrganizationKeysInOrganization',
						exclude: ['id','organizationId','created_at']
					}),
				},
			},
		}) organizationKeys: Omit<OrganizationKeys, 'id'>,
		@param.query.string('replace') replace: boolean = false,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		let secrets = await this.getSecrets();

		//Check to see if we already have a key with this name
		let existingKey = await this.organizationRepository.organizationKeys(orgId).find({where: {key: organizationKeys.key}});
		if(existingKey && existingKey.length > 0) {
			if(replace) {
				await this.organizationRepository.organizationKeys(orgId).delete({
					id: existingKey[0].id
				});
			}
			else {
				return {
					statusCode: 400,
					error: 'Key with this name already exists'
				}
			}
		}


		if(organizationKeys.secretKeyId && secrets[organizationKeys.secretKeyId]) {
			organizationKeys.value = this.encrypt(organizationKeys.value, secrets[organizationKeys.secretKeyId]);
		}
		else {
			return {
				statusCode: 400,
				error: 'Invalid secretKeyId, or secretKeyId not found in secrets manager: use default secretKeyId if you want to use default secret key.'
			}
		}
		organizationKeys.created_at = new Date().toISOString();
		return this.organizationRepository.organizationKeys(orgId).create(organizationKeys);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()

	@post('/organizations/organization-keys/generate', {
		responses: {
			'200': {
				description: 'Organization model instance',
				content: {'application/json': {
					schema: getModelSchemaRef(OrganizationKeys)
				}},
			},
		},
	})

	async generate(
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(OrganizationKeys, {
						title: 'NewOrganizationKeysInOrganization',
						exclude: ['value','id','organizationId','created_at'],
					}),
				},
			},
		}) organizationKeys: Omit<OrganizationKeys, 'id'>,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		let existingKey = await this.organizationRepository.organizationKeys(orgId).find({where: {key: organizationKeys.key}});
		if(existingKey && existingKey.length > 0) {
			return {
				statusCode: 400,
				error: 'Key with this name already exists'
			}
		}


		let secrets = await this.getSecrets();
		organizationKeys.created_at = new Date().toISOString();
		organizationKeys.value = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
		let encryptSecret = secrets[organizationKeys.secretKeyId];
		if(!organizationKeys.secretKeyId || !encryptSecret) {
			console.log('No secretKeyId provided, using default secretKeyId');
			organizationKeys.secretKeyId = DEFAULT_SECRET_KEY_NAME;
		}
		organizationKeys.value = this.encrypt(organizationKeys.value, secrets[organizationKeys.secretKeyId]);
		let newObj = await this.organizationRepository.organizationKeys(orgId).create(organizationKeys);
		newObj.value = this.decrypt(newObj.value, secrets[organizationKeys.secretKeyId]);
		return newObj;
	}

	private async getSecrets(): Promise<any> {
		const client = new SecretsManager({
			region: 'us-east-1',
		});

		const secret: any = await client.getSecretValue({
			SecretId: process.env.ORGANIZATION_KEYS_SECRET_ARN!,
		})
		.promise()
		.catch((err) => console.error(err));
		const secretString = JSON.parse(secret.SecretString!);
		return secretString;
	}

	private encrypt(text: string, key: string) {
		const IV_LENGTH = 16;
		let iv = crypto.randomBytes(IV_LENGTH);
		let cipher = crypto.createCipheriv('aes-256-cbc', Buffer.from(key), iv);
		let encrypted = cipher.update(text);

		encrypted = Buffer.concat([encrypted, cipher.final()]);

		return iv.toString('hex') + ':' + encrypted.toString('hex');
	}


	private decrypt(text: string, key: string) {
		let textParts = text.split(':');
		if (textParts.length === 0) {
			throw new Error('Invalid encrypted text');
		}
		let iv = Buffer.from(textParts.shift()!, 'hex');
		let encryptedText = Buffer.from(textParts.join(':'), 'hex');
		let decipher = crypto.createDecipheriv('aes-256-cbc', Buffer.from(key), iv);
		let decrypted = decipher.update(encryptedText);

		decrypted = Buffer.concat([decrypted, decipher.final()]);

		return decrypted.toString();
	}
}

export const DEFAULT_SECRET_KEY_NAME = 'default'
