import {authenticate} from '@loopback/authentication';
import { User } from '@loopback/authentication-jwt';
import {authorize} from '@loopback/authorization';
import {inject} from '@loopback/core';
import {repository} from '@loopback/repository';
import {api, del, get, getModelSchemaRef, HttpErrors, param, post, Request, requestBody, RestBindings} from '@loopback/rest';
import { SecurityBindings } from '@loopback/security';
import {injectUserOrgId, OrgGuardPropertyStrategy, guardStrategy, skipGuardCheck, modelForGuard} from '../interceptors';
import {Segment, SegmentDownload} from '../models';
import {OrganizationRepository, SegmentRepository, UserRepository} from '../repositories';
import {basicAuthorization} from '../services';
const fetch = require('node-fetch')
const aws4 = require('aws4')
const NodeCache = require( "node-cache" );
const myCache = new NodeCache({ stdTTL: 1800, checkperiod: 450 });

const DATA_API = 'bcie42aco8.execute-api.us-east-1.amazonaws.com'
const UTM_API = 'j0v36abmdj.execute-api.us-east-1.amazonaws.com'
const AWS_ACCESS_KEY = process.env.API_ACCESS_KEY;
const AWS_SECRET_KEY = process.env.API_SECRET_KEY;

//Figure out how to move this function to a utility file
function getURL(path: string, method: string, body?: any, host?: string) {
  const opts = {
	host: host || DATA_API,
	path: path,
	region: 'us-east-1',
	service: 'execute-api',
	mode: 'cors',
	body: body != undefined ? JSON.stringify(body) : undefined,
	headers: {
	  'Content-Type': 'application/json',
	},
	method: method
  }
  return aws4.sign(opts, {accessKeyId: AWS_ACCESS_KEY, secretAccessKey: AWS_SECRET_KEY});
}

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardPropertyStrategy<Segment>({
	orgIdModelPropertyName: 'orgid',
	repositoryClass: SegmentRepository
}))
export class SegmentsController {
	constructor(
		@inject(RestBindings.Http.REQUEST) private req: Request,
		@inject(SecurityBindings.USER) private user: User,
		@repository(SegmentRepository) public segmentRepository: SegmentRepository,
		@repository(UserRepository) private userRepository: UserRepository,
		@repository(OrganizationRepository) private orgRepository: OrganizationRepository
	) {}


	@get('/segment-list', {
		responses: {
			'200': {
				description: 'Array of Segment model instances',
				content: {
					'application/json': {
						schema: {
							type: 'array',
							items: getModelSchemaRef(Segment, {includeRelations: true}),
						},
					},
				},
			},
		},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async find(
		@injectUserOrgId() orgId: number,
	): Promise<Segment[]> {
		return await this.segmentRepository.find({
			fields: ['id', 'name', 'status', 'viewname', 'addressCount'],
			where: {
				orgid: orgId,
				status: 'PUBLISHED'
			}
		});
	}

	@post('/segment/download', {
		responses: {
			'200': {
				description: 'Download a Segment to CSV',
				content: {
					'application/json': {
						schema: getModelSchemaRef(SegmentDownload),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async downloadSegment(
		@requestBody(SegmentDownload) segmentDownload: SegmentDownload,
	): Promise<object> {
		const result: any = {};
		let response: any;
		try {
			const url = `/dev/segment/download`;
			console.log('url: ' + url);
			const signedRequest = getURL(url, 'POST', segmentDownload);
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
			throw new Error("Error: " + JSON.stringify(err));
		}

		const data: any = await response.json();
		return {
			statusCode: 200,
			body: data
		};
	}

	@post('/segment/segment-definition/metrics', {
		responses: {
			'200': {
				description: 'Updates metricsEnabled for the given segment',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@authenticate('jwt')
	@skipGuardCheck()
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async setMetricsEnabled(
		@requestBody()
		//@modelForGuard(Segment)
		request: any, //Should include viewname and metricsEnabled
	): Promise<object> {
		const result: any = {};
		let response: any;
		try {
			const url = `/dev/segment/segment-definition/metrics`;
			console.log('url: ' + url);
			const signedRequest = getURL(url, 'POST', request);
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
			throw new Error("Error: " + JSON.stringify(err));
		}

		const data: any = await response.json();
		return {
			statusCode: 200,
			body: data
		};
	}



	@post('/segment', {
		responses: {
			'200': {
				description: 'Creates a new segment definition in AWS for metric processing',
				content: {
					'application/json': {
						schema: getModelSchemaRef(Segment),
					},
				},
			},
		},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async buildSegment(
		@requestBody(Segment)
		@modelForGuard(Segment)
		segment: Segment,
	): Promise<object> {
		const org = await this.orgRepository.findById(segment.orgid);
		if (org.selfService) {
			const segments = await this.segmentRepository.find({
				where: {
					orgid: segment.orgid
				}
			});

			if (segments.filter(x => !x.viewname?.endsWith('_5') && x.status == 'PUBLISHED').length >= 2) {
				throw new HttpErrors.Forbidden('Self-service accounts can only have 2 custom audiences');
			}
		}

		const result: any = {};
		let response: any;
		try {
			const url = `/dev/segment/segment-definition`;
			console.log('url: ' + url);
			const signedRequest = getURL(url, 'POST', segment);
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
			throw new Error("Error: " + JSON.stringify(err));
		}

		const data: any = await response.json();
		return {
			statusCode: 200,
			body: data
		};
	}

	@get('/segment', {
		responses: {
			'200': {
				description: 'executes an existing segment',
				content: {
					'application/json': {
						schema: getModelSchemaRef(Segment),
					},
				},
			},
		},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async executeSegment(
		@param.query.string('name') name: string,
		@param.query.string('network') network: string,
		@param.query.string('page') page: number,
		@param.query.string('pagesize') pagesize: number,
		@param.query.string('table') table: string,
		@param.query.string('queryid', { required: false }) queryId?: string,
		@injectUserOrgId() orgid?: number,
	): Promise<object> {
		let response: any;
		try {
			let url = `/dev/segment?name=${name}&network=${network}&page=${page}&pagesize=${pagesize}&orgid=${orgid}&table=${table}`;
			if (queryId) {
				url += `&queryid=${queryId}`;
			}
			console.log('url: ' + url);
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}

		const data: any = await response.json();
		return {
			statusCode: 200,
			body: data
		};
	}

	@post('/segment/preview', {
		responses: {
		'200': {
			description: 'Previews a Segment',
			content: {
			'application/json': {
				schema: getModelSchemaRef(Segment),
			},
			},
		},
		},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async previewSegment(
		@modelForGuard(Segment)
		@requestBody(Segment)
		segment: Segment,
	): Promise<object> {
		let response: any;
		try {
			const url = `/dev/segment/preview`;
			const signedRequest = getURL(url, 'POST', segment);
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();

		return {
			statusCode: data.statusCode,
			body: data
		};
	}

	@del('/segment', {
		responses: {
			'204': {
				description: 'Deletes a segment',
			}
		}
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async deleteSegment(
		@param.query.string('name') name: string,
		@param.query.string('network') network: string,
		@injectUserOrgId() orgid: number
	): Promise<object> {
		const requestBody = {name: name, network: network, orgid: orgid};
		const url = `/dev/segment?name=${name}&network=${network}&orgid=${orgid}`;
		console.log('url: ' + url);
		const signedRequest = getURL(url, 'DELETE', requestBody);
		const response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		if (response.status != 200 && response.status != 204) {
			throw new Error("Error deleting segment: " + JSON.stringify(response));
		}
		return response;
	}

	@get('/app-info', {
		responses: {
			'200': {
				description: 'Returns Name, Category, Addres, and Exchange for a given app',
				content: {
				'application/json': {},
				},
			},
		},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async getAppInfo(
		@param.query.string('network') network: string,
	): Promise<object> {

		const cachedData = myCache.get(`app-info-${network}`);
		if (cachedData) {
			return {
				statusCode: 200,
				body: JSON.parse(cachedData)
			};
		}

		let response: any;
		try {
		const url = `/dev/app-info?network=${network}`;
		response = await fetch(`https://${DATA_API}${url}`, getURL(url, 'GET'));
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
			throw new Error(err);

		}
		const data: any = await response.json();

		myCache.set(`app-info-${network}`, JSON.stringify(data), 60 * 60); // 1 hour

		return {
			statusCode: 200,
			body: data
		};
	}

	@get('/segment/event', {
		responses: {
			'200': {
				description: 'Returns Events that have been registered for the provided org',
				content: {
				'application/json': {},
				},
			},
		},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async getEvents(
		@injectUserOrgId() orgId: number
	): Promise<object> {
		let response: any;
		try {
			const url = `/api/event?orgId=${orgId}`;
			response = await fetch(`https://${UTM_API}${url}`, getURL(url, 'GET', undefined, UTM_API));
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
			throw new Error(err);
		}
		const data: any = await response.json();

		return {
			statusCode: 200,
			body: data
		};
	}

	@get('/segment/wallet/segments/{address}', {
		responses: {
		'200': {
				description: 'Wallet Share for given network and address',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async getSegments(
		@param.path.string('address') address: string,
		@injectUserOrgId() orgId: number
	): Promise<object> {
		let response: any = {};
		try {
			const params = `orgId=${orgId}&address=${address}`;
			const url = `/dev/wallet/segments?${params}`;
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();

		if (!data || data.length == 0) {
			return {
				statusCode: 200,
				body: []
			};
		}

		const dbSegments = await this.segmentRepository.find({
			fields: ['id', 'name', 'status', 'viewname', 'addressCount'],
			where: {
				orgid: orgId,
				status: 'PUBLISHED'
			}
		});

		const segments = data?.map((segment: any) => {
			const seg = dbSegments.find((dbSegment: any) => dbSegment.viewname.toLowerCase() === segment.toLowerCase())
			return {
				name: seg?.name,
				id: seg?.id,
			}
		});

		return {
			statusCode: 200,
			body: segments
		};
	}

	@get('/all-segments', {
		responses: {
		'200': {
				description: 'Get all segments ',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async getSegment(
		@injectUserOrgId() orgId: number
	): Promise<object> {
		return await this.segmentRepository.find({ where: { orgid: orgId } });
	}

	@get('/segment/{segmentId}', {
		responses: {
		'200': {
				description: 'Segment with corresponding id',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getSegmentById(
		@param.path.string('segmentId') segmentId: string,
		@injectUserOrgId() orgId: number
	): Promise<object> {
		return await this.segmentRepository.find({
			where: {
				id: +segmentId,
				orgid: orgId,
			}
		});
	}
}
