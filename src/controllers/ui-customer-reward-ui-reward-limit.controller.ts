import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
  api
} from '@loopback/rest';
import {
  UiCustomerReward,
  UiRewardLimit,
} from '../models';
import {UiCustomerRewardRepository} from '../repositories';
import {GuardSkipStrategy, guardStrategy, skipGuardCheck} from '../interceptors';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../services/basic.authorizor';

@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class UiCustomerRewardUiRewardLimitController {
  constructor(
    @repository(UiCustomerRewardRepository) protected uiCustomerRewardRepository: UiCustomerRewardRepository,
  ) { }

  @get('/ui-customer-rewards/{id}/ui-reward-limits', {
    responses: {
      '200': {
        description: 'Array of UiCustomerReward has many UiRewardLimit',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(UiRewardLimit)},
          },
        },
      },
    },
  })
  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<UiRewardLimit>,
  ): Promise<UiRewardLimit[]> {
    return this.uiCustomerRewardRepository.uiRewardLimits(id).find(filter);
  }

  @post('/ui-customer-rewards/{id}/ui-reward-limits', {
    responses: {
      '200': {
        description: 'UiCustomerReward model instance',
        content: {'application/json': {schema: getModelSchemaRef(UiRewardLimit)}},
      },
    },
  })
  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin'],
	  voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async create(
    @param.path.number('id') id: typeof UiCustomerReward.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UiRewardLimit, {
            title: 'NewUiRewardLimitInUiCustomerReward',
            exclude: ['id'],
            optional: ['uiCustomerRewardId']
          }),
        },
      },
    }) uiRewardLimit: Omit<UiRewardLimit, 'id'>,
  ): Promise<UiRewardLimit> {
    return this.uiCustomerRewardRepository.uiRewardLimits(id).create(uiRewardLimit);
  }

  @patch('/ui-customer-rewards/{id}/ui-reward-limits', {
    responses: {
      '200': {
        description: 'UiCustomerReward.UiRewardLimit PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin'],
	  voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UiRewardLimit, {partial: true}),
        },
      },
    })
    uiRewardLimit: Partial<UiRewardLimit>,
    @param.query.object('where', getWhereSchemaFor(UiRewardLimit)) where?: Where<UiRewardLimit>,
  ): Promise<Count> {
    return this.uiCustomerRewardRepository.uiRewardLimits(id).patch(uiRewardLimit, where);
  }

  @del('/ui-customer-rewards/{id}/ui-reward-limits', {
    responses: {
      '200': {
        description: 'UiCustomerReward.UiRewardLimit DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin'],
	  voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(UiRewardLimit)) where?: Where<UiRewardLimit>,
  ): Promise<Count> {
    return this.uiCustomerRewardRepository.uiRewardLimits(id).delete(where);
  }
}
