import {Filter, repository} from '@loopback/repository';
import {LoyaltyGiveaway, LoyaltyProgram} from '../models';
import {CurrencyRepository, LoyaltyCampaignRepository, LoyaltyEarnRepository, LoyaltyGiveawayRepository, LoyaltyProgramRepository, OrganizationRepository, UiCustomerActionRepository} from '../repositories';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../services/basic.authorizor';
import {Response, RestBindings, api, get, param, patch, post, requestBody} from '@loopback/rest';
import {OrgGuardSingleHopPropertyStrategy, guardStrategy, injectUserOrgId, modelIdForGuard, restrictReadsWithGuard, skipGuardCheck} from '../interceptors';
import {LoyaltyDetailsController} from './loyalty/loyalty-details.controller';
import {LoyaltyGiveawayService} from '../services/loyalty/loyalty-giveaway.service';
import {inject, service} from '@loopback/core';
import {DevDbDataSource} from '../datasources';
import {convertCurrencyPlaceholdersToValues} from './admin-ui.controller';

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardSingleHopPropertyStrategy<LoyaltyGiveaway, LoyaltyProgram>({
	relatedIdPropertyName: 'loyaltyProgramId',
	relatedOrgIdPropertyName: 'orgId',
	relatedRepositoryClass: LoyaltyProgramRepository,
	repositoryClass: LoyaltyGiveawayRepository,
}))
export class GiveawayUiController {

	giveawayActionTypes = ['click-to-redeem'];

	constructor(
		@repository(LoyaltyGiveawayRepository)
		private loyaltyGiveawayRepository: LoyaltyGiveawayRepository,
		@repository(LoyaltyProgramRepository)
		private loyaltyProgramRepository: LoyaltyProgramRepository,
		@repository(LoyaltyCampaignRepository)
		private loyaltyCampaignRepository: LoyaltyCampaignRepository,
		@repository(LoyaltyEarnRepository)
		private loyaltyEarnRepository: LoyaltyEarnRepository,
		@repository(UiCustomerActionRepository)
		private uiCustomerActionRepository: UiCustomerActionRepository,
		@repository(CurrencyRepository)
		private currencyRepository: CurrencyRepository,
		@repository(OrganizationRepository)
		public organizationRepository: OrganizationRepository,
		@service(LoyaltyGiveawayService)
		private loyaltyGiveawayService: LoyaltyGiveawayService,
	) { }

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/giveaway/{id}/earn/{earnId}')
	async getGiveawayById(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyGiveaway)
		id: typeof LoyaltyGiveaway.prototype.id,
		@param.path.number('earnId')
		earnId: typeof LoyaltyProgram.prototype.id,
	) {
		const giveaway = await this.loyaltyGiveawayRepository.findById(id);
		if (!giveaway) {
			throw new Error('Giveaway not found');
		}
		return giveaway;
	}


	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/giveaway/{id}/earn')
	@skipGuardCheck()
	async createGivewayEarn(
		@param.path.number('id')
		id: typeof LoyaltyGiveaway.prototype.id,
		@injectUserOrgId()
		orgId: number,
	) {

	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/giveaway/actions')
	@skipGuardCheck()
	async getGiveawayCustomerActions() {
		return await this.uiCustomerActionRepository.find({
			where: {
				type: {inq: this.giveawayActionTypes},
			},
		});
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/giveaway/{id}/foundational-campaign')
	@skipGuardCheck()
	async getFoundationalCampaign(
		@injectUserOrgId()
		orgId: number,
	) {
		let loyaltyProgram = await this.loyaltyProgramRepository.findOne({
			where: {
				orgId,
				active: true,
			}
		});

		if (!loyaltyProgram) {
			throw new Error('No active loyalty program found');
		}

		return await this.loyaltyCampaignRepository.findOne({
			where: {
				evergreen: true,
				loyaltySegment: 'Everyone',
				loyaltyProgramId: loyaltyProgram.id,
			}
		});
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/giveaway/{id}/ways-to-earn')
	@skipGuardCheck()
	async getGiveawayWtes(
		@param.path.number('id')
		giveawayId: number,
		@injectUserOrgId()
		orgId: number,
	) {
		let loyaltyProgram = await this.loyaltyProgramRepository.findOne({
			where: {
				orgId,
				active: true,
			}
		});

		if (!loyaltyProgram) {
			throw new Error('No active loyalty program found');
		}

		let whereFilter: any = {
			loyaltyProgramId: loyaltyProgram.id,
		};
		let programCampaigns = await this.loyaltyCampaignRepository.find({
			where: whereFilter,
			include: [
				{
					relation: 'loyaltyEarns',
					scope: {
						order: ['priority ASC'],
						include: [
							{
								relation: 'earnEffects',
								scope: {
									include: [
										{
											relation: 'loyaltyCurrency',
											scope: {
												fields: {
													conversionToUSD: false
												}
											}
										},
										{
											relation: 'loyaltyRewardDefinition',
											scope: {
												include: [
													{
														relation: 'rewardCoupon',
													}
												]
											}
										}
									]
								}
							},
							{relation: 'earnConditions'}
						],
						where: {
							recommendationState: {inq: ['NONE', 'APPROVED_RECOMMENDATION']}
						}
					}
				},
			],
		});

		if (!programCampaigns.length) {
			throw new Error('No campaigns found');
		}

		const currentOrg = (await this.organizationRepository.findById(orgId));
		const orgLanguage = currentOrg?.language || 'en';
		let orgCurrency = await this.currencyRepository.findOne({
			where: { organizationId: orgId },
			include: [ { relation: 'supportedCurrencies' } ]
		});
		const wtes = programCampaigns.reduce((acc: any[], campaign) => {
			if (campaign.loyaltyEarns) {
				const matchingEarns = campaign.loyaltyEarns.filter(earn =>
					earn.earnEffects && earn.earnEffects.some((effect: any) =>
						effect.loyaltyRewardDefinition &&
						effect.loyaltyRewardDefinition.rewardCoupon &&
						effect.loyaltyRewardDefinition.rewardCoupon.loyaltyGiveawayId === giveawayId
					)
				);
				if (matchingEarns.length > 0) {
					for (let i = 0; i < matchingEarns.length; i++) {
						let earnConditionGroups = LoyaltyDetailsController.groupEarnConditions(matchingEarns[i].earnConditions);
						let conditionStrings = LoyaltyDetailsController.generateConditionStringsShort(earnConditionGroups, orgId, 'en');
						let conditionStringsLong = LoyaltyDetailsController.generateConditionStrings(campaign, earnConditionGroups, orgId, 'en', orgCurrency!, currentOrg?.externalDomain);
						let pillStrings = LoyaltyDetailsController.generatePillStrings(matchingEarns[i], orgId, 'en', orgCurrency!);
						acc.push({
							icon: '<svg fill="#15803D" xmlns="http://www.w3.org/2000/svg" height="33" viewBox="0 -960 960 960" width="33"><path d="M480-165q-17 0-33-7.5T419-194L113-560q-9-11-13.5-24T95-611q0-9 1.5-18.5T103-647l75-149q11-20 29.5-32t41.5-12h462q23 0 41.5 12t29.5 32l75 149q5 8 6.5 17.5T865-611q0 14-4.5 27T847-560L541-194q-12 14-28 21.5t-33 7.5Zm-95-475h190l-60-120h-70l-60 120Zm55 347v-267H218l222 267Zm80 0 222-267H520v267Zm144-347h106l-60-120H604l60 120Zm-474 0h106l60-120H250l-60 120Z"/></svg>',
							title: convertCurrencyPlaceholdersToValues(matchingEarns[i].name, orgCurrency!),
							subtitle: convertCurrencyPlaceholdersToValues(conditionStrings.join(', ') + ' → ' + pillStrings.join(', '), orgCurrency!),
							toggle: matchingEarns[i].active,
							longSummary: convertCurrencyPlaceholdersToValues(conditionStringsLong.join(', ') + ': ' + pillStrings.join(', '), orgCurrency!),
							recommendedByAI: matchingEarns[i].recommendedByAI,
							recommendationState: matchingEarns[i].recommendationState,
							priority: matchingEarns[i].priority,
							name: matchingEarns[i].name,
							id: matchingEarns[i].id,
							campaignId: campaign.id,
						});
					}
				}
			}
			return acc;
		}, []);

		return {
			summary: {wtes}
		};
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/launch-giveaway/{giveawayId}')
	async enableGiveawayEarns(
		@param.path.number('giveawayId') giveawayId: number,
		@param.query.boolean('skipLaunchStatus') skipLaunchStatus = false
	) {
		const giveaway = await this.loyaltyGiveawayRepository.findById(giveawayId);

		const adjustLaunchedStatus = !skipLaunchStatus;
		if (adjustLaunchedStatus) {
			giveaway.launched = true;
			await this.loyaltyGiveawayRepository.update(giveaway);
		}

		if (new Date(giveaway.startDate).toISOString() <= new Date().toISOString() && new Date(giveaway.endDate).toISOString() >= new Date().toISOString()) {
			await this.loyaltyGiveawayService.startGiveaway(giveawayId);
		}
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/giveaway/{giveawayId}/results')
	async endGiveaway(
		@param.path.number('giveawayId') giveawayId: number,
		@inject('datasources.dev_db') dataSource: any,
	) {
		return this.loyaltyGiveawayService.endGiveaway(giveawayId, dataSource);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/deactivate-giveaway/{giveawayId}')
	async disableGiveawayWtes(
		@param.path.number('giveawayId') giveawayId: number,
		@param.query.boolean('skipLaunchStatus') skipLaunchStatus = false
	) {
		const giveaway = await this.loyaltyGiveawayRepository.findById(giveawayId);

		const adjustLaunchedStatus = !skipLaunchStatus;
		if (adjustLaunchedStatus) {
			giveaway.launched = false;
			await this.loyaltyGiveawayRepository.update(giveaway);
		}

		await this.loyaltyGiveawayService.setGiveawayWTEsActive(giveawayId, false);
	}

	@skipGuardCheck()
	@post('/launch-all-giveaways')
	async activateAllGiveaways() {
		console.log('Launching all relevant giveaways...');

		const oneHourAgo = new Date(new Date().getTime() - 60 * 60 * 1000).toISOString();
		const oneHourFromNow = new Date(new Date().getTime() + 60 * 60 * 1000).toISOString();
		const giveawaysToLaunch = await this.loyaltyGiveawayRepository.find({
			where: {
				and: [
					{ launched: true },
					{ startDate: { gte: oneHourAgo } },
					{ startDate: { lte: oneHourFromNow } },
					{ endDate: { gte: new Date().toISOString() } }
				]
			}
		});

		for (const giveaway of giveawaysToLaunch) {
			if (giveaway.id) {
				await this.loyaltyGiveawayService.startGiveaway(giveaway.id);
			}
		}

		return {success: true};
	}

	@skipGuardCheck()
	@post('/end-all-giveaways')
	async endAllGiveaways(
		@inject('datasources.dev_db') dataSource: DevDbDataSource,
	) {
		console.log('Ending all relevant giveaways...');
		const oneHourAgo = new Date(new Date().getTime() - 60 * 60 * 1000).toISOString();
		const oneHourFromNow = new Date(new Date().getTime() + 60 * 60 * 1000).toISOString();
		const giveawaysToEnd = await this.loyaltyGiveawayRepository.find({
			where: {
				and: [
					{launched: true},
					{endDate: {gte: oneHourAgo}},
					{endDate: {lte: oneHourFromNow}},
				]
			}
		});
		console.log(`Found ${giveawaysToEnd.length} giveaways to end`);

		for (const giveaway of giveawaysToEnd) {
			if (giveaway.id) {
				await this.loyaltyGiveawayService.endGiveaway(giveaway.id, dataSource);
			}
		}

		return {success: true};
	}
}
