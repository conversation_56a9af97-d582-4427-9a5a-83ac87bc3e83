import {
	repository,
	Filter
} from '@loopback/repository';
import {
	param,
	post,
	requestBody,
	api,
	Request,
	Response,
	RestBindings
} from '@loopback/rest';


import {injectUserId, injectUserOrgId, skipGuardCheck} from '../interceptors/crud-guard.interceptor';
import {guardStrategy, GuardSkipStrategy} from '../interceptors/crud-guard.interceptor';
import {LoyaltyCurrencyBalanceRepository, LoyaltyProgramRepository, RaleonUserIdentityRepository, RaleonUserRepository} from '../repositories';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../services';
import {inject, service} from '@loopback/core';
import csv = require('csv-parser');
import multer from 'multer';
import * as stream from 'stream';
import {ShopperInfo} from '../services/shoppers/shopper-info.service';
import {ShopifyApiInvoker} from '../services/shopify/shopify-api-invoker.service';
const storage = multer.memoryStorage(); // or multer.diskStorage({ destination: './uploads' })
const upload = multer({storage});
import {promisify} from 'util';
import {LoyaltyBalanceManager} from '../services/loyalty/loyalty-balance-manager.service';
import {RaleonUserIdentity} from '../models';
import {TierService} from '../services/tier.service';
const handleFileUpload = promisify(upload.single('file'));
import {Readable} from 'stream';
import {UserRepository} from '@loopback/authentication-jwt';
import {EmailService} from '../services/email.service';
import {OrganizationSegmentRepository} from '../repositories/organization-segment.repository';
import {SegmentService} from '../services/shopify/segment.service';
import {VipTierRepository} from '../repositories/vip-tier.repository';
import {MetricSegmentRepository} from '../repositories/metric-segment.repository';

@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())


export class ImportShoppersController {
	constructor(
		@service(ShopperInfo) protected shopperInfo: ShopperInfo,
		@repository(LoyaltyCurrencyBalanceRepository) protected loyaltyCurrencyBalanceRepository: LoyaltyCurrencyBalanceRepository,
		@service(LoyaltyBalanceManager) protected loyaltyBalanceManager: LoyaltyBalanceManager,
		@repository(LoyaltyProgramRepository) protected loyaltyProgramRepository: LoyaltyProgramRepository,
		@repository(RaleonUserIdentityRepository) protected raleonUserIdentityRepository: RaleonUserIdentityRepository,
		@repository(RaleonUserRepository) protected raleonUserRepository: RaleonUserIdentityRepository,
		@repository(UserRepository) protected userRepository: UserRepository,
		@service(ShopifyApiInvoker) protected shopifyApiInvoker: ShopifyApiInvoker,
		@service(TierService) protected tierService: TierService,
		@service(EmailService) protected emailService: EmailService,
		@repository(OrganizationSegmentRepository) protected organizationSegmentRepository: OrganizationSegmentRepository,
		@service(SegmentService) protected segmentService: SegmentService,
		@repository(VipTierRepository) protected vipTierRepository: VipTierRepository,
		@repository(MetricSegmentRepository) protected metricSegmentRepository: MetricSegmentRepository,
	) {
	}


	/*
	 * Import shoppers from a CSV file
	 * DANGER this will remove all previous shopper currency data and replace it with the data from the CSV
	 * ONLY run this on stores that are not live and need to import from other systems
	*/
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['raleon-admin'],
		voters: [basicAuthorization],
	})

	@skipGuardCheck()
	@post('/update-birthdays', {
		responses: {
			'200': {
				description: 'Update birthdays',
			},
		},
	})
	async updateBirthdays(
		@param.query.string('orgId') orgId: number,
		@requestBody.file()
		@inject(RestBindings.Http.REQUEST) request: Request,
		@inject(RestBindings.Http.RESPONSE) response: Response,
	): Promise<any> {
		response.setHeader('Content-Type', 'text/plain');
		response.setHeader('Transfer-Encoding', 'chunked');

		try {
			// Await the multer file upload
			await handleFileUpload(request, response);
			const file = request.file;
			if (!file) {
				response.status(400).send('No file uploaded');
				return;
			}

			response.write('File uploaded successfully.\n');

			const csvData: {userId: number; birthMonth: number; birthDay: number; birthYear: number | null}[] = [];

			await new Promise((resolve, reject) => {
				const results: any[] = [];
				const readableStream = new Readable();
				readableStream._read = () => { }; // _read is required but you can noop it
				readableStream.push(file.buffer);
				readableStream.push(null);

				readableStream
					.pipe(csv())
					.on('data', (data) => results.push(data))
					.on('end', () => {
						results.forEach(result => {
							const birthYear = result.birth_year === 'Unknown' ? 1990 : parseInt(result.birth_year, 10);
							const birthMonth = result.birth_month === 'Unknown' ? null : parseInt(result.birth_month, 10);
							const birthDay = result.birth_day === 'Unknown' ? null : parseInt(result.birth_day, 10);

							if (birthMonth && birthDay && birthYear) {
								csvData.push({
									userId: parseInt(result.shopify_id, 10),
									birthMonth: birthMonth,
									birthDay: birthDay,
									birthYear: birthYear,
								});
							}
						});
						resolve(csvData);
					})
					.on('error', (error) => reject(error));
			});

			for (const birthdate of csvData) {
				let {userId, birthMonth, birthDay, birthYear} = birthdate;

				// Validate birthMonth, birthDay, and birthYear
				if (birthMonth < 1 || birthMonth > 12) {
					response.write(`Invalid birthMonth for user ${userId}. Skipping.\n`);
					continue;
				}
				if (birthDay < 1 || birthDay > 31) {
					response.write(`Invalid birthDay for user ${userId}. Skipping.\n`);
					continue;
				}
				if (birthYear && (birthYear < 1900 || birthYear > new Date().getFullYear())) {
					response.write(`Invalid birthYear for user ${userId}. Skipping.\n`);
					continue;
				}

				// Update the birthday
				let birthDate = new Date(birthYear ?? 0, birthMonth - 1, birthDay);

				console.log("UPDATING BIRTHDAY", userId, birthDate);
				let raleonUserIdentity = await this.raleonUserIdentityRepository.findOne({
					where: {
						orgId: orgId,
						identityValue: userId.toString(),
						identityType: 'customer_id',
					},
				});

				if (!raleonUserIdentity) {
					response.write(`User ${userId} not found. Skipping.\n`);
					continue;
				}

				await this.raleonUserIdentityRepository.updateById(raleonUserIdentity.id, {birthday: birthDate});
				response.write(`Updated birthday for user ${userId} to ${birthDate.toISOString().split('T')[0]}.\n`);
			}

			response.end('All operations completed successfully');
		} catch (error) {
			response.status(500).send(`Error during processing: ${error.message}`);
		}
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer', 'raleon-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/export-shoppers', {
		responses: {
			'200': {
				description: 'Export shoppers',
			},
		},
	})
	async exportShoppers(
		@injectUserOrgId() orgId: number,
		@injectUserId() userId: number,
		@inject(RestBindings.Http.RESPONSE) response: Response,
	): Promise<any> {
		// Respond immediately
		response.status(202).send({message: 'Export process started'});

		// Start background process
		this.handleExportShoppers(orgId, userId);
	}

	async handleExportShoppers(orgId: number, userId: number): Promise<void> {
		try {
			console.log('Starting export and email process...');

			const postgresChunkSize = 1000;
			let users = [];
			let offset = 0;
			let batch = [];

			// Fetch users in chunks to avoid overloading the database
			do {
				batch = await this.raleonUserIdentityRepository.find({
					where: {
						orgId: orgId,
						identityType: 'customer_id',
					},
					include: [
						{
							relation: 'raleonUser',
							scope: {
								include: [
									{
										relation: 'loyaltyCurrencyBalance',
									}
								],
							},
						},
						{
							relation: 'vipTier',
						}
					],
					limit: postgresChunkSize,
					skip: offset,
				});

				users.push(...batch);
				offset += postgresChunkSize;
			} while (batch.length === postgresChunkSize);

			const shopifyIds = users.map((user) => user.identityValue);

			// Split Shopify IDs into batches of 250
			const chunkSize = 250;
			const shopifyCustomers = [];

			for (let i = 0; i < shopifyIds.length; i += chunkSize) {
				const batchIds = shopifyIds.slice(i, i + chunkSize);
				const path = `/shopper-info?ids=${batchIds.join(',')}`;
				const shopifyResponse = await this.shopifyApiInvoker.invokeAdminApi(
					orgId,
					path,
					'GET',
				);
				if (shopifyResponse?.customers) {
					shopifyCustomers.push(...shopifyResponse.customers);
				}
			}

			if (shopifyCustomers.length === 0) {
				console.error('Failed to fetch Shopify customers');
				return;
			}

			const csvHeaders = [
				'email',
				'customer_id',
				'first_name',
				'last_name',
				'birth_month',
				'birth_day',
				'birth_year',
				'current_points',
				'vip_tier',
			].join(',');

			const csvData = [csvHeaders];

			for (const user of users) {
				const shopifyData = shopifyCustomers.find(
					(shopifyUser: {id: number}) =>
						shopifyUser.id.toString() === user.identityValue,
				);

				const email = shopifyData?.email || '';
				const customerId = user.identityValue || '';
				const firstName = shopifyData?.first_name || '';
				const lastName = shopifyData?.last_name || '';
				const birthDate = user.birthday;
				const birthMonth = birthDate ? birthDate.getMonth() + 1 : 'Unknown';
				const birthDay = birthDate ? birthDate.getDate() : 'Unknown';
				const birthYear = birthDate ? birthDate.getFullYear() : 'Unknown';
				const currentPoints = user.raleonUser?.loyaltyCurrencyBalance?.balance || 0;
				const vipTier = user.vipTier?.name || '';

				const csvRow = [
					email,
					customerId,
					firstName,
					lastName,
					birthMonth,
					birthDay,
					birthYear,
					currentPoints,
					vipTier,
				].join(',');

				csvData.push(csvRow);
			}

			const csvContent = csvData.join('\n');
			const currentUser = await this.userRepository.find({
				where: {
					id: userId
				}
			})

			// Send the email with the CSV attachment
			if (currentUser.length > 0) {
				await this.emailService.sendEmailWithAttachment(currentUser[0].email, csvContent);
			}

			// You can log the success or send a notification here if needed
			console.log('Export and email process completed successfully');
		} catch (error) {
			console.error(`Error during export: ${error.message}`);
		}
	}



	/*
	 * Import shoppers from a CSV file
	 * DANGER this will remove all previous shopper currency data and replace it with the data from the CSV
	 * ONLY run this on stores that are not live and need to import from other systems
	*/
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['raleon-admin'],
		voters: [basicAuthorization],
	})

	@skipGuardCheck()
	@post('/import-shoppers', {
		responses: {
			'200': {
				description: 'Import shoppers',
			},
		},
	})
	async importShoppers(
		@param.query.string('orgId') orgId: number = 1000607,
		@param.query.boolean('loadOldPoints') loadOldPoints: boolean = false,
		@param.query.boolean('useEmailFromShopify') useEmailFromShopify: boolean = false,
		// @param.query.boolean('dryRun') dryRun: boolean,
		@requestBody.file()
		@inject(RestBindings.Http.REQUEST) request: Request,
		@inject(RestBindings.Http.RESPONSE) response: Response,
	): Promise<any> {
		response.setHeader('Content-Type', 'text/plain');
		response.setHeader('Transfer-Encoding', 'chunked');
		let users: any[] = []
		if (useEmailFromShopify) {
			users = await this.gatherAllUsers(orgId);
			response.write('Fetched users from Shopify.\n');
			response.flushHeaders();
		}

		console.log("***** USERS", users)
		try {
			// Await the multer file upload
			await handleFileUpload(request, response);
			const file = request.file;
			if (!file) {
				response.status(400).send('No file uploaded');
				return;
			}

			response.write('File uploaded successfully.\n');

			const results = await this.processFile(file);
			//Find the program and its currency from the orgId
			const program = await this.loyaltyProgramRepository.findOne(
				{
					where:
					{
						orgId: orgId
					},
					include: [
						{
							relation: 'loyaltyCurrencies'
						}
					]
				},
			);

			let currencyId = program?.loyaltyCurrencies[0].id;
			if (!currencyId) {
				response.status(400).send('No currency found for the organization');
				return;
			}

			const importedIdentities = [];
			// Process user updates or creations
			for (const [index, result] of results.entries()) {


				// Update the response every 100 records, for example
				if (index % 100 === 0) {
					response.write(`Processing record ${index + 1} of ${results.length}\n`);
					response.flushHeaders();
				}

				let user: any = {};
				if (useEmailFromShopify) {
					console.log("Comparing", result.email, users.map(user => user.email));
					user = users.find(user => user.email === result.email);
				}
				else {
					let shopify_id = result.shopify_id; //Important to be named here
					if (!shopify_id) {
						response.status(400).send('No shopify_id found in the CSV');
						return;
					}

					let filter: Filter<RaleonUserIdentity> = {
						where: {
							orgId: orgId,
							identityValue: shopify_id,
							identityType: 'customer_id'
						},
						include: [
							{
								relation: 'raleonUser',
								scope: {
									include: [
										{
											relation: 'loyaltyCurrencyBalance',
											scope: {
												include: [
													{
														relation: 'loyaltyCurrencyTxLogs'
													}
												]
											}
										},
									]
								}
							}
						]
					};

					let userResult: any = await this.raleonUserIdentityRepository.findOne(filter);
					if (!userResult) {
						console.log("No user found for shopify_id", shopify_id);
						let raleonUser = await this.raleonUserRepository.create({});
						//Create User
						let newUser = {
							orgId: orgId,
							identityType: 'customer_id',
							identityValue: shopify_id,
							raleonUserId: raleonUser.id,
							firstLoginDate: new Date(), //set so we dont grant welcome bonus
						};
						let newIdentity = await this.raleonUserIdentityRepository.create(newUser);

						userResult = await this.raleonUserIdentityRepository.findOne(filter);
					}

					importedIdentities.push(userResult);

					user = {
						raleon_user_id: userResult?.raleonUser?.id,
						loyalty_currency_balance_id: userResult?.raleonUser?.loyaltyCurrencyBalance?.id,
						loyalty_balance: userResult?.raleonUser?.loyaltyCurrencyBalance?.balance,
						id: userResult?.id,
					}
				}

				if (user) {
					console.log('UPDATE USER', user, result);
					let pointsToSet = parseInt(result.points_balance);
					let points_earned = loadOldPoints ? parseInt(result.points_earned) : 0;
					let currencyBalanceId = user.loyalty_currency_balance_id;

					if (currencyBalanceId && pointsToSet != user.loyalty_balance) {
						try {
							await this.loyaltyCurrencyBalanceRepository.deleteById(currencyBalanceId);
						} catch (error) {
							console.error(`Failed to delete currency balance with ID ${currencyBalanceId}: ${error.message}`);
						}
					}

					if (pointsToSet == user.loyalty_balance) {
						console.log('SKIPPING USER', user, result);
					}
					else if (points_earned > 0) {
						let balance = pointsToSet - points_earned;
						let request1 = {
							raleonUserId: user.raleon_user_id,
							balanceChange: points_earned,
							info: 'Initial points balance',
							skipRewards: true,
						};
						await this.loyaltyBalanceManager.updateBalanceAndLog(currencyId, request1, points_earned);

						let request2 = {
							raleonUserId: user.raleon_user_id,
							balanceChange: balance,
							info: 'Setting Spent Points',
							skipRewards: true
						};
						await this.loyaltyBalanceManager.updateBalanceAndLog(currencyId, request2);

					}
					else if (pointsToSet > 0) {
						let request = {
							raleonUserId: user.raleon_user_id,
							balanceChange: pointsToSet,
							info: 'Initial points balance',
							skipRewards: true
						};
						await this.loyaltyBalanceManager.updateBalanceAndLog(currencyId, request, pointsToSet);

					}

					//Birthday
					let birthMonth = result.birth_month;
					let birthDay = result.birth_day;
					let birthYear = result.birth_year === 'Unknown' ? 1990 : (result.birth_year ? parseInt(result.birth_year, 10) : 0);

					//Verify the birthday is valid
					if (birthMonth && birthDay && birthYear) {
						if (birthMonth < 1 || birthMonth > 12) {
							birthMonth = null;
						}
						if (birthDay < 1 || birthDay > 31) {
							birthDay = null;
						}
					}

					//Update birthday if it is valid, and a number
					if (birthMonth && birthDay && birthYear && !isNaN(birthMonth) && !isNaN(birthDay) && !isNaN(birthYear)) {
						let birthDate = new Date(birthYear, birthMonth - 1, birthDay);


						//user.id holds the raleonUserIdentity id
						console.log("UPDATING BIRTHDAY", user.id, birthDate);
						await this.raleonUserIdentityRepository.updateById(user.id, {birthday: birthDate});
					}
				} else {
					console.log('STILL NO USER>>>>>>><<<<<<<<<', result);
				}
			}


			// Update VIP tiers
			await this.tierService.reassignUsersForAllTiers(orgId, importedIdentities);

			response.write('All records have been processed.\n');
			response.end('All operations completed successfully');
		} catch (error) {
			response.status(500).send(`Error during processing: ${error.message}`);
		}
	}

	async gatherAllUsers(orgId: number): Promise<any[]> {
		let page = 1;
		let users: any[] = [];
		let requestCount = 250;
		let finished = false;
		while (!finished) {
			console.log("******CALLING SHOPPER INFO", page, requestCount);
			const shopifyUsers = await this.shopperInfo.getShopperInfo(orgId, page, requestCount);
			if (shopifyUsers.shoppers.length < requestCount || shopifyUsers.pagination.nextPage === null) {
				users = users.concat(shopifyUsers.shoppers);
				finished = true;
			} else {
				users = users.concat(shopifyUsers.shoppers);
				page++;
			}
		}
		return users;
	}

	async processFile(file: Express.Multer.File): Promise<any[]> {
		const bufferStream = new stream.PassThrough();
		const results: any[] = [];

		return new Promise((resolve, reject) => {
			bufferStream
				.pipe(csv())
				.on('data', (data: any) => {
					// Skip empty rows (where all fields are empty/null/undefined)
					if (Object.values(data).some(value => value !== null && value !== undefined && value !== '')) {
						results.push(data);
					}
				})
				.on('end', () => resolve(results))
				.on('error', (error: any) => reject(error));
		});
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer', 'raleon-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/export-segment-shoppers', {
		responses: {
			'200': {
				description: 'Export segment shoppers',
			},
		},
	})
	async exportSegmentShoppers(
		@param.query.string('segmentId') segmentId: number,
		@injectUserOrgId() orgId: number,
		@injectUserId() userId: number,
		@inject(RestBindings.Http.RESPONSE) response: Response,
	): Promise<any> {
		return this.handleDirectSegmentDownload(orgId, segmentId, response, userId);
	}

	async handleDirectSegmentDownload(
		orgId: number,
		segmentId: number,
		response: Response,
		userId: number
	): Promise<void> {
		try {
			console.log(`Starting direct segment download for segment ID ${segmentId}...`);

			// Get the segment from the database
			const segment = await this.organizationSegmentRepository.findById(segmentId, {
				include: [{
					relation: 'organizationSegmentDetails',
					scope: {
						include: [{relation: 'metricSegment'}],
					},
				}],
			});

			if (!segment) {
				response.status(404).send({error: 'Segment not found'});
				return;
			}

			// Check if the segment belongs to the organization
			if (segment.orgId !== orgId) {
				response.status(403).send({error: 'Segment does not belong to this organization'});
				return;
			}

			// Get segment details
			const details = await this.organizationSegmentRepository.organizationSegmentDetails(segmentId).find({
				include: [{relation: 'metricSegment'}],
			});

			// Retrieve metric segment data from the included relation
			// First, get the metric segment IDs we need to retrieve
			const metricSegmentIds = details
				.filter(detail => detail.metricSegmentId)
				.map(detail => detail.metricSegmentId);

			// Fetch metric segments directly
			const metricSegments = await this.metricSegmentRepository.find({
				where: {
					id: {inq: metricSegmentIds}
				}
			});

			// Build the query conditions for this segment
			const data = {
				positive: details
					.filter(detail => detail.include && detail.metricSegmentId)
					.map(detail => {
						// Find the corresponding metric segment
						const ms = metricSegments.find(m => m.id === detail.metricSegmentId);
						return {
							id: ms?.id,
							query: detail.queryOverride || ms?.query
						};
					}),
				negative: details
					.filter(detail => !detail.include && detail.metricSegmentId)
					.map(detail => {
						// Find the corresponding metric segment
						const ms = metricSegments.find(m => m.id === detail.metricSegmentId);
						return {
							id: ms?.id,
							query: detail.queryOverride || ms?.query
						};
					}),
			};

			const combinedConditions = [];

			// Process positive queries
			for (const signal of data.positive) {
				if (signal!.query) {
					try {
						const parsedQuery = JSON.parse(signal!.query);
						combinedConditions.push(parsedQuery);
					} catch (error) {
						console.error(
							`Error parsing positive MetricSegment query (ID: ${signal!.id}):`,
							error
						);
					}
				}
			}

			// Process negative queries (excluded conditions)
			for (const signal of data.negative) {
				if (signal!.query) {
					try {
						const parsedQuery = JSON.parse(signal!.query);
						// Instead of flipping operators, we'll just exclude these records with a NOT EXISTS
						// This is a simplified approach for direct download
						combinedConditions.push({
							operator: '!=',
							field: 'id',
							value: 'id'  // This will effectively be ignored but prevents syntax errors
						});
					} catch (error) {
						console.error(
							`Error parsing negative MetricSegment query (ID: ${signal!.id}):`,
							error
						);
					}
				}
			}

			// Combine all conditions into a single SQL WHERE clause
			const combinedQuery = {
				operator: 'and',
				conditions: [{field: 'orgId', operator: '=', value: orgId}, ...combinedConditions],
			};

			// Build the SQL condition from the combined query
			const buildSqlCondition = (query: any): string => {
				if (!query || typeof query !== 'object') {
					throw new Error('Invalid query object');
				}

				// Track whether we need to join with attributes table
				let needsAttributesJoin = false;
				const attributeConditions: any[] = [];

				const processCondition = (condition: any): string => {
					if (!condition) return '1=1';

					if (condition.operator === 'or' || condition.operator === 'and') {
						// Process nested conditions
						const operator = condition.operator.toUpperCase();
						const subConditions = condition.conditions
							.map(processCondition)
							.filter(Boolean); // Remove invalid conditions
						return `(${subConditions.join(` ${operator} `)})`;
					}

					// If no operator, assume a single condition
					if (condition.conditions && Array.isArray(condition.conditions)) {
						// Default to AND if no operator is specified
						const subConditions = condition.conditions.map(processCondition).filter(Boolean);
						return `(${subConditions.join(' AND ')})`;
					}

					// Process individual condition
					const sqlOperator = getSqlOperator(condition.operator);

					if (condition.table === 'raleonuseridentityattributes') {
						// For custom attributes, track separately
						needsAttributesJoin = true;
						const field = condition.field;
						const value =
							typeof condition.value === 'string' && condition.value !== 'NULL'
								? `'${condition.value.replace(/'/g, "''")}'`
								: condition.value;

						attributeConditions.push({
							key: field,
							operator: sqlOperator,
							value: value
						});

						// Placeholder - actual condition will be added in final query construction
						return '1=1';
					} else {
						// Direct raleonuseridentity attribute
						const field = condition.field;
						const value =
							typeof condition.value === 'string' && condition.value !== 'NULL'
								? `'${condition.value.replace(/'/g, "''")}'`
								: condition.value;

						return `public.raleonuseridentity.${field} ${sqlOperator} ${value}`;
					}
				};

				// Process the main query
				const mainCondition = processCondition(query);

				// Construct the final query with optional join
				if (!needsAttributesJoin) {
					return mainCondition;
				}

				// Construct JOIN conditions for attributes
				const attributeJoinConditions = attributeConditions.map((attr, index) =>
					`rua${index}."key" = '${attr.key}' AND rua${index}.value ${attr.operator} ${attr.value}`
				).join(' AND ');

				// Construct the full query with multiple LEFT JOINs for each attribute condition
				return `
				  (${mainCondition}) AND
				  ${attributeConditions.map((_, index) =>
					`EXISTS (
					  SELECT 1
					  FROM public.raleonuseridentityattributes rua${index}
					  WHERE rua${index}.raleonuseridentityid = public.raleonuseridentity.id
					  AND ${attributeJoinConditions}
					)`
				).join(' AND ')}
				`;
			};

			const getSqlOperator = (operator: string): string => {
				const operatorMapping: Record<string, string> = {
					'>': '>',
					'>=': '>=',
					'<': '<',
					'<=': '<=',
					'=': '=',
					'!=': '<>',
					'IS': 'IS',
					'IS NOT': 'IS NOT',
				};
				return operatorMapping[operator] || '=';
			};

			const sqlCondition = buildSqlCondition(combinedQuery);
			console.log('Generated SQL Condition:', sqlCondition);

			// Execute SQL query to get all customers in the segment
			const query = `
			SELECT
				"id",
				"identityvalue",
				"raleonuserid",
				"identitytype",
				"viptierid",
				"birthday"
			FROM "public"."raleonuseridentity"
			WHERE ${sqlCondition} AND "identitytype" = 'customer_id'
			`;

			const segmentUsers = await this.raleonUserIdentityRepository.dataSource.execute(query);

			if (!segmentUsers || segmentUsers.length === 0) {
				response.status(404).send({
					error: 'No customers found in the segment',
					status: 'error',
					message: 'No customers were found in this segment. Please modify your segment criteria.'
				});
				return;
			}

			console.log(`Found ${segmentUsers.length} customers in segment ${segmentId}`);

			// Get Shopify user details for these users
			const shopifyIds = segmentUsers.map((user: any) => user.identityvalue);

			// Split Shopify IDs into batches of 250 for API calls
			const chunkSize = 250;
			const shopifyCustomers = [];

			for (let i = 0; i < shopifyIds.length; i += chunkSize) {
				const batchIds = shopifyIds.slice(i, i + chunkSize);
				const path = `/shopper-info?ids=${batchIds.join(',')}`;
				const shopifyResponse = await this.shopifyApiInvoker.invokeAdminApi(
					orgId,
					path,
					'GET',
				);
				if (shopifyResponse?.customers) {
					shopifyCustomers.push(...shopifyResponse.customers);
				}
			}

			if (shopifyCustomers.length === 0) {
				response.status(400).send({
					error: 'Failed to fetch customer details',
					status: 'error',
					message: 'Could not retrieve customer information. Please try again later.'
				});
				return;
			}

			// Get Raleon user details including loyalty balance
			const userIds = segmentUsers.map((user: any) => user.raleonuserid).filter(Boolean);
			let loyaltyBalances: any[] = [];

			if (userIds.length > 0) {
				// Fetch loyalty balances in batches if needed
				loyaltyBalances = await this.loyaltyCurrencyBalanceRepository.find({
					where: {
						raleonUserId: {
							inq: userIds
						}
					}
				});
			}

			// Create CSV with the user data
			const csvHeaders = [
				'email',
				'customer_id',
				'first_name',
				'last_name',
				'birth_month',
				'birth_day',
				'birth_year',
				'current_points',
				'vip_tier',
			].join(',');

			const csvData = [csvHeaders];

			for (const user of segmentUsers) {
				const shopifyData = shopifyCustomers.find(
					(shopifyUser: {id: number}) =>
						shopifyUser.id.toString() === user.identityvalue,
				);

				// Find loyalty balance for this user
				const loyaltyBalance = loyaltyBalances.find(
					(balance: any) => balance.raleonUserId === user.raleonuserid
				);

				const email = shopifyData?.email || '';
				const customerId = user.identityvalue || '';
				const firstName = shopifyData?.first_name || '';
				const lastName = shopifyData?.last_name || '';
				const birthDate = user.birthday;
				const birthMonth = birthDate ? birthDate.getMonth() + 1 : '';
				const birthDay = birthDate ? birthDate.getDate() : '';
				const birthYear = birthDate ? birthDate.getFullYear() : '';
				const currentPoints = loyaltyBalance?.balance || 0;

				// Get VIP tier name if available
				let vipTierName = '';
				if (user.viptierid) {
					try {
						// Get tier directly from repository
						const vipTier = await this.vipTierRepository.findById(user.viptierid);
						vipTierName = vipTier?.name || '';
					} catch (e) {
						console.error('Error fetching tier name:', e);
					}
				}

				const csvRow = [
					email,
					customerId,
					firstName,
					lastName,
					birthMonth,
					birthDay,
					birthYear,
					currentPoints,
					vipTierName,
				].join(',');

				csvData.push(csvRow);
			}

			const csvContent = csvData.join('\n');

			// Get the current user's email
			const currentUser = await this.userRepository.findById(userId.toString());
			if (!currentUser || !currentUser.email) {
				response.status(400).send({
					status: 'error',
					message: 'Could not determine your email address. Please check your account settings.'
				});
				return;
			}

			// Send the segment data via email instead of download
			const fileName = `segment_customers_${segmentId}.csv`;
			const emailSubject = `Segment Export: ${segment.name}`;

			// Use the email service to send the CSV as an attachment
			await this.emailService.sendSegmentExportEmail(
				currentUser.email,
				csvContent,
				fileName,
				emailSubject
			);

			// Return a success response with a notification
			response.status(200).send({
				status: 'success',
				message: `Your segment data is being prepared and will be sent to ${currentUser.email} shortly. Please check your inbox in a few minutes.`,
				segmentName: segment.name,
				totalCustomers: segmentUsers.length,
				emailSentTo: currentUser.email
			});

			console.log(`Email with segment data sent for segment ${segmentId} with ${segmentUsers.length} customers`);
		} catch (error) {
			console.error(`Error during segment export: ${error.message}`);
			response.status(500).send({
				status: 'error',
				message: `There was a problem exporting your segment: ${error.message}`,
				error: error.message
			});
		}
	}
}
