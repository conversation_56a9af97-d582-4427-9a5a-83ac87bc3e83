import {api, get, param} from '@loopback/rest';
import {authenticate} from '@loopback/authentication';
import {GuardSkipStrategy, guardStrategy, skipGuardCheck} from '../interceptors';
import { createApi } from 'unsplash-js';
import {service} from '@loopback/core';
import {UnsplashImageService} from '../services/unsplash-image.service';

@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class UnsplashImagesController {

	unsplashApi: any;

	constructor(
		@service(UnsplashImageService)
		private unsplashImageService: UnsplashImageService,
	) {}

	@skipGuardCheck()
	@authenticate('jwt')
	@get('/unsplash-images', {
		responses: {
			'200': {
				description: 'Returns whether or not loyalty is enabled in the org',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async search(
		@param.query.string('query') query: string,
		@param.query.number('page') page: number,
		@param.query.number('per_page') perPage: number,
		@param.query.string('orientation') orientation: string = 'landscape',
	): Promise<any> {
		return await this.unsplashImageService.getPhotos(
			query,
			page,
			perPage,
			orientation,
		);
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@get('/unsplash-track-download', {
		responses: {
			'200': {
				description: 'Returns whether or not loyalty is enabled in the org',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async trackDownload(
		@param.query.string('download_location') downloadLocation: string,
	): Promise<any> {
		try {
			await this.unsplashImageService.trackDownload(downloadLocation);
		} catch (error) {
			console.log(`error tracking download: ${JSON.stringify(error)}`)
		}
		return { success: true }
	}
}
