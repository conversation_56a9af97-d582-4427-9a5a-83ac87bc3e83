import {
	Filter,
	repository
} from '@loopback/repository';
import {
	api,
	get,
	getModelSchemaRef,
	param,
	post,
	requestBody,
	response,
} from '@loopback/rest';
import {
	CustomerOffer,
	LoyaltyRewardDefinitionWithRelations,
	Offer,
	OfferWithRelations,
	RaleonUserIdentity,
	RewardCoupon
} from '../models';
import {
	OfferRepository,
	CustomerOfferRepository,
	RewardCouponRepository,
	LoyaltyRewardDefinitionRepository,
	RaleonUserIdentityRepository,
	CurrencyRepository
} from '../repositories';
import {OrgGuardPropertyStrategy, skipGuardCheck, guardStrategy, injectUserOrgId, modelIdForGuard} from '../interceptors';
import {basicAuthorization} from '../services';
import {authorize} from '@loopback/authorization';
import {authenticate} from '@loopback/authentication';
import {AdminUiController, convertCurrencyPlaceholdersToValues} from './admin-ui.controller';
import {inject, service} from '@loopback/core';
import {LoyaltyRedemptionService} from '../services/loyalty/loyalty-redemption.service';
import {InventoryCoupon} from '../models/loyalty/inventory-coupon.model';
import {ShopifyApiInvoker} from '../services/shopify/shopify-api-invoker.service';
import {LoyaltyEventPublisher} from '../services/event-stream/loyalty-event-publisher.service';
import {getURL} from '../utils';
const fetch = require('node-fetch');

const OFFER_DEV_API = 'dl94meli6k.execute-api.us-east-1.amazonaws.com';
//const INTEGRATION_ADAM_API_URL = 'https://gnlppj3qp0.execute-api.us-east-1.amazonaws.com/v1/integrations';
const OFFER_URL = process.env.OFFER_API_URL || OFFER_DEV_API;

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardPropertyStrategy<Offer>({
	orgIdModelPropertyName: 'orgId',
	repositoryClass: OfferRepository
}))
export class OfferController {
	constructor(
		@repository(OfferRepository)
		public offerRepository: OfferRepository,
		@repository(CustomerOfferRepository)
		public customerOfferRepository: CustomerOfferRepository,
		@repository(RewardCouponRepository)
		public rewardCouponRepository: any,
		@repository(LoyaltyRewardDefinitionRepository)
		public loyaltyRewardDefinitionRepository: any,
		@repository(RaleonUserIdentityRepository)
		public userIdentityRepository: any,
		@service(AdminUiController)
		public adminUiController: AdminUiController,
		@service(LoyaltyRedemptionService)
		private loyaltyRedemptionService: LoyaltyRedemptionService,
		@inject('datasources.dev_db') protected dataSource: any,
		@service(ShopifyApiInvoker)
		public shopifyApiInvoker: ShopifyApiInvoker,
		@service(LoyaltyEventPublisher)
		public loyaltyEventPublisher: LoyaltyEventPublisher,
		@repository(CurrencyRepository)
		private currencyRepository: CurrencyRepository,
	) { }

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/offers')
	@response(200, {
		description: 'Offer model instance',
		content: {
			'application/json': {
				schema: {
					'x-ts-type': Offer
				},
			},
		},
	})
	async create(
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						properties: {
							type: {
								type: 'string',
								example: 'smart or basic'
							},
							name: {
								type: 'string',
								example: 'Summer Sale'
							},
							discountType: {
								type: 'string',
								example: 'dollars-off-coupon or percent-discount'
							}
						},
						required: ['type', 'name', 'discountType']
					},
				},
			},
		})
		offerData: Omit<Offer, 'id' | 'orgId' | 'generated'>,
		@injectUserOrgId() orgId: number,
	): Promise<Offer> {
		const offerDataWithOrgId = {
			...offerData,
			orgId
		};
		const newOffer = await this.offerRepository.create(offerDataWithOrgId);
		// Logic to send the offer to targeted users can be added here
		return newOffer;
	}


	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/offer/{id}/generate')
	@response(200, {
		description: 'Generate offer',
		content: {
			'application/json': {
				schema: {
					'x-ts-type': Object
				},
			},
		},
	})
	async generate(
		@modelIdForGuard(Offer)
		@param.path.number('id') id: number,
		@injectUserOrgId() orgId: number,
	): Promise<void> {
		//Remove all previous customer offers
		await this.customerOfferRepository.deleteAll({ offerId: id });

		let url = `/v1/offer?orgId=${orgId}&offerId=${id}`;
		const signedRequest = getURL(url, 'GET', undefined, OFFER_URL);

		let response;
		try {
			response = await fetch(`https://${OFFER_URL}${url}`, signedRequest);
		} catch (e) {
			console.error('Error generating offer', e);
			throw new Error('Error generating offer');
		}

		return response.text();
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/offer/{id}/send')
	@response(200, {
		description: 'Send offer',
		content: {
			'application/json': {
				schema: {
					'x-ts-type': Object
				},
			},
		},
	})
	async send(
		@modelIdForGuard(Offer)
		@param.path.number('id') id: number,
		@injectUserOrgId() orgId: number,
	): Promise<Array<InventoryCoupon>> {
		const customerOffers = await this.customerOfferRepository.find({
			where: {
				offerId: id
			},
			include: [
				{ relation: 'loyaltyRewardDefinition', scope: {
					include: ['rewardCoupon']
				} },
				{ relation: 'raleonUser', scope: {
					include: ['raleonUserIdentities'] }
				},
			]
		});

		const coupons = await this.loyaltyRedemptionService.bulkGrantOffers(customerOffers, orgId, this.dataSource);

		if (coupons.length) {
			this.processAndPublishEvents(coupons, customerOffers, orgId)
				.then(() => console.log('Events published successfully'))
				.catch((e) => console.error('Error publishing events', e));
		}

		return coupons;
	}

	private async buildBulkOfferEventPayload(coupons: InventoryCoupon[],  customerOffers: CustomerOffer[], orgId: number) {
		const rewardCouponToInventoryCoupon = new Map<number, number>();
		const inventoryCoupons = new Map<number, InventoryCoupon>();
		coupons.forEach(coupon => {
			rewardCouponToInventoryCoupon.set(coupon.rewardCouponId, coupon.id!);
			inventoryCoupons.set(coupon.id!, coupon);
		});

		let orgCurrency = await this.currencyRepository.findOne({
			where: { organizationId: orgId },
			include: [ { relation: 'supportedCurrencies' } ]
		});

		return customerOffers.map((offer: CustomerOffer) => {
			const inventoryCouponId = rewardCouponToInventoryCoupon.get((offer.loyaltyRewardDefinition as any).rewardCoupon.id);
			const matchingCoupon = inventoryCoupons.get(inventoryCouponId!);
			if (!matchingCoupon) {
				throw new Error('Coupon not found');
			}
			const rewardCoupon = (offer.loyaltyRewardDefinition as any).rewardCoupon;
			let couponName = matchingCoupon.name;
			if (rewardCoupon.amountType == 'dollar-off-product') {
				couponName = convertCurrencyPlaceholdersToValues(couponName, orgCurrency!);
			}
			return {
				customerId: (offer as any).raleonUser.raleonUserIdentities[0].identityValue,
				rewardName: encodeURIComponent(couponName),
				rewardAmount: rewardCoupon.amount,
				rewardAmountType: rewardCoupon.amountType,
				productImageUrl: rewardCoupon.imageURL,
				expiresIn: rewardCoupon.expiresInDays,
				externalId: rewardCoupon.externalId,
				externalName: rewardCoupon.externalName,
				externalHandle: rewardCoupon.externalLink,
				couponCode: matchingCoupon.uuid,
				expirationDate: matchingCoupon.expiration,
			};
		});
	}

	async processAndPublishEvents(coupons: any[], customerOffers: any[], orgId: number) {
		const events = await this.buildBulkOfferEventPayload(coupons, customerOffers, orgId);
		const chunks = this.chunkArray(events, 900);

		const publishPromises = chunks.map((chunk) => this.loyaltyEventPublisher.bulkPublishOfferReceivedEvent(chunk, orgId));

		await Promise.all(publishPromises);
		console.log('All events have been successfully published.');
	}

	// Helper function to chunk the array
	chunkArray(array: any[], chunkSize: number): any[][] {
		const results = [];
		for (let i = 0; i < array.length; i += chunkSize) {
			results.push(array.slice(i, i + chunkSize));
		}
		return results;
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/offer/{id}/archive')
	@response(200, {
		description: 'Archive offer',
		content: {
			'application/json': {
				schema: {
					'x-ts-type': Object
				},
			},
		},
	})
	async archive(
		@param.path.number('id') id: number,
	): Promise<void> {
		await this.offerRepository.updateById(id, {archived: true});
		return;
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/offer/{id}/unarchive')
	@response(200, {
		description: 'Unarchive offer',
		content: {
			'application/json': {
				schema: {
					'x-ts-type': Object
				},
			},
		},
	})
	async unarchive(
		@param.path.number('id') id: number,
	): Promise<void> {
		await this.offerRepository.updateById(id, {archived: false});
		return;
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/offers')
	@response(200, {
		description: 'Array of Offer model instances (non-archived)',
		content: {
			'application/json': {
				schema: {
					type: 'array',
					items: {
						'x-ts-type': Offer
					},
				},
			},
		},
	})
	async find(
		@injectUserOrgId() orgId: number,
	): Promise<Offer[]> {
		const offers = await this.offerRepository.find({
			where: {
				orgId: orgId,
				archived: false,
			},
		});

		const collectionAndOffer = new Map<string, Offer>();

		for (const offer of offers) {
			if (!offer.collectionId) {
				throw new Error('Collection ID not found');
			}
			collectionAndOffer.set(offer.collectionId, offer);
		}

		await this.fetchAndAttachProductsToOffers(collectionAndOffer, orgId);

		console.log(JSON.stringify(Array.from(collectionAndOffer.values())));

		return Array.from(collectionAndOffer.values());
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/offer/{id}')
	@response(200, {
		description: 'Offer model instance (non-archived)',
		content: {
			'application/json': {
				schema: {
					'x-ts-type': Offer,
				},
			},
		},
	})
	async findById(
		@modelIdForGuard(Offer)
		@param.path.number('id') id: number,
		@injectUserOrgId() orgId: number,
	): Promise<Offer> {
		const offer = await this.offerRepository.findOne({
			where: {
				id: id,
				orgId: orgId,
				archived: false,
			},
		});

		if (!offer) {
			throw new Error('Offer not found');
		}

		if (!offer.collectionId) {
			throw new Error('Collection ID not found');
		}

		const collectionAndOffer = new Map<string, Offer>();
		collectionAndOffer.set(offer.collectionId, offer);

		await this.fetchAndAttachProductsToOffers(collectionAndOffer, orgId);

		return collectionAndOffer.get(offer.collectionId) as Offer;
	}

	private async fetchAndAttachProductsToOffers(
		collectionAndOffer: Map<string, Offer>,
		orgId: number,
	) {
		const productsWithCollections = await Promise.all(
			Array.from(collectionAndOffer.keys()).map(async collectionId => {
				const data = await this.shopifyApiInvoker.invokeAdminApi(
					orgId,
					`products-in-collection?collectionId=${collectionId}`,
					'GET',
				);
				return data;
			})
		);

		productsWithCollections.forEach(productData => {
			const products = productData?.data?.collection?.products?.edges;
			if (!products?.length) {
				console.log(`no products for collectionId : ${productData?.data?.collection?.title}`)
				return;
			}
			const productInfo = products.map((product: any) => {
				return {
					id: product.node.id.match(/\d+/)[0],
					name: product.node.title,
					imageURL: product.node.images.edges[0].node.url,
					price: product.node.variants.edges[0].node.price, // Assuming only one variant
					sku: product.node.variants.edges[0].node.sku,
					title: product.node.title,
					handle: product.node.handle,
				};
			});
			const collectionId = productData.data.collection.id.match(/\d+/)[0];
			const offer = collectionAndOffer.get(collectionId);
			if (offer) {
				const newOffer = {
					...offer,
					products: productInfo
				};
				collectionAndOffer.set(collectionId, (newOffer as any));
			}
		});

	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/offers/archived')
	@response(200, {
		description: 'Array of archived Offer model instances',
		content: {
			'application/json': {
				schema: {
					type: 'array',
					items: {
						'x-ts-type': Offer
					},
				},
			},
		},
	})
	async findArchived(
		@injectUserOrgId() orgId: number,
	): Promise<Offer[]> {
		return this.offerRepository.find({
			where: {
				orgId: orgId,
				archived: true,
			}
		});
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/offers/{id}/customer-offers', {
		responses: {
			'200': {
				description: 'Array of Offer has many CustomerOffer',
				content: {
					'application/json': {
						schema: {type: 'array', items: getModelSchemaRef(CustomerOffer)},
					},
				},
			},
		},
	})
	async getCustomerOffers(
		@modelIdForGuard(Offer)
		@param.path.number('id') id: number,
		@param.query.object('filter') filter?: Filter<CustomerOffer>,
	): Promise<CustomerOffer[]> {
		return this.offerRepository.customerOffers(id).find({
			include: [
				{
					relation: 'loyaltyRewardDefinition',
					scope: {
						include: [
							{
								relation: 'rewardCoupon'
							}
						]
					}
				},
			]
		});
	}


	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/offer/{id}/smart-offer')
	@response(200, {
		description: 'Create Smart Offer and associated Loyalty Reward Definition and Reward Coupon',
		content: {
			'application/json': {
				schema: {
					'x-ts-type': CustomerOffer
				},
			},
		},
	})
	async createCO(
		@modelIdForGuard(Offer)
		@param.path.number('id') id: number,
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						properties: {
							userId: {type: 'string', example: '1123413241'},
							daysToRedeem: {type: 'number', example: 30},
							name: {type: 'string', example: 'Discount Coupon'},
							amount: {type: 'number', example: 10.0},
							amountType: {type: 'string', example: 'percent-discount'},
							imageURL: {type: 'string', example: 'https://example.com/image.jpg'},
							minimumOrderTotal: {type: 'number', example: 50.0},
							externalId: {type: 'string', example: '123456'},
							externalName: {type: 'string', example: 'Name of Product'},
							externalLink: {type: 'string', example: 'Slug for Product'},
							appliesOnSubscriptions: {type: 'boolean', example: false},
							combinesWithOrders: {type: 'boolean', example: false},
							combinesWithProducts: {type: 'boolean', example: false},
							combinesWithShipping: {type: 'boolean', example: false},
						},
						required: ['userId', 'daysToRedeem', 'name', 'amount', 'amountType', 'imageURL', 'minimumOrderTotal', 'externalId', 'externalName', 'externalLink']
					},
				},
			},
		}) customerOfferData: {
			userId: string;
			daysToRedeem: number;
			name: string;
			amount: number;
			amountType: string;
			imageURL: string;
			minimumOrderTotal: number;
			externalId: string;
			externalName: string;
			externalLink: string;
			appliesOnSubscriptions: boolean;
			combinesWithOrders: boolean;
			combinesWithProducts: boolean;
			combinesWithShipping: boolean;
			hiddenFromLoyaltyUi: boolean;
		},
		@injectUserOrgId() orgId: number,
	): Promise<CustomerOffer> {
		// Create Reward Coupon
		let couponData: RewardCoupon;
		if (customerOfferData.amountType === 'dollar-off-product') {
			couponData = await this.adminUiController.createDollarOffCoupon(customerOfferData, undefined, undefined, 'dollar-off-product');
		} else {
			couponData = await this.adminUiController.createPercentOffCoupon(customerOfferData, undefined, undefined, undefined, 'percent-off-product');
		}

		// Create Loyalty Reward Definition
		const rewardDefinitionData = {
			daysToRedeem: customerOfferData.daysToRedeem,
			rewardCouponId: couponData.id,
			grantable: true,
			redeemable: false,
			startingInventory: -1,
			maxUserGrants: 1,
			price: 0
		};

		//Find the raleon user identity
		let userIdentity = await this.userIdentityRepository.find(
			{
				where: {
					identityValue: customerOfferData.userId,
					identityType: 'customer_id',
					orgId: orgId
				}
			});
		if (userIdentity.length === 0) {
			throw new Error('User not found');
		}

		// Create Customer Offer
		const customerOfferDataWithRelations = {
			userId: userIdentity[0].raleonUserId,
			offerId: id,
		};
		const customerOfferResult = await this.customerOfferRepository.create(customerOfferDataWithRelations);
		let rewardDef = await this.customerOfferRepository.loyaltyRewardDefinition(customerOfferResult.id).create(rewardDefinitionData);

		return customerOfferResult;
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/offer/{id}/smart-offer-batch')
	@response(200, {
		description: 'Create Batch Smart Offers and associated Loyalty Reward Definitions and Reward Coupons',
		content: {
			'application/json': {
				schema: {
					'x-ts-type': Array,
					items: {'x-ts-type': CustomerOffer}
				},
			},
		},
	})
	async createBatchCO(
		@modelIdForGuard(Offer)
		@param.path.number('id') id: number,
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'array',
						items: {
							type: 'object',
							properties: {
								userId: {type: 'string', example: '1123413241'},
								daysToRedeem: {type: 'number', example: 30},
								name: {type: 'string', example: 'Discount Coupon'},
								amount: {type: 'number', example: 10.0},
								amountType: {type: 'string', example: 'percent-discount'},
								imageURL: {type: 'string', example: 'https://example.com/image.jpg'},
								minimumOrderTotal: {type: 'number', example: 50.0},
								externalId: {type: 'string', example: '123456'},
								externalName: {type: 'string', example: 'Name of Product'},
								externalLink: {type: 'string', example: 'Slug for Product'},
								appliesOnSubscriptions: {type: 'boolean', example: false},
								combinesWithOrders: {type: 'boolean', example: false},
								combinesWithProducts: {type: 'boolean', example: false},
								combinesWithShipping: {type: 'boolean', example: false},
								hiddenFromLoyaltyUi: {type: 'boolean', example: false},
							},
							required: ['userId', 'daysToRedeem', 'name', 'amount', 'amountType', 'imageURL', 'minimumOrderTotal', 'externalId', 'externalName', 'externalLink']
						}
					}
				}
			}
		}) customerOfferData: Array<{
			userId: string;
			daysToRedeem: number;
			name: string;
			amount: number;
			amountType: string;
			imageURL: string;
			minimumOrderTotal: number;
			externalId: string;
			externalName: string;
			externalLink: string;
			appliesOnSubscriptions: boolean;
			combinesWithOrders: boolean;
			combinesWithProducts: boolean;
			combinesWithShipping: boolean;
			hiddenFromLoyaltyUi: boolean;
		}>,
		@injectUserOrgId() orgId: number,
	): Promise<CustomerOffer[]> {
		const customerOffers = [];

		// Extract all user IDs from customerOfferData
		const userIds = customerOfferData.map(offer => offer.userId);

		// Query all user identities at once
		const userIdentities = await this.userIdentityRepository.find({
			where: {
				identityValue: {inq: userIds},
				identityType: 'customer_id',
				orgId: orgId
			}
		});

		const userIdMap = new Map(
			userIdentities.map((identity: {identityValue: any; raleonUserId: any;}) => [identity.identityValue, identity.raleonUserId])
		);

		for (const offerData of customerOfferData) {

			const raleonUserId = userIdMap.get(offerData.userId);
			if (!raleonUserId) {
				console.warn(`User not found for customer ID: ${offerData.userId}`);
				continue;  // Skip this offer
			}

			// Create Reward Coupon
			let couponData: RewardCoupon;
			if (offerData.amountType === 'dollar-off-product') {
				couponData = await this.adminUiController.createDollarOffCoupon(offerData, undefined, undefined, 'dollar-off-product');
			} else {
				couponData = await this.adminUiController.createPercentOffCoupon(offerData, undefined, undefined, undefined, 'percent-off-product');
			}

			// Create Loyalty Reward Definition
			const rewardDefinitionData = {
				daysToRedeem: offerData.daysToRedeem,
				rewardCouponId: couponData.id,
				grantable: true,
				redeemable: false,
				startingInventory: -1,
				maxUserGrants: 1,
				price: 0
			};

			// Create Customer Offer
			const customerOfferDataWithRelations = {
				raleonUserId: raleonUserId as number,
				offerId: id,
			};
			const customerOfferResult = await this.customerOfferRepository.create(customerOfferDataWithRelations);
			let rewardDef = await this.customerOfferRepository.loyaltyRewardDefinition(customerOfferResult.id).create(rewardDefinitionData);

			customerOffers.push(customerOfferResult);
		}

		return customerOffers;
	}
}
