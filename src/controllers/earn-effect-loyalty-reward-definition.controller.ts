import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
  api
} from '@loopback/rest';
import {
  EarnEffect,
  LoyaltyRewardDefinition,
} from '../models';
import {EarnEffectRepository} from '../repositories';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../services';
import {skipGuardCheck} from '../interceptors/crud-guard.interceptor';
import {guardStrategy, GuardSkipStrategy} from '../interceptors/crud-guard.interceptor';

@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class EarnEffectLoyaltyRewardDefinitionController {
  constructor(
    @repository(EarnEffectRepository)
    public earnEffectRepository: EarnEffectRepository,
  ) { }

  @skipGuardCheck()
  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
  })
  @get('/earn-effects/{id}/loyalty-reward-definition', {
    responses: {
      '200': {
        description: 'LoyaltyRewardDefinition belonging to EarnEffect',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LoyaltyRewardDefinition)},
          },
        },
      },
    },
  })
  async getLoyaltyRewardDefinition(
    @param.path.number('id') id: typeof EarnEffect.prototype.id,
  ): Promise<LoyaltyRewardDefinition> {
    return this.earnEffectRepository.loyaltyRewardDefinition(id);
  }
}
