import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
  api,
} from '@loopback/rest';
import {OnboardingState} from '../models';
import {OnboardingStateRepository} from '../repositories';
import {guardStrategy, injectGuardedFilter, injectGuardedWhere, injectUserOrgId, modelForGuard, modelIdForGuard, OrgGuardPropertyStrategy, restrictReadsWithGuard} from '../interceptors';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../services';

@guardStrategy(new OrgGuardPropertyStrategy<OnboardingState>({
	orgIdModelPropertyName: 'orgId',
	repositoryClass: OnboardingStateRepository
}))
@api({basePath: '/api/v1'})
export class OnboardingStateController {
  constructor(
    @repository(OnboardingStateRepository)
    public onboardingStateRepository : OnboardingStateRepository,
  ) {}

//   @post('/onboarding-states')
//   @response(200, {
//     description: 'OnboardingState model instance',
//     content: {'application/json': {schema: getModelSchemaRef(OnboardingState)}},
//   })
//   async create(
//     @requestBody({
//       content: {
//         'application/json': {
//           schema: getModelSchemaRef(OnboardingState, {
//             title: 'NewOnboardingState',
//             exclude: ['id'],
//           }),
//         },
//       },
//     })
//     onboardingState: Omit<OnboardingState, 'id'>,
//   ): Promise<OnboardingState> {
//     return this.onboardingStateRepository.create(onboardingState);
//   }

//   @get('/onboarding-states/count')
//   @response(200, {
//     description: 'OnboardingState model count',
//     content: {'application/json': {schema: CountSchema}},
//   })
//   async count(
//     @param.where(OnboardingState) where?: Where<OnboardingState>,
//   ): Promise<Count> {
//     return this.onboardingStateRepository.count(where);
//   }


  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  @restrictReadsWithGuard({ plural: true })
  @get('/onboarding-states')
  @response(200, {
    description: 'Array of OnboardingState model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(OnboardingState, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(OnboardingState) filter?: Filter<OnboardingState>
  ): Promise<OnboardingState[]> {
    return this.onboardingStateRepository.find(filter);
  }

  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  @patch('/onboarding-states')
  @response(200, {
    description: 'OnboardingState PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(OnboardingState, {partial: true}),
        },
      },
    })
	@modelForGuard(OnboardingState)
    onboardingState: OnboardingState,
	@injectGuardedWhere()
    @param.where(OnboardingState) where?: Where<OnboardingState>,
  ): Promise<Count> {
    return this.onboardingStateRepository.updateAll(onboardingState, where);
  }

  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  @get('/onboarding-states/{id}')
  @response(200, {
    description: 'OnboardingState model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(OnboardingState, {includeRelations: true}),
      },
    },
  })
  async findById(
	@modelIdForGuard(OnboardingState)
    @param.path.number('id') id: number,
    @param.filter(OnboardingState, {exclude: 'where'}) filter?: FilterExcludingWhere<OnboardingState>
  ): Promise<OnboardingState> {
    return this.onboardingStateRepository.findById(id, filter);
  }

  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  @patch('/onboarding-states/{id}')
  @response(204, {
    description: 'OnboardingState PATCH success',
  })
  async updateById(
	@modelIdForGuard(OnboardingState)
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(OnboardingState, {partial: true}),
        },
      },
    })
    onboardingState: OnboardingState,
  ): Promise<void> {
    await this.onboardingStateRepository.updateById(id, onboardingState);
  }

//   @put('/onboarding-states/{id}')
//   @response(204, {
//     description: 'OnboardingState PUT success',
//   })
//   async replaceById(
//     @param.path.number('id') id: number,
//     @requestBody() onboardingState: OnboardingState,
//   ): Promise<void> {
//     await this.onboardingStateRepository.replaceById(id, onboardingState);
//   }

//   @del('/onboarding-states/{id}')
//   @response(204, {
//     description: 'OnboardingState DELETE success',
//   })
//   async deleteById(@param.path.number('id') id: number): Promise<void> {
//     await this.onboardingStateRepository.deleteById(id);
//   }
}
