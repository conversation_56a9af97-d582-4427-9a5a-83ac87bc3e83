import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  api,
  requestBody,
} from '@loopback/rest';
import {
  UiActionRewardJunction,
  UiCustomerAction,
} from '../models';
import {UiActionRewardJunctionRepository} from '../repositories';
import {GuardSkipStrategy, guardStrategy, skipGuardCheck} from '../interceptors';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../services/basic.authorizor';

@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class UiActionRewardJunctionUiCustomerActionController {
  constructor(
    @repository(UiActionRewardJunctionRepository) protected uiActionRewardJunctionRepository: UiActionRewardJunctionRepository,
  ) { }
}
