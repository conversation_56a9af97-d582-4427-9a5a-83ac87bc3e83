// import {
//   Count,
//   CountSchema,
//   Filter,
//   repository,
//   Where,
// } from '@loopback/repository';
// import {
//   del,
//   get,
//   getModelSchemaRef,
//   getWhereSchemaFor,
//   param,
//   patch,
//   post,
//   requestBody,
// } from '@loopback/rest';
// import {guardStrategy, OrgIdMultiHopReadMutateByIdOnlyRelatedPropertyGuard, OrgIdMultiHopReadMutateByMultiHopIdOnlyRelatedPropertyGuard} from '../interceptors';
// import {
//   Quest,
//   Journey,
//   Campaign,
// } from '../models';
// import {JourneyRepository, QuestRepository} from '../repositories';

// @guardStrategy(new OrgIdMultiHopReadMutateByMultiHopIdOnlyRelatedPropertyGuard<Journey, Quest, Campaign, Quest, Campaign>({
// 	firstHopIdPropertyName: 'questId',
// 	firstHopRepositoryClass: QuestRepository,
// 	inclusionChainAfterFirstHop: { relation: "campaign" },
// 	inclusionChainForMutations: { relation: "campaign" },
// 	lastHopOrgIdPropertyName: 'orgId',
// 	mutationTargetOrgIdPropertyName: 'orgId',
// 	mutationBasisRepositoryClass: QuestRepository,
// 	repositoryClass: JourneyRepository
// }))
// export class QuestJourneyController {
//   constructor(
//     @repository(QuestRepository) protected questRepository: QuestRepository,
//   ) { }

//   @get('/quests/{id}/journeys', {
//     responses: {
//       '200': {
//         description: 'Array of Quest has many Journey',
//         content: {
//           'application/json': {
//             schema: {type: 'array', items: getModelSchemaRef(Journey)},
//           },
//         },
//       },
//     },
//   })
//   async find(
//     @param.path.number('id') id: number,
//     @param.query.object('filter') filter?: Filter<Journey>,
//   ): Promise<Journey[]> {
//     return this.questRepository.journeys(id).find(filter);
//   }

//   @post('/quests/{id}/journeys', {
//     responses: {
//       '200': {
//         description: 'Quest model instance',
//         content: {'application/json': {schema: getModelSchemaRef(Journey)}},
//       },
//     },
//   })
//   async create(
//     @param.path.number('id') id: typeof Quest.prototype.id,
//     @requestBody({
//       content: {
//         'application/json': {
//           schema: getModelSchemaRef(Journey, {
//             title: 'NewJourneyInQuest',
//             exclude: ['id'],
//             optional: ['questId']
//           }),
//         },
//       },
//     }) journey: Omit<Journey, 'id'>,
//   ): Promise<Journey> {
//     return this.questRepository.journeys(id).create(journey);
//   }

//   @patch('/quests/{id}/journeys', {
//     responses: {
//       '200': {
//         description: 'Quest.Journey PATCH success count',
//         content: {'application/json': {schema: CountSchema}},
//       },
//     },
//   })
//   async patch(
//     @param.path.number('id') id: number,
//     @requestBody({
//       content: {
//         'application/json': {
//           schema: getModelSchemaRef(Journey, {partial: true}),
//         },
//       },
//     })
//     journey: Partial<Journey>,
//     @param.query.object('where', getWhereSchemaFor(Journey)) where?: Where<Journey>,
//   ): Promise<Count> {
//     return this.questRepository.journeys(id).patch(journey, where);
//   }

//   @del('/quests/{id}/journeys', {
//     responses: {
//       '200': {
//         description: 'Quest.Journey DELETE success count',
//         content: {'application/json': {schema: CountSchema}},
//       },
//     },
//   })
//   async delete(
//     @param.path.number('id') id: number,
//     @param.query.object('where', getWhereSchemaFor(Journey)) where?: Where<Journey>,
//   ): Promise<Count> {
//     return this.questRepository.journeys(id).delete(where);
//   }
// }
