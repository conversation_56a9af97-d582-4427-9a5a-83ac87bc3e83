// Uncomment these imports to begin using these cool features!
import {inject} from '@loopback/core';
import {api, get} from '@loopback/rest';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../services';

// Add any required import for heap dumping
import {GuardSkipStrategy, skipGuardCheck, guardStrategy} from '../interceptors';

@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class ProfilingController {
  constructor() {}

  @get('/dump-heap', {
    responses: {
      '200': {
        description: 'Heap dump triggered successfully',
      },
    },
  })

  @authenticate('jwt')
  @skipGuardCheck()
  @authorize({
	  allowedRoles: ['raleon-admin'],
	  voters: [basicAuthorization],
  })

  async dumpHeap(): Promise<string> {
	//Return heap stats
	console.log(require('v8').getHeapStatistics().heap_size_limit);
	console.log(require('v8').getHeapStatistics().heap_size_limit / 1024 + ' KB');
	console.log(require('v8').getHeapStatistics().heap_size_limit / 1024 / 1024 + ' MB');
	return `Heap stats: ${require('v8').getHeapStatistics().heap_size_limit / 1024 / 1024} MB`;

    // Generate heap dump file with timestamp
    //const fileName = `heapdump-${Date.now()}.heapsnapshot`;
    // return new Promise((resolve, reject) => {
    //   heapdump.writeSnapshot(fileName, (err: any, filename: any) => {
    //     if (err) {
    //       reject(`Failed to create heap dump: ${err}`);
    //     } else {
    //       resolve(`Heap dump created: ${filename}`);
    //     }
    //   });
    // });
  }
}
