import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
  api
} from '@loopback/rest';
import {
  LoyaltyEarn,
  EarnCondition,
  LoyaltyCampaign,
  LoyaltyProgram,
} from '../../models';
import {LoyaltyCampaignRepository, LoyaltyEarnRepository} from '../../repositories';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../../services';
import {modelIdForGuard, OrgGuardMultiHopPropertyStrategy, skipGuardCheck} from '../../interceptors/crud-guard.interceptor';
import {guardStrategy, GuardSkipStrategy} from '../../interceptors/crud-guard.interceptor';

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardMultiHopPropertyStrategy<LoyaltyEarn, LoyaltyCampaign, LoyaltyProgram>({
	repositoryClass: LoyaltyEarnRepository,
	firstHopIdPropertyName: 'loyaltyCampaignId',
	firstHopRepositoryClass: LoyaltyCampaignRepository,
	inclusionChainAfterFirstHop: {relation: "loyaltyProgram"},
	lastHopOrgIdPropertyName: 'orgId',
}))
export class LoyaltyEarnEarnConditionController {
  constructor(
    @repository(LoyaltyEarnRepository) protected loyaltyEarnRepository: LoyaltyEarnRepository,
  ) { }

  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
  })
  @get('/loyalty-earns/{id}/earn-conditions', {
    responses: {
      '200': {
        description: 'Array of LoyaltyEarn has many EarnCondition',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(EarnCondition)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id')
	@modelIdForGuard(LoyaltyEarn)
	id: number,
    @param.query.object('filter') filter?: Filter<EarnCondition>,
  ): Promise<EarnCondition[]> {
    return this.loyaltyEarnRepository.earnConditions(id).find(filter);
  }

  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
  })
  @post('/loyalty-earns/{id}/earn-conditions', {
    responses: {
      '200': {
        description: 'LoyaltyEarn model instance',
        content: {'application/json': {schema: getModelSchemaRef(EarnCondition)}},
      },
    },
  })
  async create(
    @param.path.number('id')
	@modelIdForGuard(LoyaltyEarn)
	id: typeof LoyaltyEarn.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(EarnCondition, {
            title: 'NewEarnConditionInLoyaltyEarn',
            exclude: ['id'],
            optional: ['loyaltyEarnId']
          }),
        },
      },
    }) earnCondition: Omit<EarnCondition, 'id'>,
  ): Promise<EarnCondition> {
    return this.loyaltyEarnRepository.earnConditions(id).create(earnCondition);
  }

  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
  })
  @patch('/loyalty-earns/{id}/earn-conditions', {
    responses: {
      '200': {
        description: 'LoyaltyEarn.EarnCondition PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id')
	@modelIdForGuard(LoyaltyEarn)
	id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(EarnCondition, {partial: true}),
        },
      },
    })
    earnCondition: Partial<EarnCondition>,
    @param.query.object('where', getWhereSchemaFor(EarnCondition)) where?: Where<EarnCondition>,
  ): Promise<Count> {
    return this.loyaltyEarnRepository.earnConditions(id).patch(earnCondition, where);
  }

  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
  })
  @del('/loyalty-earns/{id}/earn-conditions', {
    responses: {
      '200': {
        description: 'LoyaltyEarn.EarnCondition DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id')
	@modelIdForGuard(LoyaltyEarn)
	id: number,
    @param.query.object('where', getWhereSchemaFor(EarnCondition)) where?: Where<EarnCondition>,
  ): Promise<Count> {
    return this.loyaltyEarnRepository.earnConditions(id).delete(where);
  }
}
