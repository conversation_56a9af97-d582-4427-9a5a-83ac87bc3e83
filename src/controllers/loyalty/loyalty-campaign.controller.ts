import {
	Count,
	CountSchema,
	Filter,
	FilterExcludingWhere,
	repository,
	Where,
} from '@loopback/repository';
import {
	post,
	param,
	get,
	getModelSchemaRef,
	patch,
	put,
	del,
	requestBody,
	response,
	api,
	HttpErrors
} from '@loopback/rest';
import {EarnCondition, EarnEffect, EarnEffectWithRelations, LoyaltyCampaign, LoyaltyCampaignWithRelations, LoyaltyEarn, LoyaltyEarnWithRelations, LoyaltyProgram, LoyaltyRewardDefinition, Reward, RewardCoupon} from '../../models';
import {CurrencyRepository, EarnConditionRepository, EarnEffectRepository, LoyaltyCampaignRepository, LoyaltyCurrencyRepository, LoyaltyEarnRepository, LoyaltyProgramRepository, LoyaltyRewardDefinitionRepository, OnboardingStateRepository, OnboardingTaskRepository, OrganizationRepository, RewardCouponRepository, UiCustomerActionRepository} from '../../repositories';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization, OnboardingTaskStateService} from '../../services';
import {injectGuardedFilter, injectUserId, injectUserOrgId, modelIdForGuard, restrictReadsWithGuard, skipGuardCheck} from '../../interceptors/crud-guard.interceptor';
import {guardStrategy, GuardSkipStrategy, OrgGuardSingleHopPropertyStrategy} from '../../interceptors/crud-guard.interceptor';
import {inject, service} from '@loopback/core';
import {SecurityBindings} from '@loopback/security';
import {User} from '@loopback/authentication-jwt';
import {getURL} from '../../utils';
import {LoyaltyDetailsController} from './loyalty-details.controller';
import {uuid} from 'uuidv4';
import {convertCurrencyPlaceholdersToValues, convertCurrencyValuesToPlaceholders} from '../admin-ui.controller';
const fetch = require('node-fetch');
const aws4 = require('aws4')

interface ReferralProgramBonus {
	type: 'dollars-off-coupon'|'percent-discount'|'points';
	name: string;
	description?: string;
	amount: number;
	imageURL?: string;
	minimumOrderTotal: number;
}

//NOTE -> A lot of these API's i've commented out because we don't need them yet, feel free to re-enable and test authentication

const ONBOARD_URL = process.env.ONBOARDING_AWS_URL || 'ojpurdnz1b.execute-api.us-east-1.amazonaws.com';
const AI_CAMPAIGNS_S3 = process.env.AI_CAMPAIGNS_S3 || 'shopifyonboardstack-dev-onboardaicampaignsdev0954e-396mzp5fjulj';
const AWS_ACCESS_KEY = process.env.API_ACCESS_KEY;
const AWS_SECRET_KEY = process.env.API_SECRET_KEY;

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardSingleHopPropertyStrategy<LoyaltyCampaign, LoyaltyProgram>({
	relatedIdPropertyName: 'loyaltyProgramId',
	relatedOrgIdPropertyName: 'orgId',
	relatedRepositoryClass: LoyaltyProgramRepository,
	repositoryClass: LoyaltyCampaignRepository
}))
export class LoyaltyCampaignController{
	constructor(
		@repository(LoyaltyCampaignRepository)
		public loyaltyCampaignRepository: LoyaltyCampaignRepository,
		@repository(LoyaltyProgramRepository)
		public loyaltyProgramRepository: LoyaltyProgramRepository,
		@repository(OnboardingStateRepository)
		private onboardingStateRepository: OnboardingStateRepository,
		@repository(OnboardingTaskRepository)
		private onboardingTaskRepository: OnboardingTaskRepository,
		@repository(UiCustomerActionRepository)
		private uiCustomerActionRepository: UiCustomerActionRepository,
		@repository(CurrencyRepository)
		private currencyRepository: CurrencyRepository,
		@service(OnboardingTaskStateService)
		public onboardingTaskStateService: OnboardingTaskStateService,
		@repository(OrganizationRepository)
		public organizationRepository: OrganizationRepository,
		@repository(LoyaltyEarnRepository)
		private loyaltyEarnRepository: LoyaltyEarnRepository,
		@repository(EarnConditionRepository)
		private earnConditionRepository: EarnConditionRepository,
		@repository(EarnEffectRepository)
		private earnEffectRepository: EarnEffectRepository,
		@repository(LoyaltyRewardDefinitionRepository)
		private loyaltyRewardDefinitionRepository: LoyaltyRewardDefinitionRepository,
		@repository(RewardCouponRepository)
		private rewardCouponRepository: RewardCouponRepository,
		@repository(LoyaltyCurrencyRepository)
		private loyaltyCurrencyRepository: LoyaltyCurrencyRepository,
	) { }

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/loyalty-campaigns/{id}')
	@response(200, {
		description: 'LoyaltyCampaign model instance',
		content: {
			'application/json': {
				schema: getModelSchemaRef(LoyaltyCampaign, {includeRelations: true}),
			},
		},
	})
	async findById(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyCampaign)
		id: number
	): Promise<LoyaltyCampaign> {
		return this.loyaltyCampaignRepository.findById(id,
			{
				include: ['loyaltyEarns', 'loyaltyRedemptionShopItems', 'staticEffects', 'vipTier', {
					relation: 'loyaltyProgram',
					scope: {
						include: ['loyaltyCurrencies']
					}
				}],
			});
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/loyalty-campaigns/foundational')
	@response(200, {
		description: 'LoyaltyCampaign model instance',
		content: {
			'application/json': {
				schema: getModelSchemaRef(LoyaltyCampaign, {includeRelations: true}),
			},
		},
	})
	async findFoundationalCampaign(
		@injectUserOrgId() orgId: number
	): Promise<LoyaltyCampaign | null> {
		let loyaltyPrograms = await this.loyaltyProgramRepository.find({
			where: {
				orgId: orgId
			}
		});
		const loyaltyProgramIds = loyaltyPrograms.map(lp => lp.id).filter(id => id !== undefined);

		if (loyaltyProgramIds.length === 0) {
			return null;
		}

		let campaign = this.loyaltyCampaignRepository.findOne({
			where: {
				evergreen: true,
				loyaltySegment: 'Everyone',
				loyaltyProgramId: { inq: loyaltyProgramIds.filter(id => id !== undefined) as number[] }
			}
		});

		if (campaign) {
			campaign.then(c => {
				if (c && !c.active) {
					this.loyaltyCampaignRepository.updateById(c.id, { active: true });
				}
			});
		}

		return campaign;
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@patch('/loyalty-campaigns/{id}')
	@response(204, {
		description: 'LoyaltyCampaign PATCH success',
	})
	async updateById(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyCampaign)
		id: number,
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(LoyaltyCampaign, {partial: true}),
				},
			},
		})
		loyaltyCampaign: LoyaltyCampaign,
	): Promise<void> {
		await this.loyaltyCampaignRepository.updateById(id, loyaltyCampaign);

		this.loyaltyCampaignRepository.findById(id, { include: ['loyaltyProgram'] }).then((campaign: LoyaltyCampaignWithRelations) =>
			this.onboardingTaskStateService.updateState({ state: 'Verified' }, 'Loyalty Program', campaign.loyaltyProgram.orgId).catch()
		);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@del('/loyalty-campaigns/{id}')
	@response(204, {
		description: 'LoyaltyCampaign DELETE success',
	})
	async deleteById(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyCampaign)
		id: number
	): Promise<any> {
		const response = await this.loyaltyCampaignRepository.cascadeDelete(id);
		return response.success ?
			{ status: 'success', message: response.message } :
			{ status: 'error', error: response };
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@put('/loyalty-campaigns/{id}')
	@response(204, {
		description: 'LoyaltyCampaign PUT success',
	})
	async replaceById(
		@modelIdForGuard(LoyaltyCampaign)
		@param.path.number('id') id: number,
		@requestBody() loyaltyCampaign: LoyaltyCampaign,
	): Promise<void> {
		await this.loyaltyCampaignRepository.replaceById(id, loyaltyCampaign);
	}

	/*@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/loyalty-campaigns')
	@response(200, {
		description: 'LoyaltyCampaign model instance',
		content: {'application/json': {schema: getModelSchemaRef(LoyaltyCampaign)}},
	})
	async create(
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(LoyaltyCampaign, {
						title: 'NewLoyaltyCampaign',
						exclude: ['id'],
					}),
				},
			},
		})
		loyaltyCampaign: Omit<LoyaltyCampaign, 'id'>,
	): Promise<LoyaltyCampaign> {
		return this.loyaltyCampaignRepository.create(loyaltyCampaign);
	}*/

	/*@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/loyalty-campaigns/count')
	@response(200, {
		description: 'LoyaltyCampaign model count',
		content: {'application/json': {schema: CountSchema}},
	})
	async count(
		@param.where(LoyaltyCampaign) where?: Where<LoyaltyCampaign>,
	): Promise<Count> {
		return this.loyaltyCampaignRepository.count(where);
	}*/

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@restrictReadsWithGuard({
		plural: true,
		filterOnly: true
	})
	@get('/loyalty-campaigns')
	@response(200, {
		description: 'Array of LoyaltyCampaign model instances',
		content: {
			'application/json': {
				schema: {
					type: 'array',
					items: getModelSchemaRef(LoyaltyCampaign, {includeRelations: true}),
				},
			},
		},
	})
	async find(
		@injectGuardedFilter()
		@param.filter(LoyaltyCampaign)
		filter?: Filter<LoyaltyCampaign>,
	): Promise<LoyaltyCampaign[]> {
		return this.loyaltyCampaignRepository.find(filter);
	}

	@authenticate('user-access-token')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@restrictReadsWithGuard({
		plural: true,
		filterOnly: true
	})
	@get('/loyalty-campaigns-admin')
	@response(200, {
		description: 'Array of LoyaltyCampaign model instances',
		content: {
			'application/json': {
				schema: {
					type: 'array',
					items: getModelSchemaRef(LoyaltyCampaign, {includeRelations: true}),
				},
			},
		},
	})
	async findAgain(
		@injectGuardedFilter()
		@param.filter(LoyaltyCampaign)
		filter?: Filter<LoyaltyCampaign>,
	): Promise<LoyaltyCampaign[]> {
		return this.loyaltyCampaignRepository.find(filter);
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer', 'raleon-admin'],
		voters: [basicAuthorization],
	})
	@get('/active-loyalty-campaigns')
	async getActiveLoyaltyCampaigns(
		@param.query.number('orgId') orgId: number
	) {
		const program = await this.loyaltyProgramRepository.findOne({
			where: { orgId, active: true }
		});

		if (!program) {
			return [];
		}

		let campaigns = await this.loyaltyCampaignRepository.find({
			where: {
				loyaltyProgramId: program.id,
				active: true
			},
			include: [
				{
					relation: 'loyaltyEarns',
					scope: {
						include: [
							{
								relation: 'earnEffects',
								scope: {
									include: [
										{
											relation: 'loyaltyRewardDefinition',
											scope: {
												include: ['rewardCoupon'],
											},
										}
									]
								}
							},
							{
								relation: 'earnConditions'
							}
						]
					}
				}
			]
		});

		campaigns = campaigns.filter(c => {
			if (!c.startdate && (!c.enddate || new Date(c.enddate!) > new Date())) {
				return true;
			}

			const now = new Date();
			const start = new Date(c.startdate!);
			const end = new Date(c.enddate!);
			return start <= now && now <= end;
		})

		return campaigns;
	}

	/*@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@patch('/loyalty-campaigns')
	@response(200, {
		description: 'LoyaltyCampaign PATCH success count',
		content: {'application/json': {schema: CountSchema}},
	})
	async updateAll(
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(LoyaltyCampaign, {partial: true}),
				},
			},
		})
		loyaltyCampaign: LoyaltyCampaign,
		@param.where(LoyaltyCampaign) where?: Where<LoyaltyCampaign>,
	): Promise<Count> {
		return this.loyaltyCampaignRepository.updateAll(loyaltyCampaign, where);
	}*/


	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/loyalty-campaigns/{id}/ai-generate')
	@response(200, {
		description: 'LoyaltyCampaign AI POST success',
	})
	async aiGenerate(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyCampaign)
		id: number,
		@requestBody({
			content: {
				'application/json': {
					schema: { type: 'object' },
				},
			},
		})
		params: { userPrompt: string },
		@injectUserOrgId()
		orgId: number
	): Promise<void> {
		const s3ResultKey = uuid();
		const org = await this.organizationRepository.findById(orgId);
		const orgUrl = org.externalDomain?.startsWith('http') ? org.externalDomain : `https://${org.externalDomain}`;
		const url = `/prod/onboard`;
		const signedRequest = getURL(url, 'POST', {
			...params,
			organization_id: orgId,
			url: orgUrl,

			programUserPrompt: params.userPrompt,

			campaignId: id,
			ignoreExistingProgram: 'true',
			skipPersist: 'true',
			s3Bucket: AI_CAMPAIGNS_S3,
			s3ResultKey,
			allowGptTemplateSuggestion: 'true',
			includeCampaignSummary: 'true',


			useGptVision: 'false',
			skipBranding: 'true',
			skipInsights: 'true',

		}, ONBOARD_URL);



		const response = await fetch(`https://${ONBOARD_URL}${url}`, signedRequest);
		if (!response.ok) {
			throw response.message;
			// throw new HttpErrors.BadGateway('AI service failed');
		}

		// const result = await response.json();
		// if (result.error) {
		// 	throw new HttpErrors.InternalServerError(result.error);
		// }


		const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

		// if (result.message && result.message.includes('timed out')) {
			// poll s3 for result
			const s3Url = `https://${AI_CAMPAIGNS_S3}.s3.amazonaws.com/${s3ResultKey}`;
			let s3Response = null;
			let attempts = 0;

			while (attempts < 100) {
				const response = await fetch(s3Url);
				if (response.ok) {
					s3Response = await response.json();
					if (s3Response) {
						break;
					}
				}
				await delay(3000); // Wait for 3 seconds
				attempts++;
			}

			if (!s3Response) {
				throw new Error('Failed to get response from S3');
			}

			return s3Response
		// }

		// return result;
	}


	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/loyalty-campaigns/{id}/ai-persist')
	@response(200, {
		description: 'LoyaltyCampaign AI POST success',
	})
	async aiPersist(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyCampaign)
		id: number,
		@requestBody({
			content: {
				'application/json': {
					schema: { type: 'object' },
				},
			},
		})
		params: { wteJson: any, rewardJson: any },
		@injectUserOrgId()
		orgId: number
	): Promise<void> {
		const url = `/prod/campaign-persist`;
		const signedRequest = getURL(url, 'POST', {
			...params,
			organization_id: orgId,
			loyaltyCampaignId: id
		}, ONBOARD_URL);



		const response = await fetch(`https://${ONBOARD_URL}${url}`, signedRequest);

		return response.json();
	}



	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/referral-program')
	@response(200, {
		description: 'Referral Program GET success',
	})
	@skipGuardCheck()
	async getReferralProgram(
		@injectUserOrgId()
		orgId: number
	): Promise<{
		referrerBonus: ReferralProgramBonus|null,
		referredCustomerBonus: ReferralProgramBonus|null,
		active: boolean,
		currencyPrefix: string
	 }> {
		const referralProgram = await this.loyaltyProgramRepository.findOne({
			where: {
				orgId: orgId,
			}
		});

		const foundationalCampaign = await this.loyaltyCampaignRepository.findOne({
			where: {
				evergreen: true,
				loyaltySegment: 'Everyone',
				loyaltyProgramId: referralProgram?.id
			},
			include: [{
				relation: 'loyaltyEarns',
				scope: {
					include: [{
						relation: 'earnEffects',
						scope: {
							include: [{
								relation: 'loyaltyRewardDefinition',
								scope: {
									include: ['rewardCoupon'],
								},
							}]
						}
					}, {
						relation: 'earnConditions'
					}]
				}

			}]
		});

		const kickbackBonuses: Array<LoyaltyEarnWithRelations> = foundationalCampaign!.loyaltyEarns.filter(x => x.earnConditions?.some?.((y: any) => y.type === 'referrer-bonus'));
		const referredCustomerBonuses: Array<LoyaltyEarnWithRelations> = foundationalCampaign!.loyaltyEarns.filter(x => x.earnConditions?.some?.((y: any) => y.type === 'referred-customer-bonus'));


		const orgCurrency = await this.currencyRepository.findOne({
			where: { organizationId: orgId },
			include: [ { relation: 'supportedCurrencies' } ]
		});

		if (!referralProgram || !foundationalCampaign) {
			return {
				referrerBonus: null,
				referredCustomerBonus: null,
				active: false,
				currencyPrefix: (orgCurrency as any)?.supportedCurrencies?.prefix || '$'
			};
		}

		if (kickbackBonuses.length > 1 || referredCustomerBonuses.length > 1) {
			throw new HttpErrors.BadRequest('Misconfigured campaign. More than one referral bonus or referred customer bonus found.');
		}

		if(kickbackBonuses.length === 0 || referredCustomerBonuses.length === 0) {
			return {
				referrerBonus: {
					type: 'dollars-off-coupon',
					name: 'Referral Bonus',
					description: 'Get $10 off every time friends that you refer make their first purchase',
					amount: 10,
					imageURL: 'https://raleoncdn.s3.us-east-1.amazonaws.com/WTERewardDefault.jpg',
					minimumOrderTotal: 10,
				},
				referredCustomerBonus: {
					type: 'dollars-off-coupon',
					name: 'Referred Customer Bonus',
					description: 'Get $10 off from using a friend\'s referral link.',
					amount: 10,
					imageURL: 'https://raleoncdn.s3.us-east-1.amazonaws.com/WTERewardDefault.jpg',
					minimumOrderTotal: 10,
				},
				active: referralProgram.referralsActive || false,
				currencyPrefix: (orgCurrency as any)?.supportedCurrencies?.prefix || '$'
			};
		}

		const getBonusDetails = (bonus: LoyaltyEarnWithRelations) => {
			const effect = bonus.earnEffects[0] as EarnEffectWithRelations;
			const reward = effect.loyaltyRewardDefinition;
			if (reward?.rewardCoupon) {
				return {
					type: reward.rewardCoupon.amountType as any,
					name: convertCurrencyPlaceholdersToValues(bonus.name, orgCurrency!),
					description: convertCurrencyPlaceholdersToValues(bonus.description, orgCurrency!),
					amount: reward.rewardCoupon.amount,
					imageURL: bonus.imageURL,
					minimumOrderTotal: reward.rewardCoupon.minimumOrderTotal || 0,
				};
			} else {
				return {
					type: 'points',
					name: convertCurrencyPlaceholdersToValues(bonus.name, orgCurrency!),
					description: convertCurrencyPlaceholdersToValues(bonus.description, orgCurrency!),
					amount: effect.points,
					imageURL: bonus.imageURL,
					minimumOrderTotal: 0,
				};
			}
		};

		return {
			referrerBonus: getBonusDetails(kickbackBonuses[0]) as ReferralProgramBonus,
			referredCustomerBonus: getBonusDetails(referredCustomerBonuses[0]) as ReferralProgramBonus,
			active: referralProgram.referralsActive || false,
			currencyPrefix: (orgCurrency as any)?.supportedCurrencies?.prefix || '$'
		};
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/referral-program/preview')
	@response(200, {
		description: 'Referral Program GET success',
	})
	@skipGuardCheck()
	async getReferralProgramForPreview(
		@injectUserOrgId()
		orgId: number
	): Promise<any> {
		const referralProgram = await this.loyaltyProgramRepository.findOne({
			where: {
				orgId: orgId,
			}
		});

		const foundationalCampaign = await this.loyaltyCampaignRepository.findOne({
			where: {
				evergreen: true,
				loyaltySegment: 'Everyone',
				loyaltyProgramId: referralProgram?.id
			},
			include: [{
				relation: 'loyaltyEarns',
				scope: {
					include: [{
						relation: 'earnEffects',
						scope: {
							include: [{
								relation: 'loyaltyRewardDefinition',
								scope: {
									include: ['rewardCoupon'],
								},
							}]
						}
					}, {
						relation: 'earnConditions'
					}]
				}
			}]
		});

		if (!foundationalCampaign) {
			throw new Error('no foundational campaign defined');
		}

		const orgLanguage = (await this.organizationRepository.findById(orgId))?.language || 'en';
		foundationalCampaign.loyaltyEarns = foundationalCampaign!.loyaltyEarns.filter(x => x.earnConditions?.some?.((y: any) => y?.type === 'referrer-bonus'));
		let orgCurrency = await this.currencyRepository.findOne({
			where: { organizationId: orgId },
			include: [ { relation: 'supportedCurrencies' } ]
		});

		if (!foundationalCampaign.loyaltyEarns?.length) {
			const data: any = {
				loyaltyEarns: [{
					name: 'Referral Bonus',
					type: 'dollars-off-coupon',
					description: 'Get $10 off every time friends that you refer make their first purchase.',
					imageURL: 'https://raleoncdn.s3.us-east-1.amazonaws.com/WTERewardDefault.jpg',
					earnConditions: [{type: 'referrer-bonus'}],
					earnEffects: [{
						name: 'Referred Customer Bonus',
						amount: 10,
						description: 'Get $10 off from using a friend\'s referral link.',
						imageURL: 'https://raleoncdn.s3.us-east-1.amazonaws.com/WTERewardDefault.jpg',
						type: 'dollars-off-coupon',
					}]
				}]
			};

			data.loyaltyEarns[0].instructions = LoyaltyDetailsController.generateConditionStrings(
				data,
				LoyaltyDetailsController.groupEarnConditions(data.loyaltyEarns[0].earnConditions),
				orgId,
				orgLanguage,
				orgCurrency!,
			);

			return data;
		}

		const currency = await this.loyaltyCurrencyRepository.findOne({
			where: { loyaltyProgramId: referralProgram?.id }
		});
		const pointsName = currency?.name || 'points';

		for (let i = 0; i < foundationalCampaign.loyaltyEarns.length; i++) {
			const [rewardName, rewardDescription] = LoyaltyDetailsController.generateReferralRewardText(
				foundationalCampaign.loyaltyEarns[i].earnEffects[0],
				orgId,
				orgLanguage,
				pointsName,
			);
			foundationalCampaign.loyaltyEarns[i].earnEffects[0].name = rewardName;
			foundationalCampaign.loyaltyEarns[i].earnEffects[0].description = rewardDescription;
		}

		const earnConditionGroups = LoyaltyDetailsController.groupEarnConditions(foundationalCampaign.loyaltyEarns[0].earnConditions);
		const conditionStrings = LoyaltyDetailsController.generateConditionStrings(
			foundationalCampaign,
			earnConditionGroups,
			orgId,
			orgLanguage,
			orgCurrency!,
		);
		if (foundationalCampaign?.loyaltyEarns?.[0]) {
			(foundationalCampaign.loyaltyEarns[0] as any).instructions = conditionStrings;
		}
		return foundationalCampaign;
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/referral-program/preview')
	@response(200, {
		description: 'Referral Program GET success',
	})
	@skipGuardCheck()
	async transformReferralProgramForPreview(
		@requestBody({
			content: {
				'application/json': {
					schema: {}
				},
			},
		})
		body: any,
		@injectUserOrgId()
		orgId: number
	): Promise<any> {
		const orgLanguage = (await this.organizationRepository.findById(orgId))?.language || 'en';
		let orgCurrency = await this.currencyRepository.findOne({
			where: { organizationId: orgId },
			include: [ { relation: 'supportedCurrencies' } ]
		});
		const foundationalCampaign: any = {};

		foundationalCampaign.loyaltyEarns = [{
			name: body.referrerBonus.name,
			type: body.referrerBonus.type,
			description: body.referrerBonus.description,
			amount: body.referrerBonus.amount,
			imageURL: body.referrerBonus.imageURL,
			earnConditions: [{type: 'referrer-bonus'}],
			earnEffects: [{
				name: body.referrerBonus.name,
				description: body.referrerBonus.description,
				imageURL: body.referrerBonus.imageURL,
				type: body.referrerBonus.type,
				loyaltyRewardDefinition: {
					rewardCoupon: {
						amount: body.referrerBonus.amount,
						amountType: body.referrerBonus.type,
					}
				}
			}]
		}];

		for (let i = 0; i < foundationalCampaign.loyaltyEarns.length; i++) {
			const [rewardName, rewardDescription] = LoyaltyDetailsController.generateReferralRewardText(
				foundationalCampaign.loyaltyEarns[i].earnEffects[0],
				orgId,
				orgLanguage
			);
			foundationalCampaign.loyaltyEarns[i].earnEffects[0].name = rewardName;
			foundationalCampaign.loyaltyEarns[i].earnEffects[0].description = rewardDescription;
		}

		const earnConditionGroups = LoyaltyDetailsController.groupEarnConditions(foundationalCampaign.loyaltyEarns[0].earnConditions);
		const conditionStrings = LoyaltyDetailsController.generateConditionStrings(
			foundationalCampaign,
			earnConditionGroups,
			orgId,
			orgLanguage,
			orgCurrency!
		);
		if (foundationalCampaign?.loyaltyEarns?.[0]) {
			(foundationalCampaign.loyaltyEarns[0] as any).instructions = conditionStrings;
		}
		return foundationalCampaign;
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@put('/referral-program')
	@response(200, {
		description: 'Referral Program PUT success',
	})
	@skipGuardCheck()
	async updateReferralProgram(
		@requestBody({
			content: {
				'application/json': {
					schema: {}
				},
			},
		})
		payload: {
			referrerBonus: ReferralProgramBonus,
			referredCustomerBonus: ReferralProgramBonus,
			active: boolean
		},
		@injectUserOrgId()
		orgId: number
	): Promise<void> {
		const referralProgram = await this.loyaltyProgramRepository.findOne({
			where: {
				orgId: orgId,
			}
		});

		const foundationalCampaign = await this.loyaltyCampaignRepository.findOne({
			where: {
				evergreen: true,
				loyaltySegment: 'Everyone',
				loyaltyProgramId: referralProgram?.id
			},
			include: [{
				relation: 'loyaltyEarns',
				scope: {
					include: [{
						relation: 'earnEffects',
						scope: {
							include: [{
								relation: 'loyaltyRewardDefinition',
								scope: {
									include: ['rewardCoupon'],
								},
							}]
						}
					}, {
						relation: 'earnConditions'
					}]
				}

			}]
		});

		if (!referralProgram || !foundationalCampaign) {
			throw new HttpErrors.NotFound('Referral program or foundational campaign not found');
		}

		const existingBonuses: Array<LoyaltyEarn> = foundationalCampaign!.loyaltyEarns.filter(x => x.earnConditions?.some?.((y: any) => y.type === 'referrer-bonus' || y.type === 'referred-customer-bonus'));
		const earnIds = existingBonuses?.map(x => x.id);
		const conditionIds = existingBonuses?.map(x => x.earnConditions?.map((y: any) => y.id))?.flat();
		const effectIds = existingBonuses?.map(x => x.earnEffects?.map((y: any) => y.id))?.flat();

		const referrerRewardIds = existingBonuses?.filter(x => x.earnConditions.some((y: any) => y.type === 'referrer-bonus')).map(x => x.earnEffects?.map((y: any) => y.loyaltyRewardDefinitionId)).flat()?.filter(x => x);
		const greatestReferrerRewardId = referrerRewardIds.length ? Math.max(...referrerRewardIds) : 0;
		const allButGreatestReferrerRewardIds = referrerRewardIds?.filter(x => x !== greatestReferrerRewardId);

		const referrerCouponIds = existingBonuses?.filter(x => x.earnConditions.some((y: any) => y.type === 'referrer-bonus'))?.map(x => x.earnEffects?.map((y: any) => y.loyaltyRewardDefinition?.rewardCouponId)).flat()?.filter(x => x);
		const greatestReferrerCouponId = referrerCouponIds.length ? Math.max(...referrerCouponIds) : 0;
		const allButGreatestReferrerCouponIds = referrerCouponIds?.filter(x => x !== greatestReferrerCouponId);

		const referredCustomerRewardIds = existingBonuses?.filter(x => x.earnConditions.some((y: any) => y.type === 'referred-customer-bonus'))?.map(x => x.earnEffects?.map((y: any) => y.loyaltyRewardDefinitionId)).flat()?.filter(x => x);
		const greatestReferredCustomerRewardId =  referredCustomerRewardIds.length ? Math.max(...referredCustomerRewardIds) : 0;
		const allButGreatestReferredCustomerRewardIds = referredCustomerRewardIds?.filter(x => x !== greatestReferredCustomerRewardId);

		const referredCustomerCouponIds = existingBonuses.filter(x => x.earnConditions.some((y: any) => y.type === 'referred-customer-bonus'))?.map(x => x.earnEffects?.map((y: any) => y.loyaltyRewardDefinition?.rewardCouponId)).flat().filter(x => x);
		const greatestReferredCustomerCouponId = referredCustomerCouponIds.length ? Math.max(...referredCustomerCouponIds) : 0;
		const allButGreatestReferredCustomerCouponIds = referredCustomerCouponIds.filter(x => x !== greatestReferredCustomerCouponId);

		try {
			await this.earnEffectRepository.deleteAll({ id: { inq: effectIds } });
			await this.earnConditionRepository.deleteAll({ id: { inq: conditionIds } });
			await this.loyaltyEarnRepository.deleteAll({ id: { inq: earnIds } });

			if (allButGreatestReferrerRewardIds.length > 0 || allButGreatestReferredCustomerRewardIds.length > 0)
				await this.loyaltyRewardDefinitionRepository.deleteAll({ id: { inq: [...allButGreatestReferrerRewardIds, ...allButGreatestReferredCustomerRewardIds] } });

			if (allButGreatestReferrerCouponIds.length > 0 || allButGreatestReferredCustomerCouponIds.length > 0)
				await this.rewardCouponRepository.deleteAll({ id: { inq: [...allButGreatestReferrerCouponIds, ...allButGreatestReferredCustomerCouponIds] } });

		} catch (e) {
			console.error(e);
		}

		if (!payload.referrerBonus && !payload.referredCustomerBonus) {
			return;
		}

		const amountForName = `{{currency_value:${payload.referrerBonus.amount}}}`;

		const currency = await this.loyaltyCurrencyRepository.findOne({
			where: { loyaltyProgramId: referralProgram.id }
		});
		const pointsName = currency?.name || 'points';

		let defaultReferrerName = `Referral Bonus: ${payload.referrerBonus.type === 'dollars-off-coupon' ? `${amountForName} off` : `${payload.referrerBonus.amount}% off`}`;
		if (payload.referrerBonus.type === 'points') {
			defaultReferrerName = `Referral Bonus: ${payload.referrerBonus.amount} ${pointsName}`;
		}
		let defaultReferrerDescription = `Get ${payload.referrerBonus.type === 'dollars-off-coupon' ? `${amountForName} off` : `${payload.referrerBonus.amount}% off`} every time friends that you refer make their first purchase`;
		if (payload.referrerBonus.type === 'points') {
			defaultReferrerDescription = `Get ${payload.referrerBonus.amount} ${pointsName} every time friends that you refer make their first purchase`;
		}

		const customerBonusAmount = `{{currency_value:${payload.referredCustomerBonus.amount}}}`;
		let defaultReferredCustomerName = `Referred Bonus: ${payload.referredCustomerBonus.type === 'dollars-off-coupon' ? `${customerBonusAmount} off` : `${payload.referredCustomerBonus.amount}% off`}`;
		if (payload.referredCustomerBonus.type === 'points') {
			defaultReferredCustomerName = `Referred Bonus: ${payload.referredCustomerBonus.amount} ${pointsName}`;
		}
		let defaultReferredCustomerDescription = `Get ${payload.referredCustomerBonus.type === 'dollars-off-coupon' ? `${customerBonusAmount} off` : `${payload.referredCustomerBonus.amount}% off`} just by signing up with your friends referral link`;
		if (payload.referredCustomerBonus.type === 'points') {
			defaultReferredCustomerDescription = `Get ${payload.referredCustomerBonus.amount} ${pointsName} just by signing up with your friends referral link`;
		}


		const {referrerBonusName, referrerBonusDescription, referredCustomerBonusName, referredCustomerBonusDescription} = await convertCurrencyValuesToPlaceholders({
			referrerBonusName: payload.referrerBonus.name || defaultReferrerName,
			referrerBonusDescription: payload.referrerBonus.description || defaultReferrerDescription,
			referredCustomerBonusName: payload.referredCustomerBonus.name || defaultReferredCustomerName,
			referredCustomerBonusDescription: payload.referredCustomerBonus.description || defaultReferredCustomerDescription,
		});

		const earn = await this.loyaltyEarnRepository.create({
			name: referrerBonusName,
			description: referrerBonusDescription,
			loyaltyCampaignId: foundationalCampaign.id,
			active: true,
			imageURL: payload.referrerBonus?.imageURL || ''
		});
		await this.earnConditionRepository.create({
			type: "referrer-bonus",
			variable: "referrer",
			operator: "==",
			amount: 1,
			triggeredEvent: "referrer",
			loyaltyEarnId: earn.id
		});

		const couponData = {
			name: defaultReferrerName,
			amount: payload.referrerBonus.amount,
			amountType: payload.referrerBonus.type,
			expiresInDays: 30,
			minimumOrderTotal: payload.referrerBonus.minimumOrderTotal,
			imageURL: payload.referrerBonus?.imageURL || '',
		};
		let coupon;
		let reward;
		if (payload.referrerBonus.type !== 'points') {
			if (greatestReferrerCouponId) {
				await this.rewardCouponRepository.updateById(greatestReferrerCouponId, couponData);
				coupon = await this.rewardCouponRepository.findById(greatestReferrerCouponId);
			} else {
				coupon = await this.rewardCouponRepository.create(couponData);
			}

			const rewardData = {
				loyaltyCampaignId: foundationalCampaign.id,
				grantable: true,
				redeemable: false,
				rewardCouponId: coupon.id,
				daysToRedeem: 30,
				maxUserGrants: 10000000,
			};

			if (greatestReferrerRewardId) {
				await this.loyaltyRewardDefinitionRepository.updateById(greatestReferrerRewardId, rewardData);
				reward = await this.loyaltyRewardDefinitionRepository.findById(greatestReferrerRewardId);
			} else {
				reward = await this.loyaltyRewardDefinitionRepository.create(rewardData);
			}
		}

		const earnEffect: any = {
			name: referrerBonusName,
			description: referrerBonusDescription,
			loyaltyRewardDefinitionId: reward?.id || null,
			imageURL: payload.referrerBonus?.imageURL || '',
			loyaltyEarnId: earn.id,
			type: payload.referrerBonus.type,
			loyaltyCurrencyId: currency?.id,
		};

		if (payload.referrerBonus.type === 'points') {
			earnEffect.points = payload.referrerBonus.amount;
		}
		await this.earnEffectRepository.create(earnEffect);

		const earn2 = await this.loyaltyEarnRepository.create({
			name: referredCustomerBonusName,
			description: referredCustomerBonusDescription,
			loyaltyCampaignId: foundationalCampaign.id,
			active: true,
			imageURL: payload.referredCustomerBonus?.imageURL || '',
		});
		await this.earnConditionRepository.create({
			type: "referred-customer-bonus",
			variable: "referred",
			operator: "==",
			amount: 1,
			triggeredEvent: "referred",
			loyaltyEarnId: earn2.id
		});

		const friendCouponData = {
			name: defaultReferredCustomerName,
			amount: payload.referredCustomerBonus.amount,
			amountType: payload.referredCustomerBonus.type,
			expiresInDays: 30,
			minimumOrderTotal: payload.referredCustomerBonus.minimumOrderTotal,
			imageURL: payload.referredCustomerBonus?.imageURL || '',
		};
		let friendCouponModel;
		let friendRewardModel;
		if (payload.referredCustomerBonus.type !== 'points') {
			if (greatestReferredCustomerCouponId) {
				await this.rewardCouponRepository.updateById(greatestReferredCustomerCouponId, friendCouponData);
				friendCouponModel = await this.rewardCouponRepository.findById(greatestReferredCustomerCouponId);
			} else {
				friendCouponModel = await this.rewardCouponRepository.create(friendCouponData);
			}

			const friendRewardData = {
				loyaltyCampaignId: foundationalCampaign.id,
				grantable: true,
				redeemable: false,
				rewardCouponId: friendCouponModel.id,
				daysToRedeem: 30,
				maxUserGrants: 1,
			};

			if (greatestReferredCustomerRewardId) {
				await this.loyaltyRewardDefinitionRepository.updateById(greatestReferredCustomerRewardId, friendRewardData);
				friendRewardModel = await this.loyaltyRewardDefinitionRepository.findById(greatestReferredCustomerRewardId);
			} else {
				friendRewardModel = await this.loyaltyRewardDefinitionRepository.create(friendRewardData);
			}
		}

		const friendEffect: any = {
			name: referredCustomerBonusName,
			description: referredCustomerBonusDescription,
			loyaltyRewardDefinitionId: friendRewardModel?.id || null,
			imageURL: payload.referredCustomerBonus?.imageURL || '',
			loyaltyEarnId: earn2.id,
			type: payload.referredCustomerBonus.type,
			loyaltyCurrencyId: currency?.id,
		}

		if (payload.referredCustomerBonus.type === 'points') {
			friendEffect.points = payload.referredCustomerBonus.amount;
		}
		await this.earnEffectRepository.create(friendEffect);

		await this.loyaltyProgramRepository.updateById(referralProgram.id, { referralsActive: payload.active });
	}

	@authenticate('jwt')
    @authorize({
        allowedRoles: ['admin', 'support', 'customer'],
        voters: [basicAuthorization],
    })
    @get('/loyalty-campaigns/{id}/summary')
    @response(200, {
        description: 'Summary of Loyalty Campaign elements',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        summary: {
                            type: 'object',
                            properties: {
                                wtes: {
                                    type: 'array',
                                    items: { /* schema for wtes items */ },
                                },
                                rewards: {
                                    type: 'array',
                                    items: { /* schema for rewards items */ },
                                },
                            },
                        },
                    },
                },
            },
        },
    })
    async getSummary(
        @param.path.number('id')
		@modelIdForGuard(LoyaltyCampaign)
		id: number,
		@injectUserOrgId()
		orgId: number
    ): Promise<{ summary: { wtes: any[], ppds: any[], rewards: any[], perks: any[], recommendedWTEs: any[], recommendedShopItems: any[], active: boolean } }> {
        const campaign = await this.loyaltyCampaignRepository.findById(id, {
            include: [
                {
                    relation: 'loyaltyEarns',
                    scope: {
						order: ['priority ASC'],
                        include: [{
							relation: 'earnEffects',
							scope: {
								include: [{
									relation: 'loyaltyRewardDefinition',
									scope: {
										include: ['rewardCoupon'],
									},
								}]
							}
						},
						{
							relation: 'earnConditions',
							scope: {
								where: {
									type: {nin: ['referrer-bonus', 'referred-customer-bonus']}
								}
							}
						}],
                    },
                },
                {
					relation: 'loyaltyRedemptionShopItems',
					scope: {
						order: ['priority ASC'],
						include: [
							{
								relation: 'loyaltyRewardDefinition',
								scope: {
									include: ['rewardCoupon'],
								},
						}],
					},
				},
                {
					relation: 'staticEffects',
					scope: {
						order: ['priority ASC'],
						include: [
							{
								relation: 'loyaltyRewardDefinition',
								scope: {
									include: ['rewardCoupon'],
								},
						}],
					},
				},
            ],
        });

		let uiCustomerActions = await this.uiCustomerActionRepository.find();

		const currentOrg = await this.organizationRepository.findById(orgId);
		const orgLanguage = (await this.organizationRepository.findById(orgId))?.language || 'en';
		let orgCurrency = await this.currencyRepository.findOne({
			where: { organizationId: orgId },
			include: [{relation: 'supportedCurrencies'}]
		});

        // Transform the data into the desired format
        let wtes: any[] = [];
		let ppds: any[] = [];
		const perks: any[] = [];
		let rewards: any[] = [];
		let recommendedWTEs: any[] = [];
		let recommendedShopItems: any[] = [];

		for(let i = 0; i < campaign.loyaltyEarns?.length; i++) {
			let earnConditionGroups = LoyaltyDetailsController.groupEarnConditions(campaign.loyaltyEarns[i].earnConditions);
			let conditionStrings = LoyaltyDetailsController.generateConditionStringsShort(earnConditionGroups, orgId, 'en');
			let conditionStringsLong = LoyaltyDetailsController.generateConditionStrings(campaign, earnConditionGroups, orgId, 'en', orgCurrency!, currentOrg?.externalDomain);
			let pillStrings = LoyaltyDetailsController.generatePillStrings(campaign.loyaltyEarns[i], orgId, 'en', orgCurrency!);

			let icon = '<svg fill="#15803D" xmlns="http://www.w3.org/2000/svg" height="33" viewBox="0 -960 960 960" width="33"><path d="M480-165q-17 0-33-7.5T419-194L113-560q-9-11-13.5-24T95-611q0-9 1.5-18.5T103-647l75-149q11-20 29.5-32t41.5-12h462q23 0 41.5 12t29.5 32l75 149q5 8 6.5 17.5T865-611q0 14-4.5 27T847-560L541-194q-12 14-28 21.5t-33 7.5Zm-95-475h190l-60-120h-70l-60 120Zm55 347v-267H218l222 267Zm80 0 222-267H520v267Zm144-347h106l-60-120H604l60 120Zm-474 0h106l60-120H250l-60 120Z"/></svg>';
			if(campaign.loyaltyEarns[i].hiddenFromAdminUi || !campaign.loyaltyEarns[i].earnConditions || campaign.loyaltyEarns[i].earnConditions.length === 0) {
				continue;
			}
			switch(campaign.loyaltyEarns[i].earnConditions[0].type) {
				case 'dollar-spent':
					icon = '<svg fill="#15803D" xmlns="http://www.w3.org/2000/svg" height="33" viewBox="0 -960 960 960" width="33"><path d="M237-120q-23 0-44.5-16T164-175q-25-84-41-145.5t-25.5-108Q88-475 84-511t-4-69q0-92 64-156t156-64h200q27-36 68.5-58t91.5-22q25 0 42.5 17.5T720-820q0 6-1.5 12t-3.5 11q-4 11-7.5 22t-5.5 24l91 91h47q17 0 28.5 11.5T880-620v210q0 13-7.5 23T852-372l-85 28-50 167q-8 26-29 41.5T640-120h-80q-33 0-56.5-23.5T480-200h-80q0 33-23.5 56.5T320-120h-83Zm3-80h80v-80h240v80h80l62-206 98-33v-141h-40L620-720q0-20 2.5-39t7.5-37q-29 8-51 27.5T547-720H300q-58 0-99 41t-41 99q0 41 21 140.5T240-200Zm400-320q17 0 28.5-11.5T680-560q0-17-11.5-28.5T640-600q-17 0-28.5 11.5T600-560q0 17 11.5 28.5T640-520Zm-160-80q17 0 28.5-11.5T520-640q0-17-11.5-28.5T480-680H360q-17 0-28.5 11.5T320-640q0 17 11.5 28.5T360-600h120Zm0 102Z"/></svg>'
					break;
				case 'nth-purchase':
					icon = '<svg fill="#15803D" xmlns="http://www.w3.org/2000/svg" height="33" viewBox="0 -960 960 960" width="33"><path d="M240-80q-33 0-56.5-23.5T160-160v-480q0-33 23.5-56.5T240-720h80q0-66 47-113t113-47q66 0 113 47t47 113h80q33 0 56.5 23.5T800-640v480q0 33-23.5 56.5T720-80H240Zm0-80h480v-480h-80v80q0 17-11.5 28.5T600-520q-17 0-28.5-11.5T560-560v-80H400v80q0 17-11.5 28.5T360-520q-17 0-28.5-11.5T320-560v-80h-80v480Zm160-560h160q0-33-23.5-56.5T480-800q-33 0-56.5 23.5T400-720ZM240-160v-480 480Z"/></svg>'
					break;
				case 'follow-on-instagram':
					icon = `<svg fill="#15803D" xmlns="http://www.w3.org/2000/svg" height="33" width="33" viewBox="0 0 56.7 56.7">
						<g>
							<path d="M28.2,16.7c-7,0-12.8,5.7-12.8,12.8s5.7,12.8,12.8,12.8S41,36.5,41,29.5S35.2,16.7,28.2,16.7z M28.2,37.7
							c-4.5,0-8.2-3.7-8.2-8.2s3.7-8.2,8.2-8.2s8.2,3.7,8.2,8.2S32.7,37.7,28.2,37.7z"/>
							<circle cx="41.5" cy="16.4" r="2.9" />
							<path d="M49,8.9c-2.6-2.7-6.3-4.1-10.5-4.1H17.9c-8.7,0-14.5,5.8-14.5,14.5v20.5c0,4.3,1.4,8,4.2,10.7c2.7,2.6,6.3,3.9,10.4,3.9
							h20.4c4.3,0,7.9-1.4,10.5-3.9c2.7-2.6,4.1-6.3,4.1-10.6V19.3C53,15.1,51.6,11.5,49,8.9z M48.6,39.9c0,3.1-1.1,5.6-2.9,7.3
							s-4.3,2.6-7.3,2.6H18c-3,0-5.5-0.9-7.3-2.6C8.9,45.4,8,42.9,8,39.8V19.3c0-3,0.9-5.5,2.7-7.3c1.7-1.7,4.3-2.6,7.3-2.6h20.6
							c3,0,5.5,0.9,7.3,2.7c1.7,1.8,2.7,4.3,2.7,7.2V39.9L48.6,39.9z"/>
						</g>
					</svg>`
					break;
				case 'product-review':
					icon = '<svg xmlns="http://www.w3.org/2000/svg" height="33" viewBox="0 -960 960 960" width="33" fill="#15803D"><path d="m480-461 76 46q11 7 22-.5t8-20.5l-20-87 68-59q10-9 6-21.5T622-617l-89-7-35-82q-5-12-18-12t-18 12l-35 82-89 7q-14 1-18 13.5t6 21.5l68 59-20 87q-3 13 8 20.5t22 .5l76-46ZM240-240l-92 92q-19 19-43.5 8.5T80-177v-623q0-33 23.5-56.5T160-880h640q33 0 56.5 23.5T880-800v480q0 33-23.5 56.5T800-240H240Zm-34-80h594v-480H160v525l46-45Zm-46 0v-480 480Z"/></svg>';
					break;
				case 'product-photo-review':
						icon = '<svg xmlns="http://www.w3.org/2000/svg" height="33" viewBox="0 0 32 32" width="33" fill="#15803D"><path d="M30 2.497h-28c-1.099 0-2 0.901-2 2v23.006c0 1.099 0.9 2 2 2h28c1.099 0 2-0.901 2-2v-23.006c0-1.099-0.901-2-2-2zM30 27.503l-28-0v-5.892l8.027-7.779 8.275 8.265c0.341 0.414 0.948 0.361 1.379 0.035l3.652-3.306 6.587 6.762c0.025 0.025 0.053 0.044 0.080 0.065v1.85zM30 22.806l-5.876-6.013c-0.357-0.352-0.915-0.387-1.311-0.086l-3.768 3.282-8.28-8.19c-0.177-0.214-0.432-0.344-0.709-0.363-0.275-0.010-0.547 0.080-0.749 0.27l-7.309 7.112v-14.322h28v18.309zM23 12.504c1.102 0 1.995-0.894 1.995-1.995s-0.892-1.995-1.995-1.995-1.995 0.894-1.995 1.995c0 1.101 0.892 1.995 1.995 1.995z"></path></svg>';
						break;
				case 'free-shipping':
					icon = '<svg xmlns="http://www.w3.org/2000/svg" height="33" viewBox="0 -960 960 960" width="33" fill="#15803D"><path d="M440-183v-274L200-596v274l240 139Zm80 0 240-139v-274L520-457v274Zm-80 92L160-252q-19-11-29.5-29T120-321v-318q0-22 10.5-40t29.5-29l280-161q19-11 40-11t40 11l280 161q19 11 29.5 29t10.5 40v318q0 22-10.5 40T800-252L520-91q-19 11-40 11t-40-11Zm200-528 77-44-237-137-78 45 238 136Zm-160 93 78-45-237-137-78 45 237 137Z" /></svg>'
					break;
			}

			if(campaign.loyaltyEarns[i].isRecommendation || campaign.loyaltyEarns[i].ignoreRecommendation ) {
				if(campaign.loyaltyEarns[i].isRecommendation) {
					recommendedWTEs.push({
						icon: icon,
						title: convertCurrencyPlaceholdersToValues(
							this.getEarnConditionName(campaign.loyaltyEarns[i], uiCustomerActions) + conditionStrings.join(', ') +
							' → ' + (pillStrings.join(', ')
						), orgCurrency!),
						toggle: campaign.loyaltyEarns[i].active,
						longSummary: convertCurrencyPlaceholdersToValues(conditionStringsLong.join(', ') + ': ' + pillStrings.join(', '), orgCurrency!),
						recommendedByAI: campaign.loyaltyEarns[i].recommendedByAI,
						recommendationState: campaign.loyaltyEarns[i].recommendationState,
						priority: campaign.loyaltyEarns[i].priority,
						id: campaign.loyaltyEarns[i].id,
					})
				}
			}
			else {
				let isDollarsSpent = campaign.loyaltyEarns[i].earnConditions?.[0]?.type === 'dollar-spent';
				const array = campaign.loyaltyEarns[i].earnConditions?.[0]?.type === 'dollar-spent'
					? ppds
					: wtes;

				array.push({
					icon: icon,
					title: convertCurrencyPlaceholdersToValues(campaign.loyaltyEarns[i].name, orgCurrency!),
					subtitle: convertCurrencyPlaceholdersToValues(
						this.getEarnConditionName(campaign.loyaltyEarns[i], uiCustomerActions) + conditionStrings.join(', ') +
						' → ' + (pillStrings.join(', ')
					), orgCurrency!),
					toggle: campaign.loyaltyEarns[i].active,
					longSummary: convertCurrencyPlaceholdersToValues(conditionStringsLong.join(', ') + ': ' + pillStrings.join(', '), orgCurrency!),
					recommendedByAI: campaign.loyaltyEarns[i].recommendedByAI,
					recommendationState: campaign.loyaltyEarns[i].recommendationState,
					pointsPerDollar: isDollarsSpent ? campaign.loyaltyEarns[i].earnEffects?.[0]?.pointsPerDollar : undefined,
					priority: campaign.loyaltyEarns[i].priority,
					id: campaign.loyaltyEarns[i].id,
				})
			}
		}

		for(let i = 0; i < campaign.loyaltyRedemptionShopItems?.length; i++) {
			let rewardIcon = '<svg fill="#15803D" xmlns="http://www.w3.org/2000/svg" height="33" viewBox="0 -960 960 960" width="33"><path d="M480-165q-17 0-33-7.5T419-194L113-560q-9-11-13.5-24T95-611q0-9 1.5-18.5T103-647l75-149q11-20 29.5-32t41.5-12h462q23 0 41.5 12t29.5 32l75 149q5 8 6.5 17.5T865-611q0 14-4.5 27T847-560L541-194q-12 14-28 21.5t-33 7.5Zm-95-475h190l-60-120h-70l-60 120Zm55 347v-267H218l222 267Zm80 0 222-267H520v267Zm144-347h106l-60-120H604l60 120Zm-474 0h106l60-120H250l-60 120Z"/></svg>'
			switch((campaign.loyaltyRedemptionShopItems[i] as any)?.loyaltyRewardDefinition?.rewardCoupon?.amountType) {
				case 'dollars-off-coupon':
					rewardIcon = '<svg fill="#15803D" xmlns="http://www.w3.org/2000/svg" height="33" viewBox="0 -960 960 960" width="33"><path d="M570-104q-23 23-57 23t-57-23L104-456q-11-11-17.5-26T80-514v-286q0-33 23.5-56.5T160-880h286q17 0 32 6.5t26 17.5l352 353q23 23 23 56.5T856-390L570-104Zm-57-56 286-286-353-354H160v286l353 354ZM260-640q25 0 42.5-17.5T320-700q0-25-17.5-42.5T260-760q-25 0-42.5 17.5T200-700q0 25 17.5 42.5T260-640ZM160-800Z"/></svg>';
					break;
				case 'percent-discount':
					rewardIcon = '<svg fill="#15803D" xmlns="http://www.w3.org/2000/svg" height="33" viewBox="0 -960 960 960" width="33"><path d="M300-520q-58 0-99-41t-41-99q0-58 41-99t99-41q58 0 99 41t41 99q0 58-41 99t-99 41Zm0-80q25 0 42.5-17.5T360-660q0-25-17.5-42.5T300-720q-25 0-42.5 17.5T240-660q0 25 17.5 42.5T300-600Zm360 440q-58 0-99-41t-41-99q0-58 41-99t99-41q58 0 99 41t41 99q0 58-41 99t-99 41Zm0-80q25 0 42.5-17.5T720-300q0-25-17.5-42.5T660-360q-25 0-42.5 17.5T600-300q0 25 17.5 42.5T660-240Zm-472 52q-11-11-11-28t11-28l528-528q11-11 28-11t28 11q11 11 11 28t-11 28L244-188q-11 11-28 11t-28-11Z"/></svg>';
					break;
				case 'free-product':
					rewardIcon = '<svg fill="#15803D" xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24"><path d="M160-160v-360q-33 0-56.5-23.5T80-600v-80q0-33 23.5-56.5T160-760h128q-5-9-6.5-19t-1.5-21q0-50 35-85t85-35q23 0 43 8.5t37 23.5q17-16 37-24t43-8q50 0 85 35t35 85q0 11-2 20.5t-6 19.5h128q33 0 56.5 23.5T880-680v80q0 33-23.5 56.5T800-520v360q0 33-23.5 56.5T720-80H240q-33 0-56.5-23.5T160-160Zm400-680q-17 0-28.5 11.5T520-800q0 17 11.5 28.5T560-760q17 0 28.5-11.5T600-800q0-17-11.5-28.5T560-840Zm-200 40q0 17 11.5 28.5T400-760q17 0 28.5-11.5T440-800q0-17-11.5-28.5T400-840q-17 0-28.5 11.5T360-800ZM160-680v80h280v-80H160Zm280 520v-360H240v360h200Zm80 0h200v-360H520v360Zm280-440v-80H520v80h280Z"/></svg>';
					break;
				case 'free-shipping':
					rewardIcon = '<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24" fill="#15803D"><path d="M440-183v-274L200-596v274l240 139Zm80 0 240-139v-274L520-457v274Zm-80 92L160-252q-19-11-29.5-29T120-321v-318q0-22 10.5-40t29.5-29l280-161q19-11 40-11t40 11l280 161q19 11 29.5 29t10.5 40v318q0 22-10.5 40T800-252L520-91q-19 11-40 11t-40-11Zm200-528 77-44-237-137-78 45 238 136Zm-160 93 78-45-237-137-78 45 237 137Z" /></svg>'
					break;
				case 'percent-off-product':
					rewardIcon = '<svg fill="#15803D" xmlns="http://www.w3.org/2000/svg" height="33" viewBox="0 -960 960 960" width="33"><path d="M300-520q-58 0-99-41t-41-99q0-58 41-99t99-41q58 0 99 41t41 99q0 58-41 99t-99 41Zm0-80q25 0 42.5-17.5T360-660q0-25-17.5-42.5T300-720q-25 0-42.5 17.5T240-660q0 25 17.5 42.5T300-600Zm360 440q-58 0-99-41t-41-99q0-58 41-99t99-41q58 0 99 41t41 99q0 58-41 99t-99 41Zm0-80q25 0 42.5-17.5T720-300q0-25-17.5-42.5T660-360q-25 0-42.5 17.5T600-300q0 25 17.5 42.5T660-240Zm-472 52q-11-11-11-28t11-28l528-528q11-11 28-11t28 11q11 11 11 28t-11 28L244-188q-11 11-28 11t-28-11Z"/></svg>';
					break;
				case 'dollar-off-product':
					rewardIcon = '<svg fill="#15803D" xmlns="http://www.w3.org/2000/svg" height="33" viewBox="0 -960 960 960" width="33"><path d="M570-104q-23 23-57 23t-57-23L104-456q-11-11-17.5-26T80-514v-286q0-33 23.5-56.5T160-880h286q17 0 32 6.5t26 17.5l352 353q23 23 23 56.5T856-390L570-104Zm-57-56 286-286-353-354H160v286l353 354ZM260-640q25 0 42.5-17.5T320-700q0-25-17.5-42.5T260-760q-25 0-42.5 17.5T200-700q0 25 17.5 42.5T260-640ZM160-800Z"/></svg>';
					break;
			}

			if(campaign.loyaltyRedemptionShopItems[i].isRecommendation || campaign.loyaltyRedemptionShopItems[i].ignoreRecommendation) {
				if(campaign.loyaltyRedemptionShopItems[i].isRecommendation) {
					recommendedShopItems.push({
						icon: rewardIcon,
						title: convertCurrencyPlaceholdersToValues(LoyaltyDetailsController.generateShopItemString(campaign.loyaltyRedemptionShopItems[i], (campaign.loyaltyRedemptionShopItems[i] as any)?.loyaltyRewardDefinition, (orgCurrency as any)?.supportedCurrencies), orgCurrency!),
						longSummary: convertCurrencyPlaceholdersToValues(LoyaltyDetailsController.generateShopItemString(campaign.loyaltyRedemptionShopItems[i], (campaign.loyaltyRedemptionShopItems[i] as any)?.loyaltyRewardDefinition, (orgCurrency as any)?.supportedCurrencies), orgCurrency!),
						toggle: campaign.loyaltyRedemptionShopItems[i].active,
						recommendedByAI: campaign.loyaltyRedemptionShopItems[i].recommendedByAI,
						recommendationState: campaign.loyaltyRedemptionShopItems[i].recommendationState,
						priority: campaign.loyaltyRedemptionShopItems[i].priority,
						id: campaign.loyaltyRedemptionShopItems[i].id,
					})
				}
			}
			else {
				rewards.push({
					icon: rewardIcon,
					title: convertCurrencyPlaceholdersToValues(campaign.loyaltyRedemptionShopItems[i].name, orgCurrency!),
					subtitle: convertCurrencyPlaceholdersToValues(LoyaltyDetailsController.generateShopItemString(campaign.loyaltyRedemptionShopItems[i], (campaign.loyaltyRedemptionShopItems[i] as any)?.loyaltyRewardDefinition, (orgCurrency as any)?.supportedCurrencies), orgCurrency!),
					longSummary: convertCurrencyPlaceholdersToValues(LoyaltyDetailsController.generateShopItemString(campaign.loyaltyRedemptionShopItems[i], (campaign.loyaltyRedemptionShopItems[i] as any)?.loyaltyRewardDefinition, (orgCurrency as any)?.supportedCurrencies), orgCurrency!),
					toggle: campaign.loyaltyRedemptionShopItems[i].active,
					recommendedByAI: campaign.loyaltyRedemptionShopItems[i].recommendedByAI,
					recommendationState: campaign.loyaltyRedemptionShopItems[i].recommendationState,
					id: campaign.loyaltyRedemptionShopItems[i].id,
					priority: campaign.loyaltyRedemptionShopItems[i].priority,
				})
			}



		}

		for(let i = 0; i < campaign.staticEffects?.length; i++) {
			let rewardIcon = '<svg fill="#15803D" xmlns="http://www.w3.org/2000/svg" height="33" viewBox="0 -960 960 960" width="33"><path d="M480-165q-17 0-33-7.5T419-194L113-560q-9-11-13.5-24T95-611q0-9 1.5-18.5T103-647l75-149q11-20 29.5-32t41.5-12h462q23 0 41.5 12t29.5 32l75 149q5 8 6.5 17.5T865-611q0 14-4.5 27T847-560L541-194q-12 14-28 21.5t-33 7.5Zm-95-475h190l-60-120h-70l-60 120Zm55 347v-267H218l222 267Zm80 0 222-267H520v267Zm144-347h106l-60-120H604l60 120Zm-474 0h106l60-120H250l-60 120Z"/></svg>'
			switch((campaign.staticEffects[i] as any)?.loyaltyRewardDefinition?.rewardCoupon?.amountType) {
				case 'dollars-off-coupon':
					rewardIcon = '<svg fill="#15803D" xmlns="http://www.w3.org/2000/svg" height="33" viewBox="0 -960 960 960" width="33"><path d="M570-104q-23 23-57 23t-57-23L104-456q-11-11-17.5-26T80-514v-286q0-33 23.5-56.5T160-880h286q17 0 32 6.5t26 17.5l352 353q23 23 23 56.5T856-390L570-104Zm-57-56 286-286-353-354H160v286l353 354ZM260-640q25 0 42.5-17.5T320-700q0-25-17.5-42.5T260-760q-25 0-42.5 17.5T200-700q0 25 17.5 42.5T260-640ZM160-800Z"/></svg>';
					break;
				case 'percent-discount':
					rewardIcon = '<svg fill="#15803D" xmlns="http://www.w3.org/2000/svg" height="33" viewBox="0 -960 960 960" width="33"><path d="M300-520q-58 0-99-41t-41-99q0-58 41-99t99-41q58 0 99 41t41 99q0 58-41 99t-99 41Zm0-80q25 0 42.5-17.5T360-660q0-25-17.5-42.5T300-720q-25 0-42.5 17.5T240-660q0 25 17.5 42.5T300-600Zm360 440q-58 0-99-41t-41-99q0-58 41-99t99-41q58 0 99 41t41 99q0 58-41 99t-99 41Zm0-80q25 0 42.5-17.5T720-300q0-25-17.5-42.5T660-360q-25 0-42.5 17.5T600-300q0 25 17.5 42.5T660-240Zm-472 52q-11-11-11-28t11-28l528-528q11-11 28-11t28 11q11 11 11 28t-11 28L244-188q-11 11-28 11t-28-11Z"/></svg>';
					break;
				case 'free-product':
					rewardIcon = '<svg fill="#15803D" xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24"><path d="M160-160v-360q-33 0-56.5-23.5T80-600v-80q0-33 23.5-56.5T160-760h128q-5-9-6.5-19t-1.5-21q0-50 35-85t85-35q23 0 43 8.5t37 23.5q17-16 37-24t43-8q50 0 85 35t35 85q0 11-2 20.5t-6 19.5h128q33 0 56.5 23.5T880-680v80q0 33-23.5 56.5T800-520v360q0 33-23.5 56.5T720-80H240q-33 0-56.5-23.5T160-160Zm400-680q-17 0-28.5 11.5T520-800q0 17 11.5 28.5T560-760q17 0 28.5-11.5T600-800q0-17-11.5-28.5T560-840Zm-200 40q0 17 11.5 28.5T400-760q17 0 28.5-11.5T440-800q0-17-11.5-28.5T400-840q-17 0-28.5 11.5T360-800ZM160-680v80h280v-80H160Zm280 520v-360H240v360h200Zm80 0h200v-360H520v360Zm280-440v-80H520v80h280Z"/></svg>';
					break;
				case 'free-shipping':
					rewardIcon = '<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24" fill="#15803D"><path d="M440-183v-274L200-596v274l240 139Zm80 0 240-139v-274L520-457v274Zm-80 92L160-252q-19-11-29.5-29T120-321v-318q0-22 10.5-40t29.5-29l280-161q19-11 40-11t40 11l280 161q19 11 29.5 29t10.5 40v318q0 22-10.5 40T800-252L520-91q-19 11-40 11t-40-11Zm200-528 77-44-237-137-78 45 238 136Zm-160 93 78-45-237-137-78 45 237 137Z" /></svg>'
					break;
			}

			// if(campaign.staticEffects[i].isRecommendation || campaign.staticEffects[i].ignoreRecommendation) {
			// 	if(campaign.staticEffects[i].isRecommendation) {
			// 		recommendedShopItems.push({
			// 			icon: rewardIcon,
			// 			title: LoyaltyDetailsController.generateShopItemString(campaign.staticEffects[i], (campaign.staticEffects[i] as any)?.loyaltyRewardDefinition, (orgCurrency as any)?.supportedCurrencies),
			// 			longSummary: LoyaltyDetailsController.generateShopItemString(campaign.staticEffects[i], (campaign.staticEffects[i] as any)?.loyaltyRewardDefinition, (orgCurrency as any)?.supportedCurrencies),
			// 			toggle: campaign.staticEffects[i].active,
			// 			// recommendedByAI: campaign.staticEffects[i].recommendedByAI,
			// 			// recommendationState: campaign.staticEffects[i].recommendationState,
			// 			id: campaign.staticEffects[i].id,
			// 		})
			// 	}
			// }
			// else {
				perks.push({
					icon: rewardIcon,
					title: convertCurrencyPlaceholdersToValues(campaign.staticEffects[i].name, orgCurrency!),
					subtitle: convertCurrencyPlaceholdersToValues(LoyaltyDetailsController.generateShopItemString(campaign.staticEffects[i], (campaign.staticEffects[i] as any)?.loyaltyRewardDefinition, (orgCurrency as any)?.supportedCurrencies, true), orgCurrency!),
					longSummary: convertCurrencyPlaceholdersToValues(LoyaltyDetailsController.generateShopItemString(campaign.staticEffects[i], (campaign.staticEffects[i] as any)?.loyaltyRewardDefinition, (orgCurrency as any)?.supportedCurrencies, true), orgCurrency!),
					toggle: campaign.staticEffects[i].active,
					priority: campaign.staticEffects[i].priority,
					// recommendedByAI: campaign.staticEffects[i].recommendedByAI,
					// recommendationState: campaign.staticEffects[i].recommendationState,
					id: campaign.staticEffects[i].id,
				})
			// }



		}

        return {
            summary: { wtes, ppds, rewards, perks, recommendedWTEs, recommendedShopItems, active: campaign.active },
        };
    }

	private getEarnConditionName(earn: any, uiCustomerActions: any[]): string {
		let earnConditions = earn.earnConditions;
		let earnEffects = earn.earnEffects;
		let earnConditionString = '';
		if(earnConditions && earnConditions.length > 0) {
			//Find where type of earnConditions[0].type == uiCustomerActions.type
			let earnConditionType = uiCustomerActions.find((action: any) => action.type == earnConditions[0].type);
			earnConditionString = earnConditionType?.name;
		}
		if(!earnConditionString) {
			return 'Unknown';
		}
		let earnConditionType = earnConditions
        // Implement logic to construct the description based on earnEffects and earnConditions
        // Example:
        // return `${earn.earnConditions.condition} → ${earn.earnEffects.effect}`;
		return `${earnConditionString}: `;
    }
}
