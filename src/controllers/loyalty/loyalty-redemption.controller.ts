import { repository, } from '@loopback/repository';
import {
	HttpErrors,
	api,
	get,
	getModelSchemaRef,
	param,
	post,
	requestBody,
} from '@loopback/rest';

import {authenticate} from '@loopback/authentication';
import {GuardSkipStrategy, guardStrategy, skipGuardCheck, injectRaleonUserOrgId, injectRaleonUserId, injectUserOrgId} from '../../interceptors';
import {
	InventoryCouponRepository,
	LoyaltyCampaignRepository,
	LoyaltyCurrencyBalanceRepository,
	LoyaltyCurrencyRepository,
	LoyaltyCurrencyTxLogRepository,
	LoyaltyEarnRepository,
	LoyaltyEventRepository,
	LoyaltyProgramRepository,
	LoyaltyRedemptionShopItemRepository,
	LoyaltyRewardDefinitionRepository,
	LoyaltyRewardLogRepository,
	LoyaltyStaticEffectRepository,
	RaleonUserEarnLogRepository,
	RaleonUserIdentityRepository
} from '../../repositories';
import {EarnEffectWithRelations, InventoryCoupon, LoyaltyCurrency, LoyaltyEarn, LoyaltyProgram, LoyaltyRedemptionShopItem, LoyaltyRewardDefinition, LoyaltyStaticEffect, RaleonUserIdentity} from '../../models';
import {inject, service} from '@loopback/core';
import {LoyaltyBalanceManager} from '../../services/loyalty/loyalty-balance-manager.service';
import {DevDbDataSource} from '../../datasources';
import {LoyaltyRewardLogService} from '../../services/loyalty/loyalty-reward-log.service';
import {LoyaltyProgramController} from './loyalty-program.controller';
import e from 'express';
import {LoyaltyRedemptionService} from '../../services/loyalty/loyalty-redemption.service';
import {LoyaltyDetailsService} from '../../services/loyalty/loyalty-details.service';
import {LoyaltyEventPublisher} from '../../services/event-stream/loyalty-event-publisher.service';
import {TierService} from '../../services/tier.service';
import {LoyaltyMembersService} from '../../services/loyalty/loyalty-members.service';


@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class LoyaltyRedemptionController {

	constructor(
		@repository(LoyaltyProgramRepository) public loyaltyProgramRepository: LoyaltyProgramRepository,
		@repository(LoyaltyCampaignRepository) public loyaltyCampaignRepository: LoyaltyCampaignRepository,
		@repository(LoyaltyRedemptionShopItemRepository) public loyaltyRedemptionShopItemRepository: LoyaltyRedemptionShopItemRepository,
		@repository(RaleonUserIdentityRepository) public raleonUserIdentityRepository: RaleonUserIdentityRepository,
		@repository(LoyaltyCurrencyRepository) public loyaltyCurrencyRepository: LoyaltyCurrencyRepository,
		@repository(LoyaltyRewardDefinitionRepository) public loyaltyRewardDefinitionRepository: LoyaltyRewardDefinitionRepository,
		@repository(InventoryCouponRepository) public inventoryCouponRepository: InventoryCouponRepository,
		@repository(LoyaltyEarnRepository) public loyaltyEarnRepository: LoyaltyEarnRepository,
		@service(LoyaltyBalanceManager) public loyaltyBalanceManager: LoyaltyBalanceManager,
		@service(LoyaltyRewardLogService) private loyaltyRewardLogService: LoyaltyRewardLogService,
		@repository(LoyaltyRewardLogRepository) private loyaltyRewardLogRepository: LoyaltyRewardLogRepository,
		@repository(LoyaltyCurrencyTxLogRepository) private loyaltyCurrencyTxLogRepository: LoyaltyCurrencyTxLogRepository,
		@repository(LoyaltyCurrencyBalanceRepository) private loyaltyCurrencyBalanceRepository: LoyaltyCurrencyBalanceRepository,
		@repository(RaleonUserEarnLogRepository) private raleonUserEarnLogRepository: RaleonUserEarnLogRepository,
		@inject('controllers.LoyaltyProgramController') private loyaltyProgramController: LoyaltyProgramController,
		@service(LoyaltyRedemptionService) private loyaltyRedemptionService: LoyaltyRedemptionService,
		@repository(LoyaltyStaticEffectRepository) private loyaltyStaticEffectRepository: LoyaltyStaticEffectRepository,
		@service(LoyaltyDetailsService) private loyaltyDetailsService: LoyaltyDetailsService,
		@service(LoyaltyEventPublisher) protected loyaltyEventPublisher: LoyaltyEventPublisher,
		@service(TierService) protected tierService: TierService,
		@service(LoyaltyMembersService) private loyaltyMembersService: LoyaltyMembersService,
	) {}

	@skipGuardCheck()
	@authenticate('shopify-customer-access-token')
	@post('/loyalty-redeem', {
		responses: {
			'200': {
				description: 'Returns the result of the redemption',
				content: {
					'application/json': {
						schema: getModelSchemaRef(InventoryCoupon),
					},
				},
			},
		},
	})
	async redeem(
		@requestBody({
			content: {
				'application/json': {
					schema:  {
						properties: {
							shopItemId: {
								type: 'number',
								description: 'The Shop Item ID'
							}
						},
						required: ['shopItemId']
					}
				},
			},
		}) redemptionData: any,
		@injectRaleonUserOrgId() orgId: number,
		@injectRaleonUserId() raleonUserId: number,
		@inject('datasources.dev_db') dataSource: DevDbDataSource
	) {
		console.log('Redemption Data', JSON.stringify(redemptionData));
		let shopItemId = redemptionData.shopItemId;
		let shopItem = (await this.loyaltyRedemptionShopItemRepository.find({
			where: { id: shopItemId },
			include: [
				{
					relation: 'loyaltyRewardDefinition',
					scope: {
						include: [ { relation: 'rewardCoupon' } ]
					}
				}
			]
		}))?.[0];

		const program = await this.loyaltyCampaignRepository.loyaltyProgram(shopItem?.loyaltyCampaignId);

		if (program.orgId !== orgId) {
			throw new HttpErrors.UnprocessableEntity('Invalid shop item. Organization does not match.');
		}

		const currencies: LoyaltyCurrency[] = await this.loyaltyProgramRepository.loyaltyCurrencies(program.id).find({
			include: [
				{
					relation: 'loyaltyCurrencyBalances',
					scope: {
						where: {
							raleonUserId,
						}
					}
				}
			]
		});
		const currency: LoyaltyCurrency = currencies[0]; //TODO: update to support multiple currencies in a program
		const currencyBalance = currency.loyaltyCurrencyBalances?.[0]; //TODO: update to support multiple balances per currency
		const loyaltyRewardDefinition = (shopItem as any).loyaltyRewardDefinition;

		await this.validateRedemption(
			shopItem,
			program,
			currency,
			loyaltyRewardDefinition,
			raleonUserId,
		);

		console.log(`got the balance data: ${JSON.stringify(currencyBalance)}`);
		try {
			await this.loyaltyBalanceManager.updateBalanceAndLog(currency.id, {
				raleonUserId,
				balanceChange: -shopItem.price,
				info: `Redeemed ${shopItem.name} for ${shopItem.price} ${currency.name}`,
				earnName: shopItem.name,
			})
		} catch (e) {
			console.error(`Error updating balance: ${e}`);
			throw new HttpErrors.UnprocessableEntity('Error updating balance');
		}

		//TODO: set daysToRedeem here and add it to the inventory coupon record below
		await this.loyaltyRedemptionService.incrementLoyaltyReward(loyaltyRewardDefinition.id, 'redeemed', dataSource);

		//Lets grap inventory coupon necessary data
		let expirationDays = loyaltyRewardDefinition.rewardCoupon?.expiresInDays;
		let currentDate = new Date();
		let expirationDate = new Date(currentDate.setDate(currentDate.getDate() + expirationDays));

		//min order is on rewardCoupon, as well as maximumDiscount
		const coupon = await this.inventoryCouponRepository.create({
			name: loyaltyRewardDefinition.rewardCoupon?.name,
			rewardCouponId: loyaltyRewardDefinition.rewardCoupon?.id,
			raleonUserId,
			expiration: expirationDate.toISOString(),
			orgId,
			used: false,
			hiddenFromLoyaltyUi: loyaltyRewardDefinition.rewardCoupon?.hiddenFromLoyaltyUi,
			loyaltyGiveawayId: loyaltyRewardDefinition.rewardCoupon?.loyaltyGiveawayId,
			loyaltyRewardDefinitionId: loyaltyRewardDefinition.id,
		});

		await this.loyaltyRewardLogService.createLogEntryForCoupon(coupon);
		return coupon;
	}


	@skipGuardCheck()
	@authenticate('shopify-customer-access-token')
	@post('/loyalty-static-redeem', {
		responses: {
			'200': {
				description: 'Returns the result of the redemption',
				content: {
					'application/json': {
						schema: getModelSchemaRef(InventoryCoupon),
					},
				},
			},
		},
	})
	async staticRedeem(
		@requestBody({
			content: {
				'application/json': {
					schema:  {
						properties: {
							staticEffectId: {
								type: 'number',
								description: 'The Shop Item ID'
							}
						},
						required: ['staticEffectId']
					}
				},
			},
		}) redemptionData: any,
		@injectRaleonUserOrgId() orgId: number,
		@injectRaleonUserId() raleonUserId: number,
		@inject('datasources.dev_db') dataSource: DevDbDataSource
	) {
		console.log('Redemption Data', JSON.stringify(redemptionData));
		let staticEffectId = redemptionData.staticEffectId;
		let staticEffect = (await this.loyaltyStaticEffectRepository.find({
			where: { id: staticEffectId },
			include: [
				{
					relation: 'loyaltyRewardDefinition',
					scope: {
						include: [ { relation: 'rewardCoupon' } ]
					}
				}
			]
		}))?.[0];

		const program = await this.loyaltyCampaignRepository.loyaltyProgram(staticEffect?.loyaltyCampaignId);

		if (program.orgId !== orgId) {
			throw new HttpErrors.UnprocessableEntity('Invalid shop item. Organization does not match.');
		}

		const currencies: LoyaltyCurrency[] = await this.loyaltyProgramRepository.loyaltyCurrencies(program.id).find({
			include: [
				{
					relation: 'loyaltyCurrencyBalances',
					scope: {
						where: {
							raleonUserId,
						}
					}
				}
			]
		});
		const currency: LoyaltyCurrency = currencies[0]; //TODO: update to support multiple currencies in a program
		const currencyBalance = currency.loyaltyCurrencyBalances?.[0]; //TODO: update to support multiple balances per currency
		const loyaltyRewardDefinition = (staticEffect as any).loyaltyRewardDefinition;

		await this.validateStaticRedemption(
			staticEffect,
			program,
			currency,
			loyaltyRewardDefinition,
			raleonUserId,
		);


		//TODO: set daysToRedeem here and add it to the inventory coupon record below
		await this.loyaltyRedemptionService.incrementLoyaltyReward(loyaltyRewardDefinition.id, 'redeemed', dataSource);

		//Lets grap inventory coupon necessary data
		let expirationDays = loyaltyRewardDefinition.rewardCoupon?.expiresInDays;
		let currentDate = new Date();
		let expirationDate = new Date(currentDate.setDate(currentDate.getDate() + expirationDays));

		//min order is on rewardCoupon, as well as maximumDiscount
		const coupon = await this.inventoryCouponRepository.create({
			name: loyaltyRewardDefinition.rewardCoupon?.name,
			rewardCouponId: loyaltyRewardDefinition.rewardCoupon?.id,
			raleonUserId,
			expiration: expirationDate.toISOString(),
			orgId,
			used: false,
			hiddenFromLoyaltyUi: loyaltyRewardDefinition.rewardCoupon?.hiddenFromLoyaltyUi,
			loyaltyGiveawayId: loyaltyRewardDefinition.rewardCoupon?.loyaltyGiveawayId,
			loyaltyRewardDefinitionId: loyaltyRewardDefinition.id,
		});

		await this.loyaltyRewardLogService.createLogEntryForCoupon(coupon);
		return coupon;
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@post('/loyalty-earn-log', {
		responses: {
			'200': {
				description: 'Used to Log completion of an earn for a user, use this to prevent multiple grants of the same earn',
				content: {
					'application/json': {
						schema: getModelSchemaRef(InventoryCoupon),
					},
				},
			},
		},
	})
	async earnLog(
		@requestBody({
			content: {
				'application/json': {
					schema:  {
						properties: {
							earnId: {
								type: 'number',
								description: 'The Loyalty Earn ID'
							},
							raleonUserId: {
								type: 'number',
								description: 'The Raleon User ID'
							}
						},
						required: ['earnId', 'raleonUserId']
					}
				},
			},
		}) grantData: any,
		@injectUserOrgId() orgId: number
	) {
		const program = await this.loyaltyProgramRepository.findOne({
            where: {
                orgId: orgId
            },
            include: [
                {
                    relation: 'loyaltyCampaigns',
                    scope: {
                        include: [{
                            relation: 'loyaltyEarns',
                            scope: {
                                where: { id: grantData.earnId }
                            }
                        }]
                    }
                }
            ]
        });

		if (!program) {
			throw new HttpErrors.NotFound('Loyalty Program not found');
		}

		//Check all loyaltyEarns to see if the id exists
		let found = false;
		for (let campaign of program.loyaltyCampaigns) {
			if(!campaign.loyaltyEarns) continue;
			for (let earn of campaign.loyaltyEarns) {
				if (earn.id === grantData.earnId) {
					found = true;
					break;
				}
			}
		}

		if (!found) {
			return { success: false, message: 'Earn not found'}
		}

		await this.raleonUserEarnLogRepository.create({
			raleonUserId: grantData.raleonUserId,
			loyaltyEarnId: grantData.earnId,
			completed: true,
		});

		return { success: true };
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@post('/loyalty-grant', {
		responses: {
			'200': {
				description: 'Returns the result of the grant',
				content: {
					'application/json': {
						schema: getModelSchemaRef(InventoryCoupon),
					},
				},
			},
		},
	})
	async grant(
		@requestBody({
			content: {
				'application/json': {
					schema:  {
						properties: {
							rewardDefinitionId: {
								type: 'number',
								description: 'The Reward Definition ID'
							},
							raleonUserId: {
								type: 'number',
								description: 'The Raleon User ID'
							},
							orderId: {
								type: 'number',
								description: 'the order id that triggered the reward grant, if applicable'
							},
							itemId: {
								type: 'number',
								description: 'the item id that triggered the reward grant, if applicable'
							},
							earnEffectId: {
								type: 'number',
								description: 'the earn effect id that triggered the reward grant, if applicable'
							},
						},
						required: ['rewardDefinitionId', 'raleonUserId']
					}
				},
			},
		}) grantData: any,
		@injectUserOrgId() orgId: number,
		@inject('datasources.dev_db') dataSource: DevDbDataSource
	) {
		console.log('Redemption Data', JSON.stringify(grantData));
		return this.loyaltyRedemptionService.grantReward(
			grantData.rewardDefinitionId,
			orgId,
			grantData.raleonUserId,
			dataSource,
			grantData.orderId,
			grantData.itemId,
			grantData.earnEffectId
		);
	}


	@skipGuardCheck()
	@authenticate('jwt')
	@post('/loyalty-grant-referral-bonus-and-kickback', {
		responses: {
			'200': {
				description: 'Returns the result of the grant',
				content: {
					'application/json': {},
				},
			},
		},
	})
	async grantReferralBonusAndKickback(
		@requestBody({
			content: {
				'application/json': {
					schema:  {
						properties: {
							raleonUserId: {
								type: 'number',
								description: 'The Raleon User ID'
							}
						},
						required: ['raleonUserId']
					}
				},
			},
		}) grantData: any,
		@injectUserOrgId() orgId: number,
		@inject('datasources.dev_db') dataSource: DevDbDataSource
	) {
		const referredUser = await this.raleonUserIdentityRepository.findOne({
			where: {
				raleonUserId: grantData.raleonUserId,
			}
		});

		if (!referredUser) {
			throw new HttpErrors.NotFound('Referred user not found');
		}

		if (referredUser.referralComplete) {
			return;
		}

		const referrerCode = referredUser.signupReferrer;
		if (!referrerCode) {
			return;
		}

		const referrer = await this.raleonUserIdentityRepository.findOne({
			where: {
				referralCode: referrerCode,
				orgId,
			}
		});

		if (!referrer) {
			throw new HttpErrors.NotFound('Referral code not found');
		}

		referredUser.referralComplete = true;
		await this.raleonUserIdentityRepository.update(referredUser);


		// find all referral type WTEs
		const livePrograms = await this.loyaltyProgramController.findLive(referredUser.raleonUserId, referredUser.identityValue as any as number, orgId);
		const programs = livePrograms.filter(x => x.referralsActive);

		if (!programs.length) {
			return;
		}

		const currency = programs[0].loyaltyCurrencies?.[0];
		const pointsName = currency?.name || 'points';

		const earns = programs.map(x => x.loyaltyCampaigns).flat().map(x => x.loyaltyEarns).flat();
		const kickbackBonuses: Array<LoyaltyEarn> = earns.filter(x => x.earnConditions.some((y: any) => y.type === 'referrer-bonus'));
		// const referredCustomerBonuses: Array<LoyaltyEarn> = earns.filter(x => x.earnConditions.some((y: any) => y.type === 'referred-customer-bonus'));

		let returnData: any = {};
		for (const earn of kickbackBonuses) {
			for (const effect of earn.earnEffects) {
				if (effect.loyaltyRewardDefinitionId) {
					try {
						returnData = {
							kickbackCoupon: await this.loyaltyRedemptionService.grantReward(
								effect.loyaltyRewardDefinitionId,
								orgId,
								referrer!.raleonUserId,
								dataSource,
								undefined,
								undefined,
								effect.id,
								'referrer-bonus'
							)
						};
					} catch (e) {
						console.error('Error granting referral kick-back reward', e);
					}
				} else if (effect.points) {
					returnData = {
						kickbackPoints: await this.loyaltyBalanceManager.updateBalanceAndLog(effect.loyaltyCurrencyId!, {
							raleonUserId: referrer!.raleonUserId,
							balanceChange: effect.points!,
							info: `Granted ${effect.points} referral reward ${pointsName} for ${earn.name}`,
							earnEffectId: effect.id,
							earnName: earn.name,
						})
					};
				}
			}
		}

		await this.loyaltyEventPublisher.publishReferralCompletedEvent(
			referrer.identityValue,
			orgId,
		);
		return returnData;
	}


	@skipGuardCheck()
	@authenticate('shopify-customer-access-token')
	@get('/loyalty-notifications')
	async getLogs(
		@injectRaleonUserId() raleonUserId: number,
		@injectRaleonUserOrgId() orgId: number,
		@inject('datasources.dev_db') dataSource: DevDbDataSource
	) {
		console.log('Getting loyalty notifications for user', raleonUserId, 'in org', orgId);
		const program = await this.loyaltyProgramRepository.findOne({
			where: { orgId }
		});

		const rewardLogs = await this.loyaltyRewardLogRepository.find({
			where: {
				raleonUserId,
			},
			include: [
				{
					relation: 'inventoryCoupon',
					scope: {
						include: [
							{
								relation: 'loyaltyRewardDefinition',
								scope: {
									include: [ { relation: 'rewardCoupon' } ]
								}
							}
						]
					}
				}
			]
		});
		const userBalance = await this.loyaltyCurrencyBalanceRepository.findOne({
			where: {
				raleonUserId,
			}
		});

		const balanceLogs = userBalance ? await this.loyaltyCurrencyTxLogRepository.find({
			where: {
				loyaltyCurrencyBalanceId: userBalance?.id,
			}
		}) : [];

		const unreadLogs = [
			...rewardLogs,
			...balanceLogs
		].sort((a: any, b: any) => new Date(b.date).getTime() - new Date(a.date).getTime());
		console.log('Done Getting loyalty notifications for user', raleonUserId, 'in org', orgId);
		return {
			notificationsActive: program?.notificationsActive,
			notifications: unreadLogs,
		};
	}

	@skipGuardCheck()
	@authenticate('shopify-customer-access-token')
	@post('/loyalty-notifications/{type}/{id}/mark-viewed')
	async markLogViewed(
		@param.path.string('type') type: string,
		@param.path.number('id') id: number,
	) {
		if (type === 'balance') {
			return this.loyaltyCurrencyTxLogRepository.updateById(id, { customerViewed: true });
		} else {
			return this.loyaltyRewardLogRepository.updateById(id, { customerViewed: true });
		}
	}


	@skipGuardCheck()
	@authenticate('shopify-customer-access-token')
	@post('/loyalty-notifications/mark-viewed')
	async markMultipleLogViewed(
		@requestBody({
			content: {
				'application/json': {
					schema:  {}
				},
			},
		}) payload: Array<{ type: 'balance'|'coupon', id: number }>,
	) {
		const currencyTxRequests = payload.filter(log => log.type === 'balance');
		const rewardLogRequests = payload.filter(log => log.type === 'coupon');

		if (currencyTxRequests.length) {
			await this.loyaltyCurrencyTxLogRepository.updateAll(
				{ customerViewed: true },
				{ id: { inq: currencyTxRequests.map(log => log.id) } }
			);
		}

		if (rewardLogRequests.length) {
			await this.loyaltyRewardLogRepository.updateAll(
				{ customerViewed: true },
				{ id: { inq: rewardLogRequests.map(log => log.id) } }
			);
		}
	}

	@skipGuardCheck()
	@authenticate('shopify-customer-access-token')
	@post('/loyalty-grant-customer', {
		responses: {
			'200': {
				description: 'Returns the result of the grant',
				content: {
					'application/json': {
						schema: getModelSchemaRef(InventoryCoupon),
					},
				},
			},
		},
	})
	async grantToCustomer(
		@requestBody({
			content: {
				'application/json': {
					schema:  {
						properties: {
							rewardDefinitionId: {
								type: 'number',
								description: 'The Reward Definition ID'
							},
						},
						required: ['earnId']
					}
				},
			},
		}) grantData: any,
		@injectRaleonUserId() raleonUserId: number,
		@injectRaleonUserOrgId() orgId: number,
		@inject('datasources.dev_db') dataSource: DevDbDataSource,
		bypassClientGrantableTypeCheck = false
	) {
		console.log('Grant Data', JSON.stringify(grantData));

		await this.validateNotGranted(grantData.earnId, raleonUserId);

		const earn = (await this.loyaltyEarnRepository.find({
			where: {
				id: grantData.earnId,
			},
			include: ['earnConditions', {
				relation: 'earnEffects',
				scope: {
					include: [{
						relation: 'loyaltyRewardDefinition',
						scope: {
							include: [ { relation: 'rewardCoupon' } ]
						}

					}]
				}
			}]
		}))?.[0];

		if (!earn) {
			throw new HttpErrors.NotFound('Loyalty Earn not found');
		}

		const program = await this.loyaltyCampaignRepository.loyaltyProgram(earn?.loyaltyCampaignId);
		const currency = await program.loyaltyCurrencies?.[0];
		const pointsName = currency?.name || 'points';

		if (program.orgId !== orgId) {
			throw new HttpErrors.UnprocessableEntity('Invalid earn. Organization does not match.');
		}

		if (earn.earnConditions) {
			//only one condition for grantable types for now (social wte)
			const earnCondition = earn.earnConditions[0];
			if (!bypassClientGrantableTypeCheck && !this.loyaltyRedemptionService.CLIENT_GRANTABLE_TYPES.includes(earnCondition.type)) {
				throw new HttpErrors.UnprocessableEntity('Invalid earn condition type');
			}
		}

		const grantableEffectTypes = ['dollars-off-coupon', 'percent-discount', 'free-shipping', 'free-product', 'giveaway-entry'];

		const campaignMembers = await this.loyaltyMembersService.getCampaignMemberIdentities(earn.loyaltyCampaignId, raleonUserId);
		const campaignMemberUserIds = campaignMembers.map(x => x.raleonUserId);
		if (!campaignMemberUserIds.includes(raleonUserId)) {
			throw new HttpErrors.Forbidden('User is not a member of the campaign');
		}

		if (earn.earnEffects) {
			let granted = false;
			for (let effect of earn.earnEffects) {
				if (effect.type === 'vip-entry' && (effect as EarnEffectWithRelations)?.loyaltyRewardDefinition?.rewardCoupon?.amountType === 'vip-tier') {
					const identities = await this.raleonUserIdentityRepository.find({
						where: {
							raleonUserId,
						}
					});

					await this.tierService.reassignUsersForAllTiers(orgId, identities);

					granted = true;
				} else if (grantableEffectTypes.includes(effect.type)) {
					await this.loyaltyRedemptionService.grantReward(
						effect.loyaltyRewardDefinitionId!,
						orgId,
						raleonUserId,
						dataSource,
						undefined,
						undefined,
						effect.id,
						earn.earnConditions?.[0]?.type
					);
					granted = true;
				} else if (effect.type == 'points') {
					await this.loyaltyBalanceManager.updateBalanceAndLog(effect.loyaltyCurrencyId!, {
						raleonUserId,
						balanceChange: effect.points!,
						info: `Granted ${effect.points} ${pointsName} for ${earn.name}`,
						earnEffectId: effect.id,
						earnName: earn.name,
					});
					granted = true;
				}
			}

			if (granted) {
				await this.raleonUserEarnLogRepository.create({
					raleonUserId,
					loyaltyEarnId: earn.id,
					completed: true,
				});
			}
		}
	}

	private async validateNotGranted(earnId: number, raleonUserId: number) {
		const userLogs = await this.raleonUserEarnLogRepository.find({
			where: {
				raleonUserId: raleonUserId,
				loyaltyEarnId: earnId,
				completed: true,
			}
		});

		if (userLogs?.length) {
			throw new HttpErrors.UnprocessableEntity('Earn has already been granted');
		}
	}
	private async validateRedemption(
		shopItem: LoyaltyRedemptionShopItem,
		program: LoyaltyProgram,
		currency: LoyaltyCurrency,
		rewardDefinition: LoyaltyRewardDefinition,
		raleonUserId: number,
	) {
		if (!shopItem) {
			throw new HttpErrors.NotFound('Shop Item not found');
		}
		if (!program) {
			throw new HttpErrors.NotFound('No loyalty program found');
		}
		if (!currency) {
			throw new HttpErrors.NotFound('No loyalty currencies found');
		}

		const currencyBalance = currency?.loyaltyCurrencyBalances?.[0]; //TODO: update to support multiple balances per currency
		if (!currencyBalance) {
			throw new HttpErrors.NotFound('User does not have a balance associated');
		}
		if ((currencyBalance.balance || 0) < shopItem.price) {
			throw new HttpErrors.UnprocessableEntity('User does not have enough points to redeem');
		}
		if (!rewardDefinition) {
			throw new HttpErrors.NotFound('No loyalty reward definition found');
		}

		const userLogs = await this.loyaltyRewardLogRepository.find({
			where: {
				raleonUserId: raleonUserId,
				loyaltyRewardDefinitionId: rewardDefinition.id,
			}
		});

		const redemptionLimitSet =
			rewardDefinition.redeemed !== null &&
			rewardDefinition.redeemed !== undefined;
		const userRedemptionSet =
			rewardDefinition.maxUserRedemptions !== null &&
			rewardDefinition.maxUserRedemptions !== undefined;

		//TODO: when currentInventory is added, update this to redeemed >= currentInventory
		// if (redemptionLimitSet && (rewardDefinition.redeemed! >= rewardDefinition.maxUserRedemptions!)) {
		// 	throw new HttpErrors.UnprocessableEntity('Reward has been redeemed the maximum number of times');
		// }

		if (userRedemptionSet && (userLogs?.length || 0) >= rewardDefinition.maxUserRedemptions! && rewardDefinition.maxUserRedemptions !== -1) {
			throw new HttpErrors.UnprocessableEntity('Reward has been redeemed the maximum number of times');
		}

		if (!rewardDefinition.rewardCouponId) {
			throw new HttpErrors.NotFound('No reward coupon found');
		}
	}

	private async validateStaticRedemption(
		staticEffect: LoyaltyStaticEffect,
		program: LoyaltyProgram,
		currency: LoyaltyCurrency,
		rewardDefinition: LoyaltyRewardDefinition,
		raleonUserId: number,
	) {
		if (!staticEffect) {
			throw new HttpErrors.NotFound('Shop Item not found');
		}
		if (!program) {
			throw new HttpErrors.NotFound('No loyalty program found');
		}
		if (!currency) {
			throw new HttpErrors.NotFound('No loyalty currencies found');
		}
		if (!rewardDefinition) {
			throw new HttpErrors.NotFound('No loyalty reward definition found');
		}
		if (!rewardDefinition.rewardCouponId) {
			throw new HttpErrors.NotFound('No reward coupon found');
		}

		const details = await this.loyaltyDetailsService.getLoyaltyDetails(program.orgId, raleonUserId);
		if (!details?.campaigns?.some?.(x => x.id === staticEffect.loyaltyCampaignId)) {
			throw new HttpErrors.NotFound('Invalid static effect');
		}
	}

}
