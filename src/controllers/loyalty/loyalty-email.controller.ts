// Uncomment these imports to begin using these cool features!
import {authenticate} from '@loopback/authentication';
import {User} from '@loopback/authentication-jwt';
import {authorize} from '@loopback/authorization';
import {inject} from '@loopback/core';
import {repository} from '@loopback/repository';
import { api, get, param, post, Request, requestBody, RestBindings } from '@loopback/rest';
import {SecurityBindings} from '@loopback/security';
import {injectUserOrgId, GuardSkipStrategy, guardStrategy, skipGuardCheck} from '../../interceptors';
import {basicAuthorization} from '../../services';
import {LoyaltyEventRepository} from '../../repositories/loyalty-event.repository';
import {LoyaltyEventEmailRepository} from '../../repositories/loyalty-event-email.repository';
import {EventStreamEvent} from '../../services/event-stream/loyalty-event-publisher.service';
import {LoyaltyEvent} from '../../models';
const fetch = require('node-fetch');
@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class LoyaltyEmailController {
	constructor(
		@inject(RestBindings.Http.REQUEST) private req: Request,
		@inject(SecurityBindings.USER) private user: User,
		@repository('LoyaltyEventRepository') private loyaltyEventRepository: LoyaltyEventRepository,
		@repository('LoyaltyEventEmailRepository') private loyaltyEventEmailRepository: LoyaltyEventEmailRepository,
	) { }

	@get('/loyalty-email-template', {
		responses: {
			'200': {
				description: 'Retrieve loyalty email presigned url',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getTemplate() {
		const response = await fetch('https://raleoncdn.s3.amazonaws.com/loyalty-emails/email-template-v1.html');
		const data = await response.text();
		return {
			statusCode: 200,
			body: data
		};
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/loyalty-email-branding', {
		responses: {
			'200': {
				description: 'Save email template branding',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async saveEmailTemplateBranding(
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						properties: {
							eventType: {type: 'string'},
							emailSubject: {type: 'string'},
							fromName: {type: 'string'},
							brandingData: { type: 'object' },
							smartSend: { type: 'boolean' }
						}
					}
				},
			},
		})
		data: {
			eventType: string,
			emailSubject: string,
			fromName: string,
			brandingData: any,
			smartSend: boolean,
			active?: boolean,
		},
		@injectUserOrgId() orgId: number,
	) {
		const loyaltyEvent = await this.loyaltyEventRepository.findOne({
			where: {
				name: data.eventType
			}
		});

		if (!loyaltyEvent) {
			return {
				statusCode: 400,
				body: 'Invalid event type'
			};
		}

		const existingEventEmail = await this.loyaltyEventEmailRepository.findOne({
			where: {
				loyaltyEventId: loyaltyEvent.id,
				orgId: orgId
			}
		});

		const branding = typeof data.brandingData === 'string' ? data.brandingData : JSON.stringify(data.brandingData);
		const toSave: any = {
			emailSubject: data.emailSubject,
			fromName: data.fromName,
			branding,
			smartSend: data.smartSend,
		}
		if (data.active !== undefined) {
			toSave.active = data.active;
		}
		if (existingEventEmail) {
			await this.loyaltyEventEmailRepository.updateById(existingEventEmail.id, toSave);
		} else {
			await this.loyaltyEventEmailRepository.create({
				loyaltyEventId: loyaltyEvent.id,
				orgId,
				...toSave,
			});
		}

		return {
			statusCode: 200,
			body: data
		};
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/loyalty-email-branding', {
		responses: {
			'200': {
				description: 'Save email template branding',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async loadEmailTemplateBranding(
		@param.query.string('eventName') eventName: string,
		@injectUserOrgId() orgId: number,
	) {
		const existingEmailEvent = await this.loyaltyEventEmailRepository.find({
			where: { orgId: orgId },
			include: [ { relation: 'loyaltyEvent' } ]
		});
		if (!existingEmailEvent || existingEmailEvent.length === 0) {
			return {
				statusCode: 404,
				body: 'No email branding found'
			};
		}

		const emailEvent = existingEmailEvent.find((e: any) => e.loyaltyEvent.name === eventName);
		if (!emailEvent) {
			return {
				statusCode: 404,
				body: 'No email branding found'
			};
		}

		return emailEvent;
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/loyalty-events', {
		responses: {
			'200': {
				description: 'Save email template branding',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async loyaltyEvents(
		@injectUserOrgId() orgId: number,
	) {
		const eventsWithEmailSupport: string[] = [
			EventStreamEvent.JOINED_LOYALTY,
			EventStreamEvent.BALANCE_CHANGE,
			EventStreamEvent.REWARD_EXPIRING,
			EventStreamEvent.BIRTHDAY_REWARD_GRANTED,
			EventStreamEvent.REWARD_GRANTED,
			EventStreamEvent.VIP_TIER_UPDATED,
			// EventStreamEvent.REFERRAL_RECEIVED,
			// EventStreamEvent.REFERRAL_COMPLETED,
		];
		const allEvents: LoyaltyEvent[] = await this.loyaltyEventRepository.find({
			where: {
				name: {
					inq: eventsWithEmailSupport
				}
			},
		});

		allEvents.sort((a: LoyaltyEvent, b: LoyaltyEvent) => eventsWithEmailSupport.indexOf(a.name) - eventsWithEmailSupport.indexOf(b.name));

		const configuredEvents = await this.loyaltyEventEmailRepository.find({
			where: { orgId }
		});

		if (!configuredEvents || configuredEvents.length === 0) {
			const eventsToReturn = allEvents && allEvents.length ? allEvents.map((event) => {
				return {
					...event,
					active: false,
					configured: false,
				};
			}) : [];
			return {
				statusCode: 200,
				body: eventsToReturn
			};
		}

		return {
			statusCode: 200,
			body: allEvents.map((event) => {
				const configuredEvent = configuredEvents.find((e) => e.loyaltyEventId === event.id);
				return {
					...event,
					active: configuredEvent?.active || false,
					configured: configuredEvent?.id || false,
				};
			})
		};
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/loyalty-email-events/active-toggle', {
		responses: {
			'200': {
				description: 'Activate or deactivate loyalty event',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async toggleLoyaltyEmailEventsActive(
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						properties: {
							eventName: {type: 'string'},
							active: {type: 'boolean'},
						}
					}
				},
			},
		})
		data: { eventName: string, active: boolean },
		@injectUserOrgId() orgId: number,
	) {
		const emailEvent = await this.loyaltyEventEmailRepository.find({
			where: { orgId },
			include: [ { relation: 'loyaltyEvent' } ]
		});

		if (!emailEvent || emailEvent.length === 0) {
			return {
				statusCode: 404,
				body: `No email configured for event ${data.eventName}`
			};
		}

		const eventToActivate = emailEvent.find((e) => e.loyaltyEvent.name === data.eventName);
		if (!eventToActivate) {
			return {
				statusCode: 404,
				body: `No email configured for event ${data.eventName}`
			};
		}

		await this.loyaltyEventEmailRepository.updateById(eventToActivate.id, {
			active: data.active
		});
		return {
			statusCode: 200,
			body: {}
		}
	}
}

export const EMAIL_BRANDING_DATA = {
	logoUrl: '',
	headerImageUrl: '',
	backgroundColor: '',
	title: '',
	titleFontSize: '',
	titleTextColor: '',
	subtitle: '',
	subtitleFontSize: '',
	subtitleTextColor: '',
	buttonText: '',
	buttonTextColor: '',
	buttonBackgroundColor: '',
	buttonBorderRadius: '',
};
