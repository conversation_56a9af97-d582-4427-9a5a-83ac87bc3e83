import {
	Count,
	CountSchema,
	Filter,
	repository,
	Where,
} from '@loopback/repository';
import {
	del,
	get,
	getModelSchemaRef,
	getWhereSchemaFor,
	param,
	patch,
	post,
	requestBody,
	api
} from '@loopback/rest';
import {
	LoyaltyCampaign,
	LoyaltyEarn,
	LoyaltyProgram,
} from '../../models';
import {modelIdForGuard, OrgGuardMultiHopPropertyStrategy, skipGuardCheck} from '../../interceptors/crud-guard.interceptor';
import {guardStrategy, GuardSkipStrategy} from '../../interceptors/crud-guard.interceptor';
import {LoyaltyCampaignRepository, LoyaltyEarnRepository} from '../../repositories';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../../services';

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardMultiHopPropertyStrategy<LoyaltyEarn, LoyaltyCampaign, LoyaltyProgram>({
	repositoryClass: LoyaltyEarnRepository,
	firstHopIdPropertyName: 'loyaltyCampaignId',
	firstHopRepositoryClass: LoyaltyCampaignRepository,
	inclusionChainAfterFirstHop: {relation: 'loyaltyProgram'},
	lastHopOrgIdPropertyName: 'orgId',
}))

export class LoyaltyCampaignLoyaltyEarnController {
	constructor(
		@repository(LoyaltyCampaignRepository) protected loyaltyCampaignRepository: LoyaltyCampaignRepository,
	) { }

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/loyalty-campaigns/{id}/loyalty-earns', {
		responses: {
			'200': {
				description: 'Array of LoyaltyCampaign has many LoyaltyEarn',
				content: {
					'application/json': {
						schema: {type: 'array', items: getModelSchemaRef(LoyaltyEarn)},
					},
				},
			},
		},
	})
	async find(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyCampaign)
		id: number,
		@param.query.object('filter') filter?: Filter<LoyaltyEarn>,
	): Promise<LoyaltyEarn[]> {
		return this.loyaltyCampaignRepository.loyaltyEarns(id).find(filter);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/loyalty-campaigns/{id}/loyalty-earns', {
		responses: {
			'200': {
				description: 'LoyaltyCampaign model instance',
				content: {'application/json': {schema: getModelSchemaRef(LoyaltyEarn)}},
			},
		},
	})
	async create(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyCampaign)
		id: typeof LoyaltyCampaign.prototype.id,
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(LoyaltyEarn, {
						title: 'NewLoyaltyEarnInLoyaltyCampaign',
						exclude: ['id'],
						optional: ['loyaltyCampaignId']
					}),
				},
			},
		}) loyaltyEarn: Omit<LoyaltyEarn, 'id'>,
	): Promise<LoyaltyEarn> {
		return this.loyaltyCampaignRepository.loyaltyEarns(id).create(loyaltyEarn);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@patch('/loyalty-campaigns/{id}/loyalty-earns', {
		responses: {
			'200': {
				description: 'LoyaltyCampaign.LoyaltyEarn PATCH success count',
				content: {'application/json': {schema: CountSchema}},
			},
		},
	})
	async patch(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyCampaign)
		id: number,
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(LoyaltyEarn, {partial: true}),
				},
			},
		})
		loyaltyEarn: Partial<LoyaltyEarn>,
		@param.query.object('where', getWhereSchemaFor(LoyaltyEarn)) where?: Where<LoyaltyEarn>,
	): Promise<Count> {
		return this.loyaltyCampaignRepository.loyaltyEarns(id).patch(loyaltyEarn, where);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@patch('/loyalty-campaigns/{id}/bulk-update-loyalty-earns', {
	responses: {
		'200': {
		description: 'Bulk update of LoyaltyEarns within a LoyaltyCampaign',
		content: {'application/json': {schema: CountSchema}},
		},
	},
	})
	async bulkUpdateLoyaltyEarns(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyCampaign)
		id: number,
		@requestBody({
				content: {
				'application/json': {
					schema: {
					type: 'array',
					items: getModelSchemaRef(LoyaltyEarn, {partial: true}),
					},
				},
			},
		})
		updates: Array<Partial<LoyaltyEarn>>,
	): Promise<void> {
		await Promise.all(updates.map(update =>
			this.loyaltyCampaignRepository.loyaltyEarns(id).patch(update, {id: update.id})
		));
		return;
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@patch('/loyalty-campaigns/{id}/bulk-update-perks', {
	responses: {
		'200': {
		description: 'Bulk update of Perks within a LoyaltyCampaign',
		content: {'application/json': {schema: CountSchema}},
		},
	},
	})
	async bulkUpdatePerks(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyCampaign)
		id: number,
		@requestBody({
				content: {
				'application/json': {
					schema: {
					type: 'array',
					items: getModelSchemaRef(LoyaltyEarn, {partial: true}),
					},
				},
			},
		})
		updates: Array<Partial<LoyaltyEarn>>,
	): Promise<void> {
		await Promise.all(updates.map(update =>
			this.loyaltyCampaignRepository.staticEffects(id).patch(update, {id: update.id})
		));
		return;
	}


	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@del('/loyalty-campaigns/{id}/loyalty-earns', {
		responses: {
			'200': {
				description: 'LoyaltyCampaign.LoyaltyEarn DELETE success count',
				content: {'application/json': {schema: CountSchema}},
			},
		},
	})
	async delete(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyCampaign)
		id: number,
		@param.query.object('where', getWhereSchemaFor(LoyaltyEarn)) where?: Where<LoyaltyEarn>,
	): Promise<Count> {
		return this.loyaltyCampaignRepository.loyaltyEarns(id).delete(where);
	}
}
