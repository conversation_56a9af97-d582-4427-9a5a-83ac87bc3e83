import {
	FilterExcludingWhere,
	repository,
} from '@loopback/repository';
import {
	param,
	get,
	getModelSchemaRef,
	patch,
	put,
	del,
	requestBody,
	response,
	api,
	post
} from '@loopback/rest';
import {LoyaltyProgram, LoyaltyRewardDefinition} from '../../models';
import {LoyaltyProgramRepository, LoyaltyRewardDefinitionRepository} from '../../repositories';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../../services';
import {modelIdForGuard, OrgGuardSingleHopPropertyStrategy} from '../../interceptors/crud-guard.interceptor';
import {guardStrategy} from '../../interceptors/crud-guard.interceptor';

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardSingleHopPropertyStrategy<LoyaltyRewardDefinition, LoyaltyProgram>({
	relatedIdPropertyName: 'loyaltyProgramId',
	relatedOrgIdPropertyName: 'orgId',
	relatedRepositoryClass: LoyaltyProgramRepository,
	repositoryClass: LoyaltyRewardDefinitionRepository
}))
export class LoyaltyRewardDefinitionController {
	constructor(
		@repository(LoyaltyRewardDefinitionRepository)
		public loyaltyRewardDefinitionRepository: LoyaltyRewardDefinitionRepository,
	) { }

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/loyalty-reward-definitions/{id}')
	@response(200, {
		description: 'LoyaltyRewardDefinition model instance',
		content: {
			'application/json': {
				schema: getModelSchemaRef(LoyaltyRewardDefinition, {includeRelations: true}),
			},
		},
	})
	async findById(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyRewardDefinition)
		id: number,
		@param.filter(LoyaltyRewardDefinition, {exclude: 'where'}) filter?: FilterExcludingWhere<LoyaltyRewardDefinition>
	): Promise<LoyaltyRewardDefinition> {
		return this.loyaltyRewardDefinitionRepository.findById(id, filter);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/loyalty-programs/{id}/loyalty-reward-definitions')
	@response(204, {
		description: 'LoyaltyRewardDefinition PATCH success',
	})
	async create(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyProgram)
		id: number,
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(LoyaltyRewardDefinition, {partial: true}),
				},
			},
		})
		loyaltyRewardDefinition: LoyaltyRewardDefinition,
	): Promise<LoyaltyRewardDefinition> {
		loyaltyRewardDefinition.loyaltyProgramId = id;

		return this.loyaltyRewardDefinitionRepository.create(loyaltyRewardDefinition);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@patch('/loyalty-reward-definitions/{id}')
	@response(204, {
		description: 'LoyaltyRewardDefinition PATCH success',
	})
	async updateById(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyRewardDefinition)
		id: number,
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(LoyaltyRewardDefinition, {partial: true}),
				},
			},
		})
		loyaltyRewardDefinition: LoyaltyRewardDefinition,
	): Promise<void> {
		await this.loyaltyRewardDefinitionRepository.updateById(id, loyaltyRewardDefinition);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@put('/loyalty-reward-definitions/{id}')
	@response(204, {
		description: 'LoyaltyRewardDefinition PUT success',
	})
	async replaceById(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyRewardDefinition)
		id: number,
		@requestBody() loyaltyRewardDefinition: LoyaltyRewardDefinition,
	): Promise<void> {
		await this.loyaltyRewardDefinitionRepository.replaceById(id, loyaltyRewardDefinition);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@del('/loyalty-reward-definitions/{id}')
	@response(204, {
		description: 'LoyaltyRewardDefinition DELETE success',
	})
	async deleteById(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyRewardDefinition)
		id: number
	): Promise<void> {
		await this.loyaltyRewardDefinitionRepository.deleteById(id);
	}
}
