import {
	Count,
	CountSchema,
	Filter,
	FilterExcludingWhere,
	repository,
	Where,
} from '@loopback/repository';
import {
	post,
	param,
	get,
	getModelSchemaRef,
	patch,
	put,
	del,
	requestBody,
	response,
	api,
} from '@loopback/rest';
import {LoyaltyCurrency, LoyaltyCurrencyBalance, LoyaltyProgram} from '../../models';
import {LoyaltyCurrencyBalanceRepository, LoyaltyCurrencyRepository} from '../../repositories';
import {guardStrategy, modelForGuard, modelIdForGuard, OrgGuardMultiHopPropertyStrategy} from '../../interceptors';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../../services';

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardMultiHopPropertyStrategy<LoyaltyCurrencyBalance, LoyaltyCurrency, LoyaltyProgram>({
	repositoryClass: LoyaltyCurrencyBalanceRepository,
	firstHopIdPropertyName: 'loyaltyCurrencyId',
	firstHopRepositoryClass: LoyaltyCurrencyRepository,
	inclusionChainAfterFirstHop: {relation: 'loyaltyProgram'},
	lastHopOrgIdPropertyName: 'orgId',
}))
export class LoyaltyCurrencyBalanceController {
	constructor(
		@repository(LoyaltyCurrencyBalanceRepository)
		public loyaltyCurrencyBalanceRepository: LoyaltyCurrencyBalanceRepository,
	) { }

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/loyalty-currency-balances/{id}')
	@response(200, {
		description: 'LoyaltyCurrencyBalance model instance',
		content: {
			'application/json': {
				schema: getModelSchemaRef(LoyaltyCurrencyBalance, {includeRelations: true}),
			},
		},
	})
	async findById(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyCurrencyBalance)
		id: number,
		@param.filter(LoyaltyCurrencyBalance, {exclude: 'where'}) filter?: FilterExcludingWhere<LoyaltyCurrencyBalance>
	): Promise<LoyaltyCurrencyBalance> {
		return this.loyaltyCurrencyBalanceRepository.findById(id, filter);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@patch('/loyalty-currency-balances/{id}')
	@response(204, {
		description: 'LoyaltyCurrencyBalance PATCH success',
	})
	async updateById(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyCurrencyBalance)
		id: number,
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(LoyaltyCurrencyBalance, {partial: true}),
				},
			},
		})
		loyaltyCurrencyBalance: LoyaltyCurrencyBalance,
	): Promise<void> {
		await this.loyaltyCurrencyBalanceRepository.updateById(id, loyaltyCurrencyBalance);
	}
}
