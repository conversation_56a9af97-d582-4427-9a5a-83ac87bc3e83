import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
  api
} from '@loopback/rest';
import {
  LoyaltyProgram,
  LoyaltyRewardDefinition,
} from '../../models';
import {LoyaltyProgramRepository} from '../../repositories';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../../services';
import {modelIdForGuard, OrgGuardPropertyStrategy, skipGuardCheck} from '../../interceptors/crud-guard.interceptor';
import {guardStrategy, GuardSkipStrategy} from '../../interceptors/crud-guard.interceptor';

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardPropertyStrategy<LoyaltyProgram>({
	orgIdModelPropertyName: 'orgId',
	repositoryClass: LoyaltyProgramRepository
}))
export class LoyaltyProgramLoyaltyRewardDefinitionController {
  constructor(
    @repository(LoyaltyProgramRepository) protected loyaltyProgramRepository: LoyaltyProgramRepository,
  ) { }

  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
  })
  @get('/loyalty-programs/{id}/loyalty-reward-definitions', {
    responses: {
      '200': {
        description: 'Array of LoyaltyProgram has many LoyaltyRewardDefinition',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LoyaltyRewardDefinition)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id')
	@modelIdForGuard(LoyaltyProgram)
	id: number,
    @param.query.object('filter') filter?: Filter<LoyaltyRewardDefinition>,
  ): Promise<LoyaltyRewardDefinition[]> {
    return this.loyaltyProgramRepository.loyaltyRewardDefinitions(id).find(filter);
  }

  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
  })
  @post('/loyalty-programs/{id}/loyalty-reward-definitions', {
    responses: {
      '200': {
        description: 'LoyaltyProgram model instance',
        content: {'application/json': {schema: getModelSchemaRef(LoyaltyRewardDefinition)}},
      },
    },
  })
  async create(
    @param.path.number('id')
	@modelIdForGuard(LoyaltyProgram)
	id: typeof LoyaltyProgram.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LoyaltyRewardDefinition, {
            title: 'NewLoyaltyRewardDefinitionInLoyaltyProgram',
            exclude: ['id'],
            optional: ['loyaltyProgramId']
          }),
        },
      },
    }) loyaltyRewardDefinition: Omit<LoyaltyRewardDefinition, 'id'>,
  ): Promise<LoyaltyRewardDefinition> {
    return this.loyaltyProgramRepository.loyaltyRewardDefinitions(id).create(loyaltyRewardDefinition);
  }

  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
  })
  @patch('/loyalty-programs/{id}/loyalty-reward-definitions', {
    responses: {
      '200': {
        description: 'LoyaltyProgram.LoyaltyRewardDefinition PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id')
	@modelIdForGuard(LoyaltyProgram)
	id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LoyaltyRewardDefinition, {partial: true}),
        },
      },
    })
    loyaltyRewardDefinition: Partial<LoyaltyRewardDefinition>,
    @param.query.object('where', getWhereSchemaFor(LoyaltyRewardDefinition)) where?: Where<LoyaltyRewardDefinition>,
  ): Promise<Count> {
    return this.loyaltyProgramRepository.loyaltyRewardDefinitions(id).patch(loyaltyRewardDefinition, where);
  }

  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
  })
  @del('/loyalty-programs/{id}/loyalty-reward-definitions', {
    responses: {
      '200': {
        description: 'LoyaltyProgram.LoyaltyRewardDefinition DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id')
	@modelIdForGuard(LoyaltyProgram)
	id: number,
    @param.query.object('where', getWhereSchemaFor(LoyaltyRewardDefinition)) where?: Where<LoyaltyRewardDefinition>,
  ): Promise<Count> {
    return this.loyaltyProgramRepository.loyaltyRewardDefinitions(id).delete(where);
  }
}
