import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
  api
} from '@loopback/rest';
import {
  LoyaltyCampaign,
  LoyaltyProgram,
} from '../../models';
import {LoyaltyCampaignRepository, LoyaltyProgramRepository} from '../../repositories';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../../services';
import {OrgGuardSingleHopPropertyStrategy, modelIdForGuard, skipGuardCheck} from '../../interceptors/crud-guard.interceptor';
import {guardStrategy, GuardSkipStrategy} from '../../interceptors/crud-guard.interceptor';

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardSingleHopPropertyStrategy<LoyaltyCampaign, LoyaltyProgram>({
	relatedIdPropertyName: 'loyaltyProgramId',
	relatedOrgIdPropertyName: 'orgId',
	relatedRepositoryClass: LoyaltyProgramRepository,
	repositoryClass: LoyaltyCampaignRepository
}))
export class LoyaltyCampaignLoyaltyProgramController {
  constructor(
    @repository(LoyaltyCampaignRepository)
    public loyaltyCampaignRepository: LoyaltyCampaignRepository,
  ) { }

  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
  })
  @get('/loyalty-campaigns/{id}/loyalty-program', {
    responses: {
      '200': {
        description: 'LoyaltyProgram belonging to LoyaltyCampaign',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LoyaltyProgram)},
          },
        },
      },
    },
  })
  async getLoyaltyProgram(
    @param.path.number('id')
	@modelIdForGuard(LoyaltyCampaign)
	id: typeof LoyaltyCampaign.prototype.id,
  ): Promise<LoyaltyProgram> {
    return this.loyaltyCampaignRepository.loyaltyProgram(id);
  }
}
