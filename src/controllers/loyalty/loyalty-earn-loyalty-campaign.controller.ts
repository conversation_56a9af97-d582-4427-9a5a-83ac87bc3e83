import {
	repository,
} from '@loopback/repository';
import {
	param,
	get,
	getModelSchemaRef,
	api
} from '@loopback/rest';
import {
	LoyaltyEarn,
	LoyaltyCampaign,
	LoyaltyProgram,
} from '../../models';
import {LoyaltyCampaignRepository, LoyaltyEarnRepository} from '../../repositories';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../../services';
import {OrgGuardMultiHopPropertyStrategy, modelIdForGuard} from '../../interceptors/crud-guard.interceptor';
import {guardStrategy} from '../../interceptors/crud-guard.interceptor';

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardMultiHopPropertyStrategy<LoyaltyEarn, LoyaltyCampaign, LoyaltyProgram>({
	repositoryClass: LoyaltyEarnRepository,
	firstHopIdPropertyName: 'loyaltyCampaignId',
	firstHopRepositoryClass: LoyaltyCampaignRepository,
	inclusionChainAfterFirstHop: {relation: "loyaltyProgram"},
	lastHopOrgIdPropertyName: 'orgId',
}))
export class LoyaltyEarnLoyaltyCampaignController {
	constructor(
		@repository(LoyaltyEarnRepository)
		public loyaltyEarnRepository: LoyaltyEarnRepository,
	) { }

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/loyalty-earns/{id}/loyalty-campaign', {
		responses: {
			'200': {
				description: 'LoyaltyCampaign belonging to LoyaltyEarn',
				content: {
					'application/json': {
						schema: {type: 'array', items: getModelSchemaRef(LoyaltyCampaign)},
					},
				},
			},
		},
	})
	async getLoyaltyCampaign(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyEarn)
		id: typeof LoyaltyEarn.prototype.id,
	): Promise<LoyaltyCampaign> {
		return this.loyaltyEarnRepository.loyaltyCampaign(id);
	}
}
