import {repository} from '@loopback/repository';
import {HttpErrors, RequestContext, api, get, param, post, requestBody} from '@loopback/rest';
import {authenticate} from '@loopback/authentication';
import {GuardSkipStrategy, guardStrategy, skipGuardCheck, injectRaleonUserOrgId, injectRaleonUserId, injectUserOrgId} from '../../interceptors';
import {CurrencyRepository, InventoryCouponRepository, LoyaltyCampaignRepository, LoyaltyCurrencyBalanceRepository, LoyaltyCurrencyRepository, LoyaltyCurrencyTxLogRepository, LoyaltyProgramRepository, LoyaltyRewardLogRepository, OrganizationRepository, RaleonUserEarnLogRepository, RaleonUserIdentityRepository, RewardCouponRepository, TranslationStringRepository} from '../../repositories';
import {service, inject} from '@loopback/core';
import {LoyaltyBalanceManager} from '../../services/loyalty/loyalty-balance-manager.service';
import {Currency, CurrencyRelations, CurrencyWithRelations, LoyaltyProgramWithRelations, RaleonUserIdentity, RaleonUserIdentityWithRelations} from '../../models';
import {ShopifyApiInvoker} from '../../services/shopify/shopify-api-invoker.service';
import {convertCurrencyPlaceholdersToValues, LoyaltyRedemptionController} from '../../controllers';
import {DevDbDataSource} from '../../datasources';
import {basicAuthorization, TranslationService} from '../../services';
import {String} from 'aws-sdk/clients/signer';
import {LoyaltyDetailsService} from '../../services/loyalty/loyalty-details.service';
import {LoyaltyDetailsAndBirthdayBonusService} from '../../services/loyalty/loyalty-details-and-birthday-bonus.service';
import {authorize} from '@loopback/authorization';
import {FeatureService} from '../../services/feature.service';
import NodeCache from 'node-cache';

@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class LoyaltyDetailsController {
	// Add translation cache to prevent repeated DB hits
	private translationCache = new NodeCache({
		stdTTL: 300, // 5 minutes
		checkperiod: 60, // Check for expired keys every 60 seconds
		maxKeys: 1000, // Limit to 1000 translation cache entries
		deleteOnExpire: true,
		useClones: false // Better performance for our use case
	});

	constructor(
		@repository(LoyaltyProgramRepository) public loyaltyProgramRepository: LoyaltyProgramRepository,
		@repository(LoyaltyCampaignRepository) public loyaltyCampaignRepository: LoyaltyCampaignRepository,
		@repository(LoyaltyCurrencyBalanceRepository) public loyaltyCurrencyBalanceRepository: LoyaltyCurrencyBalanceRepository,
		@service(LoyaltyBalanceManager) public loyaltyBalanceManager: LoyaltyBalanceManager,
		@repository(LoyaltyCurrencyTxLogRepository) public loyaltyCurrencyTxLogRepository: LoyaltyCurrencyTxLogRepository,
		@repository(OrganizationRepository) private organizationRepository: OrganizationRepository,
		@repository(TranslationStringRepository) private translationStringRepository: TranslationStringRepository,
		@service(LoyaltyDetailsAndBirthdayBonusService) private loyaltyDetailsAndBirthdayBonusService: LoyaltyDetailsAndBirthdayBonusService,
		@repository(LoyaltyCurrencyRepository) private loyaltyCurrencyRepository: LoyaltyCurrencyRepository,
		@repository(CurrencyRepository) private currencyRepository: CurrencyRepository,
		@repository(InventoryCouponRepository) private inventoryCouponRepository: InventoryCouponRepository,
		@service(ShopifyApiInvoker) protected shopifyApiInvoker: ShopifyApiInvoker,
		// @service(FeatureService) protected featureService: FeatureService,
	) { 
		// NodeCache handles cleanup automatically via checkperiod and deleteOnExpire
	}

	// Clean up on controller shutdown
	destroy() {
		this.translationCache.close();
	}

	@skipGuardCheck()
	@get('/launcher-details', {
		responses: {
			'200': {
				description: 'Returns org level launcher details for the active program',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async getLauncherDetails(
		@param.query.string('orgId') orgId: number,
	): Promise<any> {
		let program = await this.loyaltyProgramRepository.findOne({
			where: {
				orgId: orgId,
			}
		});

		//If null, return true for isLauncherActive (this means it wasn't disabled in the past and is now enabled)
		let isLauncherActive = program?.launcherActive;
		if (isLauncherActive === null) {
			isLauncherActive = true;
		}
		return {
			isLauncherActive: isLauncherActive,
			isReferralsActive: program?.referralsActive
		};
	}


	@skipGuardCheck()
	@get('/loyalty-preview', {
		responses: {
			'200': {
				description: 'Will Return Loyalty Preview for an org',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async getLoyaltyPreview(
		@param.query.string('orgId') orgId: number,
	): Promise<any> {
		const orgCurrency = await this.currencyRepository.findOne({
			where: {organizationId: orgId},
			include: [{relation: 'supportedCurrencies'}]
		});


		return {
			...(await this.loyaltyDetailsAndBirthdayBonusService.getLoyaltyDetailsAndGrantBirthdayBonusIfApplicable(orgId, 0, true)),
			previewCurrency: (orgCurrency as any)?.supportedCurrencies?.name
		};
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['raleon-admin'],
		voters: [basicAuthorization],
	})
	@get('/loyalty-details-admin', {
		responses: {
			'200': {
				description: 'Will Return Loyalty Details for any user',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async getAdmin(
		@param.query.string('orgId') orgId: number,
		@param.query.string('userId') userId: number,
	): Promise<any> {
		return this.loyaltyDetailsAndBirthdayBonusService.getLoyaltyDetailsAndGrantBirthdayBonusIfApplicable(orgId, userId, false, false, true);
	}

	@skipGuardCheck()
	@get('/guest-earns', {
		responses: {
			'200': {
				description: 'Will Return Loyalty Earns for a guest',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async getGuestEarns(
		@param.query.string('orgId') orgId: number,
		@param.query.boolean('preview') preview: boolean = false,
	): Promise<any> {
		let programs = await this.loyaltyProgramRepository.find({
			where: {
				orgId: orgId,
			},
		});


		let campaigns: any[] = [];
		for (let program of programs) {
			if (program.active || preview) {
				let whereFilter: any = {
					loyaltyProgramId: program.id,
				};
				if (!preview) {
					whereFilter.active = true;
				}
				let earnScope: any = {
					include: ['earnEffects','earnConditions'],
					where: {
						and: [
							{recommendationState: {inq: ['NONE', 'APPROVED_RECOMMENDATION']}},
							{
								or: [
									{earnConditions: null},
									{
										and: [
											{'earnConditions.type': {neq: 'referrer-bonus'}}
										]
									}
								]
							}
						]
					}
				};
				if (!preview) {
					earnScope.where.active = true;
				}
				let programCampaigns = await this.loyaltyCampaignRepository.find({
					where: whereFilter,
					include: [
						{
							relation: 'loyaltyEarns',
							scope: earnScope,
						},
					]
				});

				// Filter out referral-based earns from each campaign
				programCampaigns = programCampaigns.map(campaign => {
					if (campaign.loyaltyEarns) {
						// Filter out any earn that has referral-related conditions
						campaign.loyaltyEarns = campaign.loyaltyEarns.filter(earn => {
							// Check if earn has any conditions
							if (!earn.earnConditions) {
								return true; // Keep earns without conditions
							}

							// Filter out earns that have referral-related conditions
							return !earn.earnConditions.some((condition: any) =>
								condition.type === 'referred-customer-bonus' ||
								condition.type === 'referrer-bonus'
							);
						});
					}
					return campaign;
				});

				campaigns = campaigns.concat(programCampaigns);
			}
		}


		return {
			campaigns
		};
	}

	@skipGuardCheck()
	@get('/guest-earns-and-translations-by-domain', {
		responses: {
			'200': {
				description: 'Will Return Loyalty Earns for a guest',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async getGuestEarnsByDomain(
		@param.query.string('externalDomain') externalDomain: string,
		@param.query.boolean('preview') preview: boolean = false,
	): Promise<any> {
		const org = await this.organizationRepository.findOne({
			where: {
				externalDomain: externalDomain!,
			}
		});

		const orgId = org?.id;
		if (!orgId) {
			throw new HttpErrors.NotFound('Organization not found');
		}

		const [earns, translations] = await Promise.all([
			this.getGuestEarns(orgId, preview),
			this.getAllTranslations(orgId)
		]);

		return {
			earns,
			translations
		}
	}

	@skipGuardCheck()
	@authenticate('shopify-customer-access-token')
	@get('/loyalty-details', {
		responses: {
			'200': {
				description: 'Will Return Loyalty Details for a logged in user',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async get(
		@injectRaleonUserOrgId() orgId: number,
		@injectRaleonUserId() raleonUserId: number,
	): Promise<any> {
		return this.loyaltyDetailsAndBirthdayBonusService.getLoyaltyDetailsAndGrantBirthdayBonusIfApplicable(orgId, raleonUserId, false, undefined, undefined, true);
	}

	@skipGuardCheck()
	@authenticate('api-key')
	@post('/grant-birthday-rewards')
	private async grantBirthdayRewards() {
		await this.loyaltyDetailsAndBirthdayBonusService.grantBirthdayRewardsAndSendEmails();
	}

	@skipGuardCheck()
	@get('/points-earned-from-order/{orderId}', {
		responses: {
			'200': {
				description: 'Will Return points earned from order for a logged in user',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async getPointsFromOrder(
		@param.path.string('orderId') orderId: string
	): Promise<any> {
		const logs = await this.loyaltyCurrencyTxLogRepository.find({
			where: {
				orderId
			}
		});

		if (!logs || !logs.length) {
			return null;
		}

		return logs.reduce((acc, log) => acc + (log.amount || 0), 0);
	}




	@skipGuardCheck()
	@authenticate('shopify-customer-access-token')
	@get('/loyalty-details-and-translations', {
		responses: {
			'200': {
				description: 'Will Return Loyalty Details for a logged in user',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async getDetailsAndTranslations(
		@injectRaleonUserOrgId() orgId: number,
		@injectRaleonUserId() raleonUserId: number,
	): Promise<any> {
		return {
			...(await this.get(orgId, raleonUserId)),
			translations: await this.getAllTranslations(orgId)
		}
	}


	static calculateTimeToExpiration(expirationDate: string, currentDate: Date) {
		const expiration = new Date(expirationDate);
		const timeDiff = expiration.getTime() - currentDate.getTime();

		if (timeDiff <= 0) {
			return {timeVal: 0, timeLabel: 'expired'};
		}

		const minutes = Math.floor(timeDiff / (1000 * 60));
		const hours = Math.floor(timeDiff / (1000 * 60 * 60));
		const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));

		if (days > 0) {
			return {timeVal: days, timeLabel: days === 1 ? 'day' : 'days'};
		} else if (hours > 0) {
			return {timeVal: hours, timeLabel: hours === 1 ? 'hour' : 'hours'};
		} else {
			return {timeVal: minutes, timeLabel: minutes === 1 ? 'minute' : 'minutes'};
		}
	}

	static groupEarnConditions(earnConditions: any[]) {
		let earnConditionGroups: {[key: string]: any[]} = {};
		earnConditions?.forEach((condition: any) => {
			if (!earnConditionGroups[condition.type]) {
				earnConditionGroups[condition.type] = [];
			}
			earnConditionGroups[condition.type].push(condition);
		});

		return earnConditionGroups;
	}

	static getDefaultCurrencyValues(): [string, string, string] {
		return ['$', '', 'Dollar'];
	}

	static getCurrencyValues(orgCurrency?: any): [string, string, string] {
		let [defaultPreFix, defaultPostFix, defaultDollarName] = LoyaltyDetailsController.getDefaultCurrencyValues();
		if (orgCurrency?.prefix) {
			defaultPreFix = orgCurrency.prefix;
		}
		if (orgCurrency?.postfix) {
			defaultPostFix = orgCurrency.postfix;
		}
		if (orgCurrency?.name) {
			defaultDollarName = orgCurrency.name;
		}
		return [defaultPreFix, defaultPostFix, defaultDollarName];
	}

	static generateReferralRewardText(earnEffect: any, orgId: number, language: string, pointsName: string = 'points'): string[] {
		// let [defaultPreFix, defaultPostFix, defaultDollarName] = LoyaltyDetailsController.getCurrencyValues(orgCurrency);
		const earnType = earnEffect.type;
		const rewardType = earnEffect.loyaltyRewardDefinition?.rewardCoupon?.amountType;
		let valueString = rewardType === 'dollars-off-coupon' ?
			`{{currency_value:${earnEffect.loyaltyRewardDefinition?.rewardCoupon?.amount.toLocaleString()}}}` :
			`${earnEffect.loyaltyRewardDefinition?.rewardCoupon?.amount.toLocaleString()}%`;

		let name = TranslationService.getCachedTranslation(
			language,
			'referral_bonus_reward_name',
			orgId,
			{value: valueString}
		);

		let description = TranslationService.getCachedTranslation(
			language,
			'referral_bonus_reward_description',
			orgId,
			{value: valueString}
		);

		if (earnType == 'points') {
			valueString = `${earnEffect.loyaltyRewardDefinition?.rewardCoupon?.amount.toLocaleString() || earnEffect.points} ${pointsName}`;

			name = valueString;
			description = TranslationService.getCachedTranslation(
				language,
				'referral_bonus_reward_points_description',
				orgId,
				{value: valueString}
			);
		}

		if (earnEffect.name) {
			name = earnEffect.name;
		}

		return [name, description];
	}

	static generatePillStrings(wayToEarn: any, orgId: number, language: string, orgCurrency: Currency & CurrencyWithRelations): string[] {
		// let [defaultPreFix, defaultPostFix, defaultDollarName] = LoyaltyDetailsController.getCurrencyValues(orgCurrency);
		//points-per-dollar, points, dollars-off-coupon, percent-discount
		let pillStrings: string[] = [];
		if (!wayToEarn.earnEffects) return pillStrings;
		for (let effect of wayToEarn.earnEffects) {
			switch (effect.type) {
				case 'points-per-dollar':
					const currencyValue = convertCurrencyPlaceholdersToValues('{{currency_value:1}}', orgCurrency);
					pillStrings.push(TranslationService.getCachedTranslation(
						language,
						'reward_pill_points_per_dollar',
						orgId,
						{
							value: effect.pointsPerDollar.toLocaleString(),
							prefix: '',
							postfix: currencyValue,
							pointsAbbreviated: TranslationService.getCachedTranslation(language, 'points_abbreviated', orgId, {})
						}
					));
					//TODO: update this
					break;
				case 'points':
					pillStrings.push(TranslationService.getCachedTranslation(
						language,
						'reward_pill_points',
						orgId,
						{
							value: effect.points.toLocaleString(),
							pointsAbbreviated: TranslationService.getCachedTranslation(language, 'points_abbreviated', orgId, {})
						}
					));
					break;
				case 'dollars-off-coupon':
					const currencyVal = convertCurrencyPlaceholdersToValues(`{{currency_value:${effect.loyaltyRewardDefinition?.rewardCoupon?.amount}}}`, orgCurrency);
					pillStrings.push(TranslationService.getCachedTranslation(
						language,
						'reward_pill_dollars_off_coupon',
						orgId,
						{prefix: '', value: currencyVal, postfix: ''}
					));
					break;
				case 'percent-discount':
					pillStrings.push(TranslationService.getCachedTranslation(
						language,
						'reward_pill_percent_discount',
						orgId,
						{value: effect.loyaltyRewardDefinition?.rewardCoupon?.amount.toLocaleString()}
					));
					break;
				case 'free-shipping':
					pillStrings.push(TranslationService.getCachedTranslation(
						language,
						'reward_pill_free_shipping',
						orgId,
						{}
					));
					break;
				case 'free-product':
					pillStrings.push(TranslationService.getCachedTranslation(
						language,
						'reward_pill_free_product',
						orgId,
						{}
					));
					break;
				case 'percent-off-product':
					pillStrings.push(TranslationService.getCachedTranslation(
						language,
						'reward_pill_percent_off_product',
						orgId,
						{}
					));
					break;
				case 'dollar-off-product':
					pillStrings.push(TranslationService.getCachedTranslation(
						language,
						'reward_pill_dollar_off_product',
						orgId,
						{}
					));
					break;
				case 'giveaway-entry':
					const count = effect.loyaltyRewardDefinition?.rewardCoupon?.amount || 1;
					const vars = count > 1 ? {count: count.toLocaleString()} : {}
					pillStrings.push(TranslationService.getCachedTranslation(
						language,
						count > 1 ? 'reward_pill_giveaway_entry_multiple' : 'reward_pill_giveaway_entry',
						orgId,
						vars
					));
					break;

			}
		}
		return pillStrings;
	}

	static generateConditionStrings(campaign: any, earnConditionGroups: any, orgId: number, language: string, orgCurrency: Currency & CurrencyRelations, orgDomain?: string): string[] {
		let conditionStrings: string[] = [];

		for (let type in earnConditionGroups) {
			let conditions = earnConditionGroups[type];
			switch (type) {
				case 'dollar-spent':
					let translatedString = TranslationService.getCachedTranslation(
						language,
						'condition_dollar_spent',
						orgId,
						{
							pointsName: TranslationService.getCachedTranslation(language, 'points', orgId, {}),
						}
					);
					if (orgCurrency) {
						translatedString = convertCurrencyPlaceholdersToValues(translatedString, orgCurrency!)
					}
					conditionStrings.push(translatedString);
					break;
				case 'welcome-bonus':
					conditionStrings.push(TranslationService.getCachedTranslation(
						language,
						'condition_welcome_bonus',
						orgId,
						{}
					));
					break;
				case 'birthday-bonus':
					conditionStrings.push(TranslationService.getCachedTranslation(
						language,
						'condition_birthday_bonus',
						orgId,
						{}
					));
					break;
				case 'nth-purchase':
					let counts = conditions
						.filter((c: any) => c.variable === 'purchaseCount')
						.map((c: any) => c.amount);
					const text = counts?.length && counts?.[0] == 1 ? 'condition_nth_purchase_single' : 'condition_nth_purchase';
					conditionStrings.push(TranslationService.getCachedTranslation(
						language,
						text,
						orgId,
						{counts: counts.join(',')}
					));
					break;
				case 'timed-purchase':
					let orderCounts = conditions
						.filter((c: any) => c.variable === 'purchaseCount')
						.map((c: any) => c.amount);

					const today = new Date();
					let daysUntilEnd = 0;
					if (campaign.enddate) {
						const endDate = new Date(campaign.enddate);
						const timeDiff = endDate.getTime() - today.getTime();
						daysUntilEnd = Math.ceil(timeDiff / (1000 * 3600 * 24));
					}

					let daysLeftString = ` in ${daysUntilEnd} days`;
					const countText = orderCounts?.length && orderCounts?.[0] == 1 ? 'condition_timed_purchase_single' : 'condition_timed_purchase';
					conditionStrings.push(TranslationService.getCachedTranslation(
						language,
						countText,
						orgId,
						{orderCounts: orderCounts.join(','), daysUntilEnd: daysUntilEnd.toString()}
					));
					break;
				case 'follow-on-instagram':
					const val = conditions[0].textValue;
					const handle = val && val.startsWith('@') ? val.substring(1) : val;
					conditionStrings.push(TranslationService.getCachedTranslation(
						language,
						'condition_follow_on_instagram',
						orgId,
						{handle: handle}
					));
					break;
				case 'follow-on-tiktok':
					const ttValue = conditions[0].textValue;
					const ttHandle = ttValue && ttValue.startsWith('@') ? ttValue : `@${ttValue}`;
					conditionStrings.push(TranslationService.getCachedTranslation(
						language,
						'condition_follow_on_tiktok',
						orgId,
						{handle: ttHandle}
					));
					break;
				case 'follow-on-facebook':
					const fbValue = conditions[0].textValue;
					const fbHandle = fbValue && fbValue.startsWith('@') ? fbValue.substring(1) : fbValue;
					conditionStrings.push(TranslationService.getCachedTranslation(
						language,
						'condition_follow_on_facebook',
						orgId,
						{handle: fbHandle}
					));
					break;
				case 'follow-facebook-group':
					const fbGroupValue = conditions[0].textValue;
					const fbGroupHandle = fbGroupValue && fbGroupValue.startsWith('@') ? fbGroupValue.substring(1) : fbGroupValue;
					conditionStrings.push(TranslationService.getCachedTranslation(
						language,
						'condition_follow_facebook_group',
						orgId,
						{handle: fbGroupHandle}
					));
					break;
				case 'follow-on-youtube':
					const ytValue = conditions[0].textValue;
					const ytHandle = ytValue && ytValue.startsWith('@') ? ytValue : `@${ytValue}`;
					conditionStrings.push(TranslationService.getCachedTranslation(
						language,
						'condition_follow_on_youtube',
						orgId,
						{handle: ytHandle}
					));
					break;
				case 'follow-on-custom':
					const websiteValue = conditions[0].textValue;
					conditionStrings.push(TranslationService.getCachedTranslation(
						language,
						'condition_follow_on_custom',
						orgId,
						{url: websiteValue}
					));
					break;
				case 'product-review':
					conditionStrings.push(TranslationService.getCachedTranslation(
						language,
						'condition_product_review',
						orgId,
						{}
					));
					break;
				case 'product-photo-review':
					conditionStrings.push(TranslationService.getCachedTranslation(
						language,
						'condition_product_photo_review',
						orgId,
						{}
					));
					break;
				case 'specific-product-purchase':
					conditionStrings.push(TranslationService.getCachedTranslation(
						language,
						'condition_specific_product_purchase',
						orgId,
						{
							domain: orgDomain,
							handle: conditions[0].handle
						}
					));
					break;
				case 'collection-product-purchase':
					const purchaseCount = conditions.find((c: any) => c.variable === 'purchaseCount')?.amount || 1;
					conditionStrings.push(TranslationService.getCachedTranslation(
						language,
						'condition_collection_purchase',
						orgId,
						{
							count: purchaseCount,
							domain: orgDomain,
							handle: conditions[0].handle
						}
					));
					break;
				case 'referrer-bonus':
					conditionStrings.push(TranslationService.getCachedTranslation(
						language,
						'condition_referrer_bonus',
						orgId,
						{}
					));
					break;
				case 'milestone-subscription-purchase':
					let subCounts = conditions
						.filter((c: any) => c.variable === 'purchaseCount')
						.map((c: any) => c.amount);
					const subscriptionText = subCounts?.length && subCounts?.[0] == 1 ? 'condition_subscription_purchase' : 'condition_subscription_purchase_multiple';
					conditionStrings.push(TranslationService.getCachedTranslation(
						language,
						subscriptionText,
						orgId,
						{value: subCounts.join(',')}
					));
					break;
				case 'first-subscription-purchase':
					conditionStrings.push(TranslationService.getCachedTranslation(
						language,
						'condition_subscription_purchase',
						orgId,
						{}
					));
					break;
				case 'subscription-purchase':
					conditionStrings.push(TranslationService.getCachedTranslation(
						language,
						'condition_subscription_purchase',
						orgId,
						{}
					));
					break;
				case 'click-to-redeem':
					conditionStrings.push(TranslationService.getCachedTranslation(
						language,
						'condition_click_to_redeem',
						orgId,
						{}
					));
					break;
				case 'auto-redeem':
					conditionStrings.push(TranslationService.getCachedTranslation(
						language,
						'condition_auto_redeem',
						orgId,
						{}
					));
					break;
			}
		}

		return conditionStrings;
	}

	static evaluatePurchaseConditions(
		campaign: any,
		orders: any[],
		earnConditions: any[],
		conditionKey: string,
		orgId: number,
		language: string,
	): {shouldRemoveEarn: boolean, progress: string, daysLeft: string} {

		const nonCancelledOrders = orders.filter((order: any) => !order.cancelled_at);
		let totalOrderCount = nonCancelledOrders.length;
		let progress = "";
		let shouldRemoveEarn = false;
		let daysLeft = "";

		const purchaseCountCondition = earnConditions.find(cond => cond.variable === "purchaseCount");

		if (purchaseCountCondition && conditionKey === 'nth-purchase') {
			let remainingPurchases = purchaseCountCondition.amount - totalOrderCount;
			shouldRemoveEarn = remainingPurchases <= 0;
			progress = remainingPurchases > 0 ?
				TranslationService.getCachedTranslation(
					language,
					remainingPurchases > 1 ? 'condition_nth_purchase_progress' : 'condition_nth_purchase_progress_single',
					orgId,
					{counts: `${remainingPurchases}`}
				) : TranslationService.getCachedTranslation(
					language,
					'condition_nth_purchase_progress_complete',
					orgId,
					{}
				);
		}

		if (conditionKey === 'timed-purchase' && campaign.startdate && campaign.enddate) {
			const currentDate = new Date();
			const startDate = new Date(campaign.startdate);
			const endDate = new Date(campaign.enddate);
			const timeDiff = endDate.getTime() - currentDate.getTime();
			const daysDifference = Math.ceil(timeDiff / (1000 * 3600 * 24));
			daysLeft = `${daysDifference} day${daysDifference > 1 ? 's' : ''} left`;
			let filteredOrders = orders.filter(order => {
				const orderDate = new Date(order.created_at);
				return orderDate >= startDate && orderDate <= endDate;
			});

			totalOrderCount = filteredOrders.length;
			let remainingPurchases = purchaseCountCondition.amount - totalOrderCount;
			shouldRemoveEarn = remainingPurchases <= 0;
			progress = remainingPurchases > 0 ?
				TranslationService.getCachedTranslation(
					language,
					remainingPurchases > 1 ? 'condition_nth_purchase_progress' : 'condition_nth_purchase_progress_single',
					orgId,
					{counts: `${remainingPurchases}`}
				) : TranslationService.getCachedTranslation(
					language,
					'condition_nth_purchase_progress_complete',
					orgId,
					{}
				);
		}

		return {
			shouldRemoveEarn,
			progress,
			daysLeft
		};
	}

	static getOrdinal(n: number) {
		const s = ["th", "st", "nd", "rd"];
		const v = n % 100;
		return n + (s[(v - 20) % 10] || s[v] || s[0]);
	}

	static generateConditionStringsShort(earnConditionGroups: any, orgId: number, language: string): string[] {
		let conditionStrings: string[] = [];

		for (let type in earnConditionGroups) {
			let conditions = earnConditionGroups[type];
			switch (type) {
				case 'dollar-spent':
					// Logic for dollar-spent type
					conditionStrings.push(``);
					break;
				case 'welcome-bonus':
					// Logic for welcome-bonus type
					conditionStrings.push(``);
					break;
				case 'referral-bonus':
					// Logic for welcome-bonus type
					conditionStrings.push(``);
					break;
				case 'birthday-bonus':
					// Logic for welcome-bonus type
					conditionStrings.push(``);
					break;
				case 'nth-purchase':
					let counts: number[] = conditions
						.filter((c: any) => c.variable === 'purchaseCount')
						.map((c: any) => c.amount);

					counts.forEach(count => {
						conditionStrings.push(TranslationService.getCachedTranslation(
							language,
							'condition_short_nth_purchase',
							orgId,
							{count: LoyaltyDetailsController.getOrdinal(count)}
						));
					});
					break;
				case 'timed-purchase':
					let countsTimedBased: number[] = conditions
						.filter((c: any) => c.variable === 'purchaseCount')
						.map((c: any) => c.amount);

					countsTimedBased.forEach(count => {
						conditionStrings.push(TranslationService.getCachedTranslation(
							language,
							'condition_short_nth_purchase',
							orgId,
							{count: LoyaltyDetailsController.getOrdinal(count)}
						));
					});
					break;
				case 'follow-on-instagram':
					const val = conditions[0].textValue;
					const handle = val && val.startsWith('@') ? val.substring(1) : val;
					conditionStrings.push(TranslationService.getCachedTranslation(
						language,
						'condition_short_follow_on_instagram',
						orgId,
						{handle: handle}
					));
					break;
				case 'follow-on-tiktok':
					const ttValue = conditions[0].textValue;
					const ttHandle = ttValue && ttValue.startsWith('@') ? ttValue : `@${ttValue}`;
					conditionStrings.push(TranslationService.getCachedTranslation(
						language,
						'condition_short_follow_on_tiktok',
						orgId,
						{handle: ttHandle}
					));
					break;
				case 'follow-on-facebook':
					const fbValue = conditions[0].textValue;
					const fbHandle = fbValue && fbValue.startsWith('@') ? fbValue.substring(1) : fbValue;
					conditionStrings.push(TranslationService.getCachedTranslation(
						language,
						'condition_short_follow_on_facebook',
						orgId,
						{handle: fbHandle}
					));
					break;
				case 'follow-facebook-group':
					const fbGroupValue = conditions[0].textValue;
					const fbGroupHandle = fbGroupValue && fbGroupValue.startsWith('@') ? fbGroupValue.substring(1) : fbGroupValue;
					conditionStrings.push(TranslationService.getCachedTranslation(
						language,
						'condition_short_follow_on_facebook',
						orgId,
						{handle: fbGroupHandle}
					));
					break;
				case 'follow-on-youtube':
					const ytValue = conditions[0].textValue;
					const ytHandle = ytValue && ytValue.startsWith('@') ? ytValue.substring(1) : ytValue;
					conditionStrings.push(TranslationService.getCachedTranslation(
						language,
						'condition_short_follow_on_youtube',
						orgId,
						{handle: ytHandle}
					));
					break;
				case 'follow-on-custom':
					const websiteValue = conditions[0].textValue;
					conditionStrings.push(TranslationService.getCachedTranslation(
						language,
						'condition_short_follow_on_custom',
						orgId,
						{url: websiteValue}
					));
					break;
				case 'specific-product-purchase':
					conditionStrings.push(TranslationService.getCachedTranslation(
						language,
						'condition_specific_product_short_purchase',
						orgId,
						{}
					));
					break;
				case 'collection-product-purchase':
					const purchaseCount = conditions.find((c: any) => c.variable === 'purchaseCount')?.amount || 1;
					conditionStrings.push(TranslationService.getCachedTranslation(
						language,
						'condition_collection_short_purchase',
						orgId,
						{count: purchaseCount}
					));
					break;
				case 'click-to-redeem':
					conditionStrings.push(TranslationService.getCachedTranslation(
						language,
						'condition_click_to_redeem',
						orgId,
						{}
					));
					break;
				case 'auto-redeem':
					conditionStrings.push(TranslationService.getCachedTranslation(
						language,
						'condition_auto_redeem',
						orgId,
						{}
					));
					break;
			}
		}

		return conditionStrings;
	}

	static replaceDollarWithCurrency(text: string, orgCurrency?: any): string {
		if (!orgCurrency || !orgCurrency.supportedCurrencies) {
			return text;
		}

		// Regular expression to match a dollar sign followed by any number
		const dollarRegex = /\$(\d+)/g;

		if (dollarRegex.test(text)) {
			// Replace only the parts that match the pattern
			return text.replace(dollarRegex, (match, number) => {
				return `${orgCurrency.supportedCurrencies.prefix || ''}${number}${orgCurrency.supportedCurrencies.postfix || ''}`;
			});
		} else {
			// If no matching pattern, return the original text
			return text;
		}
	}

	static generateShopItemString(shopItem: any, rewardDefinitionItem: any, orgCurrency?: any, isStaticEffect = false): string {
		//I'd like the format to be
		//1,000 points = $10 off coupon (2 restrictions)
		let [defaultPreFix, defaultPostFix, defaultDollarName] = LoyaltyDetailsController.getCurrencyValues(orgCurrency);
		if (!shopItem) return '';
		let pointsString = isStaticEffect ? '' : `${shopItem.price?.toLocaleString()} points = `;
		let rewardString = '';
		if (rewardDefinitionItem?.rewardCoupon?.amount) {
			if (rewardDefinitionItem?.rewardCoupon?.amountType === 'dollars-off-coupon')
				rewardString = `${defaultPreFix}${rewardDefinitionItem?.rewardCoupon?.amount.toLocaleString()}${defaultPostFix} off coupon ${isStaticEffect ? '' : `(expires in ${rewardDefinitionItem?.rewardCoupon?.expiresInDays.toLocaleString()} days)`}`;
			else if (rewardDefinitionItem?.rewardCoupon?.amountType === 'percent-discount')
				rewardString = `${rewardDefinitionItem?.rewardCoupon?.amount.toLocaleString()}% off coupon ${isStaticEffect ? '' : `(expires in ${rewardDefinitionItem?.rewardCoupon?.expiresInDays.toLocaleString()} days)`}`;
			else if (rewardDefinitionItem?.rewardCoupon?.amountType === 'points-multiplier')
				rewardString = `${rewardDefinitionItem?.rewardCoupon?.amount.toLocaleString()}X Points Multiplier`;
			else if (rewardDefinitionItem?.rewardCoupon?.amountType === 'dollar-off-product')
				rewardString = `${defaultPreFix}${rewardDefinitionItem?.rewardCoupon?.amount.toLocaleString()}${defaultPostFix} off ${rewardDefinitionItem?.rewardCoupon?.externalName} ${isStaticEffect ? '' : `(expires in ${rewardDefinitionItem?.rewardCoupon?.expiresInDays.toLocaleString()} days)`}`;
			else if (rewardDefinitionItem?.rewardCoupon?.amountType === 'percent-off-product')
				rewardString = `${rewardDefinitionItem?.rewardCoupon?.amount.toLocaleString()}% off ${rewardDefinitionItem?.rewardCoupon?.externalName} ${isStaticEffect ? '' : `(expires in ${rewardDefinitionItem?.rewardCoupon?.expiresInDays.toLocaleString()} days)`}`;

			if (rewardDefinitionItem?.rewardCoupon?.minimumOrderTotal) {
				rewardString += `, (${defaultPreFix}${rewardDefinitionItem?.rewardCoupon?.minimumOrderTotal}${defaultPostFix} Min Order)`;
			}
		}

		if (rewardDefinitionItem?.rewardCoupon?.amountType === 'free-shipping') {
			rewardString = `Free Shipping ${isStaticEffect ? '' : `(expires in ${rewardDefinitionItem?.rewardCoupon?.expiresInDays.toLocaleString()} days)`}`;
		}
		if (rewardDefinitionItem?.rewardCoupon?.amountType === 'free-product') {
			rewardString = `Free Product ${isStaticEffect ? '' : `(expires in ${rewardDefinitionItem?.rewardCoupon?.expiresInDays.toLocaleString()} days)`}`;
		}
		if (rewardDefinitionItem?.rewardCoupon?.amountType === 'dollar-off-product') {
			rewardString = `${defaultPreFix}${rewardDefinitionItem?.rewardCoupon?.amount}${defaultPostFix} off Specific product (expires in ${rewardDefinitionItem?.rewardCoupon?.expiresInDays.toLocaleString()} days)`
		}
		if (rewardDefinitionItem?.rewardCoupon?.amountType === 'percent-off-product') {
			rewardString = `${rewardDefinitionItem?.rewardCoupon?.amount}% off Specific product (expires in ${rewardDefinitionItem?.rewardCoupon?.expiresInDays.toLocaleString()} days)`;
		}

		return `${pointsString}${rewardString}`;
	}

	@skipGuardCheck()
	@authenticate('shopify-customer-access-token')
	@get('/loyalty-log', {
		responses: {
			'200': {
				description: 'Will Return Loyalty Log for a logged in user',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async getTransactionLog(
		@injectRaleonUserOrgId() orgId: number,
		@injectRaleonUserId() raleonUserId: number
	): Promise<any> {
		let programs = await this.loyaltyProgramRepository.find({
			where: {
				orgId: orgId
			},
			include: [
				{
					relation: 'loyaltyCurrencies',
				}
			]
		});

		let allCurrencies: number[] = [];
		for (let program of programs) {
			for (let currency of program.loyaltyCurrencies) {
				allCurrencies.push(currency.id!);
			}
		}

		let userBalances = await this.loyaltyCurrencyBalanceRepository.find({
			where: {
				raleonUserId: raleonUserId,
				loyaltyCurrencyId: {inq: allCurrencies}
			}
		});

		const userBalanceIds = userBalances.map(balance => balance.id);

		let currencyTxLogs = await this.loyaltyCurrencyTxLogRepository.find({
			where: {
				loyaltyCurrencyBalanceId: {inq: userBalanceIds}
			}
		});

		return {
			currencyTxLogs: currencyTxLogs
		};
	}

	@skipGuardCheck()
	@authenticate('shopify-customer-access-token')
	@get('/exchange-rate/{currencyCode}/{countryCode}', {
		responses: {
			'200': {
				description: 'Will Return exchange rate and market adjustments for a currency',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async getMarketAdjustments(
		@param.path.string('currencyCode') currencyCode: string,
		@param.path.string('countryCode') countryCode: string,
		@injectRaleonUserOrgId() orgId: number,
	): Promise<any> {
		const result = await this.shopifyApiInvoker.invokeAdminApi(orgId, `/market-info?currencyCode=${currencyCode}&countryCode=${countryCode}`, 'GET');

		if (result?.error || !result) {
			throw new HttpErrors.NotFound('Exchange rate not found. ' + result?.error);
		}

		return result;
	}

	@skipGuardCheck()
	@authenticate('shopify-customer-access-token')
	@get('/loyalty-translations', {
		responses: {
			'200': {
				description: 'Will Return translations for a logged out user',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async getLoyaltyTranslations(
		@injectRaleonUserOrgId() orgId: number,
		@param.query.string('language') language: string = '',
		// @param.query.boolean('includeAll') includeAll?: boolean = false
	): Promise<any> {
		return this.getAllTranslations(orgId, language);
	}

	@skipGuardCheck()
	@get('/loyalty-translations-by-domain/{domain}', {
		responses: {
			'200': {
				description: 'Will Return translations for a logged out user',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async getOrgDomainTranslations(
		@param.path.string('domain') domain: string,
		@param.query.string('language') language: string = '',
		// @param.query.boolean('includeAll') includeAll?: boolean = false
	): Promise<any> {
		const org = await this.organizationRepository.findOne({
			where: {
				externalDomain: domain
			}
		});
		if (!org) throw new HttpErrors.NotFound('Organization not found');


		return this.getAllTranslations(org.id!, language);
	}


	@skipGuardCheck()
	@get('/loyalty-translations/{orgId}', {
		responses: {
			'200': {
				description: 'Will Return translations for a logged in user',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async getOrgTranslations(
		@param.path.number('orgId') orgId: number,
		@param.query.string('language') language: string = '',
		// @param.query.boolean('includeAll') includeAll?: boolean = false
	): Promise<any> {
		return this.getAllTranslations(orgId, language);
	}

	async getAllTranslations(
		orgId: number,
		language: string = '',
		// includeAll?: boolean = false
	): Promise<any> {
		// Create cache key
		const cacheKey = `translations_${orgId}_${language}`;
		
		// Check cache first - NodeCache handles expiry automatically
		const cached = this.translationCache.get<any>(cacheKey);
		if (cached) {
			console.log('GET ALL TRANSLATIONS (cached)');
			return cached;
		}

		console.log('GET ALL TRANSLATIONS');
		const org = await this.organizationRepository.findById(orgId);
		const orgLanguage = org?.language || 'en';
		language = language || orgLanguage;

		const baseTranslations = await this.translationStringRepository.find({
			where: {
				language,
				orgId: null as any
				// loyaltyUi: includeAll ? {inq: [true, false]} : true
			}
		});
		const orgTranslations = await this.translationStringRepository.find({
			where: {
				language: orgLanguage,
				orgId: orgId
				// loyaltyUi: includeAll ? {inq: [true, false]} : true
			}
		});

		const translations = baseTranslations.concat(orgTranslations);


		if (translations.length) {
			// filter translations to choose orgId translation if present, else choose default
			const keys = Array.from(new Set(translations.map(t => t.key)));
			const translationsFiltered = keys.map(key => {
				const translation = translations.find(t => t.key === key && t.orgId === orgId);
				return translation || translations.find(t => t.key === key);
			});
			console.log('END GET ALL TRANSLATIONS');
			const result = {
				isOrgLanguage: language === orgLanguage,
				language,
				translations: translationsFiltered
			};
			
			// Cache the result - NodeCache handles TTL automatically
			this.translationCache.set(cacheKey, result);
			
			return result;
		}

		if (language.includes('-')) {
			const baseFallbackTranslations = await this.translationStringRepository.find({
				where: {
					language: language.split('-')[0],
					orgId: null as any
					// orgId: { inq: [orgId, 0, null as any, undefined] }
					// loyaltyUi: includeAll ? {inq: [true, false]} : true
				}
			});
			const orgFallbackTranslations = await this.translationStringRepository.find({
				where: {
					language: orgLanguage.split('-')[0],
					orgId: orgId
					// loyaltyUi: includeAll ? {inq: [true, false]} : true
				}
			});
			const fallbackTranslations = baseFallbackTranslations.concat(orgFallbackTranslations);

			if (fallbackTranslations.length) {

				// filter translations to choose orgId translation if present, else choose default
				const keys = Array.from(new Set(fallbackTranslations.map(t => t.key)));
				const fallbackTranslationsFiltered = keys.map(key => {
					const translation = fallbackTranslations.find(t => t.key === key && t.orgId === orgId);
					return translation || fallbackTranslations.find(t => t.key === key);
				});
				console.log('END GET ALL TRANSLATIONS');
				const result = {
					isOrgLanguage: language === orgLanguage,
					language: language.split('-')[0],
					translations: fallbackTranslationsFiltered
				};
				
				// Cache the result - NodeCache handles TTL automatically
				this.translationCache.set(cacheKey, result);
				
				return result;
			}
		}

		if (language != orgLanguage) {
			const fallbackTranslations = await this.translationStringRepository.find({
				where: {
					language: orgLanguage,
					// loyaltyUi: includeAll ? {inq: [true, false]} : true
				}
			});

			if (fallbackTranslations.length) {
				console.log('END GET ALL TRANSLATIONS');
				const result = {
					isOrgLanguage: true,
					language: orgLanguage,
					translations: fallbackTranslations
				};
				
				// Cache the result - NodeCache handles TTL automatically
				this.translationCache.set(cacheKey, result);
				
				return result;
			}
		}

		return HttpErrors.NotFound('No translations found');
	}


	//unathenticated because user may not be logged in
	@skipGuardCheck()
	@get('/loyalty-enabled', {
		responses: {
			'200': {
				description: 'Returns whether or not loyalty is enabled in the org',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async getLoyaltyEnabled(
		@param.query.number('orgId') orgId: number,
	): Promise<any> {
		// if (await this.featureService.isFeatureAvailable('loyalty-app', orgId) === false) {
		// 	return {
		// 		enabled: false
		// 	};
		// }

		let programs = await this.loyaltyProgramRepository.find({
			where: {
				orgId: orgId,
				active: true
			},
		});

		if (programs && programs.length > 0) {
			programs = programs.sort((a: any, b: any) => {
				return a.id - b.id;
			});
		}

		return {
			enabled: programs && programs.length > 0 && programs[0].active
		};
	}
}
