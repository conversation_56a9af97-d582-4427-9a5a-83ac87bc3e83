import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
  api
} from '@loopback/rest';
import {
  LoyaltyCampaign,
  LoyaltyProgram,
  LoyaltyRedemptionShopItem,
  LoyaltyStaticEffect,
} from '../../models';
import {LoyaltyCampaignRepository, LoyaltyProgramRepository} from '../../repositories';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../../services';
import {modelIdForGuard, OrgGuardMultiHopPropertyStrategy, OrgGuardSingleHopPropertyStrategy, skipGuardCheck} from '../../interceptors/crud-guard.interceptor';
import {guardStrategy, GuardSkipStrategy} from '../../interceptors/crud-guard.interceptor';

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardSingleHopPropertyStrategy<LoyaltyCampaign, LoyaltyProgram>({
	relatedIdPropertyName: 'loyaltyProgramId',
	relatedOrgIdPropertyName: 'orgId',
	relatedRepositoryClass: LoyaltyProgramRepository,
	repositoryClass: LoyaltyCampaignRepository
}))
export class LoyaltyCampaignLoyaltyRedemptionShopItemController {
  constructor(
    @repository(LoyaltyCampaignRepository) protected loyaltyCampaignRepository: LoyaltyCampaignRepository,
  ) { }

  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
  })
  @get('/loyalty-campaigns/{id}/loyalty-redemption-shop-items', {
    responses: {
      '200': {
        description: 'Array of LoyaltyCampaign has many LoyaltyRedemptionShopItem',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LoyaltyRedemptionShopItem)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id')
	@modelIdForGuard(LoyaltyCampaign)
	id: number,
    @param.query.object('filter') filter?: Filter<LoyaltyRedemptionShopItem>,
  ): Promise<LoyaltyRedemptionShopItem[]> {
    let shopItems =  await this.loyaltyCampaignRepository.loyaltyRedemptionShopItems(id).find(filter);
	//Filter out any isRecommendation or ignoreRecommendation items
	shopItems = shopItems.filter((item: any) => {
		return !item.isRecommendation && !item.ignoreRecommendation;
	});
	return shopItems;
  }

  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
  })
  @post('/loyalty-campaigns/{id}/loyalty-redemption-shop-items', {
    responses: {
      '200': {
        description: 'LoyaltyCampaign model instance',
        content: {'application/json': {schema: getModelSchemaRef(LoyaltyRedemptionShopItem)}},
      },
    },
  })
  async create(
    @param.path.number('id')
	@modelIdForGuard(LoyaltyCampaign)
	id: typeof LoyaltyCampaign.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LoyaltyRedemptionShopItem, {
            title: 'NewLoyaltyRedemptionShopItemInLoyaltyCampaign',
            exclude: ['id'],
            optional: ['loyaltyCampaignId']
          }),
        },
      },
    }) loyaltyRedemptionShopItem: Omit<LoyaltyRedemptionShopItem, 'id'>,
  ): Promise<LoyaltyRedemptionShopItem> {
    return this.loyaltyCampaignRepository.loyaltyRedemptionShopItems(id).create(loyaltyRedemptionShopItem);
  }

  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
  })
  @patch('/loyalty-campaigns/{id}/loyalty-redemption-shop-items', {
    responses: {
      '200': {
        description: 'LoyaltyCampaign.LoyaltyRedemptionShopItem PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id')
	@modelIdForGuard(LoyaltyCampaign)
	id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LoyaltyRedemptionShopItem, {partial: true}),
        },
      },
    })
    loyaltyRedemptionShopItem: Partial<LoyaltyRedemptionShopItem>,
    @param.query.object('where', getWhereSchemaFor(LoyaltyRedemptionShopItem)) where?: Where<LoyaltyRedemptionShopItem>,
  ): Promise<Count> {
    return this.loyaltyCampaignRepository.loyaltyRedemptionShopItems(id).patch(loyaltyRedemptionShopItem, where);
  }

  @authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@patch('/loyalty-campaigns/{id}/bulk-update-loyalty-redemption-shop-items', {
	responses: {
		'200': {
		description: 'Bulk update of LoyaltyRedemptionShopItems within a LoyaltyCampaign',
		content: {'application/json': {schema: CountSchema}},
		},
	},
	})
	async bulkUpdateLoyaltyRedemptionShopItems(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyCampaign)
		id: number,
		@requestBody({
				content: {
				'application/json': {
					schema: {
					type: 'array',
					items: getModelSchemaRef(LoyaltyRedemptionShopItem, {partial: true}),
					},
				},
			},
		})
		updates: Array<Partial<LoyaltyRedemptionShopItem>>,
	): Promise<void> {
		await Promise.all(updates.map(update =>
			this.loyaltyCampaignRepository.loyaltyRedemptionShopItems(id).patch(update, {id: update.id})
		));
		return;
	}

  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
  })
  @del('/loyalty-campaigns/{id}/loyalty-redemption-shop-items', {
    responses: {
      '200': {
        description: 'LoyaltyCampaign.LoyaltyRedemptionShopItem DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id')
	@modelIdForGuard(LoyaltyCampaign)
	id: number,
    @param.query.object('where', getWhereSchemaFor(LoyaltyRedemptionShopItem)) where?: Where<LoyaltyRedemptionShopItem>,
  ): Promise<Count> {
    return this.loyaltyCampaignRepository.loyaltyRedemptionShopItems(id).delete(where);
  }


  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
  })
  @patch('/loyalty-campaigns/{id}/static-effects', {
    responses: {
      '200': {
        description: 'LoyaltyCampaign.StaticEffect PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patchStaticEffect(
    @param.path.number('id')
	@modelIdForGuard(LoyaltyCampaign)
	id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LoyaltyStaticEffect, {partial: true}),
        },
      },
    })
    staticEffect: Partial<LoyaltyStaticEffect>,
    @param.query.object('where', getWhereSchemaFor(LoyaltyStaticEffect)) where?: Where<LoyaltyStaticEffect>,
  ): Promise<Count> {
    return this.loyaltyCampaignRepository.staticEffects(id).patch(staticEffect, where);
  }
}
