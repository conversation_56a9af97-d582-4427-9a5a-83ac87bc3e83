import {
	Count,
	CountSchema,
	Filter,
	repository,
	Where,
} from '@loopback/repository';
import {
	api,
	del,
	get,
	getModelSchemaRef,
	getWhereSchemaFor,
	param,
	patch,
	post,
	requestBody,
} from '@loopback/rest';
import {
	LoyaltyCurrency,
	LoyaltyCurrencyBalance,
	LoyaltyCurrencyTxLog,
	LoyaltyProgram,
} from '../../models';
import {LoyaltyCurrencyBalanceRepository, LoyaltyCurrencyRepository} from '../../repositories';
import {guardStrategy, modelIdForGuard, OrgGuardMultiHopPropertyStrategy} from '../../interceptors';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../../services';


@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardMultiHopPropertyStrategy<LoyaltyCurrencyBalance, LoyaltyCurrency, LoyaltyProgram>({
	repositoryClass: LoyaltyCurrencyBalanceRepository,
	firstHopIdPropertyName: 'loyaltyCurrencyId',
	firstHopRepositoryClass: LoyaltyCurrencyRepository,
	inclusionChainAfterFirstHop: {relation: 'loyaltyProgram'},
	lastHopOrgIdPropertyName: 'orgId',
}))
export class LoyaltyCurrencyBalanceLoyaltyCurrencyTxLogController {
	constructor(
		@repository(LoyaltyCurrencyBalanceRepository) protected loyaltyCurrencyBalanceRepository: LoyaltyCurrencyBalanceRepository,
	) { }

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/loyalty-currency-balances/{id}/loyalty-currency-tx-logs', {
		responses: {
			'200': {
				description: 'Array of LoyaltyCurrencyBalance has many LoyaltyCurrencyTxLog',
				content: {
					'application/json': {
						schema: {type: 'array', items: getModelSchemaRef(LoyaltyCurrencyTxLog)},
					},
				},
			},
		},
	})
	async find(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyCurrencyBalance)
		id: number,
		@param.query.object('filter') filter?: Filter<LoyaltyCurrencyTxLog>,
	): Promise<LoyaltyCurrencyTxLog[]> {
		return this.loyaltyCurrencyBalanceRepository.loyaltyCurrencyTxLogs(id).find(filter);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/loyalty-currency-balances/{id}/loyalty-currency-tx-logs', {
		responses: {
			'200': {
				description: 'LoyaltyCurrencyBalance model instance',
				content: {'application/json': {schema: getModelSchemaRef(LoyaltyCurrencyTxLog)}},
			},
		},
	})
	async create(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyCurrencyBalance)
		id: typeof LoyaltyCurrencyBalance.prototype.id,
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(LoyaltyCurrencyTxLog, {
						title: 'NewLoyaltyCurrencyTxLogInLoyaltyCurrencyBalance',
						exclude: ['id'],
						optional: ['loyaltyCurrencyBalanceId']
					}),
				},
			},
		}) loyaltyCurrencyTxLog: Omit<LoyaltyCurrencyTxLog, 'id'>,
	): Promise<LoyaltyCurrencyTxLog> {
		return this.loyaltyCurrencyBalanceRepository.loyaltyCurrencyTxLogs(id).create(loyaltyCurrencyTxLog);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@patch('/loyalty-currency-balances/{id}/loyalty-currency-tx-logs', {
		responses: {
			'200': {
				description: 'LoyaltyCurrencyBalance.LoyaltyCurrencyTxLog PATCH success count',
				content: {'application/json': {schema: CountSchema}},
			},
		},
	})
	async patch(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyCurrencyBalance)
		id: number,
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(LoyaltyCurrencyTxLog, {partial: true}),
				},
			},
		})
		loyaltyCurrencyTxLog: Partial<LoyaltyCurrencyTxLog>,
		@param.query.object('where', getWhereSchemaFor(LoyaltyCurrencyTxLog)) where?: Where<LoyaltyCurrencyTxLog>,
	): Promise<Count> {
		return this.loyaltyCurrencyBalanceRepository.loyaltyCurrencyTxLogs(id).patch(loyaltyCurrencyTxLog, where);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@del('/loyalty-currency-balances/{id}/loyalty-currency-tx-logs', {
		responses: {
			'200': {
				description: 'LoyaltyCurrencyBalance.LoyaltyCurrencyTxLog DELETE success count',
				content: {'application/json': {schema: CountSchema}},
			},
		},
	})
	async delete(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyCurrencyBalance)
		id: number,
		@param.query.object('where', getWhereSchemaFor(LoyaltyCurrencyTxLog)) where?: Where<LoyaltyCurrencyTxLog>,
	): Promise<Count> {
		return this.loyaltyCurrencyBalanceRepository.loyaltyCurrencyTxLogs(id).delete(where);
	}
}
