import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
  api
} from '@loopback/rest';
import {
  LoyaltyEarn,
  EarnEffect,
  LoyaltyCampaign,
  LoyaltyProgram,
} from '../../models';
import {LoyaltyCampaignRepository, LoyaltyEarnRepository} from '../../repositories';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../../services';
import {modelForGuard, modelIdForGuard, OrgGuardMultiHopPropertyStrategy, skipGuardCheck} from '../../interceptors/crud-guard.interceptor';
import {guardStrategy, GuardSkipStrategy} from '../../interceptors/crud-guard.interceptor';

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardMultiHopPropertyStrategy<LoyaltyEarn, LoyaltyCampaign, LoyaltyProgram>({
	repositoryClass: LoyaltyEarnRepository,
	firstHopIdPropertyName: 'loyaltyCampaignId',
	firstHopRepositoryClass: LoyaltyCampaignRepository,
	inclusionChainAfterFirstHop: {relation: "loyaltyProgram"},
	lastHopOrgIdPropertyName: 'orgId',
}))
export class LoyaltyEarnEarnEffectController {
  constructor(
    @repository(LoyaltyEarnRepository) protected loyaltyEarnRepository: LoyaltyEarnRepository,
  ) { }

  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
  })
  @get('/loyalty-earns/{id}/earn-effects', {
    responses: {
      '200': {
        description: 'Array of LoyaltyEarn has many EarnEffect',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(EarnEffect)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id')
	@modelIdForGuard(LoyaltyEarn)
	id: number,
    @param.query.object('filter') filter?: Filter<EarnEffect>,
  ): Promise<EarnEffect[]> {
    return this.loyaltyEarnRepository.earnEffects(id).find(filter);
  }

  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
  })
  @post('/loyalty-earns/{id}/earn-effects', {
    responses: {
      '200': {
        description: 'LoyaltyEarn model instance',
        content: {'application/json': {schema: getModelSchemaRef(EarnEffect)}},
      },
    },
  })
  async create(
    @param.path.number('id')
	@modelIdForGuard(LoyaltyEarn)
	id: typeof LoyaltyEarn.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(EarnEffect, {
            title: 'NewEarnEffectInLoyaltyEarn',
            exclude: ['id'],
            optional: ['loyaltyEarnId']
          }),
        },
      },
    }) earnEffect: Omit<EarnEffect, 'id'>,
  ): Promise<EarnEffect> {
    return this.loyaltyEarnRepository.earnEffects(id).create(earnEffect);
  }

  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
  })
  @patch('/loyalty-earns/{id}/earn-effects', {
    responses: {
      '200': {
        description: 'LoyaltyEarn.EarnEffect PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id')
	@modelIdForGuard(LoyaltyEarn)
	id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(EarnEffect, {partial: true}),
        },
      },
    })
    earnEffect: Partial<EarnEffect>,
    @param.query.object('where', getWhereSchemaFor(EarnEffect)) where?: Where<EarnEffect>,
  ): Promise<Count> {
    return this.loyaltyEarnRepository.earnEffects(id).patch(earnEffect, where);
  }

  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
  })
  @del('/loyalty-earns/{id}/earn-effects', {
    responses: {
      '200': {
        description: 'LoyaltyEarn.EarnEffect DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id')
	@modelIdForGuard(LoyaltyEarn)
	id: number,
    @param.query.object('where', getWhereSchemaFor(EarnEffect)) where?: Where<EarnEffect>,
  ): Promise<Count> {
    return this.loyaltyEarnRepository.earnEffects(id).delete(where);
  }
}
