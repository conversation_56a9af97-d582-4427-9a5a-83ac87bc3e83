import {HttpErrors, Response, RestBindings, api, get, getModelSchemaRef, param, patch, post, requestBody, response} from '@loopback/rest';
import {OrgGuardPropertyStrategy, guardStrategy, injectRaleonUserId, injectRaleonUserOrgId, injectUserOrgId, restrictReadsWithGuard, skipGuardCheck} from '../../interceptors';
import {InventoryCoupon, InventoryCouponWithRelations} from '../../models/loyalty/inventory-coupon.model';
import {CurrencyRepository, InventoryCouponRepository, OrganizationRepository, RaleonUserIdentityRepository} from '../../repositories';

import {repository} from '@loopback/repository';
import {WhereBuilder} from '@loopback/repository';
import {authenticate} from '@loopback/authentication';
import {inject, service} from '@loopback/core';
import {ShopifyPostgresService} from '../../services/shopify-postgres.service';
import {CurrencyWithRelations, REWARD_COUPON_AMOUNT_TYPES} from '../../models';
import {DiscountCodeService} from '../../services/shopify/discount-code.service';
import {LoyaltyRewardLogService} from '../../services/loyalty/loyalty-reward-log.service';
import {DevDbDataSource} from '../../datasources/dev-db.datasource';
import {TranslationService} from '../../services';
import {convertCurrencyPlaceholdersToValues} from '../admin-ui.controller';

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardPropertyStrategy<InventoryCoupon>({
	orgIdModelPropertyName: 'orgId',
	repositoryClass: InventoryCouponRepository
}))
export class InventoryCouponController {
	constructor(
		@repository(InventoryCouponRepository)
		private inventoryCouponRepository: InventoryCouponRepository,
		@repository(OrganizationRepository)
		private organizationRepository: OrganizationRepository,
		@repository(RaleonUserIdentityRepository)
		private raleonUserIdentityRepository: RaleonUserIdentityRepository,
		@repository(CurrencyRepository)
		private currencyRepository: CurrencyRepository,
		@service(ShopifyPostgresService)
		private shopifyPgHandler: ShopifyPostgresService,
		@service(DiscountCodeService)
		private discountCodeService: DiscountCodeService,
		@service(LoyaltyRewardLogService)
		private loyaltyRewardLogService: LoyaltyRewardLogService,
		@inject('datasources.dev_db') private devDbDataSource: DevDbDataSource
	) { }

	@skipGuardCheck()
	@authenticate('shopify-customer-access-token')
	@get('/inventory-coupons', {
		responses: {
			'200': {
				description: 'Array of InventoryCoupon model instances',
				content: {
					'application/json': {
						schema: {
							type: 'array',
							items: getModelSchemaRef(InventoryCoupon, {includeRelations: true}),
						},
					},
				},
			},
		},
	})
	async getUserInventoryCoupons(
		@injectRaleonUserId() raleonUserId: number,
		@injectRaleonUserOrgId() raleonUserOrgId: number
	): Promise<any[]> {
		const currentDate = new Date();

		const whereCondition = new WhereBuilder()
			.eq('raleonUserId', raleonUserId)
			.eq('orgId', raleonUserOrgId)
			.eq('used', false)
			.gt('expiration', currentDate.toISOString()) // Greater than current date
			.build();

		const data = await this.inventoryCouponRepository.find({
			where: whereCondition,
			include: [{relation: 'rewardCoupon'}]
		});

		return data.map(coupon => ({
			...coupon,
			...(coupon.expiration ? this.calculateTimeToExpiration(coupon.expiration, currentDate) : {})
		}));
	}

	calculateTimeToExpiration(expirationDate: string, currentDate: Date) {
		const expiration = new Date(expirationDate);
		const timeDiff = expiration.getTime() - currentDate.getTime();

		if (timeDiff <= 0) {
			return {timeVal: 0, timeLabel: 'expired'};
		}

		const minutes = Math.floor(timeDiff / (1000 * 60));
		const hours = Math.floor(timeDiff / (1000 * 60 * 60));
		const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));

		if (days > 0) {
			return {timeVal: days, timeLabel: days === 1 ? 'day' : 'days'};
		} else if (hours > 0) {
			return {timeVal: hours, timeLabel: hours === 1 ? 'hour' : 'hours'};
		} else {
			return {timeVal: minutes, timeLabel: minutes === 1 ? 'minute' : 'minutes'};
		}
	}

	@skipGuardCheck()
	@post('/inventory-coupons/uuid/{uuid}')
	@response(200, {
		description: 'Discount Code for Coupon',
		content: {
			'text/plain': {
				schema: {
					type: 'string',
				},
			},
		},
	})
	async redeemCouponUUID(
		@param.path.string('uuid') id: string,
	): Promise<any> {
		console.log('UUID: redeemCouponUUID', id);
		const coupons: InventoryCouponWithRelations[] =
			await this.inventoryCouponRepository.find({
				where: {
					uuid: id,
				},
				include: [
					{relation: 'rewardCoupon'},
					{relation: 'loyaltyRewardDefinition'},
				],
			});
		const coupon = coupons[0];
		console.log('UUID: coupon', coupon);
		const loyaltyCampaignId = coupon.loyaltyRewardDefinition?.loyaltyCampaignId;
		console.log('UUID: loyaltyCampaignId', loyaltyCampaignId);

		if (!coupon) throw new HttpErrors.NotFound('Coupon not found');

		const used = coupon.used || coupon.usedDate && coupon.usedDate < new Date().toISOString();
		const expired = coupon.expiration && coupon.expiration < new Date().toISOString();
		if (used || expired) throw new HttpErrors.BadRequest('Coupon already used');

		const org = await this.organizationRepository.findById(coupon.orgId);
		if (!org || !org.externalDomain) throw new HttpErrors.NotFound('Organization not found');

		const customer = await this.raleonUserIdentityRepository.findOne({
			where: {
				raleonUserId: coupon.raleonUserId,
				identityType: 'customer_id'
			}
		});

		console.log('UUID: customer', customer);

		if (coupon && coupon.externalId) {
			await this.discountCodeService.deleteDiscountCode(coupon, org.id!);
		}

		const isCustomerOffer = coupon && coupon.loyaltyRewardDefinition.customerOfferId;

		let discountCode;
		if (coupon.rewardCoupon?.amountType === 'free-shipping') {
			discountCode = await this.discountCodeService.createFreeShippingCode(
				coupon,
				customer!.identityValue,
				org.externalDomain,
				org.id!,
				loyaltyCampaignId!,
				true
			);
		} else {
			const couponPrefix = isCustomerOffer ?
				`OFFER-${coupon.loyaltyRewardDefinition.customerOfferId!}` :
				loyaltyCampaignId!;

			discountCode = await this.discountCodeService.createDiscountCode(
				coupon,
				customer!.identityValue,
				org.externalDomain,
				org.id!,
				couponPrefix,
				true
			);
		}

		if (!discountCode) throw new HttpErrors.InternalServerError('Failed to create discount code');

		await this.inventoryCouponRepository.updateById(coupon.id, { externalId: discountCode, uuid: id });
		return discountCode;
	}



	@skipGuardCheck()
	@authenticate('shopify-customer-access-token')
	@post('/inventory-coupons/{id}')
	@response(200, {
		description: 'Discount Code for Coupon',
		content: {
			'text/plain': {
				schema: {
					type: 'string',
				},
			},
		},
	})
	async redeemCoupon(
		@param.path.number('id') id: number,
		@injectRaleonUserId() raleonUserId: number,
		@injectRaleonUserOrgId() raleonUserOrgId: number,
	): Promise<any> {
		const coupons: InventoryCouponWithRelations[] =
			await this.inventoryCouponRepository.find({
				where: {
					raleonUserId: raleonUserId,
					orgId: raleonUserOrgId,
					id,
				},
				include: [
					{relation: 'rewardCoupon'},
					{relation: 'loyaltyRewardDefinition'},
				],
			});
		const coupon = coupons[0];
		const loyaltyCampaignId = coupon.loyaltyRewardDefinition?.loyaltyCampaignId;

		if (!coupon) throw new HttpErrors.NotFound('Coupon not found');

		const used = coupon.used || coupon.usedDate && coupon.usedDate < new Date().toISOString();
		const expired = coupon.expiration && coupon.expiration < new Date().toISOString();
		if (used || expired) throw new HttpErrors.BadRequest('Coupon already used');

		const org = await this.organizationRepository.findById(raleonUserOrgId);
		if (!org || !org.externalDomain) throw new HttpErrors.NotFound('Organization not found');

		const customer = await this.raleonUserIdentityRepository.findOne({
			where: {
				raleonUserId: raleonUserId,
				identityType: 'customer_id'
			}
		});

		// Use existing coupon code if exists
		if (coupon && coupon.externalId) return coupon.externalId;

		const isCustomerOffer = coupon && coupon.loyaltyRewardDefinition.customerOfferId;

		let discountCode;
		if (coupon.rewardCoupon?.amountType === 'free-shipping') {
			discountCode = await this.discountCodeService.createFreeShippingCode(
				coupon,
				customer!.identityValue,
				org.externalDomain,
				org.id!,
				loyaltyCampaignId!
			);
		} else {
			const couponPrefix = isCustomerOffer ?
				`OFFER-${coupon.loyaltyRewardDefinition.customerOfferId!}` :
				loyaltyCampaignId!;

			discountCode = await this.discountCodeService.createDiscountCode(
				coupon,
				customer!.identityValue,
				org.externalDomain,
				org.id!,
				couponPrefix,
			);
		}

		if (!discountCode) throw new HttpErrors.InternalServerError('Failed to create discount code');

		await this.inventoryCouponRepository.updateById(id, {externalId: discountCode});
		return discountCode;
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@post('/inventory-coupons/used')
	async updateCouponAndLogRedemption(
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						properties: {
							discountCode: {type: 'string'},
							amount: {type: 'string'},
							amountType: {type: 'string'},
							raleonUserId: {type: 'number'}
						},
						required: ['discountCode', 'amount', 'amountType', 'raleonUserId'],
					}
				},
			},
		})
		couponData: any,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		const inventoryCoupons = await this.inventoryCouponRepository.find({
			where: {
				orgId,
				externalId: couponData.discountCode,
				raleonUserId: couponData.raleonUserId,
			},
			include: [{
				relation: 'rewardCoupon'
			}]
		});


		if (!inventoryCoupons?.length) throw new HttpErrors.NotFound(`Coupon not found for discount code ${couponData.discountCode} and org ${orgId}`);
		const filteredInventoryCoupons = inventoryCoupons.filter(ic => ic.rewardCoupon != null);
		const coupon = filteredInventoryCoupons[0];

		await this.inventoryCouponRepository.updateById(
			coupon.id,
			{used: true, usedDate: new Date().toISOString()}
		);

		await this.loyaltyRewardLogService.createLogEntryForCoupon(coupon);

		//delete from shopify
		await this.discountCodeService.deleteDiscountCode(coupon, orgId);

		return {
			statusCode: 200,
			body: JSON.stringify({
				message: `Coupon ${coupon.id} updated successfully`
			})
		}
	}
	@skipGuardCheck()
	@authenticate('jwt')
	@post('/inventory-coupons/verify')
	async verifyCoupon(
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						properties: {
							discountCode: {type: 'string'}
						},
						required: ['discountCode'],
					}
				},
			},
		})
		couponData: any,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		const inventoryCoupons = await this.inventoryCouponRepository.find({
			where: {
				orgId,
				externalId: couponData.discountCode
			},
			include: [{
				relation: 'loyaltyRewardDefinition'
			}]
		});


		if (!inventoryCoupons?.length) throw new HttpErrors.NotFound(`Coupon not found for discount code ${couponData.discountCode} and org ${orgId}`);
		const filteredInventoryCoupons = inventoryCoupons.filter(ic => ic.loyaltyRewardDefinition != null);
		const coupon = filteredInventoryCoupons[0];

		return {
			statusCode: 200,
			body: JSON.stringify({
				loyaltyCampaignId: coupon?.loyaltyRewardDefinition?.loyaltyCampaignId,
			})
		}
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@get('/inventory-coupons/{id}/value')
	async getCouponValue(
		@param.path.number('id') id: number,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		const inventoryCoupons = await this.inventoryCouponRepository.find({
			where: {orgId, id, },
			include: [{relation: 'rewardCoupon'}]
		});


		if (!inventoryCoupons?.length) {
			throw new HttpErrors.NotFound(`Coupon id ${id} not found for org ${orgId}`);
		}

		const filteredInventoryCoupons = inventoryCoupons.filter(ic => ic.rewardCoupon != null);
		const coupon = filteredInventoryCoupons[0];

		let amount = '';
		switch (coupon.rewardCoupon.amountType) {
			case 'percent-discount':
				amount = `${coupon.rewardCoupon.amount}% off`;
				break;
			case 'percent-off-product':
				amount = `${coupon.rewardCoupon.amount}% off`; //TODO: Translate 'off'
				break;
			case 'dollars-off-coupon':
				amount = `$${coupon.rewardCoupon.amount} off`; //TODO: get currency symbol for org
				break;
			case 'dollar-off-product':
				amount = `$${coupon.rewardCoupon.amount} off`; //TODO: get currency symbol for org
				break;
			case 'free-product':
				amount = `${coupon.rewardCoupon.name}`;
				break;
			default:
				break;
		}

		return {
			statusCode: 200,
			body: JSON.stringify({
				loyaltyCampaignId: coupon?.loyaltyRewardDefinition?.loyaltyCampaignId,
			})
		}
	}

	@skipGuardCheck()
	@authenticate('api-key')
	@get('/inventory-coupons/expiring')
	async getExpiringCoupons(
		@param.query.string('orgIds') orgIds: string,
	): Promise<any> {

		const orgIdArray = orgIds.split(',').map(Number);

		const couponsExpiringQuery = `
			SELECT
				ic.id AS inventorycouponid,
				ic.uuid AS uuid,
				ic.*,
				rui.identityvalue,
				rui.id AS rui_id,
				rui.orgid,
				rui.lastemailsent,
				rc.name as rewardname,
				rc.amount as rewardamount,
				rc.amounttype as rewardamounttype,
				rc.id as rewardcouponid
			FROM
				"public"."inventorycoupon" ic
			JOIN
				"public"."raleonuseridentity" rui
			ON
				ic.raleonuserid = rui.raleonuserid
			JOIN
				"public"."rewardcoupon" rc
			ON
				ic.rewardcouponid = rc.id
			WHERE
				rui.orgid = ANY($1)
			AND (
				rui.unsubscribed = false
				OR
				rui.unsubscribed IS NULL
			)
			AND
				used = false
			AND
				(
					(ic.expiration >= current_timestamp AND ic.expiration < current_timestamp + INTERVAL '1 day')
					OR
					ic.expiration::date = current_date + INTERVAL '28 days'
				)
		`;


		const expiringCoupons = await this.devDbDataSource.execute!(couponsExpiringQuery, [orgIdArray], {transaction: false});

		if (!expiringCoupons || !expiringCoupons.length) {
			console.log(`No expiring coupons found for orgs ${orgIdArray}`);
			return {
				statusCode: 200,
				body: JSON.stringify([])
			}
		}
		console.log(`got coupons: `, JSON.stringify(expiringCoupons));

		const currencies = await this.currencyRepository.find({
			where: {
				organizationId: {inq: orgIdArray}
			},
			include: [ { relation: 'supportedCurrencies' } ]
		});

		const orgCurrencies = new Map(currencies.map(currency => [currency.organizationId, currency]));

		let coupons = [];
		for (const coupon of expiringCoupons) {
			const rewardName = coupon.rewardname.includes('{{currency_value') ?
				convertCurrencyPlaceholdersToValues(coupon.rewardname, orgCurrencies.get(coupon.orgid)!) :
				coupon.rewardname;

			const expirationDate = new Date(coupon.expiration);
			const expMonth = expirationDate.getMonth() + 1;
			const expDay = expirationDate.getDate();
			const expYear = expirationDate.getFullYear();

			coupons.push({
				orgId: coupon.orgid,
				customerId: coupon.identityvalue,
				eventName: 'reward_expiring',
				data: {
					couponId: coupon.uuid,
					rewardName: rewardName,
					expirationDate: `${expMonth}/${expDay}/${expYear}`,
					expirationMonth: expMonth,
					expirationDay: expDay,
					expirationYear: expYear,
				},
			});
		}

		return {
			statusCode: 200,
			body: JSON.stringify(coupons)
		}
	}
}
