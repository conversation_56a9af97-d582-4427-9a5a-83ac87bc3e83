import {
	Count,
	CountSchema,
	Filter,
	FilterExcludingWhere,
	repository,
	Where,
} from '@loopback/repository';
import {
	post,
	param,
	get,
	getModelSchemaRef,
	patch,
	put,
	del,
	api,
	requestBody,
	response,
	HttpErrors,
} from '@loopback/rest';
import {LoyaltyProgram, LoyaltyCurrency} from '../../models';
import {LoyaltyProgramRepository, LoyaltyCurrencyRepository, RaleonUserIdentityRepository, LoyaltyCampaignRepository, OnboardingStateRepository, OnboardingTaskRepository, LoyaltyRewardLogRepository, RaleonUserEarnLogRepository, TranslationStringRepository, OrganizationRepository, FeatureSettingRepository} from '../../repositories';
import {modelForGuard, modelIdForGuard, OrgGuardPropertyStrategy, restrictReadsWithGuard, skipGuardCheck} from '../../interceptors/crud-guard.interceptor';
import {injectGuardedFilter, injectUserOrgId, guardStrategy} from '../../interceptors/crud-guard.interceptor';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../../services/basic.authorizor';
import {service} from '@loopback/core';
import {OnboardingTaskStateService, TranslationService} from '../../services';
import {FeatureService} from '../../services/feature.service';


@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardPropertyStrategy<LoyaltyProgram>({
	orgIdModelPropertyName: 'orgId',
	repositoryClass: LoyaltyProgramRepository
}))

export class LoyaltyProgramController {
	constructor(
		@repository(LoyaltyProgramRepository)
		public loyaltyProgramRepository: LoyaltyProgramRepository,
		@repository(LoyaltyCurrencyRepository)
		public loyaltyCurrencyRepository: LoyaltyCurrencyRepository,
		@repository(RaleonUserIdentityRepository)
		public raleonUserIdentityRepository: RaleonUserIdentityRepository,
		@repository(LoyaltyCampaignRepository)
		public loyaltyCampaignRepository: LoyaltyCampaignRepository,
		@repository(OnboardingStateRepository)
		private onboardingStateRepository: OnboardingStateRepository,
		@repository(OnboardingTaskRepository)
		private onboardingTaskRepository: OnboardingTaskRepository,
		@repository(LoyaltyRewardLogRepository)
		private loyaltyRewardLogRepository: LoyaltyRewardLogRepository,
		@service(OnboardingTaskStateService)
		public onboardingTaskStateService: OnboardingTaskStateService,
		@repository(RaleonUserEarnLogRepository)
		private raleonUserEarnLogRepository: RaleonUserEarnLogRepository,
		@repository(TranslationStringRepository)
		private translationStringRepository: TranslationStringRepository,
		@repository(OrganizationRepository)
		private organizationRepository: OrganizationRepository,
		@service(TranslationService)
		private translationService: TranslationService,
		@service(FeatureService)
		private featureService: FeatureService,
		@repository(FeatureSettingRepository)
		private featureSettingRepository: FeatureSettingRepository,
	) { }

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/loyalty-programs')
	@response(200, {
		description: 'LoyaltyProgram model instance',
		content: {'application/json': {schema: getModelSchemaRef(LoyaltyProgram)}},
	})
	async create(
		@modelForGuard(LoyaltyProgram)
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(LoyaltyProgram, {
						title: 'NewLoyaltyProgram',
						exclude: ['id', 'orgId'],
					}),
				},
			},
		})
		loyaltyProgram: Omit<LoyaltyProgram, 'id'>,
		@injectUserOrgId() orgId: number
	): Promise<LoyaltyProgram> {
		loyaltyProgram.orgId = orgId;
		return this.loyaltyProgramRepository.create(loyaltyProgram);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/loyalty-programs/count')
	@response(200, {
		description: 'LoyaltyProgram model count',
		content: {'application/json': {schema: CountSchema}},
	})
	@skipGuardCheck()
	async count(
		@injectUserOrgId() orgId: number
	): Promise<Count> {
		return this.loyaltyProgramRepository.count(
			{
				orgId: orgId
			}
		);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/loyalty-programs')
	@response(200, {
		description: 'Array of LoyaltyProgram model instances',
		content: {
			'application/json': {
				schema: {
					type: 'array',
					items: getModelSchemaRef(LoyaltyProgram, {includeRelations: true}),
				},
			},
		},
	})
	@restrictReadsWithGuard({
		plural: true,
		filterOnly: true
	})
	async find(
		@param.filter(LoyaltyProgram)
		@injectGuardedFilter()
		filter?: Filter<LoyaltyProgram>,
	): Promise<LoyaltyProgram[]> {
		return await this.getLoyaltyPrograms(filter);
	}

	@authenticate('user-access-token')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/loyalty-programs-admin')
	@response(200, {
		description: 'Array of LoyaltyProgram model instances',
		content: {
			'application/json': {
				schema: {
					type: 'array',
					items: getModelSchemaRef(LoyaltyProgram, {includeRelations: true}),
				},
			},
		},
	})
	@restrictReadsWithGuard({
		plural: true,
		filterOnly: true
	})
	async findPrograms(
		@param.filter(LoyaltyProgram)
		@injectGuardedFilter()
		filter?: Filter<LoyaltyProgram>,
	): Promise<LoyaltyProgram[]> {
		return await this.getLoyaltyPrograms(filter);
	}

	private getLoyaltyPrograms(filter?: Filter<LoyaltyProgram>) {
		//Add include relations
		filter = {
			...filter
		};

		if(filter.include && filter.include.length > 0) {
			filter.include.push('loyaltyCurrencies');
		}
		else {
			filter.include = ['loyaltyCurrencies'];
		}

		return this.loyaltyProgramRepository.find(filter);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/loyalty-programs/live/{raleonUserId}/{customerId}')
	@response(200, {
		description: 'Array of LoyaltyProgram model instances and campaigns that are currently live',
		content: {
			'application/json': {
				schema: {
					type: 'array',
					items: getModelSchemaRef(LoyaltyProgram, {includeRelations: true}),
				},
			},
		},
	})
	@skipGuardCheck()
	async findLive(
		@param.path.number('raleonUserId') raleonUserId: number,
		@param.path.number('customerId') customerId: number,
		@injectUserOrgId() orgId: number
	): Promise<any[]> {
		let programs : any = await this.loyaltyProgramRepository.find({
			where: {
				orgId: orgId,
				active: true
			},
			include: ['loyaltyCurrencies']
		});
		if (!programs || !programs.length) {
			return [];
		}
		//Add include relations
		const userIdentity = await this.raleonUserIdentityRepository.findOne({
			where: {
				raleonUserId: raleonUserId,
				identityType: 'customer_id',
				orgId: orgId
			},
			include: [{
				relation: 'vipTier'
			}]
		});

		if (!userIdentity || userIdentity.identityValue != customerId.toString()) {
			throw new Error('No user found');
		}

		const loyaltySegment = userIdentity.loyaltySegment;

		// const vipLiveAndEnabled = await this.featureService.isFeatureLiveAndEnabled('vip', orgId);
		const vipFeatureSetting = await this.featureSettingRepository.findOne({
			where: {
				organizationId: orgId,
				name: 'vip'
			}
		});
		const vipLiveAndEnabled = vipFeatureSetting?.enabled && vipFeatureSetting?.live;

		//Go through and gather campaigns for each program that is active
		for (let program of programs) {
			if (program.active) {
				let programCampaigns = await this.loyaltyCampaignRepository.find({
					where: {
						loyaltyProgramId: program.id,
						active: true
					},
					include: [
						{
							relation: 'loyaltyEarns',
							scope: {
								include: [
									{
										relation: 'earnEffects',
										scope: {
											include: [
												{
													relation: 'loyaltyCurrency',
													scope: {
														fields: {
															conversionToUSD: false
														}
													}
												},
												{
													relation: 'loyaltyRewardDefinition',
													scope: {
														include: [
															{
																relation: 'rewardCoupon',
															}
														]
													}
												}
											]
										}
									},
									{
										relation: 'earnConditions'
									}
								],
								where: {
									active: { inq: [null, true] },
									recommendationState: { inq: ['NONE', 'APPROVED_RECOMMENDATION'] }
								}
							}
						},
						{
							relation: 'loyaltyRedemptionShopItems',
							scope: {
								include: [
									{
										relation: 'loyaltyRewardDefinition',
										scope: {
											fields: {
												startingInventory: false,
												redeemed: false,
												granted: false,
											},
											include: [{relation: 'rewardCoupon', }]
										}
									},
								],
								where: {
									active: { inq: [null, true] } //To support old campaigns that don't have active field
								}
							}
						},
						{
							relation: "staticEffects",
							scope: {
								include: [
									{
										relation: 'loyaltyRewardDefinition',
										scope: {
											fields: {
												startingInventory: false,
												redeemed: false,
												granted: false,
											},
											include: [{relation: 'rewardCoupon', }]
										}
									},
								],
								where: {
									active: { inq: [null, true] } //To support old campaigns that don't have active field
								}
							}
						},
						{
							relation: 'vipTier'
						}
					]
				});

				let finalCampaigns = [];
				let campaigns = programCampaigns.filter((campaign: any) => {
					if (vipLiveAndEnabled) {
						const isUserTier = userIdentity.vipTier?.loyaltyCampaignId === campaign.id;
						if (isUserTier) {
							return true;
						}
					}

					if (!campaign.live) return false;
					if (!campaign.loyaltySegment && !campaign.vipTier) return true;
					//include unclassified users in very loyal campaigns
					if (campaign.loyaltySegment.toUpperCase() === 'VERY LOYAL' && loyaltySegment?.toUpperCase() === 'UNCLASSIFIED') return true;
					return campaign.loyaltySegment === loyaltySegment || campaign.loyaltySegment.toUpperCase() === 'EVERYONE';
				});

				const userLogs = await this.loyaltyRewardLogRepository.find({
					where: {
						raleonUserId: raleonUserId
					}
				});

				const logsByRewardDef = userLogs.reduce((acc, log) => {
					if (!acc.has(log.loyaltyRewardDefinitionId)) {
						acc.set(log.loyaltyRewardDefinitionId, []);
					}
					const logs = acc.get(log.loyaltyRewardDefinitionId!);
					if (logs) {
						logs.push(log);
					}
					return acc;
				}, new Map<number, any[]>());

				for (let campaign of programCampaigns) {
					if (campaign.live) {
						finalCampaigns.push(campaign);

						if (!campaign?.loyaltyEarns || !campaign.loyaltyEarns?.length) continue;

						let processedEarns = await Promise.all(campaign?.loyaltyEarns?.map(async (earn: any) => {
							let completedCount = 0;
							const userEarnLog = await this.raleonUserEarnLogRepository.find({
								where: {
									raleonUserId: raleonUserId,
									loyaltyEarnId: earn.id,
									completed: true
								}
							});

							//Mark earn as completed if user has completed it, each type of earn has different conditions
							if (earn.earnConditions && earn.earnConditions.length > 0) {
								const condition = earn.earnConditions[0];
								if (condition.type === 'specific-product-purchase' || condition.type === 'collection-product-purchase' ||
									condition.type === 'nth-purchase' || condition.type == 'timed-purchase' || condition.type ==='welcome-bonus' ||
									condition.type === 'referred-customer-bonus') {
									if (userEarnLog.length > 0) {
										earn.completed = true;
										return earn;
									}
								}

								if(condition.type === 'product-review' || condition.type === 'product-photo-review') {
									//number of reviews
									let userEarnLogCount = userEarnLog.length;
									if(condition.amount <= userEarnLogCount) {
										earn.completed = true;
										return earn;
									}
								}
							}

							if (!earn.earnEffects || !earn.earnEffects.length) return earn;

							for (const earnEffect of earn.earnEffects) {
								const rewardDefinition = earnEffect.loyaltyRewardDefinition;
								if (rewardDefinition) {
									const logs = logsByRewardDef.get(rewardDefinition.id);
									const grantCount = logs?.length || 0;
									const completed = grantCount >= rewardDefinition.maxUserGrants;
									if (completed) {
										completedCount++;
									}
								}
							}

							earn.completed = completedCount === earn.earnEffects.length;
							return earn;
						}));
						campaign.loyaltyEarns = processedEarns.filter(earn => {
							return !earn.completed;
						});

						//Probably not ideal, or maybe we should always just do this, but in the future
						//when multiple currencies are supported, we should only do this if the earnEffect
						//doesn't have a loyaltyCurrency
						if(campaign.loyaltyEarns && campaign.loyaltyEarns.length > 0) {
							for (let earn of campaign.loyaltyEarns) {
								// Iterate through earnEffects
								if(earn.earnEffects && earn.earnEffects.length > 0) {
									for (let effect of earn.earnEffects) {
										// Check if earnEffect lacks a loyaltyCurrency
										if (!(effect as any).loyaltyCurrency) {
											// Assign loyaltyCurrency from program
											(effect as any).loyaltyCurrency = program.loyaltyCurrencies[0];
										}

										if (!effect.loyaltyCurrencyId) {
											effect.loyaltyCurrencyId = program.loyaltyCurrencies[0].id;
										}
									}
								}
							}
						}
					}
				}
				program.loyaltyCampaigns = campaigns;
			}
		}
		return [...programs];
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@patch('/loyalty-programs')
	@response(200, {
		description: 'LoyaltyProgram PATCH success count',
		content: {'application/json': {schema: CountSchema}},
	})
	async updateAll(
		@modelForGuard(LoyaltyProgram)
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(LoyaltyProgram, {partial: true}),
				},
			},
		})
		loyaltyProgram: LoyaltyProgram,
		@param.where(LoyaltyProgram) where?: Where<LoyaltyProgram>,
	): Promise<Count> {
		return this.loyaltyProgramRepository.updateAll(loyaltyProgram, where);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/loyalty-programs/{id}')
	@response(200, {
		description: 'LoyaltyProgram model instance',
		content: {
			'application/json': {
				schema: getModelSchemaRef(LoyaltyProgram, {includeRelations: true}),
			},
		},
	})
	async findById(
		@modelIdForGuard(LoyaltyProgram)
		@param.path.number('id') id: number
	): Promise<LoyaltyProgram | null> {
		return this.loyaltyProgramRepository.findById(id, {
			include: ['loyaltyCurrencies', 'loyaltyCampaigns']
		});
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@patch('/loyalty-programs/{id}')
	@response(204, {
		description: 'LoyaltyProgram PATCH success',
	})
	async updateById(
		@param.path.number('id') id: number,
		@modelForGuard(LoyaltyProgram)
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(LoyaltyProgram, {partial: true}),
				},
			},
		})
		loyaltyProgram: LoyaltyProgram,
	): Promise<void> {
		if (!this.featureService.isFeatureAvailable('loyalty-app', loyaltyProgram.orgId)) {
			throw new HttpErrors.Forbidden('Feature not available');
		}

		await this.loyaltyProgramRepository.updateById(id, loyaltyProgram);


		this.onboardingTaskStateService.updateState({ state: 'Verified' }, 'Launch Program', loyaltyProgram.orgId).catch();
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@put('/loyalty-programs/{id}')
	@response(204, {
		description: 'LoyaltyProgram PUT success',
	})
	async replaceById(
		@modelForGuard(LoyaltyProgram)
		@param.path.number('id') id: number,
		@requestBody() loyaltyProgram: LoyaltyProgram,
	): Promise<void> {
		await this.loyaltyProgramRepository.replaceById(id, loyaltyProgram);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@del('/loyalty-programs/{id}')
	@response(204, {
		description: 'LoyaltyProgram DELETE success',
	})
	async deleteById(
		@modelIdForGuard(LoyaltyProgram)
		@param.path.number('id') id: number
	): Promise<void> {
		await this.loyaltyProgramRepository.deleteById(id);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/loyalty-programs/{id}/loyalty-currencies', {
		responses: {
			'200': {
				description: 'LoyaltyProgram model instance',
				content: {'application/json': {schema: getModelSchemaRef(LoyaltyCurrency)}},
			},
		},
	})
	async createLoyaltyCurrency(
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(LoyaltyCurrency, {
						title: 'NewLoyaltyCurrencyForLoyaltyProgram',
						exclude: ['id', 'loyaltyProgramId'],
					}),
				},
			},
		})
		loyaltyCurrency: Omit<LoyaltyCurrency, 'id'>,
		@param.path.number('id') loyaltyProgramId: number,
		@injectUserOrgId() orgId: number
	): Promise<any> {
		//Grab the current Loyalty Program
		let loyaltyProgram = await this.loyaltyProgramRepository.findById(loyaltyProgramId);
		if(!loyaltyProgram) {
			return {
				statusCode: 404,
				body: 'Loyalty Program not found'
			}
		}
		else if(loyaltyProgram.orgId != orgId) {
			return {
				statusCode: 403,
				body: 'Unauthorized'
			}
		}

		loyaltyCurrency.loyaltyProgramId = loyaltyProgramId;
		return this.loyaltyCurrencyRepository.create(loyaltyCurrency);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@patch('/loyalty-programs/{id}/loyalty-currencies/{currencyid}', {
		responses: {
			'200': {
				description: 'LoyaltyProgram model instance',
				content: {'application/json': {schema: getModelSchemaRef(LoyaltyCurrency)}},
			},
		},
	})
	async updateLoyaltyCurrency(
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(LoyaltyCurrency, {
						title: 'NewLoyaltyCurrencyForLoyaltyProgram'
					}),
				},
			},
		})
		@modelForGuard(LoyaltyProgram)
		loyaltyCurrency: LoyaltyCurrency,
		@param.path.number('id') loyaltyProgramId: number,
		@param.path.number('currencyid') currencyId: number,
	): Promise<any> {
		let currencyBeingUpdated = await this.loyaltyCurrencyRepository.findById(currencyId);
		if(!currencyBeingUpdated || currencyBeingUpdated.loyaltyProgramId != loyaltyProgramId) {
			return {
				statusCode: 404,
				body: 'Currency not found'
			}
		}

		const orgId = (await this.loyaltyProgramRepository.findById(loyaltyProgramId)).orgId;
		const language = (await this.organizationRepository.findById(orgId))?.language;

		await this.loyaltyCurrencyRepository.updateById(currencyId, loyaltyCurrency);

		const pointsNameOrgTranslation = await this.translationStringRepository.findOne({
			where: { key: 'points', orgId, }
		});

		const pointsAbbreviatedNameOrgTranslation = await this.translationStringRepository.findOne({
			where: { key: 'points_abbreviated', orgId, }
		});

		if (!pointsNameOrgTranslation) {
			await this.translationStringRepository.create({
				key: 'points',
				value: loyaltyCurrency.name,
				orgId,
				language: language || 'en',
				id: `${language}_points_org_${orgId}`
			});
		} else {
			await this.translationStringRepository.updateById(pointsNameOrgTranslation.id, {
				value: loyaltyCurrency.name
			});
		}

		if (!pointsAbbreviatedNameOrgTranslation) {
			await this.translationStringRepository.create({
				key: 'points_abbreviated',
				value: loyaltyCurrency.abbreviatedName,
				orgId,
				language: language || 'en',
				id: `${language}_points_abbreviated_org_${orgId}`,
			});
		} else {
			await this.translationStringRepository.updateById(pointsAbbreviatedNameOrgTranslation.id, {
				value: loyaltyCurrency.abbreviatedName
			});
		}

		await this.translationService.populateCache();

		return await this.loyaltyProgramRepository.loyaltyCurrencies(loyaltyProgramId).patch(loyaltyCurrency);
	}
}
