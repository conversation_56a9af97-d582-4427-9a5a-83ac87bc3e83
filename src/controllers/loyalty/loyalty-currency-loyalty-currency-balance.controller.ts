import {
	Count,
	CountSchema,
	Filter,
	repository,
	Where,
} from '@loopback/repository';
import {
	api,
	del,
	get,
	getModelSchemaRef,
	getWhereSchemaFor,
	HttpErrors,
	param,
	patch,
	post,
	requestBody,
} from '@loopback/rest';
import {
	LoyaltyCurrency,
	LoyaltyCurrencyBalance,
	LoyaltyCurrencyTxLog,
	LoyaltyProgram,
} from '../../models';
import {LoyaltyCurrencyBalanceRepository, LoyaltyCurrencyRepository, LoyaltyCurrencyTxLogRepository, LoyaltyProgramRepository} from '../../repositories';
import {guardStrategy, modelForGuard, modelIdForGuard, OrgGuardMultiHopPropertyStrategy, OrgGuardSingleHopPropertyStrategy, skipGuardCheck} from '../../interceptors';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../../services';
import {LoyaltyBalanceManager} from '../../services/loyalty/loyalty-balance-manager.service';
import {service} from '@loopback/core';

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardSingleHopPropertyStrategy<LoyaltyCurrency, LoyaltyProgram>({
	relatedIdPropertyName: 'loyaltyProgramId',
	relatedOrgIdPropertyName: 'orgId',
	relatedRepositoryClass: LoyaltyProgramRepository,
	repositoryClass: LoyaltyCurrencyRepository
}))
export class LoyaltyCurrencyLoyaltyCurrencyBalanceController {
	constructor(
		@repository(LoyaltyCurrencyRepository)
		protected loyaltyCurrencyRepository: LoyaltyCurrencyRepository,
		@repository(LoyaltyCurrencyBalanceRepository)
		protected loyaltyCurrencyBalanceRepository: LoyaltyCurrencyBalanceRepository,
		@repository(LoyaltyCurrencyTxLogRepository)
		protected loyaltyCurrencyTxLogRepository: LoyaltyCurrencyTxLogRepository,
		@service(LoyaltyBalanceManager)
		protected loyaltyBalanceManager: LoyaltyBalanceManager,
	) { }

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/loyalty-currencies/{id}/loyalty-currency-balances', {
		responses: {
			'200': {
				description: 'Array of LoyaltyCurrency has many LoyaltyCurrencyBalance',
				content: {
					'application/json': {
						schema: {type: 'array', items: getModelSchemaRef(LoyaltyCurrencyBalance)},
					},
				},
			},
		},
	})
	async find(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyCurrency)
		id: number,
		@param.query.object('filter') filter?: Filter<LoyaltyCurrencyBalance>,
	): Promise<LoyaltyCurrencyBalance[]> {
		return this.loyaltyCurrencyRepository.loyaltyCurrencyBalances(id).find(filter);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/loyalty-currencies/{id}/loyalty-currency-balances', {
		responses: {
			'200': {
				description: 'LoyaltyCurrency model instance',
				content: {'application/json': {schema: getModelSchemaRef(LoyaltyCurrencyBalance)}},
			},
		},
	})
	async create(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyCurrency)
		id: typeof LoyaltyCurrency.prototype.id,
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(LoyaltyCurrencyBalance, {
						title: 'NewLoyaltyCurrencyBalanceInLoyaltyCurrency',
						exclude: ['id'],
						optional: ['loyaltyCurrencyId']
					}),
				},
			},
		}) loyaltyCurrencyBalance: Omit<LoyaltyCurrencyBalance, 'id'>,
	): Promise<LoyaltyCurrencyBalance> {
		return this.loyaltyCurrencyRepository.loyaltyCurrencyBalances(id).create(loyaltyCurrencyBalance);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@patch('/loyalty-currencies/{id}/loyalty-currency-balances', {
		responses: {
			'200': {
				description: 'LoyaltyCurrency.LoyaltyCurrencyBalance PATCH success count',
				content: {'application/json': {schema: CountSchema}},
			},
		},
	})
	async patch(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyCurrency)
		id: number,
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(LoyaltyCurrencyBalance, {partial: true}),
				},
			},
		})
		loyaltyCurrencyBalance: Partial<LoyaltyCurrencyBalance>,
		@param.query.object('where', getWhereSchemaFor(LoyaltyCurrencyBalance)) where?: Where<LoyaltyCurrencyBalance>,
	): Promise<Count> {
		return this.loyaltyCurrencyRepository.loyaltyCurrencyBalances(id).patch(loyaltyCurrencyBalance, where);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@del('/loyalty-currencies/{id}/loyalty-currency-balances', {
		responses: {
			'200': {
				description: 'LoyaltyCurrency.LoyaltyCurrencyBalance DELETE success count',
				content: {'application/json': {schema: CountSchema}},
			},
		},
	})
	async delete(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyCurrency)
		id: number,
		@param.query.object('where', getWhereSchemaFor(LoyaltyCurrencyBalance)) where?: Where<LoyaltyCurrencyBalance>,
	): Promise<Count> {
		return this.loyaltyCurrencyRepository.loyaltyCurrencyBalances(id).delete(where);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/loyalty-currencies/{id}/balance-change', {
		responses: {
			'200': {
				description: 'LoyaltyCurrency model instance',
				content: {
					'application/json': {
						type: 'object',
						properties: {
							"info": {
								"type": "string"
							},
							"balanceChange": {
								"type": "number"
							},
							"raleonUserId": {
								"type": "number"
							},
						}
					}
				},
			},
		},
	})
	async changeBalance(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyCurrency)
		id: typeof LoyaltyCurrency.prototype.id,
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						properties: {
							"info": {
								"type": "string"
							},
							"balanceChange": {
								"type": "number"
							},
							"raleonUserId": {
								"type": "number"
							},
							"orderId": {
								"type": "number",
								description: "The order id that triggered the balance change, if applicable"
							},
							"itemId": {
								"type": "number",
								description: "The item id that triggered the balance change, if applicable"
							},
							"earnEffectId": {
								"type": "number",
								description: "The earn effect id that triggered the balance change, if applicable"
							}
						},
						required: ["info", "balanceChange", "raleonUserId"]
					}
				}
			}
		})
		request: any,
	): Promise<any> {
		// Fetch the existing balance record
		return await this.loyaltyBalanceManager.updateBalanceAndLog(id, request);
	}


	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/loyalty-currencies/refund-balance-change', {
		responses: {
			'200': {
				description: 'LoyaltyCurrency model instance',
				content: {
					'application/json': {
						type: 'object',
						properties: {}
					}
				},
			},
		},
	})
	async refundBalanceChange(
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						properties: {
							"raleonUserId": {
								"type": "number"
							},
							"originalAmount": {
								"type": "string",
								description: "The original total price"
							},
							"totalRefund": {
								"type": "number",
								description: "The refunded amount"
							},
							"orderId": {
								"type": "number",
								description: "The order id that triggered the refund balance change"
							},
							"kind": {
								"type": "string",
								description: "The kind of refund ('authorization'|'capture'|'sale'|'void'|'refund'"
							}
						},
						required: ["orderId", "originalAmount", "totalRefund", "raleonUserId"]
					}
				}
			}
		})
		request: any,
	): Promise<any> {
		// Fetch the existing balance record
		return this.loyaltyBalanceManager.processRefund(request.raleonUserId, request.orderId, Number(request.originalAmount), request.totalRefund, request.kind);
	}

	@authenticate('shopify-customer-access-token')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/loyalty-currencies/{id}/balance-change-access-token', {
		responses: {
			'200': {
				description: 'LoyaltyCurrency model instance',
				content: {
					'application/json': {
						type: 'object',
						properties: {
							"info": {
								"type": "string"
							},
							"balanceChange": {
								"type": "number"
							},
							"raleonUserId": {
								"type": "number"
							},
						}
					}
				},
			},
		},
	})
	async changeBalanceUserAccess(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyCurrency)
		id: typeof LoyaltyCurrency.prototype.id,
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						properties: {
							"info": {
								"type": "string"
							},
							"balanceChange": {
								"type": "number"
							},
							"raleonUserId": {
								"type": "number"
							},
						}
					}
				}
			}
		})
		request: any,
	): Promise<any> {
		// Fetch the existing balance record
		return this.loyaltyBalanceManager.updateBalanceAndLog(id, request);
	}
}
