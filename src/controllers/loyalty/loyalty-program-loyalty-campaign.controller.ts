import {
	Count,
	CountSchema,
	Filter,
	repository,
	Where,
} from '@loopback/repository';
import {
	del,
	get,
	getModelSchemaRef,
	getWhereSchemaFor,
	param,
	patch,
	post,
	requestBody,
	api
} from '@loopback/rest';
import {
	LoyaltyProgram,
	LoyaltyCampaign,
} from '../../models';
import {LoyaltyProgramRepository} from '../../repositories';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../../services';
import {modelIdForGuard, OrgGuardPropertyStrategy, skipGuardCheck} from '../../interceptors/crud-guard.interceptor';
import {guardStrategy, GuardSkipStrategy} from '../../interceptors/crud-guard.interceptor';

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardPropertyStrategy<LoyaltyProgram>({
	orgIdModelPropertyName: 'orgId',
	repositoryClass: LoyaltyProgramRepository
}))
export class LoyaltyProgramLoyaltyCampaignController {
	constructor(
		@repository(LoyaltyProgramRepository) protected loyaltyProgramRepository: LoyaltyProgramRepository,
	) { }

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/loyalty-programs/{id}/loyalty-campaigns', {
		responses: {
			'200': {
				description: 'Array of LoyaltyProgram has many LoyaltyCampaign',
				content: {
					'application/json': {
						schema: {type: 'array', items: getModelSchemaRef(LoyaltyCampaign)},
					},
				},
			},
		},
	})
	async find(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyProgram)
		id: number,
		@param.query.object('filter') filter?: Filter<LoyaltyCampaign>,
	): Promise<LoyaltyCampaign[]> {
		return this.loyaltyProgramRepository.loyaltyCampaigns(id).find(filter);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/loyalty-programs/{id}/loyalty-campaigns', {
		responses: {
			'200': {
				description: 'LoyaltyProgram model instance',
				content: {'application/json': {schema: getModelSchemaRef(LoyaltyCampaign)}},
			},
		},
	})
	async create(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyProgram)
		id: typeof LoyaltyProgram.prototype.id,
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(LoyaltyCampaign, {
						title: 'NewLoyaltyCampaignInLoyaltyProgram',
						exclude: ['id'],
						optional: ['loyaltyProgramId']
					}),
				},
			},
		}) loyaltyCampaign: Omit<LoyaltyCampaign, 'id'>,
	): Promise<LoyaltyCampaign> {
		return this.loyaltyProgramRepository.loyaltyCampaigns(id).create(loyaltyCampaign);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@patch('/loyalty-programs/{id}/loyalty-campaigns', {
		responses: {
			'200': {
				description: 'LoyaltyProgram.LoyaltyCampaign PATCH success count',
				content: {'application/json': {schema: CountSchema}},
			},
		},
	})
	async patch(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyProgram)
		id: number,
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(LoyaltyCampaign, {partial: true}),
				},
			},
		})
		loyaltyCampaign: Partial<LoyaltyCampaign>,
		@param.query.object('where', getWhereSchemaFor(LoyaltyCampaign)) where?: Where<LoyaltyCampaign>,
	): Promise<Count> {
		return this.loyaltyProgramRepository.loyaltyCampaigns(id).patch(loyaltyCampaign, where);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@del('/loyalty-programs/{id}/loyalty-campaigns', {
		responses: {
			'200': {
				description: 'LoyaltyProgram.LoyaltyCampaign DELETE success count',
				content: {'application/json': {schema: CountSchema}},
			},
		},
	})
	async delete(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyProgram)
		id: number,
		@param.query.object('where', getWhereSchemaFor(LoyaltyCampaign)) where?: Where<LoyaltyCampaign>,
	): Promise<Count> {
		return this.loyaltyProgramRepository.loyaltyCampaigns(id).delete(where);
	}
}
