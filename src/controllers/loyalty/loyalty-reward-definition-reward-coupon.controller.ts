import {
	Count,
	CountSchema,
	Filter,
	repository,
	Where,
} from '@loopback/repository';
import {
	api,
	del,
	get,
	getModelSchemaRef,
	getWhereSchemaFor,
	param,
	patch,
	post,
	requestBody,
} from '@loopback/rest';
import {
	LoyaltyProgram,
	LoyaltyRewardDefinition,
	RewardCoupon,
} from '../../models';
import {LoyaltyProgramRepository, LoyaltyRewardDefinitionRepository, RewardCouponRepository} from '../../repositories';
import {guardStrategy, modelIdForGuard, OrgGuardSingleHopPropertyStrategy, restrictReadsWithGuard, skipGuardCheck} from '../../interceptors/crud-guard.interceptor';
import {authenticate} from '@loopback/authentication';

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardSingleHopPropertyStrategy<LoyaltyRewardDefinition, LoyaltyProgram>({
	repositoryClass: LoyaltyRewardDefinitionRepository,
	relatedIdPropertyName: 'loyaltyProgramId',
	relatedRepositoryClass: LoyaltyProgramRepository,
	relatedOrgIdPropertyName: 'orgId',
}))
export class LoyaltyRewardDefinitionRewardCouponController {
	constructor(
		@repository(LoyaltyRewardDefinitionRepository)
		protected loyaltyRewardDefinitionRepository: LoyaltyRewardDefinitionRepository,
		@repository(RewardCouponRepository)
		protected rewardCouponRepository: RewardCouponRepository
	) { }

	@authenticate('jwt')
	@restrictReadsWithGuard({ plural: false })
	@get('/loyalty-reward-definitions/{id}/reward-coupon', {
		responses: {
			'200': {
				description: 'LoyaltyRewardDefinition has one RewardCoupon',
				content: {
					'application/json': {
						schema: getModelSchemaRef(RewardCoupon),
					},
				},
			},
		},
	})
	async get(
		@param.path.number('id') id: number,
		@param.query.object('filter') filter?: Filter<RewardCoupon>,
	): Promise<RewardCoupon> {
		const loyaltyRewardDefinition = await this.loyaltyRewardDefinitionRepository.findById(id);
		return this.rewardCouponRepository.findById(loyaltyRewardDefinition.rewardCouponId);
	}

	@authenticate('jwt')
	@post('/loyalty-reward-definitions/{id}/reward-coupon', {
		responses: {
			'200': {
				description: 'LoyaltyRewardDefinition model instance',
				content: {'application/json': {schema: getModelSchemaRef(RewardCoupon)}},
			},
		},
	})
	async create(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyRewardDefinition)
		id: typeof LoyaltyRewardDefinition.prototype.id,
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(RewardCoupon, {
						title: 'NewRewardCouponInLoyaltyRewardDefinition',
						exclude: ['id'],
					}),
				},
			},
		}) rewardCoupon: Omit<RewardCoupon, 'id'>,
	): Promise<RewardCoupon> {
		const newRewardCoupon = await this.rewardCouponRepository.create(rewardCoupon);
		await this.loyaltyRewardDefinitionRepository.updateById(id, {rewardCouponId: newRewardCoupon.id})
		return newRewardCoupon;
	}

	@authenticate('jwt')
	@patch('/loyalty-reward-definitions/{id}/reward-coupon', {
		responses: {
			'200': {
				description: 'LoyaltyRewardDefinition.RewardCoupon PATCH success count',
				content: {'application/json': {schema: CountSchema}},
			},
		},
	})
	async patch(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyRewardDefinition)
		id: number,
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(RewardCoupon, {partial: true}),
				},
			},
		})
		rewardCoupon: Partial<RewardCoupon>,
		@param.query.object('where', getWhereSchemaFor(RewardCoupon)) where?: Where<RewardCoupon>,
	): Promise<Count> {
		const loyaltyRewardDefinition = await this.loyaltyRewardDefinitionRepository.findById(id);
  		await this.rewardCouponRepository.updateById(loyaltyRewardDefinition.rewardCouponId, rewardCoupon);
		return { count: 1 };
	}
}
