import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {inject, service} from '@loopback/core';
import {Filter, repository} from '@loopback/repository';
import {api, get, getModelSchemaRef, HttpErrors, param, post, put, Request, requestBody, Response, RestBindings} from '@loopback/rest';
import {GuardMultiHopPropertyStrategy, GuardSkipStrategy, guardStrategy, modelIdForGuard, OrgGuardSingleHopPropertyStrategy, skipGuardCheck} from '../interceptors';
import {Campaign, Journey, Quest, Reward} from '../models';
import {CustomEventRewardType} from '../services/quests/reward-types/custom-event-reward-type.service';
import {getNftImageUrl, RaleonStandardNftRewardType, uploadNftImageToBucket} from '../services/quests/reward-types/raleon-standard-nft-reward-type.service';
import {RewardGrantReceipt, RewardTransaction} from '../services/quests/reward-types/reward-transaction.abstract';
import {WebhookRewardType} from '../services/quests/reward-types/webhook-reward-type.service';
import {CampaignRepository, JourneyRepository, QuestRepository} from '../repositories';
import {basicAuthorization, RaleonUserService} from '../services';
import {ConfigSchema, RewardBadRequestError, RewardConfigData, RewardForbiddenError, RewardType} from '../services/quests/reward-types/reward-type.abstract';
import {ReserveListRewardType} from '../services/quests/reward-types/reserve-list-reward-type.service';
import {uuid} from 'uuidv4';
import {XpRewardType} from '../services/quests/reward-types/xp-reward.service';
import {RaleonEthBcnReward} from '../services/quests/reward-types/raleon-eth-bcn-reward.service';
const multer  = require('multer')
const upload = multer()
const { v4: uuidv4 } = require('uuid');

import {getURL} from '../utils/utils';
const fetch = require('node-fetch');

const WEBAPP_API = 'app.raleon.io/api/v1';
const AWS_API = 'bcie42aco8.execute-api.us-east-1.amazonaws.com'

@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class RewardController {
  constructor(
	@service(RaleonUserService)
	private raleonUserService: RaleonUserService,

	@repository(QuestRepository)
	private questRepository: QuestRepository,

	@repository(JourneyRepository)
	private journeyRepository: JourneyRepository,

    @service(RaleonStandardNftRewardType)
    private nftRewardType: RaleonStandardNftRewardType,

    @service(CustomEventRewardType)
    private customEventRewardType: CustomEventRewardType,

	@service(XpRewardType)
	private xpRewardType: XpRewardType,

    @service(ReserveListRewardType)
    private reserveListRewardType: ReserveListRewardType,

    @service(WebhookRewardType)
    private webhookRewardType: WebhookRewardType,

    private rewardTypes: Array<RewardType<any, any, any>> = [
      nftRewardType,
      customEventRewardType,
	  xpRewardType,
	  reserveListRewardType,
      webhookRewardType
    ]
  ) {}

  @skipGuardCheck()
  @get('/rewards/types/all', {
      responses: {
          '200': {
              description: 'Array of all RewardTypes',
              content: {
                  'application/json': {
                      schema: {type: 'array', items: 'object'},
                  },
              },
          },
      },
  })
  async getRewardTypes(): Promise<object[]> {
      return this.rewardTypes.map(x => generateClientSafeRewardType(x));
  }

  // This is called from the snippet, so can't require auth for now
  @skipGuardCheck()
  @get('/rewards/types/{questId}', {
      responses: {
          '200': {
              description: 'Array of all RewardTypes',
              content: {
                  'application/json': {
                      schema: {type: 'array', items: 'object'},
                  },
              },
          },
      },
  })
  async getRewardTypesForQuest(
	@param.path.number('questId') questId: number
  ): Promise<object[]> {
		const quest = await this.questRepository.findById(questId, { include: ["rewards"] });
		if (!quest) {
			throw new HttpErrors.NotFound('Quest does not exist');
		}

		const types = quest.rewards.map(x => x.type.trim().toLowerCase());

		return this.rewardTypes
			.filter(x => types.includes(x.getRewardTypeName().trim().toLowerCase()))
			.map(x => generateClientSafeRewardType(x));
  }


  // This is called from the snippet, so can't require auth for now
  @skipGuardCheck()
  @get('/rewards/status/{questId}', {
      responses: {
          '200': {
              description: 'Array of reward grant transaction details',
              content: {
                  'application/json': {
                      schema: {type: 'array', items: 'object'},
                  },
              },
          },
      },
  })
  async getRewardsStatusForQuest(
	@param.path.number('questId') questId: number,
	@param.query.string('raleonId') raleonId: string,
	@param.query.string('walletAddress') walletAddress?: string,
  ): Promise<object[]> {
	const identities: any[] = [{
		identityType: 'raleon_id',
		identityValue: raleonId
	}];

	if (walletAddress) {
		identities.push({
			identityType: 'address',
			identityValue: walletAddress.toLowerCase()
		})
	}

	const raleonUser = await this.raleonUserService.getUserFromIdentities(identities);

	if (!raleonUser || !raleonUser.id) {
		throw new RewardForbiddenError('Identity was not resolvable, unable to grant reward');
	}

	const quest = await this.questRepository.findById(questId, { include: ["rewards"] });
	if (!quest) {
		throw new HttpErrors.NotFound('Quest does not exist');
	}

	const types = quest.rewards.map(x => x.type.trim().toLowerCase());

	return Promise.all(this.rewardTypes
		.filter(x => types.includes(x.getRewardTypeName().trim().toLowerCase()))
		.map(x => x.getTransaction(raleonUser.id!, questId, false).then(y => ({
			rewardType: x.getRewardTypeName(),
			receiptType: x.getRewardReceiptTypeName(),
			executed: y?.executed || false,
			executionTimeMs: y?.executionTimeMs,
			granted: y?.granted || false,
			grantTimeMs: y?.grantTimeMs,
			grantFailed: !!y?.grantFailureReason,
			grantFailureReason: y?.grantFailureReason?.toString(),
			grantFailureTimeMs: y?.grantFailureTimeMs,
			receipt: y?.receipt,
			transactionHash: y?.transactionHash,
		}))));
  }

  // This is called from the snippet, so can't require auth for now
  @skipGuardCheck()
  @get('/rewards/status/{questId}/{type}', {
      responses: {
          '200': {
              description: 'Array of reward grant transaction details',
              content: {
                  'application/json': {
                      schema: {type: 'array', items: 'object'},
                  },
              },
          },
      },
  })
  async getRewardsStatusForQuestForOneType(
	@param.path.number('questId') questId: number,
	@param.path.number('type') rewardType: string,
	@param.query.string('raleonId') raleonId: string,
	@param.query.string('walletAddress') walletAddress?: string,
  ): Promise<object> {
	const results = await this.getRewardsStatusForQuest(questId, raleonId, walletAddress);
	const result = results.find((x: any) => x.rewardType === rewardType);

	if (!result) {
		return HttpErrors.NotFound('Reward type not found for quest');
	}

	return result;
  }


  // This is called from the snippet, so can't require auth for now
  @skipGuardCheck()
  @post('/rewards/{questId}/{type}/requestGrant', {
      responses: {
          '200': {
              description: 'Request a reward grant',
              content: {
                  'application/json': {
                      schema: 'object',
                  },
              },
          },
      },
  })
  async requestRewardGrant(
	@param.path.number('questId') questId: number,
	@param.path.string('type') type: string,
	@param.query.string('raleonId') raleonId: string,
	@param.query.string('walletAddress') walletAddress?: string,
	@param.query.boolean('regrant') regrant?: boolean,
	@requestBody({
		content: {
			'application/json': {},
		},
	}) userDataUpdates?: any,
  ): Promise<RewardGrantReceipt> {
      const match = this.rewardTypes.find(x => x.getRewardTypeName().trim().toLowerCase() === type.trim().toLowerCase());
	  if (!match) {
		throw new RewardBadRequestError('Reward type does not exist, unable to grant reward');
	  }

	  const identities = [{
		identityType: 'raleon_id',
		identityValue: raleonId
	  }];

	  if (walletAddress) {
		identities.push({
			identityType: 'address',
			identityValue: walletAddress.toLowerCase()
		})
	  }

	  const raleonUser = await this.raleonUserService.getUserFromIdentities(identities);

	  if (!raleonUser || !raleonUser.id) {
		throw new RewardForbiddenError('Identity was not resolvable, unable to grant reward');
	  }

	  return match.requestGrant(raleonUser.id, questId, walletAddress, regrant, userDataUpdates as any);
	}

	@skipGuardCheck()
	@get('/rewards/eth-bcn/mint-final-nft', {
		responses: {
			'200': {
				description: 'Returns NFT Mint data',
				content: {
					'application/json': {
						schema: 'object',
					},
				},
			},
		},
	})
	async mintFinalNft(
	  @param.query.string('walletAddress') walletAddress: string,
	  @param.query.string('orgId') orgId: string,
	): Promise<any> {
		const url = `/leaderboard/rank/${orgId}/default/${walletAddress}`;
		const response = await fetch(`https://${WEBAPP_API}${url}`);
		const data = await response.json();

		const userRankPct = ((data.totalPlayers - data.userRank + 1) / data.totalPlayers) * 100; // percentile rank

		let imageUrl = '';
		let userRankImage = `${data.userRank}_wht.png`;
		if (userRankPct <= 23) {
			imageUrl = 'PLAIN.jpg';
		} else if (userRankPct > 23 && userRankPct <= 45) {
			imageUrl = 'COMMON2.jpg';
		} else if (userRankPct > 45 && userRankPct <= 65) {
			imageUrl = 'RARE.jpg';
		} else if (userRankPct > 65 && userRankPct <= 80) {
			imageUrl = 'EPIC.jpg';
		} else if (userRankPct > 80 && userRankPct <= 90) {
			imageUrl = 'LEGENDARY.jpg';
		} else if (userRankPct > 90) {
			imageUrl = 'MYTHICAL.jpg';
			userRankImage = `${data.userRank}_blk.png`;
		} else {
			imageUrl = 'PLAIN.jpg';
		}

		const path = '/dev/rewards/combine-images';
		const request = {
			method: 'POST',
			service: 'execute-api',
			region: 'us-east-1',
			path,
			host: AWS_API,
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({images: [imageUrl, userRankImage]})
		};

		let combinedImageUrl = '';
		try {
			const response = await fetch(`https://${AWS_API}${path}`, {
				method: request.method,
				headers: request.headers,
				body: request.body
			});

			const data = await response.json();
			combinedImageUrl = data.url;
		} catch (error) {
			console.error('Failed to combine images:', error);
		}
		const htmlFilename = generateRandomFilename('html');
		const htmlUrl = `https://ethbcn.raleon.io/share/${htmlFilename}`;
		const nftMintData = await (new RaleonEthBcnReward().getNftMintData(walletAddress, combinedImageUrl, htmlFilename));
		return {
			nftMintData: nftMintData,
			imageUrl: combinedImageUrl,
			htmlUrl: htmlUrl
		};
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/rewards/raleon-standard-nft-reward-v1/image', {
		responses: {
			'200': {
				description: 'URI of uploaded image',
				content: {
					'text/plain': {},
				},
			},
		},
	})
	async uploadRaleonStandardNftV1Image(
		@requestBody.file()
		@inject(RestBindings.Http.REQUEST) request: Request,
		@inject(RestBindings.Http.RESPONSE) response: Response,
	): Promise<string> {
		const filename = `${uuid()}.png`;
		const storage = multer.memoryStorage();
		const upload = multer({storage});
		return new Promise<string>((resolve, reject) => {
			try {
				upload.single('file')(request, response, async (err: any) => {
					try {
						if (err) return reject(err);

						const image = (request as any).file.buffer;

						await uploadNftImageToBucket(filename, image);

						resolve(getNftImageUrl(filename));
					} catch (e2) {
						console.error(e2);
						reject(e2);
					}
				});
			} catch (e) {
				console.error(e);
				reject(e);
			}
		  });
	}

	@skipGuardCheck()
	@put('/rewards/{questId}/{type}/transaction-hash', {
		responses: {
			'200': {
				description: 'Request a reward grant',
				content: {
					'application/json': {
						schema: 'object',
					},
				},
			},
		},
	})
	async setTransactionHash(
	  @param.path.number('questId') questId: number,
	  @param.path.string('type') type: string,
	  @param.query.string('raleonId') raleonId: string,
	  @requestBody({
		  content: {
			  'application/json': {
				transactionHash: 'string'
			  },
			},
		}) data: any,
		@param.query.string('walletAddress') walletAddress?: string,
	): Promise<RewardTransaction|undefined> {
		const match = this.rewardTypes.find(x => x.getRewardTypeName().trim().toLowerCase() === type.trim().toLowerCase());
		if (!match) {
			throw new RewardBadRequestError('Reward type does not exist, unable to grant reward');
		}

		const identities = [{
			identityType: 'raleon_id',
			identityValue: raleonId
		}];

		if (walletAddress) {
			identities.push({
				identityType: 'address',
				identityValue: walletAddress.toLowerCase()
			})
		}

		const raleonUser = await this.raleonUserService.getUserFromIdentities(identities);

		if (!raleonUser || !raleonUser.id) {
			throw new RewardForbiddenError('Identity was not resolvable, unable to grant reward');
		}
		return await match.addTransactionHash(raleonUser.id, questId, data.transactionHash);
	}
}

function generateClientSafeRewardType(rewardType: RewardType): any {
	return {
		rewardType: rewardType.getRewardTypeName(),
		receiptType: rewardType.getRewardReceiptTypeName(),
		friendlyName: rewardType.getFriendlyName(),
		configSchema: rewardType.getConfigDataSchemaForClient(),
		configTemplateKey: rewardType.getConfigDataTemplateIdentifyingConfigKey(),
		configTemplates: rewardType.getConfigDataTemplates().map(template => {
			const clientTemplate = {};
			const schema = rewardType.getConfigDataSchema();

			Object.keys(template).filter(x => !(schema as any)[x].omitFromClient).forEach(x => {
				(clientTemplate as any)[x] = (template as any)[x];
			});

			return clientTemplate;
		}),
		userDataSchema: rewardType.getUserDataSchema()
	};
}

function  generateRandomFilename(extension: any) {
	const randomString = uuidv4().replace(/-/g, '');
	return `${randomString}.${extension}`;
}
