// import {
//   repository,
// } from '@loopback/repository';
// import {
//   param,
//   get,
//   getModelSchemaRef,
//   api,
// } from '@loopback/rest';
// import {
//   Journey,
//   Quest,
// } from '../models';
// import {JourneyRepository} from '../repositories';

// @api({basePath: '/api/v1'})
// export class JourneyQuestController {
//   constructor(
//     @repository(JourneyRepository)
//     public journeyRepository: JourneyRepository,
//   ) { }

//   @get('/journeys/{id}/quest', {
//     responses: {
//       '200': {
//         description: 'Quest belonging to Journey',
//         content: {
//           'application/json': {
//             schema: {type: 'array', items: getModelSchemaRef(Quest)},
//           },
//         },
//       },
//     },
//   })
//   async getQuest(
//     @param.path.number('id') id: typeof Journey.prototype.id,
//   ): Promise<Quest> {
//     return this.journeyRepository.quest(id);
//   }
// }
