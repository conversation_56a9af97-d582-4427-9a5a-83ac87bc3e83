import {authenticate} from '@loopback/authentication';
import {service} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
  api,
} from '@loopback/rest';
import {modelForGuard, modelIdForGuard, guardStrategy, OrgGuardMultiHopPropertyStrategy, restrictReadsWithGuard, skipGuardCheck, guardAutoCount, injectUserOrgId, injectGuardedWhere} from '../interceptors';
import {Campaign, Journey, JourneyEvent, Quest} from '../models';
import {JourneyRepository, QuestRepository} from '../repositories';
import {JourneyEventService} from '../services';

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardMultiHopPropertyStrategy<Journey, Quest, Campaign>({
	inclusionChainAfterFirstHop: { relation: "campaign" },
	lastHopOrgIdPropertyName: 'orgId',
	firstHopRepositoryClass: QuestRepository,
	repositoryClass: JourneyRepository,
	firstHopIdPropertyName: 'questId'
}))
export class JourneyController {
  constructor(
    @repository(JourneyRepository)
    public journeyRepository : JourneyRepository,
    @service(JourneyEventService)
    private journeyService: JourneyEventService,
  ) {}

  // called from marketing snippet, do not guard
  @post('/journeys/register-event')
  @response(200, {
    description: 'Register journey-related event',
    content: {'application/json': {schema: getModelSchemaRef(Journey)}},
  })
  @skipGuardCheck()
  async registerEvent(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(JourneyEvent, {
            title: 'Journey-Related Event',
          }),
        },
      },
    })
    journeyEvent: JourneyEvent
  ): Promise<Journey> {
    return this.journeyService.registerEvent(journeyEvent);
  }

  @get('/journeys/count')
  @response(200, {
    description: 'Journey model count',
    content: {'application/json': {schema: CountSchema}},
  })
  @authenticate('jwt')
  @guardAutoCount()
  async count(
    @param.where(Journey) where?: Where<Journey>,
  ): Promise<Array<Journey>> {
    return this.journeyRepository.find({ where });
  }

  @get('/journeys')
  @response(200, {
    description: 'Array of Journey model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Journey, {includeRelations: true}),
        },
      },
    },
  })
  @authenticate('jwt')
  @restrictReadsWithGuard({ filterOnly: false, plural: true })
  async find(
    @param.filter(Journey)
	filter?: Filter<Journey>,
  ): Promise<Journey[]> {
    return this.journeyRepository.find(filter);
  }

  @patch('/journeys')
  @response(200, {
    description: 'Journey PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  @authenticate('jwt')
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Journey, {partial: true}),
        },
      },
    })
	@modelForGuard(Journey)
    journey: Journey,

	@injectGuardedWhere()
    @param.where(Journey) where?: Where<Journey>,
  ): Promise<Count> {
    return this.journeyRepository.updateAll(journey, where);
  }

  @get('/journeys/{id}')
  @response(200, {
    description: 'Journey model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(Journey, {includeRelations: true}),
      },
    },
  })
  @authenticate('jwt')
  @restrictReadsWithGuard({ filterOnly: false, plural: false })
  async findById(
    @param.path.number('id')
	@modelIdForGuard(Journey)
	id: typeof Journey.prototype.id,

    @param.filter(Journey, {exclude: 'where'}) filter?: FilterExcludingWhere<Journey>
  ): Promise<Journey> {
    return this.journeyRepository.findById(id, filter);
  }

  @patch('/journeys/{id}')
  @response(204, {
    description: 'Journey PATCH success',
  })
  @authenticate('jwt')
  async updateById(
	@modelIdForGuard(Journey)
    @param.path.number('id')
	id: typeof Journey.prototype.id,

    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Journey, {partial: true}),
        },
      },
    })
	@modelForGuard(Journey)
    journey: Journey,
  ): Promise<void> {
    await this.journeyRepository.updateById(id, journey);
  }

  @put('/journeys/{id}')
  @response(204, {
    description: 'Journey PUT success',
  })
  @authenticate('jwt')
  async replaceById(
	@modelIdForGuard(Journey)
    @param.path.number('id')
	id: typeof Journey.prototype.id,

    @requestBody()
	@modelForGuard(Journey)
	journey: Journey,
  ): Promise<void> {
    await this.journeyRepository.replaceById(id, journey);
  }

  @del('/journeys/{id}')
  @response(204, {
    description: 'Journey DELETE success',
  })
  @authenticate('jwt')
  async deleteById(
	@param.path.number('id')
	@modelIdForGuard(Journey)
	id: typeof Journey.prototype.id
  ): Promise<void> {
    await this.journeyRepository.deleteById(id);
  }
}
