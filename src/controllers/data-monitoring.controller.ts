import {authenticate} from '@loopback/authentication';
import {User} from '@loopback/authentication-jwt';
import {authorize} from '@loopback/authorization';
import {inject} from '@loopback/core';
import {api, post, requestBody} from '@loopback/rest';
import {SecurityBindings} from '@loopback/security';
import {GuardSkipStrategy, guardStrategy, skipGuardCheck} from '../interceptors';
import {basicAuthorization} from '../services/basic.authorizor';
const fetch = require('node-fetch')
const aws4 = require('aws4')

const AWS_ACCESS_KEY = process.env.API_ACCESS_KEY;
const AWS_SECRET_KEY = process.env.API_SECRET_KEY;
const DATA_MONITORING_API = 'dmuyhgez0j.execute-api.us-east-1.amazonaws.com'

function getURL(path: string, method: string, body?: any, host?: string) {
	const opts = {
	  host: host || DATA_MONITORING_API,
	  path: path,
	  region: 'us-east-1',
	  service: 'execute-api',
	  mode: 'cors',
	  body: body != undefined ? JSON.stringify(body) : undefined,
	  headers: {
		'Content-Type': 'application/json',
	  },
	  method: method
	}
	return aws4.sign(opts, {accessKeyId: AWS_ACCESS_KEY, secretAccessKey: AWS_SECRET_KEY});
}

@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class DataMonitoringController {
	constructor(
		@inject(SecurityBindings.USER, {optional: true})
		private user: User,
	) {}

  	@authenticate('jwt')
	@authorize({
		allowedRoles: ['raleon-support'],
		voters: [basicAuthorization],
	})
	@post('/monitoring/smart-contract')
	@skipGuardCheck()
	async create(
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						properties: {
							address: {
								type: 'string',
							},
						},
					},
				},
			},
		})
		request: any,
	): Promise<any> {
		const path = '/v1/smart-contract-metrics';
		const signedRequest = getURL(path, 'POST', request);
		const response = await fetch(`https://${DATA_MONITORING_API}${path}`, signedRequest);
		const data = await response.json();
		return data;
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['raleon-support'],
		voters: [basicAuthorization],
	})
	@post('/monitoring/nft')
	@skipGuardCheck()
	async createNFT(
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						properties: {
							address: {
								type: 'string',
							},
						},
					},
				},
			},
		})
		request: any,
	): Promise<any> {
		const path = '/v1/nft-metrics';
		const signedRequest = getURL(path, 'POST', request);
		const response = await fetch(`https://${DATA_MONITORING_API}${path}`, signedRequest);
		const data = await response.json();
		return data;
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['raleon-support'],
		voters: [basicAuthorization],
	})
	@post('/monitoring/token')
	@skipGuardCheck()
	async createToken(
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						properties: {
							address: {
								type: 'string',
							},
						},
					},
				},
			},
		})
		request: any,
	): Promise<any> {
		const path = '/v1/token-metrics';
		const signedRequest = getURL(path, 'POST', request);
		const response = await fetch(`https://${DATA_MONITORING_API}${path}`, signedRequest);
		const data = await response.json();
		return data;
	}
}
