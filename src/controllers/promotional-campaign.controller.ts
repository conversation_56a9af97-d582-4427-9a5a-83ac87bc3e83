import {
	Count,
	CountSchema,
	Filter,
	FilterExcludingWhere,
	repository,
	Where,
} from '@loopback/repository';
import {
	post,
	param,
	get,
	getModelSchemaRef,
	patch,
	put,
	del,
	requestBody,
	response,
	api,
	HttpErrors,
} from '@loopback/rest';
import {PromotionalCampaign} from '../models';
import {PromotionalCampaignDetailsRepository, PromotionalCampaignRepository} from '../repositories';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../services/basic.authorizor';
import {guardStrategy, injectUserOrgId, OrgGuardPropertyStrategy, skipGuardCheck} from '../interceptors';
import {ShopifyApiInvoker} from '../services/shopify/shopify-api-invoker.service';
import {service} from '@loopback/core';
import {FeatureService} from '../services/feature.service';

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardPropertyStrategy<PromotionalCampaign>({
	repositoryClass: PromotionalCampaignRepository,
	orgIdModelPropertyName: 'orgId',
}))
export class PromotionalCampaignController {
	constructor(
		@repository(PromotionalCampaignRepository)
		public promotionalCampaignRepository: PromotionalCampaignRepository,
		@repository(PromotionalCampaignDetailsRepository)
		public promotionalCampaignDetailsRepository: PromotionalCampaignDetailsRepository,
		@service(ShopifyApiInvoker)
		private shopifyApiInvoker: ShopifyApiInvoker,
		// @service(FeatureService)
		// private featureService: FeatureService,
	) { }

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/promotional-campaigns', {
		responses: {
			'200': {
				description: 'Will return promotionalcampaigns',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async getPromotionalCampaigns(
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		console.log('orgId', orgId);
		const campaigns = await this.promotionalCampaignRepository.find({
			where: {
				orgId,
			},
			include: [{relation: 'promotionalCampaignDetails'}],
		});

		return campaigns;
	}

	@skipGuardCheck()
	@get('/promotional-campaign/free-gift-enabled', {
		responses: {
			'200': {
				description: 'Free Gift Enabled',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async freeGiftEnabled(
		@param.query.number('orgId') orgId: number,
	): Promise<any> {
		console.log("Free Gift Enabled called for orgId:", orgId);
		const campaign = await this.promotionalCampaignRepository.find({
			where: {
				orgId,
			},
			include: [{ relation: 'promotionalCampaignDetails' }],
		});

		if (!campaign?.[0]) {
			return { status: 404, message: 'Campaign not found' };
		}

		let detail = campaign[0].promotionalCampaignDetails[0]; // TODO: update this to support more than 1 record
		const data = {
			config: detail.externalData ? JSON.parse(detail.externalData) : {
				mode: 'basic',
				giftOptions: []
			}
		};

		// Extract the first gift option for basic mode
		const firstGiftOption = data.config.giftOptions?.[0];

		const response = {
			active: campaign?.[0].active,
			config: {
				minimumOrderAmount: data.config.minimumOrderAmount,
				productVariantId: firstGiftOption?.productVariantId?.match(/\d+/)?.[0],
				price: firstGiftOption?.price,
				discountCode: detail.externalId ? detail.externalId : undefined,
				secondaryProductVariantId: data.config.giftOptions?.[1]?.productVariantId?.match(/\d+/)?.[0],
				secondaryProductVariantPrice: data.config.giftOptions?.[1]?.price,
				disableCouponStacking: data.config.disableCouponStacking || false,
				forceDiscountCode: data.config.forceDiscountCode || false,
				campaignId: campaign[0].id,
				campaignDetailsId: detail.id,
				mode: data.config.mode,
				giftOptions: data.config.giftOptions.map((option: {productVariantId: {match: (arg0: RegExp) => any[];}; price: any;}) => ({
					productVariantId: option.productVariantId?.match(/\d+/)?.[0],
					price: option.price
				}))
			}
		};

		console.log("Free Gift Enabled ended for orgId:", orgId);
		return response;
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/promotional-campaign/free-gift', {
		responses: {
			'200': {
				description: 'Creates a free gift cart transformation',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async createFreeGift(
		@requestBody({
			content: {
				'application/json': {
					schema: {},
				},
			},
		}) body: any,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		if (!body.functionId || !body.minimumOrderAmount) {
			return {status: 400, message: 'Missing required fields: functionId, minimumOrderAmount, or price'};
		}

		const numericPrice = typeof body.minimumOrderAmount === 'string'
			? parseFloat(body.minimumOrderAmount)
			: body.minimumOrderAmount;


		// Validate based on mode
		if (body.mode === 'choose_gift') {
			// Choose_gift mode requires at least one gift option
			if (!Array.isArray(body.giftOptions) || body.giftOptions.length === 0) {
				return {status: 400, message: 'Gift options are required for choose_gift mode'};
			}
			for (const option of body.giftOptions) {
				if (!option.productVariantId || isNaN(parseFloat(option.price))) {
					return {status: 400, message: 'Each gift option must have a valid productVariantId and price'};
				}
			}
		} else {
			// Default to basic mode
			// Basic mode requires exactly one gift option
			if (!Array.isArray(body.giftOptions) || body.giftOptions.length !== 1) {
				return {status: 400, message: 'Basic mode requires exactly one gift option'};
			}
			const giftOption = body.giftOptions[0];
			if (!giftOption.productVariantId || isNaN(parseFloat(giftOption.price))) {
				return {status: 400, message: 'The gift option must have a valid productVariantId and price'};
			}
		}

		// Define types for params
		type GiftOption = {
			productVariantId: string;
			price?: number;
		};

		type BasicModeParams = {
			mode: 'basic';
			functionId: string;
			minimumOrderAmount: number;
			giftOptions: GiftOption[];
			forceDiscountCode: boolean;
			disableCouponStacking: boolean;
			isShopifyPlus: boolean;
		};

		type ChooseGiftModeParams = {
			mode: 'choose_gift';
			functionId: string;
			minimumOrderAmount: number;
			giftOptions: GiftOption[];
			forceDiscountCode: boolean;
			disableCouponStacking: boolean;
			isShopifyPlus: boolean;
		};

		// Construct params based on mode
		const params: BasicModeParams | ChooseGiftModeParams = body.mode === 'choose_gift'
			? {
				mode: 'choose_gift',
				functionId: body.functionId,
				minimumOrderAmount: body.minimumOrderAmount,
				giftOptions: body.giftOptions.map((option: any) => ({
					productVariantId: option.productVariantId.match(/\d+/)[0],
					price: parseFloat(option.price?.toString() || '0'),
				})),
				forceDiscountCode: body.forceDiscountCode || false,
				disableCouponStacking: body.disableCouponStacking || false,
				isShopifyPlus: body.isShopifyPlus || false,
			}
			: {
				mode: 'basic',
				functionId: body.functionId,
				minimumOrderAmount: body.minimumOrderAmount,
				giftOptions: [
					{
						productVariantId: body.giftOptions[0].productVariantId.match(/\d+/)[0],
						price: parseFloat(body.giftOptions[0].price),
					},
				],
				forceDiscountCode: body.forceDiscountCode || false,
				disableCouponStacking: body.disableCouponStacking || false,
				isShopifyPlus: body.isShopifyPlus || false,
			};


		//TODO: don't call to create the free gift if the campaign is not active
		const response = await this.shopifyApiInvoker.invokeAdminApi(
			orgId,
			'/free-gift',
			'POST',
			JSON.stringify(params),
		);
		console.log('response', JSON.stringify(response));
		if (response.success) {
			let externalId = undefined;
			if (response.codeData) {
				externalId = response
					.codeData
					.data
					.discountCodeAppCreate
					.codeAppDiscount
					.codes.nodes[0].code;
			}

			const campaign = await this.promotionalCampaignRepository.create({
				orgId: orgId,
				startDate: new Date().toISOString(), //TODO: update this to be from request body
				//endDate: new Date().toISOString(), TODO: update this to be from request body
				active: true, //TODO: update this to be from request body
				// loyaltySegment: 'someSegment', TODO: update
				// loyaltySegmentType: 'shopify', TODO: update
			});

			const details = await this.promotionalCampaignDetailsRepository.create({
				promotionalCampaignId: campaign.id,
				externalId,
				splitPercentage: 100, //TODO: for A/B testing,
				functionId: body.functionId,
				mode: body.mode || 'basic',
				giftOptions: JSON.stringify(body.giftOptions.map((option: GiftOption) => ({
					productVariantId: `gid://shopify/ProductVariant/${option.productVariantId.match(/\d+/)?.[0] || ''}`,
					price: parseFloat(option.price?.toString() || '0')
				}))),
				externalData: JSON.stringify(params),
			});

			return {
				status: response.status,
				message: 'Successfully Configured Free Gift',
				data: {
					...campaign!,
					campaignDetails: details
				}
			};
		}

		return {status: response.status, message: 'Failed to configure free gift'};
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/promotional-campaign/{id}/free-gift', {
		responses: {
			'200': {
				description: 'Creates a free gift cart transformation',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async getFreeGift(
		@param.path.number('id') id: number,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		const campaign = await this.promotionalCampaignRepository.findOne({
			where: {
				id,
				orgId,
			},
			include: [{relation: 'promotionalCampaignDetails'}],
		});
		if (!campaign || !campaign.active) {
			return {enabled: false};
		}

		const params = campaign.promotionalCampaignDetails[0].externalId ? `?code=${campaign.promotionalCampaignDetails[0].externalId}` : '';
		const response = await this.shopifyApiInvoker.invokeAdminApi(
			orgId,
			`/free-gift${params}`,
			'GET',
		);

		return response;
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@del('/promotional-campaign/{id}/free-gift', {
		responses: {
			'200': {
				description: 'Creates a free gift cart transformation',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async deleteFreeGift(
		@requestBody({
			content: {
				'application/json': {
					schema: {},
				},
			},
		}) body: any,
		@param.path.number('id') id: number,
		@injectUserOrgId() orgId: number,
	): Promise<any> {

		if (!id) {
			return {status: 400, message: 'Missing required fields'};
		}

		const campaign = await this.promotionalCampaignRepository.find({
			where: {
				id,
				orgId,
			},
			include: [{relation: 'promotionalCampaignDetails'}],
		});

		if (!campaign) {
			return {status: 404, message: 'Campaign not found'};
		}

		let deleteBody = JSON.stringify({
			functionId: campaign[0].promotionalCampaignDetails[0].functionId,//TODO: update this to be used with more than 1 detail record
			// functionId: '811d87fa-5a00-41e9-ba01-7ba1e61a60cf'
		});

		if (campaign[0].promotionalCampaignDetails[0].externalId) { //TODO: update this to be used with more than 1 detail record
			deleteBody = JSON.stringify({
				code: campaign[0].promotionalCampaignDetails[0].externalId,
			});
		}

		const response = await this.shopifyApiInvoker.invokeAdminApi(
			orgId,
			'/free-gift',
			'DELETE',
			deleteBody,
		);
		if (!response.success) {
			return {status: response.status, message: 'Failed to delete free gift'};
		}
		await this.promotionalCampaignDetailsRepository.deleteById(campaign[0].promotionalCampaignDetails[0].id); //TODO: update this to be used with more than 1 detail record
		await this.promotionalCampaignRepository.deleteById(id);

		return {status: response.status, message: 'Successfully Deleted Free Gift'};
	}
}


