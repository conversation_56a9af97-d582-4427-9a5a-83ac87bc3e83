import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {inject} from '@loopback/core';
import {repository} from '@loopback/repository';
import {get, post, patch, del, requestBody, param, api, HttpErrors, Request, Response, RestBindings} from '@loopback/rest';
import * as AWS from 'aws-sdk';
import multer from 'multer';
import {uuid} from 'uuidv4';
import {GuardSkipStrategy, guardStrategy, injectUserOrgId, skipGuardCheck} from '../interceptors';
import {CampaignSegment, Image, Organization} from '../models';
import {CampaignRepository, CampaignSegmentRepository, ContentRepository, GoalRepository, ImageRepository, OrganizationRepository, QuestRepository, RewardRepository} from '../repositories';
import {basicAuthorization} from '../services';

const AWS_ACCESS_KEY = process.env.API_ACCESS_KEY;
const AWS_SECRET_KEY = process.env.API_SECRET_KEY;
AWS.config.update({
  region: "us-east-1",
  accessKeyId: AWS_ACCESS_KEY,
  secretAccessKey:AWS_SECRET_KEY
});

const docClient = new AWS.DynamoDB.DocumentClient();

@api({basePath: '/api/v1/chat-graph'})
@guardStrategy(new GuardSkipStrategy())
export class ChatGraphController {
	constructor(
		@repository(CampaignRepository) private campaignRepository: CampaignRepository,
		@repository(CampaignSegmentRepository) private campaignSegmentRepository: CampaignSegmentRepository,
		@repository(QuestRepository) private questRepository: QuestRepository,
		@repository(RewardRepository) private rewardRepository: RewardRepository,
		@repository(GoalRepository) private goalRepository: GoalRepository,
		@repository(ContentRepository) private contentRepository: ContentRepository,
		@repository(OrganizationRepository) private orgRepository: OrganizationRepository,
		@repository(ImageRepository) private imageRepository: ImageRepository
	) {}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
  @post('/')
  @skipGuardCheck()
  async create(
    @requestBody() item: any,
	@injectUserOrgId() orgId: number
  ): Promise<any> {
	const org = await this.orgRepository.findById(orgId);
	item.chatGraph = item.chatGraph || '[]';
	const parsed = JSON.parse(item.chatGraph);

	org.onboardingComplete = true;
	this.orgRepository.update(org).catch();

	const data = {
		priority: 1,
		...item,
		orgId: orgId.toString(),
	  };
    const params = {
      TableName: "chat-graph-prod",
      Item: data,
    };

    docClient.put(params).promise().catch();

	const quest = await this.createCampaign(data, parsed, org);

    return quest;
  }

  private async createCampaign(item: any, parsed: any, org: Organization) {

	if (item.segmentId !== -1 && org.selfService) {
		throw new HttpErrors.Forbidden('Must use Everyone audience for self-service accounts');
	}

	if (org.selfService && ![undefined, 'raleon.standard-nft-reward-v1', 'award-xp'].includes(parsed.find((x: any) => x.type === 'show-quest-info')?.data?.reward?.type)) {
		throw new HttpErrors.Forbidden('Only NFT and XP rewards can be used for self-service account');
	}

	if (!item.name) {
		throw new HttpErrors.BadRequest('Name is required');
	}

	const now = new Date();
	const campaign = await this.campaignRepository.create({
		orgId: Number(item.orgId),
		name: item.name,
		type: 'NativeQuest',
		category: item.category || ' ',
		startDate: new Date(item.startDate).toISOString(),
		endDate: new Date(item.endDate).toISOString(),
		priority: item.priority || 0,
		description: item.description || ' ',
		hiddenUntilComplete: !!item.hiddenUntilComplete
	});

	this.campaignSegmentRepository.create({
		campaignId: campaign.id,
		segmentId: item.segmentId || -1
	}).then(console.log).catch();

	const quest = await this.questRepository.create({
		campaignId: campaign.id,
		chatGraph: item.id,
		name: item.name,
		isLoginQuest: item.isLoginQuest || false,
	});

	try {
		const questActionsCommand = parsed.find((x: any) => x.type === 'show-quest-info' && x.data?.reward);
		const reward = questActionsCommand.data?.reward;
		const actions = questActionsCommand.data?.actions?.goals;

		actions?.filter((x: any) => x.type)?.forEach(async (action: any) => {
			const shallowAction = {...action};
			delete shallowAction.content;
			const goal = await this.goalRepository.create({
				externalId: uuid(),
				...shallowAction,
				questId: quest.id,
			});

			if (action.content) {
				this.contentRepository.create({
					...action.content,
					goalId: goal.id
				})
			}
		})

		if (reward && reward.type) {
			this.rewardRepository.create({
				externalId: uuid(),
				...reward,
				questId: quest.id
			}).catch();
		}
	} catch (e) {
		console.error('Error creating actions/reward', e);
	}

	console.log(quest);

	return quest;
  }

  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
  })
  @get('/{id}')
  @skipGuardCheck()
  async findById(
    @param.path.string('id') id: string,
	@injectUserOrgId() orgId: number
  ): Promise<any> {
    const params = {
      TableName: "chat-graph-prod",
      Key: { "id": id },
    };

    const item: any = await docClient.get(params).promise();
	if (item.Item.orgId != orgId) {
		throw new HttpErrors.Forbidden('Org mismatch');
	}

	return item.Item;
  }

  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
  })
  @get('/campaign/{campaignId}')
  @skipGuardCheck()
  async findByCampaignId(
    @param.path.string('campaignId') campaignId: string,
	@injectUserOrgId() orgId: number
  ): Promise<any> {
	const campaign = await this.campaignRepository.findById(Number(campaignId), {
		include: ['quests', 'image']
	});
	if (!campaign.image) {
		campaign.image = await this.imageRepository.findOne({
			where: {
				orgId,
				campaignId: null as any
			}
		}) as any;
	}
	const id = campaign.quests?.[0]?.chatGraph;
	if (!id) {
		throw HttpErrors.NotFound('No chatgraph for campaign');
	}

    const params = {
      TableName: "chat-graph-prod",
      Key: { "id": id },
    };

    const item: any = await docClient.get(params).promise();
	if (item.Item.orgId != orgId) {
		throw new HttpErrors.Forbidden('Org mismatch');
	}

	const campaignSegment = await this.campaignSegmentRepository.findOne({
		where: {
			campaignId: campaign.id
		}
	});

	return {
		...(item.Item),
		segmentId: campaignSegment?.segmentId,
		startDate: campaign.startDate,
		endDate: campaign.endDate,
		priority: campaign.priority,
		category: campaign.category,
		description: campaign.description,
		image: campaign.image,
		hiddenUntilCompletion: campaign.hiddenUntilComplete
	};
  }



  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
  })
  @patch('/campaign/{campaignId}')
  @skipGuardCheck()
  async updateByCampaignId(
    @param.path.string('campaignId') campaignId: string,
    @requestBody() item: any,
	@injectUserOrgId() orgId: number
  ): Promise<any> {
	item.chatGraph = item.chatGraph || '[]';
	const parsed = JSON.parse(item.chatGraph);
	const campaign = await this.campaignRepository.findById(Number(campaignId), {
		include: ['quests']
	});
	const id = campaign.quests?.[0]?.chatGraph;
	if (!id) {
		throw HttpErrors.NotFound('No chatgraph for campaign');
	}


	const org = await this.orgRepository.findById(orgId);
	if (org.selfService && item.segmentId !== -1) {
		throw new HttpErrors.Forbidden('Must use Everyone audience for self-service account');
	}
	if (org.selfService && ![undefined, 'raleon-standard-nft-reward-v1', 'award-xp'].includes(parsed.find((x: any) => x.type === 'show-quest-info')?.data?.reward?.type)) {
		throw new HttpErrors.Forbidden('Only NFT and XP rewards can be used for self-service account');
	}

	if (!item.name) {
		throw new HttpErrors.BadRequest('Name is required');
	}

	const shallowCampaign: any = {...campaign};
	delete shallowCampaign.quests;
	shallowCampaign.name = item.name || ' ';
	shallowCampaign.priority = item.priority || 0;
	shallowCampaign.category = item.category || ' ';
	shallowCampaign.startDate = new Date(item.startDate).toISOString();
	shallowCampaign.endDate = new Date(item.endDate).toISOString();
	shallowCampaign.description = item.description || ' ';
	shallowCampaign.hiddenUntilComplete = !!item.hiddenUntilComplete;
	this.campaignRepository.updateById(campaign.id, shallowCampaign).catch();


	this.campaignSegmentRepository.findOne({
		where: {
			campaignId: campaign.id
		}
	}).then(campaignSegment => {
		if (!campaignSegment) {
			if (!item.segmentId) {
				return;
			}

			return this.campaignSegmentRepository.create({
				campaignId: campaign.id,
				segmentId: item.segmentId
			}).catch();
		}

		campaignSegment.segmentId = item.segmentId;
		this.campaignSegmentRepository.updateById(campaignSegment.id, campaignSegment);
	});


	const quest = await this.questRepository.findOne({
		where: {
			campaignId: campaign.id
		}
	});

	try {
		const questActionsCommand = parsed.find((x: any) => x.type === 'show-quest-info');
		const reward = questActionsCommand.data?.reward;
		const actions = questActionsCommand.data?.actions?.goals;

		if (reward || actions) {
			if (!quest) {
				throw new HttpErrors.BadRequest('Cant save actions/reward without a quest');
			}

			const dbActions = await this.goalRepository.find({
				where: {
					questId: quest.id
				}
			});

			dbActions?.forEach((action: any) => {
				const found = actions?.find((x: any) => x.externalId === action.externalId);
				if (!found) {
					this.goalRepository.deleteById(action.id);
				}
			});

			actions?.filter((x: any) => x.type)?.forEach(async (action: any) => {
				const shallowAction = { ...action };
				delete shallowAction.content;

				const existing = await this.goalRepository.findOne({
				  where: {
					externalId: action.externalId,
				  },
				});

				if (existing) {
				  await this.goalRepository.updateById(
					existing.id,
					{
					  ...existing,
					  ...shallowAction
					}
				  );

				  if (action.content) {
					const existingContent = await this.contentRepository.findOne({
					  where: {
						goalId: existing.id,
					  },
					});

					if (existingContent) {
					  await this.contentRepository.updateById(
						existingContent.id,
						{
						  ...existingContent,
						  ...action.content,
						}
					  );
					} else {
					  await this.contentRepository.create({
						...action.content,
						goalId: existing.id,
					  });
					}
				  }
				} else {
				  const goal = await this.goalRepository.create({
					...shallowAction,
					questId: quest.id,
				  });

				  if (action.content) {
					await this.contentRepository.create({
					  ...action.content,
					  goalId: goal.id,
					});
				  }
				}
			  });



			  if (reward && reward.type) {
				const existingReward = await this.rewardRepository.findOne({
				  where: {
					questId: quest.id,
				  },
				});

				if (existingReward) {
				  await this.rewardRepository.updateById(
					existingReward.id,
					{
					  ...existingReward,
					  ...reward,
					}
				  );
				} else {
				  await this.rewardRepository.create({
					externalId: uuid(),
					...reward,
					questId: quest.id,
				  });
				}
			  }
		}
	} catch (e) {
		console.error('Error creating actions/reward', e);
	}

    const getParams = {
      TableName: "chat-graph-prod",
      Key: { "id": id },
    };

    const getResult: any = await docClient.get(getParams).promise();
	if (getResult.Item.orgId != orgId) {
		throw new HttpErrors.Forbidden('Org mismatch');
	}



    const params = {
		TableName: "chat-graph-prod",
		Item: {
		  ...item,
		  id: id,
		  orgId: orgId.toString()
		},
	  };

	  return docClient.put(params).promise();
  }


  @skipGuardCheck()
  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
  })
	@post('/image', {
		responses: {
			'200': {
				description: 'URI of uploaded image',
				content: {
					'text/plain': {},
				},
			},
		},
	})
	async uploadImage(
		@requestBody.file()
		@inject(RestBindings.Http.REQUEST) request: Request,
		@inject(RestBindings.Http.RESPONSE) response: Response,
	): Promise<string> {
		const filename = `${uuid()}.png`;
		const storage = multer.memoryStorage();
		const upload = multer({storage});
		return new Promise<string>((resolve, reject) => {
			try {
				upload.single('file')(request, response, async (err: any) => {
					try {
						if (err) return reject(err);

						const image = (request as any).file.buffer;

						await uploadImageToBucket(filename, image);

						resolve(getImageUrl(filename));
					} catch (e2) {
						console.error(e2);
						reject(e2);
					}
				});
			} catch (e) {
				console.error(e);
				reject(e);
			}
		  });
	}

  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
  })
  @patch('/{id}')
  @skipGuardCheck()
  async updateById(
    @param.path.string('id') id: string,
    @requestBody() item: any,
	@injectUserOrgId() orgId: number
  ): Promise<any> {
	item.chatGraph = item.chatGraph || '[]';
	const parsed = JSON.parse(item.chatGraph);

    const paramsGet = {
		TableName: "chat-graph-prod",
		Key: { "id": id },
	  };

	  const getResult: any = await docClient.get(paramsGet).promise();
	  if (getResult.Item.orgId != orgId) {
		  throw new HttpErrors.Forbidden('Org mismatch');
	  }

	if (!item.name) {
		throw new HttpErrors.BadRequest('Name is required');
	}

	const org = await this.orgRepository.findById(orgId);
	if (org.selfService && item.segmentId !== -1) {
		throw new HttpErrors.Forbidden('Must use Everyone audience for self-service account');
	}

    const params = {
      TableName: "chat-graph-prod",
      Item: {
		...item,
		id: id,
		orgId: orgId.toString()
	  },
    };
    return docClient.put(params).promise();
  }

  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
  })
  @del('/{id}')
  @skipGuardCheck()
  async deleteById(
    @param.path.string('id') id: string,
	@injectUserOrgId() orgId: number
  ): Promise<any> {

    const paramsGet = {
		TableName: "chat-graph-prod",
		Key: { "id": id },
	  };

	  const getResult: any = await docClient.get(paramsGet).promise();
	  if (getResult.Item.orgId != orgId) {
		  throw new HttpErrors.Forbidden('Org mismatch');
	  }

    const params = {
      TableName: "chat-graph-prod",
      Key: { "id": id },
    };
    return docClient.delete(params).promise();
  }

  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
  })
  @del('/campaign/{campaignId}')
  @skipGuardCheck()
  async deleteByCampaignId(
    @param.path.string('campaignId') campaignId: string,
	@injectUserOrgId() orgId: number
  ): Promise<any> {

	const campaign = await this.campaignRepository.findById(Number(campaignId), {
		include: ['quests']
	});
	if (campaign.orgId != orgId) {
		throw new HttpErrors.Forbidden('Org mismatch on campaign');
	}

	this.campaignRepository.deleteById(Number(campaignId));

	const id = campaign.quests?.[0]?.chatGraph;
	if (!id) {
		throw HttpErrors.NotFound('No chatgraph for campaign');
	}

    const getParams = {
      TableName: "chat-graph-prod",
      Key: { "id": id },
    };

    const getResult: any = await docClient.get(getParams).promise();
	if (getResult.Item.orgId != orgId) {
		throw new HttpErrors.Forbidden('Org mismatch on quest script');
	}

    const params = {
      TableName: "chat-graph-prod",
      Key: { "id": id },
    };
    return docClient.delete(params).promise();
  }

	@get('/{orgId}/customization', {
		responses: {
			'200': {
				description: 'loyalty bot customizations',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	async getLoyaltyBotCustomizations(
		@param.path.string('orgId') orgId: number
	): Promise<any> {
		const org = await this.orgRepository.findById(Number(orgId));

		return {
			launchText: org.questLaunchText || 'Quest Waiting',
			avatarUrl: org.questLaunchAvatarUrl || 'https://raleon.io/wp-content/uploads/2023/06/raleon-white-icon.png',
			brandColor: org.questBrandColor || '#400F92'
		};
	}
}


export function getImageUrl(filename: string) {
	const s3Domain = 'raleon-quest-images-prod.s3.amazonaws.com';
	const s3Path = '/'+encodeURIComponent(filename);

	return `https://${s3Domain}${s3Path}`;
}

export async function uploadImageToBucket(filename: string, imageBuffer: Buffer) {
	if (imageBuffer.length > 30 * 1024 * 1024) {
		throw new HttpErrors.PayloadTooLarge('Image size too large');
	}

	const AWS_ACCESS_KEY = process.env.API_ACCESS_KEY;
	const AWS_SECRET_KEY = process.env.API_SECRET_KEY;
	const s3 = new AWS.S3({
		accessKeyId: AWS_ACCESS_KEY,
		secretAccessKey: AWS_SECRET_KEY,
		region: 'us-east-1'
	});
	s3.putObject({
		Bucket: 'raleon-quest-images-prod',
		Key: filename,
		Body: imageBuffer,
		ContentType: 'image/png'
	}, (err: any, data: any) => {
		if (err) {
			console.error(err);
		}
	});
}
