import {authenticate} from '@loopback/authentication';
import {
  Filter,
  repository,
} from '@loopback/repository';
import {
  api,
  del,
  get,
  getModelSchemaRef,
  param,
  post,
  requestBody,
} from '@loopback/rest';
import {injectGuardedFilter, OrgGuardPropertyStrategy, guardStrategy, CrudGuardInterceptor, restrictReadsWithGuard, skipGuardCheck, injectUserOrgId, modelIdForGuard} from '../interceptors'
import {
  Project,
  Organization,
} from '../models';
import {OrganizationRepository, ProjectRepository, OrganizationPlanRepository, PlanRepository} from '../repositories';
import { SecurityBindings } from '@loopback/security';
import {User, UserServiceBindings} from '@loopback/authentication-jwt';
import {inject} from '@loopback/core';
import {basicAuthorization} from '../services';
import {authorize} from '@loopback/authorization';

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardPropertyStrategy<any>({
	orgIdModelPropertyName: 'organizationId',
	repositoryClass: OrganizationRepository
}))
export class OrgController {
  OrganizationRepository: any;
  constructor(
    @inject(SecurityBindings.USER, {optional: true})
	private user: User,
	@repository(OrganizationRepository) public organizationRepository: OrganizationRepository,
	@repository(ProjectRepository) public projectRepository: ProjectRepository,
	@repository(OrganizationPlanRepository) public organizationPlanRepository: OrganizationPlanRepository,
	@repository(PlanRepository) public planRepository: PlanRepository,
  ) { }

  @get('/admin/organizations', {
    responses: {
      '200': {
        description: 'Array of Organizations',
        content: {
          'application/json': {
            schema: {type: 'object', items: getModelSchemaRef(Organization)},
          },
        },
      },
    },
  })
  @authenticate('jwt')
  @authorize({
	allowedRoles: ['raleon-admin'],
	voters: [basicAuthorization],
  })
  @restrictReadsWithGuard({ plural: true })
  @skipGuardCheck()
  async find(
	@injectUserOrgId() orgId: string,
  ): Promise<any[]> {
	const allOrganizations = await this.organizationRepository.find({
		where: {
			externalDomain: {neq: null as any}
		}
	});

	// Fetch active organization plans for all organizations
	const orgIds = allOrganizations.map(org => org.id).filter(id => id !== undefined) as number[];
	const activePlans = await this.organizationPlanRepository.find({
		where: {
			orgId: {inq: orgIds},
			status: 'ACTIVE'
		},
		include: [{relation: 'plan'}]
	});
	console.log("Active Plans", activePlans);

	// Create a map of orgId to active plan
	const planMap = new Map();
	activePlans.forEach(orgPlan => {
		planMap.set(orgPlan.orgId, orgPlan);
	});

	// Enhance organizations with plan data
	const enhancedOrgs = allOrganizations.map(org => {
		const activePlan = planMap.get(org.id);
		return {
			...org,
			activeOrganizationPlan: activePlan
		};
	});

	return enhancedOrgs;
  }

  @post('/admin/projects')
  @authenticate('jwt')
  @restrictReadsWithGuard({ plural: true })
  @skipGuardCheck()
  async create(
	@requestBody({
		content: {
			'application/json': {
				schema: getModelSchemaRef(Project, {
					exclude: ['id'],
				}),
			},
		},
	}) proj: Project,
	@injectUserOrgId() orgId: string
  ): Promise<any> {
	if (orgId == '1' || orgId =='5') {
		await this.projectRepository.create(proj);
		return {
			statusCode: 200,
			body: 'Invite resent'
		}
	} else {
		throw new Error('Unauthorized');
	}
  }

  @get('/admin/projects/{projectUuid}')
  @authenticate('jwt')
  @restrictReadsWithGuard({ plural: true })
  @skipGuardCheck()
  async findProject(
	@param.path.string('projectUuid')
	projectUuid: typeof Project.prototype.uuid,
	@injectUserOrgId() orgId: string
  ): Promise<Project[]> {
	if (orgId == '1' || orgId =='5') {
		return this.projectRepository.find({where: {uuid: projectUuid, isAdmin: false}, include: [{relation: 'organization'}]});
	} else {
		throw new Error('Unauthorized');
	}
  }

  @del('/admin/projects/{projectId}')
  @authenticate('jwt')
  @restrictReadsWithGuard({ plural: true })
  @skipGuardCheck()
  async deleteProject(
	@param.path.number('projectId')
	projectId: typeof Project.prototype.id,
	@injectUserOrgId() orgId: string
  ): Promise<any> {
	if (orgId == '1' || orgId =='5') {
		this.projectRepository.deleteById(projectId);
		return {
			statusCode: 200,
			body: 'Project Deleted'
		}
	} else {
		throw new Error('Unauthorized');
	}
  }
}
