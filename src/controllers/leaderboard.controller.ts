import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {get, post, patch, del, requestBody, param, api, HttpErrors} from '@loopback/rest';
import * as AWS from 'aws-sdk';
import {GuardSkipStrategy, guardStrategy, injectUserOrgId, skipGuardCheck} from '../interceptors';
import {basicAuthorization} from '../services';

const AWS_ACCESS_KEY = process.env.API_ACCESS_KEY;
const AWS_SECRET_KEY = process.env.API_SECRET_KEY;
AWS.config.update({
	region: "us-east-1",
	accessKeyId: AWS_ACCESS_KEY,
	secretAccessKey: AWS_SECRET_KEY
});

const docClient = new AWS.DynamoDB.DocumentClient();

@api({basePath: '/api/v1/leaderboard'})
@guardStrategy(new GuardSkipStrategy())
export class LeaderboardController {
	constructor(
	) { }
	@get('/rank/{orgId}/{season}/{address}')
	@skipGuardCheck()
	async getRankInfo(
		@param.path.string('season') season: string,
		@param.path.string('address') address: string,
		@param.path.string('orgId') orgId: string
	): Promise<any> {
		const primaryKey = `${season}_${orgId}`;
        let userRank = 0;
        let totalPlayers = 0;
		let totalXp = 0;
        let found = false;

        // params to query all players
        let params: any = {
            TableName: "leaderboard-store-prod",
            KeyConditionExpression: 'seasonId = :hkey',
            ExpressionAttributeValues: {
                ':hkey': primaryKey,
            },
        };

		let allData: any = [];

        do {
            const dataAll = await docClient.query(params).promise();

            // Count all players
            totalPlayers += dataAll?.Items?.length || 0;
			allData = allData.concat(dataAll?.Items || []);
            if (dataAll.LastEvaluatedKey) {
                params.ExclusiveStartKey = dataAll.LastEvaluatedKey;
            } else {
                params = null;
            }
        } while (params);

		const sortedItems = allData.sort((a: any, b: any) => {
			// Sort by score in descending order
			if (b.score !== a.score) {
				return b.score - a.score;
			}

			// If the scores are the same, sort by lastUpdate in ascending order
			return new Date(a.lastUpdate).getTime() - new Date(b.lastUpdate).getTime();
		});
		for (let item of sortedItems) {
			userRank++;
			if (item.address.toLowerCase() === address.toLowerCase()) {
				found = true;
				totalXp = item.score;
				break;
			}
		}

        return {
            userRank: userRank,
            totalPlayers: totalPlayers,
			totalXp,
        };
	}
}
