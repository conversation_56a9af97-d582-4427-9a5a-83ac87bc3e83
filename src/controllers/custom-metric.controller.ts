// Uncomment these imports to begin using these cool features!
import {authenticate} from '@loopback/authentication';
import {User} from '@loopback/authentication-jwt';
import {authorize} from '@loopback/authorization';
import {inject} from '@loopback/core';
import {repository} from '@loopback/repository';
import {
	api,
	get, getModelSchemaRef, param, post, Request, requestBody, RestBindings
} from '@loopback/rest';
import {SecurityBindings} from '@loopback/security';
import {injectUserOrgId, GuardSkipStrategy, guardStrategy, skipGuardCheck} from '../interceptors';
import {CustomMetricRepository, UserRepository} from '../repositories';
import {basicAuthorization} from '../services';
import {getURL} from '../utils/utils';
const fetch = require('node-fetch');
const aws4 = require('aws4');

const DATA_API = 'bcie42aco8.execute-api.us-east-1.amazonaws.com';


@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class CustomMetricController {
	constructor(
		@inject(RestBindings.Http.REQUEST) private req: Request,
		@inject(SecurityBindings.USER) private user: User,
		@repository(CustomMetricRepository)
		public customMetricRepository: CustomMetricRepository,
		@repository('UserRepository') private userRepository: UserRepository,
	) { }
	@get('/metric/custom-metric/{network}/{address}', {
		responses: {
			'200': {
				description: 'Retrieve custom metric results for an address (metric id)',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getCustomMetricResults(
		@param.path.string('network') network: string,
		@param.path.string('address') address: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	) {
		let response: any = {};
		try {
			const url = `/dev/metric?network=NONE&address=${address}&metric-name=CUSTOM_METRIC&end-date=latest`;
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/custom-metric/info/{network}/{address}', {
		responses: {
			'200': {
				description: 'Returns data about the custom metric including query, schema, and other info',
				content: {
					'application/json': {
						schema: {
							type: 'object',
							properties: {
								"query": {
									type: 'string'
								},
								"schema": {
									type: 'string'
								},
								addresstype: {
									type: 'string'
								},
								network: {
									type: 'string'
								}
							}
						},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getCustomMetricInfo(
		@param.path.string('network') network: string,
		@param.path.string('address') address: string
	) {
		let response: any = {};
		try {
			const url = `/dev/address?address=${address}&network=${network}&addresstype=custom-metric`;
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();

		return {
			statusCode: 200,
			body: data
		};
	}

	@get('/metric/custom-metric/available', {
		responses: {
			'200': {
				description: 'Returns an array of custom metrics available for the user',
				content: {
					'application/json': {
						schema: {
						},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getAvailableCustomMetrics(@injectUserOrgId() orgId: number) {
		let customMetrics: any = await this.customMetricRepository.find({ where: { organizationId: orgId } });
		//console.log(customMetrics);
		//console.log("Number of custom metrics: " + customMetrics.length);

		// Create a new array to store the result
		let result: any = [];

		for (let i = 0; i < customMetrics.length; i++) {
			let response: any = {};
			try {
				console.log("Getting info for: " + customMetrics[i].uuid);
				const url = `/dev/address?address=${customMetrics[i].uuid}&network=NONE&addresstype=custom-metric`;
				const signedRequest = getURL(url, 'GET');
				response = await fetch(`https://${DATA_API}${url}`, signedRequest);
			} catch (err) {
				console.log("Error: " + JSON.stringify(err));
			}

			const data: any = await response.json();
			customMetrics[i].info = data;
			console.log("Info: " + JSON.stringify(data));

			// Push the modified custom metric object to the result array
			result.push({
				"uuid": customMetrics[i].uuid,
				"name": customMetrics[i].name,
				"query": customMetrics[i].info.query,
				"schema": customMetrics[i].info.schema,
				"addresstype": customMetrics[i].info.addresstype,
				"id": customMetrics[i].id,
				"address": customMetrics[i].info.address
			});
		}

		return {
			statusCode: 200,
			body: result
		};
	}


}
