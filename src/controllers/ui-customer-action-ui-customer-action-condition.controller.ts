import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
  api
} from '@loopback/rest';
import {
  UiCustomerAction,
  UiCustomerActionCondition,
} from '../models';
import {UiCustomerActionRepository} from '../repositories';
import {GuardSkipStrategy, guardStrategy, skipGuardCheck} from '../interceptors';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../services/basic.authorizor';


@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class UiCustomerActionUiCustomerActionConditionController {
  constructor(
    @repository(UiCustomerActionRepository) protected uiCustomerActionRepository: UiCustomerActionRepository,
  ) { }

  @get('/ui-customer-actions/{id}/ui-customer-action-conditions', {
    responses: {
      '200': {
        description: 'Array of UiCustomerAction has many UiCustomerActionCondition',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(UiCustomerActionCondition)},
          },
        },
      },
    },
  })
  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<UiCustomerActionCondition>,
  ): Promise<UiCustomerActionCondition[]> {
    return this.uiCustomerActionRepository.uiCustomerActionConditions(id).find(filter);
  }

  @post('/ui-customer-actions/{id}/ui-customer-action-conditions', {
    responses: {
      '200': {
        description: 'UiCustomerAction model instance',
        content: {'application/json': {schema: getModelSchemaRef(UiCustomerActionCondition)}},
      },
    },
  })
  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin'],
	  voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async create(
    @param.path.number('id') id: typeof UiCustomerAction.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UiCustomerActionCondition, {
            title: 'NewUiCustomerActionConditionInUiCustomerAction',
            exclude: ['id'],
            optional: ['uiCustomerActionId']
          }),
        },
      },
    }) uiCustomerActionCondition: Omit<UiCustomerActionCondition, 'id'>,
  ): Promise<UiCustomerActionCondition> {
    return this.uiCustomerActionRepository.uiCustomerActionConditions(id).create(uiCustomerActionCondition);
  }

  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin'],
	  voters: [basicAuthorization],
  })
  @skipGuardCheck()
  @patch('/ui-customer-actions/{id}/ui-customer-action-conditions', {
    responses: {
      '200': {
        description: 'UiCustomerAction.UiCustomerActionCondition PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UiCustomerActionCondition, {partial: true}),
        },
      },
    })
    uiCustomerActionCondition: Partial<UiCustomerActionCondition>,
    @param.query.object('where', getWhereSchemaFor(UiCustomerActionCondition)) where?: Where<UiCustomerActionCondition>,
  ): Promise<Count> {
    return this.uiCustomerActionRepository.uiCustomerActionConditions(id).patch(uiCustomerActionCondition, where);
  }


  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin'],
	  voters: [basicAuthorization],
  })
  @skipGuardCheck()
  @del('/ui-customer-actions/{id}/ui-customer-action-conditions', {
    responses: {
      '200': {
        description: 'UiCustomerAction.UiCustomerActionCondition DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(UiCustomerActionCondition)) where?: Where<UiCustomerActionCondition>,
  ): Promise<Count> {
    return this.uiCustomerActionRepository.uiCustomerActionConditions(id).delete(where);
  }
}
