import {repository} from '@loopback/repository';
import {HttpErrors, api, get, post, del, requestBody, param, put, patch} from '@loopback/rest';
import {EarnConditionRepository, EarnEffectRepository, LoyaltyCampaignRepository, LoyaltyEarnRepository, LoyaltyProgramRepository, LoyaltyRewardDefinitionRepository, RewardCouponRepository, VipTierRepository} from '../repositories';
import {GuardSkipStrategy, guardStrategy, injectUserOrgId, skipGuardCheck} from '../interceptors';
import {Http} from 'winston/lib/winston/transports';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../services';
import {LoyaltyDetailsService} from '../services/loyalty/loyalty-details.service';
import {EarnCondition, EarnEffectWithRelations} from '../models';
import {TierService} from '../services/tier.service';
import {inject, service} from '@loopback/core';
import {FeatureSettingController} from './feature-setting.controller';

@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class TierController {
  constructor(
	@repository(VipTierRepository)
	private vipTierRepository: VipTierRepository,
	@repository(LoyaltyProgramRepository)
	private loyaltyProgramRepository: LoyaltyProgramRepository,
	@repository(LoyaltyCampaignRepository)
	private loyaltyCampaignRepository: LoyaltyCampaignRepository,
	@repository(LoyaltyEarnRepository)
	private loyaltyEarnRepository: LoyaltyEarnRepository,
	@repository(EarnConditionRepository)
	private earnConditionRepository: EarnConditionRepository,
	@repository(EarnEffectRepository)
	private earnEffectRepository: EarnEffectRepository,
	@repository(LoyaltyRewardDefinitionRepository)
	private loyaltyRewardDefinitionRepository: LoyaltyRewardDefinitionRepository,
	@repository(RewardCouponRepository)
	private rewardCouponRepository: RewardCouponRepository,
	@service(TierService)
	private tierService: TierService,
	@inject('controllers.FeatureSettingController')
	private featureSettingController: FeatureSettingController,
  ) {}

  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
  })
  @post('/tier')
  @skipGuardCheck()
  async createTier(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              tierName: {type: 'string'},
              tierDescription: {type: 'string'},
              tierImageUrl: {type: 'string'},
              minPoints: {type: 'number'},
			  enabled: {type: 'boolean'}
            },
            required: ['tierName', 'minPoints'],
          },
        },
      },
    })
    payload: {tierName: string; tierDescription?: string; minPoints: number; tierImageUrl?: string, enabled?: boolean},
	@injectUserOrgId() orgId: number,
  ) {
	// get org program
	const program = await this.loyaltyProgramRepository.findOne({
		where: {
			orgId,
		},
	});

	if (!program) {
		throw new HttpErrors.NotFound('Program not found');
	}

	const foundationalCampaign = await this.loyaltyCampaignRepository.findOne({
		where: {
			loyaltyProgramId: program.id,
			evergreen: true,
			loyaltySegment: 'Everyone'
		},
	});

	if (!foundationalCampaign) {
		throw new HttpErrors.NotFound('Foundational campaign not found');
	}

	// create viptier campaign
	const campaign = await this.loyaltyCampaignRepository.create({
		loyaltyProgramId: program.id,
		name: payload.tierName,
		active: payload.enabled,
		evergreen: true,
		loyaltySegment: 'vip',
	});

    // creater VipTier
	const tier = await this.vipTierRepository.create({
		name: payload.tierName,
		loyaltyCampaignId: campaign.id,
		imageURL: payload.tierImageUrl,
		description: payload.tierDescription,
	});

	// create tier entry condition
	const earn = await this.loyaltyEarnRepository.create({
		loyaltyCampaignId: foundationalCampaign.id,
		name: `Vip Entry For VIP Tier ID ${tier.id} / VIP Campaign ${campaign.id}`,
		imageURL: payload.tierImageUrl,
		active: payload.enabled,
		hiddenFromAdminUi: true,
		hiddenFromLoyaltyUi: true
	});

	const condition = await this.earnConditionRepository.create({
		loyaltyEarnId: earn.id,
		type: 'vip-entry',
		amount: payload.minPoints,
		variable: 'points-ttm',
		operator: '>=',
		triggeredEvent: "referrer",
	});

	// create reward coupon
	const coupon = await this.rewardCouponRepository.create({
		name: payload.tierName,
		imageURL: payload.tierImageUrl,
		amount: tier.id,
		amountType: 'vip-tier',
		expiresInDays: 1,
		minimumOrderTotal: 0,
	});

	// create tier reward definition
	const reward = await this.loyaltyRewardDefinitionRepository.create({
		rewardCouponId: coupon.id,
		loyaltyProgramId: program.id,
		loyaltyCampaignId: foundationalCampaign.id,
		grantable: true,
		redeemable: false,
		maxUserGrants: 10000000,
		maxUserRedemptions: 0,
		daysToRedeem: 1
	});

	// create tier entry effect
	const effect = await this.earnEffectRepository.create({
		loyaltyEarnId: earn.id,
		type: 'vip-entry',
		loyaltyRewardDefinitionId: reward.id,
		name: payload.tierName,
		description: payload.tierDescription || '',
		imageURL: payload.tierImageUrl,
	});


	this.tierService.reassignUsersForAllTiers(orgId).catch(e =>
		console.error('Error reassigning users to tiers', e)
	);
  }



  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
  })
  @patch('/tier/{id}')
  @skipGuardCheck()
  async updateTier(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
			  id: {type: 'number'},
              tierName: {type: 'string'},
              tierDescription: {type: 'string'},
              tierImageUrl: {type: 'string'},
              minPoints: {type: 'number'},
			  enabled: {type: 'boolean'},
            },
            required: ['id'],
          },
        },
      },
    })
    payload: {id: number, tierName?: string; tierDescription?: string; minPoints?: number; tierImageUrl?: string, enabled?: boolean},
	@injectUserOrgId() orgId: number,
  ) {
	// get org program
	const program = await this.loyaltyProgramRepository.findOne({
		where: {
			orgId,
		},
	});

	if (!program) {
		throw new HttpErrors.NotFound('Program not found');
	}

	const foundationalCampaign = await this.loyaltyCampaignRepository.findOne({
		where: {
			loyaltyProgramId: program.id,
			evergreen: true,
			loyaltySegment: 'Everyone'
		},
	});

	if (!foundationalCampaign) {
		throw new HttpErrors.NotFound('Foundational campaign not found');
	}

	// find tier
	const tier = await this.vipTierRepository.findOne({
		where: {
			id: payload.id,
		},
		include: [
			{
				relation: 'loyaltyCampaign',
			}
		]
	});

	if (!tier) {
		throw new HttpErrors.NotFound('Tier not found');
	}

	// update tier
	await this.vipTierRepository.updateById(tier.id, {
		name: payload.tierName,
		imageURL: payload.tierImageUrl,
		description: payload.tierDescription,

	});

	// find viptier campaign
	if (payload.tierName && tier.loyaltyCampaign) {
		const campaign = tier.loyaltyCampaign;
		await this.loyaltyCampaignRepository.updateById(campaign.id, {
			name: payload.tierName,
			active: payload.enabled,
		});
	}

	// find tier entry conditions
	if (payload.minPoints) {
		const coupons = await this.rewardCouponRepository.find({
			where: {
				amount: tier.id,
				amountType: 'vip-tier',
			},
		});

		if (coupons && coupons.length > 0) {
			await this.rewardCouponRepository.updateAll({
				name: payload.tierName,
				imageURL: payload.tierImageUrl,
			}, {
				id: {
					inq: coupons.map(x => x.id as number),
				},
			});

			const rewards = await this.loyaltyRewardDefinitionRepository.find({
				where: {
					rewardCouponId: {
						inq: coupons.map(x => x.id as number),
					},
				},
			});

			const effects = await this.earnEffectRepository.find({
				where: {
					loyaltyRewardDefinitionId: {
						inq: rewards.map(x => x.id as number),
					},
				},
			});

			if (effects && effects.length > 0) {
				await this.earnEffectRepository.updateAll({
					name: payload.tierName,
					description: payload.tierDescription || '',
					imageURL: payload.tierImageUrl,
				}, {
					id: {
						inq: effects.map(x => x.id as number),
					},
				});
			}

			const earns = await this.loyaltyEarnRepository.find({
				where: {
					id: { inq: effects.map(x => x.loyaltyEarnId as number) },
				},
			});

			if (earns && earns.length > 0) {
				await this.loyaltyEarnRepository.updateAll({
					description: payload.tierDescription || '',
					imageURL: payload.tierImageUrl,
					active: payload.enabled,
				}, {
					id: {
						inq: earns.map(x => x.id as number),
					},
				});
			}

			const conditions = await this.earnConditionRepository.find({
				where: {
					loyaltyEarnId: {
						inq: earns.map(x => x.id as number),
					},
				},
			});

			if (conditions && conditions.length > 0) {
				await this.earnConditionRepository.updateAll({
					amount: payload.minPoints,
				}, {
					id: {
						inq: conditions.map(x => x.id as number),
					},
				});
			}
		}
	}

	this.tierService.reassignUsersForAllTiers(orgId).catch(e =>
		console.error('Error reassigning users to tiers', e)
	);
  }



  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
  })
  @get('/tiers')
  @skipGuardCheck()
  async getTiers(
	@injectUserOrgId() orgId: number,
  ) {
	const programs = await this.loyaltyProgramRepository.find({
		where: {
			orgId,
		},
	});

	const campaigns = await this.loyaltyCampaignRepository.find({
		where: {
			loyaltyProgramId: {
				inq: programs.map(x => x.id as number),
			},
		},
		include: [
			{
				relation: 'loyaltyEarns',
				scope: {
					include: [
						{
							relation: 'earnEffects',
							scope: {
								include: [
									{
										relation: 'loyaltyCurrency',
										scope: {
											fields: {
												conversionToUSD: false
											}
										}
									},
									{
										relation: 'loyaltyRewardDefinition',
										scope: {
											include: [
												{
													relation: 'rewardCoupon',
												}
											]
										}
									}
								]
							}
						},
						{
							relation: 'earnConditions'
						}
					],
					where: {
						// active: { inq: [null, true] },
						recommendationState: { inq: ['NONE', 'APPROVED_RECOMMENDATION'] }
					}
				}
			},
			// {
			// 	relation: 'loyaltyRedemptionShopItems',
				// scope: {
				// 	include: [
				// 		{
				// 			relation: 'loyaltyRewardDefinition',
				// 			scope: {
				// 				fields: {
				// 					startingInventory: false,
				// 					redeemed: false,
				// 					granted: false,
				// 				},
				// 				include: [{relation: 'rewardCoupon', }]
				// 			}
				// 		},
				// 	],
				// 	where: {
				// 		active: { inq: [null, true] } //To support old campaigns that don't have active field
				// 	}
				// }
			// },
			// {
			// 	relation: "staticEffects",
			// },
			{
				relation: 'vipTier',
				// scope: {
				// 	include: [{
				// 		relation: 'loyaltyCampaign',
				// 		scope: {
				// 			include: [{
				// 				relation: 'loyaltyEarns',
				// 			}, {
				// 				relation: 'staticEffects',
				// 			}, {
				// 				relation: 'loyaltyRedemptionShopItems',
				// 			}]
				// 		}
				// 	}]
				// }
			}
		]
	});

	const tiers = campaigns.filter(x => x.vipTier).map(x => x.vipTier!);

	const entryConditionMap = new Map<number, Array<EarnCondition>>();
	campaigns
		.map(x => x.loyaltyEarns)
		.flat()
		.filter(x => x?.earnEffects?.some?.(y => (y as EarnEffectWithRelations)?.loyaltyRewardDefinition?.rewardCoupon?.amountType === 'vip-tier'))
		.forEach(earn => {
			const effect = (earn?.earnEffects?.find?.(y => (y as EarnEffectWithRelations)?.loyaltyRewardDefinition?.rewardCoupon?.amountType === 'vip-tier'));
			const coupon = (effect as EarnEffectWithRelations)?.loyaltyRewardDefinition?.rewardCoupon;
			const vipTierId = coupon?.amount;

			const condition = earn?.earnConditions?.find?.(x => x.type === 'vip-entry');
			if (vipTierId && condition) {
				if (!entryConditionMap.has(vipTierId)) {
					entryConditionMap.set(vipTierId, []);
				}
				entryConditionMap.get(vipTierId)!.push(condition);
			}
		});

	return tiers.map(x => ({
		...x,
		entryConditions: entryConditionMap.get(x.id as number) || [],
	}));
  }

  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
  })
  @del('/tier/{id}')
  @skipGuardCheck()
  async deleteTier(
	@param.path.number('id') id: number,
	@injectUserOrgId() orgId: number,
  ) {
	const tier = await this.vipTierRepository.findOne({
		where: {
			id,
		},
	});

	if (!tier) {
		throw new HttpErrors.NotFound('Tier not found');
	}
	await this.vipTierRepository.deleteById(id);


	try {

		const coupons = await this.rewardCouponRepository.find({
			where: {
				amount: tier.id,
				amountType: 'vip-tier',
			},
		});

		if (!coupons || coupons.length === 0) {
			return;
		}

		if (coupons && coupons.length > 0) {
			await this.rewardCouponRepository.deleteAll({
				id: {
					inq: coupons.map(x => x.id as number),
				},
			});
		}

		const rewards = await this.loyaltyRewardDefinitionRepository.find({
			where: {
				rewardCouponId: {
					inq: coupons.map(x => x.id as number),
				},
			},
		});

		if (rewards && rewards.length > 0) {
			await this.loyaltyRewardDefinitionRepository.deleteAll({
				id: {
					inq: rewards.map(x => x.id as number),
				},
			});
		}

		const effects = await this.earnEffectRepository.find({
			where: {
				loyaltyRewardDefinitionId: {
					inq: rewards.map(x => x.id as number),
				},
			},
		});

		if (effects && effects.length > 0) {
			await this.earnEffectRepository.deleteAll({
				id: {
					inq: effects.map(x => x.id as number),
				},
			});
		}

		const earns = await this.loyaltyEarnRepository.find({
			where: {
				id: { inq: effects.map(x => x.loyaltyEarnId as number) },
			},
		});

		await this.loyaltyEarnRepository.deleteAll({
			id: {
				inq: earns.map(x => x.id as number),
			},
		});

		const conditions = await this.earnConditionRepository.find({
			where: {
				loyaltyEarnId: {
					inq: earns.map(x => x.id as number),
				},
			},
		});

		if (conditions && conditions.length > 0) {
			await this.earnConditionRepository.deleteAll({
				id: {
					inq: conditions.map(x => x.id as number),
				},
			});
		}
	} catch (e) {
		console.error('Error deleting VIP earns', e);
	}


	const campaign = await this.loyaltyCampaignRepository.findOne({
		where: {
			id: tier.loyaltyCampaignId,
		},
	});


	if (!campaign) {
		return;
	}

	await this.loyaltyCampaignRepository.deleteById(campaign.id);


	this.tierService.reassignUsersForAllTiers(orgId).catch(e =>
		console.error('Error reassigning users to tiers', e)
	);
  }


  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
  })
  @post('/tier/{id}/enable/{enable}')
  @skipGuardCheck()
  async enableDisableTierApi(
	@param.path.number('id') id: number,
	@injectUserOrgId() orgId: number,
	@param.path.boolean('enable') enable: boolean,
  ) {
	const result = await this.enableDisableTier(id, orgId, enable);

	this.tierService.reassignUsersForAllTiers(orgId).catch(e =>
		console.error('Error reassigning users to tiers', e)
	);

	return result;
  }


  async enableDisableTier(
	id: number,
	orgId: number,
	enable: boolean,
  ) {
	const tier = await this.vipTierRepository.findOne({
		where: {
			id,
		},
	});

	if (!tier) {
		throw new HttpErrors.NotFound('Tier not found');
	}


	try {

		const coupons = await this.rewardCouponRepository.find({
			where: {
				amount: tier.id,
				amountType: 'vip-tier',
			},
		});


		if (coupons && coupons.length > 0) {

			const rewards = await this.loyaltyRewardDefinitionRepository.find({
				where: {
					rewardCouponId: {
						inq: coupons.map(x => x.id as number),
					},
				},
			});

			const effects = await this.earnEffectRepository.find({
				where: {
					loyaltyRewardDefinitionId: {
						inq: rewards.map(x => x.id as number),
					},
				},
			});

			const earns = await this.loyaltyEarnRepository.find({
				where: {
					id: { inq: effects.map(x => x.loyaltyEarnId as number) },
				},
			});

			if (earns && earns.length > 0) {
				await this.loyaltyEarnRepository.updateAll({
					active: enable,
				}, {
					id: {
						inq: earns.map(x => x.id as number),
					},
				});
			}
		}
	} catch (e) {
		console.error('Error enabling/disabling VIP earns', e);
	}


	const campaign = await this.loyaltyCampaignRepository.findOne({
		where: {
			id: tier.loyaltyCampaignId,
		},
	});

	if (campaign) {
		campaign.active = enable;
		await this.loyaltyCampaignRepository.update(campaign);
	}
  }

  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
  })
  @post('/vip/setEnabledAndLive')
  @skipGuardCheck()
  async enableDisableVip(
	@param.query.boolean('enabled') enabled: boolean,
	@param.query.boolean('live') live: boolean,
	@injectUserOrgId() orgId: number,
  ) {
		if (!enabled) {
			live = false;
		}

		const programs = await this.loyaltyProgramRepository.find({
			where: {
				orgId,
			},
		});

		const campaigns = await this.loyaltyCampaignRepository.find({
			where: {
				loyaltyProgramId: {
					inq: programs.map(x => x.id as number),
				},
				vipTier: { neq: null as any }
			},
			include: [
				{
					relation: 'vipTier',
				}
			]
		});

		const tiers = campaigns.map(x => x.vipTier!);

		this.featureSettingController.createOrUpdateFeatureSetting({
			name: 'vip',
			enabled,
			live,
		}, orgId);

		for (const tier of tiers) {
			const id = tier?.id as number;
			if (id) {
				await this.enableDisableTier(id, orgId, enabled);
			}
		}

		this.tierService.reassignUsersForAllTiers(orgId).catch(e =>
			console.error('Error reassigning users to tiers', e)
		);
  }

}
