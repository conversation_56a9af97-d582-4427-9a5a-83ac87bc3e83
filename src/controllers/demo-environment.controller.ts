import {authenticate} from '@loopback/authentication';
import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
  api,
  HttpErrors,
} from '@loopback/rest';
import {DemoEnvironment, UserWithPassword} from '../models';
import {DemoEnvironmentRepository, LoyaltyProgramRepository,
	LoyaltyCurrencyRepository, LoyaltyEarnRepository,
	OrganizationPlanRepository, RaleonUserIdentityRepository,
	EarnEffectRepository, EarnConditionRepository,
	RaleonUserRepository, LoyaltyCampaignRepository} from '../repositories';
import {SecurityBindings} from '@loopback/security';
import {User} from '@loopback/authentication-jwt';
import {inject, service} from '@loopback/core';
import {authorize} from '@loopback/authorization';
import {basicAuthorization, UserManagementService} from '../services';
import {GuardSkipStrategy, guardStrategy, skipGuardCheck} from '../interceptors';
import {OrganizationRepository} from '../repositories';
import {UserRepository} from '../repositories';
import {UserCredentialsRepository} from '../repositories/user-credentials.repository';

@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class DemoEnvironmentController {
  constructor(
    @inject(SecurityBindings.USER, {optional: true})
    private user: User,
    @repository(DemoEnvironmentRepository)
    public demoEnvironmentRepository: DemoEnvironmentRepository,
    @repository(OrganizationRepository)
    public organizationRepository: OrganizationRepository,
    @repository(UserRepository)
    public userRepository: UserRepository,
    @repository(UserCredentialsRepository)
    public userCredentialsRepository: UserCredentialsRepository,
	@service(UserManagementService)
	public userManagementService: UserManagementService,
	@repository(OrganizationPlanRepository)
	public organizationPlanRepository: OrganizationPlanRepository,
	@repository(LoyaltyProgramRepository)
	public loyaltyProgramRepository: LoyaltyProgramRepository,
	@repository(RaleonUserIdentityRepository)
	public raleonUserIdentityRepository: RaleonUserIdentityRepository,
	@repository(RaleonUserRepository)
	public raleonUserRepository: RaleonUserRepository,
	@repository(LoyaltyCampaignRepository)
	public loyaltyCampaignRepository: LoyaltyCampaignRepository,
	@repository(LoyaltyCurrencyRepository)
	public loyaltyCurrencyRepository: LoyaltyCurrencyRepository,
	@repository(LoyaltyEarnRepository)
	public loyaltyEarnRepository: LoyaltyEarnRepository,
	@repository(EarnEffectRepository)
	public earnEffectRepository: EarnEffectRepository,
	@repository(EarnConditionRepository)
	public earnConditionRepository: EarnConditionRepository,
  ) {}

  @authenticate('jwt')
  @authorize({
	allowedRoles: ['raleon-admin'],
	voters: [basicAuthorization],
  })
  @post('/demo-environments')
  @response(200, {
    description: 'DemoEnvironment model instance',
    content: {'application/json': {schema: getModelSchemaRef(DemoEnvironment)}},
  })
  @skipGuardCheck()
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DemoEnvironment, {
            title: 'NewDemoEnvironment',
            exclude: ['id'],
          }),
        },
      },
    })
    demoEnvironment: Omit<DemoEnvironment, 'id'>,
  ): Promise<DemoEnvironment> {
    // Create a new organization
    const organization = await this.organizationRepository.create({
      name: `Demo Organization ${new Date().toISOString()}`,
      dev: true,
	  branding: '{"logoUrl":"https://d3q4ufbgs1i4ak.cloudfront.net/raleon_icon.png","colors":{"backgroundColor":"#ffffff","useBackgroundColor":true,"textColor":"#333333","buttonTextColor":"#ffffff","buttonBackgroundColor":"#007b5f","linkColor":"#007b5f","accentColor":{"from":"#ff6347","to":"#ff4500"},"secondaryColor":"#ffb347","warningColor":"#ff4500"},"launcher":{"callToAction":"JOIN OUR LOYALTY PROGRAM","styling":{"textColor":"#ffffff","backgroundColor":"#333333"}},"guest":{"content":{"title":"JOIN COMMUNITY","subtitle":"You receive rewards by completing ways to earn and making purchases. Collect XP to move across levels or spend it on extra rewards!","benefitsTitle":"Member Benefits"},"heroImageSearchText":"hot air balloon","heroImageUrl":"https://images.unsplash.com/photo-1497531551184-06b252e1bee1?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w1NTg1NjV8MHwxfHNlYXJjaHwxfHxob3QlMjBhaXIlMjBiYWxsb29ufGVufDB8MHx8fDE3MzcwNTc1ODh8MA&ixlib=rb-4.0.3&q=80&w=400"},"member":{"content":{"rewardsTitle":"Your Rewards","wteTitle":"Ways to Earn","rewardShopTitle":"Available Rewards"}}}',
      isDemoOrg: true
    });

	try {
		await this.userManagementService.createNewUser(new UserWithPassword({
			email: demoEnvironment.defaultEmail,
			password: demoEnvironment.defaultPassword,
			organizationId: organization.id,
			firstName: 'Demo',
			lastName: 'User',
			roles: ['admin']
		}));
	} catch (e) {
		throw new HttpErrors.BadRequest(e);
	}

    // Update demo environment with organization ID
    demoEnvironment.orgId = organization.id;


	//Lets create an organizationPlan of plan 11 (enterprise so nothing is locked)
	await this.organizationPlanRepository.create({
		orgId: organization.id,
		planId: 15,
		status: 'ACTIVE'
	});

	//Lets create a base loyalty program for the organization
	const loyaltyProgram = await this.loyaltyProgramRepository.create({
		orgId: organization.id,
		name: 'Default Loyalty Program',
		active: true
	});

	const loyaltyCampaign = await this.loyaltyCampaignRepository.create({
		loyaltyProgramId: loyaltyProgram.id,
		name: 'Default Campaign',
		active: true,
		loyaltySegment: 'Everyone',
		evergreen: true
	});

	const loyaltyCurrency = await this.loyaltyCurrencyRepository.create({
		loyaltyProgramId: loyaltyProgram.id,
		name: 'Points',
		abbreviatedName: 'PTS',
		conversionToUSD: 1
	});

	let loyaltyEarn = await this.loyaltyEarnRepository.create({
		name: 'Demo Earn'
	})

	let earnCondition = await this.earnConditionRepository.create({
		type: 'triggered',
		triggeredEvent: 'orders/create',
		loyaltyEarnId: loyaltyEarn.id,
		variable: 'purchaseCount',
		operator: '==',
		amount: 1
	})

	let earnEffect = await this.earnEffectRepository.create({
		pointsPerDollar: 1,
		loyaltyCurrencyId: loyaltyCurrency.id,
		loyaltyEarnId: loyaltyEarn.id,
		type: 'points-per-dollar'
	})

	if(organization.id) {
		await this.generateUsers(demoEnvironment.aov || 65, demoEnvironment.ltv || 65, demoEnvironment.numberOfUsers || 100, organization.id);
	}


    // Create demo environment
    return this.demoEnvironmentRepository.create(demoEnvironment);
  }

  async generateUsers(aov: number, ltv: number, count: number, orgId: number) {
	/*
		if (!userResult) {
			console.log("No user found for shopify_id", shopify_id);
			let raleonUser = await this.raleonUserRepository.create({});
			//Create User
			let newUser = {
				orgId: orgId,
				identityType: 'customer_id',
				identityValue: shopify_id,
				raleonUserId: raleonUser.id,
				firstLoginDate: new Date(), //set so we dont grant welcome bonus
			};
			let newIdentity = await this.raleonUserIdentityRepository.create(newUser);

			userResult = await this.raleonUserIdentityRepository.findOne(filter);
		}
	*/

	for (let i = 0; i < count; i++) {
	  // Create a new RaleonUser base record
	  const raleonUser = await this.raleonUserRepository.create({});

	  // Generate random variations around the AOV and LTV
	  const randomAOV = Math.floor(this.generateRandomValue(aov, 0.2)); // 20% variation
	  const totalOrders = Math.floor(Math.random() * 10) + 1;
	  const randomLTV = totalOrders * randomAOV;

	  // Create a unique customer ID
	  const customerId = `DEMO_${Date.now()}_${Math.floor(Math.random() * 10000)}`;


	  //random create date within the last year
	  const randomDate = new Date();
	  randomDate.setFullYear(randomDate.getFullYear() - 1);
	  randomDate.setDate(randomDate.getDate() - Math.floor(Math.random() * 365));
	  const l30emailOpens = Math.random() < 0.2 ? Math.floor(Math.random() * 10) : 0;
	  const l60emailOpens = Math.random() < 0.2 ? Math.floor(Math.random() * 10) + l30emailOpens : l30emailOpens;
	  const l90emailOpens = Math.random() < 0.2 ? Math.floor(Math.random() * 10) + l60emailOpens : l60emailOpens;

	  const l30uniqueEmailOpens = l30emailOpens;
	  const l60uniqueEmailOpens = l60emailOpens - l30emailOpens;
	  const l90uniqueEmailOpens = l90emailOpens - l60emailOpens;

	  //On a scale of 0 - 1000 calculate the following
	  const discountScore = Math.floor(Math.random() * 1000);
	  const refundPropensity = Math.floor(Math.random() * 1000);
	  const replenishmentScore = Math.floor(Math.random() * 1000);
	  const ltvDistribution = Math.floor(Math.random() * 1000);
	  const rebuyPropensity = Math.floor(Math.random() * 1000);
	  const winbackScore = Math.floor(Math.random() * 1000);
	  const engagement60days = Math.floor(Math.random() * 1000);
	  const engagement90days = Math.floor(Math.random() * 1000);
	  const subscriptionPropensity = Math.floor(Math.random() * 1000);
	  //Classify each user based on their total orders and how recent their last order was
	  //Into New Users, Growth, Not Loyal, and Loyal
	  let loyaltySegment = 'New Users';
	  if (totalOrders > 1) {
		loyaltySegment = 'Growth';
	  }
	  if (totalOrders > 5) {
		loyaltySegment = 'Very Loyal';
	  }


	  // 10% of new users are not loyal and if createdDate is greater than 90 days ago
	  if(totalOrders == 1 && Math.random() < 0.5) {
		loyaltySegment = 'Not Loyal';
	  }

	  // Create the user identity
	  const newUser = {
		orgId: orgId,
		identityType: 'customer_id',
		identityValue: customerId,
		raleonUserId: raleonUser.id,
		firstLoginDate: randomDate,
		aov: randomAOV,
		ltv: randomLTV,
		discountScore: discountScore,
		totalOrders: totalOrders,
		totalRefunds: Math.floor(Math.random() < 0.1 ? totalOrders - Math.floor(Math.random() * totalOrders) : 0),
		has_abandoned_checkout: Math.random() < 0.1 ? Math.floor(Math.random() * 10) : 0,
		email_total_opensl90: l90emailOpens,
		email_total_opensl60: l60emailOpens,
		email_total_opensl30: l30emailOpens,
		email_unique_opensl90: l90uniqueEmailOpens,
		email_unique_opensl60: l60uniqueEmailOpens,
		email_unique_opensl30: l30uniqueEmailOpens,
		revenue: randomLTV,
		refundPropensity: refundPropensity,
		replenishmentScore: replenishmentScore,
		ltvDistribution: ltvDistribution,
		daysSinceLastSubscription: Math.random() < 0.1 ? Math.floor(Math.random() * 365) : 0,
		rebuypropensity: rebuyPropensity,
		winbackScore: winbackScore,
		engagement60days: engagement60days,
		engagement90days: engagement90days,
		subscriptionpropensity: subscriptionPropensity,
		churnRisk: Math.floor(Math.random() * 1000),
		engagement30days: Math.floor(Math.random() * 1000),
		loyaltySegment: loyaltySegment,
		isDemoUser: true
	  };
	  await this.raleonUserIdentityRepository.create(newUser);
	}
  }
	generateRandomValue(aov: number, variation: number) {
		return aov * (1 - variation + Math.random() * 2 * variation);
	}

  @authenticate('jwt')
  @authorize({
	allowedRoles: ['raleon-admin'],
	voters: [basicAuthorization],
  })
  @skipGuardCheck()
  @get('/demo-environments')
  @response(200, {
    description: 'Array of DemoEnvironment model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(DemoEnvironment, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(DemoEnvironment) filter?: Filter<DemoEnvironment>,
  ): Promise<DemoEnvironment[]> {
    return this.demoEnvironmentRepository.find(filter);
  }

  @authenticate('jwt')
  @authorize({
	allowedRoles: ['raleon-admin'],
	voters: [basicAuthorization],
  })
  @skipGuardCheck()
  @get('/demo-environments/{id}')
  @response(200, {
    description: 'DemoEnvironment model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(DemoEnvironment, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(DemoEnvironment, {exclude: 'where'}) filter?: FilterExcludingWhere<DemoEnvironment>
  ): Promise<DemoEnvironment> {
    return this.demoEnvironmentRepository.findById(id, filter);
  }

  @authenticate('jwt')
  @authorize({
	allowedRoles: ['raleon-admin'],
	voters: [basicAuthorization],
  })
  @skipGuardCheck()
  @del('/demo-environments/{id}')
  @response(204, {
    description: 'DemoEnvironment DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    // Get the demo environment first to access its orgId
    const demoEnvironment = await this.demoEnvironmentRepository.findById(id);

	await this.organizationRepository.deleteById(demoEnvironment.orgId);

    if (demoEnvironment.orgId) {
      // Find and delete all raleonuseridentities for this org
      const userIdentities = await this.raleonUserIdentityRepository.find({
        where: {
          orgId: demoEnvironment.orgId
        }
      });

      // Delete all raleonuser records and their identities
      for (const identity of userIdentities) {
        if (identity.raleonUserId) {
          await this.raleonUserRepository.deleteById(identity.raleonUserId);
        }
        await this.raleonUserIdentityRepository.deleteById(identity.id);
      }
    }

    // Finally delete the demo environment itself
    await this.demoEnvironmentRepository.deleteById(id);
  }
}
