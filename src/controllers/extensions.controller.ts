import {
	Count,
	CountSchema,
	Filter,
	FilterExcludingWhere,
	repository,
	Where,
} from '@loopback/repository';
import {
	post,
	param,
	get,
	getModelSchemaRef,
	patch,
	put,
	del,
	requestBody,
	response,
	api,
} from '@loopback/rest';
import {Extensions} from '../models';
import {ExtensionsRepository} from '../repositories';
import {guardStrategy, injectUserOrgId, OrgGuardPropertyStrategy, skipGuardCheck} from '../interceptors';
import {service} from '@loopback/core';
import {ShopifyApiInvoker} from '../services/shopify/shopify-api-invoker.service';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../services/basic.authorizor';


@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardPropertyStrategy<Extensions>({
	repositoryClass: ExtensionsRepository,
	orgIdModelPropertyName: 'organizationId',
}))
export class ExtensionsController {
	constructor(
		@repository(ExtensionsRepository)
		public extensionsRepository: ExtensionsRepository,
		@service(ShopifyApiInvoker)
		private shopifyApiInvoker: ShopifyApiInvoker,
	) { }

	@skipGuardCheck()
	@get('free-gift-enabled', {
		responses: {
			'200': {
				description: 'Free Gift Extension Enabled',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async freeGiftEnabled(
		@param.query.number('orgId') orgId: number,
	): Promise<any> {
		const extension = await this.extensionsRepository.findOne({
			where: {
				organizationId: orgId,
				type: 'free-gift',
			},
		});
		if (!extension || !extension.enabled) {
			return { enabled: false };
		}

		let params = extension.externalId ? `?code=${extension.externalId}` : '';

		let data = await this.shopifyApiInvoker.invokeAdminApi(
			orgId,
			`/free-gift${params}`,
			'GET',
		);

		if (typeof data === 'string') {
			data = JSON.parse(data);
		}

		return {
			enabled: extension?.enabled,
			config: {
				minimumOrderAmount: data.config.minimumOrderAmount,
				productVariantId: data.config.freeProductVariantId.match(/\d+/)[0],
				price: data.config.productVariantPrice,
				discountCode: extension.externalId ? extension.externalId : undefined,
				secondaryProductVariantId: data.config.secondaryProductVariantId ? data.config.secondaryProductVariantId.match(/\d+/)[0] : undefined,
				secondaryProductVariantPrice: data.config.secondaryProductVariantPrice || undefined,
			}
		};
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/extensions/free-gift', {
		responses: {
			'200': {
				description: 'Creates a free gift cart transformation',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async createFreeGift(
		@requestBody({
			content: {
				'application/json': {
					schema: {},
				},
			},
		}) body: any,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		if (!body.functionId ||
			!body.productVariantId?.match(/\d+/)[0] ||
			!body.price
		) {
			return { status: 400, message: 'Missing required fields' };
		}

		const params = {
			functionId: body.functionId,
			minimumOrderAmount: body.minimumOrderAmount,
			productVariantId: body.productVariantId.match(/\d+/)[0],
			price: body.price,
			forceDiscountCode: body.forceDiscountCode || false,
			secondaryProductVariantId: body.secondaryProductVariantId ? body.secondaryProductVariantId.match(/\d+/)[0] : undefined,
			secondaryProductVariantPrice: body.secondaryProductVariantPrice || undefined,
			disableCouponStacking: body.disableCouponStacking || false,
			isShopifyPlus: body.isShopifyPlus || false,
		};

		const response = await this.shopifyApiInvoker.invokeAdminApi(
			orgId,
			'/free-gift',
			'POST',
			JSON.stringify(params),
		);

		if(response.success) {
			let externalId = undefined;
			if (response.codeData) {
				externalId = response
					.codeData
					.data
					.discountCodeAppCreate
					.codeAppDiscount
					.codes.nodes[0].code;
			}

			this.extensionsRepository.create({
				organizationId: orgId,
				enabled: true,
				type: 'free-gift',
				externalId,
				availableExtensionsId: 2,
			});
		}
		return { status: response.status, message: 'Successfully Configured Free Gift' };
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/extensions/free-gift', {
		responses: {
			'200': {
				description: 'Creates a free gift cart transformation',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async getFreeGift(
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		const extension = await this.extensionsRepository.findOne({
			where: {
				organizationId: orgId,
				type: 'free-gift',
			},
		});

		if (!extension || !extension.enabled) {
			return { enabled: false };
		}

		const params = extension.externalId ? `?code=${extension.externalId}` : '';
		const response = await this.shopifyApiInvoker.invokeAdminApi(
			orgId,
			`/free-gift${params}`,
			'GET',
		);

		return response;
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@del('/extensions/free-gift', {
		responses: {
			'200': {
				description: 'Creates a free gift cart transformation',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async deleteFreeGift(
		@requestBody({
			content: {
				'application/json': {
					schema: {},
				},
			},
		}) body: any,
		@injectUserOrgId() orgId: number,
	): Promise<any> {

		if (!body.extensionId) {
			return { status: 400, message: 'Missing required fields' };
		}

		const extension = await this.extensionsRepository.findById(body.extensionId);

		let deleteBody = JSON.stringify({
			functionId: body.functionId,
			// functionId: '811d87fa-5a00-41e9-ba01-7ba1e61a60cf'
		});

		if (extension.externalId) {
			deleteBody = JSON.stringify({
				code: extension.externalId,
			});
		}

		const response = await this.shopifyApiInvoker.invokeAdminApi(
			orgId,
			'/free-gift',
			'DELETE',
			deleteBody,
		);

		await this.extensionsRepository.deleteById(body.extensionId);

		return { status: response.status, message: 'Successfully Deleted Free Gift' };
	}
}
