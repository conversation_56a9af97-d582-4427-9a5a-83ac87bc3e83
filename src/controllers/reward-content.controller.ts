import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
	api,
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {guardStrategy, OrgGuardMultiHopPropertyStrategy, skipGuardCheck} from '../interceptors';
import {
  Reward,
  Content,
  Campaign,
  Quest,
} from '../models';
import {QuestRepository, RewardRepository} from '../repositories';
import {basicAuthorization} from '../services';

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardMultiHopPropertyStrategy<Reward, Quest, Campaign>({
	repositoryClass: RewardRepository,
	firstHopIdPropertyName: 'questId',
	firstHopRepositoryClass: QuestRepository,
	inclusionChainAfterFirstHop: {relation: "campaign"},
	lastHopOrgIdPropertyName: 'orgId',
}))
export class RewardContentController {
	constructor(
		@repository(RewardRepository) protected rewardRepository: RewardRepository,
	) { }

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck() //TODO: update
	@get('/rewards/{id}/content', {
		responses: {
			'200': {
				description: 'Reward has one Content',
				content: {
					'application/json': {
						schema: getModelSchemaRef(Content),
					},
				},
			},
		},
	})
	async get(
		@param.path.number('id') id: number,
		@param.query.object('filter') filter?: Filter<Content>,
	): Promise<Content> {
		return this.rewardRepository.content(id).get(filter);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck() //TODO: update
	@post('/rewards/{id}/content', {
		responses: {
			'200': {
				description: 'Reward model instance',
				content: {'application/json': {schema: getModelSchemaRef(Content)}},
			},
		},
	})
	async create(
		@param.path.number('id') id: typeof Reward.prototype.id,
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(Content, {
						title: 'NewContentInReward',
						exclude: ['id'],
						optional: ['rewardId']
					}),
				},
			},
		}) content: Omit<Content, 'id'>,
	): Promise<Content> {
		return this.rewardRepository.content(id).create(content);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck() //TODO: update
	@patch('/rewards/{id}/content', {
		responses: {
			'200': {
				description: 'Reward.Content PATCH success count',
				content: {'application/json': {schema: CountSchema}},
			},
		},
	})
	async patch(
		@param.path.number('id') id: number,
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(Content, {partial: true}),
				},
			},
		})
		content: Partial<Content>,
		@param.query.object('where', getWhereSchemaFor(Content)) where?: Where<Content>,
	): Promise<Count> {
		return this.rewardRepository.content(id).patch(content, where);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck() //TODO: update
	@del('/rewards/{id}/content', {
		responses: {
			'200': {
				description: 'Reward.Content DELETE success count',
				content: {'application/json': {schema: CountSchema}},
			},
		},
	})
	async delete(
		@param.path.number('id') id: number,
		@param.query.object('where', getWhereSchemaFor(Content)) where?: Where<Content>,
	): Promise<Count> {
		return this.rewardRepository.content(id).delete(where);
	}
}
