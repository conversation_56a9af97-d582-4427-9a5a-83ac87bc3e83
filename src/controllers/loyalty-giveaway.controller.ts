import {Filter, repository} from '@loopback/repository';
import {LoyaltyGiveaway, LoyaltyProgram} from '../models';
import {LoyaltyGiveawayRepository, LoyaltyProgramRepository} from '../repositories';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../services/basic.authorizor';
import {api, get, param, patch, post, requestBody} from '@loopback/rest';
import {OrgGuardSingleHopPropertyStrategy, guardStrategy, injectUserOrgId, modelIdForGuard, restrictReadsWithGuard, skipGuardCheck} from '../interceptors';

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardSingleHopPropertyStrategy<LoyaltyGiveaway, LoyaltyProgram>({
	relatedIdPropertyName: 'loyaltyProgramId',
	relatedOrgIdPropertyName: 'orgId',
	relatedRepositoryClass: LoyaltyProgramRepository,
	repositoryClass: LoyaltyGiveawayRepository,
}))
export class LoyaltyGiveawayController {
	constructor(
		@repository(LoyaltyGiveawayRepository)
		private loyaltyGiveawayRepository: LoyaltyGiveawayRepository,
		@repository(LoyaltyProgramRepository)
		private loyaltyProgramRepository: LoyaltyProgramRepository,
	) { }


	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/giveaway/{id}')
	async getGiveawayById(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyGiveaway)
		id: typeof LoyaltyGiveaway.prototype.id,
	) {
		const giveaway = await this.loyaltyGiveawayRepository.findById(id);
		if (!giveaway) {
			throw new Error('Giveaway not found');
		}
		return giveaway;
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/giveaways')
	@restrictReadsWithGuard({plural: true})
	async getAllGiveaways(
		@param.filter(LoyaltyGiveaway)
		filter?: Filter<LoyaltyGiveaway>,
	) {
		const giveaway = await this.loyaltyGiveawayRepository.find(filter);
		if (!giveaway) {
			throw new Error('Giveaway not found');
		}
		return giveaway;
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/giveaway')
	@skipGuardCheck()
	async createGiveaway(
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						properties: {},
						required: ['name', 'startDate', 'endDate'],
					},
				},
			},
		})
		payload: {name: string; startDate: string; endDate: string;},
		@injectUserOrgId() orgId: number,
	) {
		const loyaltyProgram = await this.loyaltyProgramRepository.findOne({where: {orgId}});
		if (!loyaltyProgram) {
			throw new Error('Loyalty program not found');
		}
		const giveaway = new LoyaltyGiveaway({
			...payload,
			loyaltyProgramId: loyaltyProgram.id,
		});
		return this.loyaltyGiveawayRepository.create(giveaway);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@patch('/giveaway/{id}')
	@skipGuardCheck()
	async updateGiveaway(
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						properties: {},
						required: [],
					},
				},
			},
		})
		payload: Partial<LoyaltyGiveaway>,
		@param.path.number('id') id: number,
	) {
		const giveaway = new LoyaltyGiveaway({
			...payload,
		});
		if (giveaway.id !== id) {
			throw new Error('Id mismatch');
		}
		return this.loyaltyGiveawayRepository.updateById(id, giveaway);
	}
}
