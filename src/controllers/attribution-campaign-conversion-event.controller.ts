import {
	Count,
	CountSchema,
	Filter,
	repository,
	Where,
} from '@loopback/repository';
import {
	api,
	del,
	get,
	getModelSchemaRef,
	getWhereSchemaFor,
	param,
	patch,
	post,
	requestBody,
} from '@loopback/rest';
import {
	AttributionCampaign,
	ConversionEvent,
} from '../models';
import {AttributionCampaignRepository} from '../repositories';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {injectUserOrgId, GuardSkipStrategy, guardStrategy, skipGuardCheck} from '../interceptors';
import {basicAuthorization} from '../services';

@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class AttributionCampaignConversionEventController {
	constructor(
		@repository(AttributionCampaignRepository) protected attributionCampaignRepository: AttributionCampaignRepository,
	) { }

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/attribution-campaigns/{id}/conversion-event', {
		responses: {
			'200': {
				description: 'AttributionCampaign has one ConversionEvent',
				content: {
					'application/json': {
						schema: getModelSchemaRef(ConversionEvent),
					},
				},
			},
		},
	})
	async get(
		@param.path.number('id') id: number,
		@param.query.object('filter') filter?: Filter<ConversionEvent>,
	): Promise<ConversionEvent> {
		return this.attributionCampaignRepository.conversionEvent(id).get(filter);
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/attribution-campaigns/{id}/conversion-event', {
		responses: {
			'200': {
				description: 'AttributionCampaign model instance',
				content: {'application/json': {schema: getModelSchemaRef(ConversionEvent)}},
			},
		},
	})
	async create(
		@param.path.number('id') id: typeof AttributionCampaign.prototype.id,
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(ConversionEvent, {
						title: 'NewConversionEventInAttributionCampaign',
						exclude: ['id'],
						optional: ['attributionCampaignId']
					}),
				},
			},
		}) conversionEvent: Omit<ConversionEvent, 'id'>,
	): Promise<ConversionEvent> {
		return this.attributionCampaignRepository.conversionEvent(id).create(conversionEvent);
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@patch('/attribution-campaigns/{id}/conversion-event', {
		responses: {
			'200': {
				description: 'AttributionCampaign.ConversionEvent PATCH success count',
				content: {'application/json': {schema: CountSchema}},
			},
		},
	})
	async patch(
		@param.path.number('id') id: number,
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(ConversionEvent, {partial: true}),
				},
			},
		})
		conversionEvent: Partial<ConversionEvent>,
		@param.query.object('where', getWhereSchemaFor(ConversionEvent)) where?: Where<ConversionEvent>,
	): Promise<Count> {
		return this.attributionCampaignRepository.conversionEvent(id).patch(conversionEvent, where);
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@del('/attribution-campaigns/{id}/conversion-event', {
		responses: {
			'200': {
				description: 'AttributionCampaign.ConversionEvent DELETE success count',
				content: {'application/json': {schema: CountSchema}},
			},
		},
	})
	async delete(
		@param.path.number('id') id: number,
		@param.query.object('where', getWhereSchemaFor(ConversionEvent)) where?: Where<ConversionEvent>,
	): Promise<Count> {
		return this.attributionCampaignRepository.conversionEvent(id).delete(where);
	}
}
