import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  api,
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  Organization,
  OrganizationPlannerPlan,
} from '../models';
import {OrganizationPlannerPlanRepository, OrganizationRepository} from '../repositories';
import {modelForGuard, modelIdForGuard, OrgGuardPropertyStrategy, restrictReadsWithGuard, skipGuardCheck} from '../interceptors/crud-guard.interceptor';
import {injectGuardedFilter, injectUserOrgId, guardStrategy} from '../interceptors/crud-guard.interceptor';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../services/basic.authorizor';

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardPropertyStrategy<Organization>({
  orgIdModelPropertyName: 'id',
  repositoryClass: OrganizationRepository
}))
export class OrganizationOrganizationPlannerPlanController {
  constructor(
    @repository(OrganizationRepository) protected organizationRepository: OrganizationRepository,
  ) { }

  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  @get('/organizations/organization-planner-plans', {
    responses: {
      '200': {
        description: 'Array of Organization has many OrganizationPlannerPlan',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(OrganizationPlannerPlan)},
          },
        },
      },
    },
  })
  async find(
    @param.query.object('filter') filter?: Filter<OrganizationPlannerPlan>,
    @injectUserOrgId() orgId?: number
  ): Promise<OrganizationPlannerPlan[]> {
    // Prepare filter object with default ordering and includes
    const finalFilter: Filter<OrganizationPlannerPlan> = {
      order: ['startdate ASC'],
      include: [
        {
          relation: 'plannerPlanVersions',
          scope: {
            include: [{
              relation: 'plannerCampaigns',
              scope: {
                where: { or: [{ isDeleted: false }, { isDeleted: { eq: null } }] },
                include: [{
                  relation: 'task'
                }]
              }
            }]
          }
        }
      ],
    };

    // If a filter was provided, merge it with our defaults
    if (filter) {
      // If caller provided 'where' clause, merge it with our defaults
      if (filter.where) {
        finalFilter.where = filter.where;
      }
      
      // If caller provided custom 'order', use that instead of default
      if (filter.order) {
        finalFilter.order = filter.order;
      }
      
      // Preserve other filter properties (like 'limit', 'skip', etc.)
      if (filter.limit) finalFilter.limit = filter.limit;
      if (filter.skip) finalFilter.skip = filter.skip;
      if (filter.fields) finalFilter.fields = filter.fields;
    }

    return this.organizationRepository.organizationPlannerPlans(orgId).find(finalFilter);
  }

  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  @get('/organizations/organization-planner-plans/{id}', {
    responses: {
      '200': {
        description: 'Array of Organization has many OrganizationPlannerPlan',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(OrganizationPlannerPlan)},
          },
        },
      },
    },
  })
  async findOne(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<OrganizationPlannerPlan>,
    @injectUserOrgId() orgId?: number
  ): Promise<OrganizationPlannerPlan[]> {
    // Prepare filter object with default where and includes
    const finalFilter: Filter<OrganizationPlannerPlan> = {
      where: {id: id},
      include: [
        {
          relation: 'plannerPlanVersions',
          scope: {
            include: [{
              relation: 'plannerCampaigns',
              scope: {
                where: { or: [{ isDeleted: false }, { isDeleted: { eq: null } }] },
                include: [{
                  relation: 'task'
                }]
              }
            }]
          }
        }
      ],
    };

    // If a filter was provided, merge it with our defaults
    if (filter) {
      // If caller provided 'where' clause, merge it with our id filter
      if (filter.where) {
        finalFilter.where = {and: [
          {id: id},
          filter.where
        ]};
      }
      
      // Preserve other filter properties (like 'limit', 'skip', etc.)
      if (filter.order) finalFilter.order = filter.order;
      if (filter.limit) finalFilter.limit = filter.limit;
      if (filter.skip) finalFilter.skip = filter.skip;
      if (filter.fields) finalFilter.fields = filter.fields;
    }

    return this.organizationRepository.organizationPlannerPlans(orgId).find(finalFilter);
  }

  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  @post('/organizations/{id}/organization-planner-plans', {
    responses: {
      '200': {
        description: 'Organization model instance',
        content: {'application/json': {schema: getModelSchemaRef(OrganizationPlannerPlan)}},
      },
    },
  })
  async create(
    @modelIdForGuard(Organization)
    @param.path.number('id') id: typeof Organization.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(OrganizationPlannerPlan, {
            title: 'NewOrganizationPlannerPlanInOrganization',
            exclude: ['id'],
            optional: ['organizationId']
          }),
        },
      },
    }) organizationPlannerPlan: Omit<OrganizationPlannerPlan, 'id'>,
  ): Promise<OrganizationPlannerPlan> {
    // Set the creation date
    organizationPlannerPlan.createdDate = new Date();
    return this.organizationRepository.organizationPlannerPlans(id).create(organizationPlannerPlan);
  }

  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  @patch('/organizations/{id}/organization-planner-plans', {
    responses: {
      '200': {
        description: 'Organization.OrganizationPlannerPlan PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @modelIdForGuard(Organization)
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(OrganizationPlannerPlan, {partial: true}),
        },
      },
    })
    organizationPlannerPlan: Partial<OrganizationPlannerPlan>,
    @param.query.object('where', getWhereSchemaFor(OrganizationPlannerPlan)) where?: Where<OrganizationPlannerPlan>,
  ): Promise<Count> {
    return this.organizationRepository.organizationPlannerPlans(id).patch(organizationPlannerPlan, where);
  }

  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  @del('/organizations/{id}/organization-planner-plans', {
    responses: {
      '200': {
        description: 'Organization.OrganizationPlannerPlan DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @modelIdForGuard(Organization)
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(OrganizationPlannerPlan)) where?: Where<OrganizationPlannerPlan>,
  ): Promise<Count> {
    return this.organizationRepository.organizationPlannerPlans(id).delete(where);
  }
}
