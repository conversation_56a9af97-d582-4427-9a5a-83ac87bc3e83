import {authenticate} from '@loopback/authentication';
import {User} from '@loopback/authentication-jwt';
import {authorize} from '@loopback/authorization';
import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where
} from '@loopback/repository';
import {
  api,
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody
} from '@loopback/rest';
import {SecurityBindings} from '@loopback/security';
import {injectGuardedFilter, injectGuardedWhere, injectUserOrgId, modelIdForGuard, OrgGuardSingleHopPropertyStrategy, skipGuardCheck} from '../interceptors/crud-guard.interceptor';
import {modelForGuard} from '../interceptors/crud-guard.interceptor';
import {guardStrategy, restrictReadsWithGuard} from '../interceptors/crud-guard.interceptor';
import {
  Dashboard,
  Widget} from '../models';
import {DashboardRepository, UserRepository, WidgetRepository} from '../repositories';
import {basicAuthorization} from '../services';

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardSingleHopPropertyStrategy<Widget, Dashboard>({
	relatedIdPropertyName: 'dashboardId',
	relatedOrgIdPropertyName: 'organizationId',
	repositoryClass: WidgetRepository,
	relatedRepositoryClass: DashboardRepository
}))
export class DashboardWidgetController {
  constructor(
    @repository(DashboardRepository) protected dashboardRepository: DashboardRepository,
	@repository(UserRepository)
	private readonly _userRepository: UserRepository,
	@inject(SecurityBindings.USER, {optional: true}) private _user: User,
    @repository(WidgetRepository) protected widgetRepository: WidgetRepository,
  ) {   }

  @get('/dashboards/{id}/widgets', {
    responses: {
      '200': {
        description: 'Array of Dashboard has many Widget',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Widget)},
          },
        },
      },
    },
  })
  @authenticate('jwt') //Is this working here...
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  @restrictReadsWithGuard({ plural: true })
  async find(
    @param.path.number('id') id: typeof Dashboard.prototype.id,
    @param.query.object('filter') filter?: Filter<Widget>,
  ): Promise<Widget[]> {
    const all = await this.dashboardRepository.widgets(id).find(filter);

	return all;
  }

  @post('/dashboards/{id}/widgets', {
    responses: {
      '200': {
        description: 'Dashboard model instance',
        content: {'application/json': {schema: getModelSchemaRef(Widget)}},
      },
    },
  })
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  async create(
    @param.path.number('id')
	@modelIdForGuard(Dashboard)
	id: typeof Dashboard.prototype.id,

    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Widget, {
            title: 'NewWidgetInDashboard',
            exclude: ['id'],
            optional: ['dashboardId']
          }),
        },
      },
    })
	@modelForGuard(Widget)
	widget: Omit<Widget, 'id'>,
  ): Promise<Widget> {
    return this.dashboardRepository.widgets(id).create(widget);
  }

  @patch('/dashboards/{id}/widgets', {
    responses: {
      '200': {
        description: 'Dashboard.Widget PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  async patch(
	@param.path.number('id')
	@modelIdForGuard(Dashboard)
	id: typeof Dashboard.prototype.id,

    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Widget, {partial: true}),
        },
      },
    })
    widget: Partial<Widget>,
    @param.query.object('where', getWhereSchemaFor(Widget))
	@injectGuardedWhere()
	where?: Where<Widget>,
  ): Promise<Count> {
    return this.dashboardRepository.widgets(id).patch(widget, where);
  }

  @del('/dashboards/{id}/widgets', {
    responses: {
      '200': {
        description: 'Dashboard.Widget DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  async delete(
	@modelIdForGuard(Dashboard)
    @param.path.number('id')
	id: typeof Dashboard.prototype.id,

    @param.query.object('where', getWhereSchemaFor(Widget))
	where?: Where<Widget>,
  ): Promise<Count> {
    return this.dashboardRepository.widgets(id).delete(where);
  }
}
