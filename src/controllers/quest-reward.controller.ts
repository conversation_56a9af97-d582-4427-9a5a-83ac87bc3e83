import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {
	Count,
	CountSchema,
	Filter,
	repository,
	Where,
} from '@loopback/repository';
import {
	api,
	del,
	get,
	getModelSchemaRef,
	getWhereSchemaFor,
	param,
	patch,
	post,
	requestBody,
} from '@loopback/rest';
import {guardStrategy, OrgGuardSingleHopPropertyStrategy, skipGuardCheck} from '../interceptors';
import {
	Quest,
	Reward,
	Campaign,
} from '../models';
import {CampaignRepository, QuestRepository} from '../repositories';
import {basicAuthorization} from '../services';

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardSingleHopPropertyStrategy<Quest, Campaign>({
	repositoryClass: QuestRepository,
	relatedIdPropertyName: 'campaignId',
	relatedRepositoryClass: CampaignRepository,
	relatedOrgIdPropertyName: 'orgId',
}))
export class QuestRewardController {
	constructor(
		@repository(QuestRepository) protected questRepository: QuestRepository,
	) { }

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck() 
	@get('/quests/{id}/rewards', {
		responses: {
			'200': {
				description: 'Array of Quest has many Reward',
				content: {
					'application/json': {
						schema: {type: 'array', items: getModelSchemaRef(Reward)},
					},
				},
			},
		},
	})
	async find(
		@param.path.number('id') id: number,
		@param.query.object('filter') filter?: Filter<Reward>,
	): Promise<Reward[]> {
		return this.questRepository.rewards(id).find(filter);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck() //TODO: update
	@post('/quests/{id}/rewards', {
		responses: {
			'200': {
				description: 'Quest model instance',
				content: {'application/json': {schema: getModelSchemaRef(Reward)}},
			},
		},
	})
	async create(
		@param.path.number('id') id: typeof Quest.prototype.id,
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(Reward, {
						title: 'NewRewardInQuest',
						exclude: ['id'],
						optional: ['questId']
					}),
				},
			},
		}) reward: Omit<Reward, 'id'>,
	): Promise<Reward> {
		return this.questRepository.rewards(id).create(reward);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck() //TODO: update
	@patch('/quests/{id}/rewards', {
		responses: {
			'200': {
				description: 'Quest.Reward PATCH success count',
				content: {'application/json': {schema: CountSchema}},
			},
		},
	})
	async patch(
		@param.path.number('id') id: number,
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(Reward, {partial: true}),
				},
			},
		})
		reward: Partial<Reward>,
		@param.query.object('where', getWhereSchemaFor(Reward)) where?: Where<Reward>,
	): Promise<Count> {
		return this.questRepository.rewards(id).patch(reward, where);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck() //TODO: update
	@del('/quests/{id}/rewards', {
		responses: {
			'200': {
				description: 'Quest.Reward DELETE success count',
				content: {'application/json': {schema: CountSchema}},
			},
		},
	})
	async delete(
		@param.path.number('id') id: number,
		@param.query.object('where', getWhereSchemaFor(Reward)) where?: Where<Reward>,
	): Promise<Count> {
		return this.questRepository.rewards(id).delete(where);
	}
}
