
import {
	Count,
	CountSchema,
	Filter,
	repository,
	Where,
} from '@loopback/repository';
import {
	del,
	get,
	getModelSchemaRef,
	getWhereSchemaFor,
	param,
	patch,
	post,
	requestBody,
	api,
	HttpErrors
} from '@loopback/rest';
import {
	CurrencyWithRelations,
	EarnCondition,
	LoyaltyCampaign,
	LoyaltyEarn,
	LoyaltyProgram,
	LoyaltyRedemptionShopItem,
	Organization,
	RewardCoupon,
	Segment,
	UiCustomerReward,
	UiCustomerRewardRelations,
} from '../models';
import {injectUserOrgId, modelIdForGuard, OrgGuardMultiHopPropertyStrategy, skipGuardCheck} from '../interceptors/crud-guard.interceptor';
import {guardStrategy, GuardSkipStrategy} from '../interceptors/crud-guard.interceptor';
import {AvailableExtensionsRepository, CurrencyRepository, EarnConditionRepository, EarnEffectRepository, ExtensionsRepository, IntegrationRepository, LoyaltyCampaignRepository, LoyaltyEarnRepository, LoyaltyGiveawayRepository, LoyaltyProgramRepository, LoyaltyRedemptionShopItemRepository, LoyaltyRewardDefinitionRepository, LoyaltyStaticEffectRepository, OrganizationIntegrationDetailsRepository, OrganizationRepository, RewardCouponRepository, TranslationStringRepository, UiCustomerActionRepository, UiCustomerRewardRepository, UiShopItemConditionRepository} from '../repositories';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization, TranslationService, UnsplashImageService} from '../services';
import OpenAI from 'openai';
import {service} from '@loopback/core';
import {LoyaltyDetailsController} from './loyalty/loyalty-details.controller';
import {ShopifyApiInvoker} from '../services/shopify/shopify-api-invoker.service';
import {Translation} from 'openai/resources/audio/translations';
import e from 'express';
import {SegmentService} from '../services/shopify/segment.service';
import {FeatureService} from '../services/feature.service';

const openai = new OpenAI({apiKey: '***************************************************'});
const openaiForCurrencyProcessing = new OpenAI({
	apiKey: process.env.DATABASE_NAME === 'raleon'
	 ? '********************************************************'
	 : '********************************************************'
});

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardMultiHopPropertyStrategy<LoyaltyEarn, LoyaltyCampaign, LoyaltyProgram>({
	repositoryClass: LoyaltyEarnRepository,
	firstHopIdPropertyName: 'loyaltyCampaignId',
	firstHopRepositoryClass: LoyaltyCampaignRepository,
	inclusionChainAfterFirstHop: {relation: 'loyaltyProgram'},
	lastHopOrgIdPropertyName: 'orgId',
}))


export class AdminUiController {

	private SOCIAL_MEDIA_WTE_TYPES = ['follow-on-instagram', 'follow-on-tiktok', 'follow-on-facebook', 'follow-facebook-group', 'follow-on-youtube', 'follow-on-custom'];

	constructor(
		@repository(LoyaltyCampaignRepository) protected loyaltyCampaignRepository: LoyaltyCampaignRepository,
		@repository(LoyaltyEarnRepository) protected loyaltyEarnRepository: LoyaltyEarnRepository,
		@repository(EarnConditionRepository) protected earnConditionRepository: EarnConditionRepository,
		@repository(EarnEffectRepository) protected earnEffectRepository: EarnEffectRepository,
		@repository(UiCustomerActionRepository) protected uiCustomerActionRepository: UiCustomerActionRepository,
		@repository(UiCustomerRewardRepository) protected uiCustomerRewardRepository: UiCustomerRewardRepository,
		@repository(RewardCouponRepository) protected rewardCouponRepository: RewardCouponRepository,
		@repository(LoyaltyRewardDefinitionRepository) protected loyaltyRewardDefinitionRepository: LoyaltyRewardDefinitionRepository,
		@repository(UiShopItemConditionRepository) protected uiShopItemConditionRepository: UiShopItemConditionRepository,
		@repository(LoyaltyRedemptionShopItemRepository) protected loyaltyRedemptionShopItemRepository: LoyaltyRedemptionShopItemRepository,
		@repository(LoyaltyProgramRepository) protected loyaltyProgramRepository: LoyaltyProgramRepository,
		@repository(OrganizationRepository) protected organizationRepository: OrganizationRepository,
		@repository(OrganizationIntegrationDetailsRepository) protected organizationIntegrationDetailsRepository: OrganizationIntegrationDetailsRepository,
		@repository(IntegrationRepository) protected integrationRepository: IntegrationRepository,
		@repository(CurrencyRepository) private currencyRepository: CurrencyRepository,
		@service(UnsplashImageService) private unsplashImageService: UnsplashImageService,
		@service(ShopifyApiInvoker) private shopifyApiInvoker: ShopifyApiInvoker,
		@service(TranslationService) private translationService: TranslationService,
		@repository(LoyaltyStaticEffectRepository) private loyaltyStaticEffectRepository: LoyaltyStaticEffectRepository,
		@repository(TranslationStringRepository) private translationStringRepository: TranslationStringRepository,
		@repository(AvailableExtensionsRepository) private availableExtensionsRepository: AvailableExtensionsRepository,
		@repository(ExtensionsRepository) private extensionsRepository: ExtensionsRepository,
		@repository(LoyaltyGiveawayRepository) private loyaltyGiveawayRepository: LoyaltyGiveawayRepository,
		@service(SegmentService) private segmentService: SegmentService,
		@service(FeatureService) private featureService: FeatureService,
	) { }

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/ui-loyalty-earn-details/{campaignId}', {
		responses: {
			'200': {
				description: 'Array of LoyaltyCampaign has many LoyaltyEarn',
				content: {
					'application/json': {
						schema: {type: 'array', items: getModelSchemaRef(LoyaltyEarn)},
					},
				},
			},
		},
	})
	async find(
		@param.path.number('campaignId')
		@modelIdForGuard(LoyaltyCampaign)
		campaignId: number,
		@param.query.object('filter') filter?: Filter<LoyaltyEarn>,
	): Promise<LoyaltyEarn[]> {
		//return this.loyaltyCampaignRepository.loyaltyEarns(id).find(filter);
		let waysToEarn = await this.loyaltyCampaignRepository.loyaltyEarns(campaignId).find({
			include: [
				{
					relation: 'earnEffects',
					scope: {
						include: [
							'loyaltyRewardDefinition'
						]
					}
				},
				{
					relation: 'earnConditions',
				}
			]
		});
		//Filter out any isRecommendation or ignoreRecommendation items
		waysToEarn = waysToEarn.filter((wte: any) => {
			return !wte.isRecommendation && !wte.ignoreRecommendation;
		});
		return waysToEarn;
	}


	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/all-shopify-segments')
	async testSegments(
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		return this.segmentService.getSegments(orgId);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/wte/customer-actions', {
		responses: {
			'200': {
				description: 'Returns available customer actions',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async getCustomerActions(
		@param.query.number('campaignId') campaignId: number,
		@injectUserOrgId() orgId: number,
	): Promise<any[]> {
		let actions =  await this.uiCustomerActionRepository.find({
			where: {
				type: {nin: ['referrer-bonus', 'referred-customer-bonus']}
			},
			include: [
				{ relation: 'uiCustomerActionConditions' }
			],
		})
		let campaign = null;
		if(campaignId) {
			campaign = await this.loyaltyCampaignRepository.findById(campaignId);
		}

		//Lets see if any require integrations to be installed, if so and the integration isn't enabled
		//we need to set the enabled flag to false
		for(var i = 0; i < actions.length; i++) {
			if(actions[i].type == 'timed-purchase' && (campaign == null || campaign.evergreen)) {
				actions[i].enabled = false;
			}
			if(campaign && actions[i].type == 'nth-purchase' && !campaign.evergreen) {
				actions[i].enabled = false;
			}
			if (['welcome-bonus', 'birthday-bonus'].includes(actions[i].type)) {
				actions[i].enabled = await this.featureService.isFeatureAvailable(`wte-${actions[i].type}`, orgId);
			}
			if (actions[i].type.startsWith('follow-')) {
				actions[i].enabled = await this.featureService.isFeatureAvailable(`wte-social-follows`, orgId);
			}
			if(actions[i].integrationCategory) {
				let integration = await this.integrationRepository.find({
					where: {
						category: actions[i].integrationCategory,
					}
				});

				if(!integration || integration.length == 0) {
					actions[i].enabled = false;
				}
				else {
					//We have an integration (metadata) with a matching category, lets see if an org has one
					let hasOrgIntegrationEnabled = false;
					for(var j = 0; j < integration.length; j++) {
						let orgIntegration = await this.organizationIntegrationDetailsRepository.find({
							where: {
								orgId: orgId,
								integrationId: integration[j].id,
								enabled: true
							}
						});

						if(orgIntegration && orgIntegration.length > 0) {
							hasOrgIntegrationEnabled = true;
							break;
						}
					}

					//We want to turn it off only if the org doesn't have it enabled
					if(!hasOrgIntegrationEnabled) {
						actions[i].enabled = false;
					}
				}
			}
		}
		return actions;
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/wte/customer-rewards', {
		responses: {
			'200': {
				description: 'Returns available customer rewards based on customer action',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async getCustomerRewards(
		@requestBody({
			content: {
				'application/json': {
					schema: {},
				},
			},
		}) body: any,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		//actions = [] array of ids
		if (body.actions && body.actions.length > 0) {
			let actions = body.actions;
			let rewards: any[] = [];
			for (var i = 0; i < actions.length; i++) {
				let reward = (await this.uiCustomerActionRepository.uiCustomerRewards(actions[i]).find(
					{
						include: [
							{
								relation: 'uiRewardRestrictions',
							},
							{
								relation: 'uiRewardLimits'
							}
						]
					}
				)) as any;

				for (var j = 0; j < reward.length; j++) {

					if (['free-product', 'percent-off-product'].includes(reward[j].type)) {
						reward[j].enabled = (await this.featureService.isFeatureAvailable(`reward-free-product`, orgId));
					}

				}

				rewards.push(...reward);
			}

			let uniqueRewards = rewards.filter((reward, index, self) =>
				index === self.findIndex((t) => (
					t.id === reward.id
				))
			);

			return uniqueRewards;
		}
		return {
			status: 'error',
			error: 'No actions provided'
		}
	}


	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/extensions/vip-shipping', {
		responses: {
			'200': {
				description: 'Creates a shopify delivery customizations extension',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async createVIPShipping(
		@requestBody({
			content: {
				'application/json': {
					schema: {},
				},
			},
		}) body: any,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		const discount = await this.shopifyApiInvoker.invokeAdminApi(
			orgId,
			'/vip-shipping',
			'POST',
			JSON.stringify({
				functionId: body.functionId,
				metafield: body.metafield,
				segment: body.segment,
			}),
		);

		if(discount) {
			this.extensionsRepository.create({
				organizationId: orgId,
				enabled: true,
				type: 'vip-shipping',
				externalId: discount.id,
				availableExtensionsId: 1
			});
		}

		console.log(discount);
		return discount;
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/extensions/vip-shipping', {
		responses: {
			'200': {
				description: 'Creates a shopify delivery customizations extension',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async getDeliveryCustomizations(
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		const discount = await this.shopifyApiInvoker.invokeAdminApi(
			orgId,
			'/vip-shipping',
			'GET'
		);

		console.log(discount);
		return discount;
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/extensions', {
		responses: {
			'200': {
				description: 'Will return specific extensions available + those that are active and merge them',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async getExtensions(
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		let allAvailableExtensions = await this.availableExtensionsRepository.find();
		let orgExtensions = await this.extensionsRepository.find({
			where: {
				organizationId: orgId
			}
		});

		//Merge the two based on available extensions
		let mergedExtensions = [];
		for (var i = 0; i < allAvailableExtensions.length; i++) {
			let extension = allAvailableExtensions[i];
			let orgExtension = orgExtensions.find((ext) => ext.availableExtensionsId == extension.id);
			if (orgExtension) {
				mergedExtensions.push({
					...extension,
					enabled: orgExtension.enabled,
					externalId: orgExtension.externalId,
					extensionId: orgExtension.id
				});
			}
			else {
				mergedExtensions.push({
					...extension,
					enabled: false
				});
			}
		}

		return mergedExtensions;
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer', 'customer-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/organization/current', {
		responses: {
		  '200': {
			description: 'Current Organization',
			content: {
			  'application/json': {
				schema: getModelSchemaRef(Organization),
			  },
			},
		  },
		},
	  })
	  async getCurrentOrganization(
		@injectUserOrgId() orgId: number,
	  ): Promise<Organization> {
		const organization = await this.organizationRepository.findById(orgId);
		if (!organization) {
			throw new Error('Organization not found');
		}
		return organization;
	  }

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@del('/extensions/vip-shipping', {
		responses: {
			'200': {
				description: 'Creates a shopify delivery customizations extension',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async deleteDeliveryCustomization(
		@requestBody({
			content: {
				'application/json': {
					schema: {},
				},
			},
		}) body: any,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		const discount = await this.shopifyApiInvoker.invokeAdminApi(
			orgId,
			'/vip-shipping',
			'DELETE',
			JSON.stringify({
				id: body.externalId
			}),
		);

		await this.extensionsRepository.deleteById(body.extensionId);

		console.log(discount);
		return discount;
	}


	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/wte/all-customer-rewards', {
		responses: {
			'200': {
				description: 'Returns available customer rewards based on customer action',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async getAllCustomerRewards(
	): Promise<any> {
		return this.uiCustomerRewardRepository.find(
			{
				include: [
					{
						relation: 'uiRewardRestrictions',
					},
					{
						relation: 'uiRewardLimits'
					}
				]
			}
		);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/wte/action-summary', {
		responses: {
			'200': {
				description: 'returns an in progress summary for the way to earn based on the payload provided',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async generateWTESummary(
		@requestBody({
			content: {
				'application/json': {
					schema: {},
				},
			},
		}) body: any,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		try {
			let validProgram = await this.validateCustomerOwnsProgram(body.programId, orgId);
			let validCampaign = await this.validateCampaignBelongsToProgram(body.programId, body.campaignId);
			if (!validCampaign || !validProgram) {
				return {
					status: 'error',
					error: 'Unauthorized'
				}
			}

			let allConditions: any[] = [];
			for (var i = 0; i < body.condition.length; i++) {
				if (body.condition[i]) {
					for (var j = 0; j < body.condition[i]?.uiCustomerActionConditions?.length; j++) {
						allConditions.push({
							type: body.condition[i].type,
							variable: body.condition[i].uiCustomerActionConditions[j].variable,
							operator: body.condition[i].uiCustomerActionConditions[j].operatorComparison,
							amount: body.condition[i].uiCustomerActionConditions[j].defaultAmount,
							textValue: body.condition[i].uiCustomerActionConditions[j].defaultTextValue,
							triggeredEvent: body.condition[i].triggeredEvent
						});
					}
				}

				if(!body.condition[i]?.uiCustomerActionConditions || body.condition[i]?.uiCustomerActionConditions?.length == 0) {
					allConditions.push({
						type: body.condition[i]?.type,
						variable: '',
						operator: '',
						amount: 0, //This is overriden by the UI
						triggeredEvent: body.condition[i]?.triggeredEvent
					});
				}
			}
			const orgLanguage = (await this.organizationRepository.findById(orgId))?.language || 'en';
			let orgCurrency: any = await this.currencyRepository.findOne({
				where: {
					organizationId: orgId
				},
				include: [
					{
						relation: 'supportedCurrencies'
					}
				]
			});

			let earnConditionGroups = LoyaltyDetailsController.groupEarnConditions(allConditions);
			let conditionStrings = LoyaltyDetailsController.generateConditionStringsShort(earnConditionGroups, orgId, orgLanguage);

			let rewardSummaryObjects: any[] = [];
			for(var i = 0; i < body.rewards.length; i++) {
				let reward = body.rewards[i];
				let rewardSummary = {
					type: reward.type,
					title: reward.title,
					imageSlotKey: reward.imageSlotKey,
					subtitle: ''
				}

				switch(reward.type) {
					case 'points':
						rewardSummary.subtitle = `(${reward.amount} pts)`;
						break;
					case 'points-per-dollar':
						rewardSummary.subtitle = `${reward.amount} pts per ${orgCurrency?.supportedCurrencies?.prefix}1${orgCurrency?.supportedCurrencies?.postfix} spent`;
						break;
					case 'dollars-off-coupon':
						rewardSummary.subtitle= `${orgCurrency?.supportedCurrencies?.prefix}${reward.amount}${orgCurrency?.supportedCurrencies?.postfix} off coupon`;
						break;
					case 'percent-discount':
						rewardSummary.subtitle = `${reward.amount}% off coupon`;
						break;
					case 'free-shipping':
						rewardSummary.subtitle = 'Free Shipping';
						break;
					case 'free-product':
						rewardSummary.subtitle = `Free Product`;
						break;
					case 'percent-off-product':
						rewardSummary.subtitle = `${reward.amount}% off Specific product`;
						break;
					case 'dollar-off-product':
						rewardSummary.subtitle = `${orgCurrency?.supportedCurrencies?.prefix}${reward.amount}${orgCurrency?.supportedCurrencies?.postfix} off Specific product`;
						break;
				}

				rewardSummaryObjects.push(rewardSummary);
			}

			return {
				wte: {
					title: body.condition[0]?.name || body.earn.name,
					subtitle: conditionStrings.join(', '),
					imageSlotKey: body.condition[0]?.imageSlotKey,

				},
				rewards: rewardSummaryObjects
			};
		}
		catch (e) {
			console.log(e);
			return {
				code: 500,
				status: 'error',
				error: e
			}
		}
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@del('/wte/{id}', {
		responses: {
			'200': {
				description: 'Deletes a way to earn and all associated data',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async deleteWTE(
		@param.path.number('id')
		@modelIdForGuard(LoyaltyEarn)
		id: number,
	): Promise<any> {
		const response = await this.loyaltyEarnRepository.cascadeDelete(id);
		return response.success ?
			{ status: 'success', message: response.message } :
			{ status: 'error', error: response };
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@del('/shop-item/{id}', {
		responses: {
			'200': {
				description: 'Deletes a shop item and all associated data',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async deleteShopItem(
		@param.path.number('id')
		id: number,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		const shopItem = await this.loyaltyRedemptionShopItemRepository.find({
			where: { id: id },
			include: [{ relation: 'loyaltyRewardDefinition' }]
		});

		const program = await this.loyaltyProgramRepository.findById((shopItem[0] as any).loyaltyRewardDefinition.loyaltyProgramId);

		if (!shopItem || program.orgId !== orgId) {
			throw new HttpErrors.Unauthorized('Unauthorized');
		}

		const response = await this.loyaltyRedemptionShopItemRepository.cascadeDelete(id);
		return response.success ?
			{ status: 'success', message: response.message } :
			{ status: 'error', error: response };
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@del('/perk/{id}', {
		responses: {
			'200': {
				description: 'Deletes a shop item and all associated data',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async perk(
		@param.path.number('id') id: number,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		const response = await this.loyaltyStaticEffectRepository.cascadeDelete(id);
		return response.success ?
			{ status: 'success', message: response.message } :
			{ status: 'error', error: response };
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/wte/enable', {
		responses: {
			'200': {
				description: 'Creates and enables the way to Earn and all necessary data that goes with it',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async enableWTE(
		@requestBody({
			content: {
				'application/json': {
					schema: {},
				},
			},
		}) body: any,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		//body will be the entire earn payload generated from the UI
		/*
			earn:{
				id: ...
				name: ...
				imageURL: ...
				description: ...
			}
			condition: [],
			rewards: [],
			campaignId: number,
			currencyId: number,
			programId: number
		*/

		//So condition has an array of uiCustomerActionConditions that need to be turned into EarnConditions
		try {
			let validProgram = await this.validateCustomerOwnsProgram(body.programId, orgId);
			let validCampaign = await this.validateCampaignBelongsToProgram(body.programId, body.campaignId);
			if (!validCampaign || !validProgram) {
				return {
					status: 'error',
					error: 'Unauthorized'
				}
			}

			let wayToEarnId = body.earn.id;
			let giveawayId = null;
			let giveawayCampaignId = null;
			if (body.earn?.giveawayId) {
				giveawayId = body.earn.giveawayId;
				delete body.earn.giveawayId;

				giveawayCampaignId = body.condition[0].uiCustomerActionConditions[0].defaultTextValue
			}

			const result = await convertCurrencyValuesToPlaceholders({
				name: body.earn.name,
				description: body.earn.description,
				...body.rewards.reduce((acc: any, reward: any, index: number) => { return { ...acc, [`reward-name-${index}`]: reward.name } }, {}),
				...body.rewards.reduce((acc: any, reward: any, index: number) => { return { ...acc, [`reward-desc-${index}`]: reward.description } }, {})
			});
			body.earn.name = result.name || body.earn.name;
			body.earn.description = result.description || body.earn.description;
			body.rewards.forEach((reward: any, index: number) => {
				reward.name = result[`reward-name-${index}`] || reward.name;
				reward.description = result[`reward-desc-${index}`] || reward.description;
			});

			if (body.earn.id) {
				//First we need to delete all the existing conditions and effects
				await this.loyaltyEarnRepository.earnEffects(body.earn.id).delete();
				await this.loyaltyEarnRepository.earnConditions(body.earn.id).delete();

				//Update the way to earn
				const payload: any = {
					name: body.earn.name,
					description: body.earn.description,
					imageURL: body.earn.imageURL,
				}
				if (body.earn.active) {
					payload.active = true;
				}
				if (giveawayCampaignId) {
					payload.active = false;
					payload.loyaltyCampaignId = giveawayCampaignId;

					const existingGiveaway = await this.loyaltyGiveawayRepository.findById(giveawayId);
					if (existingGiveaway) {
						const launched = existingGiveaway.launched;
						const startDatePassed = new Date(existingGiveaway.startDate).toISOString() <= new Date().toISOString();
						const beforeEndDate = new Date(existingGiveaway.endDate).toISOString() > new Date().toISOString();
						if (launched && startDatePassed && beforeEndDate) {
							payload.active = true;
						}
					}

				}
				await this.loyaltyEarnRepository.updateById(body.earn.id, payload);
			}
			else {
				//Create the way to earn
				let active = true;
				if (giveawayId) {
					active = false
					const existingGiveaway = await this.loyaltyGiveawayRepository.findById(giveawayId);
					if (existingGiveaway) {
						const launched = existingGiveaway.launched;
						const startDatePassed = new Date(existingGiveaway.startDate).toISOString() <= new Date().toISOString();
						const beforeEndDate = new Date(existingGiveaway.endDate).toISOString() > new Date().toISOString();
						if (launched && startDatePassed && beforeEndDate) {
							active = true;
						}
					}
				}

				let wayToEarn = await this.loyaltyEarnRepository.create({
					name: body.earn.name,
					loyaltyCampaignId: giveawayCampaignId || body.campaignId,
					description: body.earn.description,
					imageURL: body.earn.imageURL,
					active,
					hiddenFromAdminUi: giveawayId || false,
				});
				wayToEarnId = wayToEarn.id;
			}

			//Create Conditions
			for (var i = 0; i < body.condition.length; i++) {
				for (var j = 0; j < body.condition[i].uiCustomerActionConditions?.length; j++) {
					await this.earnConditionRepository.create({
						type: body.condition[i].type,
						variable: body.condition[i].uiCustomerActionConditions[j].variable,
						operator: body.condition[i].uiCustomerActionConditions[j].operatorComparison,
						amount: body.condition[i].uiCustomerActionConditions?.[j]?.defaultAmount, //This is overriden by the UI
						textValue: body.condition[i].uiCustomerActionConditions[j].defaultTextValue,
						handle: body.condition[i].uiCustomerActionConditions[j].handle,
						loyaltyEarnId: wayToEarnId,
						triggeredEvent: body.condition[i].triggeredEvent
					});
				}

				//If there are no conditions, we need to create a blank condition (might need to rethink this part in the future)
				if(!body.condition[i].uiCustomerActionConditions || body.condition[i].uiCustomerActionConditions?.length == 0) {
					let amount = 0;
					if (body.condition[i].type == 'first-subscription-purchase') {
						amount = 1;
					}
					await this.earnConditionRepository.create({
						type: body.condition[i].type,
						variable: '',
						operator: '',
						amount, //This is overriden by the UI
						loyaltyEarnId: wayToEarnId,
						triggeredEvent: body.condition[i].triggeredEvent
					});
				}
			}

			//What we'd want to do here is go through each reward and based on type create the necessary records
			for (var i = 0; i < body.rewards.length; i++) {
				let defaultValues = {
					name: body.rewards[i].name,
					description: body.rewards[i].description,
					imageURL: body.rewards[i].imageURL,
				}
				switch (body.rewards[i].type) {
					case 'points':
						await this.earnEffectRepository.create({
							points: body.rewards[i].amount,
							pointsPerDollar: 0,
							loyaltyCurrencyId: body.currencyId,
							loyaltyEarnId: wayToEarnId,
							type: body.rewards[i].type,
							...defaultValues
						});
						break;
					case 'points-per-dollar':
						await this.earnEffectRepository.create({
							points: 0,
							pointsPerDollar: body.rewards[i].amount,
							loyaltyCurrencyId: body.currencyId,
							loyaltyEarnId: wayToEarnId,
							includeTaxes: body.rewards[i].includeTaxes,
							includeShipping: body.rewards[i].includeShipping,
							type: body.rewards[i].type,
							...defaultValues
						});
						break;
					case 'dollars-off-coupon':
						let couponResult = await this.createDollarOffCoupon(body.rewards[i]);

						let result = await this.loyaltyRewardDefinitionRepository.create({
							daysToRedeem: 0,
							redeemable: false,
							grantable: true,
							redeemed: 0,
							granted: 0,
							startingInventory: -1,
							maxUserGrants: this.getMaxUserGrants(body.condition[0].type, 999), //TODO: Add in restrictions/limits
							maxUserRedemptions: 0,
							price: 0, //Not purchaseable
							loyaltyProgramId: body.programId,
							rewardCouponId: couponResult.id,
							loyaltyCampaignId: body.campaignId,
						});

						await this.earnEffectRepository.create({
							points: 0,
							pointsPerDollar: 0,
							loyaltyCurrencyId: body.currencyId,
							loyaltyRewardDefinitionId: result.id,
							loyaltyEarnId: wayToEarnId,
							type: body.rewards[i].type,
							...defaultValues
						});
						break;
					case 'percent-discount':
						let percentCouponDiscount = await this.createPercentOffCoupon(body.rewards[i]);
						let percentResult = await this.loyaltyRewardDefinitionRepository.create({
							daysToRedeem: 0,
							redeemable: false,
							grantable: true,
							redeemed: 0,
							granted: 0,
							startingInventory: -1,
							maxUserGrants: this.getMaxUserGrants(body.condition[0].type, 3), //TODO: Add in restrictions/limits
							maxUserRedemptions: 0,
							price: 0, //Not purchaseable
							loyaltyProgramId: body.programId,
							rewardCouponId: percentCouponDiscount.id,
							loyaltyCampaignId: body.campaignId,
						});

						await this.earnEffectRepository.create({
							points: 0,
							pointsPerDollar: 0,
							loyaltyCurrencyId: body.currencyId,
							loyaltyRewardDefinitionId: percentResult.id,
							loyaltyEarnId: wayToEarnId,
							type: body.rewards[i].type,
							...defaultValues
						});
						break;
					case 'free-shipping':
						let freeShippingCoupon = await this.createFreeShippingCoupon(body.rewards[i]);
						let freeShippingResult = await this.loyaltyRewardDefinitionRepository.create({
							daysToRedeem: 0,
							redeemable: false,
							grantable: true,
							redeemed: 0,
							granted: 0,
							startingInventory: -1,
							maxUserGrants: 1,
							maxUserRedemptions: 0,
							price: 0,
							loyaltyProgramId: body.programId,
							rewardCouponId: freeShippingCoupon.id,
							loyaltyCampaignId: body.campaignId,
						});

						await this.earnEffectRepository.create({
							points: 0,
							pointsPerDollar: 0,
							loyaltyCurrencyId: body.currencyId,
							loyaltyRewardDefinitionId: freeShippingResult.id,
							loyaltyEarnId: wayToEarnId,
							type: body.rewards[i].type,
							...defaultValues
						});
						break;
					case 'free-product':
						const orgLanguage = (await this.organizationRepository.findById(orgId))?.language || 'en';
						const translatedCouponName = TranslationService.getCachedTranslation(
							orgLanguage,
							'free_product_from_collection_name',
							orgId,
							{value: body.rewards[i].externalName}
						)
						let percentCoupon = await this.createPercentOffCoupon(body.rewards[i], translatedCouponName);
						let percentRewardDefintion = await this.loyaltyRewardDefinitionRepository.create({
							daysToRedeem: 0,
							redeemable: false,
							grantable: true,
							redeemed: 0,
							granted: 0,
							startingInventory: -1,
							maxUserGrants: 3,
							maxUserRedemptions: 0,
							price: 0, //Not purchaseable
							loyaltyProgramId: body.programId,
							rewardCouponId: percentCoupon.id,
							loyaltyCampaignId: body.campaignId,
						});

						await this.earnEffectRepository.create({
							points: 0,
							pointsPerDollar: 0,
							loyaltyCurrencyId: body.currencyId,
							loyaltyRewardDefinitionId: percentRewardDefintion.id,
							loyaltyEarnId: wayToEarnId,
							type: body.rewards[i].type,
							...defaultValues
						});
						break;
					case 'giveaway-entry':
						let coupon = await this.createGiveawayCoupon(body.rewards[i], giveawayId);
						let couponRewardDef = await this.loyaltyRewardDefinitionRepository.create({
							daysToRedeem: 0,
							redeemable: false,
							grantable: true,
							redeemed: 0,
							granted: 0,
							startingInventory: -1,
							maxUserGrants: 1,
							maxUserRedemptions: 0,
							price: 0, //Not purchaseable
							loyaltyProgramId: body.programId,
							rewardCouponId: coupon.id,
							loyaltyCampaignId: body.campaignId,
						});

						await this.earnEffectRepository.create({
							points: 0,
							pointsPerDollar: 0,
							loyaltyCurrencyId: body.currencyId,
							loyaltyRewardDefinitionId: couponRewardDef.id,
							loyaltyEarnId: wayToEarnId,
							type: body.rewards[i].type,
							...defaultValues
						});
						break;

					case 'percent-off-product':
						let percentOffProductDiscount = await this.createPercentOffCoupon(body.rewards[i], undefined, undefined, undefined, 'percent-off-product');
						let percentOffProductResult = await this.loyaltyRewardDefinitionRepository.create({
							daysToRedeem: 0,
							redeemable: false,
							grantable: true,
							redeemed: 0,
							granted: 0,
							startingInventory: -1,
							maxUserGrants: this.getMaxUserGrants(body.condition[0].type, 3), //TODO: Add in restrictions/limits
							maxUserRedemptions: 0,
							price: 0, //Not purchaseable
							loyaltyProgramId: body.programId,
							rewardCouponId: percentOffProductDiscount.id,
							loyaltyCampaignId: body.campaignId,
						});

						await this.earnEffectRepository.create({
							points: 0,
							pointsPerDollar: 0,
							loyaltyCurrencyId: body.currencyId,
							loyaltyRewardDefinitionId: percentOffProductResult.id,
							loyaltyEarnId: wayToEarnId,
							type: body.rewards[i].type,
							...defaultValues
						});
						break;

					case 'dollar-off-product':
						let dollarOffProductDiscount = await this.createDollarOffCoupon(body.rewards[i], undefined, undefined, 'dollar-off-product');

						let dollarOffProductResult = await this.loyaltyRewardDefinitionRepository.create({
							daysToRedeem: 0,
							redeemable: false,
							grantable: true,
							redeemed: 0,
							granted: 0,
							startingInventory: -1,
							maxUserGrants: this.getMaxUserGrants(body.condition[0].type, 999), //TODO: Add in restrictions/limits
							maxUserRedemptions: 0,
							price: 0, //Not purchaseable
							loyaltyProgramId: body.programId,
							rewardCouponId: dollarOffProductDiscount.id,
							loyaltyCampaignId: body.campaignId,
						});

						await this.earnEffectRepository.create({
							points: 0,
							pointsPerDollar: 0,
							loyaltyCurrencyId: body.currencyId,
							loyaltyRewardDefinitionId: dollarOffProductResult.id,
							loyaltyEarnId: wayToEarnId,
							type: body.rewards[i].type,
							...defaultValues
						});
						break;
				}
			}

			return {
				status: 'success',
				message: 'Successfully enabled way to earn',
				wayToEarnId: wayToEarnId
			};
		}
		catch (e) {
			console.log(e);
			return {
				code: 500,
				status: 'error',
				error: e
			}
		}
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/transform-earn-data', {
		responses: {
			'200': {
				description: 'Transforms earn data to a specific format',
				content: {'application/json': {schema: {/* Define your output schema here */}}},
			},
		},
	})
	async transformEarnData(
		@requestBody({
			content: {
				'application/json': {
					schema: {/* Define your input schema here */},
				},
			},
		}) clientData: any,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		const orgLanguage = (await this.organizationRepository.findById(orgId))?.language || 'en';
		const orgCurrency = await this.currencyRepository.findOne({
			where: { organizationId: orgId },
			include: [ { relation: 'supportedCurrencies' } ]
		});
		const currentOrg = await this.organizationRepository.findById(orgId);
		const earnId = clientData.earn.id || 123;
		const earnEffects = this.mapRewardsToEarnEffects(clientData.earn.rewards, earnId);
		await this.translationService.populateCache();

		const transformedData: any = {
			id: earnId,
			name: clientData.earn.name,
			description: clientData.earn.description,
			imageURL: clientData.earn.imageURL,
			earnConditions: clientData.earn.condition?.uiCustomerActionConditions?.map((cond: any) => ({
				id: clientData.earn.condition.id || 123,
				type: clientData.earn.condition.type,
				variable: cond.variable,
				operator: cond.operatorComparison,
				amount: cond.defaultAmount,
				triggeredEvent: clientData.earn.condition.triggeredEvent,
				loyaltyEarnId: earnId,
				textValue: cond.defaultTextValue,
			})),
			earnEffects: earnEffects,
			rewardStrings: [],
			pillStrings: LoyaltyDetailsController.generatePillStrings(
				{ earnEffects },
				orgId,
				orgLanguage,
				orgCurrency!,
			)
		};

		if (clientData.earn.condition && clientData.earn.condition.type === 'birthday-bonus') {
			transformedData.earnConditions = [{
				id: clientData.earn.condition.id || 123,
				type: clientData.earn.condition.type,
				triggeredEvent: clientData.earn.condition.triggeredEvent,
				loyaltyEarnId: earnId,
			}]
		}

		transformedData.instructions = LoyaltyDetailsController.generateConditionStrings(
			clientData.earn.campaign,
			LoyaltyDetailsController.groupEarnConditions(transformedData.earnConditions),
			orgId,
			orgLanguage,
			orgCurrency!,
			currentOrg?.externalDomain,
		);

		return transformedData;
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/transform-shop-item-data', {
		responses: {
			'200': {
				description: 'Transforms shop item data to a specific format',
				content: {'application/json': {schema: {}}},
			},
		},
	})
	async transformShopItemData(
		@requestBody({
			content: {
				'application/json': {
					schema: {},
				},
			},
		}) clientData: any,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		const orgLanguage = (await this.organizationRepository.findById(orgId))?.language || 'en';
		const rewardCoupon: any = {
			name: clientData.earn.rewards[0].name,
			amount: clientData.earn.rewards[0].amount,
			amountType: clientData.earn.rewards[0].type,
			description: clientData.earn.rewards[0].description,
			imageURL: clientData.earn.rewards[0].imageURL,
		};

		if (clientData.earn?.rewards?.[0]?.restrictions && clientData.earn?.rewards?.[0]?.restrictions.length > 0) {
			clientData.earn.rewards[0].restrictions.forEach((restriction: any) => {
				rewardCoupon[restriction.type] = restriction.amount;
			});
		}

		let fieldsToSave: any[] = [];
		for (var i = 0; i < clientData.earn.shopItemConfig?.length; i++) {
			fieldsToSave.push({
				fieldOnDataModel: clientData.earn.shopItemConfig[i].fieldOnDataModel,
				defaultAmount: clientData.earn.shopItemConfig[i].defaultAmount,
			})
		}

		let price = 0;
		let maxUserRedemptions = 3;
		for (var i = 0; i < fieldsToSave.length; i++) {
			if (fieldsToSave[i].fieldOnDataModel == 'price') {
				price = fieldsToSave[i].defaultAmount;
			} else if (fieldsToSave[i].fieldOnDataModel == 'maxUserRedemptions') {
				maxUserRedemptions = fieldsToSave[i].defaultAmount;
			}
		}

		return {
			shopItemConfig: clientData.earn.shopItemConfig,
			rewards: [clientData.earn.rewards[0]],
			loyaltyRewardDefinition: {
				rewardCoupon: rewardCoupon,
				numUserRedeemed: 0,
				maxUserRedemptions,
			},
			price,
			name: clientData.earn.rewards[0].name || '',
			amount: clientData.earn.rewards[0].amount || 0,
			amountType: clientData.earn.rewards[0].type || '',
			description: clientData.earn.rewards[0].description || '',
			imageURL: clientData.earn.rewards[0].imageURL || '',
		};
	}


	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/wte/{wteId}/load', {
		responses: {
			'200': {
				description: 'Returns available customer actions',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async loadWayToEarn(
		@param.path.number('wteId') wteId: number,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		let wteInfo = await this.loyaltyEarnRepository.findById(wteId, {
			include: [
				{
					relation: 'earnEffects',
					scope: {
						include: [
							{
								relation: 'loyaltyRewardDefinition',
								scope: {
									include: [
										{
											relation: 'rewardCoupon'
										}
									]
								}
							}
						]
					}
				},
				{
					relation: 'earnConditions',
				}
			]
		});

		let orgCurrency = await this.currencyRepository.findOne({
			where: { organizationId: orgId },
			include: [ { relation: 'supportedCurrencies' } ]
		});

		// convert currency placeholders in the title and description
		wteInfo.name = convertCurrencyPlaceholdersToValues(wteInfo.name, orgCurrency!);
		wteInfo.description = convertCurrencyPlaceholdersToValues(wteInfo.description, orgCurrency!);

		let hasAccessToWTE = await this.validateCampaignBelongsToOrg(orgId, wteInfo.loyaltyCampaignId);
		if (!hasAccessToWTE) {
			return {
				status: 'error',
				error: 'Unauthorized'
			}
		}

		let allConditions: any[] = [];
		for (var i = 0; i < wteInfo?.earnConditions.length; i++) {
			let uiCondition: any = await this.uiCustomerActionRepository.findOne({
				where: {
					type: wteInfo.earnConditions[i].type,
				},
				include: [
					{
						relation: 'uiCustomerActionConditions'
					}
				]
			});
			allConditions.push(uiCondition);
		}

		let allEffects: any[] = [];
		for (var i = 0; i < wteInfo.earnEffects?.length; i++) {
			let uiEffect: any = await this.uiCustomerRewardRepository.findOne({
				where: {
					type: wteInfo.earnEffects[i].type,
				},
				include: [
					'uiRewardRestrictions',
					'uiRewardLimits'
				]
			});
			allEffects.push(uiEffect);
		}

		//Lets update all conditions with inflated data from the DB
		for (var i = 0; i < allConditions.length; i++) {
			//We need to take the earn effects from the api and map them to the uiCustomerActionConditions
			//Find all the earn conditions that match the type
			//TODO I think this can change to just index directly into wteInfo.earnConditions in the future
			let earnConditions = wteInfo.earnConditions.filter((condition) => {
				return condition?.type == allConditions[i]?.type;
			});

			//Now we need to map the earnConditions to the uiCustomerActionConditions
			if(allConditions[i]?.uiCustomerActionConditions) {
				for (var j = 0; j < allConditions[i].uiCustomerActionConditions.length; j++) {
					let earnCondition = earnConditions.find((condition) => {
						return condition.variable == allConditions[i].uiCustomerActionConditions[j].variable;
					});

					if (earnCondition) {
						allConditions[i].uiCustomerActionConditions[j].defaultAmount = earnCondition.amount;
						allConditions[i].uiCustomerActionConditions[j].defaultTextValue = earnCondition.textValue;
						allConditions[i].uiCustomerActionConditions[j].operatorComparison = earnCondition.operator;
					}
				}
			}
		}

		//Lets update all effects with inflated data from the DB
		for (var i = 0; i < allEffects.length; i++) {
			//We need to take the earn effects from the api and map them to the uiCustomerActionConditions
			//Find all the earn conditions that match the type
			/*let earnEffects: any[] = wteInfo.earnEffects.filter((effect) => {
				return effect.type == allEffects[i].type;
			});*/
			let currentEarnEffect : any = wteInfo.earnEffects[i];

			if(allEffects[i]?.hasRestrictions) {
				allEffects[i].restrictions = [];
				for(var j = 0; j < allEffects[i].uiRewardRestrictions.length; j++) {
					let defaultAmount = allEffects[i].uiRewardRestrictions[j].defaultAmount;
					let currentAmount = 0;
					if (allEffects[i].uiRewardRestrictions[j].definitionModel?.toLowerCase() == 'rewardcoupon') {
						currentAmount = currentEarnEffect.loyaltyRewardDefinition.rewardCoupon[allEffects[i].uiRewardRestrictions[j].fieldOnDefinition];
					}
					const isExpiration = allEffects[i].uiRewardRestrictions[j].fieldOnDefinition == 'expiresInDays';
					console.log("Default Amount", defaultAmount, "Current Amount", currentAmount);
					if(isExpiration || defaultAmount != currentAmount) {
						allEffects[i].restrictions.push({
							type: allEffects[i].uiRewardRestrictions[j].fieldOnDefinition,
							amount: currentAmount,
							required: allEffects[i].uiRewardRestrictions[j].required,
							minimumAmount: allEffects[i].uiRewardRestrictions[j].minimumAmount,
						})
					}
				}
			}

			//Now we need to map the earnEffects to the proper values in the uiCustomerReward
			switch (allEffects[i]?.type) {
				case 'points':
					allEffects[i] = {
						...allEffects[i],
						amount: currentEarnEffect.points,
					}
					break;
				case 'points-per-dollar':
					allEffects[i] = {
						...allEffects[i],
						amount: currentEarnEffect.pointsPerDollar,
						includeTaxes: currentEarnEffect.includeTaxes == false ? false : true,
						includeShipping: currentEarnEffect.includeShipping == false ? false : true,
					}
					break;
				case 'dollars-off-coupon':
					allEffects[i] = {
						...allEffects[i],
						amount: currentEarnEffect.loyaltyRewardDefinition.rewardCoupon.amount,
						combinesWithOrders: currentEarnEffect.loyaltyRewardDefinition.rewardCoupon.combinesWithOrders,
						combinesWithProducts: currentEarnEffect.loyaltyRewardDefinition.rewardCoupon.combinesWithProducts,
						combinesWithShipping: currentEarnEffect.loyaltyRewardDefinition.rewardCoupon.combinesWithShipping,
						appliesOnSubscriptions: currentEarnEffect.loyaltyRewardDefinition.rewardCoupon.appliesOnSubscriptions,
					}
					break;
				case 'percent-discount':
					allEffects[i] = {
						...allEffects[i],
						amount: currentEarnEffect.loyaltyRewardDefinition.rewardCoupon.amount,
						combinesWithOrders: currentEarnEffect.loyaltyRewardDefinition.rewardCoupon.combinesWithOrders,
						combinesWithProducts: currentEarnEffect.loyaltyRewardDefinition.rewardCoupon.combinesWithProducts,
						combinesWithShipping: currentEarnEffect.loyaltyRewardDefinition.rewardCoupon.combinesWithShipping,
						appliesOnSubscriptions: currentEarnEffect.loyaltyRewardDefinition.rewardCoupon.appliesOnSubscriptions,
					}
					break;
				case 'free-product':
					allEffects[i] = {
						...allEffects[i],
						amount: currentEarnEffect.loyaltyRewardDefinition.rewardCoupon.amount,
						externalId: currentEarnEffect.loyaltyRewardDefinition.rewardCoupon.externalId,
						externalName: currentEarnEffect.loyaltyRewardDefinition.rewardCoupon.externalName,
						secondaryExternalId: currentEarnEffect.loyaltyRewardDefinition.rewardCoupon.secondaryExternalId,
						combinesWithOrders: currentEarnEffect.loyaltyRewardDefinition.rewardCoupon.combinesWithOrders,
						combinesWithProducts: currentEarnEffect.loyaltyRewardDefinition.rewardCoupon.combinesWithProducts,
						combinesWithShipping: currentEarnEffect.loyaltyRewardDefinition.rewardCoupon.combinesWithShipping,
						appliesOnSubscriptions: currentEarnEffect.loyaltyRewardDefinition.rewardCoupon.appliesOnSubscriptions,
					}
					break;
				case 'percent-off-product':
					allEffects[i] = {
						...allEffects[i],
						amount: currentEarnEffect.loyaltyRewardDefinition.rewardCoupon.amount,
						externalId: currentEarnEffect.loyaltyRewardDefinition.rewardCoupon.externalId,
						externalName: currentEarnEffect.loyaltyRewardDefinition.rewardCoupon.externalName,
						secondaryExternalId: currentEarnEffect.loyaltyRewardDefinition.rewardCoupon.secondaryExternalId,
						combinesWithOrders: currentEarnEffect.loyaltyRewardDefinition.rewardCoupon.combinesWithOrders,
						combinesWithProducts: currentEarnEffect.loyaltyRewardDefinition.rewardCoupon.combinesWithProducts,
						combinesWithShipping: currentEarnEffect.loyaltyRewardDefinition.rewardCoupon.combinesWithShipping,
						appliesOnSubscriptions: currentEarnEffect.loyaltyRewardDefinition.rewardCoupon.appliesOnSubscriptions,
					}
					break;
				case 'dollar-off-product':
					allEffects[i] = {
						...allEffects[i],
						amount: currentEarnEffect.loyaltyRewardDefinition.rewardCoupon.amount,
						externalId: currentEarnEffect.loyaltyRewardDefinition.rewardCoupon.externalId,
						externalName: currentEarnEffect.loyaltyRewardDefinition.rewardCoupon.externalName,
						secondaryExternalId: currentEarnEffect.loyaltyRewardDefinition.rewardCoupon.secondaryExternalId,
						combinesWithOrders: currentEarnEffect.loyaltyRewardDefinition.rewardCoupon.combinesWithOrders,
						combinesWithProducts: currentEarnEffect.loyaltyRewardDefinition.rewardCoupon.combinesWithProducts,
						combinesWithShipping: currentEarnEffect.loyaltyRewardDefinition.rewardCoupon.combinesWithShipping,
						appliesOnSubscriptions: currentEarnEffect.loyaltyRewardDefinition.rewardCoupon.appliesOnSubscriptions,
					}
					break;
			}
			allEffects[i] = {
				...allEffects[i],
				name: convertCurrencyPlaceholdersToValues(currentEarnEffect.name, orgCurrency!),
				description: convertCurrencyPlaceholdersToValues(currentEarnEffect.description, orgCurrency!),
				imageURL: currentEarnEffect.imageURL,
				amount: allEffects[i].amount || currentEarnEffect.loyaltyRewardDefinition.rewardCoupon.amount,
			}
		}

		let finalPayload = {
			earn: wteInfo,
			condition: allConditions[0], //TODO make this work for multiple conditions
			rewards: allEffects,
		}

		console.log("FINAL PAYLOAD", finalPayload)

		return finalPayload;
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/shopitems/uiconfig', {
		responses: {
			'200': {
				description: 'Returns Shop Item Config',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async getShopItemConfig(): Promise<any[]> {
		return await this.uiShopItemConditionRepository.find()
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})

	@skipGuardCheck()
	@get('/shopitems/rewards', {
		responses: {
			'200': {
				description: 'Returns Shop Item Config',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async getShopItemRewards(): Promise<any[]> {
		return await this.uiCustomerRewardRepository.find({
			where: {
				canShowInShop: true
			},
			include: [
				'uiRewardRestrictions',
				'uiRewardLimits'
			]
		})
	}


	@skipGuardCheck()
	@get('/perks/rewards', {
		responses: {
			'200': {
				description: 'Returns Shop Item Config',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async getPerkRewards(): Promise<any[]> {
		return await this.uiCustomerRewardRepository.find({
			where: {
				canShowInPerks: true,
			},
			include: [
				'uiRewardRestrictions',
				'uiRewardLimits'
			]
		})
	}


	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/translation-overrides', {
		responses: {
			'200': {
				description: 'Creates or updates a translation org-override',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async overrideTranslation(
		@requestBody({
			content: {
				'application/json': {
					schema: [],
				},
			},
		}) body: any,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		const overrides = body;
		for (const override of overrides) {
			const { language, key, value } = override;

			const baseTranslation = TranslationService.getCachedTranslation(language, key, -1);

			const existingOverride = await this.translationStringRepository.findOne({
				where: {
					language,
					key,
					orgId
				}
			});

			// if (baseTranslation === value) {
			// 	if (existingOverride) {
			// 		await this.translationStringRepository.deleteById(existingOverride.id);
			// 	}

			// 	continue;
			// }

			if (existingOverride) {
				existingOverride.value = value;
				await this.translationStringRepository.updateById(existingOverride.id, {
					value
				});

				return existingOverride;
			} else {
				return this.translationStringRepository.create({
					id: `${language}_${key}_org_${orgId}`,
					language,
					key,
					value,
					orgId
				});
			}
		}
	}



	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/shopitem/enable', {
		responses: {
			'200': {
				description: 'Creates or updates a new shop item',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async enableShopItem(
		@requestBody({
			content: {
				'application/json': {
					schema: {},
				},
			},
		}) body: any,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		const totalAmount = body.rewards.filter((x: any) => ['dollars-off-coupon', 'dollar-off-product'].includes(x.type)).reduce((acc: number, reward: any) => acc + reward.amount, 0);


		//body will be the entire earn payload generated from the UI
		/*
			shopitem:{
				id: ...
				name: ...
				imageURL: ...
				description: ...
			}
			shopItemConfig: [],
			condition: [],
			rewards: [],
			campaignId: number,
			currencyId: number,
			programId: number
		*/
		//We need to take and build a structure based on the shopItemConfig
		//fieldOnDataModel and defaultAmount
		let fieldsToSave: any[] = [];
		for (var i = 0; i < body.shopItemConfig?.length; i++) {
			fieldsToSave.push({
				fieldOnDataModel: body.shopItemConfig[i].fieldOnDataModel,
				defaultAmount: body.shopItemConfig[i].defaultAmount,
			})
		}

		let price = 0;
		for (var i = 0; i < fieldsToSave.length; i++) {
			if (fieldsToSave[i].fieldOnDataModel == 'price') {
				price = fieldsToSave[i].defaultAmount;
			}
		}

		try {
			console.log(body);
			//Authenticate Endpoint
			let validProgram = await this.validateCustomerOwnsProgram(body.programId, orgId);
			let validCampaign = await this.validateCampaignBelongsToProgram(body.programId, body.campaignId);
			if (!validCampaign || !validProgram) {
				return {
					status: 'error',
					error: 'Unauthorized'
				}
			}

			const results = await convertCurrencyValuesToPlaceholders({
				name: body.shopitem.name,
				description: body.shopitem.description,
				...body.rewards.reduce((acc: any, reward: any, index: number) => {
					acc[`rewards-name-${index}`] = reward.name;
					acc[`rewards-desc-${index}`] = reward.description;
					return acc;
				}, {})
			})

			body.shopitem.name = results.name || body.shopitem.name;
			body.shopitem.description = results.description || body.shopitem.description;
			body.rewards.forEach((reward: any, index: number) => {
				reward.name = results[`rewards-name-${index}`] || reward.name;
				reward.description = results[`rewards-desc-${index}`] || reward.description;
			});

			let shopItemId = body.shopitem.id;
			let existingCoupon = undefined;
			let existingLoyaltyRewardDefinition = undefined;
			if (shopItemId) {
				const payload: any = {
					name: body.shopitem.name,
					description: body.shopitem.description,
					imageURL: body.shopitem.imageURL,
					price: price,
					loyaltyCampaignId: body.campaignId,
				}
				if (body.shopitem.active) {
					payload.active = true;
				}
				await this.loyaltyRedemptionShopItemRepository.updateById(shopItemId, payload);

				let currentshopItem: any = await this.loyaltyRedemptionShopItemRepository.findById(shopItemId, {
					include: [
						{
							relation: 'loyaltyRewardDefinition',
							scope: {
								include: [
									{
										relation: 'rewardCoupon'
									}
								]
							}
						}
					]
				});
				existingLoyaltyRewardDefinition = currentshopItem?.loyaltyRewardDefinition;
				existingCoupon = currentshopItem?.loyaltyRewardDefinition?.rewardCoupon;
			}
			else {
				let shopItem = await this.loyaltyRedemptionShopItemRepository.create({
					name: body.shopitem.name,
					description: body.shopitem.description,
					imageURL: body.shopitem.imageURL,
					price: price,
					loyaltyCampaignId: body.campaignId,
					active: true,
				});
				shopItemId = shopItem.id;
			}

			//What we'd want to do here is go through each reward and based on type create the necessary records
			for (var i = 0; i < body.rewards.length; i++) {
				switch (body.rewards[i].type) {
					case 'dollars-off-coupon':
						body.rewards[i].imageURL = body.shopitem.imageURL;
						let data: any = {
							daysToRedeem: 30,
							redeemable: true,
							grantable: false,
							redeemed: 0,
							granted: 0,
							startingInventory: -1,
							maxUserGrants: 0,
							maxUserRedemptions: 3,
							price: 0,
							loyaltyProgramId: body.programId,
							loyaltyCampaignId: body.campaignId,
						}

						//Now we need to add in the fieldsToSave
						for (var j = 0; j < fieldsToSave.length; j++) {
							data[fieldsToSave[j].fieldOnDataModel] = fieldsToSave[j].defaultAmount;
						}

						//Create the coupon as a new one if its changed
						if(!existingCoupon || existingCoupon.amountType != body.rewards[i].type || existingCoupon.amount != body.rewards[i].amount) {
							let couponResult = await this.createDollarOffCoupon(body.rewards[i]);
							data.rewardCouponId = couponResult.id;
							let result = await this.loyaltyRewardDefinitionRepository.create(data);
							await this.loyaltyRedemptionShopItemRepository.updateById(shopItemId, {
								loyaltyRewardDefinitionId: result.id,
							});
						}
						else {
							//We need to update the existin coupon and the loyaltyrewarddefinition
							let couponResult = await this.updateDollarOffCoupon(existingCoupon, body.rewards[i]);
							data.rewardCouponId = existingCoupon.id;
							await this.loyaltyRewardDefinitionRepository.updateById(existingLoyaltyRewardDefinition.id, data);
						}
						break;
					case 'percent-discount':
						body.rewards[i].imageURL = body.shopitem.imageURL;
						let data2: any = {
							daysToRedeem: 0,
							redeemable: false,
							grantable: true,
							redeemed: 0,
							granted: 0,
							startingInventory: -1,
							maxUserGrants: 3, //TODO: Add in restrictions/limits
							maxUserRedemptions: 0, //this is overridden below with fieldsToSave if specified
							price: 0,
							loyaltyProgramId: body.programId,
							loyaltyCampaignId: body.campaignId,
						}


						for (var j = 0; j < fieldsToSave.length; j++) {
							data2[fieldsToSave[j].fieldOnDataModel] = fieldsToSave[j].defaultAmount;
						}

						//Create the coupon as a new one if its changed
						if(!existingCoupon || existingCoupon.amountType != body.rewards[i].type || existingCoupon.amount != body.rewards[i].amount) {
							let percentCouponDiscount = await this.createPercentOffCoupon(body.rewards[i]);
							data2.rewardCouponId = percentCouponDiscount.id;
							let percentResult = await this.loyaltyRewardDefinitionRepository.create(data2);
							await this.loyaltyRedemptionShopItemRepository.updateById(shopItemId, {
								loyaltyRewardDefinitionId: percentResult.id,
							});
						}
						else {
							//We need to update the existin coupon and the loyaltyrewarddefinition
							let percentCouponDiscount = await this.updatePercentOffCoupon(existingCoupon, body.rewards[i]);
							data2.rewardCouponId = existingCoupon.id;
							await this.loyaltyRewardDefinitionRepository.updateById(existingLoyaltyRewardDefinition.id, data2);
						}
						break;
					case 'free-shipping':
						body.rewards[i].imageURL = body.shopitem.imageURL;
						let data3: any = {
							daysToRedeem: 0,
							redeemable: true,
							grantable: true,
							redeemed: 0,
							granted: 0,
							startingInventory: -1,
							maxUserGrants: 0,
							maxUserRedemptions: 1,
							price: 0,
							loyaltyProgramId: body.programId,
							loyaltyCampaignId: body.campaignId,
						}


						for (var j = 0; j < fieldsToSave.length; j++) {
							data3[fieldsToSave[j].fieldOnDataModel] = fieldsToSave[j].defaultAmount;
						}

						//Create the coupon as a new one if its changed
						if(!existingCoupon || existingCoupon.amountType != body.rewards[i].type || existingCoupon.amount != body.rewards[i].amount) {
							let freeShippingCoupon = await this.createFreeShippingCoupon(body.rewards[i]);
							data3.rewardCouponId = freeShippingCoupon.id;
							let rewardDef = await this.loyaltyRewardDefinitionRepository.create(data3);
							await this.loyaltyRedemptionShopItemRepository.updateById(shopItemId, {
								loyaltyRewardDefinitionId: rewardDef.id,
							});

						} else {
							await this.updateFreeShippingCoupon(existingCoupon, body.rewards[i]);
							data3.rewardCouponId = existingCoupon.id;
							await this.loyaltyRewardDefinitionRepository.updateById(existingLoyaltyRewardDefinition.id, data3);
						}
						break;
					case 'free-product':
						body.rewards[i].imageURL = body.shopitem.imageURL;
						let freeProductData: any = {
							daysToRedeem: 0,
							redeemable: true,
							grantable: false,
							redeemed: 0,
							granted: 0,
							startingInventory: -1,
							maxUserGrants: 0, //TODO: Add in restrictions/limits
							maxUserRedemptions: 3,
							price: 0,
							loyaltyProgramId: body.programId,
							loyaltyCampaignId: body.campaignId,
						}


						for (var j = 0; j < fieldsToSave.length; j++) {
							freeProductData[fieldsToSave[j].fieldOnDataModel] = fieldsToSave[j].defaultAmount;
						}

						const orgLanguage = (await this.organizationRepository.findById(orgId))?.language || 'en';
						const translatedCouponName = TranslationService.getCachedTranslation(
							orgLanguage,
							'free_product_from_collection_name',
							orgId,
							{value: body.rewards[i].externalName}
						);
						//Create the coupon as a new one if its changed
						if(!existingCoupon || existingCoupon.amountType != body.rewards[i].type || existingCoupon.amount != body.rewards[i].amount) {
							let percentCouponDiscount = await this.createPercentOffCoupon(body.rewards[i], translatedCouponName);
							freeProductData.rewardCouponId = percentCouponDiscount.id;
							let percentResult = await this.loyaltyRewardDefinitionRepository.create(freeProductData);
							await this.loyaltyRedemptionShopItemRepository.updateById(shopItemId, {
								loyaltyRewardDefinitionId: percentResult.id,
							});
						}
						else {
							//We need to update the existin coupon and the loyaltyrewarddefinition
							let percentCouponDiscount = await this.updatePercentOffCoupon(existingCoupon, body.rewards[i], translatedCouponName, false, false, 'free-product');
							freeProductData.rewardCouponId = existingCoupon.id;
							await this.loyaltyRewardDefinitionRepository.updateById(existingLoyaltyRewardDefinition.id, freeProductData);
						}
						break;
					case 'percent-off-product':
						body.rewards[i].imageURL = body.shopitem.imageURL;
						let data4: any = {
							daysToRedeem: 0,
							redeemable: true,
							grantable: true,
							redeemed: 0,
							granted: 0,
							startingInventory: -1,
							maxUserGrants: 0,
							maxUserRedemptions: 1,
							price: 0,
							loyaltyProgramId: body.programId,
							loyaltyCampaignId: body.campaignId,
						}

						for (var j = 0; j < fieldsToSave.length; j++) {
							data4[fieldsToSave[j].fieldOnDataModel] = fieldsToSave[j].defaultAmount;
						}

						// const orgLanguage2 = (await this.organizationRepository.findById(orgId))?.language || 'en';
						// const translatedCouponName2 = TranslationService.getCachedTranslation(
						// 	orgLanguage2,
						// 	'free_product_from_collection_name',
						// 	orgId,
						// 	{value: body.rewards[i].externalName}
						// );
						if(!existingCoupon || existingCoupon.amountType != body.rewards[i].type || existingCoupon.amount != body.rewards[i].amount) {
							let name = `${body.rewards[i].amount}% off ${body.rewards[i].externalName}`;
							let percentCouponDiscount = await this.createPercentOffCoupon(body.rewards[i], name, false, true, 'percent-off-product');
							data4.rewardCouponId = percentCouponDiscount.id;
							let percentResult = await this.loyaltyRewardDefinitionRepository.create(data4);
							await this.loyaltyRedemptionShopItemRepository.updateById(shopItemId, {
								loyaltyRewardDefinitionId: percentResult.id,
							});
						}
						else {
							//We need to update the existin coupon and the loyaltyrewarddefinition
							let name = `${body.rewards[i].amount}% off ${body.rewards[i].externalName}`;
							let percentCouponDiscount = await this.updatePercentOffCoupon(existingCoupon, body.rewards[i], name, false, true, 'percent-off-product');
							data4.rewardCouponId = existingCoupon.id;
							await this.loyaltyRewardDefinitionRepository.updateById(existingLoyaltyRewardDefinition.id, data4);
						}
						break;
					case 'dollar-off-product':
						body.rewards[i].imageURL = body.shopitem.imageURL;
						let data5: any = {
							daysToRedeem: 0,
							redeemable: true,
							grantable: true,
							redeemed: 0,
							granted: 0,
							startingInventory: -1,
							maxUserGrants: 0,
							maxUserRedemptions: 1,
							price: 0,
							loyaltyProgramId: body.programId,
							loyaltyCampaignId: body.campaignId,
						}


						for (var j = 0; j < fieldsToSave.length; j++) {
							data5[fieldsToSave[j].fieldOnDataModel] = fieldsToSave[j].defaultAmount;
						}

						if(!existingCoupon || existingCoupon.amountType != body.rewards[i].type || existingCoupon.amount != body.rewards[i].amount) {
							let name = `{{currency_value:${body.rewards[i].amount}}} off ${body.rewards[i].externalName}`;
							let couponResult = await this.createDollarOffCoupon(body.rewards[i], false, true, 'dollar-off-product', name);
							data5.rewardCouponId = couponResult.id;
							let result = await this.loyaltyRewardDefinitionRepository.create(data5);
							await this.loyaltyRedemptionShopItemRepository.updateById(shopItemId, {
								loyaltyRewardDefinitionId: result.id,
							});
						}
						else {
							//We need to update the existin coupon and the loyaltyrewarddefinition
							let name = `{{currency_value:${body.rewards[i].amount}}} off ${body.rewards[i].externalName}`;
							let percentCouponDiscount = await this.updateDollarOffCoupon(existingCoupon, body.rewards[i], false, true, 'dollar-off-product', name);
							data5.rewardCouponId = existingCoupon.id;
							await this.loyaltyRewardDefinitionRepository.updateById(existingLoyaltyRewardDefinition.id, data5);
						}
						break;
				}
			}

			return {
				status: 'success',
				message: 'Successfully added reward.',
				shopItemId: shopItemId
			};
		}
		catch (e) {
			console.log(e);
			return {
				code: 500,
				status: 'error',
				error: e
			}
		}
	}



	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/perk/enable', {
		responses: {
			'200': {
				description: 'Creates or updates a new shop item',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async enablePerk(
		@requestBody({
			content: {
				'application/json': {
					schema: {},
				},
			},
		}) body: any,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		//body will be the entire earn payload generated from the UI
		/*
			shopitem:{
				id: ...
				name: ...
				imageURL: ...
				description: ...
			}
			// shopItemConfig: [],
			condition: [],
			rewards: [],
			campaignId: number,
			currencyId: number,
			programId: number
		*/
		// //We need to take and build a structure based on the shopItemConfig
		// //fieldOnDataModel and defaultAmount
		// let fieldsToSave: any[] = [];
		// for (var i = 0; i < body.shopItemConfig?.length; i++) {
		// 	fieldsToSave.push({
		// 		fieldOnDataModel: body.shopItemConfig[i].fieldOnDataModel,
		// 		defaultAmount: body.shopItemConfig[i].defaultAmount,
		// 	})
		// }

		// let price = 0;
		// for (var i = 0; i < fieldsToSave.length; i++) {
		// 	if (fieldsToSave[i].fieldOnDataModel == 'price') {
		// 		price = fieldsToSave[i].defaultAmount;
		// 	}
		// }

		try {
			console.log(body);
			//Authenticate Endpoint
			let validProgram = await this.validateCustomerOwnsProgram(body.programId, orgId);
			let validCampaign = await this.validateCampaignBelongsToProgram(body.programId, body.campaignId);
			if (!validCampaign || !validProgram) {
				return {
					status: 'error',
					error: 'Unauthorized'
				}
			}

			const results = await convertCurrencyValuesToPlaceholders({
				name: body.staticEffect.name,
				description: body.staticEffect.description,
				...body.rewards.reduce((acc: any, reward: any, index: number) => {
					acc[`rewards-name-${index}`] = reward.name;
					acc[`rewards-desc-${index}`] = reward.description;
					return acc;
				}, {})
			});

			body.staticEffect.name = results.name;
			body.staticEffect.description = results.description;
			body.rewards.forEach((reward: any, index: number) => {
				reward.name = results[`rewards-name-${index}`];
				reward.description = results[`rewards-desc-${index}`];
			});

			let staticEffectId = body.staticEffect.id;
			let existingCoupon = undefined;
			let existingLoyaltyRewardDefinition = undefined;
			if (staticEffectId) {
				const payload: any = {
					name: body.staticEffect.name,
					description: body.staticEffect.description,
					imageURL: body.staticEffect.imageURL,
					externalLink: body.rewards[0]?.externalLink || null,
					loyaltyCampaignId: body.campaignId,
					type: body.rewards[0]?.type,
				}
				if (body.staticEffect.active) {
					payload.active = true;
				}
				await this.loyaltyStaticEffectRepository.updateById(staticEffectId, payload);

				let currentstaticEffect: any = await this.loyaltyStaticEffectRepository.findById(staticEffectId, {
					include: [
						{
							relation: 'loyaltyRewardDefinition',
							scope: {
								include: [
									{
										relation: 'rewardCoupon'
									}
								]
							}
						}
					]
				});
				existingLoyaltyRewardDefinition = currentstaticEffect?.loyaltyRewardDefinition;
				existingCoupon = currentstaticEffect?.loyaltyRewardDefinition?.rewardCoupon;
			}
			else {
				let staticEffect = await this.loyaltyStaticEffectRepository.create({
					name: body.staticEffect.name,
					description: body.staticEffect.description,
					imageURL: body.staticEffect.imageURL,
					externalLink: body.rewards[0]?.externalLink || null,
					type: body.rewards[0]?.type || body.staticEffect.type,
					// price: price,
					loyaltyCampaignId: body.campaignId,
					active: true,
				});
				staticEffectId = staticEffect.id;
			}

			//What we'd want to do here is go through each reward and based on type create the necessary records
			for (var i = 0; i < body.rewards.length; i++) {
				switch (body.rewards[i].type) {
					case 'dollars-off-coupon':
						body.rewards[i].imageURL = body.staticEffect.imageURL;
						let data: any = {
							daysToRedeem: 30,
							redeemable: false,
							grantable: false,
							redeemed: 0,
							granted: 0,
							startingInventory: -1,
							maxUserGrants: 0,
							maxUserRedemptions: 0,
							price: 0,
							loyaltyProgramId: body.programId,
							loyaltyCampaignId: body.campaignId,
						}

						// //Now we need to add in the fieldsToSave
						// for (var j = 0; j < fieldsToSave.length; j++) {
						// 	data[fieldsToSave[j].fieldOnDataModel] = fieldsToSave[j].defaultAmount;
						// }

						//Create the coupon as a new one if its changed
						if(!existingCoupon || existingCoupon.amountType != body.rewards[i].type || existingCoupon.amount != body.rewards[i].amount) {
							let couponResult = await this.createDollarOffCoupon(body.rewards[i], true, false);
							data.rewardCouponId = couponResult.id;
							let result = await this.loyaltyRewardDefinitionRepository.create(data);
							await this.loyaltyStaticEffectRepository.updateById(staticEffectId, {
								loyaltyRewardDefinitionId: result.id,
							});
						}
						else {
							//We need to update the existin coupon and the loyaltyrewarddefinition
							let couponResult = await this.updateDollarOffCoupon(existingCoupon, body.rewards[i], true, false);
							data.rewardCouponId = existingCoupon.id;
							await this.loyaltyRewardDefinitionRepository.updateById(existingLoyaltyRewardDefinition.id, data);
						}
						break;
					case 'percent-discount':
						body.rewards[i].imageURL = body.staticEffect.imageURL;
						let data2: any = {
							daysToRedeem: 0,
							redeemable: false,
							grantable: false,
							redeemed: 0,
							granted: 0,
							startingInventory: -1,
							maxUserGrants: 0, //TODO: Add in restrictions/limits
							maxUserRedemptions: 0, //this is overridden below with fieldsToSave if specified
							price: 0,
							loyaltyProgramId: body.programId,
							loyaltyCampaignId: body.campaignId,
						}


						// for (var j = 0; j < fieldsToSave.length; j++) {
						// 	data2[fieldsToSave[j].fieldOnDataModel] = fieldsToSave[j].defaultAmount;
						// }

						//Create the coupon as a new one if its changed
						if(!existingCoupon || existingCoupon.amountType != body.rewards[i].type || existingCoupon.amount != body.rewards[i].amount) {
							let percentCouponDiscount = await this.createPercentOffCoupon(body.rewards[i], undefined, true, false);
							data2.rewardCouponId = percentCouponDiscount.id;
							let percentResult = await this.loyaltyRewardDefinitionRepository.create(data2);
							await this.loyaltyStaticEffectRepository.updateById(staticEffectId, {
								loyaltyRewardDefinitionId: percentResult.id,
							});
						}
						else {
							//We need to update the existin coupon and the loyaltyrewarddefinition
							let percentCouponDiscount = await this.updatePercentOffCoupon(existingCoupon, body.rewards[i], undefined, true, false);
							data2.rewardCouponId = existingCoupon.id;
							await this.loyaltyRewardDefinitionRepository.updateById(existingLoyaltyRewardDefinition.id, data2);
						}
						break;
					case 'free-shipping':
						body.rewards[i].imageURL = body.staticEffect.imageURL;
						let data3: any = {
							daysToRedeem: 0,
							redeemable: false,
							grantable: false,
							redeemed: 0,
							granted: 0,
							startingInventory: -1,
							maxUserGrants: 0,
							maxUserRedemptions: 0,
							price: 0,
							loyaltyProgramId: body.programId,
							loyaltyCampaignId: body.campaignId,
						}


						// for (var j = 0; j < fieldsToSave.length; j++) {
						// 	data3[fieldsToSave[j].fieldOnDataModel] = fieldsToSave[j].defaultAmount;
						// }

						//Create the coupon as a new one if its changed
						if(!existingCoupon || existingCoupon.amountType != body.rewards[i].type || existingCoupon.amount != body.rewards[i].amount) {
							let freeShippingCoupon = await this.createFreeShippingCoupon(body.rewards[i], false);
							data3.rewardCouponId = freeShippingCoupon.id;
							let rewardDef = await this.loyaltyRewardDefinitionRepository.create(data3);
							await this.loyaltyStaticEffectRepository.updateById(staticEffectId, {
								loyaltyRewardDefinitionId: rewardDef.id,
							});

						} else {
							await this.updateFreeShippingCoupon(existingCoupon, body.rewards[i], false);
							data3.rewardCouponId = existingCoupon.id;
							await this.loyaltyRewardDefinitionRepository.updateById(existingLoyaltyRewardDefinition.id, data3);
						}
						break;
					case 'free-product':
						body.rewards[i].imageURL = body.shopitem.imageURL;
						let freeProductData: any = {
							daysToRedeem: 0,
							redeemable: false,
							grantable: false,
							redeemed: 0,
							granted: 0,
							startingInventory: -1,
							maxUserGrants: 0, //TODO: Add in restrictions/limits
							maxUserRedemptions: 0,
							price: 0,
							loyaltyProgramId: body.programId,
							loyaltyCampaignId: body.campaignId,
						}


						// for (var j = 0; j < fieldsToSave.length; j++) {
						// 	freeProductData[fieldsToSave[j].fieldOnDataModel] = fieldsToSave[j].defaultAmount;
						// }

						const orgLanguage = (await this.organizationRepository.findById(orgId))?.language || 'en';
						const translatedCouponName = TranslationService.getCachedTranslation(
							orgLanguage,
							'free_product_from_collection_name',
							orgId,
							{value: body.rewards[i].externalName}
						);
						//Create the coupon as a new one if its changed
						if(!existingCoupon || existingCoupon.amountType != body.rewards[i].type || existingCoupon.amount != body.rewards[i].amount) {
							let percentCouponDiscount = await this.createPercentOffCoupon(body.rewards[i], translatedCouponName, true);
							freeProductData.rewardCouponId = percentCouponDiscount.id;
							let percentResult = await this.loyaltyRewardDefinitionRepository.create(freeProductData);
							await this.loyaltyRedemptionShopItemRepository.updateById(staticEffectId, {
								loyaltyRewardDefinitionId: percentResult.id,
							});
						}
						else {
							//We need to update the existin coupon and the loyaltyrewarddefinition
							let percentCouponDiscount = await this.updatePercentOffCoupon(existingCoupon, body.rewards[i], translatedCouponName, true);
							freeProductData.rewardCouponId = existingCoupon.id;
							await this.loyaltyRewardDefinitionRepository.updateById(existingLoyaltyRewardDefinition.id, freeProductData);
						}
						break;
					case 'points-multiplier':
						body.rewards[i].imageURL = body.staticEffect.imageURL;
						let pointsMultiplierData: any = {
							daysToRedeem: 0,
							redeemable: false,
							grantable: false,
							redeemed: 0,
							granted: 0,
							startingInventory: -1,
							maxUserGrants: 0, //TODO: Add in restrictions/limits
							maxUserRedemptions: 0, //this is overridden below with fieldsToSave if specified
							price: 0,
							loyaltyProgramId: body.programId,
							loyaltyCampaignId: body.campaignId,
						}


						// for (var j = 0; j < fieldsToSave.length; j++) {
						// 	pointsMultiplierData[fieldsToSave[j].fieldOnDataModel] = fieldsToSave[j].defaultAmount;
						// }

						//Create the coupon as a new one if its changed
						if(!existingCoupon || existingCoupon.amountType != body.rewards[i].type || existingCoupon.amount != body.rewards[i].amount) {
							let pointMultiplierCouponDiscount = await this.createPointsMultiplierCoupon(body.rewards[i]);
							pointsMultiplierData.rewardCouponId = pointMultiplierCouponDiscount.id;
							let pointMultiplierResult = await this.loyaltyRewardDefinitionRepository.create(pointsMultiplierData);
							await this.loyaltyStaticEffectRepository.updateById(staticEffectId, {
								loyaltyRewardDefinitionId: pointMultiplierResult.id,
							});
						}
						else {
							//We need to update the existin coupon and the loyaltyrewarddefinition
							let pointMultiplierCouponDiscount = await this.updatePointsMultiplierCoupon(existingCoupon, body.rewards[i]);
							pointsMultiplierData.rewardCouponId = existingCoupon.id;
							await this.loyaltyRewardDefinitionRepository.updateById(existingLoyaltyRewardDefinition.id, pointsMultiplierData);
						}
						break;
				}
			}

			return {
				status: 'success',
				message: 'Successfully added reward.',
				staticEffectId: staticEffectId
			};
		}
		catch (e) {
			console.log(e);
			return {
				code: 500,
				status: 'error',
				error: e
			}
		}
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/shopitem/{shopItemId}/load', {
		responses: {
			'200': {
				description: 'Loads a specific shop item',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async loadShopItem(
		@param.path.number('shopItemId') shopItemId: number,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		try {
			let shopItem: any = await this.loyaltyRedemptionShopItemRepository.findById(shopItemId, {
				include: [
					{
						relation: 'loyaltyRewardDefinition',
						scope: {
							include: [
								{
									relation: 'rewardCoupon'
								}
							]
						}
					}
				]
			});

			let hasAccessToShopItem = await this.validateCampaignBelongsToOrg(orgId, shopItem.loyaltyCampaignId);
			if (!hasAccessToShopItem) {
				return {
					status: 'error',
					error: 'Unauthorized'
				}
			}

			let shopItemConfig = await this.uiShopItemConditionRepository.find();
			for (var i = 0; i < shopItemConfig.length; i++) {
				if (shopItem.loyaltyRewardDefinition) {
					shopItemConfig[i].defaultAmount = shopItem.loyaltyRewardDefinition[shopItemConfig[i].fieldOnDataModel];
					if(shopItemConfig[i].fieldOnDataModel == 'price') {
						shopItemConfig[i].defaultAmount = shopItem.price;
					}
				}
			}

			let orgCurrency = await this.currencyRepository.findOne({
				where: { organizationId: orgId },
				include: [ { relation: 'supportedCurrencies' } ]
			});

			//Lets load in rewards now
			let rewards: any[] = [];
			if (shopItem.loyaltyRewardDefinition?.rewardCoupon) {
				rewards.push({
					type: shopItem.loyaltyRewardDefinition.rewardCoupon.amountType,
					amount: shopItem.loyaltyRewardDefinition.rewardCoupon.amount,
					name: convertCurrencyPlaceholdersToValues(shopItem.loyaltyRewardDefinition.rewardCoupon.name, orgCurrency!),
					description:  convertCurrencyPlaceholdersToValues(shopItem.loyaltyRewardDefinition.rewardCoupon.name, orgCurrency!),
					imageURL: shopItem.loyaltyRewardDefinition.rewardCoupon.imageURL,
					combinesWithOrders: shopItem.loyaltyRewardDefinition.rewardCoupon.combinesWithOrders,
					combinesWithProducts: shopItem.loyaltyRewardDefinition.rewardCoupon.combinesWithProducts,
					combinesWithShipping: shopItem.loyaltyRewardDefinition.rewardCoupon.combinesWithShipping,
					appliesOnSubscriptions: shopItem.loyaltyRewardDefinition.rewardCoupon.appliesOnSubscriptions,
					externalId: shopItem.loyaltyRewardDefinition.rewardCoupon.externalId,
					externalName: shopItem.loyaltyRewardDefinition.rewardCoupon.externalName,
					externalLink: shopItem.loyaltyRewardDefinition.rewardCoupon.externalLink,
					secondaryExternalId: shopItem.loyaltyRewardDefinition.rewardCoupon.secondaryExternalId,
				});
			}

			let allEffects: any[] = [];
			for (var i = 0; i < rewards.length; i++) {
				let uiEffect: any = await this.uiCustomerRewardRepository.findOne({
					where: {
						type: rewards[i].type,
					},
					include: [
						'uiRewardRestrictions',
						'uiRewardLimits'
					]
				});
				allEffects.push(uiEffect);
			}

			for (var i = 0; i < allEffects.length; i++) {
				if(allEffects[i]?.hasRestrictions) {
					rewards[i].restrictions = [];
					for(var j = 0; j < allEffects[i].uiRewardRestrictions.length; j++) {
						let defaultAmount = allEffects[i].uiRewardRestrictions[j].defaultAmount;
						let currentAmount = 0;
						switch(allEffects[i].uiRewardRestrictions[j].definitionModel) {
							case 'rewardcoupon':
								currentAmount = shopItem.loyaltyRewardDefinition.rewardCoupon[allEffects[i].uiRewardRestrictions[j].fieldOnDefinition];
								break;
						}
						const isExpiration = allEffects[i].uiRewardRestrictions[j].fieldOnDefinition == 'expiresInDays';
						console.log("Default Amount", defaultAmount, "Current Amount", currentAmount);
						if(isExpiration || defaultAmount != currentAmount) {
							rewards[i].restrictions.push({
								type: allEffects[i].uiRewardRestrictions[j].fieldOnDefinition,
								amount: currentAmount,
								required: allEffects[i].uiRewardRestrictions[j].required,
								minimumAmount: allEffects[i].uiRewardRestrictions[j].minimumAmount,
							})
						}
					}
				}
			}

			let finalPayload = {
				shopitem: {
					id: shopItem.id,
					name: convertCurrencyPlaceholdersToValues(shopItem.name, orgCurrency!),
					description: convertCurrencyPlaceholdersToValues(shopItem.description, orgCurrency!),
					imageURL: shopItem.imageURL,
					active: shopItem.active,
				},
				shopItemConfig: shopItemConfig,
				rewards: rewards,
			}

			return finalPayload;
		} catch (e) {

			console.log(e);
			return {
				code: 500,
				status: 'error',
				error: e
			}
		}
	}


	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/static-effect/{staticEffectId}/load', {
		responses: {
			'200': {
				description: 'Loads a specific shop item',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async loadStaticEffect(
		@param.path.number('staticEffectId') staticEffectId: number,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		try {
			let staticEffect: any = await this.loyaltyStaticEffectRepository.findById(staticEffectId, {
				include: [
					{
						relation: 'loyaltyRewardDefinition',
						scope: {
							include: [
								{
									relation: 'rewardCoupon'
								}
							]
						}
					}
				]
			});

			let hasAccessToShopItem = await this.validateCampaignBelongsToOrg(orgId, staticEffect.loyaltyCampaignId);
			if (!hasAccessToShopItem) {
				return {
					status: 'error',
					error: 'Unauthorized'
				}
			}

			// let staticEffectConfig = await this.uiShopItemConditionRepository.find();
			// for (var i = 0; i < staticEffectConfig.length; i++) {
			// 	if (staticEffect.loyaltyRewardDefinition) {
			// 		staticEffectConfig[i].defaultAmount = staticEffect.loyaltyRewardDefinition[staticEffectConfig[i].fieldOnDataModel];
			// 		if(staticEffectConfig[i].fieldOnDataModel == 'price') {
			// 			staticEffectConfig[i].defaultAmount = staticEffect.price;
			// 		}
			// 	}
			// }


			let orgCurrency = await this.currencyRepository.findOne({
				where: { organizationId: orgId },
				include: [ { relation: 'supportedCurrencies' } ]
			});

			//Lets load in rewards now
			let rewards: any[] = [];
			if (staticEffect.loyaltyRewardDefinition?.rewardCoupon) {
				rewards.push({
					type: staticEffect.loyaltyRewardDefinition.rewardCoupon.amountType,
					amount: staticEffect.loyaltyRewardDefinition.rewardCoupon.amount,
					name: convertCurrencyPlaceholdersToValues(staticEffect.loyaltyRewardDefinition.rewardCoupon.name, orgCurrency!),
					description: convertCurrencyPlaceholdersToValues(staticEffect.loyaltyRewardDefinition.rewardCoupon.name, orgCurrency!),
					imageURL: staticEffect.loyaltyRewardDefinition.rewardCoupon.imageURL,
				});
			}

			let allEffects: any[] = [];
			for (var i = 0; i < rewards.length; i++) {
				let uiEffect: any = await this.uiCustomerRewardRepository.findOne({
					where: {
						type: rewards[i].type,
					},
					include: [
						'uiRewardRestrictions',
						'uiRewardLimits'
					]
				});
				allEffects.push(uiEffect);
			}

			for (var i = 0; i < allEffects.length; i++) {
				if(allEffects[i]?.hasRestrictions) {
					rewards[i].restrictions = [];
					for(var j = 0; j < allEffects[i].uiRewardRestrictions.length; j++) {
						let defaultAmount = allEffects[i].uiRewardRestrictions[j].defaultAmount;
						let currentAmount = 0;
						switch(allEffects[i].uiRewardRestrictions[j].definitionModel) {
							case 'rewardcoupon':
								currentAmount = staticEffect.loyaltyRewardDefinition.rewardCoupon[allEffects[i].uiRewardRestrictions[j].fieldOnDefinition];
								break;
						}
						console.log("Default Amount", defaultAmount, "Current Amount", currentAmount);
						const isExpiration = allEffects[i].uiRewardRestrictions[j].fieldOnDefinition == 'expiresInDays';
						if(isExpiration || defaultAmount != currentAmount) {
							rewards[i].restrictions.push({
								type: allEffects[i].uiRewardRestrictions[j].fieldOnDefinition,
								amount: currentAmount,
								required: allEffects[i].uiRewardRestrictions[j].required,
								minimumAmount: allEffects[i].uiRewardRestrictions[j].minimumAmount,
							})
						}
					}
				}
			}

			let finalPayload = {
				staticEffect: {
					id: staticEffect.id,
					name: convertCurrencyPlaceholdersToValues(staticEffect.name, orgCurrency!),
					description: convertCurrencyPlaceholdersToValues(staticEffect.description, orgCurrency!),
					imageURL: staticEffect.imageURL,
					active: staticEffect.active,
					externalLink: staticEffect.externalLink,
					type: staticEffect.type,
				},
				// staticEffectConfig: staticEffectConfig,
				rewards: rewards,
			}

			return finalPayload;
		} catch (e) {

			console.log(e);
			return {
				code: 500,
				status: 'error',
				error: e
			}
		}
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/wte/predict', {
		responses: {
			'200': {
				description: 'Creates or updates a new shop item',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async filldefaults(
		@requestBody({
			content: {
				'application/json': {
					schema: {},
				},
			},
		}) body: any,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		const defaultWayToEarn = {
			name: "Earn Some Great Rewards",
			description: "Meet the goals and score the rewards",
			imageURL: "https://raleoncdn.s3.us-east-1.amazonaws.com/WTERewardDefault.jpg",
		};

		const defaultReward = {
			name: "Loyalty Reward",
			description: "Earned by Completing the Way to Earn",
			imageURL: "https://raleoncdn.s3.us-east-1.amazonaws.com/WTERewardDefault.jpg",
		};

		const desiredOutputSchema = {
			"name": "<generated name for way to earn based on condition type>",
			"description": "<generated description for way to earn based on condition>",
			"imageSearchTopic": "<generated image search topic for the way to earn based on name and description, this should always be in English>",
			"rewards": [
				{
					"name": "<generated name for the reward based on type>",
					"description": "<generated description for the reward>",
					"imageSearchTopic": "<generated image search topic for the way to earn based on name and description, this should always be in English>",
				}
			]
		}

		if(!body.condition || !body.rewards || body.rewards.length == 0) {
			return body;
		}

		let org = await this.organizationRepository.findById(orgId);
		if(!org) {
			return {
				statusCode: 500,
				body: {
					orgId: orgId,
					error: "Organization not found"
				}
			}
		}

		let exampleLanguage = org.sampleLanguage || "We sell amazing things at a great price, you can't beat our quality.";
		let description = org.description || "We are a company that sells things";

		let orgCurrency = await this.currencyRepository.findOne({
			where: { organizationId: orgId },
			include: [ { relation: 'supportedCurrencies' } ]
		});
		let currencySymbol = (orgCurrency as any)?.supportedCurrencies?.prefix || "$";

		//TODO switch this to "response_format": {"type": "json_object"}, using https://platform.openai.com/docs/guides/function-calling
		const completion = await openai.chat.completions.create({
			messages: [
				{
					role: "system",
					content:
						"I need to generate dynamic names, descriptions and an image search topic for a loyalty program. The names should use language the store would use based on the store description and example Language. The descriptions should be on brand, yet provide a clear idea of the benefit to the user. \n\n" +
						"Here are the guidelines:\n" +
						"- The names and descriptions should be unique and not generic.\n" +
						"- The names and descriptions should be casually worded.\n" +
						"- Top Level Name: No longer than 40 characters, avoiding direct phrases like 'Make X Purchases.'.\n" +
						"- Top Level Description: No more than 100 characters, should be easy to understand and clear.\n\n" +
						"- Top Level Image Search Topic: No more than 3 words. This should describe the aspiration of the reward like happy people, or excited. Ideally fitting the style of the store. For example if the store sells makeup it would show happy, excited, amazed women.\n\n" +
						"- Reward Level Name: No longer than 40 characters'. It should include the amount of the REWARD.\n" +
						"- Reward Level Description: No more than 100 characters, should be easy to understand and clear.\n\n" +
						"- Reward Level Image Search Topic: No more than 3 words. This should describe the aspiration of the reward like happy people, or excited. Ideally fitting the style of the store. For example if the store sells makeup it would show happy, excited, amazed women.\n\n" +
						"Here's an example of the style I'm looking for:\n" +
						"- Top Level Name: 'Earn Points for Each Purchase'\n" +
						"- Top Level Description: 'Unlock rewards with every shop visit! Exciting, right?'\n\n" +
						" - Reward Name: '$5 Coupon'\n" +
						" - Reward Name Example 2: Earn 5 Points for Every $1 Spent\n" +
						" - Reward Description: 'Get $5 off your next purchase'\n\n" +
						" - Reward Image Search Topic: '$5 Coupon'\n\n" +
						"This specific stores description is: " + description + "\n\n" +
						"This specific stores currency symbol is: " + currencySymbol + "\n\n" +
						"This specific stores example language is: " + exampleLanguage + "\n\n" +
						"Based on the following data, please generate names and descriptions in this style:\n\n" +
						`${JSON.stringify({
							condition: body.condition,
							rewards: body.rewards
						})} \n\n` +
						"The response should only include the filled-in request JSON in this format:\n\n" +
						`${JSON.stringify(desiredOutputSchema)}`,
				}
			],
			model: "gpt-4-1106-preview",
			response_format: { type: "json_object" }
		} as any );

		try {
			console.log(completion.choices[0].message.content);
			if(completion.choices[0].message.content)
			{
				let response = JSON.parse(completion.choices[0].message.content);
				body.name = body.name || response.name;
				body.description = body.description || response.description;

				if (response.imageSearchTopic && !body.imageURL && body.imageURL != defaultReward.imageURL) {
					const images = await this.unsplashImageService.getPhotos(response.imageSearchTopic, 1, 1, 'landscape');
					console.log(`WTE image topic: ${response.imageSearchTopic} - Image: ${JSON.stringify(images)}`);
					body.imageURL = images[0]?.urls?.small || defaultReward.imageURL;
				}
				// Apply defaults to Reward objects
				if(response.rewards.length == body.rewards.length) {
					for(let i = 0; i < response.rewards.length; i++) {
						body.rewards[i].name = body.rewards[i].name || response.rewards[i].name;
						body.rewards[i].description = body.rewards[i].description || response.rewards[i].description;

						//TODO: should we be checking if the reward already has imageUrl before attempting to replace it?
						if (response.rewards[i].imageSearchTopic && !body.rewards[i].imageURL && body.rewards[i].imageURL != defaultReward.imageURL) {
							const images = await this.unsplashImageService.getPhotos(response.rewards[i].imageSearchTopic, 1, 1, 'landscape');
							console.log(`Reward image topic: ${response.rewards[i].imageSearchTopic} - Image: ${JSON.stringify(images)}`);
							body.rewards[i].imageURL = images[0]?.urls?.small || defaultReward.imageURL;
						}
						body.rewards[i].imageURL = body.rewards[i].imageURL || defaultReward.imageURL;
					}
				}
			}
			return body;
		} catch (error) {
			console.log(error);
		}

		// Apply defaults to 'Way to Earn' objects
		body.name = body.name || defaultWayToEarn.name;
		body.description = body.description || defaultWayToEarn.description;
		body.imageURL = body.imageURL || defaultWayToEarn.imageURL;

		// Apply defaults to Reward objects
		body.rewards.forEach((reward: any) => {
			reward.name = reward.name || defaultReward.name;
			reward.description = reward.description || defaultReward.description;
			reward.imageURL = reward.imageURL || defaultReward.imageURL;
		});

		// Return the modified body
		return body;
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/wte/reward/predict', {
		responses: {
			'200': {
				description: 'Creates or updates a new shop item',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async predictRewardData(
		@requestBody({
			content: {
				'application/json': {
					schema: {},
				},
			},
		}) body: any,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		const defaultReward = {
			name: "Loyalty Reward",
			description: "Earned by Completing the Way to Earn",
			imageURL: "https://raleoncdn.s3.us-east-1.amazonaws.com/WTERewardDefault.jpg",
		};

		const desiredOutputSchema = {
			"name": "<generated name for the reward based on type>",
			"description": "<generated description for the reward>",
			"imageSearchTopic": "<generated image search topic for the reward based on the reward name and description, This should always be in English>",
		}

		if(!body) {
			return body;
		}

		let org = await this.organizationRepository.findById(orgId);
		if(!org) {
			return { statusCode: 500, body: { orgId: orgId, error: "Organization not found" } }
		}

		let exampleLanguage = org.sampleLanguage || "We sell amazing things at a great price, you can't beat our quality.";
		let description = org.description || "We are a company that sells things";

		let orgCurrency = await this.currencyRepository.findOne({
			where: { organizationId: orgId },
			include: [ { relation: 'supportedCurrencies' } ]
		});
		let currencySymbol = (orgCurrency as any)?.supportedCurrencies?.prefix || "$";

		const completion = await openai.chat.completions.create({
			messages: [
				{
					role: "system",
					content:
						"I need to generate a name, description, and an image search topic for a reward for a loyalty program. The names should use language the store would use based on the store description and example Language. The descriptions should be on brand, yet provide a clear idea of the benefit to the user. \n\n" +
						"Here are the guidelines:\n" +
						"- Reward Name: No longer than 40 characters'. It should include the amount of the REWARD.\n" +
						"- Reward Description: No more than 100 characters, should be easy to understand and clear.\n\n" +
						"- Reward Image Search Topic: No more than 3 words. This should describe the aspiration of the reward and should be related to the generated name and description.\n\n" +
						"- If the reward type is a free product, the name and description should be relevant to the externalCollectionName provided and it should be 100% off. Not a dollar amount. \n" +
						"Here's an example of the style I'm looking for:\n" +
						" - Reward Name: '$5 Coupon'\n" +
						" - Reward Name Example 2: Earn 5 Points for Every $1 Spent\n" +
						" - Reward Description: 'Get $5 off your next purchase'\n\n" +
						" - Reward Image Search Topic: '$5 Coupon'\n\n" +
						"This specific stores description is: " + description + "\n\n" +
						"This specific stores currency symbol is: " + currencySymbol + "\n\n" +
						"This specific stores example language is: " + exampleLanguage + "\n\n" +
						"The data should be generated in the proper language. The language code for this store is: " + org.language + "\n\n" +
						"Based on the following data, please generate names and descriptions in this style:\n\n" +
						`${JSON.stringify(body)}\n\n` +
						"The response should only include the filled-in request JSON in this format:\n\n" +
						`${JSON.stringify(desiredOutputSchema)}`,
				}
			],
			model: "gpt-4-1106-preview",
			response_format: { type: "json_object" }
		} as any );

		try {
			console.log(completion.choices[0].message.content);
			if(completion.choices[0].message.content)
			{
				let response = JSON.parse(completion.choices[0].message.content);
				body.name = response.name;
				body.description = response.description;

				if (response.imageSearchTopic || body.imageURL == defaultReward.imageURL) {
					const images = await this.unsplashImageService.getPhotos(response.imageSearchTopic, 1, 1, 'landscape');
					console.log(`WTE image topic: ${response.imageSearchTopic} - Image: ${JSON.stringify(images)}`);
					body.imageURL = images[0]?.urls?.small || defaultReward.imageURL;
				}
			}
			return body;
		} catch (error) {
			console.log(error);
		}
		return body;
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/earn/predict', {
		responses: {
			'200': {
				description: 'Creates or updates a new shop item',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async predictWTEData(
		@requestBody({
			content: {
				'application/json': {
					schema: {},
				},
			},
		}) body: any,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		const defaultWayToEarn = {
			name: "Earn Some Great Rewards",
			description: "Meet the goals and score the rewards",
			imageURL: "https://raleoncdn.s3.us-east-1.amazonaws.com/WTERewardDefault.jpg",
		};

		const desiredOutputSchema = {
			"name": "<generated name for way to earn based on condition type>",
			"description": "<generated description for way to earn based on condition>",
			"imageSearchTopic": "<generated image search topic for the way to earn based on name and description, This should always be in English>",
		}

		if(!body || !body.condition) { return body; }

		let org = await this.organizationRepository.findById(orgId);
		if(!org) {
			return { statusCode: 500, body: { orgId: orgId, error: "Organization not found" } }
		}

		let orgCurrency = await this.currencyRepository.findOne({
			where: { organizationId: orgId },
			include: [ { relation: 'supportedCurrencies' } ]
		});

		let exampleLanguage = org.sampleLanguage || "We sell amazing things at a great price, you can't beat our quality.";
		let description = org.description || "We are a company that sells things";
		let currencySymbol = (orgCurrency as any)?.supportedCurrencies?.prefix || "$";

		const completion = await openai.chat.completions.create({
			messages: [
				{
					role: "system",
					content:
						"I need to generate a name, description, and an image search topic for a reward for a loyalty program. The names should use language the store would use based on the store description and example Language. The descriptions should be on brand, yet provide a clear idea of the benefit to the user. \n\n" +
						"Here are the guidelines:\n" +
						"- The names and descriptions should be unique and not generic.\n" +
						"- The names and descriptions should be casually worded.\n" +
						"- Name: No longer than 40 characters, avoiding direct phrases like 'Make X Purchases.'.\n" +
						"- Description: No more than 100 characters, should be easy to understand and clear.\n\n" +
						"- Image Search Topic: No more than 3 words. This should describe the aspiration of the reward like happy people, or excited. Ideally fitting the style of the store. For example if the store sells makeup it would show happy, excited, amazed women.\n\n" +
						"Here's an example of the style I'm looking for:\n" +
						" - Name: 'Earn Points for Each Purchase'\n" +
						" - Name Example 2: Earn 5 Points for Every $1 Spent\n" +
						" - Description: 'Unlock rewards with every shop visit! Exciting, right?'\n\n" +
						"This specific stores description is: " + description + "\n\n" +
						"This specific stores example language is: " + exampleLanguage + "\n\n" +
						"This specific store uses the following currency symbol: " + currencySymbol + "\n\n" +
						"The data should be generated in the proper language. The language code for this store is: " + org.language + "\n\n" +
						"Based on the following data, please generate names and descriptions in this style:\n\n" +
						`${JSON.stringify(body)}\n\n` +
						"The response should only include the filled-in request JSON in this format:\n\n" +
						`${JSON.stringify(desiredOutputSchema)}`,
				}
			],
			model: "gpt-4-1106-preview",
			response_format: { type: "json_object" }
		} as any );

		try {
			console.log(completion.choices[0].message.content);
			if(completion.choices[0].message.content)
			{
				let response = JSON.parse(completion.choices[0].message.content);
				body.name = response.name;
				body.description = response.description;

				if (response.imageSearchTopic || body.imageURL == defaultWayToEarn.imageURL) {
					const images = await this.unsplashImageService.getPhotos(response.imageSearchTopic, 1, 1, 'landscape');
					console.log(`WTE image topic: ${response.imageSearchTopic} - Image: ${JSON.stringify(images)}`);
					body.imageURL = images[0]?.urls?.small || defaultWayToEarn.imageURL;
				}
			}
			return body;
		} catch (error) {
			console.log(error);
		}
		return body;
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/shopitem/predict', {
		responses: {
			'200': {
				description: 'predicts text for shop item',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async fillDefaulShopItem(
		@requestBody({
			content: {
				'application/json': {
					schema: {},
				},
			},
		}) body: any,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		const defaultShopItem = {
			name: "Shop Item",
			active: true,
			description: "Meet the goals and score the rewards",
			imageURL: "https://raleoncdn.s3.us-east-1.amazonaws.com/WTERewardDefault.jpg",
		};

		const desiredOutputSchema = {
			"name": "<generated name for way to earn based on type in reward>",
			"description": "<generated description for shop item based on type in reward>",
			"imageSearchTopic": "<generated brief one to three word search term to be used for image, this should always be in English>"
		}

		if(!body.shopItemConfig && (!body.rewards || body.rewards.length == 0)) {
			return body;
		}

		let org = await this.organizationRepository.findById(orgId);
		if(!org) {
			return {
				statusCode: 500,
				body: {
					orgId: orgId,
					error: "Organization not found"
				}
			}
		}

		let orgCurrency = await this.currencyRepository.findOne({
			where: {
				organizationId: orgId
			},
			include: [
				{
					relation: 'supportedCurrencies'
				}
			]
		});

		let exampleLanguage = org.sampleLanguage || "We sell amazing things at a great price, you can't beat our quality.";
		let description = org.description || "We are a company that sells things";
		let currencySymbol = (orgCurrency as any)?.supportedCurrencies?.prefix || "$";

		const completion = await openai.chat.completions.create({
			messages: [
				{
					role: "system",
					content:
						"I need to generate dynamic names and descriptions for a loyalty program shop item, that will be purchased for points. \n\n" +
						"The names should use language the store would use based on the store description and example Language. \n\n" +
						"The descriptions should be on brand, yet provide a clear idea of the benefit to the user. \n\n" +
						"I also need a brief one to three word search term to be used for image search, based on the store description. \n\n" +
						"Here are the guidelines:\n" +
						"- The name and descriptions should be unique and not generic.\n" +
						"- Shop Item Name: No longer than 40 characters, and should describe what the item is, and should use the amount field in reward, and the type to determine the context of the amount.\n" +
						"- Shop Item Description: No Longer than 100 characters and should provide extra details about the item.\n\n" +
						"- Shop Item image search term: No more than 3 words. This should describe the aspiration of the reward like happy people, or excited. Ideally fitting the style of the store. For example if the store sells makeup it would show happy, excited, amazed women.\n\n" +
						"Here's an example of the style I'm looking for:\n" +
						"- Shop Item Name: '10% off coupon'\n" +
						"- Shop Item Description: 'Save 10% off, pretty sweet right?'\n\n" +
						"- Shop Item Image Search Term: 'No more than 3 words. This should describe the aspiration of the reward like happy people, or excited. Ideally fitting the style of the store. For example if the store sells makeup it would show happy, excited, amazed women.'\n\n" +
						"This specific stores description is: " + description + "\n\n" +
						"This specific stores example language is: " + exampleLanguage + "\n\n" +
						"This specific store uses the following currency symbol: " + currencySymbol + "\n\n" +
						"The data should be generated in the proper language. The language code for this store is: " + org.language + "\n\n" +
						"Based on the following data, please generate names and descriptions in this style:\n\n" +
						`${JSON.stringify({
							shopItemConfig: body.shopItemConfig,
							rewards: body.rewards,
						})} \n\n` +
						"The response should only include the filled-in request JSON in this format:\n\n" +
						`${JSON.stringify(desiredOutputSchema)}`,
				}
			],
			model: "gpt-4-1106-preview",
			response_format: { type: "json_object" }
		} as any );
		try {
			console.log(completion.choices[0].message.content);
			if(completion.choices[0].message.content) {
				let response = JSON.parse(completion.choices[0].message.content);
				body.shopItem.name = response.name || body.shopItem.name;
				body.shopItem.description = response.description || body.shopItem.description;

				if (response.imageSearchTopic && !body.shopItem.imageURL && body.shopItem.imageURL !== defaultShopItem.imageURL) {
					const images = await this.unsplashImageService.getPhotos(response.imageSearchTopic, 1, 1, 'landscape');
					console.log(`Shop Item image topic: ${response.imageSearchTopic} - Image: ${JSON.stringify(images)}`);
					body.shopItem.imageURL = images[0]?.urls?.small || defaultShopItem.imageURL;
				} else {
					body.shopItem.imageURL = body.shopItem.imageURL || defaultShopItem.imageURL;
				}
			}
			return body;
		} catch (error) {
			console.log(error);
		}

		// Return the modified body
		return body;
	}

	getMaxUserGrants(conditionType: string, defaultValue: number): number {
		if (this.SOCIAL_MEDIA_WTE_TYPES.includes(conditionType)) {
			return 1;
		}
		if(conditionType == 'birthday-bonus') {
			return 99;
		}
		if (conditionType == 'milestone-subscription-purchase' || conditionType == 'first-subscription-purchase') {
			return 1;
		}
		if (conditionType == 'subscription-purchase') {
			return 1000000;
		}
		return defaultValue;
	}


	async validateCustomerOwnsProgram(programId: number, orgId: number) {
		let program = await this.loyaltyProgramRepository.findById(programId);
		if (program.orgId != orgId) {
			throw new Error('Unauthorized');
		}

		return true;
	}

	async validateCampaignBelongsToProgram(programId: number, campaignId: number) {
		let campaign = await this.loyaltyCampaignRepository.findById(campaignId);
		console.log(campaign.loyaltyProgramId, programId);
		if (campaign.loyaltyProgramId != programId) {
			throw new Error('Unauthorized');
		}

		return true;
	}

	async validateCampaignBelongsToOrg(orgId: number, campaignId: number) {
		let campaign = await this.loyaltyCampaignRepository.findById(campaignId);
		let programId = campaign.loyaltyProgramId;
		let program = await this.loyaltyProgramRepository.findById(programId);
		if (program.orgId != orgId) {
			throw new Error('Unauthorized');
		}

		return true;
	}

	async updateDollarOffCoupon(currentCoupon: any, reward: any, expiresToday = false, maximumDiscount = true, overrideType = '', name?: string) {
		let amountType = 'dollars-off-coupon';
		if (overrideType != '') {
			amountType = overrideType;
		}
		let couponData : any = {
			name: name || `{{currency_value:${reward.amount}}} off coupon`,
			amount: parseInt(reward.amount),
			amountType: amountType,
			expiresInDays: expiresToday ? 1 : 30,
			minimumOrderTotal: 0,
			maximumDiscount: maximumDiscount ? 100 : undefined, //Need to make this configurable
			imageURL: reward.imageURL,
			externalId: reward.externalId,
			secondaryExternalId: reward.secondaryExternalId,
			externalName: reward.externalName,
			externalLink: reward.externalLink,
			combinesWithOrders: reward.combinesWithOrders || false,
			combinesWithProducts: reward.combinesWithProducts || false,
			combinesWithShipping: reward.combinesWithShipping || false,
			appliesOnSubscriptions: reward.appliesOnSubscriptions || false,
		}

		//We need to load in defaults for rewardCoupons if they exist
		for(var k = 0; k < reward.uiRewardRestrictions?.length; k++) {
			if(reward.uiRewardRestrictions[k].definitionModel == 'rewardcoupon') {
				couponData[reward.uiRewardRestrictions[k].fieldOnDefinition] = reward.uiRewardRestrictions[k].defaultAmount;
			}
		}

		//build out restrictions
		if(reward.restrictions && reward.restrictions.length > 0) {
			for(let restriction of reward.restrictions) {
				couponData[restriction.type] = parseInt(restriction.amount);
			}
		}
		let couponResult = await this.rewardCouponRepository.updateById(currentCoupon.id, couponData);
		return couponResult;
	}

	async createDollarOffCoupon(reward: any, expiresToday = false, maximumDiscount = true, overrideType = '', name?: string) {
		let amountType = 'dollars-off-coupon';
		if (overrideType != '') {
			amountType = overrideType;
		}
		let couponData : any = {
			name: name || `{{currency_value:${reward.amount}}} off coupon`,
			amount: parseInt(reward.amount),
			amountType: amountType,
			expiresInDays: expiresToday ? 1 : 30,
			minimumOrderTotal: 0,
			maximumDiscount: maximumDiscount ? 100 : undefined, //Need to make this configurable
			imageURL: reward.imageURL,
			externalId: reward.externalId,
			secondaryExternalId: reward.secondaryExternalId,
			externalName: reward.externalName,
			externalLink: reward.externalLink,
			combinesWithOrders: reward.combinesWithOrders || false,
			combinesWithProducts: reward.combinesWithProducts || false,
			combinesWithShipping: reward.combinesWithShipping || false,
			appliesOnSubscriptions: reward.appliesOnSubscriptions || false,
		}

		if (reward.appliesOnSubscriptions) {
			couponData.appliesOnSubscriptions = true;
		}

		//We need to load in defaults for rewardCoupons if they exist
		for(var k = 0; k < reward.uiRewardRestrictions?.length; k++) {
			if(reward.uiRewardRestrictions[k].definitionModel == 'rewardcoupon') {
				couponData[reward.uiRewardRestrictions[k].fieldOnDefinition] = reward.uiRewardRestrictions[k].defaultAmount;
			}
		}

		//build out restrictions
		if(reward.restrictions && reward.restrictions.length > 0) {
			for(let restriction of reward.restrictions) {
				couponData[restriction.type] = parseInt(restriction.amount);
			}
		}
		let couponResult = await this.rewardCouponRepository.create(couponData);
		return couponResult;
	}

	async updatePercentOffCoupon(currentCoupon: any, reward: any, name?: string, expiresToday = false, maximumDiscount = true, overrideType = '') {
		const isFreeProduct = reward.type === 'free-product';
		let amountType = isFreeProduct ? 'free-product' : 'percent-discount';
		if (overrideType != '') {
			amountType = overrideType;
		}
		let percentCouponData : any = {
			name: name || `${reward.amount}% off coupon`,
			amount: parseInt(reward.amount),
			amountType: amountType,
			expiresInDays: expiresToday ? 1 : 30,
			minimumOrderTotal: 0,
			externalId: reward.externalId,
			secondaryExternalId: reward.secondaryExternalId,
			externalName: reward.externalName,
			externalLink: reward.externalLink,
			maximumDiscount: maximumDiscount ? 100 : undefined, //Need to make this configurable
			imageURL: reward.imageURL,
			combinesWithOrders: reward.combinesWithOrders || false,
			combinesWithProducts: reward.combinesWithProducts || false,
			combinesWithShipping: reward.combinesWithShipping || false,
			appliesOnSubscriptions: reward.appliesOnSubscriptions || false,
		}
		if (reward.appliesOnSubscriptions) {
			percentCouponData.appliesOnSubscriptions = reward.appliesOnSubscriptions;
		}
		//We need to load in defaults for rewardCoupons if they exist
		for(var k = 0; k < reward.uiRewardRestrictions?.length; k++) {
			if(reward.uiRewardRestrictions[k].definitionModel == 'rewardcoupon') {
				percentCouponData[reward.uiRewardRestrictions[k].fieldOnDefinition] = reward.uiRewardRestrictions[k].defaultAmount;
			}
		}

		//build out restrictions
		if(reward.restrictions && reward.restrictions.length > 0) {
			for(let restriction of reward.restrictions) {
				percentCouponData[restriction.type] = parseInt(restriction.amount);
			}
		}

		let percentCouponDiscount = await this.rewardCouponRepository.updateById(currentCoupon.id, percentCouponData);
		return percentCouponDiscount;
	}

	async createPercentOffCoupon(reward: any, name?: string, expiresToday = false, maximumDiscount = true, overrideType = '') {
		const isFreeProduct = reward.type === 'free-product';
		let amountType = isFreeProduct ? 'free-product' : 'percent-discount';
		if (overrideType != '') {
			amountType = overrideType;
		}
		let percentCouponData : any = {
			name: name || `${reward.amount}% off coupon`,
			amount: parseInt(reward.amount),
			amountType: amountType,
			expiresInDays: expiresToday ? 1 : 30,
			minimumOrderTotal: 0,
			maximumDiscount: maximumDiscount ? 100 : undefined,
			imageURL: reward.imageURL,
			externalId: reward.externalId,
			secondaryExternalId: reward.secondaryExternalId,
			externalName: reward.externalName,
			externalLink: reward.externalLink,
			combinesWithOrders: reward.combinesWithOrders || false,
			combinesWithProducts: reward.combinesWithProducts || false,
			combinesWithShipping: reward.combinesWithShipping || false,
			appliesOnSubscriptions: reward.appliesOnSubscriptions || false,
		}
		if (reward.appliesOnSubscriptions) {
			percentCouponData.appliesOnSubscriptions = reward.appliesOnSubscriptions;
		}
		//We need to load in defaults for rewardCoupons if they exist
		for(var k = 0; k < reward.uiRewardRestrictions?.length; k++) {
			if(reward.uiRewardRestrictions[k].definitionModel == 'rewardcoupon') {
				percentCouponData[reward.uiRewardRestrictions[k].fieldOnDefinition] = reward.uiRewardRestrictions[k].defaultAmount;
			}
		}

		//build out restrictions
		if(reward.restrictions && reward.restrictions.length > 0) {
			for(let restriction of reward.restrictions) {
				percentCouponData[restriction.type] = parseInt(restriction.amount);
			}
		}

		let percentCouponDiscount = await this.rewardCouponRepository.create(percentCouponData);
		return percentCouponDiscount;
	}

	async createGiveawayCoupon(reward: any, giveawayId: number) {
		let percentCouponData: Partial<RewardCoupon> = {
			name: reward.name || 'Giveaway Entry',
			amount: parseInt(reward.amount),
			amountType: 'giveaway-entry',
			imageURL: reward.imageURL,
			hiddenFromLoyaltyUi: true,
			loyaltyGiveawayId: giveawayId,
			// externalId: reward.externalId,
			// externalName: reward.externalName,
			// externalLink: reward.externalLink,
		}

		return await this.rewardCouponRepository.create(percentCouponData);
	}

	async updateGiveawayCoupon(currentCoupon: any, reward: any) {
		let giveawayCouponData : any = {
			name: reward.name || 'Giveaway Entry',
			amount: parseInt(reward.amount),
			amountType: 'giveaway-entry',
			imageURL: reward.imageURL,
		}

		return await this.rewardCouponRepository.updateById(currentCoupon.id, giveawayCouponData);
	}

	async updatePointsMultiplierCoupon(currentCoupon: any, reward: any, name?: string) {
		let pointMultiplierCouponData : any = {
			name: name || `${reward.amount}X Points`,
			amount: reward.amount,
			amountType: 'points-multiplier',
			// expiresInDays: 100000000000,
			minimumOrderTotal: 0,
			maximumDiscount: 100000000000, //Need to make this configurable
			imageURL: reward.imageURL,
			combinesWithOrders: reward.combinesWithOrders || false,
			combinesWithProducts: reward.combinesWithProducts || false,
			combinesWithShipping: reward.combinesWithShipping || false,
			appliesOnSubscriptions: reward.appliesOnSubscriptions || false,
		}
		//We need to load in defaults for rewardCoupons if they exist
		for(var k = 0; k < reward.uiRewardRestrictions?.length; k++) {
			if(reward.uiRewardRestrictions[k].definitionModel == 'rewardcoupon') {
				pointMultiplierCouponData[reward.uiRewardRestrictions[k].fieldOnDefinition] = reward.uiRewardRestrictions[k].defaultAmount;
			}
		}

		//build out restrictions
		if(reward.restrictions && reward.restrictions.length > 0) {
			for(let restriction of reward.restrictions) {
				pointMultiplierCouponData[restriction.type] = parseInt(restriction.amount);
			}
		}

		let pointMultiplierCouponDiscount = await this.rewardCouponRepository.updateById(currentCoupon.id, pointMultiplierCouponData);
		return pointMultiplierCouponDiscount;
	}

	async createPointsMultiplierCoupon(reward: any, name?: string) {
		let pointMultiplierCouponData : any = {
			name: name || `${reward.amount}X Points`,
			amount: reward.amount,
			amountType: 'points-multiplier',
			// expiresInDays: 30,
			minimumOrderTotal: 0,
			maximumDiscount: 100000000000, //Need to make this configurable
			imageURL: reward.imageURL,
			externalId: reward.externalId,
			externalName: reward.externalName,
			externalLink: reward.externalLink,
			combinesWithOrders: reward.combinesWithOrders || false,
			combinesWithProducts: reward.combinesWithProducts || false,
			combinesWithShipping: reward.combinesWithShipping || false,
			appliesOnSubscriptions: reward.appliesOnSubscriptions || false,
		}
		//We need to load in defaults for rewardCoupons if they exist
		for(var k = 0; k < reward.uiRewardRestrictions?.length; k++) {
			if(reward.uiRewardRestrictions[k].definitionModel == 'rewardcoupon') {
				pointMultiplierCouponData[reward.uiRewardRestrictions[k].fieldOnDefinition] = reward.uiRewardRestrictions[k].defaultAmount;
			}
		}

		//build out restrictions
		if(reward.restrictions && reward.restrictions.length > 0) {
			for(let restriction of reward.restrictions) {
				pointMultiplierCouponData[restriction.type] = parseInt(restriction.amount);
			}
		}

		let pointMultiplierCouponDiscount = await this.rewardCouponRepository.create(pointMultiplierCouponData);
		return pointMultiplierCouponDiscount;
	}

	mapRewardsToEarnEffects(rewards: any[], wayToEarnId: number) {
		return rewards.map((reward: any) => {
			const defaultValues = {
				name: reward.name,
				description: reward.description,
				imageURL: reward.imageURL,
				loyaltyEarnId: wayToEarnId,
				loyaltyCurrencyId: reward.currencyId,
				imageSlotKey: reward.imageSlotKey,
				restrictions: reward.restrictions,
			};

			let earnEffect = {};

			switch (reward.type) {
				case 'points':
					earnEffect = {
						...defaultValues,
						points: reward.amount ? reward.amount : 0,
						pointsPerDollar: 0,
						type: reward.type,
					};
					break;
				case 'points-per-dollar':
					earnEffect = {
						...defaultValues,
						points: 0,
						pointsPerDollar: reward.amount ? reward.amount : 0,
						type: reward.type,
						includeShipping: reward.includeShipping,
						includeTaxes: reward.includeTaxes
					};
					break;
				case 'dollars-off-coupon':
				case 'percent-discount':
				case 'free-product':
				case 'free-shipping':
					earnEffect = {
						...defaultValues,
						points: 0,
						pointsPerDollar: 0,
						type: reward.type,
					};
					break;
				case 'giveaway-entry':
					earnEffect = {
						...defaultValues,
						points: 0,
						pointsPerDollar: 0,
						type: reward.type,
						loyaltyGiveawayId: reward.giveawayId,
					};
					break;
				default:
					console.warn(`Unexpected reward type: ${reward.type}`);
					earnEffect = { ...defaultValues };
					break;
			}

			return earnEffect;
		});
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/shop-collections', {
		responses: {
			'200': {
				description: 'Returns available custom collections',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async getCollections(
		@injectUserOrgId() orgId: number,
	): Promise<any[]> {
		return await this.fetchListofCollections(orgId);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/shop-products', {
		responses: {
			'200': {
				description: 'Returns available custom collections',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async getProducts(
		@injectUserOrgId() orgId: number,
	): Promise<any[]> {
		interface Variant {
			id: string;
			title: string;
			price: string;
			url: string;
			inventory_quantity: number;
		}

		interface Media {
			url: string;
			status: string;
			alt_text: string;
		}

		interface Product {
			id: string;
			title: string;
			handle: string;
			image: string;
			description: string;
			featuredMediaURL: string;
			minPrice: string;
			maxPrice: string;
			tracksInventory: boolean;
			totalInventory: number;
			variants: Variant[];
			media: Media[];
		}

		let products = await this.fetchListOfProducts(orgId);
		let finalProducts: Product[] = [];
		for (let i = 0; i < products.length; i++) {
			// Calculate min and max prices from variants
			let prices: number[] = products[i].variants?.map((v: any) => parseFloat(v.price)) || [];
			let minPrice = prices.length > 0 ? Math.min(...prices).toString() : '0';
			let maxPrice = prices.length > 0 ? Math.max(...prices).toString() : '0';

			// Calculate total inventory and determine if the product tracks inventory
			let totalInventory = 0;
			let tracksInventory = false;

			if (products[i].variants && products[i].variants.length > 0) {
				// Check if any variant tracks inventory
				tracksInventory = products[i].variants.some((v: any) =>
					v.inventory_management === 'shopify' || v.inventory_policy === 'deny'
				);

				// Sum up available inventory across all variants
				totalInventory = products[i].variants.reduce((sum: number, v: any) => {
					const inventoryQuantity = parseInt(v.inventory_quantity || '0', 10);
					return sum + (isNaN(inventoryQuantity) ? 0 : inventoryQuantity);
				}, 0);
			}

			let productInfo: Product = {
				id: products[i].id,
				title: products[i].title,
				handle: products[i].handle,
				image: products[i].image?.src || '',
				description: products[i].body_html || products[i].description || '',
				featuredMediaURL: products[i].featuredMedia?.preview_image?.src || products[i].image?.src || '',
				minPrice: minPrice,
				maxPrice: maxPrice,
				tracksInventory: tracksInventory,
				totalInventory: totalInventory,
				variants: [],
				media: []
			};

			for (let j = 0; j < products[i].variants?.length; j++) {
				productInfo.variants.push({
					id: products[i].variants[j].id,
					title: products[i].variants[j].title,
					price: products[i].variants[j].price,
					url: products[i].variants[j].url,
					inventory_quantity: products[i].variants[j].inventoryQuantity,
				});
			}

			for (let j = 0; j < products[i].media?.length; j++) {
				productInfo.media.push({
					url: products[i].media[j].url,
					status: products[i].media[j].status,
					alt_text: products[i].media[j].altText,
				});
			}

			finalProducts.push(productInfo);
		}
		return finalProducts;
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/app-embed-status', {
		responses: {
			'200': {
				description: 'Returns active script tags for the store',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async getScriptTags(
		@param.query.string('appEmbedId') appEmbedId: string,
		@injectUserOrgId() orgId: number,
	): Promise<any[]> {
		try {
			const path = `/app-embeds?appEmbedId=${appEmbedId}`;
			const response = await this.shopifyApiInvoker.invokeAdminApi(orgId, path, 'GET');
			//todo need to see format here
			return response;
		} catch (error) {
			console.error('Error fetching script tags from Shopify:', error);
			return [];
		}
	}

	async fetchListofCollections(orgId: number) {
		try {
			const path = `/custom-collections`;
			const response = await this.shopifyApiInvoker.invokeAdminApi(orgId, path, 'GET');
			//todo need to see format here
			return response;
		} catch (error) {
			console.error('Error fetching collections from Shopify:', error);
			return [];
		}
	}

	async fetchListOfProducts(orgId: number) {
		try {
			const path = `/product-catalogue`;
			return await this.shopifyApiInvoker.invokeAdminApi(orgId, path, 'GET');
		} catch (error) {
			console.error('Error fetching orders from Shopify:', error);
			return [];
		}
	}

	async createFreeShippingCoupon(reward: any, expiresToday = false) {
		let freeShippingData : any = {
			name: `Free Shipping`,
			amount: 0,
			amountType: 'free-shipping',
			expiresInDays: expiresToday ? 1 : 30,
			minimumOrderTotal: 0,
			imageURL: reward.imageURL,
			combinesWithOrders: reward.combinesWithOrders || false,
			combinesWithProducts: reward.combinesWithProducts || false,
			combinesWithShipping: reward.combinesWithShipping || false,
			appliesOnSubscriptions: reward.appliesOnSubscriptions || false,
		}
		//We need to load in defaults for rewardCoupons if they exist
		for(var k = 0; k < reward.uiRewardRestrictions?.length; k++) {
			if(reward.uiRewardRestrictions[k].definitionModel == 'rewardcoupon') {
				freeShippingData[reward.uiRewardRestrictions[k].fieldOnDefinition] = reward.uiRewardRestrictions[k].defaultAmount;
			}
		}

		//build out restrictions
		if(reward.restrictions && reward.restrictions.length > 0) {
			for(let restriction of reward.restrictions) {
				freeShippingData[restriction.type] = parseInt(restriction.amount);
			}
		}

		let discount = await this.rewardCouponRepository.create(freeShippingData);
		return discount;
	}

	async updateFreeShippingCoupon(currentCoupon: any, reward: any, expiresToday = false) {
		let freeShippingData : any = {
			name: `Free Shipping`,
			amount: 0,
			amountType: 'free-shipping',
			expiresInDays: expiresToday ? 1 : 30,
			minimumOrderTotal: 0,
			imageURL: reward.imageURL,
		}
		//We need to load in defaults for rewardCoupons if they exist
		for(var k = 0; k < reward.uiRewardRestrictions?.length; k++) {
			if(reward.uiRewardRestrictions[k].definitionModel == 'rewardcoupon') {
				freeShippingData[reward.uiRewardRestrictions[k].fieldOnDefinition] = reward.uiRewardRestrictions[k].defaultAmount;
			}
		}

		//build out restrictions
		if(reward.restrictions && reward.restrictions.length > 0) {
			for(let restriction of reward.restrictions) {
				freeShippingData[restriction.type] = parseInt(restriction.amount);
			}
		}


		let freeShippingDiscount = await this.rewardCouponRepository.updateById(currentCoupon.id, freeShippingData);
		return freeShippingDiscount;
	}
}

// export function updateCurrencyPlaceholderValues(string: string, amount: number): string {
// 	return string?.replace?.(/\{{1,2}\s*(?:currency|\$)(?:_val|_value|_template)?\s*(?::\s*([^}]+)?)?\s*}{1,2}/gi, `{$:${amount}}`)
// }

export function convertCurrencyPlaceholdersToValues(string: string, orgCurrency: CurrencyWithRelations, amountOverride?: number): string {
	const currencyPrefix = (orgCurrency as any)?.supportedCurrencies?.prefix || "$";
	const currencySuffix = (orgCurrency as any)?.supportedCurrencies?.suffix || "";

	return string?.replace?.(/\{{1,2}\s*(?:currency|\$)(?:_val|_value|_template)?\s*(?::\s*([^}]+)?)?\s*}{1,2}/gi, (match, amount) =>
		`${currencyPrefix}${amountOverride !== undefined ? amountOverride : (amount || '')}${currencySuffix}`
	);
}

export async function convertCurrencyValuesToPlaceholders<U extends {[key: string]: string}>(strings: U): Promise<U> {
	const completion = await openaiForCurrencyProcessing.chat.completions.create({
		messages: [
			{
				role: "system",
				content: `
					- I need to convert the currency values in the string to placeholders.
					- The placeholder syntax should be {{currency_value:<VALUE>}}, where <VALUE> is the numerical value
					- Make sure both the symbol/code/colloquial name/etc and the value are replaced
						- "Enter to win, and get 5 USD back" -> "Enter to win, and get {{currency_value:5}} back"
					- If the currency reference is not valued / is a reference to the currency itself, it should be replaced with this exact string: {{currency_value:}}
					- All references to currency values should be replaced. Here are some examples:
						- Symbols:
							- $5 -> {{currency_value:5}}
							- Spend $, Get $ -> Spend {{currency_value:}}, Get {{currency_value:}}
							- 5$ -> {{currency_value:5}}
							- MX$ 5.00 -> {{currency_value:5.00}}
							- three hundred and fifty $ -> {{currency_value:350}}
						- Currency codes:
							- Get USD 5 back -> Get {{currency_value:5}} back
							- Spend EUR, Get EUR -> Spend {{currency_value:}}, Get {{currency_value:}}
							- Score 5 USD off -> Score {{currency_value:5}} off
							- twenty five USD -> {{currency_value:25}}
							- three and a half thousand USD -> {{currency_value:3500}}
						- Colloquial/worded references to currency values:
							- 5 dollars -> {{currency_value:5}}
							- 5-dollar -> {{currency_value:5}}
							- 5 bucks -> {{currency_value:5}}
							- Spend dollars, get dollars -> Spend {{currency_value:}}, get {{currency_value:}}
							- twelve bucks -> {{currency_value:12}}
							- twelve-euro -> {{currency_value:12}}
							- three dollars fifty -> {{currency_value:3.50}}
							- 99 cents -> {{currency_value:0.99}}
							- twenty five cents -> {{currency_value:0.25}}
							- two grand -> {{currency_value:2000}}
							- In certain cases, this may require using surrounding context to differentiate between currency references and other uses of the word
								- Win 5 pounds -> Win {{currency_value:5}}
								- 5 pounds of apples -> 5 pounds of apples
								- 5 pounds of apples for 5 pounds -> 5 pounds of apples for {{currency_value:5}}
								- At least 5 pounds worth of apples for 10% off -> At least {{currency_value:5}} worth of apples for 10% off
								- Two grand off -> {{currency_value:2000}} off
								- Two grand apples for ten dollars -> Two grand apples for {{currency_value:10}}
								- Two grand pianos -> Two grand pianos
							- Some cases may be tricky when it comes to plurality, for example:
								- 5 dollar off coupons -> 5 {{currency_value:1}} off coupons
								- 5 one-dollar coupons -> 5 {{currency_value:1}} coupons
								- 5 dollar off coupon -> {{currency_value:5}} off coupon
								- five-dollar coupon -> {{currency_value:5}} coupon

					- The response should be in the following JSON format:
						- {
							"key1": "<converted string 1>",
							"key2": "<converted string 2>",
						}
						- Be sure to use the same keys as the input strings, and the values should be the converted strings
				`
			},
			{
				role: "user",
				content: JSON.stringify(strings)
			}
			// strings.map(string => ({
			// 	role: "user",
			// 	content: string
			// }))
		],
		model: "gpt-4o",
		response_format: { type: "json_object" }
	} as any );
	try {
		console.log(completion.choices[0].message.content);
		const response = JSON.parse(completion.choices[0].message.content!);

		for (const key in strings) {
			if (!response.hasOwnProperty(key)) {
				console.error(`Currency placeholderization key "${key}" not found in GPT response`, strings, response);
				response[key] = strings[key];

				// TODO: remove before push
				throw new Error(`Currency placeholderization key "${key}" not found in GPT response`);
			}
		}

		return response;
	} catch (error) {
		console.error(error);
	}

	return strings;
}
