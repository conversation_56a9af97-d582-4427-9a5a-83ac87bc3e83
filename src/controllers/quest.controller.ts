import {authenticate} from '@loopback/authentication';
import {User} from '@loopback/authentication-jwt';
import {service} from '@loopback/core';
import {IsolationLevel, repository} from '@loopback/repository';
import {
	api,
	get,
	getModelSchemaRef,
	param,
	post,
	requestBody,
} from '@loopback/rest';
import {injectUserOrgId, modelIdForGuard, guardStrategy, skipGuardCheck, OrgGuardSingleHopPropertyStrategy} from '../interceptors';
import {Campaign, Quest, Content} from '../models';
import {QuestTemplateBuilder} from '../quests/quest-template-builder';
import {
	CampaignRepository,
	ContentRepository,
	QuestRepository,
	GoalRepository,
	CampaignSegmentRepository,
	SegmentRepository,
	UserRepository,
	OrganizationRepository,
	ImageRepository,
} from '../repositories';
import {RaleonUserService} from '../services';
import {SegmentsController} from './segment.controller';

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardSingleHopPropertyStrategy<Quest, Campaign>({
	relatedIdPropertyName: 'campaignId',
	relatedOrgIdPropertyName: 'orgId',
	relatedRepositoryClass: CampaignRepository,
	repositoryClass: QuestRepository
}))
export class CampaignQuestController {
	constructor(
		@repository(CampaignRepository)
		protected campaignRepository: CampaignRepository,
		@repository(ContentRepository)
		protected contentRepository: ContentRepository,
		@repository(QuestRepository)
		protected questRepository: QuestRepository,
		@repository(GoalRepository)
		protected goalRepository: GoalRepository,
		@repository(SegmentRepository)
		protected segmentRepository: SegmentRepository,
		@repository(CampaignSegmentRepository)
		protected campaignSegmentRepository: CampaignSegmentRepository,
		@repository(UserRepository)
		private userRepository: UserRepository,
		@service(RaleonUserService)
		private raleonUserService: RaleonUserService,
		@repository(OrganizationRepository)
		private orgRepository: OrganizationRepository,
		@repository(ImageRepository)
		private imageRepository: ImageRepository
	) { }

	@get('/campaigns/{orgId}/login', {
		responses: {
			'200': {
				description: 'Fetch Login Quest',
				content: {
					'application/json': {
						schema: {type: 'array', items: getModelSchemaRef(Campaign)},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	async getLoginQuest(
		@param.path.number('orgId')
		orgId: number,
	) {
		let campaign = await this.campaignRepository.findOne({
			where: {
				orgId: orgId,
			},
			include: [{
				relation: 'quests',
				scope: {
					where: {
						isLoginQuest: true
					}
				}
			}]
		});

		return campaign || {};
	}

	@get('/campaigns/{orgId}/{questid}', {
		responses: {
			'200': {
				description: 'Find Specific Quest By Id',
				content: {
					'application/json': {
						schema: {type: 'array', items: getModelSchemaRef(Quest)},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	async questById(
		@param.path.number('orgId') // this comes from the snippet, do not inject user org id
		orgId: number,
		@param.path.number('questid') // this comes from the snippet, do not inject user org id
		questid: number,
	): Promise<any> {
		let campaigns = await this.campaignRepository.find({
			include: [{
				relation: 'quests',
				scope: {
					include: [
						'content',
						{
							relation: 'goals',
							scope: {include: ['content']}
						}, {
							relation: 'rewards',
						}
					],
					where: {
						id: questid
					}
				}
			}],
			where: {orgId: orgId},
		});

		if (!campaigns.length) {
			return {
				campaign: undefined,
				template: undefined
			}
		}

		//Find campaign that includes quest with id
		let foundCampaign: any = {};
		for (let i = 0; i < campaigns.length; i++) {
			if (campaigns[i].quests && campaigns[i].quests.length) {
				if (campaigns[i].quests[0].id === questid) {
					foundCampaign = campaigns[i];
					break;
				}
			}
		}

		const template = QuestTemplateBuilder.buildTemplate(
			foundCampaign.quests[0].content!,
		);

		return {
			campaign: foundCampaign,
			template,
		};
	}

	//orgId here is being sent from snippet on customer's site. Should remain throughout these apis
	@get('/campaigns/{orgId}/quests', {
		responses: {
			'200': {
				description: 'Array of Campaign has many Quest',
				content: {
					'application/json': {
						schema: {type: 'array', items: getModelSchemaRef(Quest)},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	async find(
		@param.path.number('orgId') // this comes from the snippet, do not inject user org id
		orgId: number
	): Promise<any> {
		let campaigns = await this.campaignRepository.find({
			include: [{
				relation: 'quests',
				scope: {
					include: ['content',
						{
							relation: 'goals',
							scope: {include: ['content']}
						}]
				}
			}],
			where: {orgId: orgId},
		});
		campaigns = campaigns.filter(campaign => campaign.status.toLowerCase() == 'running');

		if (!campaigns.length) {
			return {
				campaign: undefined,
				template: undefined
			}
		}

		campaigns = campaigns.filter(x => x.type.toLowerCase() != 'actionprompt').sort(
			(a: any, b: any) =>
				(a.priority == null ? Infinity : a.priority) -
				(b.priority == null ? Infinity : b.priority),
		);
		const template = QuestTemplateBuilder.buildTemplate(
			campaigns[0].quests[0].content!,
		);

		return {
			campaign: campaigns[0],
			template,
		};
	}

	@get('/campaigns/{orgId}/action-prompts', {
		responses: {
			'200': {
				description: 'Array of Campaign has many Quest',
				content: {
					'application/json': {
						schema: {type: 'array', items: getModelSchemaRef(Quest)},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	async findActionPrompt(
		@param.path.number('orgId') // this comes from the snippet, do not inject user org id
		orgId: number
	): Promise<any> {
		let campaigns = await this.campaignRepository.find({
			include: [{
				relation: 'quests',
				scope: {
					include: ['content',
						{
							relation: 'goals',
							scope: {include: ['content']}
						}]
				}
			}],
			where: {orgId: orgId},
		});
		campaigns = campaigns.filter(campaign => campaign.status.toLowerCase() == 'running');

		if (!campaigns.length) {
			return {
				campaign: undefined,
				template: undefined
			}
		}

		campaigns = campaigns.filter(x => x.type.toLowerCase() == 'actionprompt').sort(
			(a: any, b: any) =>
				(a.priority == null ? Infinity : a.priority) -
				(b.priority == null ? Infinity : b.priority),
		);
		const template = QuestTemplateBuilder.buildTemplate(
			campaigns[0].quests[0].content!,
		);

		return {
			campaign: campaigns[0],
			template,
		};
	}

	@get('/quests/available/{orgId}/{walletAddress}', {
		responses: {
			'200': {
				description: 'Quest model instance',
				content: {'application/json': {schema: getModelSchemaRef(Quest)}},
			},
		},
	})
	@skipGuardCheck()
	async getAllAvailableCampaigns(
		@param.path.number('orgId') orgId: number, // this is called from the snippet, do not inject user org id
		@param.path.string('walletAddress') walletAddress: string,
	): Promise<any> {
		const segmentCtrl = new SegmentsController(
			undefined as any,
			{} as User,
			this.segmentRepository,
			this.userRepository,
			this.orgRepository
		);

		const raleonUser = await this.raleonUserService.getUserFromIdentities([{
			identityType: 'address',
			identityValue: walletAddress.toLowerCase()
		}]);

		const segments: any = await segmentCtrl.getSegments(walletAddress, orgId);
		let campaigns: any[] = [];
		const segmentNames = segments.body.map((segment: any) => segment.name);
		campaigns = await this.campaignRepository.find({
			include: [
				{
					relation: 'quests',
					scope: {
						include: [
							'content',
							{
								relation: 'goals',
								scope: {include: ['content']},
							},
							{
								relation: 'rewards',
								scope: {include: ['content'], },
							},
							{
								relation: 'journeys',
								scope: {
									where: {
										raleonUserId: raleonUser.id
									}
								},
							}
						],
					},
				},
				{
					relation: 'segments',
					scope: {
						fields: [
							'id',
							'name',
							'description',
							'network',
							'orgId',
							'status',
							'addressCount',
						],
					},
				},
				{
					relation: 'image',
					scope: {
						fields: [
							'id',
							'friendlyName',
							'url',
						]
					}
				}
			],
			where: {orgId: orgId},
		});

		const defaultImage = await this.imageRepository.findOne({
			where: {
				orgId,
				campaignId: null as any
			}
		});

		campaigns = campaigns.map(campaign => {
			const completed = !(areNoStartedJourneys(campaign) || areSomeIncompleteJourneys(campaign));
			let hidden = false;
			if(campaign.hiddenUntilComplete) {
				if(completed) {
					hidden = false;
				}
				else {
					hidden = true;
				}
			}
			if (!campaign.image) {
				campaign.image = defaultImage;
			}

			return {...campaign, completed: completed, hidden: hidden};
		});

		campaigns = campaigns
			.filter(campaign => {
				if (campaign.status.toLowerCase() != 'running') return false;
				if (campaign.segments === undefined || campaign.segments.length == 0) return true;
				else if (campaign.segments && campaign.segments.length) {
					return campaign.segments.map((segment: any) => segment.name).some((name: any) => segmentNames.includes(name));
				}
				return false;
			});

		if (campaigns.length > 0) {
			return {
				campaigns: [...campaigns]
			};
		}
		return {} as Campaign;
	}

	@get('/quests/actionprompt/{orgId}/{walletAddress}', {
		responses: {
			'200': {
				description: 'Quest model instance',
				content: {'application/json': {schema: getModelSchemaRef(Quest)}},
			},
		},
	})
	@skipGuardCheck()
	async getActionPrompts(
		@param.path.number('orgId') orgId: number, // this is called from the snippet, do not inject user org id
		@param.path.string('walletAddress') walletAddress: string,
	): Promise<any> {
		const segmentCtrl = new SegmentsController(
			undefined as any,
			{} as User,
			this.segmentRepository,
			this.userRepository,
			this.orgRepository
		);

		const raleonUser = await this.raleonUserService.getUserFromIdentities([{
			identityType: 'address',
			identityValue: walletAddress.toLowerCase()
		}]);

		const segments: any = await segmentCtrl.getSegments(walletAddress, orgId);
		let campaigns: Campaign[] = [];
		const segmentNames = segments.body.map((segment: any) => segment.name);
		campaigns = await this.campaignRepository.find({
			include: [
				{
					relation: 'quests',
					scope: {
						include: [
							'content',
							{
								relation: 'goals',
								scope: {include: ['content']},
							},
							{
								relation: 'rewards',
								scope: {include: ['content'], },
							},
							{
								relation: 'journeys',
								scope: {
									where: {
										raleonUserId: raleonUser.id
									}
								},
							},
						],
					},
				},
				{
					relation: 'segments',
					scope: {
						//where: {name: {inq: segmentNames}},
						fields: [
							'id',
							'name',
							'description',
							'network',
							'orgId',
							'status',
							'addressCount',
						],
					},
				},
			],
			where: {orgId: orgId, type: 'ActionPrompt', status: 'Running'},
		});

		campaigns = campaigns
			.filter(campaign => {
				if (campaign.status.toLowerCase() != 'running') return false;
				if (!(areNoStartedJourneys(campaign) || areSomeIncompleteJourneys(campaign))) return false;
				if (campaign.segments === undefined || campaign.segments.length == 0) return true;
				else if (campaign.segments && campaign.segments.length) {
					return campaign.segments.map((segment: any) => segment.name).some((name: any) => segmentNames.includes(name));
				}
				return false;
			}).sort((a: any, b: any) => {
				if ((a.segments && a.segments.length) && (!b.segments && !b.segments?.length)) return -1;
				if ((!a.segments && !a.segments?.length) && (b.segments && b.segments.length)) return 1;
				if (a.priority > b.priority) return -1;
				if (a.priority < b.priority) return 1;
				return 0;
			});

		if (campaigns.length > 0) {
			const template = (campaigns[0].quests || [])[0]
				? QuestTemplateBuilder.buildTemplate(
					campaigns[0].quests[0].content!,
				)
				: undefined;

			return {
				campaign: campaigns[0],
				template,
			};
		}
		return {} as Campaign;
	}

	@get('/quests/{orgId}/{walletAddress}', {
		responses: {
			'200': {
				description: 'Quest model instance',
				content: {'application/json': {schema: getModelSchemaRef(Quest)}},
			},
		},
	})
	@skipGuardCheck()
	async getQuestForWalletNoCategory(
		@param.path.number('orgId') orgId: number, // this is called from the snippet, do not inject user org id
		@param.path.string('walletAddress') walletAddress: string,
	): Promise<any> {
		const segmentCtrl = new SegmentsController(
			undefined as any,
			{} as User,
			this.segmentRepository,
			this.userRepository,
			this.orgRepository
		);

		const raleonUser = await this.raleonUserService.getUserFromIdentities([{
			identityType: 'address',
			identityValue: walletAddress.toLowerCase()
		}]);

		const segments: any = await segmentCtrl.getSegments(walletAddress, orgId);
		let campaigns: Campaign[] = [];
		const segmentNames = segments.body.map((segment: any) => segment.name);
		campaigns = await this.campaignRepository.find({
			include: [
				{
					relation: 'quests',
					scope: {
						include: [
							'content',
							{
								relation: 'goals',
								scope: {include: ['content']},
							},
							{
								relation: 'rewards',
								scope: {include: ['content'], },
							},
							{
								relation: 'journeys',
								scope: {
									where: {
										raleonUserId: raleonUser.id
									}
								},
							},
						],
					},
				},
				{
					relation: 'segments',
					scope: {
						//where: {name: {inq: segmentNames}},
						fields: [
							'id',
							'name',
							'description',
							'network',
							'orgId',
							'status',
							'addressCount',
						],
					},
				},
			],
			where: {orgId: orgId, status: 'Running'},
		});

		campaigns = campaigns
			.filter(campaign => {
				if (campaign.status.toLowerCase() != 'running') return false;
				if (!(areNoStartedJourneys(campaign) || areSomeIncompleteJourneys(campaign))) return false;
				if (campaign.segments === undefined || campaign.segments.length == 0) return true;
				else if (campaign.segments && campaign.segments.length) {
					return campaign.segments.map((segment: any) => segment.name).some((name: any) => segmentNames.includes(name));
				}
				return false;
			}).sort((a: any, b: any) => {
				if ((a.segments && a.segments.length) && (!b.segments && !b.segments?.length)) return -1;
				if ((!a.segments && !a.segments?.length) && (b.segments && b.segments.length)) return 1;
				if (a.priority > b.priority) return -1;
				if (a.priority < b.priority) return 1;
				return 0;
			});

		if (campaigns.length > 0) {
			const template = (campaigns[0].quests || [])[0]
				? QuestTemplateBuilder.buildTemplate(
					campaigns[0].quests[0].content!,
				)
				: undefined;

			return {
				campaign: campaigns[0],
				template,
			};
		}
		return {} as Campaign;
	}

	@get('/quests/{orgId}/{walletAddress}/{category}', {
		responses: {
			'200': {
				description: 'Quest model instance',
				content: {'application/json': {schema: getModelSchemaRef(Quest)}},
			},
		},
	})
	@skipGuardCheck()
	async getQuestForWallet(
		@param.path.number('orgId') orgId: number, // this is called from the snippet, do not inject user org id
		@param.path.string('walletAddress') walletAddress: string,
		@param.path.string('category') category?: string,
	): Promise<any> {
		const segmentCtrl = new SegmentsController(
			undefined as any,
			{} as User,
			this.segmentRepository,
			this.userRepository,
			this.orgRepository
		);

		const raleonUser = await this.raleonUserService.getUserFromIdentities([{
			identityType: 'address',
			identityValue: walletAddress.toLowerCase()
		}]);

		const segments: any = await segmentCtrl.getSegments(walletAddress, orgId);
		let campaigns: Campaign[] = [];
		const segmentNames = segments.body.map((segment: any) => segment.name);
		campaigns = await this.campaignRepository.find({
			include: [
				{
					relation: 'quests',
					scope: {
						include: [
							'content',
							{
								relation: 'goals',
								scope: {include: ['content']},
							},
							{
								relation: 'rewards',
								scope: {include: ['content'], },
							},
							{
								relation: 'journeys',
								scope: {
									where: {
										raleonUserId: raleonUser.id
									}
								},
							},
						],
					},
				},
				{
					relation: 'segments',
					scope: {
						fields: [
							'id',
							'name',
							'description',
							'network',
							'orgId',
							'status',
							'addressCount',
						],
					},
				},
			],
			where: {orgId: orgId},
		});

		campaigns = campaigns
			.filter(campaign => {
				if (campaign.status.toLowerCase() != 'running') return false;
				if (!(areNoStartedJourneys(campaign) || areSomeIncompleteJourneys(campaign))) return false;
				if (campaign.segments === undefined || campaign.segments.length == 0) return true;
				if (campaign.segments && campaign.segments.length) {
					return campaign.segments.map((segment: any) => segment.name).some((name: any) => segmentNames.includes(name));
				}
				return false;
			}).sort((a: any, b: any) => {
				if ((a.segments && a.segments.length) && (!b.segments && !b.segments?.length)) return -1;
				if ((!a.segments && !a.segments?.length) && (b.segments && b.segments.length)) return 1;

				if (category) {
					if (a.category == category && b.category != category) return -1;
					if (a.category != category && b.category == category) return 1;
				}

				if (a.priority > b.priority) return -1;
				if (a.priority < b.priority) return 1;
				return 0;
			});

		if (campaigns.length > 0) {
			const template = (campaigns[0].quests || [])[0]
				? QuestTemplateBuilder.buildTemplate(
					campaigns[0].quests[0].content!,
				)
				: undefined;

			return {
				campaign: campaigns[0],
				template,
			};
		}
		return {} as Campaign;
	}

	@get('/quests/{orgId}/no-wallet/{raleonId}', {
		responses: {
			'200': {
				description: 'Quest model instance',
				content: {'application/json': {schema: getModelSchemaRef(Quest)}},
			},
		},
	})
	@skipGuardCheck()
	async getQuestForNoWallet(
		@param.path.number('orgId') orgId: number, // this is called from the snippet, do not inject user org id
		@param.path.string('raleonId') raleonId: string,
	): Promise<any> {
		const raleonUser = await this.raleonUserService.getUserFromIdentities([{
			identityType: 'raleon_id',
			identityValue: raleonId
		}]);

		let campaigns: Campaign[] = await this.campaignRepository.find({
			include: [
				{
					relation: 'quests',
					scope: {
						include: [
							'content',
							{
								relation: 'goals',
								scope: {include: ['content']},
							},
							{
								relation: 'rewards',
								scope: {include: ['content']},
							},
							{
								relation: 'journeys',
								scope: {
									where: {
										raleonUserId: raleonUser.id
									}
								},
							}
						],
					},
				},
				{
					relation: 'segments'
				}
			],
			where: {orgId: orgId},
		});

		campaigns = campaigns
			.filter(campaign =>
				(
					!campaign.segments ||
					!campaign.segments.length
				) &&
				campaign.status.toLowerCase() == 'running' &&
				(
					areNoStartedJourneys(campaign) ||
					areSomeIncompleteJourneys(campaign)
				)
			).sort((a: any, b: any) =>
				(a.priority == null ? Infinity : a.priority) -
				(b.priority == null ? Infinity : b.priority)
			);

		if (campaigns.length > 0) {
			const template = (campaigns[0].quests || [])[0]
				? QuestTemplateBuilder.buildTemplate(
					campaigns[0].quests[0].content!,
				)
				: undefined;

			return {
				campaign: campaigns[0],
				template,
			};
		}

		return null;
	}

	@post('/campaigns/{orgId}/{segmentId}', {
		responses: {
			'200': {
				description: 'Campaign model instance',
				content: {'application/json': {schema: getModelSchemaRef(Quest)}},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	async createQuestAndDefinitionWithSegment(
		@injectUserOrgId() orgId: number,
		@param.path.number('segmentId') segmentId: number,
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(Quest, {
						title: 'NewQuestInCampaign',
						exclude: ['id', 'campaignId'],
					}),
				},
			},
		})
		quest: Partial<Quest>,
	): Promise<Quest> {
		let quests;
		let transaction: any =
			await this.campaignRepository.dataSource.beginTransaction({
				isolationLevel: IsolationLevel.READ_COMMITTED,
			});

		try {
			let campaign = new Campaign();
			campaign.orgId = orgId;
			campaign.name = quest.name + ' Definition';
			campaign = await this.campaignRepository.create(campaign, {transaction});

			await this.campaignSegmentRepository.create(
				{
					campaignId: campaign.id,
					segmentId: segmentId,
				},
				{transaction},
			);

			quests = await this.campaignRepository
				.quests(campaign.id)
				.create(quest, {transaction});

			await transaction.commit();
		} catch (err) {
			console.log(`Error: ${err}`);
			await transaction.rollback();
		}
		return quests || ({} as Quest);
	}

	@post('/campaigns/{orgId}', {
		responses: {
			'200': {
				description: 'Campaign model instance',
				content: {'application/json': {schema: getModelSchemaRef(Quest)}},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	async createQuestAndDefinition(
		@injectUserOrgId() orgId: number,
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(Quest, {
						title: 'NewQuestInCampaign',
						exclude: ['id', 'campaignId'],
					}),
				},
			},
		})
		quest: Partial<Quest>,
	): Promise<Quest> {
		let campaign = new Campaign();
		campaign.orgId = orgId;
		campaign.name = quest.name + ' Definition';
		campaign = await this.campaignRepository.create(campaign);

		return await this.campaignRepository.quests(campaign.id).create(quest);
	}

	@post('/quests/{id}/content', {
		responses: {
			'200': {
				description: 'Quest Content',
				content: {'application/json': {schema: getModelSchemaRef(Content)}},
			},
		},
	})
	@authenticate('jwt')
	async createContent(
		@param.path.number('id')
		@modelIdForGuard(Quest)
		id: typeof Quest.prototype.id,

		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(Content, {
						title: 'NewContentInQuest',
						exclude: ['id'],
					}),
				},
			},
		})
		content: Partial<Content>,
	): Promise<Content> {
		return await this.questRepository.content(id).create(content);
	}
}


function areNoStartedJourneys(campaign: Campaign): boolean {
	return (campaign.quests || []).every(quest => !(quest.journeys || []).length);
}

function areSomeIncompleteJourneys(campaign: Campaign): boolean {
	/*if (campaign.orgId === 1 || campaign.orgId === 5) {
		return true;
	}*/
	return (campaign.quests || [])
		.every(quest => (quest.journeys || [])
			.some(journey => journey.status !== 'completed' && journey.status !== 'ignored'));
}
