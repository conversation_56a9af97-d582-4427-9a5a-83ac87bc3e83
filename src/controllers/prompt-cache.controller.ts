import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {inject, service} from '@loopback/core';
import {post, get, param, api, HttpErrors} from '@loopback/rest';
import {SecurityBindings, UserProfile} from '@loopback/security';
import {PromptCacheService} from '../services/prompt/prompt-cache.service';
import {basicAuthorization} from '../services';
import {GuardSkipStrategy, guardStrategy, skipGuardCheck, injectUserOrgId} from '../interceptors';

/**
 * Controller for handling prompt cache operations
 */
@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class PromptCacheController {
  constructor(
    @inject(SecurityBindings.USER) private user: UserProfile,
    @service(PromptCacheService) private promptCacheService: PromptCacheService,
  ) {}

  /**
   * Initialize the prompt cache for the user's organization
   */
  @post('/initialize-prompt-cache')
  @authenticate('jwt')
  @skipGuardCheck()
  async initializeCache(
    @injectUserOrgId() orgId: number
  ): Promise<object> {
    try {
      if (!orgId) {
        throw new HttpErrors.BadRequest('No organization associated with user');
      }
      
      // Start caching in the background - don't wait for completion
      Promise.all([
        this.promptCacheService.cacheContextData(orgId),
        this.promptCacheService.cacheMetricsData(orgId)
      ]).catch(error => {
        console.error('Error during cache initialization:', error);
      });
      
      // Return success immediately
      return {
        success: true,
        message: 'Cache initialization started'
      };
    } catch (error) {
      console.error('Error in cache initialization:', error);
      throw new HttpErrors.InternalServerError('Failed to initialize cache');
    }
  }
  
  /**
   * Check cache status for the current organization
   */
  @get('/prompt-cache-status')
  @authenticate('jwt')
  @skipGuardCheck()
  async getCacheStatus(
    @injectUserOrgId() orgId: number
  ): Promise<object> {
    try {
      if (!orgId) {
        throw new HttpErrors.BadRequest('No organization associated with user');
      }
      
      const contextCacheExists = await this.promptCacheService.checkContextCacheExists(orgId);
      const metricsCacheExists = await this.promptCacheService.checkMetricsCacheExists(orgId);
      
      return {
        success: true,
        contextCached: contextCacheExists,
        metricsCached: metricsCacheExists,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error checking cache status:', error);
      throw new HttpErrors.InternalServerError('Failed to check cache status');
    }
  }
  
  /**
   * Admin endpoint to clear cache for an organization
   */
  @post('/admin/clear-prompt-cache/{orgId}')
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['raleon-admin'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async clearCache(
    @param.path.number('orgId') orgId: number
  ): Promise<object> {
    try {
      if (!orgId) {
        throw new HttpErrors.BadRequest('No organization ID provided');
      }
      
      await this.promptCacheService.clearCache(orgId);
      
      return {
        success: true,
        message: 'Cache cleared successfully'
      };
    } catch (error) {
      console.error('Error clearing cache:', error);
      throw new HttpErrors.InternalServerError('Failed to clear cache');
    }
  }
}