import {User} from '@loopback/authentication-jwt';
import {api, post, requestBody} from '@loopback/rest';
import {guardStrategy, GuardSkipStrategy, skipGuardCheck, injectUserOrgId} from '../interceptors';
import sharp from 'sharp';
const Color = require('color');

import OpenAI from "openai";
import {repository} from '@loopback/repository';
import {OrganizationRepository} from '../repositories';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../services';
import {ProoductCatalogService} from '../services/shopify/product-catalog.service';
import {service} from '@loopback/core';
import { getURL } from '../utils';
import * as cheerio from 'cheerio'
import {OnboardingService} from '../services/onboarding.service';
const fetch = require('node-fetch')
const openai = new OpenAI({ apiKey: '***************************************************' });
const ONBOARD_URL = process.env.ONBOARDING_AWS_URL || 'ojpurdnz1b.execute-api.us-east-1.amazonaws.com';

@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class OnboardAutomationController {

	static historyCache: any = {};

	constructor(
		@repository(OrganizationRepository) public organizationRepository: OrganizationRepository,
		@service(ProoductCatalogService) public productCatalogService: ProoductCatalogService,
		@service(OnboardingService) public onboardingService: OnboardingService,
	) { }

	@post('/onboard/begin', {
		responses: {
			'200': {
				description: 'User',
				content: {
					'application/json': {
						schema: {
							'x-ts-type': User,
						},
					},
				},
			},
		},
	})

	@authenticate('jwt')
	@skipGuardCheck()
	@authorize({
		allowedRoles: ['admin', 'raleon-support', 'customer-admin'],
		voters: [basicAuthorization],
	})
	async onboardBegin(
		@requestBody()
		body: any,
		@injectUserOrgId() orgId: number
	) {
		return this.onboardingService.onboardBegin(body, orgId);
	};

	@post('/onboard/description/generate', {
		responses: {
			'200': {
				description: 'User',
				content: {
					'application/json': {
						schema: {
							'x-ts-type': User,
						},
					},
				},
			},
		},
	})

	@authenticate('jwt')
	@skipGuardCheck()
	@authorize({
		allowedRoles: ['admin', 'raleon-support', 'customer-admin'],
		voters: [basicAuthorization],
	})
	async generateDescription(
		@requestBody()
		body: any,
		@injectUserOrgId() orgId: number
	) {
		try {
			if (!body.url || typeof body.url !== 'string') {
				throw new Error('Invalid or missing URL.');
			}

			const url = body.url.startsWith('http')
				? body.url
				: `https://${body.url}`;

			// Fetch the HTML content
			const response = await fetch(url);
			const data = await response.text();

			// Load HTML into cheerio
			const $ = cheerio.load(data);

			let valuableData: string[] = [];

			// Extract and print meta description tags
			$('meta[name="description"]').each((i, elem) => {
				let description = $(elem).attr('content');
				if (description) {
					valuableData.push(description);
				}
			});

			// Extract and print title tags
			$('title').each((i, elem) => {
				let title = $(elem).text();
				if (title) {
					valuableData.push(title);
				}
			});

			$('h1, h2, h3, h4, h5, h6, p, li, span, article').each((i, elem) => {
				let text = $(elem).text();
				if (text) {
					valuableData.push(text);
				}
			});

			const completion = await openai.chat.completions.create({
				messages: [
					{
						role: "system",
						content:
							"I need to generate description and sampleLanguage of what this ecommerce store does.\n\n" +
							"Here are the guidelines:\n" +
							"- The description should be concise, but include language the store uses.\n" +
							"- The sampleLanugage should be an example of language the store uses.\n" +
							"Based on the following data, please generate names and descriptions in this style:\n\n" +
							`${JSON.stringify({
								text: valuableData.join('\n\n')
							})} \n\n` +
							"The response should only include the filled-in request JSON in this format:\n\n" +
							`${JSON.stringify({
								description: "A description of the store",
								sampleLanguage: "A sample sentence that the store might use",
							})}`,
					}
				],
				model: "gpt-4-1106-preview",
				response_format: { type: "json_object" }
				//model: "gpt-4"
			} as any );

			try {
				console.log(completion.choices[0].message.content);
				if(completion.choices[0].message.content)
				{
					let response = JSON.parse(completion.choices[0].message.content);
					if(response.description && response.sampleLanguage){
						this.organizationRepository.updateById(orgId, {
							description: response.description,
							sampleLanguage: response.sampleLanguage
						});
					}
					return {
						statusCode: 200,
						body: {
							orgId: orgId,
							data: response
						}
					}
				}
				return body;
			} catch (error) {
				console.log(error);
			}

			return {
				statusCode: 200,
				body: {
					orgId: orgId,
					externalDomain: body.externalDomain,
					data: data
				}
			}
		} catch (error) {
			console.error(`Error fetching data from URL: ${error.message}`);
			return {
				statusCode: 500,
				body: {
					error: error.message
				}
			}
		}
	}

	isValidUrl(url: string) {
		try {
			new URL(url);
			return true;
		} catch (_) {
			return false;
		}
	}


	@post('/onboard/pre-process', {
		responses: {
			'200': {
				description: 'User',
				content: {
					'application/json': {
						schema: {
							'x-ts-type': User,
						},
					},
				},
			},
		},
	})
	// @authenticate('jwt')
	// @authorize({
	// 	allowedRoles: ['admin', 'raleon-support'],
	// 	voters: [basicAuthorization],
	// })
	@skipGuardCheck()
	async onboardPreprocess(
		@requestBody()
		body: any,

		// @injectUserOrgId() orgId: number
	) {
		// const org = await this.organizationRepository.findById(orgId);
		let url = body.url; // 'https://www.vervecoffee.com/';
		// if (!url) {
		// 	url = org?.externalDomain;
		// }

		// const { screenshot, text, html, links } = await generateScreenshotAndText(url);
		// const colors = await getMostCommonColors(screenshot, 10);

		const colors = [{ color: '#FAFAF9', count: 2500000 }, { color: "#FFFFFF", count: 750000 }, { color: "#000000", count: 500000}] // await getMostCommonColors(screenshot, 10);

		const text = `Skip to content\nHome\nCatalog\nContact\nRaleon Brandon Test\nCart\nGenerated test data\n\nA theme and populated test store by Shopify to help you test commerce primitives.\n\nShop products\nFeatured products\nThe Collection Snowboard: Liquid\nRegular price\n$749.95\nThe Multi-managed Snowboard\nRegular price\n$629.95\nThe Complete Snowboard\nRegular price\n$699.95\nThe Multi-location Snowboard\nRegular price\n$729.95\nThe Collection Snowboard: Hydrogen\nRegular price\n$600.00\nSale\nThe Compare at Price Snowboard\nRegular price\n$885.95 \nSale price\n$785.95\n1\n/\nof\n4\nStay hydrated\n\nTry our wax subscription so you can fly high, but never dry.\n\nGet waxy now\nCollections\nHydrogen\nAutomated Collection\nQuick links\nHome\nCatalog\nContact\n© 2023 / Shopify, Inc.\n\n©2023/ Shopify, Inc.\n\nResources\nGenerated test data docs\nGraphiQL API explorer\nPayment methods\n© 2023, Raleon Brandon Test Powered by Shopify`;
		const links = ['https://shopify.dev/docs/apps/tools/development-stores/generated-data', 'https://shopify.dev/docs/apps/tools/graphiql-admin-api', 'https://www.shopify.com/?utm_campaign=poweredby&utm_medium=shopify&utm_source=onlinestore']


		const input = [{
				role: "system",
				content: `The messages following the questions are data scraped from an ecommerce website, ${url}. Please determine and answer the following questions:
				  - which industry does the store most closely fit into out of the following: (Health, Household Care, Beauty, Toys, Hobby, DIY, Fashion - Non-Luxury, Fashion - Luxury, Food, Electronics, Cooking)?
				  - what are the social media URLs for this ecommerce website?
				    - Make sure to include all discovered links from sites including but not limited to:
					  - Facebook
					  - Twitter
					  - Instagram
					  - Pintrest
					  - LinkedIn
					  - Youtube
					  - TikTok
					  - Snapchat
					  - Twitch
					  - Reddit
					  - Tumblr
				  - If you were to create a loyalty program for this store:
				    - Loyalty Program Objectives:
						- Customers can earn points and other rewards for certain actions
						  - The action portion of the "way to earn" will be represented by conditions
						  	- For example, "Each $ Spent" is a condition that represents the action of a customer spending money
							- NTH Purchase, would represent a customer making their NTH purchase since being a patron of the store
							- etc
						- Customers can redeem points for specific rewards directly
						- The objective of the points and rewards is to incentivize customers to return for more purchases in the future, building brand loyalty in the process.
							- However, the points system and rewards must be appropriately awarded so that the store doesn't give away too much or too little.
							- In your recommendations, attempt to find the optimal balance in the rewards such that they are:
							- enticing and relevant for a customer of this store
								- ie. rewards should be valuable and attainable
							- profitable to the store
								- ie. ensure that the store isn't giving away too much gross profit wtih excessive points, discounting, rewards, etc that are too easily achieved.
					- What branding text and colors should be used for this loyalty program?
					    - Make sure the background color contrasts with the webpage
						- None of the colors should blend in with the chosen background color
						- The launcher colors should stand out from the rest of the site, to draw attention to the loyalty program.
						- The warning color:
							- should usually be a yellowish, redish, or orangeish color
							- ideally should be a color that is used elsewhere on the site, but if not just choose the best yellow, red, or orange color to use against the branding background
					- What would the approximate cost/benefit be for the store to run this program?
					  - use specific or approximate figures for the impacts to revenue/margins/etc

				  Format the answer in its entirety as valid JSON (no leading or trailing text around the JSON, no backtick delimiters/etc around the JSON, no trailing commas after the last array item or object property, no comments, correctly escaped string values, etc), with the following schema:

				  {
					"industry": "Fashion - Non-Luxury",
					"socialMedia": [
						{ "type": "facebook", "url":  "https://www.facebook.com/vervecoffee" },
						{ "type": "instagram", "url":  "https://www.instagram.com/vervecoffee/" },
					],
					"branding": {
						"colors": {
							"backgroundColor": "#000000",
							"textColor": "#ffffff",
							"buttonTextColor": "#000000",
							"buttonBackgroundColor": "#ffffff",
							"linkColor": "#aaaaaa",
							"accentColor": {
								"from": "#f0abfc",
								"to": "#f87171",
							},
							"secondaryColor": "#BBF7D0",
							"warningColor": "#EFC030"
						},
						"launcher": {
							"callToAction": "JOIN OUR LOYALTY PROGRAM",
							"styling": {
								"textColor": "#ffffff",
								"backgroundColor": "#000000",
							}
						},
						"guest": {
							"content": {
								"title": "JOIN COMMUNITY",
								"subtitle": "You receive rewards by completing ways to earn and making purchases. Collect XP to move across levels or spend it on extra rewards!",
								"benefitsTitle": "Member Benefits",
							}
						},
						"member": {
							"content": {
								"rewardsTitle": "Your Rewards",
								"wteTitle": "Ways to Earn",
								"rewardShopTitle": "Available Rewards",
							}
						}
					}
				  }

				`
			}, {
				role: 'user',
				content: `Scraped Text:

				${text}`
			}, {
				role: 'user',
				content: `Scraped Links:

				${links.join('\n')}
				`
			}, {
				role: 'user',
				content: `Scraped Color Frequency (determined by taking a screnshot of the e-commerce store, and counting the frequency of each color on a per-pixel basis):

				${JSON.stringify(colors)}
				`
			}
		];
		const output = await openai.chat.completions.create({
			messages: input as any,
			model: "gpt-4",
		});

		OnboardAutomationController.historyCache[url] = {
			input,
			output
		};

		return output.choices[0].message.content;
	}

	@post('/onboard/auto', {
		responses: {
			'200': {
				description: 'User',
				content: {
					'application/json': {
						schema: {
							'x-ts-type': User,
						},
					},
				},
			},
		},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'raleon-support'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async onboard(
		@requestBody()
		body: any,

		@injectUserOrgId() orgId: number
	) {
		let url = body.url;
		if (!url) {
			const org = await this.organizationRepository.findById(orgId);
			url = org?.externalDomain;
		}

		// const productCatalog = await this.productCatalogService.getProductCatalog('test', '', orgId);
		// console.log(productCatalog);

		if (!OnboardAutomationController.historyCache[url]) {
			await this.onboardPreprocess(body);
		}

		const { input, output } = OnboardAutomationController.historyCache[url];

		const productCatalog = {
			"data": {
			  "products": {
				"edges": [
				  {
					"node": {
					  "id": "gid://shopify/Product/6931570982985",
					  "createdAt": "2023-10-26T14:20:56Z",
					  "updatedAt": "2023-10-26T14:21:07Z",
					  "title": "The Collection Snowboard: Liquid",
					  "handle": "the-collection-snowboard-liquid",
					  "productType": "",
					  "priceRangeV2": {
						"maxVariantPrice": {
						  "amount": "749.95",
						  "currencyCode": "USD"
						},
						"minVariantPrice": {
						  "amount": "749.95",
						  "currencyCode": "USD"
						}
					  },
					  "description": "",
					  "totalInventory": 50,
					  "status": "ACTIVE"
					}
				  },
				  {
					"node": {
					  "id": "gid://shopify/Product/6931570950217",
					  "createdAt": "2023-10-26T14:20:56Z",
					  "updatedAt": "2023-10-26T14:21:04Z",
					  "title": "Selling Plans Ski Wax",
					  "handle": "selling-plans-ski-wax",
					  "productType": "",
					  "priceRangeV2": {
						"maxVariantPrice": {
						  "amount": "49.95",
						  "currencyCode": "USD"
						},
						"minVariantPrice": {
						  "amount": "9.95",
						  "currencyCode": "USD"
						}
					  },
					  "description": "",
					  "totalInventory": 30,
					  "status": "ACTIVE"
					}
				  },
				  {
					"node": {
					  "id": "gid://shopify/Product/6931570917449",
					  "createdAt": "2023-10-26T14:20:54Z",
					  "updatedAt": "2023-10-26T14:20:57Z",
					  "title": "The Collection Snowboard: Oxygen",
					  "handle": "the-collection-snowboard-oxygen",
					  "productType": "",
					  "priceRangeV2": {
						"maxVariantPrice": {
						  "amount": "1025.0",
						  "currencyCode": "USD"
						},
						"minVariantPrice": {
						  "amount": "1025.0",
						  "currencyCode": "USD"
						}
					  },
					  "description": "",
					  "totalInventory": 50,
					  "status": "ACTIVE"
					}
				  },
				  {
					"node": {
					  "id": "gid://shopify/Product/6931570720841",
					  "createdAt": "2023-10-26T14:20:53Z",
					  "updatedAt": "2023-10-26T14:20:57Z",
					  "title": "The Collection Snowboard: Hydrogen",
					  "handle": "the-collection-snowboard-hydrogen",
					  "productType": "",
					  "priceRangeV2": {
						"maxVariantPrice": {
						  "amount": "600.0",
						  "currencyCode": "USD"
						},
						"minVariantPrice": {
						  "amount": "600.0",
						  "currencyCode": "USD"
						}
					  },
					  "description": "",
					  "totalInventory": 50,
					  "status": "ACTIVE"
					}
				  },
				  {
					"node": {
					  "id": "gid://shopify/Product/6931570491465",
					  "createdAt": "2023-10-26T14:20:51Z",
					  "updatedAt": "2023-10-26T14:20:57Z",
					  "title": "The Videographer Snowboard",
					  "handle": "the-videographer-snowboard",
					  "productType": "",
					  "priceRangeV2": {
						"maxVariantPrice": {
						  "amount": "885.95",
						  "currencyCode": "USD"
						},
						"minVariantPrice": {
						  "amount": "885.95",
						  "currencyCode": "USD"
						}
					  },
					  "description": "",
					  "totalInventory": 50,
					  "status": "ACTIVE"
					}
				  },
				  {
					"node": {
					  "id": "gid://shopify/Product/6931570524233",
					  "createdAt": "2023-10-26T14:20:52Z",
					  "updatedAt": "2023-10-26T14:20:56Z",
					  "title": "The Draft Snowboard",
					  "handle": "the-draft-snowboard",
					  "productType": "",
					  "priceRangeV2": {
						"maxVariantPrice": {
						  "amount": "2629.95",
						  "currencyCode": "USD"
						},
						"minVariantPrice": {
						  "amount": "2629.95",
						  "currencyCode": "USD"
						}
					  },
					  "description": "",
					  "totalInventory": 20,
					  "status": "DRAFT"
					}
				  },
				  {
					"node": {
					  "id": "gid://shopify/Product/6931570884681",
					  "createdAt": "2023-10-26T14:20:53Z",
					  "updatedAt": "2023-10-26T14:20:55Z",
					  "title": "The 3p Fulfilled Snowboard",
					  "handle": "the-3p-fulfilled-snowboard",
					  "productType": "",
					  "priceRangeV2": {
						"maxVariantPrice": {
						  "amount": "2629.95",
						  "currencyCode": "USD"
						},
						"minVariantPrice": {
						  "amount": "2629.95",
						  "currencyCode": "USD"
						}
					  },
					  "description": "",
					  "totalInventory": 20,
					  "status": "ACTIVE"
					}
				  },
				  {
					"node": {
					  "id": "gid://shopify/Product/6931570851913",
					  "createdAt": "2023-10-26T14:20:53Z",
					  "updatedAt": "2023-10-26T14:20:55Z",
					  "title": "The Multi-managed Snowboard",
					  "handle": "the-multi-managed-snowboard",
					  "productType": "",
					  "priceRangeV2": {
						"maxVariantPrice": {
						  "amount": "629.95",
						  "currencyCode": "USD"
						},
						"minVariantPrice": {
						  "amount": "629.95",
						  "currencyCode": "USD"
						}
					  },
					  "description": "",
					  "totalInventory": 100,
					  "status": "ACTIVE"
					}
				  },
				  {
					"node": {
					  "id": "gid://shopify/Product/6931570819145",
					  "createdAt": "2023-10-26T14:20:53Z",
					  "updatedAt": "2023-10-26T14:20:55Z",
					  "title": "The Multi-location Snowboard",
					  "handle": "the-multi-location-snowboard",
					  "productType": "",
					  "priceRangeV2": {
						"maxVariantPrice": {
						  "amount": "729.95",
						  "currencyCode": "USD"
						},
						"minVariantPrice": {
						  "amount": "729.95",
						  "currencyCode": "USD"
						}
					  },
					  "description": "",
					  "totalInventory": 100,
					  "status": "ACTIVE"
					}
				  },
				  {
					"node": {
					  "id": "gid://shopify/Product/6931570753609",
					  "createdAt": "2023-10-26T14:20:53Z",
					  "updatedAt": "2023-10-26T14:20:55Z",
					  "title": "The Compare at Price Snowboard",
					  "handle": "the-compare-at-price-snowboard",
					  "productType": "",
					  "priceRangeV2": {
						"maxVariantPrice": {
						  "amount": "785.95",
						  "currencyCode": "USD"
						},
						"minVariantPrice": {
						  "amount": "785.95",
						  "currencyCode": "USD"
						}
					  },
					  "description": "",
					  "totalInventory": 10,
					  "status": "ACTIVE"
					}
				  },
				  {
					"node": {
					  "id": "gid://shopify/Product/6931570655305",
					  "createdAt": "2023-10-26T14:20:52Z",
					  "updatedAt": "2023-10-26T14:20:55Z",
					  "title": "The Complete Snowboard",
					  "handle": "the-complete-snowboard",
					  "productType": "snowboard",
					  "priceRangeV2": {
						"maxVariantPrice": {
						  "amount": "699.95",
						  "currencyCode": "USD"
						},
						"minVariantPrice": {
						  "amount": "699.95",
						  "currencyCode": "USD"
						}
					  },
					  "description": "This PREMIUM snowboard is so SUPERDUPER awesome!",
					  "totalInventory": 50,
					  "status": "ACTIVE"
					}
				  },
				  {
					"node": {
					  "id": "gid://shopify/Product/6931570589769",
					  "createdAt": "2023-10-26T14:20:52Z",
					  "updatedAt": "2023-10-26T14:20:55Z",
					  "title": "The Archived Snowboard",
					  "handle": "the-archived-snowboard",
					  "productType": "",
					  "priceRangeV2": {
						"maxVariantPrice": {
						  "amount": "629.95",
						  "currencyCode": "USD"
						},
						"minVariantPrice": {
						  "amount": "629.95",
						  "currencyCode": "USD"
						}
					  },
					  "description": "",
					  "totalInventory": 50,
					  "status": "ARCHIVED"
					}
				  },
				  {
					"node": {
					  "id": "gid://shopify/Product/6931570557001",
					  "createdAt": "2023-10-26T14:20:52Z",
					  "updatedAt": "2023-10-26T14:20:55Z",
					  "title": "The Hidden Snowboard",
					  "handle": "the-hidden-snowboard",
					  "productType": "",
					  "priceRangeV2": {
						"maxVariantPrice": {
						  "amount": "749.95",
						  "currencyCode": "USD"
						},
						"minVariantPrice": {
						  "amount": "749.95",
						  "currencyCode": "USD"
						}
					  },
					  "description": "",
					  "totalInventory": 50,
					  "status": "ACTIVE"
					}
				  },
				  {
					"node": {
					  "id": "gid://shopify/Product/6931570458697",
					  "createdAt": "2023-10-26T14:20:51Z",
					  "updatedAt": "2023-10-26T14:20:55Z",
					  "title": "The Minimal Snowboard",
					  "handle": "the-minimal-snowboard",
					  "productType": "",
					  "priceRangeV2": {
						"maxVariantPrice": {
						  "amount": "885.95",
						  "currencyCode": "USD"
						},
						"minVariantPrice": {
						  "amount": "885.95",
						  "currencyCode": "USD"
						}
					  },
					  "description": "",
					  "totalInventory": 50,
					  "status": "ACTIVE"
					}
				  },
				  {
					"node": {
					  "id": "gid://shopify/Product/6931570786377",
					  "createdAt": "2023-10-26T14:20:53Z",
					  "updatedAt": "2023-10-26T14:20:54Z",
					  "title": "The Inventory Not Tracked Snowboard",
					  "handle": "the-inventory-not-tracked-snowboard",
					  "productType": "",
					  "priceRangeV2": {
						"maxVariantPrice": {
						  "amount": "949.95",
						  "currencyCode": "USD"
						},
						"minVariantPrice": {
						  "amount": "949.95",
						  "currencyCode": "USD"
						}
					  },
					  "description": "",
					  "totalInventory": 0,
					  "status": "ACTIVE"
					}
				  },
				  {
					"node": {
					  "id": "gid://shopify/Product/6931570688073",
					  "createdAt": "2023-10-26T14:20:53Z",
					  "updatedAt": "2023-10-26T14:20:54Z",
					  "title": "Gift Card",
					  "handle": "gift-card",
					  "productType": "",
					  "priceRangeV2": {
						"maxVariantPrice": {
						  "amount": "100.0",
						  "currencyCode": "USD"
						},
						"minVariantPrice": {
						  "amount": "10.0",
						  "currencyCode": "USD"
						}
					  },
					  "description": "This is a gift card for the store",
					  "totalInventory": 0,
					  "status": "ACTIVE"
					}
				  },
				  {
					"node": {
					  "id": "gid://shopify/Product/6931570622537",
					  "createdAt": "2023-10-26T14:20:52Z",
					  "updatedAt": "2023-10-26T14:20:54Z",
					  "title": "The Out of Stock Snowboard",
					  "handle": "the-out-of-stock-snowboard",
					  "productType": "",
					  "priceRangeV2": {
						"maxVariantPrice": {
						  "amount": "885.95",
						  "currencyCode": "USD"
						},
						"minVariantPrice": {
						  "amount": "885.95",
						  "currencyCode": "USD"
						}
					  },
					  "description": "",
					  "totalInventory": 0,
					  "status": "ACTIVE"
					}
				  }
				]
			  }
			},
			"extensions": {
			  "cost": {
				"requestedQueryCost": 502,
				"actualQueryCost": 36,
				"throttleStatus": {
				  "maximumAvailable": 1000,
				  "currentlyAvailable": 964,
				  "restoreRate": 50
				}
			  }
			}
		  };

		const wteSchemas =  [{
			type: 'dollar-spent',
			label: 'Each $ Spent',
			subtitle: 'Converts all purchases to points.',
			conditionTypes: [{
				fake: true,
				variables: [{
					label: null,
					value: 'purchaseTotalIncrease',
					noInput: true,
					amount: 1,

					operators: [{
						label: 'No Conditions For This Type',
						value: '='
					}]
				}]
			},{
				fake: true,
				variables: [{
					label: null,
					value: null,
					noInput: true,
					amount: null,

					operators: [{
						label: 'Click SETUP REWARDS To Continue',
						value: null
					}]
				}]
			}],
			imageSlotKey: 'test-image'
		}, {
			type: 'nth-purchase',
			label: 'NTH Purchase',
			subtitle: 'Customer Makes their [X] Purchase Within the last Y days/months',
			conditionTypes: [{
				variables: [{
					label: 'Purchases',
					value: 'purchaseCount',

					operators: [{
						label: 'Custormer Makes At Least',
						value: '>='
					}],
				}],

			}, {
				optional: true,
				variables: [{
					label: 'Days',
					value: 'purchaseDateAgo',
					operators: [{
						label: 'Within The Last',
						value: '<='
					}]
				}],

			}],
			imageSlotKey: 'test-image'
		}, {
			type: 'first-loyalty-purchase',
			label: 'First Purchase As Loyalty Member',
			subtitle: 'Customer makes their first purchase as a loyalty member Within the last X days/months',

			conditionTypes: [{
				fake: true,
				variables: [{
					value: 'purchaseCount',
					noInput: true,
					amount: 1,
					operators: [{
						label: 'Customer Makes First Purchase',
						value: '='
					}],
				}],

			}, {
				optional: true,
				variables: [{
					label: 'Days',
					value: 'purchaseDateAgo',

					operators: [{
						label: 'Within The Last',
						value: '<='
					}]
				}]
			}],
			imageSlotKey: 'test-image'
		}, {
			type: 'buy-product',
			label: 'Buy Specific Product',
			subtitle: 'Customer has purchased [Product] from store',
			conditionTypes: [{
				variables: [{
					label: '(SKU)',
					value: 'purchasedSku',
					operators: [{
						label: 'Product Is',
						value: '='
					}],
				}],

			}],
			imageSlotKey: 'test-image'
		}];

		const rewardSchemas = [{
			label: 'Points',
			value: 'points',
			collapsedTitle: 'Points',
			subtitle: '# of Points',
			imageSlotKey: 'test-image',
			showPointsEquivalency: true,
			restrictionTypes: [],
			limitTypes: [{
				value: 'maxUserRedemptions',
				label: 'Redeems per User'
			},{
				value: 'maxUserGrants',
				label: 'Max Amount That Can Be Earned',
			}]
		}, {
			label: 'Dollars Off Coupon',
			value: 'dollars-off-coupon',
			collapsedTitle: 'Dollars Off',
			subtitle: 'Dollars off',
			imageSlotKey: 'test-image',
			restrictionTypes: [{
				value: 'minimumOrderAmount',
				label: 'Minimum Order Amount'
			}, {
				value: 'expiresInDays',
				label: 'Expires In (Days)'
			}],
			limitTypes: [{
				value: 'maxUserRedemptions',
				label: 'Redeems per User'
			},{
				value: 'maxUserGrants',
				label: 'Max Amount That Can Be Earned',
			}]
		}, {
			label: '% Discount',
			value: 'percent-discount',
			collapsedTitle: '% Discount',
			subtitle: 'Percent off',
			imageSlotKey: 'test-image',
			restrictionTypes: [{
				value: 'minimumOrderAmount',
				label: 'Minimum Order Amount'
			}, {
				value: 'expiresInDays',
				label: 'Expires In (Days)'
			}, {
				value: 'maximumDiscount',
				label: 'Maximum Discount'
			}],
			limitTypes: [{
				value: 'maxUserRedemptions',
				label: 'Redeems per User'
			},{
				value: 'maxUserGrants',
				label: 'Max Amount That Can Be Earned',
			}]
		}, {
			label: 'Free Product',
			value: 'free-product',
			collapsedTitle: 'Free Product',
			subtitle: 'Select product to get for free',
			imageSlotKey: 'test-image',
			restrictionTypes: [{
				value: 'minimumOrderAmount',
				label: 'Minimum Order Amount'
			}, {
				value: 'expiresInDays',
				label: 'Expires In (Days)'
			}],
			limitTypes: [{
				value: 'maxUserRedemptions',
				label: 'Redeems per User'
			},{
				value: 'maxUserGrants',
				label: 'Max Amount That Can Be Earned',
			}]
		}];


		const completion = await openai.chat.completions.create({
			messages: [
			...input,
			{
				role: 'assistant',
				content: output.choices[0].message.content
			},
			{
				role: 'user',
				content: `Store Product Catalog:

				${JSON.stringify(productCatalog)}`
			},
			{
				role: "system",
				content: `
				  Based on what you know from above,
				  - If you were to create a loyalty program for this store:
				    - Loyalty Program Objectives:
						- Customers can earn points and other rewards for certain actions
						  - The action portion of the "way to earn" will be represented by conditions
						  	- For example, "Each $ Spent" is a condition that represents the action of a customer spending money
							- NTH Purchase, would represent a customer making their NTH purchase since being a patron of the store
							- etc
						- Customers can redeem points for specific rewards directly
						- The objective of the points and rewards is to incentivize customers to return for more purchases in the future, building brand loyalty in the process.
							- However, the points system and rewards must be appropriately awarded so that the store doesn't give away too much or too little.
							- In your recommendations, attempt to find the optimal balance in the rewards such that they are:
							- enticing and relevant for a customer of this store
								- ie. rewards should be valuable and attainable
							- profitable to the store
								- ie. ensure that the store isn't giving away too much gross profit wtih excessive points, discounting, rewards, etc that are too easily achieved.
				  	- How much would each point be worth (in dollars)?
					- What would some examples be of ways to earn rewards (including loyalty points) be for this store?
						- Must at least include "Each $ Spent" grants "points"
						- Each way to earn can have one or more rewards, although usually only one
						- For each way to earn, ALL conditionTypes from the schema must be represented in the conditions array in the answer
						- The available action/condition types that represent the ways to earn are based off of the "ways to earn" listed further below
						- The available reward types are based off of the "rewards" listed further below
					- What would some examples be of rewards that customers could redeem the loyalty points for in this hypothetical loyalty system for this store be?
						- The available reward types are based off of the "rewards" listed further below
						- For the free item rewards, the "amount" value should be just the numeric portion of the product node's id from the product catalog, following gid://shopify/Product/
						- Ideally try to include at least one of each type, with preferably 2+ free item examples
					- What branding text and colors should be used for this loyalty program?
					    - Make sure the background color contrasts with the webpage
						- None of the colors should blend in with the chosen background color
						- The launcher colors should stand out from the rest of the site, to draw attention to the loyalty program.
						- The warning color:
							- should usually be a yellowish, redish, or orangeish color
							- ideally should be a color that is used elsewhere on the site, but if not just choose the best yellow, red, or orange color to use against the branding background
					- What would the approximate cost/benefit be for the store to run this program?
					  - use specific or approximate figures for the impacts to revenue/margins/etc

				  Format the answer in its entirety as valid JSON (no leading or trailing text around the JSON, no backtick delimiters/etc around the JSON, no trailing commas after the last array item or object property, no comments, correctly escaped string values, etc), with the following schema:

				  {
					"dollarValuePerPoint": 0.01,
					"approximateCostBenefitToStore": "summary of cost/benefit impact to revenue / margins / etc goes here, use specific or approximate figures for the impacts to revenue/margins/etc",
					"waysToEarn": [{
						"conditions": [{
							"type":"dollar-spent",
							"label":"Each $ Spent",
							"subtitle":"Converts all purchases to points.",
							"variable": "purchaseTotalIncrease",
							"operator": "=",
							"amount": 1
						}],
						"rewards":[{
							"loyaltyRewardDefinition": {
								"grantable": true,
								"redeemable": false,
								"rewardCoupon": {
									"name": "Points",
									"amount": 100,
									"amountType": "points"
								}
							}
						}]
					},{
						"conditions": [{
							"type":"nth-purchase",
							"label":"NTH Purchase",
							"subtitle":"Customer Makes their [X] Purchase Within the last Y days/months",
							"variable": "purchaseCount",
							"operator": ">=",
							"amount": 10
						}, {
							"type":"nth-purchase",
							"label":"NTH Purchase",
							"subtitle":"Customer Makes their [X] Purchase Within the last Y days/months",
							"variable": "purchaseDateAgo",
							"operator": "<=",
							"amount": 30
						}],
						"rewards":[{
							"loyaltyRewardDefinition": {
								"grantable": true,
								"redeemable": false,
								"rewardCoupon": {
									"name": "30% off coupon",
									"amount": 30,
									"amountType": "percent-discount",
									"expiresInDays": 60,
									"maximumDiscount": 100
								},
								"maxUserGrants": 2
							}
						} ,{
							"points": 2000,
							"loyaltyRewardDefinition": {
								"grantable": true,
								"redeemable": false,
								"rewardCoupon": {
									"name": "$10 off coupon",
									"amount": 10,
									"amountType": "dollars-off-coupon",
									"minimumOrderTotal": 20,
									"expiresInDays": 90
								},
								"maxUserGrants": 2
							}
						}]
					}],
					"redeemableRewards": [
						{
							"points": 500,
							"loyaltyRewardDefinition": {
								"redeemable": true,
								"rewardCoupon": {
									"name": "5% off coupon",
									"amount": 5,
									"amountType": "percent-discount",
									"minimumOrderTotal": 10,
									"maximumDiscount": 50
								}
							}
						},
						{
							"points": 2000,
							"loyaltyRewardDefinition": {
								"redeemable": true,
								"rewardCoupon": {
									"name": "$10 off coupon",
									"amount": 10,
									"amountType": "dollars-off-coupon",
									"minimumOrderTotal": 50
								}
							}
						},
						{
							"points": 20000,
							"loyaltyRewardDefinition": {
								"redeemable": true,
								"rewardCoupon": {
									"name": "Free Coffee Variety pack with Mug",
									"amount": 54020340,
									"amountType": "free-product"
								}
							}
						}

					]
				  }


				  The possible ways to earn are:
				  ${JSON.stringify(wteSchemas)}

				  - Each of these specify the values used within the "waysToEarn" property in the answer JSON
				    - For each way to earn type used, ALL conditionTypes must be used in the conditions array in the answer

				  The available reward types are:
				  ${JSON.stringify(rewardSchemas)}

				  - Each of these specify the values used within both the "waysToEarn[].rewards" and "redeemableRewards" properties in the answer JSON
				  - For each reward in the answer, both in "waysToEarn" and "redeemableRewards" the following limit properties may be used, but are not required:
					- "maxUserGrants" - this should only be used for "waysToEarn" rewards, when "grantable" is true, and prevents users from earning more than the specified amount of the reward
					- "maxUserRedemptions" - this should be used for "redeemableRewards", when "redeemable" is true, and prevents users from redeeming more than the specified amount of the reward

				`
			}
		],
			model: "gpt-4",
		});


		// const results = JSON.parse(completion.choices[0].message.content || '{}');

		return completion.choices[0].message.content;

		// return {
		// 	url,
		// 	...JSON.parse(completion.choices[0].message.content || '{}'),
		// 	colors
		// }
	}


}

/* async function generateScreenshotAndText(url: string, outputPath: string = 'screenshot.png'): Promise<any> {
    const browser = await puppeteer.launch();
    const page = await browser.newPage();

    await page.goto(url, {
        waitUntil: 'networkidle2',
    });

	const self = globalThis;


    await page.evaluate(function () {
        (self as any).document.querySelectorAll("img,video").forEach((x: any) => x.remove());
        (self as any).document.querySelectorAll("*").forEach((x: any) => x.style.backgroundImage = "none" );
    });

    const text = await page.evaluate(function () {
        return (self as any).document.body.innerText;
    });

    const html = await page.evaluate(function () {
        return (self as any).document.body.innerHTML;
    });


    const links = await page.evaluate(function () {
        return [...(self as any).document.querySelectorAll('a[href^=http]')].map(x => x.href);
    });


    const screenshotBuffer = await page.screenshot({ fullPage: true });

    await browser.close();

    await sharp(screenshotBuffer).toFile(outputPath);

    return {
        screenshot: screenshotBuffer,
        text,
		html,
		links
    };
} */


async function getMostCommonColors(imagePath: any, topN = 5) {
    const image = sharp(imagePath);
    const metadata = await image.metadata();
    const raw = await image.raw().toBuffer();

    const colorCount: any = {};

    for (let i = 0; i < raw.length; i += 3) {
        const r = raw[i];
        const g = raw[i + 1];
        const b = raw[i + 2];
        const color = new Color({ r, g, b }).hex();

        if (colorCount[color]) {
            colorCount[color]++;
        } else {
            colorCount[color] = 1;
        }
    }

    const sortedColors = Object.entries(colorCount)
        .sort((a: any, b: any) => b[1] - a[1])
        .slice(0, topN)
        .map(entry => ({ color: entry[0], count: entry[1] }));

    return sortedColors;
}
