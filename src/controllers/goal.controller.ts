import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {inject, service} from '@loopback/core';
import {
	repository,
} from '@loopback/repository';
import {
	param,
	get,
	api,
	HttpErrors,
	RestBindings,
	Request,
	Response,
	del,
} from '@loopback/rest';
import {OrgGuardMultiHopPropertyStrategy, guardStrategy, modelIdForGuard, skipGuardCheck} from '../interceptors';
import {
	Campaign,
	Goal, Quest, RaleonUser,
} from '../models';
import {GoalRepository, JourneyRepository, QuestRepository} from '../repositories';
import {basicAuthorization} from '../services/basic.authorizor';
import {HasCustomEvent} from '../services/quests/goal-types/has-custom-event.service';
import {FollowOnTwitter} from '../services/quests/goal-types/follow-on-twitter.service';
import {ConfigSchema, GoalType, IsGoalCompleteObjectResult} from '../services/quests/goal-types/goal-type.abstract';
import {RaleonUserService} from '../services/raleon-user.service';
import {JoinDiscordServer} from '../services/quests/goal-types/join-discord-server.service';
import {HasTokenAction} from '../services/quests/goal-types/has-token-action.service';
import { DistinctEventCount } from '../services/quests/goal-types/distinct-event-count.service';
import fetch from 'node-fetch';

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardMultiHopPropertyStrategy<Goal, Quest, Campaign>({
	repositoryClass: GoalRepository,
	firstHopIdPropertyName: 'questId',
	firstHopRepositoryClass: QuestRepository,
	inclusionChainAfterFirstHop: {relation: 'campaign'},
	lastHopOrgIdPropertyName: 'orgId',
}))
export class GoalController {
	constructor(
		@repository(GoalRepository)
		public goalRepository: GoalRepository,

		@repository(JourneyRepository)
		private journeyRepository: JourneyRepository,

		@service(RaleonUserService)
		private raleonUserService: RaleonUserService,

		@service(HasCustomEvent)
		private hasCustomEventGoalType: HasCustomEvent,

		@service(HasTokenAction)
		private hasTokenActionType: HasTokenAction,

		@service(FollowOnTwitter)
		private followOnTwitterGoalType: FollowOnTwitter,

		@service(JoinDiscordServer)
		private joinDiscordServerGoalType: JoinDiscordServer,

		@service(DistinctEventCount)
		private distinctEventCountGoalType: DistinctEventCount,

		private goalTypes: GoalType[] = [
			hasCustomEventGoalType,
			followOnTwitterGoalType,
			joinDiscordServerGoalType,
			hasTokenActionType,
			distinctEventCountGoalType
		]
	) { }

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/goals/types/all', {
		responses: {
			'200': {
				description: 'Array of all GoalTypes',
				content: {
					'application/json': {
						schema: {type: 'array', items: 'object'},
					},
				},
			},
		},
	})
	async getGoalTypes(): Promise<object[]> {
		//TODO: Remove filter for FollowOnTwitter.GOAL_TYPE_NAME when it's live
		return this.goalTypes.map(x => ({
			goalType: x.getGoalTypeName(),
			friendlyName: x.getFriendlyName(),
			configSchema: x.getConfigDataSchema(),
			contentConfigSchema: x.getContentConfigSchema(),
			userDataSchema: x.getUserDataSchema()
		})).filter(x => x.goalType !== FollowOnTwitter.GOAL_TYPE_NAME);
	}

	@skipGuardCheck() //invoked from snippet, no auth
	@get('/goals/{id}/validate', {
		responses: {
			'200': {
				description: 'Has Goal Been Completed?',
				content: {
					'application/json': {
						schema: {type: 'boolean'},
					},
				},
			},
		},
	})
	async isComplete(
		@param.path.number('id') id: typeof Goal.prototype.id,
		@param.query.string('raleonId') raleonId: string,
		@param.query.string('orgId') orgId: string,
		@param.query.string('walletAddress') walletAddress?: string,
	): Promise<boolean | IsGoalCompleteObjectResult> {
		const goal = await this.goalRepository.findById(id);
		const goalType = this.goalTypes.find(goalType => {
			return goalType.getGoalTypeName() === goal.type.trim()
		});

		if (!goalType) {
			throw new HttpErrors.NotFound('Goal Type not found');
		}

		const identities = [{
			identityType: 'raleon_id',
			identityValue: raleonId
		}];

		if (walletAddress) {
			identities.push({
				identityType: 'address',
				identityValue: walletAddress.toLowerCase()
			});
		}

		const raleonUser = await this.raleonUserService.getUserFromIdentities(identities);

		return await goalType.isComplete({
			raleonUserId: raleonUser.id!,
			walletAddress,
			orgId,
			goalId: id!,
			questId: goal.questId,
			configData: goal.requiredData as ConfigSchema<any>,
			userData: {}
		});
	}

	@del('/goals/{id}', {
		responses: {
			'204': {
				description: 'Goal DELETE success',
			},
		},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'raleon-support', 'customer', 'support'],
		voters: [basicAuthorization],
	})
	async deleteById(
		@modelIdForGuard(Goal)
		@param.path.number('id')
		id: number,
	): Promise<void> {
		await this.goalRepository.content(id).delete();
		await this.goalRepository.deleteById(id);
	}

	@skipGuardCheck() //invoked from snippet, no auth
	@get('/goals/oauth-redirect/{service}')
	public async oauthRedirect(
		@param.query.string('code') code: string,
		@param.query.string('state') state: string,
		@param.path.string('service') service: string,
		@inject(RestBindings.Http.REQUEST) req: Request,
		@inject(RestBindings.Http.RESPONSE) res: Response,
	): Promise<void> {
		const { raleonId, orgId, goalId, questId  } =
			JSON.parse(Buffer.from(state, 'base64').toString('ascii'));

		const data = await fetch(JoinDiscordServer.OAUTH_TOKEN_URI, {
			method: 'POST',
			body: new URLSearchParams({
				client_id: JoinDiscordServer.CLIENT_ID,
				client_secret: JoinDiscordServer.CLIENT_SECRET,
				grant_type: 'authorization_code',
				code,
				redirect_uri: JoinDiscordServer.REDIRECT_URI,
				scope: 'identify guilds guilds.members.read'
			}).toString(),
			headers: {
				'Content-Type': 'application/x-www-form-urlencoded'
			}
		});
		const jsonData = await data.json();

		await this.createOrUpdateGoalUserJourney(raleonId, orgId, questId, {
			accessToken: jsonData.access_token,
			refreshToken: jsonData.refresh_token,
			expiresIn: jsonData.expires_in,
			goalId,
		});

		res.redirect('/utility/oauth-redirect');
	}


	private async createOrUpdateGoalUserJourney(
		raleonId: string,
		orgId: string,
		questId: number,
		goalUserData: any
	) {
		const goalId = goalUserData.goalId;
		const raleonUser: RaleonUser = await this.raleonUserService.getUserFromIdentities([{
			identityType: 'raleon_id',
			identityValue: raleonId
		}]);
		const userJourney = await this.raleonUserService.findOrCreateJourney(questId, raleonUser.id!, 'running');

		userJourney!.goalUserData = {
			...(userJourney!.goalUserData || {}),
			[goalUserData.goalId]: goalUserData
		};

		await this.journeyRepository.update(userJourney!);
	}
}
