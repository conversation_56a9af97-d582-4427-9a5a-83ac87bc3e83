import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  PromotionalCampaign,
  PromotionalCampaignDetails,
} from '../models';
import {PromotionalCampaignDetailsRepository, PromotionalCampaignRepository} from '../repositories';
import {guardStrategy,GuardSkipStrategy, skipGuardCheck, OrgGuardSingleHopPropertyStrategy} from '../interceptors';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../services/basic.authorizor';

@guardStrategy(new OrgGuardSingleHopPropertyStrategy<PromotionalCampaignDetails, PromotionalCampaign>({
	relatedIdPropertyName: 'promotionalCampaignId',
	relatedOrgIdPropertyName: 'orgId',
	relatedRepositoryClass: PromotionalCampaignRepository,
	repositoryClass: PromotionalCampaignDetailsRepository,
}))
export class PromotionalCampaignPromotionalCampaignDetailsController {
  constructor(
    @repository(PromotionalCampaignRepository) protected promotionalCampaignRepository: PromotionalCampaignRepository,
  ) { }


  @skipGuardCheck()
  @get('/promotional-campaigns/{id}/promotional-campaign-details', {
    responses: {
      '200': {
        description: 'Array of PromotionalCampaign has many PromotionalCampaignDetails',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(PromotionalCampaignDetails)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<PromotionalCampaignDetails>,
  ): Promise<PromotionalCampaignDetails[]> {
    return this.promotionalCampaignRepository.promotionalCampaignDetails(id).find(filter);
  }

  @authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
  @skipGuardCheck()
  @post('/promotional-campaigns/{id}/promotional-campaign-details', {
    responses: {
      '200': {
        description: 'PromotionalCampaign model instance',
        content: {'application/json': {schema: getModelSchemaRef(PromotionalCampaignDetails)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof PromotionalCampaign.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PromotionalCampaignDetails, {
            title: 'NewPromotionalCampaignDetailsInPromotionalCampaign',
            exclude: ['id'],
            optional: ['promotionalCampaignId']
          }),
        },
      },
    }) promotionalCampaignDetails: Omit<PromotionalCampaignDetails, 'id'>,
  ): Promise<PromotionalCampaignDetails> {
    return this.promotionalCampaignRepository.promotionalCampaignDetails(id).create(promotionalCampaignDetails);
  }

  @authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
  @skipGuardCheck()
  @patch('/promotional-campaigns/{id}/promotional-campaign-details', {
    responses: {
      '200': {
        description: 'PromotionalCampaign.PromotionalCampaignDetails PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PromotionalCampaignDetails, {partial: true}),
        },
      },
    })
    promotionalCampaignDetails: Partial<PromotionalCampaignDetails>,
    @param.query.object('where', getWhereSchemaFor(PromotionalCampaignDetails)) where?: Where<PromotionalCampaignDetails>,
  ): Promise<Count> {
    return this.promotionalCampaignRepository.promotionalCampaignDetails(id).patch(promotionalCampaignDetails, where);
  }

  @authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
  @skipGuardCheck()
  @del('/promotional-campaigns/{id}/promotional-campaign-details', {
    responses: {
      '200': {
        description: 'PromotionalCampaign.PromotionalCampaignDetails DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(PromotionalCampaignDetails)) where?: Where<PromotionalCampaignDetails>,
  ): Promise<Count> {
    return this.promotionalCampaignRepository.promotionalCampaignDetails(id).delete(where);
  }
}
