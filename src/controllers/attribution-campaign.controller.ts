import {
	Count,
	CountSchema,
	Filter,
	FilterExcludingWhere,
	repository,
	Where,
} from '@loopback/repository';
import {
	api,
	post,
	param,
	get,
	getModelSchemaRef,
	patch,
	put,
	del,
	requestBody,
	response,
} from '@loopback/rest';
import {AttributionCampaign} from '../models';
import {AttributionCampaignRepository} from '../repositories';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {injectUserOrgId, GuardSkipStrategy, guardStrategy, skipGuardCheck} from '../interceptors';
import {basicAuthorization} from '../services';

@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class AttributionCampaignController {
	constructor(
		@repository(AttributionCampaignRepository)
		public attributionCampaignRepository: AttributionCampaignRepository,
	) { }

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/attribution-campaigns')
	@response(200, {
		description: 'AttributionCampaign model instance',
		content: {'application/json': {schema: getModelSchemaRef(AttributionCampaign)}},
	})
	async create(
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(AttributionCampaign, {
						title: 'NewAttributionCampaign',
						exclude: ['id'],
					}),
				},
			},
		})
		attributionCampaign: Omit<AttributionCampaign, 'id'>,
	): Promise<AttributionCampaign> {
		return this.attributionCampaignRepository.create(attributionCampaign);
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/attribution-campaigns/count')
	@response(200, {
		description: 'AttributionCampaign model count',
		content: {'application/json': {schema: CountSchema}},
	})
	async count(
		@param.where(AttributionCampaign) where?: Where<AttributionCampaign>,
	): Promise<Count> {
		return this.attributionCampaignRepository.count(where);
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/attribution-campaigns')
	@response(200, {
		description: 'Array of AttributionCampaign model instances',
		content: {
			'application/json': {
				schema: {
					type: 'array',
					items: getModelSchemaRef(AttributionCampaign, {includeRelations: true}),
				},
			},
		},
	})
	async find(
		@param.filter(AttributionCampaign) filter?: Filter<AttributionCampaign>,
	): Promise<AttributionCampaign[]> {
		return this.attributionCampaignRepository.find(filter);
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@patch('/attribution-campaigns')
	@response(200, {
		description: 'AttributionCampaign PATCH success count',
		content: {'application/json': {schema: CountSchema}},
	})
	async updateAll(
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(AttributionCampaign, {partial: true}),
				},
			},
		})
		attributionCampaign: AttributionCampaign,
		@param.where(AttributionCampaign) where?: Where<AttributionCampaign>,
	): Promise<Count> {
		return this.attributionCampaignRepository.updateAll(attributionCampaign, where);
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/attribution-campaigns/{id}')
	@response(200, {
		description: 'AttributionCampaign model instance',
		content: {
			'application/json': {
				schema: getModelSchemaRef(AttributionCampaign, {includeRelations: true}),
			},
		},
	})
	async findById(
		@param.path.number('id') id: number,
		@param.filter(AttributionCampaign, {exclude: 'where'}) filter?: FilterExcludingWhere<AttributionCampaign>
	): Promise<AttributionCampaign> {
		return this.attributionCampaignRepository.findById(id, filter);
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@patch('/attribution-campaigns/{id}')
	@response(204, {
		description: 'AttributionCampaign PATCH success',
	})
	async updateById(
		@param.path.number('id') id: number,
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(AttributionCampaign, {partial: true}),
				},
			},
		})
		attributionCampaign: AttributionCampaign,
	): Promise<void> {
		await this.attributionCampaignRepository.updateById(id, attributionCampaign);
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@put('/attribution-campaigns/{id}')
	@response(204, {
		description: 'AttributionCampaign PUT success',
	})
	async replaceById(
		@param.path.number('id') id: number,
		@requestBody() attributionCampaign: AttributionCampaign,
	): Promise<void> {
		await this.attributionCampaignRepository.replaceById(id, attributionCampaign);
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@del('/attribution-campaigns/{id}')
	@response(204, {
		description: 'AttributionCampaign DELETE success',
	})
	async deleteById(@param.path.number('id') id: number): Promise<void> {
		await this.attributionCampaignRepository.deleteById(id);
	}
}
