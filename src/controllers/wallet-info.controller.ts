import {
	repository,
} from '@loopback/repository';
import {
	param,
	get,
	api,
	getModelSchemaRef,
} from '@loopback/rest';
import {GuardSkipStrategy, guardStrategy, OrgGuardMultiHopPropertyStrategy, skipGuardCheck} from '../interceptors';
import {json} from 'express';

const fetch = require('node-fetch')
const NodeCache = require( "node-cache" );

const walletCache = new NodeCache({ stdTTL: 1800, checkperiod: 450 });

@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class WalletInfoController {
	constructor() { }

	@get('/wallet/info/{wallet}', {
		responses: {
			'200': {
				description: 'Returns ens and pfp if applicable',
				content: {
					'application/json': {
						schema: {
							type: 'object',
							properties: {
								"pfp_url": {
									"type": "string"
								},
								"ens": {
									"type": "string"
								}
							}

						}
					},
				},
			},
		},
	})
	@skipGuardCheck()
	async getWalletInfo(
		@param.path.string('wallet') walletAddress: string
	): Promise<any> {

		const cachedData = walletCache.get(`wallet-info-${walletAddress.toLowerCase()}`);

		if (cachedData) {
			return JSON.parse(cachedData);
		}

		//Start with ENS
		let jsonresponse: any = {};
		try {
			const response = await fetch(`https://ethereum-rest.api.mnemonichq.com/wallets/v1beta2/ens/by_address/${walletAddress}`, {
				method: 'GET',
				withCreditentials: true,
				credentials: 'omit',
				headers: {
					'x-api-key': '8toIOd3vzAhXM0qxL7f8OXdaT7WJlPUsfU2WRlyGxanwAR6Q', //NEED TO CHANGE THIS
					'Access-Control-Allow-Origin': '*',
					'Content-Type': 'application/json'
				}
			});
			jsonresponse = await response.json();
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
			return {
				error: true,
				message: err.message
			}
		}

		let finalResult: any = {
			"pfp_urls": new Array<string>(),
			"ens": ""
		}

		if (jsonresponse && jsonresponse.entities && jsonresponse.entities.length > 0) {
			for (let i = 0; i < jsonresponse.entities.length; i++) {
				if (jsonresponse.entities[i].matchesReverseRecord) {
					finalResult.ens = jsonresponse.entities[i].name;
					break;
				}
			}
		}

		//Polygon NFTs
		jsonresponse = {};
		try {
			const response = await fetch(`https://polygon-rest.api.mnemonichq.com/wallets/v1beta2/${walletAddress}/nfts?spam=SPAM_FILTER_EXCLUDE`, {
				method: 'GET',
				withCreditentials: true,
				credentials: 'omit',
				headers: {
					'x-api-key': '8toIOd3vzAhXM0qxL7f8OXdaT7WJlPUsfU2WRlyGxanwAR6Q', //NEED TO CHANGE THIS
					'Access-Control-Allow-Origin': '*',
					'Content-Type': 'application/json'
				}
			});
			jsonresponse = await response.json();
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
			return {
				error: true,
				message: err.message
			}
		}

		if(jsonresponse && jsonresponse.nfts && jsonresponse.nfts.length > 0) {
			for(let i = 0; i < jsonresponse.nfts.length; i++) {
				if(jsonresponse.nfts[i] && jsonresponse.nfts[i].nft && jsonresponse.nfts[i].nft.metadata && jsonresponse.nfts[i].nft.metadata.image
					&& jsonresponse.nfts[i].nft.metadata.image.uri) {
					finalResult.pfp_urls.push(jsonresponse.nfts[i].nft.metadata.image.uri);
				}
			}
		}

		//Ethereum NFTs
		jsonresponse = {};
		try {
			const response = await fetch(`https://ethereum-rest.api.mnemonichq.com/wallets/v1beta2/${walletAddress}/nfts?spam=SPAM_FILTER_EXCLUDE`, {
				method: 'GET',
				withCreditentials: true,
				credentials: 'omit',
				headers: {
					'x-api-key': '8toIOd3vzAhXM0qxL7f8OXdaT7WJlPUsfU2WRlyGxanwAR6Q', //NEED TO CHANGE THIS
					'Access-Control-Allow-Origin': '*',
					'Content-Type': 'application/json'
				}
			});
			jsonresponse = await response.json();
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
			return {
				error: true,
				message: err.message
			}
		}

		if(jsonresponse && jsonresponse.nfts && jsonresponse.nfts.length > 0) {
			for(let i = 0; i < jsonresponse.nfts.length; i++) {
				if(jsonresponse.nfts[i] && jsonresponse.nfts[i].nft && jsonresponse.nfts[i].nft.metadata && jsonresponse.nfts[i].nft.metadata.image
					&& jsonresponse.nfts[i].nft.metadata.image.uri) {
					finalResult.pfp_urls.push(jsonresponse.nfts[i].nft.metadata.image.uri);
				}
			}
		}

		finalResult.pfp_urls = finalResult.pfp_urls.slice(0, 100);

		walletCache.set(`wallet-info-${walletAddress.toLowerCase()}`, JSON.stringify(finalResult), 60 * 60 * 24 * 3); //3 day
		return finalResult;
	}

}
