import {
	Count,
	CountSchema,
	Filter,
	FilterExcludingWhere,
	repository,
	Where,
} from '@loopback/repository';
import {
	post,
	param,
	get,
	getModelSchemaRef,
	patch,
	put,
	del,
	requestBody,
	response,
	api,
} from '@loopback/rest';
import {UnlayerComponent} from '../models';
import {UnlayerComponentRepository} from '../repositories';
import {authenticate} from '@loopback/authentication';
import {GuardSkipStrategy, guardStrategy, skipGuardCheck} from '../interceptors';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../services';

@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class UnlayerComponentController {
	constructor(
		@repository(UnlayerComponentRepository)
		public unlayerComponentRepository: UnlayerComponentRepository,
	) { }

	@post('/unlayer-components')
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['raleon-admin', 'customer', 'admin', 'support'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@response(200, {
		description: 'UnlayerComponent model instance',
		content: {'application/json': {schema: getModelSchemaRef(UnlayerComponent)}},
	})
	async create(
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(UnlayerComponent, {
						title: 'NewUnlayerComponent',
						exclude: ['id'],
					}),
				},
			},
		})
		unlayerComponent: Omit<UnlayerComponent, 'id'>,
	): Promise<UnlayerComponent> {
		// Validate JSON before saving
		try {
			JSON.parse(unlayerComponent.json);
		} catch (e) {
			throw new Error('Invalid JSON in component');
		}

		// Ensure editableFields is a string
		if (unlayerComponent.editableFields && typeof unlayerComponent.editableFields !== 'string') {
			unlayerComponent.editableFields = JSON.stringify(unlayerComponent.editableFields);
		}

		return this.unlayerComponentRepository.create(unlayerComponent);
	}

	@get('/unlayer-components')
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['raleon-admin', 'customer', 'admin', 'support'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@response(200, {
		description: 'Array of UnlayerComponent model instances',
		content: {
			'application/json': {
				schema: {
					type: 'array',
					items: getModelSchemaRef(UnlayerComponent, {includeRelations: true}),
				},
			},
		},
	})
	async find(
		@param.filter(UnlayerComponent) filter?: Filter<UnlayerComponent>,
		@param.query.number('orgId') orgId?: number,
		@param.query.number('overrideId') overrideId?: number,
	): Promise<UnlayerComponent[]> {
		// If orgId and overrideId are provided but not in the filter, add them to the filter
		if (!filter) {
			filter = {};
		}

		if (!filter.where) {
			filter.where = {};
		}

		// If orgId and overrideId are provided as query parameters, use them to filter
		if (orgId !== undefined || overrideId !== undefined) {
			const whereClause: any = {};

			// Build the where clause based on provided parameters
			if (orgId !== undefined && overrideId !== undefined) {
				// Filter for both orgId and overrideId
				whereClause.or = [
					{ orgId: null }, // Global components
					{ orgId: orgId, overrideId: null }, // Org-specific components
					{ orgId: orgId, overrideId: overrideId } // Override-specific components
				];
			} else if (orgId !== undefined) {
				// Filter for orgId only
				whereClause.or = [
					{ orgId: null }, // Global components
					{ orgId: orgId } // Org-specific components
				];
			} else if (overrideId !== undefined) {
				// Filter for overrideId only
				whereClause.or = [
					{ overrideId: null }, // Components without override
					{ overrideId: overrideId } // Override-specific components
				];
			}

			// Apply the where clause to the filter
			filter.where = whereClause;
		}

		return this.unlayerComponentRepository.find(filter);
	}

	@del('/unlayer-components/{id}')
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['raleon-admin', 'customer', 'admin', 'support'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@response(200, {
		description: 'UnlayerComponent DELETE success',
		content: {'application/json': {schema: {type: 'object', properties: {success: {type: 'boolean'}}}}},
	})
	async deleteById(
		@param.path.number('id') id: number,
		@param.query.number('orgId') orgId?: number
	): Promise<{success: boolean}> {
		// If orgId is provided, ensure the component belongs to that organization
		if (orgId !== undefined) {
			const component = await this.unlayerComponentRepository.findById(id);

			// Only allow deletion if the component belongs to the specified organization
			// or if the component has no orgId (global) and the user is a raleon-admin
			if (component.orgId !== orgId) {
				throw new Error('You can only delete components that belong to your organization');
			}
		}

		await this.unlayerComponentRepository.deleteById(id);
		return {success: true};
	}

	@patch('/unlayer-components/{id}')
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['raleon-admin', 'customer', 'admin', 'support'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@response(200, {
		description: 'UnlayerComponent PATCH success',
		content: {'application/json': {schema: {type: 'object', properties: {success: {type: 'boolean'}}}}},
	})
	async updateById(
		@param.path.number('id') id: number,
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(UnlayerComponent, {partial: true}),
				},
			},
		})
		unlayerComponent: Partial<UnlayerComponent>,
	): Promise<{success: boolean}> {
		// Validate JSON if it's being updated
		if (unlayerComponent.json) {
			try {
				JSON.parse(unlayerComponent.json);
			} catch (e) {
				throw new Error('Invalid JSON in component');
			}
		}

		// Ensure editableFields is a string
		if (unlayerComponent.editableFields && typeof unlayerComponent.editableFields !== 'string') {
			unlayerComponent.editableFields = JSON.stringify(unlayerComponent.editableFields);
		}

		await this.unlayerComponentRepository.updateById(id, unlayerComponent);
		return {success: true};
	}
}
