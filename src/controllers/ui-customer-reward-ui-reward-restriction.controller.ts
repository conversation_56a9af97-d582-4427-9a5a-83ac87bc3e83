import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
  api
} from '@loopback/rest';
import {
  UiCustomerReward,
  UiRewardRestriction,
} from '../models';
import {UiCustomerRewardRepository} from '../repositories';
import {GuardSkipStrategy, guardStrategy, skipGuardCheck} from '../interceptors';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../services/basic.authorizor';

@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class UiCustomerRewardUiRewardRestrictionController {
  constructor(
    @repository(UiCustomerRewardRepository) protected uiCustomerRewardRepository: UiCustomerRewardRepository,
  ) { }

  @get('/ui-customer-rewards/{id}/ui-reward-restrictions', {
    responses: {
      '200': {
        description: 'Array of UiCustomerReward has many UiRewardRestriction',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(UiRewardRestriction)},
          },
        },
      },
    },
  })
  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<UiRewardRestriction>,
  ): Promise<UiRewardRestriction[]> {
    return this.uiCustomerRewardRepository.uiRewardRestrictions(id).find(filter);
  }

  @post('/ui-customer-rewards/{id}/ui-reward-restrictions', {
    responses: {
      '200': {
        description: 'UiCustomerReward model instance',
        content: {'application/json': {schema: getModelSchemaRef(UiRewardRestriction)}},
      },
    },
  })
  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin'],
	  voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async create(
    @param.path.number('id') id: typeof UiCustomerReward.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UiRewardRestriction, {
            title: 'NewUiRewardRestrictionInUiCustomerReward',
            exclude: ['id'],
            optional: ['uiCustomerRewardId']
          }),
        },
      },
    }) uiRewardRestriction: Omit<UiRewardRestriction, 'id'>,
  ): Promise<UiRewardRestriction> {
    return this.uiCustomerRewardRepository.uiRewardRestrictions(id).create(uiRewardRestriction);
  }

  @patch('/ui-customer-rewards/{id}/ui-reward-restrictions', {
    responses: {
      '200': {
        description: 'UiCustomerReward.UiRewardRestriction PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin'],
	  voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UiRewardRestriction, {partial: true}),
        },
      },
    })
    uiRewardRestriction: Partial<UiRewardRestriction>,
    @param.query.object('where', getWhereSchemaFor(UiRewardRestriction)) where?: Where<UiRewardRestriction>,
  ): Promise<Count> {
    return this.uiCustomerRewardRepository.uiRewardRestrictions(id).patch(uiRewardRestriction, where);
  }

  @del('/ui-customer-rewards/{id}/ui-reward-restrictions', {
    responses: {
      '200': {
        description: 'UiCustomerReward.UiRewardRestriction DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin'],
	  voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(UiRewardRestriction)) where?: Where<UiRewardRestriction>,
  ): Promise<Count> {
    return this.uiCustomerRewardRepository.uiRewardRestrictions(id).delete(where);
  }
}
