import {
	Count,
	CountSchema,
	Filter,
	repository,
	Where,
} from '@loopback/repository';
import {
	del,
	get,
	getModelSchemaRef,
	getWhereSchemaFor,
	param,
	patch,
	post,
	api,
	requestBody,
} from '@loopback/rest';
import {
	Organization,
	OrganizationSettings,
} from '../models';
import {OrganizationRepository, OrganizationSettingsRepository} from '../repositories';
import {guardStrategy, GuardSkipStrategy} from '../interceptors/crud-guard.interceptor';
import {injectUserOrgId, modelIdForGuard, OrgGuardPropertyStrategy, skipGuardCheck} from '../interceptors/crud-guard.interceptor';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../services';
import {ShopifyApiInvoker} from '../services/shopify/shopify-api-invoker.service';
import {service} from '@loopback/core';

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardPropertyStrategy<any>({
	orgIdModelPropertyName: 'organizationId',
	repositoryClass: OrganizationSettingsRepository
}))

export class OrganizationOrganizationSettingsController {
	constructor(
		@repository(OrganizationRepository) protected organizationRepository: OrganizationRepository,
		@service(ShopifyApiInvoker) protected shopifyApiInvoker: ShopifyApiInvoker,
	) { }

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/organizations/organization-settings', {
		responses: {
			'200': {
				description: 'Array of Organization has many OrganizationSettings',
				content: {
					'application/json': {
						schema: {type: 'array', items: getModelSchemaRef(OrganizationSettings)},
					},
				},
			},
		},
	})
	async find(
		@injectUserOrgId() orgId: number,
		@param.query.object('filter') filter?: Filter<OrganizationSettings>,
	): Promise<OrganizationSettings[]> {
		return this.organizationRepository.organizationSettings(orgId).find(filter);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/organizations/organization-settings', {
		responses: {
			'200': {
				description: 'Organization model instance',
				content: {'application/json': {schema: getModelSchemaRef(OrganizationSettings)}},
			},
		},
	})
	async create(
		@injectUserOrgId() orgId: number,
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(OrganizationSettings, {
						title: 'NewOrganizationSettingsInOrganization',
						exclude: ['id'],
						optional: ['organizationId']
					}),
				},
			},
		}) organizationSettings: Omit<OrganizationSettings, 'id'>,
	): Promise<OrganizationSettings> {
		return this.organizationRepository.organizationSettings(orgId).create(organizationSettings);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@del('/organizations/organization-settings', {
		responses: {
			'200': {
				description: 'Organization.OrganizationSettings DELETE success count',
				content: {'application/json': {schema: CountSchema}},
			},
		},
	})
	async delete(
		@injectUserOrgId() orgId: number,
		@param.query.object('where', getWhereSchemaFor(OrganizationSettings)) where?: Where<OrganizationSettings>,
	): Promise<Count> {
		return this.organizationRepository.organizationSettings(orgId).delete(where);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/organizations/organization-settings/{key}', {
		responses: {
			'200': {
				description: 'OrganizationSettings model instance',
				content: {
					'application/json': {
						schema: getModelSchemaRef(OrganizationSettings, {includeRelations: true}),
					},
				},
			},
		},
	})
	async findByKey(
		@param.path.string('key') key: string,
		@injectUserOrgId() orgId: number,
	): Promise<OrganizationSettings | null> {
		const filter = {where: {key: key, organizationId: orgId}};
		const result = await this.organizationRepository.organizationSettings(orgId).find(filter);
		if (result.length === 0) {
			console.log('No settings found for key', key);
			return null;
		}
		return result[0];
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/organizations/organization-settings/upsert', {
		responses: {
			'200': {
				description: 'OrganizationSettings model instance',
				content: {'application/json': {schema: getModelSchemaRef(OrganizationSettings, {
				})}},
			},
		},
	})
	async upsert(
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(OrganizationSettings, {
						exclude: ['id', 'organizationId'],
					}),
				},
			},
		}) organizationSettings: Omit<OrganizationSettings, 'id'>,
		@injectUserOrgId() orgId: number,
	): Promise<any> {

		console.log("**************************************************");
		console.log('organizationSettings', organizationSettings);
		console.log('orgId', orgId);
		const existingSetting = await this.organizationRepository.organizationSettings(orgId).find({
			where: {key: organizationSettings.key, organizationId: orgId},
		});

		console.log('Found existing setting', existingSetting);

		if (existingSetting.length > 0) {
			console.log('Updating existing setting');
			await this.organizationRepository.organizationSettings(orgId).patch({
				value: organizationSettings.value,
			},{
				key: organizationSettings.key,
				organizationId: orgId,
			});
			return {
				status: 'updated'
			};
		} else {
			await this.organizationRepository.organizationSettings(orgId).create(organizationSettings);
			return {
				status: 'created'
			};
		}
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/organizations/external-plan-details', {
		responses: {
			'200': {
				description: 'Array of Organization has many OrganizationSettings',
				content: {
					'application/json': {
						schema: {type: 'array', items: getModelSchemaRef(OrganizationSettings)},
					},
				},
			},
		},
	})
	async setOrgExternalPlanDetails(
		@injectUserOrgId() orgId: number,
	): Promise<void> {
		const orgData = await this.organizationRepository.findById(orgId);
		if (orgData.externalDomain) {
			//can include check for myshopify in domain
			const shopifyPlanData = await this.shopifyApiInvoker.invokeAdminApi(orgId, '/get-shop-info', 'GET');
			if (shopifyPlanData?.shop?.plan) {
				await this.organizationRepository.updateById(orgId, {
					externalPlanDetails: shopifyPlanData.shop.plan
				});
			}
		}
	}
}
