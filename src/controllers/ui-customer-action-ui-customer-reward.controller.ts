import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
  import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
  api
} from '@loopback/rest';
import {
UiCustomerAction,
UiActionRewardJunction,
UiCustomerReward,
} from '../models';
import {UiCustomerActionRepository} from '../repositories';
import {GuardSkipStrategy, guardStrategy, skipGuardCheck} from '../interceptors';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../services/basic.authorizor';

@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class UiCustomerActionUiCustomerRewardController {
  constructor(
    @repository(UiCustomerActionRepository) protected uiCustomerActionRepository: UiCustomerActionRepository,
  ) { }

  @get('/ui-customer-actions/{id}/ui-customer-rewards', {
    responses: {
      '200': {
        description: 'Array of UiCustomerAction has many UiCustomerReward through UiActionRewardJunction',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(UiCustomerReward)},
          },
        },
      },
    },
  })
  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin', 'support', 'customer'],
	  voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<UiCustomerReward>,
  ): Promise<UiCustomerReward[]> {
    return this.uiCustomerActionRepository.uiCustomerRewards(id).find(filter);
  }

  @post('/ui-customer-actions/{id}/ui-customer-rewards', {
    responses: {
      '200': {
        description: 'create a UiCustomerReward model instance',
        content: {'application/json': {schema: getModelSchemaRef(UiCustomerReward)}},
      },
    },
  })
  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin'],
	  voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async create(
    @param.path.number('id') id: typeof UiCustomerAction.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UiCustomerReward, {
            title: 'NewUiCustomerRewardInUiCustomerAction',
            exclude: ['id'],
          }),
        },
      },
    }) uiCustomerReward: Omit<UiCustomerReward, 'id'>,
  ): Promise<UiCustomerReward> {
    return this.uiCustomerActionRepository.uiCustomerRewards(id).create(uiCustomerReward);
  }

  @patch('/ui-customer-actions/{id}/ui-customer-rewards', {
    responses: {
      '200': {
        description: 'UiCustomerAction.UiCustomerReward PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin'],
	  voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UiCustomerReward, {partial: true}),
        },
      },
    })
    uiCustomerReward: Partial<UiCustomerReward>,
    @param.query.object('where', getWhereSchemaFor(UiCustomerReward)) where?: Where<UiCustomerReward>,
  ): Promise<Count> {
    return this.uiCustomerActionRepository.uiCustomerRewards(id).patch(uiCustomerReward, where);
  }

  @del('/ui-customer-actions/{id}/ui-customer-rewards', {
    responses: {
      '200': {
        description: 'UiCustomerAction.UiCustomerReward DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  @authenticate('jwt')
  @authorize({
	  allowedRoles: ['admin'],
	  voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(UiCustomerReward)) where?: Where<UiCustomerReward>,
  ): Promise<Count> {
    return this.uiCustomerActionRepository.uiCustomerRewards(id).delete(where);
  }
}
