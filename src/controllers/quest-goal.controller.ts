// import {authenticate} from '@loopback/authentication';
// import {authorize} from '@loopback/authorization';
// import {
// 	Count,
// 	CountSchema,
// 	Filter,
// 	repository,
// 	Where,
// } from '@loopback/repository';
// import {
// 	api,
// 	del,
// 	get,
// 	getModelSchemaRef,
// 	getWhereSchemaFor,
// 	param,
// 	patch,
// 	post,
// 	requestBody,
// } from '@loopback/rest';
// import {guardStrategy, OrgGuardSingleHopPropertyStrategy, skipGuardCheck} from '../interceptors';
// import {
// 	Quest,
// 	Goal,
// 	Campaign,
// } from '../models';
// import {CampaignRepository, QuestRepository} from '../repositories';
// import {basicAuthorization} from '../services';

// @api({basePath: '/api/v1'})
// @guardStrategy(new OrgGuardSingleHopPropertyStrategy<Quest, Campaign>({
// 	repositoryClass: QuestRepository,
// 	relatedIdPropertyName: 'campaignId',
// 	relatedRepositoryClass: CampaignRepository,
// 	relatedOrgIdPropertyName: 'orgId',
// }))
// export class QuestGoalController {
// 	constructor(
// 		@repository(QuestRepository) protected questRepository: QuestRepository,
// 	) { }

// 	@authenticate('jwt')
// 	@authorize({
// 		allowedRoles: ['admin', 'customer-admin'],
// 		voters: [basicAuthorization],
// 	})
// 	@skipGuardCheck() //TODO: update
// 	@get('/quests/{id}/goals', {
// 		responses: {
// 			'200': {
// 				description: 'Array of Quest has many Goal',
// 				content: {
// 					'application/json': {
// 						schema: {type: 'array', items: getModelSchemaRef(Goal)},
// 					},
// 				},
// 			},
// 		},
// 	})
// 	async find(
// 		@param.path.number('id') id: number,
// 		@param.query.object('filter') filter?: Filter<Goal>,
// 	): Promise<Goal[]> {
// 		return this.questRepository.goals(id).find(filter);
// 	}

// 	@authenticate('jwt')
// 	@authorize({
// 		allowedRoles: ['admin', 'customer-admin'],
// 		voters: [basicAuthorization],
// 	})
// 	@skipGuardCheck() //TODO: update
// 	@post('/quests/{id}/goals', {
// 		responses: {
// 			'200': {
// 				description: 'Quest model instance',
// 				content: {'application/json': {schema: getModelSchemaRef(Goal)}},
// 			},
// 		},
// 	})
// 	async create(
// 		@param.path.number('id') id: typeof Quest.prototype.id,
// 		@requestBody({
// 			content: {
// 				'application/json': {
// 					schema: getModelSchemaRef(Goal, {
// 						title: 'NewGoalInQuest',
// 						exclude: ['id'],
// 						optional: ['questId']
// 					}),
// 				},
// 			},
// 		}) goal: Omit<Goal, 'id'>,
// 	): Promise<Goal> {
// 		return this.questRepository.goals(id).create(goal);
// 	}

// 	@authenticate('jwt')
// 	@authorize({
// 		allowedRoles: ['admin', 'customer-admin'],
// 		voters: [basicAuthorization],
// 	})
// 	@skipGuardCheck() //TODO: update
// 	@patch('/quests/{id}/goals', {
// 		responses: {
// 			'200': {
// 				description: 'Quest.Goal PATCH success count',
// 				content: {'application/json': {schema: CountSchema}},
// 			},
// 		},
// 	})
// 	async patch(
// 		@param.path.number('id') id: number,
// 		@requestBody({
// 			content: {
// 				'application/json': {
// 					schema: getModelSchemaRef(Goal, {partial: true}),
// 				},
// 			},
// 		})
// 		goal: Partial<Goal>,
// 		@param.query.object('where', getWhereSchemaFor(Goal)) where?: Where<Goal>,
// 	): Promise<Count> {
// 		return this.questRepository.goals(id).patch(goal, where);
// 	}
// }
