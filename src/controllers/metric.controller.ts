// Uncomment these imports to begin using these cool features!
import {authenticate} from '@loopback/authentication';
import {User} from '@loopback/authentication-jwt';
import {authorize} from '@loopback/authorization';
import {inject} from '@loopback/core';
import {repository} from '@loopback/repository';
import {
	api,
	get, getModelSchemaRef, param, post, Request, requestBody, RestBindings
} from '@loopback/rest';
import {SecurityBindings} from '@loopback/security';
import {injectUserOrgId, GuardSkipStrategy, guardStrategy, skipGuardCheck} from '../interceptors';
import {ActiveUserInteractionCount, ActiveUserPercentActivity, ActiveUsers, AddressBalance, AddressLastTransactionDate, AddressTokenBalance, AtRiskActivities, AtRiskUsers, CommonTokensHeld, DormantUsers, HighestValueWallet, InteractionsByType, LifetimeTransaction, MetricProof, MostActiveTime, NewUserActivities, NewUsers, TotalTokenHolders, TotalUSDValueOfWallets, UniqueWallets, WalletOverview} from '../models';
import {UserRepository} from '../repositories';
import {basicAuthorization} from '../services';
import {getClusterPersona} from '../utils/utils';
const fetch = require('node-fetch')
const aws4 = require('aws4')

const DATA_API = 'bcie42aco8.execute-api.us-east-1.amazonaws.com'
const UTM_API = 'j0v36abmdj.execute-api.us-east-1.amazonaws.com'
const AWS_ACCESS_KEY = process.env.API_ACCESS_KEY;
const AWS_SECRET_KEY = process.env.API_SECRET_KEY;

export function getURL(path: string, method: string, body?: any, host?: string, stringify = true) {
	const opts = {
		host: host || DATA_API,
		path: path,
		region: 'us-east-1',
		service: 'execute-api',
		mode: 'cors',
		body: body != undefined ? (stringify ? JSON.stringify(body) : body) : undefined,
		headers: {
			'Content-Type': 'application/json',
		},
		method: method
	}
	return aws4.sign(opts, {accessKeyId: AWS_ACCESS_KEY, secretAccessKey: AWS_SECRET_KEY});
}

@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class MetricController {
	constructor(
		@inject(RestBindings.Http.REQUEST) private req: Request,
		@inject(SecurityBindings.USER) private user: User,
		@repository('UserRepository') private userRepository: UserRepository,
	) { }

	@get('/metric/proof', {
		responses: {
			'200': {
				description: 'Wallet Ids as proofs for a metric',
				content: {
					'application/json': {
						schema: getModelSchemaRef(MetricProof),
					},
				},
			},
		},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async getMetricProof(
		@param.query.string('address') address: string,
		@param.query.string('network') network: string,
		@param.query.string('metricName') metricName: string,
		@param.query.string('dateProcessed') dateProcessed: string,
		@param.query.string('page') page: number,
	): Promise<object> {
		let errResponse = {
			statusCode: 404,
			body: 'Wallet Data Not Available'
		};
		let response: any;
		try {
			const url = `/dev/metric/proof?network=${network}&address=${address}&metricName=${metricName}&dateProcessed=${dateProcessed}&page=${page}`;
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			return errResponse;
		}

		if (response.status < 200 || response.status >= 300) {
			return errResponse;
		}
		return {
			statusCode: 200,
			body: await response.json()
		};
	}

	@get('/metric/proof/download', {
		responses: {
			'200': {
				description: 'Wallet Ids as proofs for a metric',
				content: {
					'application/json': {
						schema: getModelSchemaRef(MetricProof),
					},
				},
			},
		},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async getMetricProofDownload(
		@param.query.string('address') address: string,
		@param.query.string('network') network: string,
		@param.query.string('metricName') metricName: string,
		@param.query.string('dateProcessed') dateProcessed: string,
	): Promise<object> {
		let errResponse = {
			statusCode: 404,
			body: 'Wallet Data Not Available'
		};
		let response: any;
		try {
			const url = `/dev/metric/proof/download?network=${network}&address=${address}&metricName=${metricName}&dateProcessed=${dateProcessed}`;
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			return errResponse;
		}

		if (response.status < 200 || response.status >= 300) {
			return errResponse;
		}

		const data = await response.text();
		return {
			statusCode: 200,
			body: data
		};
	}


	@get('/metric/address-balance/{network}/{address}', {
		responses: {
			'200': {
				description: 'Returns the USD value of an address, value is in USD, not including NFTs',
				content: {
					'application/json': {
						schema: getModelSchemaRef(AddressBalance),
					},
				},
			},
		},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async getAccountBalance(
		@param.path.string('address') address: string,
		@param.path.string('network') network: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	): Promise<object> {
		const result: any = {};
		let response: any;
		try {
			const url = `/dev/metric?metric-name=address-balance&network=${network}&address=${address}&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			console.log('url: ' + url);
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}

		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/lifetime-transaction/{network}/{address}', {
		responses: {
			'200': {
				description: 'Lifetime-transaction, always returns the most recent calculated result',
				content: {
					'application/json': {
						schema: getModelSchemaRef(LifetimeTransaction),
					},
				},
			},
		},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async getLifetimeTransactions(
		@param.path.string('address') address: string,
		@param.path.string('network') network: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	): Promise<object> {
		const result: any = {};
		let response: any;
		try {
			const url = `/dev/metric/lifetime-transactions?network=${network}&address=${address}&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}

		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/token-balances/{network}/{address}', {
		responses: {
			'200': {
				description: 'token-balances, returns an array of ticker tokens, that corresponds with an array of usd_values',
				content: {
					'application/json': {
						schema: getModelSchemaRef(AddressTokenBalance),
					},
				},
			},
		},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async getAddressTokenBalances(
		@param.path.string('address') address: string,
		@param.path.string('network') network: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	): Promise<object> {
		const result: any = {};
		let response: any;
		try {
			const url = `/dev/metric/token-balances?network=${network}&address=${address}&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			console.log('url: ' + url);
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}

		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/last-transaction-date/{network}/{address}', {
		responses: {
			'200': {
				description: 'last-transaction-date, returns a date with the last time a transaction was detected',
				content: {
					'application/json': {
						schema: getModelSchemaRef(AddressLastTransactionDate),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getAddressLastTransaction(
		@param.path.string('address') address: string,
		@param.path.string('network') network: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	): Promise<object> {
		const result: any = {};
		let response: any;
		try {
			const url = `/dev/metric/last-transaction-date?network=${network}&address=${address}&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			console.log('url: ' + url);
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}

		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/new-users/{network}/{address}', {
		responses: {
			'200': {
				description: 'returns new user count',
				content: {
					'application/json': {
						schema: getModelSchemaRef(NewUsers),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getProjectNewUsers(
		@param.path.string('address') address: string,
		@param.path.string('network') network: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	): Promise<object> {
		let response: any;
		try {
			const url = `/dev/metric?network=${network}&metric-name=NEW_USERS&address=${address}&end-date=latest`;
			console.log('url: ' + url);
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}

		const data: any = await response.json();
		console.log(`new users: ${JSON.stringify(data)}`);
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/recovered-users/{network}/{address}', {
		responses: {
			'200': {
				description: 'returns new user count',
				content: {
					'application/json': {
						schema: getModelSchemaRef(NewUsers),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getRecoveredUsers(
		@param.path.string('address') address: string,
		@param.path.string('network') network: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	): Promise<object> {
		let response: any;
		try {
			const url = `/dev/metric?network=${network}&metric-name=RECOVERED_USERS&address=${address}&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			console.log('url: ' + url);
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}

		const data: any = await response.json();
		console.log(`recovered users: ${JSON.stringify(data)}`);
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/usd-value-all-users/{network}/{address}', {
		responses: {
			'200': {
				description: 'returns usd value of all users, not all tokens are counted',
				content: {
					'application/json': {
						schema: getModelSchemaRef(NewUsers),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getProjectUSDValue(
		@param.path.string('address') address: string,
		@param.path.string('network') network: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	): Promise<object> {
		const result: any = {};
		let response: any;
		try {
			const url = `/dev/metric/usd-value-all-users?network=${network}&address=${address}&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			console.log('url: ' + url);
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}

		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/active-users/{network}/{address}', {
		responses: {
			'200': {
				description: 'last-transaction-date, returns a date with the last time a transaction was detected',
				content: {
					'application/json': {
						schema: getModelSchemaRef(ActiveUsers),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getProjectActiveUsers(
		@param.path.string('address') address: string,
		@param.path.string('network') network: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	): Promise<object> {
		const result: any = {};
		let response: any;
		try {
			const url = `/dev/metric?network=${network}&metric-name=ACTIVE_USERS&address=${address}&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}

		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/at-risk-users/{network}/{address}', {
		responses: {
			'200': {
				description: 'last-transaction-date, returns a date with the last time a transaction was detected',
				content: {
					'application/json': {
						schema: getModelSchemaRef(AtRiskUsers),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getProjectAtRiskUsers(
		@param.path.string('address') address: string,
		@param.path.string('network') network: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	): Promise<object> {
		let response: any;
		try {
			const url = `/dev/metric?network=${network}&metric-name=AT_RISK_USERS&address=${address}&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}

		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/dormant-users/{network}/{address}', {
		responses: {
			'200': {
				description: 'last-transaction-date, returns a date with the last time a transaction was detected',
				content: {
					'application/json': {
						schema: getModelSchemaRef(DormantUsers),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getProjectDormantUsers(
		@param.path.string('address') address: string,
		@param.path.string('network') network: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	): Promise<object> {
		const result: any = {};
		let response: any;
		try {
			const url = `/dev/metric/dormant-users?network=${network}&address=${address}&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}

		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/active-user-percent-activity/{network}/{address}', {
		responses: {
			'200': {
				description: 'active users percent of overall wallet activity, expressed as percent',
				content: {
					'application/json': {
						schema: getModelSchemaRef(ActiveUserPercentActivity),
					},
				},
			},
		},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async getProjectActiveUserPercentActivity(
		@param.path.string('address') address: string,
		@param.path.string('network') network: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	): Promise<object> {
		const result: any = {};
		let response: any;
		try {
			const url = `/dev/metric/active-user-percent-activity?network=${network}&address=${address}&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}

		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/unique-wallets/{network}/{address}', {
		responses: {
			'200': {
				description: 'number of unique wallets',
				content: {
					'application/json': {
						schema: getModelSchemaRef(UniqueWallets),
					},
				},
			},
		},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async getUniqueWallets(
		@param.path.string('address') address: string,
		@param.path.string('network') network: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	): Promise<object> {
		const result: any = {};
		let response: any;
		try {
			const url = `/dev/metric/unique-wallets?network=${network}&address=${address}&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}

		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/active-user-transaction-count/{network}/{address}', {
		responses: {
			'200': {
				description: 'number of interactions from active users',
				content: {
					'application/json': {
						schema: getModelSchemaRef(ActiveUserInteractionCount),
					},
				},
			},
		},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async getActiveUserInteractionsCount(
		@param.path.string('address') address: string,
		@param.path.string('network') network: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	): Promise<object> {
		const result: any = {};
		let response: any;
		try {
			const url = `/dev/metric/active-user-transaction-count?network=${network}&address=${address}&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}

		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/token-holders-usd-value/{network}/{address}', {
		responses: {
			'200': {
				description: 'usd value of all token holders',
				content: {
					'application/json': {
						schema: getModelSchemaRef(TotalUSDValueOfWallets),
					},
				},
			},
		},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async getProjectTotalTokenHolderUSD(
		@param.path.string('address') address: string,
		@param.path.string('network') network: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	): Promise<object> {
		const result: any = {};
		let response: any;
		try {
			const url = `/dev/metric/token-holders-usd-value?network=${network}&address=${address}&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}

		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/top-eth-holder/{network}/{address}', {
		responses: {
			'200': {
				description: 'highest valued wallet by eth',
				content: {
					'application/json': {
						schema: getModelSchemaRef(HighestValueWallet),
					},
				},
			},
		},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async getProjectHighestValueWalletETH(
		@param.path.string('address') address: string,
		@param.path.string('network') network: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	): Promise<object> {
		let response: any;
		try {
			const url = `/dev/metric?network=${network}&metric-name=TOKEN_HOLDER_MAX_ETH&address=${address}&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			console.log('url: ' + url);
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}

		const data: any = await response.json();
		console.log(`max_eth_holder: ${JSON.stringify(data)}`);
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};


		/*const result: any = {};
		let response: any;
		try {
		  const url = `/dev/metric/top-eth-holder?network=${network}&address=${address}&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
		  const signedRequest = getURL(url, 'GET');
		  response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
		  console.log("Error: " + JSON.stringify(err));
		}

		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
		  for (let index = 0; index < data.Items.length; index++) {
			const element = data.Items[index];
			results.push(element)
		  }
		}

		return {
		  statusCode: 200,
		  body: results
		};*/
	}

	@get('/metric/avg-token-transfer/{network}/{address}', {
		responses: {
			'200': {
				description: 'average transfer size',
				content: {
					'application/json': {
						schema: getModelSchemaRef(HighestValueWallet),
					},
				},
			},
		},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async getProjectAvgTokenTransfer(
		@param.path.string('address') address: string,
		@param.path.string('network') network: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	): Promise<object> {
		const result: any = {};
		let response: any;
		try {
			const url = `/dev/metric/avg-token-transfer?network=${network}&address=${address}&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}

		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/most-active-time/{network}/{address}', {
		responses: {
			'200': {
				description: 'most active time',
				content: {
					'application/json': {
						schema: getModelSchemaRef(MostActiveTime),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getProjectMostActiveTime(
		@param.path.string('address') address: string,
		@param.path.string('network') network: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	): Promise<object> {
		const result: any = {};
		let response: any;
		try {
			const url = `/dev/metric/most-active-time?network=${network}&address=${address}&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}

		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				let element = data.Items[index];
				let hourVal = "";
				if (element.hour === 24)
					hourVal = "24 - 1 UTC";
				else
					hourVal = element.hour + " - " + (element.hour + 1);

				element.time = hourVal;
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/total-token-holders/{network}/{address}', {
		responses: {
			'200': {
				description: 'average transfer size',
				content: {
					'application/json': {
						schema: getModelSchemaRef(TotalTokenHolders),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getProjectTokenHolders(
		@param.path.string('address') address: string,
		@param.path.string('network') network: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	): Promise<object> {
		const result: any = {};
		let response: any;
		try {
			const url = `/dev/metric/total-token-holders?network=${network}&address=${address}&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}

		const data: any = await response.json();
		let results = []
		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/action-by-type/{network}/{address}', {
		responses: {
			'200': {
				description: 'average transfer size',
				content: {
					'application/json': {
						schema: getModelSchemaRef(InteractionsByType),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getProjectInteractionsByType(
		@param.path.string('address') address: string,
		@param.path.string('network') network: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	): Promise<object> {
		const result: any = {};
		let response: any;
		try {
			const url = `/dev/metric/action-by-type?network=${network}&address=${address}&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}

		const data: any = await response.json();
		let results = []
		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/common-tokens/{network}/{address}', {
		responses: {
			'200': {
				description: 'average transfer size',
				content: {
					'application/json': {
						schema: getModelSchemaRef(CommonTokensHeld),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getProjectCommonTokensHeld(
		@param.path.string('address') address: string,
		@param.path.string('network') network: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	): Promise<object> {
		const result: any = {};
		let response: any;
		try {
			const url = `/dev/metric/common-tokens?network=${network}&address=${address}&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}

		const data: any = await response.json();
		let results = []
		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/nfts/{network}/{address}', {
		responses: {
			'200': {
				description: 'average transfer size',
				content: {
					'application/json': {
						schema: getModelSchemaRef(CommonTokensHeld),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getProjectCommonNFTS(
		@param.path.string('address') address: string,
		@param.path.string('network') network: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	): Promise<object> {
		const result: any = {};
		let response: any;
		try {
			const url = `/dev/metric/nfts?network=${network}&address=${address}&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}

		const data: any = await response.json();
		let results = []
		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/activities-at-risk/{network}/{address}', {
		responses: {
			'200': {
				description: 'average transfer size',
				content: {
					'application/json': {
						schema: getModelSchemaRef(AtRiskActivities),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getProjectAtRiskActivities(
		@param.path.string('address') address: string,
		@param.path.string('network') network: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	): Promise<object> {
		const result: any = {};
		let response: any;
		try {
			const url = `/dev/metric/activities-at-risk?network=${network}&address=${address}&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}

		const data: any = await response.json();
		let results = []
		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/activities-new-user/{network}/{address}', {
		responses: {
			'200': {
				description: 'average transfer size',
				content: {
					'application/json': {
						schema: getModelSchemaRef(NewUserActivities),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getProjectNewUserActivities(
		@param.path.string('address') address: string,
		@param.path.string('network') network: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	): Promise<object> {
		const result: any = {};
		let response: any = {};
		try {
			const url = `/dev/metric/activities-new-user?network=${network}&address=${address}&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results = []
		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/persona-active-count/{network}/{address}', {
		responses: {
			'200': {
				description: 'Persona Active Count',
				content: {
					'application/json': {
						schema: getModelSchemaRef(NewUserActivities),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getPersonaActiveCount(
		@param.path.string('address') address: string,
		@param.path.string('network') network: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	): Promise<object> {
		const result: any = {};
		let response: any = {};
		try {
			const url = `/dev/metric/persona-active-count?network=${network}&address=${address}&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results = []
		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];

				for (let n = 0; n < element.data.length; n++) {
					element.data[n].cluster = getClusterPersona(element.data[n].cluster);
				}
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/{network}/{address}/{metricName}', {
		responses: {
			'200': {
				description: 'Persona Active Count',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getMetric(
		@param.path.string('network') network: string,
		@param.path.string('address') address: string,
		@param.path.string('metricName') metricName: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	) {
		let response: any = {};
		try {
			const url = `/dev/metric?network=${network}&address=${address}&metric-name=${metricName}&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		return {
			statusCode: 200,
			body: data
		};
	}

	@get('/metric/activities-active/{network}/{address}', {
		responses: {
			'200': {
				description: 'Active Users - Activities',
				content: {
					'application/json': {
						schema: getModelSchemaRef(NewUserActivities),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getActiveUserActivities(
		@param.path.string('address') address: string,
		@param.path.string('network') network: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	): Promise<object> {
		const result: any = {};
		let response: any = {};
		try {
			const url = `/dev/metric/activities-active?network=${network}&address=${address}&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results = []
		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/activities-dormant/{network}/{address}', {
		responses: {
			'200': {
				description: 'Dormant Users - Activities',
				content: {
					'application/json': {
						schema: getModelSchemaRef(NewUserActivities),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getDormantUserActivities(
		@param.path.string('address') address: string,
		@param.path.string('network') network: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	): Promise<object> {
		const result: any = {};
		let response: any = {};
		try {
			const url = `/dev/metric/activities-dormant?network=${network}&address=${address}&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results = []
		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/returning-users-last-1/{network}/{address}', {
		responses: {
			'200': {
				description: 'Returning Users over last 24 hours',
				content: {
					'application/json': {
						schema: getModelSchemaRef(NewUserActivities),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getReturningUsersLast1Day(
		@param.path.string('address') address: string,
		@param.path.string('network') network: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	): Promise<object> {
		const result: any = {};
		let response: any = {};
		try {
			const url = `/dev/metric/returning-users-last-1?network=${network}&address=${address}&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results = []
		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/returning-users-last-7/{network}/{address}', {
		responses: {
			'200': {
				description: 'Returning Users over last 7 days',
				content: {
					'application/json': {
						schema: getModelSchemaRef(NewUserActivities),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getReturningUsersLast7Day(
		@param.path.string('address') address: string,
		@param.path.string('network') network: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	): Promise<object> {
		const result: any = {};
		let response: any = {};
		try {
			const url = `/dev/metric/returning-users-last-7?network=${network}&address=${address}&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results = []
		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/recovered-users-last-1/{network}/{address}', {
		responses: {
			'200': {
				description: 'Recovered Users Over the Last 24 hours',
				content: {
					'application/json': {
						schema: getModelSchemaRef(NewUserActivities),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getRecoveredUsersLast1(
		@param.path.string('address') address: string,
		@param.path.string('network') network: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	): Promise<object> {
		const result: any = {};
		let response: any = {};
		try {
			const url = `/dev/metric/recovered-users-last-1?network=${network}&address=${address}&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results = []
		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/project-nft-total-counts/{network}/{address}', {
		responses: {
			'200': {
				description: 'Macro stats for NFTs in a defined Project',
				content: {
					'application/json': {
						schema: getModelSchemaRef(NewUserActivities),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getNFTMacroStats(
		@param.path.string('address') address: string,
		@param.path.string('network') network: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	): Promise<object> {
		const result: any = {};
		let response: any = {};
		try {
			const url = `/dev/metric/project-nft-total-counts?network=${network}&address=${address}&end-date=latest`; //Includes time as part of metric
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results = []
		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/project-nft-avg-hold-time/{network}/{address}', {
		responses: {
			'200': {
				description: 'In days returns avg hold time for NFTs in a project',
				content: {
					'application/json': {
						schema: getModelSchemaRef(NewUserActivities),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getNFTAvgHoldTime(
		@param.path.string('address') address: string,
		@param.path.string('network') network: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	): Promise<object> {
		const result: any = {};
		let response: any = {};
		try {
			const url = `/dev/metric/project-nft-avg-hold-time?network=${network}&address=${address}&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results = []
		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/saved-users/{network}/{address}', {
		responses: {
			'200': {
				description: 'Saved Users By Day, At Risk -> Active',
				content: {
					'application/json': {
						schema: getModelSchemaRef(NewUserActivities),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getSavedUserList(
		@param.path.string('address') address: string,
		@param.path.string('network') network: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	): Promise<object> {
		let response: any;
		try {
			const url = `/dev/metric?network=${network}&metric-name=SAVED_USERS&address=${address}&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			console.log('url: ' + url);
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}

		const data: any = await response.json();
		console.log(`saved users: ${JSON.stringify(data)}`);
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/token-info/{network}/{address}', {
		responses: {
			'200': {
				description: 'Returns the token-info for a given project (supply, holders, etc)',
				content: {
					'application/json': {
						schema: getModelSchemaRef(NewUserActivities),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getTokenInfo(
		@param.path.string('address') address: string,
		@param.path.string('network') network: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	): Promise<object> {
		const result: any = {};
		let response: any = {};
		try {
			const url = `/dev/metric?network=${network}&metric-name=PROJECT_TOKEN_INFO&address=${address}&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results = []
		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}


	@get('/metric/utm/campaigns', {
		responses: {
			'200': {
				description: 'Return list of known campaigns for a given org',
				content: {
					'application/json': {
						schema: getModelSchemaRef(NewUserActivities),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getKnownCampaigns(): Promise<object> {
		const org_id = await this.userRepository.findOrganization(this.user);
		let response: any = {};
		try {
			const url = `/dev/utm/campaigns?org-id=${org_id}`;
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results = []
		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/utm/sources', {
		responses: {
			'200': {
				description: 'Return list of sources by given org and campaign',
				content: {
					'application/json': {
						schema: getModelSchemaRef(NewUserActivities),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getSources(
		@param.query.string('campaignid') campaign: string,
		@param.query.string('projectid') project_id: string,
	): Promise<object> {
		const org_id = await this.userRepository.findOrganization(this.user)
		let response: any = {};
		try {
			const url = `/dev/utm/sources?org-id=${org_id}&campaign-id=${campaign}&project-id=${project_id}`;
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results = []
		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/utm/content', {
		responses: {
			'200': {
				description: 'Return list of content by given org and campaign',
				content: {
					'application/json': {
						schema: getModelSchemaRef(NewUserActivities),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getContent(
		@param.query.string('campaignid') campaign: string,
	): Promise<object> {
		const org_id = await this.userRepository.findOrganization(this.user)
		let response: any = {};
		try {
			const url = `/dev/utm/content?org-id=${org_id}&campaign-id=${campaign}`;
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results = []
		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/utm/impressions-by-day', {
		responses: {
			'200': {
				description: 'Total events for this campaign pre-grouped by day',
				content: {
					'application/json': {
						schema: getModelSchemaRef(NewUserActivities),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getImpressionsByDay(
		@param.query.string('campaignid') campaign: string,
	): Promise<object> {
		const org_id = await this.userRepository.findOrganization(this.user)
		let response: any = {};
		try {
			const url = `/dev/utm/impressions?org-id=${org_id}&campaign-id=${campaign}&days=30`;
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results = []
		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/utm/events', {
		responses: {
			'200': {
				description: 'Return list of events by given org and campaign',
				content: {
					'application/json': {
						schema: getModelSchemaRef(NewUserActivities),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getEvents(
		@param.query.string('campaignid') campaign: string,
	): Promise<object> {
		const org_id = await this.userRepository.findOrganization(this.user)
		let response: any = {};
		try {
			const url = `/dev/utm/events?org-id=${org_id}&campaign-id=${campaign}`;
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results = []
		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/utm/connections', {
		responses: {
			'200': {
				description: 'Return list of connections by given org and campaign',
				content: {
					'application/json': {
						schema: getModelSchemaRef(NewUserActivities),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getConnections(
		@param.query.string('campaignid') campaign: string,
		@param.query.string('eventtype') event_type: string,
		@param.query.string('days') days?: number
	): Promise<object> {
		const org_id = await this.userRepository.findOrganization(this.user)
		let response: any = {};
		try {
			const url = `/dev/utm/connections?org-id=${org_id}&campaign-id=${campaign}&event-type=${event_type}${days != undefined ? `&days=${days}` : ''}`;
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results = []
		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/utm/wallet-connections', {
		responses: {
			'200': {
				description: 'Return list of connections by wallet address and org id',
				content: {
					'application/json': {
						schema: getModelSchemaRef(NewUserActivities),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getWalletConnections(
		@param.query.string('address') address: string,
	): Promise<object> {
		const org_id = await this.userRepository.findOrganization(this.user)
		let response: any = {};
		try {
			const url = `/dev/utm/wallet-connections?org-id=${org_id}&address=${address}`;
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results = []
		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/utm/wallet-sources', {
		responses: {
			'200': {
				description: 'Return list of connections by wallet address and org id',
				content: {
					'application/json': {
						schema: getModelSchemaRef(NewUserActivities),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getUTMWalletSources(
		@param.query.string('address') address: string,
		@injectUserOrgId() orgId: number
	): Promise<object> {
		let response: any = {};
		try {
			const url = `/dev/utm/wallet-sources?org-id=${orgId}&address=${address}`;
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results = []
		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/utm/conversions-count', {
		responses: {
			'200': {
				description: 'Return count of conversions for campaign',
				content: {
					'application/json': {
						schema: getModelSchemaRef(NewUserActivities),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getConversionCount(
		@param.query.string('campaignid') campaign_id: string,
		@param.query.string('projectid') project_id: string,
	): Promise<object> {
		const org_id = await this.userRepository.findOrganization(this.user)
		let response: any = {};
		try {
			const url = `/dev/utm/conversions?org-id=${org_id}&campaign-id=${campaign_id}&project-id=${project_id}`;
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results = []
		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/utm/conversion-percent', {
		responses: {
			'200': {
				description: 'Return percent of conversions for campaign',
				content: {
					'application/json': {
						schema: getModelSchemaRef(NewUserActivities),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getConversionPercent(
		@param.query.string('campaignid') campaign_id: string,
		@param.query.string('projectid') project_id: string,
	): Promise<object> {
		const org_id = await this.userRepository.findOrganization(this.user)
		let response: any = {};
		try {
			const url = `/dev/utm/conversion-percent?org-id=${org_id}&campaign-id=${campaign_id}&project-id=${project_id}`;
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results = []
		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/utm/wallets', {
		responses: {
			'200': {
				description: 'Return list of wallets with a limit',
				content: {
					'application/json': {
						schema: getModelSchemaRef(NewUserActivities),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getWalletList(
		@param.query.string('campaignid') campaign_id: string,
		@param.query.string('projectid') project_id: string,
	): Promise<object> {
		const org_id = await this.userRepository.findOrganization(this.user)
		let response: any = {};
		try {
			const url = `/dev/utm/wallets?org-id=${org_id}&campaign-id=${campaign_id}&event-type=connected-wallet&project-id=${project_id}`;
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results = []
		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/activities-by-wallet/{network}/{address}', {
		responses: {
			'200': {
				description: 'Return list of wallets with a limit',
				content: {
					'application/json': {
						schema: getModelSchemaRef(NewUserActivities),
					},
				},
			},
		},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async getWalletActivity(
		@param.path.string('network') network: string,
		@param.path.string('address') address: string,
	): Promise<object> {
		const result: any = {};
		let response: any = {};
		try {
			const url = `/dev/metric/activities-by-wallet?network=${network}&address=${address}&end-date=latest`;
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results = []
		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}


	@get('/metric/utm/conversions', {
		responses: {
			'200': {
				description: 'Return list of conversions by given org and campaign',
				content: {
					'application/json': {
						schema: getModelSchemaRef(NewUserActivities),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getConversionsByDay(
		@param.query.string('campaignid') campaign: string,
		@param.query.string('projectid') project_id: string,
		@param.query.string('days') days: number
	): Promise<object> {
		const org_id = await this.userRepository.findOrganization(this.user)
		let response: any = {};
		try {
			const url = `/dev/utm/conversions?org-id=${org_id}&campaign-id=${campaign}&project-id=${project_id}&days=${days}`;
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results = []
		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/utm/new-engagers', {
		responses: {
			'200': {
				description: 'Return count of new-engagers',
				content: {
					'application/json': {
						schema: getModelSchemaRef(NewUserActivities),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getNewEngagers(
		@param.query.string('campaignid') campaign: string,
		@param.query.string('projectid') project_id: string,
	): Promise<object> {
		const org_id = await this.userRepository.findOrganization(this.user)
		let response: any = {};
		try {
			const url = `/dev/utm/new-engagers?org-id=${org_id}&campaign-id=${campaign}&project-id=${project_id}`;
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results = []
		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/utm/total-views', {
		responses: {
			'200': {
				description: 'Return count of new-engagers',
				content: {
					'application/json': {
						schema: getModelSchemaRef(NewUserActivities),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getTotalViews(
		@param.query.string('campaignId') campaignId: string,
		@injectUserOrgId() orgId: number
	): Promise<object> {
		let response: any = {};
		try {
			const url = `/dev/utm/total-views?org-id=${orgId}&campaign-id=${campaignId}`;
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results = []
		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/tags/{network}/{address}', {
		responses: {
			'200': {
				description: 'List of Wallet Tags',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getWalletTags(
		@param.path.string('network') network: string,
		@param.path.string('address') address: string,
		@param.query.string('organization') organization: string,
	): Promise<object> {
		let response: any = {};
		try {
			const url = `/dev/tags?network=${network}&address=${address}&organization=${organization}`;
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		return {
			statusCode: 200,
			body: data
		};
	}

	@get('/metric/wallet-share/{network}/{address}', {
		responses: {
			'200': {
				description: 'Wallet Share for given network and address',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async getWalletShare(
		@param.path.string('network') network: string,
		@param.path.string('address') address: string,
	): Promise<object> {
		let response: any = {};
		try {
			const url = `/dev/wallet/share?network=ETH&address=${address}`;
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/wallet-tokens/{network}/{address}', {
		responses: {
			'200': {
				description: 'Wallet Share for given network and address',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async getWalletTokens(
		@param.path.string('network') network: string,
		@param.path.string('address') address: string,
	): Promise<object> {
		let response: any = {};
		try {
			const url = `/dev/wallet/tokens?network=ETH&address=${address}`;
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/wallet-nfts/{network}/{address}', {
		responses: {
			'200': {
				description: 'Wallet Share for given network and address',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getNFTs(
		@param.path.string('network') network: string,
		@param.path.string('address') address: string,
	): Promise<object> {
		let response: any = {};
		try {
			//TODO fix this so network can be passed in via reportbuilder
			const url = `/dev/wallet/nfts?network=ETH&address=${address}`;
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];

				for (let nft = 0; nft < element.data.length; nft++) {
					let nft_data = element.data[nft];
					let chain_id = nft_data.chain_id;
					switch (chain_id) {
						case 1:
							nft_data.chain_name = "ETHEREUM";
							break;
						case 137:
							nft_data.chain_name = "POLYGON";
							break;
						case 42161:
							nft_data.chain_name = "ARBITRUM";
							break;
					}
				}
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/wallet-flow/{network}/{address}', {
		responses: {
			'200': {
				description: 'Wallet Share for given network and address',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getInflowOutflow(
		@param.path.string('network') network: string,
		@param.path.string('address') address: string,
	): Promise<object> {
		let response: any = {};
		try {
			//TODO fix this so network can be passed in via reportbuilder
			const url = `/dev/wallet/flow?network=ETH&address=${address}`;
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@post('/wallets-overview', {
		responses: {
			'200': {
				description: 'Creates a new segment definition in AWS for metric processing',
				content: {
					'application/json': {
						schema: getModelSchemaRef(WalletOverview),
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async buildSegment(
		@requestBody(WalletOverview) walletOverview: WalletOverview,
	): Promise<object> {
		const result: any = {};
		let response: any;
		try {
			const url = `/dev/wallets/overview`;
			console.log('url: ' + url);
			const signedRequest = getURL(url, 'POST', walletOverview);
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
			throw new Error("Error: " + JSON.stringify(err));
		}

		const data: any = await response.json();
		return {
			statusCode: 200,
			body: data
		};
	}

	@get('/metric/segment-count/{network}/{address}', {
		responses: {
			'200': {
				description: 'Count for a given segment, note address is the viewname in this case',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getSegmentCount(
		@param.path.string('network') network: string,
		@param.path.string('address') address: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	) {
		let response: any = {};
		try {
			//Segments don't take a network, so override to NONE for now
			const url = `/dev/metric?network=NONE&address=${address}&metric-name=SEGMENT_COUNT&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/segment-nft-avg-hold-time/{network}/{address}', {
		responses: {
			'200': {
				description: 'In days for a given segment, the average hold time for NFTs',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getSegmentNFTAverageHoldTime(
		@param.path.string('network') network: string,
		@param.path.string('address') address: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	) {
		let response: any = {};
		try {
			//Segments don't take a network, so override to NONE for now
			const url = `/dev/metric?network=NONE&address=${address}&metric-name=SEGMENT_NFT_AVG_HOLD_TIME&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/segment-activity-count/{network}/{address}', {
		responses: {
			'200': {
				description: 'Count for a given segment, note address is the viewname in this case',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getActivityCount(
		@param.path.string('network') network: string,
		@param.path.string('address') address: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	) {
		let response: any = {};
		try {
			//Segments don't take a network, so override to NONE for now
			const url = `/dev/metric?network=NONE&address=${address}&metric-name=SEGMENT_ACTIVITY_COUNT&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}


	@get('/metric/segment-dapp-activities/{network}/{address}', {
		responses: {
			'200': {
				description: 'Activities based on a segment of wallets',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getSegmentDappActivities(
		@param.path.string('network') network: string,
		@param.path.string('address') address: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	) {
		let response: any = {};
		try {
			//Segments don't take a network, so override to NONE for now
			const url = `/dev/metric?network=NONE&address=${address}&metric-name=SEGMENT_DAPP_ACTIVITIES&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/segment-smart-contract-activities/{network}/{address}', {
		responses: {
			'200': {
				description: 'Activities based on a segment of wallets, grouped by smart contracts',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getSegmentSmartContractActivities(
		@param.path.string('network') network: string,
		@param.path.string('address') address: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	) {
		let response: any = {};
		try {
			//Segments don't take a network, so override to NONE for now
			const url = `/dev/metric?network=NONE&address=${address}&metric-name=SEGMENT_SMART_CONTRACT_ACTIVITIES&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/segment-category-activities/{network}/{address}', {
		responses: {
			'200': {
				description: 'Activities based on a segment of wallets',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getSegmentCategoryActivities(
		@param.path.string('network') network: string,
		@param.path.string('address') address: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	) {
		let response: any = {};
		try {
			//Segments don't take a network, so override to NONE for now
			const url = `/dev/metric?network=NONE&address=${address}&metric-name=SEGMENT_CATEGORY_ACTIVITIES&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/segment-usd-value/{network}/{address}', {
		responses: {
			'200': {
				description: 'Count for a given segment, note address is the viewname in this case',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getSegmentUSDValue(
		@param.path.string('network') network: string,
		@param.path.string('address') address: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	) {
		let response: any = {};
		try {
			//Segments don't take a network, so override to NONE for now
			const url = `/dev/metric?network=NONE&address=${address}&metric-name=SEGMENT_USD_VALUE&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/segment-nfts/{network}/{address}', {
		responses: {
			'200': {
				description: 'NFTs based on a segment of wallets',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getSegmentNFTs(
		@param.path.string('network') network: string,
		@param.path.string('address') address: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	) {
		let response: any = {};
		try {
			//Segments don't take a network, so override to NONE for now
			const url = `/dev/metric?network=NONE&address=${address}&metric-name=SEGMENT_NFTS&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/segment-common-tokens/{network}/{address}', {
		responses: {
			'200': {
				description: 'NFTs based on a segment of wallets',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getSegmentCommonTokens(
		@param.path.string('network') network: string,
		@param.path.string('address') address: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	) {
		let response: any = {};
		try {
			//Segments don't take a network, so override to NONE for now
			const url = `/dev/metric?network=NONE&address=${address}&metric-name=SEGMENT_COMMON_TOKENS&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/segment-net-worth-distribution/{network}/{address}', {
		responses: {
			'200': {
				description: 'Retrieve the net worth distribution of a segment of wallets',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getNetWorthDistribution(
		@param.path.string('network') network: string,
		@param.path.string('address') address: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	) {
		let response: any = {};
		try {
			//Segments don't take a network, so override to NONE for now
			const url = `/dev/metric?network=NONE&address=${address}&metric-name=SEGMENT_NET_WORTH_DISTRIBUTION&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/segment-most-valuable-tokens/{network}/{address}', {
		responses: {
			'200': {
				description: 'Most valuable tokens based on a segment of wallets',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getMostValuableTokens(
		@param.path.string('network') network: string,
		@param.path.string('address') address: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	) {
		let response: any = {};
		try {
			//Segments don't take a network, so override to NONE for now
			const url = `/dev/metric?network=NONE&address=${address}&metric-name=SEGMENT_MOST_VALUABLE_TOKENS&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/segment-model-persona/{network}/{address}', {
		responses: {
			'200': {
				description: 'NFTs based on a segment of wallets',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getSegmentModelPersona(
		@param.path.string('network') network: string,
		@param.path.string('address') address: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	) {
		let response: any = {};
		try {
			//Segments don't take a network, so override to NONE for now
			const url = `/dev/metric?network=NONE&address=${address}&metric-name=SEGMENT_MODEL_PERSONA&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results: any = []
		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];

				for (let n = 0; n < element.data.length; n++) {
					element.data[n].cluster = getClusterPersona(element.data[n].cluster);
				}
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/segment-nft-bought-sold/{network}/{address}', {
		responses: {
			'200': {
				description: 'NFTs based on a segment of wallets',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getSegmentNFTsBoughtSold(
		@param.path.string('network') network: string,
		@param.path.string('address') address: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	) {
		let response: any = {};
		try {
			//Segments don't take a network, so override to NONE for now
			const url = `/dev/metric?network=NONE&address=${address}&metric-name=SEGMENT_NFT_BOUGHT_SOLD&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '');
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/segment-nft-top-bought/{network}/{address}', {
		responses: {
			'200': {
				description: 'Top bought NFTs based on a segment of wallets',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getSegmentNFTTopBought(
		@param.path.string('network') network: string,
		@param.path.string('address') address: string,
	) {
		let response: any = {};
		try {
			//Segments don't take a network, so override to NONE for now
			const url = `/dev/metric?network=NONE&address=${address}&metric-name=SEGMENT_TOP_BOUGHT_NFT_USD`;
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/segment-nft-top-sold/{network}/{address}', {
		responses: {
			'200': {
				description: 'Top sold NFTs based on a segment of wallets',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getSegmentNFTTopSold(
		@param.path.string('network') network: string,
		@param.path.string('address') address: string,
	) {
		let response: any = {};
		try {
			//Segments don't take a network, so override to NONE for now
			const url = `/dev/metric?network=NONE&address=${address}&metric-name=SEGMENT_TOP_SOLD_NFT_USD`;
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/project-dapp-dormant-activities/{network}/{address}', {
		responses: {
			'200': {
				description: 'Activities grouped by dapp',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getDormantDapps(
		@param.path.string('network') network: string,
		@param.path.string('address') address: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	) {
		let response: any = {};
		try {
			//Segments don't take a network, so override to NONE for now
			const url = `/dev/metric?network=NONE&address=${address}&metric-name=PROJECT_ACTIVITIES_BY_DORMANT_USER&end-date=latest`;
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/project-new-user-origin/{network}/{address}', {
		responses: {
			'200': {
				description: 'Activities grouped by dapp',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getNewUserOrigin(
		@param.path.string('network') network: string,
		@param.path.string('address') address: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	) {
		let response: any = {};
		try {
			//Segments don't take a network, so override to NONE for now
			const url = `/dev/metric?network=NONE&address=${address}&metric-name=PROJECT_NEW_USER_ORIGIN&end-date=latest`;
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/project-dapp-new-user-activities/{network}/{address}', {
		responses: {
			'200': {
				description: 'Activities grouped by dapp',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getNewUserDapps(
		@param.path.string('network') network: string,
		@param.path.string('address') address: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	) {
		let response: any = {};
		try {
			//Segments don't take a network, so override to NONE for now
			const url = `/dev/metric?network=NONE&address=${address}&metric-name=PROJECT_ACTIVITIES_BY_NEW_USER_V2&end-date=latest`;
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/project-dapp-at-risk-user-activities/{network}/{address}', {
		responses: {
			'200': {
				description: 'Activities grouped by dapp',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getAtRiskDApps(
		@param.path.string('network') network: string,
		@param.path.string('address') address: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	) {
		let response: any = {};
		try {
			//Segments don't take a network, so override to NONE for now
			const url = `/dev/metric?network=NONE&address=${address}&metric-name=PROJECT_ACTIVITIES_BY_AT_RISK_V2&end-date=latest`;
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/project-dapp-active-activities/{network}/{address}', {
		responses: {
			'200': {
				description: 'Activities grouped by dapp',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getActiveDapps(
		@param.path.string('network') network: string,
		@param.path.string('address') address: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	) {
		let response: any = {};
		try {
			//Segments don't take a network, so override to NONE for now
			const url = `/dev/metric?network=NONE&address=${address}&metric-name=PROJECT_ACTIVITIES_BY_ACTIVE_USER_V2&end-date=latest`;
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/project-category-dormant-activities/{network}/{address}', {
		responses: {
			'200': {
				description: 'Activities grouped by dapp',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getDormantCategories(
		@param.path.string('network') network: string,
		@param.path.string('address') address: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	) {
		let response: any = {};
		try {
			//Segments don't take a network, so override to NONE for now
			const url = `/dev/metric?network=NONE&address=${address}&metric-name=PROJECT_CATEGORIES_BY_DORMANT_USER&end-date=latest`;
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/project-category-active-activities/{network}/{address}', {
		responses: {
			'200': {
				description: 'Activities grouped by dapp',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getActiveCategories(
		@param.path.string('network') network: string,
		@param.path.string('address') address: string,
		@param.query.string('enddate') enddate: string,
		@param.query.string('startdate') startdate?: string,
	) {
		let response: any = {};
		try {
			//Segments don't take a network, so override to NONE for now
			const url = `/dev/metric?network=NONE&address=${address}&metric-name=PROJECT_CATEGORIES_BY_ACTIVE_USER&end-date=latest`;
			const signedRequest = getURL(url, 'GET');
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		let results: any = []

		if (data.Items != undefined && data.Items.length > 0) {
			for (let index = 0; index < data.Items.length; index++) {
				const element = data.Items[index];
				results.push(element)
			}
		}

		return {
			statusCode: 200,
			body: results
		};
	}

	@get('/metric/refresh/{metric}/{address}', {
		responses: {
			'200': {
				description: 'Queues a request to reprocess metric for a specific audience',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async refreshMetric(
		@param.path.string('metric') metric: string,
		@param.path.string('address') address: string
	) {
		let response: any = {};
		try {
			//Segments don't take a network, so override to NONE for now
			const url = `/dev/metric/refresh`;
			let data = {
				"metricName": metric,
				"address": address
			}
			const signedRequest = getURL(url, 'POST', data);
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();

		return data;
	}

	@get('/event/{address}', {
		responses: {
			'200': {
				description: 'Gets events for a given address, within a given org',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getEventsForOrgByAddress(
		@param.path.string('address') address: string,
		@injectUserOrgId() orgId: number
	) {
		let response: any = {};
		try {
			const url = `/api/event?orgId=${orgId}&address=${address}`;
			const signedRequest = getURL(url, 'GET', undefined, UTM_API);
			response = await fetch(`https://${UTM_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		return {
			statusCode: 200,
			body: data
		};
	}

	@get('/campaigns/{campaignId}/events', {
		responses: {
			'200': {
				description: 'Gets events for a given campaign, within a given org',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getEventsForOrgByCampaign(
		@param.path.string('campaignId') campaignId: string,
		@injectUserOrgId() orgId: number
	) {
		let response: any = {};
		try {
			const url = `/api/event?orgId=${orgId}&campaignId=${campaignId}`;
			const signedRequest = getURL(url, 'GET', undefined, UTM_API);
			response = await fetch(`https://${UTM_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		return {
			statusCode: 200,
			body: data
		};
	}

	@get('/metric/event', {
		responses: {
			'200': {
				description: 'Gets events for a given address, within a given org',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getEventsForOrg(
		@param.query.string('countOnly') countOnly = 'false',
		@injectUserOrgId() orgId: number
	) {
		let response: any = {};
		try {
			const responseLimit = 400000;
			const sizePerEvent = 500;
			const limit = responseLimit / sizePerEvent;
			const url = `/api/event?orgId=${orgId}&latest=true${limit > 0 ? `&limit=${limit}` : ''}&count=${countOnly}`;
			const signedRequest = getURL(url, 'GET', undefined, UTM_API);
			response = await fetch(`https://${UTM_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		return {
			statusCode: 200,
			body: data
		};
	}

	@get('/event-types', {
		responses: {
			'200': {
				description: 'Gets events for a given address, within a given org',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getEventTypesForOrg(
		@param.query.string('applicationFilter') applicationFilter: string = '',
		@injectUserOrgId() orgId: number,
	) {
		let response;
		try {
			let url = `/api/event?orgId=${orgId}`;
			if (applicationFilter === 'true') {
				url = url.concat('&applications=true');
			}
			const signedRequest = getURL(url, 'GET', undefined, UTM_API);
			response = await fetch(`https://${UTM_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		return {
			statusCode: 200,
			body: data
		};
	}

	@get('/event-sankey', {
		responses: {
			'200': {
				description: 'Gets events Sankey data for a given address, within a given org',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getEventSankeyForOrg(
		@param.query.string('options') options: string,
		@injectUserOrgId() orgId: number
	) {
		let response: any = {};
		try {
			const url = `/api/event-sankey?orgId=${orgId}&options=${options}`;
			const signedRequest = getURL(url, 'GET', undefined, UTM_API);
			response = await fetch(`https://${UTM_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		return {
			statusCode: 200,
			body: data
		};
	}

	@get('/event/events-by-day', {
		responses: {
			'200': {
				description: 'Gets events for a given address, within a given org',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async getEventsForOrgByDay(
		@injectUserOrgId() orgId: number
	) {
		let response: any = {};
		try {
			const url = `/api/event/events-by-day?org-id=${orgId}`;
			const signedRequest = getURL(url, 'GET', undefined, UTM_API);
			console.log(`https://${UTM_API}${url}`);
			response = await fetch(`https://${UTM_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();
		return {
			statusCode: 200,
			body: data
		};
	}
}
