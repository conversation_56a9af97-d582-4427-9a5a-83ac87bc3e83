import {
	repository,
} from '@loopback/repository';
import {
	param,
	get,
	api,
	getModelSchemaRef,
} from '@loopback/rest';
import {
	OrganizationIntegrationDetails,
	Integration,
} from '../models';
import {OrganizationIntegrationDetailsRepository} from '../repositories';
import {modelForGuard, modelIdForGuard, OrgGuardPropertyStrategy, restrictReadsWithGuard, skipGuardCheck} from '../interceptors/crud-guard.interceptor';
import {injectGuardedFilter, injectUserOrgId, guardStrategy} from '../interceptors/crud-guard.interceptor';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../services/basic.authorizor';

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardPropertyStrategy<OrganizationIntegrationDetails>({
	orgIdModelPropertyName: 'orgId',
	repositoryClass: OrganizationIntegrationDetailsRepository
}))
export class OrganizationIntegrationDetailsIntegrationController {
	constructor(
		@repository(OrganizationIntegrationDetailsRepository)
		public organizationIntegrationDetailsRepository: OrganizationIntegrationDetailsRepository,
	) { }

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/organization-integration-details/{id}/integration', {
		responses: {
			'200': {
				description: 'Integration belonging to OrganizationIntegrationDetails',
				content: {
					'application/json': {
						schema: getModelSchemaRef(Integration),
					},
				},
			},
		},
	})
	@restrictReadsWithGuard({
		plural: true,
		filterOnly: false
	})
	async getIntegration(
		@param.path.number('id') id: typeof OrganizationIntegrationDetails.prototype.id,
	): Promise<Integration> {
		return this.organizationIntegrationDetailsRepository.integration(id);
	}
}
