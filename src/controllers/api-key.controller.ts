import {
	post,
	get,
	param,
	del,
	requestBody,
	HttpErrors,
	api,
  } from '@loopback/rest';
  import {inject} from '@loopback/core';
  import {authenticate} from '@loopback/authentication';
  import {ApiKeyService} from '../services/api-key.service';
  import {basicAuthorization} from '../services';
  import {authorize} from '@loopback/authorization';
  import {GuardSkipStrategy, guardStrategy, skipGuardCheck} from '../interceptors';
  import {scopeAuthorization} from '../services/api-key.authorizor';

  @api({basePath: '/api/v1'})
  @guardStrategy(new GuardSkipStrategy())

  export class ApiKeyController {
	constructor(
	  @inject('services.ApiKeyService')
	  private apiKeyService: ApiKeyService,
	) {}

	@authenticate('jwt')
	@authorize({
	  allowedRoles: ['raleon-admin'],
	  voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/api-keys')
	async createApiKey(
	  @requestBody({
		content: {
		  'application/json': {
			schema: {
			  type: 'object',
			  properties: {
				name: {type: 'string'},
				scopes: {type: 'array', items: {type: 'string'}},
			  },
			  required: ['name'],
			},
		  },
		},
	  })
	  apiKeyData: {name: string; scopes?: string[]},
	) {
	  const newApiKey = await this.apiKeyService.generateApiKey(
		apiKeyData.name,
		apiKeyData.scopes,
	  );
	  return {id: newApiKey.id, key: newApiKey.key};
	}

	@authenticate('jwt')
	@authorize({
	  allowedRoles: ['raleon-admin'],
	  voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/api-keys')
	async listApiKeys() {
	  return this.apiKeyService.listApiKeys();
	}

	@authenticate('jwt')
	@authorize({
	  allowedRoles: ['raleon-admin'],
	  voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@del('/api-keys/{id}')
	async revokeApiKey(@param.path.number('id') id: number) {
	  await this.apiKeyService.revokeApiKey(id);
	}

	@authenticate('api-key')
	@authorize({
	  allowedRoles: ['standard'],
	  voters: [scopeAuthorization],
	})
	@skipGuardCheck()
	@get('/api-keys/test')
	async testApiKey() {
	  return {
		message: 'API key is valid',
		timestamp: new Date().toISOString(),
	  };
	}

  }
