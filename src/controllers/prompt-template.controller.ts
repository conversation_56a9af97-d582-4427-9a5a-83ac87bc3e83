import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {service} from '@loopback/core';
import {
  post,
  get,
  patch,
  del,
  param,
  requestBody,
  HttpErrors,
  api,
} from '@loopback/rest';
import {PromptTemplate} from '../models';
import {PromptTemplateService} from '../services';
import {basicAuthorization} from '../services';
import {GuardSkipStrategy, guardStrategy, skipGuardCheck} from '../interceptors';


@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class PromptTemplateController {
  constructor(
    @service(PromptTemplateService)
    private promptTemplateService: PromptTemplateService,
  ) {}

  @post('/prompt-templates')
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['raleon-admin'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async create(
    @requestBody() promptTemplate: Omit<PromptTemplate, 'id'>,
  ): Promise<PromptTemplate> {
    return this.promptTemplateService.create(promptTemplate);
  }

  @get('/prompt-templates')
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['raleon-admin'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async find(): Promise<PromptTemplate[]> {
    return this.promptTemplateService.find();
  }

  @get('/prompt-templates/{id}')
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['raleon-admin'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async findById(
    @param.path.number('id') id: number,
  ): Promise<PromptTemplate> {
    return this.promptTemplateService.findById(id);
  }

  @patch('/prompt-templates/{id}')
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['raleon-admin'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async updateById(
    @param.path.number('id') id: number,
    @requestBody() promptTemplate: Partial<PromptTemplate>,
  ): Promise<void> {
    await this.promptTemplateService.updateById(id, promptTemplate);
  }

  @del('/prompt-templates/{id}')
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['raleon-admin'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.promptTemplateService.deleteById(id);
  }
}
