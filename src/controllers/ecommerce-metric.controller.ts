import {authenticate} from '@loopback/authentication';
import {User} from '@loopback/authentication-jwt';
import {authorize} from '@loopback/authorization';
import {inject} from '@loopback/core';
import {repository} from '@loopback/repository';
import {
	api,
	get, getModelSchemaRef, param, post, Request, requestBody, RestBindings
} from '@loopback/rest';
import {SecurityBindings} from '@loopback/security';
import {injectUserOrgId, GuardSkipStrategy, guardStrategy, skipGuardCheck} from '../interceptors';
import {ActiveUserInteractionCount, ActiveUserPercentActivity, ActiveUsers, AddressBalance, AddressLastTransactionDate, AddressTokenBalance, AtRiskActivities, AtRiskUsers, CommonTokensHeld, DormantUsers, HighestValueWallet, InteractionsByType, LifetimeTransaction, MetricProof, MostActiveTime, NewUserActivities, NewUsers, TotalTokenHolders, TotalUSDValueOfWallets, UniqueWallets, WalletOverview} from '../models';
import {MetricRepository, MetricSegmentRepository, OrganizationMetricRepository, OrganizationRepository, UserRepository} from '../repositories';
import {basicAuthorization} from '../services';
import {getClusterPersona} from '../utils/utils';
import {scopeAuthorization} from '../services/api-key.authorizor';
const fetch = require('node-fetch')
const aws4 = require('aws4')

let DATA_API: string = process.env.ECOMMERCE_AWS_URL! || "nq2mcp0pfh.execute-api.us-east-1.amazonaws.com";
const AWS_ACCESS_KEY = process.env.API_ACCESS_KEY;
const AWS_SECRET_KEY = process.env.API_SECRET_KEY;

export function getURL(path: string, method: string, body?: any, host?: string, stringify = true) {
	const opts = {
		host: host || DATA_API,
		path: path,
		region: 'us-east-1',
		service: 'execute-api',
		mode: 'cors',
		body: body != undefined ? (stringify ? JSON.stringify(body) : body) : undefined,
		headers: {
			'Content-Type': 'application/json',
		},
		method: method
	}
	return aws4.sign(opts, {accessKeyId: AWS_ACCESS_KEY, secretAccessKey: AWS_SECRET_KEY});
}
@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class EcommerceMetricController {
	constructor(
		@inject(RestBindings.Http.REQUEST) private req: Request,
		@inject(SecurityBindings.USER) private user: User,
		@repository('UserRepository') private userRepository: UserRepository,
		@repository(OrganizationMetricRepository) private organizationMetricRepository: OrganizationMetricRepository,
		@repository(MetricRepository) private metricRepository: MetricRepository,
		@repository(MetricSegmentRepository) private metricSegmentRepository: MetricSegmentRepository,
		@repository(OrganizationRepository) private organizationRepository: OrganizationRepository,
	) { }

	@get('/automation/metric', {
		responses: {
			'200': {
				description: 'Returns the metric results for the given parameters',
				content: {
					'application/json': {
						schema: getModelSchemaRef(AddressBalance),
					},
				},
			},
		},
	})
	@authenticate('api-key')
	@authorize({
		allowedRoles: ['standard'],
		voters: [scopeAuthorization],
	})
	@skipGuardCheck()
	async getAutomationMetric(
		@param.query.string('name') name: string,
		@param.query.string('calculation') calculation: string,
		@param.query.string('group-by') groupby: string,
		@param.query.string('end-date') enddate: string,
		@param.query.string('start-date') startdate: string,
		@param.query.string('campaignId') campaignId: number,
		@param.query.string('orgId') orgId: number,
	): Promise<object> {
		return await this.fetchMetric(name, calculation, groupby, enddate, startdate, orgId, campaignId);
	}

	@get('/metric', {
		responses: {
			'200': {
				description: 'Returns the metric results for the given parameters',
				content: {
					'application/json': {
						schema: getModelSchemaRef(AddressBalance),
					},
				},
			},
		},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async getMetric(
		@param.query.string('name') name: string,
		@param.query.string('calculation') calculation: string,
		@param.query.string('group-by') groupby: string,
		@param.query.string('end-date') enddate: string,
		@param.query.string('start-date') startdate: string,
		@param.query.string('campaignId') campaignId: number,
		@injectUserOrgId() orgId: number,
	): Promise<object> {
		// use this to test chomp demo locally
		//orgId = 1000438;
		return await this.fetchMetric(name, calculation, groupby, enddate, startdate, orgId, campaignId);
	}

	@get('/metric-admin', {
		responses: {
			'200': {
				description: 'Returns the metric results for the given parameters',
				content: {
					'application/json': {
						schema: getModelSchemaRef(AddressBalance),
					},
				},
			},
		},
	})
	@authenticate('user-access-token')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async getMetricAdmin(
		@param.query.string('name') name: string,
		@param.query.string('calculation') calculation: string,
		@param.query.string('group-by') groupby: string,
		@param.query.string('end-date') enddate: string,
		@param.query.string('start-date') startdate: string,
		@injectUserOrgId() orgId: number,
	): Promise<object> {
		return await this.fetchMetric(name, calculation, groupby, enddate, startdate, orgId);
	}

	@get('/metric-progress', {
		responses: {
			'200': {
				description: 'Returns the progress status of metrics calculation for an organization',
				content: {
					'application/json': {
						schema: {
							type: 'object',
							properties: {
								statusCode: {
									type: 'number',
								},
								body: {
									type: 'object',
									properties: {
										progress: {
											type: 'number',
											description: 'Progress percentage of metric calculation'
										},
										status: {
											type: 'string',
											description: 'Current status of the calculation'
										},
										lastUpdated: {
											type: 'string',
											format: 'date-time',
											description: 'Timestamp of the last update'
										}
									}
								}
							}
						}
					},
				},
			},
		},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async getMetricProgress(
		@injectUserOrgId() orgId: number,
	): Promise<object> {
		let progress = 25;
		let status = 'Processing started';

		try {
			const organization = await this.organizationRepository.findById(orgId);
			if (organization.historicalDataComplete) {
				progress = 50;
			}

			const today = new Date();
			today.setHours(0, 0, 0, 0);

			const metrics = await this.organizationMetricRepository.find({
				where: {
					orgId: orgId
				}
			});

			if (metrics.length > 0) {
				const completedMetrics = metrics.filter(metric => {
					const lastRunDate = metric.lastRunDate ? new Date(metric.lastRunDate) : null;
					const errorDate = metric.errorDate ? new Date(metric.errorDate) : null;

					return (lastRunDate && lastRunDate.setHours(0, 0, 0, 0) === today.getTime()) ||
						(errorDate && errorDate.setHours(0, 0, 0, 0) === today.getTime());
				});

				const completionRatio = completedMetrics.length / metrics.length;

				if (completionRatio === 1) {
					progress = 100;
					status = 'Complete';
				} else if (completionRatio >= 0.5) {
					progress = 75;
					status = 'Almost Finished';
				} else if (progress === 50) {
					status = 'Processing metrics';
				} else if (progress === 25) {
					status = 'Retrieving Order Data';
				}
			}
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
			throw err;
		}

		return {
			statusCode: 200,
			body: {
				progress: progress,
				status: status,
				lastUpdated: new Date().toISOString()
			}
		};
	}

	@authenticate('jwt')
	@authorize({
	allowedRoles: ['admin', 'support', 'customer'],
	voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/organization-metrics-status', {
	responses: {
		'200': {
		description: 'Get metrics status for organization',
		content: {
			'application/json': {
			schema: {
				type: 'object',
				properties: {
				metrics: {
					type: 'array',
					items: {
					type: 'object',
					properties: {
						id: { type: 'number' },
						name: { type: 'string' },
						lastRunDate: { type: 'string', format: 'date-time' },
						completed: { type: 'boolean' }
					}
					}
				}
				}
			}
			}
		}
		}
	}
	})
	async getMetricsCompletion(
	@injectUserOrgId() orgId: number
	): Promise<{ metrics: Array<{ id: number; name: string; lastRunDate: string | null; completed: boolean }> }> {
		const orgMetrics = await this.organizationMetricRepository.find({
			where: { orgId }
		});

		const metric = await this.metricRepository.find();

		const mappedMetrics = metric.map(metric => {
			const orgMetric = orgMetrics.find(om => om.metricId === metric.id);
			return {
			id: metric.id!,
			name: metric.name,
			lastRunDate: orgMetric?.lastRunDate || null,
			completed: orgMetric?.lastRunDate !== null
			};
		});

		return { metrics: mappedMetrics.filter(metric => metric !== undefined) };
	}

	public async fetchMetric(
		name: string,
		calculation: string,
		groupby: string,
		enddate: string,
		startdate: string,
		orgId: number,
		campaignId?: number
	): Promise<object> {
		let response: any;
		try {
			let campaignfilter = '';
			if (campaignId != undefined) {
				campaignfilter = `&campaignId=${campaignId}`;
			}
			let environment = 'prod'
			if (!DATA_API.includes("nq2mcp0pfh")) {
				environment = 'dev'
			}
			console.log("Node_env=" + process.env.NODE_ENV);
			const url = `/${environment}/metric?orgId=${orgId}&name=${name}&calculation=${calculation}&group-by=${groupby}&end-date=${enddate}` + (startdate != undefined ? '&start-date=' + startdate : '') + campaignfilter;
			console.log('url: ' + url);
			const signedRequest = getURL(url, 'GET');
			console.log('signedRequest: ' + JSON.stringify(signedRequest));
			console.log('fulurl: ' + `https://${DATA_API}${url}`);
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
		const data: any = await response.json();

		return {
			statusCode: 200,
			body: data
		};
	}
}
