import {
	repository,
} from '@loopback/repository';
import {
	param,
	get,
	getModelSchemaRef,
	api,
	HttpErrors,
	requestBody,
	post,
} from '@loopback/rest';
import {
	FeatureSetting,
	Organization,
	OrganizationPlan,
	OrganizationPlanRelations,
} from '../models';
import {FeatureSettingRepository, OrganizationPlanRepository, OrganizationRepository, PlanFeatureRepository} from '../repositories';
import {OrgGuardPropertyStrategy, guardStrategy, injectUserOrgId, modelForGuard, modelIdForGuard, restrictReadsWithGuard, skipGuardCheck} from '../interceptors';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../services/basic.authorizor';
import {service} from '@loopback/core';
import {FeatureService} from '../services/feature.service';


@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardPropertyStrategy<any>({
	orgIdModelPropertyName: 'orgId',
	repositoryClass: OrganizationRepository
}))
export class FeatureSettingController {
	constructor(
		@repository(FeatureSettingRepository)
		public featureSettingRepository: FeatureSettingRepository,
		@repository(OrganizationRepository)
		public organizationRepository: OrganizationRepository,
		@repository(OrganizationPlanRepository)
		public organizationPlanRepository: OrganizationPlanRepository,
		@repository(PlanFeatureRepository)
		public planFeatureRepository: PlanFeatureRepository,
		@service(FeatureService)
		public featureService: FeatureService,
	) { }

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'raleon-admin', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/feature-setting/{type}', {
		responses: {
			'200': {
				description: 'Gets feature setting information for a feature type',
				content: {
					'application/json': {
						schema: {type: 'array', items: getModelSchemaRef(Organization)},
					},
				},
			},
		},
	})
	async getFeatureSetting(
		@param.path.string('type') type: string,
		@injectUserOrgId() orgId: number,
	): Promise<FeatureSetting | null> {
		if (!FeatureSettingTypes.includes(type!)) {
			throw new HttpErrors.BadRequest('Invalid feature type');
		}
		return this.featureSettingRepository.findOne({
			where: {
				organizationId: orgId,
				name: type,
			},
		});
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'raleon-admin', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/features', {
		responses: {
			'200': {
				description: 'List of feature states for the current organization and its plan',
				content: {
					'application/json': {
						schema: {
							type: 'object',
							properties: {
								// organization: { type: 'object' },
								// plan: { type: 'object' },
								features: {
									type: 'array',
									items: { type: 'object' },
								},
							},
						},
					},
				},
			},
		},
	})
	async getFeatureStates(
		@injectUserOrgId() orgId: number,
	): Promise<{ /*organization: any; /*plan: any;*/ features: any[] }> {
		return this.featureService.getFeatureStates(orgId);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'raleon-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/feature-setting/{type}', {
		responses: {
			'200': {
				description: 'Gets feature setting information for a feature type',
				content: {
					'application/json': {
						schema: {type: 'array', items: getModelSchemaRef(Organization)},
					},
				},
			},
		},
	})
	async createOrUpdateFeatureSetting(
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						properties: {
							name: {type: 'string'},
							enabled: {type: 'boolean'},
							live: {type: 'boolean'},
						},
						required: ['name', 'enabled', 'live'],
					},
				},
			},
		}) payload: Partial<FeatureSetting>,
		@injectUserOrgId() orgId: number,
	): Promise<void> {
		if (!FeatureSettingTypes.includes(payload.name!)) {
			throw new HttpErrors.BadRequest('Invalid feature type');
		}
		const featureSetting = await this.featureSettingRepository.findOne({
			where: {
				name: payload.name,
				organizationId: orgId,
			},
		});

		// this.featureService.invalidateCache(orgId);

		if (featureSetting) {
			await this.featureSettingRepository.updateById(featureSetting.id, payload);
		} else {
			await this.featureSettingRepository.create({
				...payload,
				organizationId: orgId,
			});
		}
	}
}

export const FeatureSettingTypes = [
	'vip',
]
