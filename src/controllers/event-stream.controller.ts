// Uncomment these imports to begin using these cool features!

import {api, get, post, requestBody} from '@loopback/rest';
import {GuardSkipStrategy, guardStrategy, injectRaleonUserId, injectUserOrgId, skipGuardCheck} from '../interceptors';
import {repository} from '@loopback/repository';
import {LoyaltyEventRepository, RaleonUserIdentityRepository} from '../repositories';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../services/basic.authorizor';
import {EventStreamEvent, LoyaltyEventPublisher} from '../services/event-stream/loyalty-event-publisher.service';
import {service} from '@loopback/core';


@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class EventStreamController {
	constructor(
		@repository(RaleonUserIdentityRepository) private raleonUserIdentityRepository: RaleonUserIdentityRepository,
		@repository(LoyaltyEventRepository) protected loyaltyEventRepository: LoyaltyEventRepository,
		@service(LoyaltyEventPublisher) protected loyaltyEventPublisher: LoyaltyEventPublisher,
	) {}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/event-stream', {
		responses: {
			'200': {
				description: 'Sends the event stream to be processed',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async sendEvent(
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						properties: {
							eventName: {type: 'string'},
							data: {type: 'object'},
						},
					},
				},
			},
		}) body: any,
		@injectUserOrgId() orgId: number,
	): Promise<any> {

		let eventData = this.getTestEventData(body.eventName);
		if (body.testData) {
			eventData = JSON.parse(body.testData);
		}
		console.log('Event Data:', eventData);
		await this.loyaltyEventPublisher.publishEvent(
			body.eventName,
			body.customerId,
			orgId,
			eventData,
		);

		return {
			success: true,
		};
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/event-stream', {
		responses: {
			'200': {
				description: 'Gets supported events',
				content: { 'application/json': { schema: {}, }, },
			},
		},
	})
	async getEvents() {
		const events = await this.loyaltyEventRepository.find();
		const testEvents = events.map(event => {
            return { ...event, testData: this.getTestEventData(event.name) };
        });
        return testEvents;
	}

	private getTestEventData(eventName: string) {
		if (eventName == EventStreamEvent.CUSTOMER_BIRTHDAY) {
			return { birthday: new Date().toISOString() };
		} else if (eventName == EventStreamEvent.BALANCE_CHANGE) {
			return { balanceChange: 10, totalPoints: 100 };
		} else if (eventName == EventStreamEvent.REFERRAL_SHARED) {
			return { friendEmail: '<EMAIL>' }
		} else if (eventName == EventStreamEvent.REFERRAL_RECEIVED) {
			return { email: '<EMAIL>', referralLink: 'abc123' }
		} else if (eventName == EventStreamEvent.VIP_TIER_UPDATED) {
			return { vipTier: 'Gold' }
		} else if (eventName == EventStreamEvent.NEXT_REWARD_UPDATE) {
			return { nextReward: 'Free Coffee', deficit: 0 }
		} else if (eventName == EventStreamEvent.OFFER_RECEIVED) {
			return {
				customerId: '7591118602398',
				rewardName: '25% off coupon',
				rewardAmount: 25,
				rewardAmountType: 'percent-off-product',
				productImageUrl: 'https://cdn.shopify.com/s/files/1/0642/3989/4686/products/BearSucker.png?v=1707232035',
				expiresIn: 30,
				externalId: '7657886482590',
				externalName: 'Bear Super Lollypop',
				externalHandle: 'bear-super-lollypop',
				couponCode: '0c7f3338-fd1b-4542-82b8-4141a56ae289',
				expirationDate: new Date().toISOString(),
			};
		} else if (eventName == EventStreamEvent.REFERRAL_COMPLETED) {
			return '';
		} else if (eventName == EventStreamEvent.JOINED_LOYALTY) {
			return '';
		} else if (eventName == EventStreamEvent.REWARD_EXPIRING) {
			return { "rewardName": "Exipring Reward Name", "expirationDate": "2024-12-22T00:00:00", "couponId": "123" };
		} else if (eventName == EventStreamEvent.REWARD_GRANTED) {
			return { "rewardName": "Test Reward Granted", "expirationDate": "2024-12-22T00:00:00", "couponId": "123" };
		} else if (eventName == EventStreamEvent.BIRTHDAY_REWARD_GRANTED) {
			return { "rewardName": "Bday Reward", "birthday": "1988-12-25T00:00:00", "couponId": "123"};
		}
		return '';
	}
}
