import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {
	Count,
	CountSchema,
	Filter,
	repository,
	Where,
} from '@loopback/repository';
import {
	api,
	del,
	get,
	getModelSchemaRef,
	getWhereSchemaFor,
	param,
	patch,
	post,
	requestBody,
} from '@loopback/rest';
import {guardStrategy, OrgGuardMultiHopPropertyStrategy, skipGuardCheck} from '../interceptors';
import {
	Goal,
	Content,
	Quest,
	Campaign,
} from '../models';
import {GoalRepository, QuestRepository} from '../repositories';
import {basicAuthorization} from '../services';

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardMultiHopPropertyStrategy<Goal, Quest, Campaign>({
	repositoryClass: GoalRepository,
	firstHopIdPropertyName: 'questId',
	firstHopRepositoryClass: QuestRepository,
	inclusionChainAfterFirstHop: {relation: "campaign"},
	lastHopOrgIdPropertyName: 'orgId',
}))
export class GoalContentController {
	constructor(
		@repository(GoalRepository) protected goalRepository: GoalRepository,
	) { }

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck() //TODO: update
	@get('/goals/{id}/content', {
		responses: {
			'200': {
				description: 'Goal has one Content',
				content: {
					'application/json': {
						schema: getModelSchemaRef(Content),
					},
				},
			},
		},
	})
	async get(
		@param.path.number('id') id: number,
		@param.query.object('filter') filter?: Filter<Content>,
	): Promise<Content> {
		return this.goalRepository.content(id).get(filter);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck() //TODO: update
	@post('/goals/{id}/content', {
		responses: {
			'200': {
				description: 'Goal model instance',
				content: {'application/json': {schema: getModelSchemaRef(Content)}},
			},
		},
	})
	async create(
		@param.path.number('id') id: typeof Goal.prototype.id,
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(Content, {
						title: 'NewContentInGoal',
						exclude: ['id'],
						optional: ['goalId']
					}),
				},
			},
		}) content: Omit<Content, 'id'>,
	): Promise<Content> {
		return this.goalRepository.content(id).create(content);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck() //TODO: update
	@patch('/goals/{id}/content', {
		responses: {
			'200': {
				description: 'Goal.Content PATCH success count',
				content: {'application/json': {schema: CountSchema}},
			},
		},
	})
	async patch(
		@param.path.number('id') id: number,
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(Content, {partial: true}),
				},
			},
		})
		content: Partial<Content>,
		@param.query.object('where', getWhereSchemaFor(Content)) where?: Where<Content>,
	): Promise<Count> {
		return this.goalRepository.content(id).patch(content, where);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck() //TODO: update
	@del('/goals/{id}/content', {
		responses: {
			'200': {
				description: 'Goal.Content DELETE success count',
				content: {'application/json': {schema: CountSchema}},
			},
		},
	})
	async delete(
		@param.path.number('id') id: number,
		@param.query.object('where', getWhereSchemaFor(Content)) where?: Where<Content>,
	): Promise<Count> {
		return this.goalRepository.content(id).delete(where);
	}
}
