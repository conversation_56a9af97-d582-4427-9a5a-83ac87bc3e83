// import {
//   repository,
// } from '@loopback/repository';
// import {
//   param,
//   get,
//   getModelSchemaRef,
//   api,
// } from '@loopback/rest';
// import {
//   Journey,
//   RaleonUser,
// } from '../models';
// import {JourneyRepository} from '../repositories';

// @api({basePath: '/api/v1'})
// export class JourneyRaleonUserController {
//   constructor(
//     @repository(JourneyRepository)
//     public journeyRepository: JourneyRepository,
//   ) { }

//   @get('/journeys/{id}/raleon-user', {
//     responses: {
//       '200': {
//         description: 'RaleonU<PERSON> belonging to Journey',
//         content: {
//           'application/json': {
//             schema: {type: 'array', items: getModelSchemaRef(RaleonUser)},
//           },
//         },
//       },
//     },
//   })
//   async getRaleonUser(
//     @param.path.number('id') id: typeof Journey.prototype.id,
//   ): Promise<RaleonUser> {
//     return this.journeyRepository.raleonUser(id);
//   }
// }
