import {AuthenticationBindings, AuthenticationComponent, registerAuthenticationStrategy} from '@loopback/authentication';
import {
  JWTAuthenticationComponent,
  TokenServiceBindings} from '@loopback/authentication-jwt';
import {AuthorizationComponent} from '@loopback/authorization';
import {BootMixin} from '@loopback/boot';
import {ApplicationConfig, createBindingFromClass, inject, MetadataInspector} from '@loopback/core';
import {RepositoryMixin} from '@loopback/repository';
import {RestApplication, RestBindings, RequestContext, Middleware} from '@loopback/rest';
import {CrudRestComponent, CrudRestControllerOptions, ModelCrudRestApiConfig} from '@loopback/rest-crud';
import {
  RestExplorerBindings,
  RestExplorerComponent
} from '@loopback/rest-explorer';
import {ServiceMixin} from '@loopback/service-proxy';
import crypto from 'crypto';
import 'dotenv/config';
import path from 'path';
import {Metric<PERSON>ontroller, OnboardController} from './controllers';
import {PasswordHasherBindings, UserServiceBindings} from './keys';
import {MySequence} from './sequence';
import {BcryptHasher, JWTService, UserManagementService, ApiKeyAuthenticationStrategy} from './services';

import { defineCrudRestController } from './crud-rest-default';
import * as restConfigurations from './model-endpoints/index';
import {CrudGuardInterceptor} from './interceptors/crud-guard.interceptor';
import {globalBindingRequestRepositoryClasses} from './global-binding-request-container';
import {AccessTokenService} from './services/oauth/token-service';
import {ShopifyTokenService} from './services/oauth/shopify-token-service';
import {ShopifyCustomerAuthenticationStrategy} from './services/oauth/shopify-customer-auth';
import { UserAccessTokenAuthenticationStrategy } from './services/oauth/user-access-token-auth-strategy';
import {MessageQuotaService} from './services/message-quota.service';
import {CleanupService} from './services/cleanup.service';
export {ApplicationConfig};



export class RaleonWebappApplication extends BootMixin(
  ServiceMixin(RepositoryMixin(RestApplication)),
) {
  repositoryBindingRegistry = new Map<any, any>();
  private readonly controllersToCheck = new Set<any>();

  constructor(options: ApplicationConfig = {}) {
    // Configure REST options before calling super()
    options.rest = {
      ...options.rest,
      maxBodySize: '50mb',
      port: process.env.PORT || 3000,
      cors: {
        origin: '*',
        methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
        preflightContinue: false,
        optionsSuccessStatus: 204,
        maxAge: 86400,
        credentials: true,
      },
      expressSettings: {
        'trust proxy': true,
      },
      bodyParsers: {
        json: {
          limit: '50mb',
        },
        urlencoded: {
          extended: true,
          limit: '50mb',
        },
        raw: {
          limit: '50mb',
        },
        text: {
          limit: '50mb',
        },
      },
    };

    super(options);

    this.controller(MetricController);
	this.controller(OnboardController);


    // Mount authentication system
    this.component(AuthenticationComponent);
    this.component(JWTAuthenticationComponent);
    this.component(AuthorizationComponent);

    this.setUpBindings();

    // Set up the custom sequence
    this.sequence(MySequence);

    // Set up default home page
    this.static('/', path.join(__dirname, '../public/dist'));

    // Customize @loopback/rest-explorer configuration here
    this.configure(RestExplorerBindings.COMPONENT).to({
      path: '/explorer',
    });
    this.component(RestExplorerComponent);

    this.projectRoot = __dirname;
    // Customize @loopback/boot Booter Conventions here
    this.bootOptions = {
      controllers: {
        // Customize ControllerBooter Conventions here
        dirs: ['controllers'],
        extensions: ['.controller.js'],
        nested: true,

      },
    };
    this.component(CrudRestComponent);
  }

  controller = (cls: any, ...args: any[]): any => {
	super.controller(cls, ...args);

	this.controllersToCheck.add(cls);
  };

  setUpBindings(): void {
    // Configure request body parser options
    this.bind(RestBindings.REQUEST_BODY_PARSER_OPTIONS).to({
      limit: '50mb',
      json: {
        verify: (req: any, res: any, buf: Buffer) => {
          // Store raw body for Stripe webhooks
          if (req.url.includes('/stripe/webhooks')) {
            req.rawBody = buf.toString('utf8');
          }
        },
      },
    });

    // Bind bcrypt hash services
    this.bind(PasswordHasherBindings.ROUNDS).to(10);
    this.bind(PasswordHasherBindings.PASSWORD_HASHER).toClass(BcryptHasher);
    this.bind(TokenServiceBindings.TOKEN_SERVICE).toClass(JWTService);
	this.bind('services.AccessTokenService').toClass(AccessTokenService);
	this.bind('services.ShopifyTokenService').toClass(ShopifyTokenService);
    this.bind('services.ApiKeyAuthenticationStrategy').toClass(ApiKeyAuthenticationStrategy);
    this.bind('services.MessageQuotaService').toClass(MessageQuotaService);
    this.bind('services.CleanupService').toClass(CleanupService);

	registerAuthenticationStrategy(this, ShopifyCustomerAuthenticationStrategy);
	registerAuthenticationStrategy(this, UserAccessTokenAuthenticationStrategy);
	registerAuthenticationStrategy(this, ApiKeyAuthenticationStrategy);

    this.bind(UserServiceBindings.USER_SERVICE).toClass(UserManagementService);
    // Use JWT secret from JWT_SECRET environment variable if set
    // otherwise create a random string of 64 hex digits
    const secret =
      process.env.JWT_SECRET ?? crypto.randomBytes(32).toString('hex');
    this.bind(TokenServiceBindings.TOKEN_SECRET).to(secret);


  }

  async boot(): Promise<void> {
    await super.boot();
	restConfigurations
    for (let i = 0; i < restConfigurations.default.length; i++) {
		const restConfig: ModelCrudRestApiConfig = restConfigurations.default[i] as ModelCrudRestApiConfig;
		let repoBinding;
		try {
		  repoBinding = this.repository(restConfig.repository as any);
		} catch (e) {
		  throw new Error("Repository not found for " + restConfig.model.name);
		}

		this.repositoryBindingRegistry.set(restConfig.repository, repoBinding);
	}

	globalBindingRequestRepositoryClasses.forEach((repoClass) => {
		if (this.repositoryBindingRegistry.has(repoClass)) {
			return;
		}

		this.repositoryBindingRegistry.set(repoClass, this.repository(repoClass));
	});

	await Promise.all(Array.from(this.controllersToCheck).map(cls =>
		Promise.all(Object.getOwnPropertyNames(cls.prototype).map(propertyName => {
			if (typeof cls.prototype[propertyName] === 'function') {

				const operationMetadata = MetadataInspector.getMethodMetadata<object>(
					'openapi-v3:methods',
					cls.prototype,
					propertyName
				);

				if (operationMetadata) {
					return CrudGuardInterceptor.checkControllerConfig(cls, propertyName, this);
				}
			}
		}))
	));

    for (let i = 0; i < restConfigurations.default.length; i++) {
      const restConfig: ModelCrudRestApiConfig = restConfigurations.default[i] as ModelCrudRestApiConfig;
      const repoBinding = this.repositoryBindingRegistry.get(restConfig.repository);

	  if (!restConfig.hasOwnProperty('skipGuardCheckOnAllRequests') && !restConfig.hasOwnProperty('guardStrategy')) {

		throw new Error(`Missing Guard config on Default CRUD REST controller: ${restConfig.basePath}

			The config *requires* one of the following:
				- skipGuardCheckOnAllRequests: true
					OR
				- guardStrategy: new GuardStrategy<Model>(...)

			If guardStrategy is used:

				All of the following are also required:

					- restrictMutationsWithGuard: true/false
					- restrictReadsWithGuard: true/false

				The following can be used in place of / in addition to restrictMutationsWithGuard
				for more granularity:

					- restrictCreatesWithGuard: true/false
					- restrictUpdatesWithGuard: true/false
					- restrictDeletesWithGuard: true/false

		*** CONFIG ERROR *** - ${restConfig.basePath} (Default CRUD REST config)
		`);
	  }


	  if (
		restConfig.hasOwnProperty('guardStrategy') &&
		(
			!restConfig.hasOwnProperty('restrictReadsWithGuard') ||
			(
				!restConfig.hasOwnProperty('restrictMutationsWithGuard') &&
				(
					!restConfig.hasOwnProperty('restrictCreatesWithGuard') ||
					!restConfig.hasOwnProperty('restrictUpdatesWithGuard') ||
					!restConfig.hasOwnProperty('restrictDeletesWithGuard')
				)
			)
		)
	  ) {

		throw new Error(`Missing Guard config on Default CRUD REST controller: ${restConfig.basePath}

			The config *requires* all of the following when guardStrategy is used:

				- restrictMutationsWithGuard: true/false
				- restrictReadsWithGuard: true/false

			The following can be used in place of / in addition to restrictMutationsWithGuard
			for more granularity:
				- restrictCreatesWithGuard: true/false
				- restrictUpdatesWithGuard: true/false
				- restrictDeletesWithGuard: true/false

		*** CONFIG ERROR *** - ${restConfig.basePath} (Default CRUD REST config)
		`);
	  }
	  const idName = 'id';

        //@ts-ignore
        const controller = defineCrudRestController<restConfig.model, 'string', idName>(
          //@ts-ignore
          restConfig.model,
          restConfig as CrudRestControllerOptions
        );
        inject(repoBinding.key)(controller, undefined, 0);

        this.controller(controller);
    }
  }
}
