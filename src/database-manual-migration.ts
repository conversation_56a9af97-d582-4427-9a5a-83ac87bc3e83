

export class DatabaseManualMigration {

	public async executeDropConstraints(datasource: any) {
		await this.executeOnboardingDropConstraint(datasource);
		await this.executeRuiDropConstraint(datasource);
		await this.executeLoyaltyCurrencyBalanceDropConstraint(datasource);
		await this.executeFeatureSettingNameOrgDropConstraint(datasource);
	}

	public async executeManualMigrations(datasource: any) {
		await this.executeOnboardingStateAddConstraint(datasource);
		await this.executeRuiAddConstraint(datasource);
		await this.executeLoyaltyCurrencyBalanceConstraint(datasource);
		await this.executeRuiCreatedDateDefaultValue(datasource);
		await this.executeFeatureSettingNameOrgConstraint(datasource);
	}

	private async executeOnboardingDropConstraint(datasource: any) {
		const dropConstraintScript = `
			ALTER TABLE "onboardingstate"
			DROP CONSTRAINT "unique_orgid_taskid"
		`;

		await this.executeScript(datasource, dropConstraintScript);
		console.log('Unique onboarding state constraint dropped successfully.');
	}

	private async executeRuiDropConstraint(datasource: any) {
		const dropRalUserIdentityConstraint = `
			ALTER TABLE "raleonuseridentity"
			DROP CONSTRAINT "unique_orgid_identitytype_identityvalue"
		`;

		await this.executeScript(datasource, dropRalUserIdentityConstraint);
		console.log('Unique raleon user identity constraint dropped successfully.');
	}

	private async executeLoyaltyCurrencyBalanceDropConstraint(datasource: any) {
		const loyaltyCurrencyBalanceConstraint = `
			ALTER TABLE "loyaltycurrencybalance"
			DROP CONSTRAINT "unique_loyaltycurrencyid_raleonuserid"
		`;

		await this.executeScript(datasource, loyaltyCurrencyBalanceConstraint);
		console.log('Unique loyalty currency balance constraint has been dropped successfully.');
	}

	private async executeFeatureSettingNameOrgDropConstraint(datasource: any) {
		const featureSettingUniqueNameConstraint = `
			ALTER TABLE "featuresetting"
			DROP CONSTRAINT "unique_name_orgid"
		`;

		await this.executeScript(datasource, featureSettingUniqueNameConstraint);
		console.log('Unique feature setting key org constraint added successfully.');
	}

	private async executeOnboardingStateAddConstraint(datasource: any) {
		const constraintScript = `
			ALTER TABLE "onboardingstate"
			ADD CONSTRAINT "unique_orgid_taskid"
			UNIQUE ("orgid", "taskid");
		`;

		await this.executeScript(datasource, constraintScript);
		console.log('Unique onboarding state constraint added successfully.');
	}

	private async executeRuiAddConstraint(datasource: any) {
		const raleonUserIdentityConstraint = `
			ALTER TABLE "raleonuseridentity"
			ADD CONSTRAINT "unique_orgid_identitytype_identityvalue"
			UNIQUE ("orgid", "identitytype", "identityvalue");
		`;

		await this.executeScript(datasource, raleonUserIdentityConstraint);
		console.log('Unique raleon user identity constraint added successfully.');
	}

	private async executeLoyaltyCurrencyBalanceConstraint(datasource: any) {
		const loyaltyCurrencyBalanceConstraint = `
			ALTER TABLE "loyaltycurrencybalance"
			ADD CONSTRAINT "unique_loyaltycurrencyid_raleonuserid"
			UNIQUE ("loyaltycurrencyid", "raleonuserid");
		`;

		await this.executeScript(datasource, loyaltyCurrencyBalanceConstraint);
		console.log('Unique loyalty currency balance constraint added successfully.');

	}

	private async executeRuiCreatedDateDefaultValue(datasource: any) {
		const setCreatedDateDefault = `
			ALTER TABLE "raleonuseridentity"
			ALTER COLUMN "createddate" SET DEFAULT NOW();
		`;

		await this.executeScript(datasource, setCreatedDateDefault);
		console.log('RUI createdDate default value set successfully.');
	}

	private async executeFeatureSettingNameOrgConstraint(datasource: any) {
		const featureSettingUniqueNameConstraint = `
			ALTER TABLE "featuresetting"
			ADD CONSTRAINT "unique_name_orgid"
			UNIQUE ("name", "organizationid");
		`;

		await this.executeScript(datasource, featureSettingUniqueNameConstraint);
		console.log('Unique feature setting key org constraint added successfully.');
	}

	private async executeScript(datasource: any, script: string) {
		try {
			await new Promise((r, reject) => datasource.connector?.execute!(
				script,
				undefined,
				undefined,
				(err: any, result: any) => {
					if (err) { return reject(err); }
					r(result);
				}
			));
		} catch (error) {
			console.log(`Error executing migration script: ${error}`);
		}
	}
}
