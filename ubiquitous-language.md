# Raleon Web App - Ubiquitous Language

This document defines the common terminology and concepts used throughout the Raleon Web App. It serves as a reference for developers, stakeholders, and users to ensure consistent understanding of domain concepts.

## Core Concepts

### Organization

An **Organization** represents a business entity using the Raleon platform. Organizations have users, projects, and various settings that configure their experience.

- **Organization Settings**: Configuration options for an organization, including feature flags and preferences.
- **Organization Plan**: Subscription plan details for an organization, including pricing and features.
- **Organization Keys**: API keys and other security credentials associated with an organization.

### User Management

- **User**: A person with access to the Raleon platform, associated with an Organization.
- **User Credentials**: Authentication information for a User.
- **Roles**: Permissions assigned to Users that determine their access levels.

### Customer Data

- **Raleon User**: A customer or end-user of an Organization's services.
- **Raleon User Identity**: Specific identification method for a Raleon User (e.g., email, wallet address).
- **Raleon User Identity Attributes**: Additional properties associated with a user identity.
- **User Identity**: Alternative representation of user identity information.

### Projects and Data

- **Project**: A container for related data and functionality within an Organization.
- **Data Connections**: Integration points with external data sources.
- **Segment**: A group of users defined by specific criteria for targeting.
- **Segment Download**: Exported data from a segment.

### Marketing and Campaigns

- **Campaign**: A marketing initiative targeting specific users with defined goals.
- **Campaign Segment**: Association between a Campaign and a Segment.
- **Attribution Campaign**: Campaign with tracking for conversion attribution.
- **Conversion Event**: An action taken by a user that represents a successful campaign outcome.
- **Campaign Metric**: Performance measurements for campaigns.

### Planner System

- **Organization Planner Plan**: A marketing plan for an organization with multiple campaigns.
- **Planner Plan Version**: A specific iteration of a marketing plan.
- **Planner Campaign**: A planned marketing campaign within a plan.
- **Plan Campaign Content**: Content associated with a planned campaign.
- **Planner Campaign Image**: Images associated with a planned campaign.

### Task Management

- **Task**: A unit of work to be completed, often associated with a campaign.
- **Task Step**: A specific action within a task.
- **Task Type**: Classification of tasks by their purpose and requirements.
- **Async Job**: Background process for long-running operations.

### Email and Content

- **Email Generation**: Process of creating email content for campaigns.
- **Email Template**: Structure and design for email communications.
- **Content**: Reusable messaging and media for campaigns and communications.
- **Unlayer Component**: Building blocks for email design using the Unlayer editor.
- **Plan Campaign Content**: Content specifically for planned campaigns.

### Chat and Conversation

- **Conversation**: A series of messages between a user and the AI assistant.
- **Message**: Individual communication within a conversation.
- **Message Role**: Classification of message sender (user, assistant, system).
- **Message Status**: State of a message (streaming, completed, failed).
- **Prompt Template**: Pre-defined structure for AI prompts.
- **Prompt Log**: Record of prompts sent to AI systems.

### Loyalty Program

- **Loyalty Program**: A system for rewarding customer engagement and purchases.
- **Loyalty Currency**: Points or credits earned and spent within a loyalty program.
- **Loyalty Currency Balance**: Amount of loyalty currency held by a user.
- **Loyalty Currency Transaction Log**: Record of loyalty currency transactions.
- **Loyalty Campaign**: Marketing initiative specifically for loyalty program members.
- **Loyalty Earn**: Definition of how users can earn loyalty currency.
- **Earn Condition**: Criteria that must be met to earn loyalty currency.
- **Earn Effect**: Result of meeting earn conditions (e.g., points awarded).
- **Loyalty Reward Definition**: Specification of rewards available in a loyalty program.
- **Loyalty Reward Log**: Record of rewards granted to users.
- **Loyalty Redemption Shop Item**: Item available for purchase with loyalty currency.
- **VIP Tier**: Status level within a loyalty program based on customer activity.

### Rewards and Incentives

- **Reward**: Benefit offered to users for specific actions or achievements.
- **Reward Coupon**: Discount or offer code provided as a reward.
- **Inventory Coupon**: Limited-quantity coupon codes available for distribution.
- **Customer Offer**: Promotion targeted to specific customers.
- **Offer**: General promotion available to users.

### Media and Assets

- **Image**: Visual asset used in campaigns and content.
- **Planner Campaign Image**: Images specifically associated with planned campaigns.

## Status and State Terminology

### Campaign Statuses

- **Campaign Ready**: Initial state when a campaign is ready to be worked on.
- **Ready for Copywriting**: Campaign is ready for content creation.
- **In Copywriting**: Content is being created for the campaign.
- **Ready for Design**: Content is complete and ready for visual design.
- **In Design**: Visual elements are being created or arranged.
- **Quality Check**: Campaign is being reviewed for quality.
- **Ready for Review**: Campaign is complete and awaiting final approval.
- **In Review**: Campaign is being reviewed by stakeholders.
- **Approved**: Campaign has been approved for execution.
- **Done/Complete**: Campaign has been executed and is finished.

### Generation Statuses

- **Processing**: Content or plan is being generated.
- **Completed**: Generation process has finished successfully.
- **Failed**: Generation process encountered an error.

## Technical Concepts

- **Prompt**: Input provided to AI systems to generate content.
- **LLM System**: AI language model system used for content generation.
- **LLM Provider**: Service provider for AI language models.
- **LLM Model**: Specific AI model used for content generation.
- **Tool Calls**: Functions executed by AI systems during content generation.
- **Tool Results**: Output from functions executed by AI systems.
