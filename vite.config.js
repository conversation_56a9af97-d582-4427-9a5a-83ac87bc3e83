import vue from '@vitejs/plugin-vue';
import { defineConfig } from 'vite';

// https://vitejs.dev/config/
export default defineConfig({
	define: {
		'process.env': process.env
	},
	plugins: [vue()],
	root: "./public/",
	test: {
		globals: true,
		environment: 'happy-dom',
		include: ['**/tests/**/*.spec.[jt]s?(x)']
	},
	resolve: {
		alias: [
			{
				find: /^~.+/,
				replacement: (val) => {
					return val.replace(/^~/, "");
				},
			},
		],
	},
	build: {
		commonjsOptions: {
			transformMixedEsModules: true,
		}
	},
	server: {
		watch: {
			usePolling: true,
			interval: 100
		},
		port: 3030
	}
})
