version: 0.2

phases:
  install:
    runtime-versions:
      nodejs: latest
    commands:
      - echo Installing
      - docker login -u $DOCKER_USERNAME -p $DOCKER_PASSWORD
  pre_build:
    commands:
      - echo Pre-Build
      - aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com
      # - yum install -y xorg-x11-server-Xvfb gtk2-devel gtk3-devel libnotify-devel GConf2 nss libXScrnSaver alsa-lib #dependencies for cypress
      - npm i
  build:
    commands:
      - echo Building webapp
      - echo "Are we in a dev environment? $IS_DEV"
      - npm run build
      - |
        if [ "$IS_DEV" = "true" ]; then
          export DATABASE_NAME=raleon-test
          echo "Running dev migrations with DATABASE_NAME set to $DATABASE_NAME"
          npm run migrate -- --skip
        else
          export DATABASE_NAME=raleon
          echo "Running prod migrations with DATABASE_NAME set to $DATABASE_NAME"
          npm run migrate -- --skip
        fi
      - echo Build Docker
      - docker build -t $IMAGE_REPO_NAME:$IMAGE_TAG .
      - docker tag $IMAGE_REPO_NAME:$IMAGE_TAG $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$IMAGE_REPO_NAME:$IMAGE_TAG
  post_build:
    commands:
      - echo Build completed on `date`
      - docker push $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$IMAGE_REPO_NAME:$IMAGE_TAG
artifacts:
  files:
    - '**/*'
