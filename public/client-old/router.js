import { createRouter, createWebHistory } from 'vue-router';



const routerHistory = createWebHistory()

const router = createRouter({
	history: routerHistory,
	routes: [

	]
});

const unauthenticatedRoutes = [
	'/signin',
	'/signup',
	'/invite',
	'/reset-password',
	'/reset-password-confirm',
	'/utility/oauth-redirect',
	'/set-cookie',
];

router.beforeEach(async (to, from, next) => {
	if (!unauthenticatedRoutes.includes(to.path)) {
		const isLoggedIn = !!localStorage.getItem('token');
		if (!isLoggedIn && (to.path !== '/' && to.path !== '/signin' && to.path !== '/docsignin')) {
			console.log("not logged in")
			next('/');
			return;
		}
	}
	next();
});



export default router;
