import resolveConfig from 'tailwindcss/resolveConfig';
import { marked } from 'marked';
import { getProjectsByOrgId } from '../services/project';

export const tailwindConfig = () => {
	// Tailwind config
	return resolveConfig('./client/css/tailwind.config.js')
}

// change approach for v1 here when we need to route requests to a v2 endpoint
// we can remove v1 and use the version number in the client side api requests
export const URL_DOMAIN = `${window.location.origin.includes('3030') ? 'http://localhost:3000' : window.location.origin}/api/v1`;

export const GOOGLE_CLIENT_ID = window.location.origin.includes('localhost') || window.location.origin.includes('3030') || window.location.origin.includes('dev.raleon.io')
  ? '606701202896-gu4rr25m8907orue6kiuls1qp2lqah2b.apps.googleusercontent.com' // Add your dev Google Client ID here
  : '606701202896-votutn1d07dt697u0ndf2ln3bdraed9e.apps.googleusercontent.com'; // Add your production Google Client ID here

export const REVENUE_RANGES = [
  { value: 0, revenueUsdMin: 0, revenueUsdMax: 250000, label: '$0 - $250K' },
  { value: 250000, revenueUsdMin: 250000, revenueUsdMax: 500000, label: '$250K - $500K' },
  { value: 500000, revenueUsdMin: 500000, revenueUsdMax: 1000000, label: '$500K - $1M' },
  { value: 1000000, revenueUsdMin: 1000000, revenueUsdMax: 2500000, label: '$1M - $2.5M' },
  { value: 2500000, revenueUsdMin: 2500000, revenueUsdMax: 5000000, label: '$2.5M - $5M' },
  { value: 5000000, revenueUsdMin: 5000000, revenueUsdMax: 7500000, label: '$5M - $7.5M' },
  { value: 7500000, revenueUsdMin: 7500000, revenueUsdMax: 10000000, label: '$7.5M - $10M' },
  { value: 10000000, revenueUsdMin: 10000000, revenueUsdMax: 15000000, label: '$10M - $15M' },
  { value: 15000000, revenueUsdMin: 15000000, revenueUsdMax: 100000000, label: '$15M+' },
];

export function uuidv4() {
	return ([1e7] + -1e3 + -4e3 + -8e3 + -1e11).replace(/[018]/g, c =>
		(c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16)
	);
}

export function cleanMarkdown(markdown) {
        const decodeBriefText = markdown
                .replace(/\\n/g, '\n')
                .replace(/\\"/g, '"');
        return decodeBriefText;
}

export function markdownToPlainText(markdown) {
        const html = marked.parse(markdown || '', { breaks: true, gfm: true });
        const temp = document.createElement('div');
        temp.innerHTML = html;
        const text = temp.textContent || temp.innerText || '';
        return text.replace(/\n{3,}/g, '\n\n').trim();
}


export async function loadProjects() {
	const projects = await getProjectsByOrgId(
		localStorage.getItem('userOrgId'),
	) || 1;

	if (projects && projects.length) {
		return projects.map(project => {
			return {
				id: project.uuid,
				name: project.name
			}
		});
	}
}

export const DEFAULT_SEGMENT_END_DAYS = 380;

export const hexToRGB = (h) => {
	let r = 0;
	let g = 0;
	let b = 0;
	if (h.length === 4) {
		r = `0x${h[1]}${h[1]}`;
		g = `0x${h[2]}${h[2]}`;
		b = `0x${h[3]}${h[3]}`;
	} else if (h.length === 7) {
		r = `0x${h[1]}${h[2]}`;
		g = `0x${h[3]}${h[4]}`;
		b = `0x${h[5]}${h[6]}`;
	}
	return `${+r},${+g},${+b}`;
};

export function formatNumberWithCommas(num) {
	if (isNaN(num)) return num;
	const fixed = Number(num).toFixed(2);
	return fixed.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}


export const formatValue = (value) => Intl.NumberFormat('en-US', {
	style: 'currency',
	currency: 'USD',
	maximumSignificantDigits: 3,
	notation: 'compact',
}).format(value);

export const formatNumber = (value) => Intl.NumberFormat('en-US', {
	maximumSignificantDigits: 3,
	notation: 'compact',
}).format(value);

export const formatThousands = (value) => Intl.NumberFormat('en-US', {
	maximumSignificantDigits: 3,
	notation: 'compact',
}).format(value);

export function delay(ms) {
	return new Promise(resolve => setTimeout(resolve, ms));
}

export function isEmpty(value) {
	const valueString = JSON.stringify(value);
	return valueString == '{}' || valueString == '[]' || valueString == '' || valueString == 'null' || valueString == undefined;
}

export function abbrevAmount(total) {
	total = parseInt(total);
	if (total == null || total == undefined)
		return 0;

	total = total.toFixed((total - Math.floor(total)) > 0 ? 2 : 0);
	var newVal = 0;
	if (total >= 1000 && total < 1000000) {
		newVal = (total / 1000).toFixed(1) + 'K';
	}
	else if (total >= 1000000 && total < 1000000000) {
		newVal = (total / 1000000).toFixed(1) + 'M';
	}
	else if (total >= 1000000000 && total < 1000000000000) {
		newVal = (total / 1000000000).toFixed(1) + 'B';
	}
	else { newVal = total; }

	if (newVal.toString().split('.')[1] == '0') {
		return parseInt(newVal);
	}
	else {
		//console.log("did not find 0")
		return newVal;
	}
}

//var current= new Date(2012, 04, 24, 12, 30, 30, 30);
//alert(timeDifference(current, new Date(2010, 03, 24, 12, 00, 00, 00)));
export function timeDifference(current, previous) {

	var msPerMinute = 60 * 1000;
	var msPerHour = msPerMinute * 60;
	var msPerDay = msPerHour * 24;
	var msPerMonth = msPerDay * 30;
	var msPerYear = msPerDay * 365;

	var fullDate = (previous.getMonth() + 1) + "/" + previous.getDate() + "/" + previous.getFullYear();

	var elapsed = current - previous;

	console.log("elasped time is " + elapsed)

	if (elapsed < msPerMinute) {
		var sec = Math.floor(elapsed / 1000);
		if (sec <= 1)
			return sec + ' second ago';
		else
			return sec + ' seconds ago';
	}

	else if (elapsed < msPerHour) {
		var min = Math.floor(elapsed / msPerMinute);
		if (min <= 1)
			return min + ' minute ago';
		else
			return min + ' minutes ago';
	}

	else if (elapsed < msPerDay) {
		var hr = Math.floor(elapsed / msPerHour);
		if (hr <= 1)
			return hr + ' hour ago';
		else
			return hr + ' hours ago';
	}

	else if (elapsed < msPerMonth) {
		var day = Math.floor(elapsed / msPerDay);
		if (day <= 1)
			return day + ' day ago';
		else
			return day + ' days ago';
	}

	else if (elapsed < msPerYear) {
		var month = Math.floor(elapsed / msPerMonth);
		if (month <= 1)
			return month + ' month ago';
		else
			return fullDate;
	}

	else {
		//var year = Math.floor(elapsed/msPerYear);
		//return year + ' years ago';
		return fullDate;
	}
}

export function formatDate(date) {
	date = new Date(date);
	return `${date.getMonth() + 1}/${date.getDate()}/${date.getFullYear()}`;
}

//Build array from string due to athena. Taking "[Value, Value2, Value3]" and making it an array
export function arrayBuilder(str) {
	var newStr = str.replace(/\s/g, '');

	if (newStr[0] == "[")
		newStr = newStr.slice(1, (newStr.length - 1))

	return newStr.split(',')
}

export function buildDate(d) {
	var dString = ""
	if (d.getMonth() + 1 < 10)
		dString = "0" + (d.getMonth() + 1)
	else
		dString = (d.getMonth() + 1)

	if (d.getDate() < 10)
		dString = dString + "-0" + d.getDate()
	else
		dString = dString + "-" + d.getDate()


	return dString + "-" + d.getFullYear()
}

export function debounce(fn, wait) {
	let timer;
	return function (...args) {
		if (timer) {
			clearTimeout(timer); // clear any pre-existing timer
		}
		const context = this; // get the current context
		timer = setTimeout(() => {
			fn.apply(context, args); // call the function if time expires
		}, wait);
	}
}

export function copyTextToClipboard(text) {
	// Create a temporary textarea element to hold the text
	const textarea = document.createElement('textarea')
	textarea.value = text

	// Append the textarea to the document body
	document.body.appendChild(textarea)

	// Select the text in the textarea
	textarea.select()

	// Copy the text to the clipboard
	document.execCommand('copy')

	// Remove the temporary textarea from the document body
	document.body.removeChild(textarea)
}

export function getWidgetSizeFromNumber(size) {
	if (size == 3)
		return 'SMALL';
	else if (size == 6)
		return 'MEDIUM';
	else if (size == 9)
		return 'LARGE';
	else if (size == 12)
		return 'FULL';
	else
		return 'MEDIUM';
}

export function getWidgetSize(size) {
	if(typeof size === 'number')
		return size;

	size = size.toUpperCase()
	if (size == 'SMALL')
		return 3;
	else if (size == 'MEDIUM')
		return 6;
	else if (size == 'LARGE')
		return 9;
	else if (size == 'FULL')
		return 12;
	else
		return 6;
}

//Make this more like a cached function
export async function getProjectList(orgId) {
	console.log("Fired project list ")
	if (typeof projectList == 'undefined') {
		console.log("We're getting results")
		const projResult = await getProjectsByOrgId(orgId)
		console.log("Project results were " + JSON.stringify(projResult))
		const projectList = []
		var i = 0
		for (let proj of projResult) {
			var p = {
				id: i,
				name: proj.name
			}
			projectList.push(p)
			i++
		}
		return projectList
	} else {
		return projectList
	}
}

//treat datasource as enum
// Plan ID constants for better readability and maintenance
export const PLAN_IDS = {
	// Legacy plans (1-12)
	LEGACY_START: 1,
	LEGACY_END: 12,

	// Strategist plans (13-17)
	STRATEGIST_LITE: 13,
	STRATEGIST: 14,
	STRATEGIST_MAX: 15,
	AGENCY_PLATFORM: 16,
	EXTRA_AGENCY_BRAND: 17,

	// Plan ranges
	STRATEGIST_START: 13,
	STRATEGIST_END: 17
};

export const dashboardTemplates = [
	{
		id: 1,
		title: 'Project Overview',
		description: 'Start with a dashboard of 11 charts that give you a high level view of the health of your project such as new, active, at risk, and lost users, along with common tokens.',
		info: '',
		image: 'template_projectoverview',
		dataSource: 'project',
		template: JSON.stringify([{"name":"New Users L7","type":"NUMBER","size":"SMALL","label":"New Users L7","data":"{\"apiMethod\":\"new-users\",\"inputs\":[\"seven_day_total\"],\"datasource\":\"project\"}"},{"name":"Active Users L30","type":"NUMBER","size":"SMALL","label":"Active Users L30","data":"{\"apiMethod\":\"active-users\",\"inputs\":[\"data\"],\"datasource\":\"project\"}"},{"name":"At Risk Users (30-90D)","type":"NUMBER","size":"SMALL","label":"At Risk Users (30-90D)","data":"{\"apiMethod\":\"at-risk-users\",\"inputs\":[\"data\"]}"},{"name":"Lost Users (>90D)","type":"NUMBER","size":"SMALL","label":"Lost Users (>90D)","data":"{\"apiMethod\":\"dormant-users\",\"inputs\":[\"data\"]}"},{"name":"Total Unique Wallets","type":"NUMBER","size":"SMALL","label":"Total Unique Wallets","data":"{\"apiMethod\":\"unique-wallets\",\"inputs\":[\"data\"]}"},{"name":"# of Token Holders (from project)","type":"NUMBER","size":"SMALL","label":"# of Token Holders (from project)","data":"{\"apiMethod\":\"total-token-holders\",\"inputs\":[\"data\"]}"},{"name":"Avg. Transfer Size of Token Holders","type":"NUMBER","size":"SMALL","label":"Avg. Transfer Size of Token Holders","data":"{\"apiMethod\":\"avg-token-transfer\",\"inputs\":[\"data\"]}"},{"name":"Largest Holder of ETH - USD Value","type":"NUMBER","size":"SMALL","label":"Largest Holder of ETH - USD Value","data":"{\"apiMethod\":\"top-eth-holder\",\"inputs\":[\"quote\"],\"datasource\":\"project\"}"},{"name":"New Users L30","type":"LINE","size":"MEDIUM","label":"New Users L30","data":"{\"apiMethod\":\"new-users\",\"inputs\":[\"seven_day_total\"],\"XAxisInputLabel\":\"day\",\"time\":\"Last 90 Days\",\"datasource\":\"project\"}"},{"name":"Lost Users L30","type":"LINE","size":"MEDIUM","label":"Lost Users L30","data":"{\"apiMethod\":\"dormant-users\",\"inputs\":[\"data\"],\"XAxisInputLabel\":\"time\",\"time\":\"Last 30 Days\",\"datasource\":\"project\"}"},{"name":"Common Tokens Across Active Users","type":"BAR HORIZONTAL","size":"MEDIUM","label":"Common Tokens Across Active Users","data":"{\"apiMethod\":\"common-tokens\",\"inputs\":[\"wallet_count\"],\"XAxisInputLabel\":\"contract_ticker\",\"datasource\":\"project\"}"},{"name":"Active Users Last 30 Days","type":"LINE","size":"MEDIUM","label":"Active Users Last 30 Days","data":"{\"apiMethod\":\"active-users\",\"inputs\":[\"data\"],\"XAxisInputLabel\":\"time\",\"time\":\"Last 30 Days\",\"datasource\":\"project\"}"}])
	},
	{
		id: 2,
		title: 'User Source Insights',
		description: 'Start with a dashboard of 4 charts that show you where your new users come from on-chain, where your lost users go, and what categories they are most active in.',
		info: '',
		image: 'template_behaviorinsights',
		dataSource: 'project',
		template: JSON.stringify([{"name":"New User On-Chain Source","type":"BAR HORIZONTAL","size":"MEDIUM","label":"New User On-Chain Source","data":"{\"apiMethod\":\"project-new-user-origin\",\"inputs\":[\"user_count\"],\"XAxisInputLabel\":\"dapp_name\",\"datasource\":\"project\"}"},{"name":"Lost User On-Chain Destination","type":"BAR HORIZONTAL","size":"MEDIUM","label":"Lost User On-Chain Destination","data":"{\"apiMethod\":\"project-dapp-dormant-activities\",\"inputs\":[\"user_count\"],\"XAxisInputLabel\":\"dapp_name\",\"datasource\":\"project\"}"},{"name":"Categories by Active Users","type":"PIE","size":"MEDIUM","label":"Categories by Active Users","data":"{\"apiMethod\":\"project-category-active-activities\",\"inputs\":[\"user_count\"],\"XAxisInputLabel\":\"category_name\",\"datasource\":\"project\"}"},{"name":"Categories by Lost Users","type":"PIE","size":"MEDIUM","label":"Categories by Lost Users","data":"{\"apiMethod\":\"project-category-dormant-activities\",\"inputs\":[\"user_count\"],\"XAxisInputLabel\":\"category_name\",\"datasource\":\"project\"}"}])
	},
	{
		id: 3,
		title: 'Retention Insights',
		description: 'Start with a dashboard of 5 charts that show you active user and retention user details to help inform how to improve retention.',
		info: '',
		image: 'template_retention',
		dataSource: 'project',
		template: JSON.stringify([{"name":"Active Users Last 30 Days","type":"LINE","size":"MEDIUM","label":"Active Users Last 30 Days","data":"{\"apiMethod\":\"active-users\",\"inputs\":[\"data\"],\"XAxisInputLabel\":\"time\",\"time\":\"Last 30 Days\",\"datasource\":\"project\"}"},{"name":"At Risk Users Last 30 Days","type":"LINE","size":"MEDIUM","label":"At Risk Users Last 30 Days","data":"{\"apiMethod\":\"at-risk-users\",\"inputs\":[\"data\"],\"XAxisInputLabel\":\"time\",\"time\":\"Last 30 Days\",\"datasource\":\"project\"}"},{"name":"What dApps At Risk Users Use","type":"BAR HORIZONTAL","size":"MEDIUM","label":"What dApps At Risk Users Use","data":"{\"apiMethod\":\"project-dapp-at-risk-user-activities\",\"inputs\":[\"user_count\"],\"XAxisInputLabel\":\"dapp_name\",\"datasource\":\"project\"}"},{"name":"Active User Personas","type":"PIE","size":"MEDIUM","label":"Active User Personas","data":"{\"apiMethod\":\"persona-active-count\",\"inputs\":[\"count\"],\"XAxisInputLabel\":\"cluster\",\"datasource\":\"project\"}"},{"name":"Returning Users Last 30 Days","type":"LINE","size":"MEDIUM","label":"Returning Users Last 30 Days","data":"{\"apiMethod\":\"returning-users-last-1\",\"inputs\":[\"data\"],\"XAxisInputLabel\":\"time\",\"time\":\"Last 30 Days\",\"datasource\":\"project\"}"},{"name":"Saved From At Risk Last 30 Days","type":"LINE","size":"MEDIUM","label":"Saved From At Risk Last 30 Days","data":"{\"apiMethod\":\"saved-users\",\"inputs\":[\"saved_user_count\"],\"XAxisInputLabel\":\"day\",\"time\":\"Last 30 Days\",\"datasource\":\"project\"}"}])
	},
	{
		id: 4,
		title: 'Wallet Analysis',
		description: 'Start with a dashboard of 8 charts that give you common details about your wallets, like NFTs in common, most used dApps, marketplace activity, and net worth.',
		info: '',
		image: 'template_walletinsights',
		dataSource: 'audience',
		template: JSON.stringify([{"name":"Top Common NFT's Held by Wallet","type":"BAR HORIZONTAL","size":"MEDIUM","label":"Top Common NFT's Held by Wallet","data":"{\"apiMethod\":\"segment-nft-top-sold\",\"inputs\":[\"total_holders\"],\"XAxisInputLabel\":\"contract_name\",\"datasource\":\"segment\"}"},{"name":"Common Tokens Held by Wallet","type":"BAR HORIZONTAL","size":"MEDIUM","label":"Common Tokens Held by Wallet","data":"{\"apiMethod\":\"segment-common-tokens\",\"inputs\":[\"wallet_count\"],\"XAxisInputLabel\":\"contract_ticker\",\"datasource\":\"segment\"}"},{"name":"NFT Bought and Sold","type":"LINE","size":"FULL","label":"NFT Bought and Sold","data":"{\"apiMethod\":\"segment-nft-bought-sold\",\"inputs\":[\"num_bought\",\"num_sold\"],\"XAxisInputLabel\":\"day\",\"time\":\"Last 7 Days\",\"datasource\":\"segment\"}"},{"name":"Net Worth Distribution by Users","type":"PIE","size":"MEDIUM","label":"Net Worth Distribution by Users","data":"{\"apiMethod\":\"segment-net-worth-distribution\",\"inputs\":[\"number_users\"],\"XAxisInputLabel\":\"net_worth_category\",\"datasource\":\"segment\"}"},{"name":"Most Used dApps by Wallet","type":"PIE","size":"MEDIUM","label":"Most Used dApps by Wallet","data":"{\"apiMethod\":\"segment-dapp-activities\",\"inputs\":[\"user_count\"],\"XAxisInputLabel\":\"key\",\"datasource\":\"segment\"}"},{"name":"Avg. Hold Time of NFTs","type":"LINE","size":"MEDIUM","label":"Avg. Hold Time of NFTs","data":"{\"apiMethod\":\"segment-nft-avg-hold-time\",\"inputs\":[\"data\"],\"XAxisInputLabel\":\"time\",\"time\":\"Last 7 Days\",\"datasource\":\"segment\"}"},{"name":"Most Active Categories by Wallet","type":"BAR","size":"MEDIUM","label":"Most Active Categories by Wallet","data":"{\"apiMethod\":\"segment-category-activities\",\"inputs\":[\"user_count\"],\"XAxisInputLabel\":\"key\",\"datasource\":\"segment\"}"},{"name":"Wallet Activity On-Chain","type":"LINE","size":"FULL","label":"Wallet Activity On-Chain","data":"{\"apiMethod\":\"segment-activity-count\",\"inputs\":[\"last_30_count\"],\"XAxisInputLabel\":\"time\",\"time\":\"Last 30 Days\",\"datasource\":\"segment\"}"}])
	},
	{
		id: 5,
		title: 'Potential Users',
		description: 'Start with a dashboard of 4 charts that show details about the users who have come to your dapp/project but haven not become your users yet.',
		info: "Requires the use of audiences that look for connected wallet events or the 'Event Stream Users' wallets in an audience.",
		image: 'template_potentialusers',
		dataSource: 'audience',
		template: JSON.stringify([{"name":"Prospective Users dApp Activity","type":"BAR","size":"MEDIUM","label":"Prospective Users dApp Activity","data":"{\"apiMethod\":\"segment-dapp-activities\",\"inputs\":[\"user_count\"],\"XAxisInputLabel\":\"key\",\"datasource\":\"segment\"}"},{"name":"Active Categories of Prospective Users","type":"PIE","size":"MEDIUM","label":"Active Categories of Prospective Users","data":"{\"apiMethod\":\"segment-category-activities\",\"inputs\":[\"user_count\"],\"XAxisInputLabel\":\"key\",\"datasource\":\"segment\"}"},{"name":"Commonly Held NFT Across Prospects","type":"BAR HORIZONTAL","size":"MEDIUM","label":"Commonly Held NFT Across Prospects","data":"{\"apiMethod\":\"segment-nfts\",\"inputs\":[\"wallet_count\"],\"XAxisInputLabel\":\"contract_name\",\"datasource\":\"segment\"}"},{"name":"Prospects Who Have Visited You But Not Converted","type":"LINE","size":"MEDIUM","label":"Prospects Who Have Visited You But Not Converted","data":"{\"apiMethod\":\"segment-count\",\"inputs\":[\"data\"],\"XAxisInputLabel\":\"time\",\"time\":\"Last 30 Days\",\"datasource\":\"segment\"}"}])
	},
	{
		id: 6,
		title: 'Whale Analysis',
		description: 'Start with a dashboard of 8 charts that show you details specifically designed to give you insights around your crypto whales.',
		info: 'Best used with audiences targeting whales based on Net Worth or NFT/Tokens held',
		image: 'template_whaleanalysis',
		dataSource: 'audience',
		template: JSON.stringify([{"name":"Current # of Crypto Whales","type":"NUMBER","size":"SMALL","label":"Current # of Crypto Whales","data":"{\"apiMethod\":\"segment-count\",\"inputs\":[\"data\"],\"datasource\":\"segment\"}"},{"name":"Est. Net Worth of Crypto Whales","type":"NUMBER","size":"SMALL","label":"Est. Net Worth of Crypto Whales","data":"{\"apiMethod\":\"segment-usd-value\",\"inputs\":[\"data\"],\"datasource\":\"segment\"}"},{"name":"All Time Activity from Whales","type":"NUMBER","size":"SMALL","label":"All Time Activity from Whales","data":"{\"apiMethod\":\"segment-activity-count\",\"inputs\":[\"all_transactions\"],\"datasource\":\"segment\"}"},{"name":"Last 30 Day Activity","type":"NUMBER","size":"SMALL","label":"Last 30 Day Activity","data":"{\"apiMethod\":\"segment-activity-count\",\"inputs\":[\"last_30_count\"],\"datasource\":\"segment\"}"},{"name":"# of Crypto Whales","type":"LINE","size":"MEDIUM","label":"# of Crypto Whales","data":"{\"apiMethod\":\"segment-count\",\"inputs\":[\"data\"],\"XAxisInputLabel\":\"time\",\"time\":\"Last 30 Days\",\"datasource\":\"segment\"}"},{"name":"Top Common Tokens Across Whales by Wallet Count","type":"BAR HORIZONTAL","size":"MEDIUM","label":"Top Common Tokens Across Whales by Wallet Count","data":"{\"apiMethod\":\"segment-common-tokens\",\"inputs\":[\"wallet_count\"],\"XAxisInputLabel\":\"contract_ticker\",\"datasource\":\"segment\"}"},{"name":"Most Commonly Used dApp By User Count","type":"BAR HORIZONTAL","size":"MEDIUM","label":"Most Commonly Used dApp By User Count","data":"{\"apiMethod\":\"segment-dapp-activities\",\"inputs\":[\"user_count\"],\"XAxisInputLabel\":\"key\",\"datasource\":\"segment\"}"},{"name":"Most Used dApp By Activity","type":"BAR HORIZONTAL","size":"MEDIUM","label":"Most Used dApp By Activity","data":"{\"apiMethod\":\"segment-dapp-activities\",\"inputs\":[\"count\"],\"XAxisInputLabel\":\"key\",\"datasource\":\"segment\"}"}])
	},
	{
		id: 7,
		title: 'Shared NFT List',
		description: 'Start with a dashboard of 1 long charts that shows you a long list of NFTs held in common, by wallet count, for all wallets from the data source.',
		image: 'template_nftlist',
		dataSource: 'audience',
		template: JSON.stringify([{"name":"NFTs Held In Common","type":"TABLE","size":"FULL","label":"NFTs Held In Common","data":"{\"apiMethod\":\"segment-nfts\",\"inputs\":[\"contract_name\",\"wallet_count\"],\"datasource\":\"segment\"}"}])
	},
];
