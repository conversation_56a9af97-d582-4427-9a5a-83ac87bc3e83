// Switch element
.form-switch {
    @apply relative select-none;
    width: 44px;

    label {
        @apply block overflow-hidden cursor-pointer h-6 rounded-full;

        > span:first-child {
            @apply absolute block rounded-full;
            width: 20px;
            height: 20px;
            top: 2px;
            left: 2px;
            right: 50%;
            transition: all .15s ease-out;
        }
    }

    input[type="checkbox"] {

        &:checked {

            + label {
                @apply bg-ralsuccess-dark;

                > span:first-child {
                    left: 22px;
                }
            }
        }

        &:disabled {

            + label {
                @apply cursor-not-allowed bg-slate-100 border border-slate-200;

                > span:first-child {
                    @apply bg-slate-400;
                }
            }
        }
    }
}
