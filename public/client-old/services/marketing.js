import * as metrics from '../services/metrics';

class MarketingService {
  constructor(projectid, callback, specificCampaign, finalCallback) {
    this.campaignId = specificCampaign;
    this.currentDayRange = 60;
    this.projectId = projectid;
    this.callback = callback;
    this.finalCallback = finalCallback;
    this.pendingRequests = 0;
    this.currentCampaigns = [];
    this.savedData = []
    this.resetData();
    if (this.campaignId != undefined) {
      // start pulling data for just this campaign
      this.savedData.push({
        id: this.campaignId,
        connections: 0,
        name: this.campaignId,
        conversion: '0%',
        revenue: 0,
        status: 'UNVERIFIED',
        sources: [],
        content: [],
        connectionsByDay: [],
        conversionsByDay: [],
        walletInfo: [],
        impressionsByDay: [],
        newEngagers: 0,
        totalViews: 0,
      });
      this.getConnectionsCountInternal(this.campaignId);
      this.getSourcesInternal(this.campaignId);
      this.getConnectionsByDayInternal(this.campaignId);
      this.getConversionsByDayInternal(this.campaignId);
      this.getWalletListInternal(this.campaignId, this.projectId);
      this.getConversionPercentageInternal(this.campaignId);
      this.getContentInternal(this.campaignId);
      this.getImpressionsByDayInternal(this.campaignId);
      this.getNewEngagers(this.campaignId);
      this.getTotalViews(this.campaignId);
    }
  }

  getCampaignsInternal() {
    let prom = metrics.getUTMCampaigns();
    prom.then(campaigns => {
      this.currentCampaigns = campaigns.body[0].data;
      for (let index = 0; index < this.currentCampaigns.length; index++) {
        this.getConnectionsCountInternal(this.currentCampaigns[index].campaign_id);
        this.getConversionPercentageInternal(this.currentCampaigns[index].campaign_id);
        this.getTotalViews(this.currentCampaigns[index].campaign_id);
        this.savedData.push({
          id: this.currentCampaigns[index].campaign_id,
          connections: 0,
          name: this.currentCampaigns[index].campaign_id,
          conversion: '0%',
          revenue: 0,
          status: 'UNVERIFIED',
          sources: [],
          connectionsByDay: [],
          conversionsByDay: [],
          walletInfo: [],
          newEngagers: 0,
          totalViews: 0,
        });
      }
      this.callback(this.savedData);
    });
  }

  getIndexOfSavedData(campaign_id) {
    for (let index = 0; index < this.savedData.length; index++) {
      if (this.savedData[index].id === campaign_id) {
        return index;
      }
    }
    return -1;
  }

  getConnectionsCountInternal(campaign_id) {
    let prom = metrics.getUTMConnectionCount(campaign_id);

    this.wrapRequest(prom).then(connections => {
      let count = connections.body[0].data;
      this.savedData[this.getIndexOfSavedData(campaign_id)].connections = count;
    });
  }

  getNewEngagers(campaign_id) {
    let prom = metrics.getUTMNewEngagers(campaign_id, this.projectId);
    this.wrapRequest(prom).then(connections => {
      let count = connections.body[0].data;
      this.savedData[this.getIndexOfSavedData(campaign_id)].newEngagers = count;
    });
  }

  getTotalViews(campaign_id) {
    let prom = metrics.getTotalViews(campaign_id);
    prom.then(connections => {
      let count = connections.body[0].data;
      this.savedData[this.getIndexOfSavedData(campaign_id)].totalViews = count;
      this.callback(this.savedData);
    });
  }

  getSourcesInternal(campaign_id) {
    let prom = metrics.getUTMSources(campaign_id, this.projectId);
    this.wrapRequest(prom).then(connections => {
      let sourcesArray = connections.body[0].data;
      this.savedData[this.getIndexOfSavedData(campaign_id)].sources = sourcesArray;
    });
  }

  getConnectionsByDayInternal(campaign_id) {
    let prom = metrics.getUTMConnection(campaign_id, this.currentDayRange);
    this.wrapRequest(prom).then(connections => {
      let sourcesArray = connections.body[0].data;
      this.savedData[this.getIndexOfSavedData(campaign_id)].connectionsByDay = sourcesArray;
    });
  }

  getImpressionsByDayInternal(campaign_id) {
    let prom = metrics.getImpressionsByDay(campaign_id);
    prom.then(connections => {
      let sourcesArray = connections.body[0].data;
      this.savedData[this.getIndexOfSavedData(campaign_id)].impressionsByDay = sourcesArray;
      if (this.callback) {
        this.callback(this.savedData);
      }
    });
  }

  getConversionsByDayInternal(campaign_id) {
    let prom = metrics.getUTMConversions(campaign_id, this.projectId, this.currentDayRange);
    this.wrapRequest(prom).then(connections => {
      let sourcesArray = connections.body[0].data;
      this.savedData[this.getIndexOfSavedData(campaign_id)].conversionsByDay = sourcesArray;
    });
  }

  getWalletListInternal(campaign_id, project_id) {
    let prom = metrics.getUTMWalletList(campaign_id, project_id);
    this.wrapRequest(prom).then(connections => {
      let sourcesArray = connections.body[0].data;
      this.savedData[this.getIndexOfSavedData(campaign_id)].walletInfo = sourcesArray;
    });
  }

  getConversionPercentageInternal(campaign_id) {
    let prom = metrics.getUTMConversionPercent(campaign_id, this.projectId);
    this.wrapRequest(prom).then(connections => {
      const round = (num) => {
        return +(Math.round(num + "e+2")  + "e-2");
      }
      let conversionPercent = round(connections.body[0].data);
      this.savedData[this.getIndexOfSavedData(campaign_id)].conversion = `${conversionPercent}%`;
    });
  }

  getContentInternal(campaign_id) {
    let prom = metrics.getUTMContent(campaign_id);
    prom.then(connections => {
      let contentArray = connections.body[0].data;
      this.savedData[this.getIndexOfSavedData(campaign_id)].content = contentArray;
      if (this.callback) {
        this.callback(this.savedData);
      }
    });
  }

  setCampaign(campaign_id, number_days) {
    this.campaignId = campaign_id;
    this.currentDayRange = number_days;
  }

  getCampaigns() {
    if (this.currentCampaigns.length === 0) {
      this.getCampaignsInternal();
    }
    return this.currentCampaigns;
  }

  getConnectionsCount() {
    return this.totalConnections;
  }

  getConnectionsByDay() {
    return this.currentConnections;
  }

  resetData() {
    this.currentContent = [];
    this.currentSources = [];
    this.currentConnections = [];
    this.connectionsByDay = [];
    this.totalConnections = 0;
  }

  wrapRequest(promise) {
    this.pendingRequestStarted();

    return promise
      .then(response => this.pendingRequestCompleted(response))
      .catch(() => this.pendingRequestCompleted())
  }

  pendingRequestStarted() {
    this.pendingRequests++;
  }

  pendingRequestCompleted(response) {
    this.pendingRequests--;
    if (this.callback) {
      this.callback(this.savedData);
    }
    if (this.pendingRequests === 0 && this.finalCallback) {
      this.finalCallback(this.savedData);
    }

    return response;
  }
}

export { MarketingService };

