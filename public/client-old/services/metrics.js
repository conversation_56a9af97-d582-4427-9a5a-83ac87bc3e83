var testMode = 0;
import Router from '../router';
import * as Utils from '../utils/Utils';

const URL_DOMAIN = Utils.URL_DOMAIN;

export async function getMetricProof(network, address, metricName, dateProcessed, page) {
  let jsonresponse = {};
  try {
    const response = await fetch(`${URL_DOMAIN}/metric/proof?network=${network}&address=${address}&metricName=${metricName}&dateProcessed=${dateProcessed}&page=${page}`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function getMetricProofDownload(network, address, metricName, dateProcessed, page) {
  let jsonresponse = {};
  try {
    const response = await fetch(`${URL_DOMAIN}/metric/proof/download?network=${network}&address=${address}&metricName=${metricName}&dateProcessed=${dateProcessed}`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function getMetric(network, address, metricName) {
  let jsonresponse = {};
  try {
    const response = await fetch(`${URL_DOMAIN}/metric/${network}/${address}/${metricName}?enddate=latest`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function getWalletTokens(address) {
	let jsonresponse = {};
	try {
	  const response = await fetch(`${URL_DOMAIN}/metric/wallet-tokens/ETH/${address.toLowerCase()}`, {
		method: 'GET',
		withCreditentials: true,
		credentials: 'omit',
		headers: {
		  'Authorization': `Bearer ${localStorage.getItem('token')}`,
		  'Access-Control-Allow-Origin': '*',
		  'Content-Type': 'application/json'
		}
	  });
	  jsonresponse = await response.json();
	} catch (err) {
	  console.log("Error: " + JSON.stringify(err));
	  return {
		error: true,
		message: err.message
	  }
	}
	if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
	  Router.push('/');
	}
	return jsonresponse;
  }

export async function getLifetimeTransactions(network, address) {
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/lifetime-transaction/${network}/${address}?enddate=latest`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function getAddressBalance(network, address) {
  if (testMode)
    return { "statusCode": 200, "body": { "total": 120, "address": "******************************************", "tokens": "[DAI, ETH,WETH, HND, SOX, UNI-V2, eMax, UNI-V2, ANDROTTWEILER, QNT, LINK, RLC, CORE, RSR, FANNY, SHKI, DELTA, RBC, coreDAO/LP3, WOOFY, USDT, GTC, CoreDAO, coreDAO/LP1, DoTx, DELTA rLP]", "usd_values": "[87.49928, 61.934734, 13.215977, 11.883923, 4.892171, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]" } }

  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/address-balance/${network}/${address}?enddate=latest`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function getAddressTokenBalances(network, address) {
  if (testMode)
    return { "statusCode": 200, "body": { "address": "******************************************", "tokens": "[DAI, ETH,WETH, HND, SOX, UNI-V2, eMax, UNI-V2, ANDROTTWEILER, QNT, LINK, RLC, CORE, RSR, FANNY, SHKI, DELTA, RBC, coreDAO/LP3, WOOFY, USDT, GTC, CoreDAO, coreDAO/LP1, DoTx, DELTA rLP]", "usd_values": "[87.49928, 61.934734, 13.215977, 11.883923, 4.892171, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]" } }

  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/token-balances/${network}/${address}?enddate=latest`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function getWalletTags(network, address, organization) {
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/tags/${network}/${address}?organization=${organization}`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function getAddressEngagementScore(network, address) {
  return { "statusCode": 200, "body": { "address": "******************************************", "engagement_score": "82.56" } };
  /*
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/token-balances/${network}/${address}`);
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
  }
  return jsonresponse;
  */
}

export async function getAddressPersona(network, address) {
  return { "statusCode": 200, "body": { "address": "******************************************", "personas": ["NFT Collector", "Gaming", "DeFi", "Trader", "Holder", "DAO", "Other"], "persona_scores": [42.35, 22.82, 69.98, 15.05, 55.52, 8.23, 5.02] } };
  /*
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/token-balances/${network}/${address}`);
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
  }
  return jsonresponse;
  */
}

export async function getAddressWhaleWatcher(network, address) {
  return { "statusCode": 200, "body": { "address": "******************************************", "whale_rank": "3" } }
  /*
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/token-balances/${network}/${address}`);
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
  }
  return jsonresponse;
  */
}

export async function getAddressAllTimeActivityByNetwork(network, address) {
  return { "statusCode": 200, "body": { "address": "******************************************", "date_label": ["12-01-2020", "12-02-2020", "12-03-2020"], "datasets": { "ETH": [62.35, 22.82, 29.98], "Gnosis": [31.45, 19.22, 86.02], "Polygon": [55.21, 63.32, 73.98] } } }
  /*
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/token-balances/${network}/${address}`);
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
  }
  return jsonresponse;
  */
}

export async function getAddressAllTimeActivityByDapp(network, address) {
  return { "statusCode": 200, "body": { "address": "******************************************", "date_label": ["12-01-2020", "12-02-2020", "12-03-2020"], "datasets": { "Uniswap": [62.35, 22.82, 29.98], "Opensea": [31.45, 19.22, 86.02], "ENS": [55.21, 63.32, 73.98] } } }
  /*
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/token-balances/${network}/${address}`);
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
  }
  return jsonresponse;
  */
}

export async function getAddressDappStickiness(network, address) {
  return { "statusCode": 200, "body": { "address": "******************************************", "dapps": ["Uniswap", "OpenSea", "Gem", "Hop Protocol", "Lido", "Zora"], "usage": [150, 35, 25, 20, 12, 10], "categories": ["Exchange", "Marketplace", "Social", "DeFi", "DeFi", "Games"] } };
  /*
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/token-balances/${network}/${address}`);
    jsonresponse = await response.json();
  } catch  (err) {
    console.log("Error: " + JSON.stringify(err));
  }
  return jsonresponse;
  */
}

export async function getAddressActiveTimes(network, address) {
  return { "Items": { "date_processed": "2022-05-24T13:47:08.565Z", "data": { "hours": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23], "address": "******************************************", "transaction_counts": [8, 2, 6, 5, 10, 5, 9, 1, 7, 3, 6, 9, 4, 14, 13, 15, 22, 18, 11, 13, 15, 19, 3, 4], "gmt_offset": 2 }, "address_network_metric": "******************************************_ETH_ADDRESS_ACTIVE_TIMES", "address": "******************************************", "metric": "ADDRESS_ACTIVE_TIMES", "data_type": "json" }, "Count": 1, "ScannedCount": 1, "LastEvaluatedKey": { "address_network_metric": "******************************************_ETH_ADDRESS_ACTIVE_TIMES", "date_processed": "2022-05-24T13:47:08.565Z" } }
  /*
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/token-balances/${network}/${address}`);
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
  }
  return jsonresponse;
  */
}

export async function getAddressLastTransaction(network, address) {
  if (testMode)
    return { "statusCode": 200, "body": { "address": "******************************************", "date": "2022-05-31T23:23:13Z" } }

  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/${network}/${address}/${metricName}?enddate=latest`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function getAddressLastInteractionWithYou(network, address) {
  return { "Items": { "date_processed": "2022-05-24T13:47:08.565Z", "data": { "transaction_date": "2022-05-20T12:00:00Z", "address": "******************************************" }, "address_network_metric": "******************************************_ETH_ADDRESS_LAST_TRANSACTION_WITH_YOU", "address": "******************************************", "metric": "ADDRESS_LAST_TRANSACTION_WITH_YOU", "data_type": "json" }, "Count": 1, "ScannedCount": 1, "LastEvaluatedKey": { "address_network_metric": "******************************************_ETH_ADDRESS_LAST_TRANSACTION_WITH_YOU", "date_processed": "2022-05-24T13:47:08.565Z" } }
  /*
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/token-balances/${network}/${address}`);
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
  }
  return jsonresponse;
  */
}

export async function getAddressCustomerSince(network, address) {
  return { "Items": { "date_processed": "2022-05-24T13:47:08.565Z", "data": { "customer_since_date": "03/20/2022", "address": "******************************************" }, "address_network_metric": "******************************************_ETH_ADDRESS_CUSTOMER_SINCE", "address": "******************************************", "metric": "ADDRESS_CUSTOMER_SINCE", "data_type": "json" }, "Count": 1, "ScannedCount": 1, "LastEvaluatedKey": { "address_network_metric": "******************************************_ETH_ADDRESS_CUSTOMER_SINCE", "date_processed": "2022-05-24T13:47:08.565Z" } }
  /*
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/token-balances/${network}/${address}`);
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
  }
  return jsonresponse;
  */
}

export async function getAddressLTV(network, address) {
  return { "Items": { "date_processed": "2022-05-24T13:47:08.565Z", "data": { "ltv": "752", "address": "******************************************" }, "address_network_metric": "******************************************_ETH_ADDRESS_LTV", "address": "******************************************", "metric": "ADDRESS_LTV", "data_type": "json" }, "Count": 1, "ScannedCount": 1, "LastEvaluatedKey": { "address_network_metric": "******************************************_ETH_ADDRESS_LTV", "date_processed": "2022-05-24T13:47:08.565Z" } }
  /*
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/token-balances/${network}/${address}`);
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
  }
  return jsonresponse;
  */
}

export async function getAddressHoldDuration(network, address) {
  return { "Items": { "date_processed": "2022-05-24T13:47:08.565Z", "data": { "tokens": ["USDC", "DAI", "NEAR", "ETH"], "hold_duration_days": [240, 120, 35, 15], "avg_hold": "35 days", "address": "******************************************", "info": "The average amount of time a token is held across all tokens from wallet." }, "address_network_metric": "******************************************_ETH_ADDRESS_LTV", "address": "******************************************", "metric": "ADDRESS_LTV", "data_type": "json" }, "Count": 1, "ScannedCount": 1, "LastEvaluatedKey": { "address_network_metric": "******************************************_ETH_ADDRESS_LTV", "date_processed": "2022-05-24T13:47:08.565Z" } }
  /*
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/token-balances/${network}/${address}`);
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
  }
  return jsonresponse;
  */
}

export async function getAddressTokenOutflow(network, address) {
  return { "Items": { "date_processed": "2022-05-23T18:47:08.381Z", "data": { "tokens": ["ETH", "WETH", "HND", "SOX", "UNI-V2", "UNI-V2", "QNT", "LINK"], "usd_values": [2635.2918, 297.313, 82.34235, 143.987061, 12.673372, 4.943226, 3.55, 6.35], "info": "Indexes are aligned so tokens[0] = usd_values[0]" }, "address_network_metric": "******************************************_ETH_ADDRESS_TOKEN_VALUES", "address": "******************************************", "metric": "ADDRESS_TOKEN_VALUES", "data_type": "json" }, "Count": 1, "ScannedCount": 1, "LastEvaluatedKey": { "address_network_metric": "******************************************_ETH_ADDRESS_TOKEN_VALUES", "date_processed": "2022-05-23T18:47:08.381Z" } }
  /*
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/token-balances/${network}/${address}`);
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
  }
  return jsonresponse;
  */
}

export async function getAddressTokenInflow(network, address) {
  return { "Items": { "date_processed": "2022-05-23T18:47:08.381Z", "data": { "tokens": ["ETH", "WETH", "MATIC", "DAI", "AAVE", "SOL"], "usd_values": [1635.2918, 2297.313, 182.34235, 43.987061, 212.673372, 43.943226], "info": "Indexes are aligned so tokens[0] = usd_values[0]" }, "address_network_metric": "******************************************_ETH_ADDRESS_TOKEN_VALUES", "address": "******************************************", "metric": "ADDRESS_TOKEN_VALUES", "data_type": "json" }, "Count": 1, "ScannedCount": 1, "LastEvaluatedKey": { "address_network_metric": "******************************************_ETH_ADDRESS_TOKEN_VALUES", "date_processed": "2022-05-23T18:47:08.381Z" } }
  /*
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/token-balances/${network}/${address}`);
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
  }
  return jsonresponse;
  */
}

export async function getProjectNewUsers(network, address, startDate, endDate) {

  if (testMode)
    return { "statusCode": 200, "body": { "address": "ebbcbcb1-1cf3-4450-baf5-dee789402ec6", "metric": 125 } }

  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/new-users/${network}/${address}?enddate=${endDate}&startdate=${startDate}`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function getProjectActiveUsers(network, address) {

  if (testMode)
    return { "statusCode": 200, "body": { "address": "ebbcbcb1-1cf3-4450-baf5-dee789402ec6", "metric": 685 } }

  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/active-users/${network}/${address}?enddate=latest`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function getProjectAtRiskUsers(network, address) {

  if (testMode)
    return { "statusCode": 200, "body": { "address": "ebbcbcb1-1cf3-4450-baf5-dee789402ec6", "metric": 300 } }

  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/at-risk-users/${network}/${address}?enddate=latest`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;

}

export async function getProjectDormantUsers(network, address, startDate, endDate) {

  if (testMode)
    return { "statusCode": 200, "body": { "address": "ebbcbcb1-1cf3-4450-baf5-dee789402ec6", "metric": 12350 } }

  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/dormant-users/${network}/${address}?startdate=${startDate}&enddate=${endDate}`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;

}

export async function getProjectActiveUserPercentActivity(network, address) {
  if (testMode)
    return { "statusCode": 200, "body": { "address": "******************************************", "percent": "80.2" } }

  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/active-user-percent-activity/${network}/${address}?enddate=latest`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function getProjectUniqueWallets(network, address) {
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/unique-wallets/${network}/${address}?enddate=latest`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function getProjectActiveUserInteractionCount(network, address) {
  if (testMode)
    return { "statusCode": 200, "body": { "address": "******************************************", "metric": "245" } }

  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/active-user-transaction-count/${network}/${address}?enddate=latest`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function getProjectTotalTokenHolderUSD(network, address) {
  if (testMode)
    return { "statusCode": 200, "body": { "address": "******************************************", "metric": "51261" } }

  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/token-holders-usd-value/${network}/${address}?enddate=latest`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function getProjectHighestValueWalletETH(network, address) {
  if (testMode)
    return { "statusCode": 200, "body": { "address": "******************************************", "metric": "511261" } }

  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/top-eth-holder/${network}/${address}?enddate=latest`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function getProjectMostActiveTime(network, address) {
  if (testMode)
    return { "statusCode": 200, "body": { "address": "******************************************", "metric": "511261" } }

  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/most-active-time/${network}/${address}?enddate=latest`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function getProjectAvgTokenTransfer(network, address) {
  if (testMode)
    return { "statusCode": 200, "body": { "address": "******************************************", "metric": "203" } }

  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/avg-token-transfer/${network}/${address}?enddate=latest`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function getProjectTotalTokenholders(network, address) {
  if (testMode)
    return { "statusCode": 200, "body": { "address": "******************************************", "metric": "203" } }

  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/total-token-holders/${network}/${address}?enddate=latest`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function getProjectInteractionsByType(network, address) {
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/action-by-type/${network}/${address}?enddate=latest`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function getProjectCommonTokensHeld(network, address) {
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/common-tokens/${network}/${address}?enddate=latest`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function getProjectAtRiskActivities(network, address) {
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/activities-at-risk/${network}/${address}?enddate=latest`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function getProjectNewUserActivities(network, address) {
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/activities-new-user/${network}/${address}?enddate=latest`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export function getMetricAPI(api, network, address, startDate, endDate) {
	return `${URL_DOMAIN}/metric/${api}/${network}/${address}?&enddate=${endDate}${startDate ? '&startdate=' + startDate : ''}`;
}

export async function genericMetricCall(api, network, address, startDate, endDate) {
  console.log("genericMetricCall: " + api + " " + network + " " + address + " " + startDate + " " + endDate);
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/${api}/${network}/${address}?&enddate=${endDate}${startDate ? '&startdate=' + startDate : ''}`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      mode: 'cors',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

//UTM Metrics
export async function getUTMCampaigns() {
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/utm/campaigns`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function getUTMConnectionCount(campaign_id) {
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/utm/connections?campaignid=${campaign_id}&eventtype=connected-wallet`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function getUTMWalletConnectionCount(address) {
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/utm/wallet-connections?address=${address}`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function getUtmWalletSources(address) {
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/utm/wallet-sources?address=${address}`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function getUTMConnection(campaign_id, days) {
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/utm/connections?campaignid=${campaign_id}&eventtype=connected-wallet&days=${days}`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function getUTMConversions(campaign_id, project_id, days) {
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/utm/conversions?campaignid=${campaign_id}&projectid=${project_id}&eventtype=connected-wallet&days=${days}`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}


export async function getUTMSources(campaign_id, project_id) {
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/utm/sources?campaignid=${campaign_id}&projectid=${project_id}`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function getUTMContent(campaign_id) {
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/utm/content?campaignid=${campaign_id}`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function getImpressionsByDay(campaign_id) {
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/utm/impressions-by-day?campaignid=${campaign_id}`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function getUTMConversionPercent(campaign_id, project_id) {
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/utm/conversion-percent?campaignid=${campaign_id}&projectid=${project_id}`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function getUTMWalletList(campaign_id, project_id) {
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/utm/wallets?campaignid=${campaign_id}&projectid=${project_id}`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function getWalletEmail(address) {
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/event/${address}`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function getEventsForOrg() {
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/event`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function getUTMNewEngagers(campaign_id, project_id) {
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/utm/new-engagers?campaignid=${campaign_id}&projectid=${project_id}`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function getTotalViews(campaignId) {
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/metric/utm/total-views?campaignId=${campaignId}`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: err.message
    }
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}
