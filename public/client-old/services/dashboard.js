var testMode = 0;

import Router from '../router';
import * as Utils from '../utils/Utils';

export async function getDashboardsByOrgId(orgId) {
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${Utils.URL_DOMAIN}/dashboards?filter[where][organizationId]=${orgId}&filter[where][shared]=1`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function getPersonalDashboardsByOrgId(orgId, ownerId) {
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${Utils.URL_DOMAIN}/dashboards?filter[where][organizationId]=${orgId}&filter[where][ownerId]=${ownerId}&filter[where][shared]=0`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function getDashboardById(dashId) {
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${Utils.URL_DOMAIN}/dashboards/${dashId}`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function getWidgetsByDashboardId(dashId) {
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${Utils.URL_DOMAIN}/dashboards/${dashId}/widgets`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

//Shared Status = 0 or 1
export async function addDashboardForOrg(orgId, dashboardName, dashboardDescription, ownerId, sharedStatus) {
  var data = {
    name: dashboardName,
    description: dashboardDescription,
    organizationId: orgId,
    ownerId: ownerId.toString(),
    shared: sharedStatus
  };

  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${Utils.URL_DOMAIN}/dashboards`, {
      method: "post",
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        "accept": "application/json",
        "Content-Type": "application/json"
      },
      body: JSON.stringify(data),
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function updateDashboardById(dashId, dashboardName, dashboardDescription, privacy) {
  var data = {
    name: dashboardName,
    description: dashboardDescription,
    shared: privacy
  };

  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${Utils.URL_DOMAIN}/dashboards/${dashId}`, {
      method: "PATCH",
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        "accept": "*/*",
        "Content-Type": "application/json"
      },
      body: JSON.stringify(data),
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function deleteDashboardById(id) {
  var data = {};

  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${Utils.URL_DOMAIN}/dashboards/${id}`, {
      method: "delete",
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        "accept": "*/*"
      },
      body: JSON.stringify(data),
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}

export async function getWidgetById(widgetId) {
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${Utils.URL_DOMAIN}/widgets/${widgetId}`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
  }
  if (jsonresponse.error != undefined && jsonresponse.error.statusCode == 401) {
    Router.push('/');
  }
  return jsonresponse;
}
