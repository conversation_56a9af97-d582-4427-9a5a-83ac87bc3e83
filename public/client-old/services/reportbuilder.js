import _ from 'underscore';

export const CHART_TYPE = {
	BAR: 'Bar',
	BAR_HORIZONTAL: 'Bar Horizontal',
	LINE: 'Line',
	PIE: 'Pie',
	DONUT: 'Donut',
	TABLE: 'Table',
	NUMBER: 'Number'
}

export const CHART_SIZE = {
	SMALL: 'Small',
	MEDIUM: 'Medium',
	LARGE: 'Large',
	FULL: 'Full'
}

const BAR_CHART = {
	type: CHART_TYPE.BAR,
	labels_needed: true,
	number_dimension: 1,
	multiple_datasets: true,
	time_supported: false,
	data_needed: ['json'],
	data_requires_number: true,
}

const BAR_CHART_HORIZONTAL = {
	type: CHART_TYPE.BAR_HORIZONTAL,
	labels_needed: true,
	number_dimension: 1,
	multiple_datasets: true,
	time_supported: false,
	data_needed: ['json'],
	data_requires_number: true,
}

const LINE_CHART = {
	type: CHART_TYPE.LINE,
	labels_needed: true,
	number_dimension: 3,
	multiple_datasets: false,
	time_supported: true,
	data_needed: ['json', 'number'],
	data_requires_number: true,
}

const DONUT_CHART = {
	type: CHART_TYPE.DONUT,
	labels_needed: true,
	number_dimension: 1,
	multiple_datasets: false,
	time_supported: false,
	data_needed: ['json'],
	data_requires_number: true,
}

const PIE_CHART = {
	type: CHART_TYPE.PIE,
	labels_needed: true,
	number_dimension: 1,
	multiple_datasets: false,
	time_supported: false,
	data_needed: ['json'],
	data_requires_number: true,
}

const TABLE_CHART = {
	type: CHART_TYPE.TABLE,
	labels_needed: false,
	number_dimension: 2,
	multiple_datasets: false,
	time_supported: false,
	data_needed: ['json'],
	data_requires_number: false,
}

const NUMBER_CHART = {
	type: CHART_TYPE.NUMBER,
	labels_needed: false,
	number_dimension: 1,
	multiple_datasets: false,
	time_supported: false,
	data_needed: ['number'],
	data_requires_number: true,
}

const CHART_LOOKUP = {
	[CHART_TYPE.BAR]: BAR_CHART,
	[CHART_TYPE.BAR_HORIZONTAL]: BAR_CHART_HORIZONTAL,
	[CHART_TYPE.LINE]: LINE_CHART,
	[CHART_TYPE.PIE]: PIE_CHART,
	[CHART_TYPE.DONUT]: DONUT_CHART,
	[CHART_TYPE.TABLE]: TABLE_CHART,
	[CHART_TYPE.NUMBER]: NUMBER_CHART
}

export const CHART_DATA_GROUP_TYPE = {
	DORMANT_USERS: 'Dormant Users',
	ACTIVE_USERS: 'Active Users',
	AT_RISK_USERS: 'At Risk Users',
	NEW_USERS: 'New Users',
	TOKEN_HOLDERS: 'Token Holders',
	RETURNING_USERS: 'Returning Users',
	ALL_USERS: 'All Users',
	SEGMENT: 'Segment',
}


export const CHART_DATA_INSIGHT = {
	COUNT: 'Total',  //dormant-users
	COUNT_NEW_USERS: 'New Users Count',  //new-users
	ACTIVITIES: 'Activities',  //activities-at-risk
	PROJECT_ACTIONS: 'Project Actions',  //project-actions
	PERSONA: 'Persona',   //persona-active-count
	TRANSACTIONS: 'Transactions',  // active-user-transaction-count
	AVG_TOKEN_TRANSFER: 'Average Token Transfers',  //avg-token-transfer
	MOST_ACTIVE_TIME: 'Most Active Time',  //most-active-time
	USER_PERCENT_ACTIVITY: 'User Percent Activity', //'active-user-percent-activity'
	COMMON_TOKENS: 'Common Tokens',  //common-tokens  (for active only)
	NFTS: 'NFTS', //nfts (for all users)
	UNIQUE_WALLETS: 'Unique Wallets',  //unique-wallets  (for active only)
	TOKEN_USD_VALUE: 'Token USD Value',  //token-holders-usd-value
	TOP_ETH_HOLDER: 'Top ETH Holder',  //top-eth-holder
	RETURNING_USERS_LAST_7_DAYS: 'Returning Users Last 7 Days',  //returning-users-last-7
	RETURNING_USERS_LAST_1_DAYS: 'Returning Users Last 1 Day',  //returning-users-last-1
	RECOVERED_USERS_BY_DAY: 'Recovered Users Last 1 Day',  //recoverd-users-by-day
	RECOVERED_USERS: 'Recovered Users Count',  //recovered-users
	SAVED_USERS_BY_DAY: 'Saved Users Count',  //saved-users-by-day
	USD_VALUE: 'USD Value',  //usd-value-all-users
	TOKEN_INFO: 'Token Info',  //token-info
	SEGMENT_COUNT: 'Segment Count',  //segment-count
	SEGMENT_DAPP_ACTIVITIES: 'Segment dApp Activities',  //segment-dapp-activities
	SEGMENT_CATEGORY_ACTIVITIES: 'Segment Category Activities',  //segment-category-activities
	SEGMENT_USD_VALUE: 'Segment USD Value',  //segment-usd-value
	SEGMENT_NFTS: 'Segment NFTs',  //segment-nfts
	SEGMENT_MOST_VALUABLE_TOKENS: 'Segment Most Valuable Tokens',  //segment-most-valuable-tokens
	SEGMENT_COMMON_TOKENS: 'Segment Common Tokens', //segment-common-tokens
	SEGMENT_MODEL_PERSONA: 'Segment Model Persona', //segment-model-persona
	SEGMENT_ACTIVITY_COUNT: 'Segment Activity Count', //segment-activity-count
	SEGMENT_NFT_BOUGHT_SOLD: 'Segment NFT Bought/Sold', //segment-nft-bought-sold
	SEGMENT_NFT_TOP_BOUGHT: 'Segment NFT Top Bought', //segment-top-bought-nft-usd
	SEGMENT_NFT_TOP_SOLD: 'Segment NFT Top Sold', //segment-top-sold-nft-usd
	SEGMENT_NFT_HOLD_TIME: 'Segment NFT Hold Time', //segment-nft-hold-time
	SEGMENT_NET_WORTH_DISTRIBUTION: 'Segment Net Worth Distribution', //segment-net-worth-distribution
	PROJECT_NFT_TOTAL_COUNTS: 'Project NFT Macro Stats', //project-nft-total-counts
	PROJECT_NFT_AVG_HOLD_TIME: 'Project NFT Avg Hold Time', //project-nft-avg-hold-time
	SEGMENT_SMART_CONTRACT_ACTIVITIES: 'Segment Smart Contract Activities', //segment-smart-contract-activities
	CUSTOM_METRIC: 'Custom Metric', //custom-metric
	PROJECT_ACTIVITIES: 'dApp Activities',
	PROJECT_CATEGORIES: 'Category Activities',
	PROJECT_NEW_USER_ORIGIN: 'New User Origin'
}

export const API_MAP = [{
	group: CHART_DATA_GROUP_TYPE.DORMANT_USERS,
	insight: CHART_DATA_INSIGHT.COUNT,
	api: 'dormant-users'
},
{
	group: CHART_DATA_GROUP_TYPE.NEW_USERS,
	insight: CHART_DATA_INSIGHT.PROJECT_NEW_USER_ORIGIN,
	definition: 'PROJECT_NEW_USER_ORIGIN',
	api: 'project-new-user-origin'
},
{
	group: CHART_DATA_GROUP_TYPE.DORMANT_USERS,
	insight: CHART_DATA_INSIGHT.PROJECT_ACTIVITIES,
	definition: 'PROJECT_ACTIVITIES_BY_DORMANT_USER',
	api: 'project-dapp-dormant-activities'
},
{
	group: CHART_DATA_GROUP_TYPE.ACTIVE_USERS,
	insight: CHART_DATA_INSIGHT.PROJECT_ACTIVITIES,
	definition: 'PROJECT_ACTIVITIES_BY_ACTIVE_USER_V2',
	api: 'project-dapp-active-activities'
},
{
	group: CHART_DATA_GROUP_TYPE.NEW_USERS,
	insight: CHART_DATA_INSIGHT.PROJECT_ACTIVITIES,
	definition: 'PROJECT_ACTIVITIES_BY_NEW_USER_V2',
	api: 'project-dapp-new-user-activities'
},
{
	group: CHART_DATA_GROUP_TYPE.AT_RISK_USERS,
	insight: CHART_DATA_INSIGHT.PROJECT_ACTIVITIES,
	definition: 'PROJECT_ACTIVITIES_BY_AT_RISK_V2',
	api: 'project-dapp-at-risk-user-activities'
},
{
	group: CHART_DATA_GROUP_TYPE.DORMANT_USERS,
	insight: CHART_DATA_INSIGHT.PROJECT_CATEGORIES,
	definition: 'PROJECT_CATEGORIES_BY_DORMANT_USER',
	api: 'project-category-dormant-activities'
},{
	group: CHART_DATA_GROUP_TYPE.ACTIVE_USERS,
	insight: CHART_DATA_INSIGHT.PROJECT_CATEGORIES,
	definition: 'PROJECT_CATEGORIES_BY_ACTIVE_USER',
	api: 'project-category-active-activities'
},
{
	group: CHART_DATA_GROUP_TYPE.ALL_USERS,
	insight: CHART_DATA_INSIGHT.CUSTOM_METRIC,
	api: 'custom-metric'
},
{
	group: CHART_DATA_GROUP_TYPE.ALL_USERS,
	insight: CHART_DATA_INSIGHT.RECOVERED_USERS,
	api: 'recovered-users'
},
{
	group: CHART_DATA_GROUP_TYPE.ALL_USERS,
	insight: CHART_DATA_INSIGHT.PROJECT_NFT_TOTAL_COUNTS,
	api: 'project-nft-total-counts'
},
{
	group: CHART_DATA_GROUP_TYPE.ALL_USERS,
	insight: CHART_DATA_INSIGHT.PROJECT_NFT_AVG_HOLD_TIME,
	api: 'project-nft-avg-hold-time'
},
{
	group: CHART_DATA_GROUP_TYPE.SEGMENT,
	insight: CHART_DATA_INSIGHT.SEGMENT_COUNT,
	definition: 'SEGMENT_COUNT',
	api: 'segment-count'
},
{
	group: CHART_DATA_GROUP_TYPE.SEGMENT,
	insight: CHART_DATA_INSIGHT.SEGMENT_NFTS,
	definition: 'SEGMENT_NFTS',
	api: 'segment-nfts'
},
{
	group: CHART_DATA_GROUP_TYPE.SEGMENT,
	insight: CHART_DATA_INSIGHT.SEGMENT_MOST_VALUABLE_TOKENS,
	definition: 'SEGMENT_MOST_VALUABLE_TOKENS',
	api: 'segment-most-valuable-tokens'
},
{
	group: CHART_DATA_GROUP_TYPE.SEGMENT,
	insight: CHART_DATA_INSIGHT.SEGMENT_NET_WORTH_DISTRIBUTION,
	definition: 'SEGMENT_NET_WORTH_DISTRIBUTION',
	api: 'segment-net-worth-distribution'
},
{
	group: CHART_DATA_GROUP_TYPE.SEGMENT,
	insight: CHART_DATA_INSIGHT.SEGMENT_ACTIVITY_COUNT,
	definition: 'SEGMENT_ACTIVITY_COUNT',
	api: 'segment-activity-count'
},
{
	group: CHART_DATA_GROUP_TYPE.SEGMENT,
	insight: CHART_DATA_INSIGHT.SEGMENT_COMMON_TOKENS,
	definition: 'SEGMENT_COMMON_TOKENS',
	api: 'segment-common-tokens'
},
{
	group: CHART_DATA_GROUP_TYPE.SEGMENT,
	insight: CHART_DATA_INSIGHT.SEGMENT_NFT_BOUGHT_SOLD,
	definition: 'SEGMENT_NFT_BOUGHT_SOLD',
	api: 'segment-nft-bought-sold'
},
{
	group: CHART_DATA_GROUP_TYPE.SEGMENT,
	insight: CHART_DATA_INSIGHT.SEGMENT_NFT_TOP_BOUGHT,
	definition: 'SEGMENT_TOP_BOUGHT_NFT_USD',
	api: 'segment-nft-top-bought'
},
{
	group: CHART_DATA_GROUP_TYPE.SEGMENT,
	insight: CHART_DATA_INSIGHT.SEGMENT_NFT_TOP_SOLD,
	definition: 'SEGMENT_TOP_SOLD_NFT_USD',
	api: 'segment-nft-top-sold'
},
{
	group: CHART_DATA_GROUP_TYPE.SEGMENT,
	insight: CHART_DATA_INSIGHT.SEGMENT_NFT_HOLD_TIME,
	definition: 'SEGMENT_NFT_AVG_HOLD_TIME',
	api: 'segment-nft-avg-hold-time'
},
{
	group: CHART_DATA_GROUP_TYPE.SEGMENT,
	insight: CHART_DATA_INSIGHT.SEGMENT_MODEL_PERSONA,
	definition: 'SEGMENT_MODEL_PERSONA',
	api: 'segment-model-persona'
},
{
	group: CHART_DATA_GROUP_TYPE.SEGMENT,
	insight: CHART_DATA_INSIGHT.SEGMENT_USD_VALUE,
	definition: 'SEGMENT_USD_VALUE',
	api: 'segment-usd-value'
},
{
	group: CHART_DATA_GROUP_TYPE.SEGMENT,
	insight: CHART_DATA_INSIGHT.SEGMENT_DAPP_ACTIVITIES,
	api: 'segment-dapp-activities',
	definition: 'SEGMENT_DAPP_ACTIVITIES'
},
{
	group: CHART_DATA_GROUP_TYPE.SEGMENT,
	insight: CHART_DATA_INSIGHT.SEGMENT_SMART_CONTRACT_ACTIVITIES,
	api: 'segment-smart-contract-activities',
	definition: 'SEGMENT_SMART_CONTRACT_ACTIVITIES'
},
{
	group: CHART_DATA_GROUP_TYPE.SEGMENT,
	insight: CHART_DATA_INSIGHT.SEGMENT_CATEGORY_ACTIVITIES,
	api: 'segment-category-activities',
	definition: 'SEGMENT_CATEGORY_ACTIVITIES'
},
{
	group: CHART_DATA_GROUP_TYPE.ALL_USERS,
	insight: CHART_DATA_INSIGHT.RECOVERED_USERS_BY_DAY,
	api: 'recovered-users-last-1'
},
{
	group: CHART_DATA_GROUP_TYPE.TOKEN_HOLDERS,
	insight: CHART_DATA_INSIGHT.TOKEN_INFO,
	api: 'token-info'
},
{
	group: CHART_DATA_GROUP_TYPE.ALL_USERS,
	insight: CHART_DATA_INSIGHT.TOKEN_INFO,
	api: 'token-info'
},
{
	group: CHART_DATA_GROUP_TYPE.ALL_USERS,
	insight: CHART_DATA_INSIGHT.SAVED_USERS_BY_DAY,
	api: 'saved-users'
},
{
	group: CHART_DATA_GROUP_TYPE.RETURNING_USERS,
	insight: CHART_DATA_INSIGHT.RETURNING_USERS_LAST_7_DAYS,
	api: 'returning-users-last-7'
},
{
	group: CHART_DATA_GROUP_TYPE.RETURNING_USERS,
	insight: CHART_DATA_INSIGHT.RETURNING_USERS_LAST_1_DAYS,
	api: 'returning-users-last-1'
},
{
	group: CHART_DATA_GROUP_TYPE.TOKEN_HOLDERS,
	insight: CHART_DATA_INSIGHT.TOKEN_USD_VALUE,
	api: 'token-holders-usd-value'
},
{
	group: CHART_DATA_GROUP_TYPE.ALL_USERS,
	insight: CHART_DATA_INSIGHT.USD_VALUE,
	api: 'usd-value-all-users'
},
{
	group: CHART_DATA_GROUP_TYPE.TOKEN_HOLDERS,
	insight: CHART_DATA_INSIGHT.AVG_TOKEN_TRANSFER,
	api: 'avg-token-transfer'
},
{
	group: CHART_DATA_GROUP_TYPE.TOKEN_HOLDERS,
	insight: CHART_DATA_INSIGHT.TOP_ETH_HOLDER,
	api: 'top-eth-holder'
},
{
	group: CHART_DATA_GROUP_TYPE.ALL_USERS,
	insight: CHART_DATA_INSIGHT.PROJECT_ACTIONS,
	api: 'action-by-type'
},
{
	group: CHART_DATA_GROUP_TYPE.ACTIVE_USERS,
	insight: CHART_DATA_INSIGHT.COUNT,
	api: 'active-users'
},
{
	group: CHART_DATA_GROUP_TYPE.AT_RISK_USERS,
	insight: CHART_DATA_INSIGHT.COUNT,
	api: 'at-risk-users'
},
{
	group: CHART_DATA_GROUP_TYPE.NEW_USERS,
	insight: CHART_DATA_INSIGHT.COUNT_NEW_USERS,
	definition: 'NEW_USERS',
	api: 'new-users'
},
{
	group: CHART_DATA_GROUP_TYPE.DORMANT_USERS,
	insight: CHART_DATA_INSIGHT.ACTIVITIES,
	api: 'activities-dormant'
},
{
	group: CHART_DATA_GROUP_TYPE.ACTIVE_USERS,
	insight: CHART_DATA_INSIGHT.ACTIVITIES,
	api: 'activities-active'
},
{
	group: CHART_DATA_GROUP_TYPE.AT_RISK_USERS,
	insight: CHART_DATA_INSIGHT.ACTIVITIES,
	api: 'activities-at-risk'
},
{
	group: CHART_DATA_GROUP_TYPE.NEW_USERS,
	insight: CHART_DATA_INSIGHT.ACTIVITIES,
	api: 'activities-new-user'
},
{
	group: CHART_DATA_GROUP_TYPE.ACTIVE_USERS,
	insight: CHART_DATA_INSIGHT.PERSONA,
	api: 'persona-active-count'
},
{
	group: CHART_DATA_GROUP_TYPE.ACTIVE_USERS,
	insight: CHART_DATA_INSIGHT.COMMON_TOKENS,
	api: 'common-tokens'
},
{
	group: CHART_DATA_GROUP_TYPE.ALL_USERS,
	insight: CHART_DATA_INSIGHT.NFTS,
	api: 'nfts'
},
{
	group: CHART_DATA_GROUP_TYPE.ALL_USERS,
	insight: CHART_DATA_INSIGHT.COUNT,
	api: 'unique-wallets'
},
{
	group: CHART_DATA_GROUP_TYPE.ALL_USERS,
	insight: CHART_DATA_INSIGHT.UNIQUE_WALLETS,
	api: 'unique-wallets'
},
{
	group: CHART_DATA_GROUP_TYPE.ALL_USERS,
	insight: CHART_DATA_INSIGHT.MOST_ACTIVE_TIME,
	api: 'most-active-time'
},
{
	group: CHART_DATA_GROUP_TYPE.TOKEN_HOLDERS,
	insight: CHART_DATA_INSIGHT.COUNT,
	api: 'total-token-holders'
},
{
	group: CHART_DATA_GROUP_TYPE.ACTIVE_USERS,
	insight: CHART_DATA_INSIGHT.TRANSACTIONS,
	api: 'active-user-transaction-count'
},
{
	group: CHART_DATA_GROUP_TYPE.ACTIVE_USERS,
	insight: CHART_DATA_INSIGHT.USER_PERCENT_ACTIVITY,
	api: 'active-user-percent-activity'
}
]

export function getAPIURL(group, insight) {
	console.log('getAPIURL', group, insight)
	const api = API_MAP.find(api => {
		console.log(api.group, group, api.insight, insight)
		return api.group === group && api.insight === insight
	})
	return api ? api.api : null
}

export const CHART_TIME_RANGE = {
	LAST_24_HOURS: 'Last 24 Hours',
	LAST_7_DAYS: 'Last 7 Days',
	LAST_30_DAYS: 'Last 30 Days',
	LAST_90_DAYS: 'Last 90 Days',
	LAST_365_DAYS: 'Last 365 Days',
	LAST_3_YEARS: 'Last 3 Years',
}

export function getChartDefinition(chartType) {
	return CHART_LOOKUP[chartType]
}

export function getNumberOfDaysFromTimeRange(timeRange) {
	switch (timeRange) {
		case CHART_TIME_RANGE.LAST_24_HOURS:
			return 1
		case CHART_TIME_RANGE.LAST_7_DAYS:
			return 7
		case CHART_TIME_RANGE.LAST_30_DAYS:
			return 30
		case CHART_TIME_RANGE.LAST_90_DAYS:
			return 90
		case CHART_TIME_RANGE.LAST_365_DAYS:
			return 365
		case CHART_TIME_RANGE.LAST_3_YEARS:
			return 365 * 3
		default:
			return 1
	}
}

export function getFriendlyDateName(timeRange) {
	switch (timeRange) {
		case CHART_TIME_RANGE.LAST_24_HOURS:
			return "Date range: In the last 24 hours"
		case CHART_TIME_RANGE.LAST_7_DAYS:
			return "Date range: In the last 7 days"
		case CHART_TIME_RANGE.LAST_30_DAYS:
			return "Date range: In the last 30 days"
		case CHART_TIME_RANGE.LAST_90_DAYS:
			return "Date range: In the last 90 days"
		case CHART_TIME_RANGE.LAST_365_DAYS:
			return "Date range: In the last year"
		case CHART_TIME_RANGE.LAST_3_YEARS:
			return "Date range: In the last 3 years"
		default:
			return ""
	}
}

export function getAllInsightOptions() {
	let insightOptions = []
	for (let insight in CHART_DATA_INSIGHT) {
		insightOptions.push(CHART_DATA_INSIGHT[insight]);
	}

	let finalResults = [];
	var idx = 0;
	for (let insight of insightOptions) {
		finalResults.push({
			id: idx++,
			name: insight
		})
	}

	return finalResults;
}

export function getInsightOptionsByChart(chartTypeId, isProject) {
	let chartTypes = getReportChartTypeList();
	let chartType = chartTypes.find(chartType => chartType.id === chartTypeId).name;
	let chartDefinition = CHART_LOOKUP[chartType];
	let insightOptions = [];
	if (isProject) {
		if (chartDefinition.data_needed.includes('json')) {
			insightOptions.push(CHART_DATA_INSIGHT.ACTIVITIES, CHART_DATA_INSIGHT.PERSONA, CHART_DATA_INSIGHT.ACTIVITIES, CHART_DATA_INSIGHT.COMMON_TOKENS,
				CHART_DATA_INSIGHT.PROJECT_ACTIONS, CHART_DATA_INSIGHT.NFTS, CHART_DATA_INSIGHT.TOKEN_INFO, CHART_DATA_INSIGHT.COUNT_NEW_USERS,
				CHART_DATA_INSIGHT.RECOVERED_USERS, CHART_DATA_INSIGHT.SAVED_USERS_BY_DAY, CHART_DATA_INSIGHT.TOP_ETH_HOLDER, CHART_DATA_INSIGHT.PROJECT_ACTIVITIES,
				CHART_DATA_INSIGHT.PROJECT_CATEGORIES, CHART_DATA_INSIGHT.PROJECT_NEW_USER_ORIGIN)
		}
		if (chartDefinition.data_needed.includes('number')) {
			insightOptions.push(CHART_DATA_INSIGHT.TRANSACTIONS, CHART_DATA_INSIGHT.COUNT, CHART_DATA_INSIGHT.COUNT_NEW_USERS, CHART_DATA_INSIGHT.MOST_ACTIVE_TIME,
				CHART_DATA_INSIGHT.USER_PERCENT_ACTIVITY, CHART_DATA_INSIGHT.TOP_ETH_HOLDER, CHART_DATA_INSIGHT.UNIQUE_WALLETS,
				CHART_DATA_INSIGHT.TOKEN_USD_VALUE, CHART_DATA_INSIGHT.AVG_TOKEN_TRANSFER, CHART_DATA_INSIGHT.RETURNING_USERS_LAST_7_DAYS,
				CHART_DATA_INSIGHT.RETURNING_USERS_LAST_1_DAYS, CHART_DATA_INSIGHT.SAVED_USERS_BY_DAY,
				CHART_DATA_INSIGHT.USD_VALUE, CHART_DATA_INSIGHT.PROJECT_NFT_TOTAL_COUNTS, CHART_DATA_INSIGHT.PROJECT_NFT_AVG_HOLD_TIME,
				CHART_DATA_INSIGHT.RECOVERED_USERS, CHART_DATA_INSIGHT.TOKEN_INFO)
		}
	}
	else {
		if (chartDefinition.data_needed.includes('number')) {
			insightOptions.push(CHART_DATA_INSIGHT.SEGMENT_COUNT,
				CHART_DATA_INSIGHT.SEGMENT_USD_VALUE,
				CHART_DATA_INSIGHT.SEGMENT_ACTIVITY_COUNT,
				CHART_DATA_INSIGHT.SEGMENT_NFT_HOLD_TIME);
		}
		if (chartDefinition.data_needed.includes('json')) {
			insightOptions.push(
				CHART_DATA_INSIGHT.SEGMENT_DAPP_ACTIVITIES,
				CHART_DATA_INSIGHT.SEGMENT_SMART_CONTRACT_ACTIVITIES,
				CHART_DATA_INSIGHT.SEGMENT_CATEGORY_ACTIVITIES,
				CHART_DATA_INSIGHT.SEGMENT_NFTS,
				CHART_DATA_INSIGHT.SEGMENT_COMMON_TOKENS,
				CHART_DATA_INSIGHT.SEGMENT_MODEL_PERSONA,
				CHART_DATA_INSIGHT.SEGMENT_NFT_BOUGHT_SOLD,
				CHART_DATA_INSIGHT.SEGMENT_NFT_TOP_BOUGHT,
				CHART_DATA_INSIGHT.SEGMENT_NFT_TOP_SOLD,
				CHART_DATA_INSIGHT.SEGMENT_MOST_VALUABLE_TOKENS,
				CHART_DATA_INSIGHT.SEGMENT_NET_WORTH_DISTRIBUTION
			);
		}
	}

	insightOptions = _.uniq(insightOptions);
	let tooltips = new Map();
	tooltips.set(CHART_DATA_INSIGHT.ACTIVITIES, '(BEING DEPRECATED) The on-chain activities of the project users');
	tooltips.set(CHART_DATA_INSIGHT.PERSONA, 'Predicted Persona of the project users');
	tooltips.set(CHART_DATA_INSIGHT.COMMON_TOKENS, 'Common tokens of the projects users');
	tooltips.set(CHART_DATA_INSIGHT.NFTS, 'NFTs of the projects users');
	tooltips.set(CHART_DATA_INSIGHT.PROJECT_ACTIONS, 'The actions that users take with the project');
	tooltips.set(CHART_DATA_INSIGHT.TRANSACTIONS, 'The number of transactions that users take with the project');
	tooltips.set(CHART_DATA_INSIGHT.COUNT, 'Total Value. Use this when you want a single value. This can be done over time');
	tooltips.set(CHART_DATA_INSIGHT.COUNT_NEW_USERS, 'The number of new users that joined the project');
	tooltips.set(CHART_DATA_INSIGHT.RECOVERED_USERS, 'The number of users that recovered after going dormant');
	tooltips.set(CHART_DATA_INSIGHT.MOST_ACTIVE_TIME, 'The most active UTC time of the project users');
	tooltips.set(CHART_DATA_INSIGHT.USER_PERCENT_ACTIVITY, 'Percentage of activity your project takes up with the users compared to overall user activity on-chain');
	tooltips.set(CHART_DATA_INSIGHT.TOP_ETH_HOLDER, 'The Top ETH holder of the project');
	tooltips.set(CHART_DATA_INSIGHT.UNIQUE_WALLETS, 'All time unique wallets of the project');
	tooltips.set(CHART_DATA_INSIGHT.TOKEN_USD_VALUE, 'Of token holders specified in your project, the USD value of their wallets');
	tooltips.set(CHART_DATA_INSIGHT.USD_VALUE, 'Total detected USD value of wallets, not all tokens are supported');
	tooltips.set(CHART_DATA_INSIGHT.AVG_TOKEN_TRANSFER, 'Average token transfer size of the project users');
	tooltips.set(CHART_DATA_INSIGHT.RETURNING_USERS_LAST_7_DAYS, 'Number of users that previously interacted with the project and have interacted again in the last 7 days');
	tooltips.set(CHART_DATA_INSIGHT.RETURNING_USERS_LAST_1_DAYS, 'Number of users that previously interacted with the project and have interacted again in the last 24 hours');
	tooltips.set(CHART_DATA_INSIGHT.RECOVERED_USERS_BY_DAY, 'Number of users that were dormant with the project and have interacted again in the last 24 hours');
	tooltips.set(CHART_DATA_INSIGHT.SAVED_USERS_BY_DAY, 'Number of users that were at risk with the project and have interacted again in the last 24 hours');
	tooltips.set(CHART_DATA_INSIGHT.TOKEN_INFO, 'Token information of the project');
	tooltips.set(CHART_DATA_INSIGHT.SEGMENT_COUNT, 'The number of users in the segment');
	tooltips.set(CHART_DATA_INSIGHT.SEGMENT_DAPP_ACTIVITIES, 'The on-chain activities of the segment users per-dApp');
	tooltips.set(CHART_DATA_INSIGHT.SEGMENT_SMART_CONTRACT_ACTIVITIES, 'The on-chain activities of the segment users per-smart contract');
	tooltips.set(CHART_DATA_INSIGHT.SEGMENT_CATEGORY_ACTIVITIES, 'The on-chain activities of the segment users per-category');
	tooltips.set(CHART_DATA_INSIGHT.SEGMENT_USD_VALUE, 'The USD value of the segment users');
	tooltips.set(CHART_DATA_INSIGHT.SEGMENT_NFTS, 'The NFTs of the segment users');
	tooltips.set(CHART_DATA_INSIGHT.SEGMENT_ACTIVITY_COUNT, 'The number of activities of the segment users, multiple options available');
	tooltips.set(CHART_DATA_INSIGHT.SEGMENT_COMMON_TOKENS, 'The most common tokens of the segment users');
	tooltips.set(CHART_DATA_INSIGHT.SEGMENT_MOST_VALUABLE_TOKENS, 'The most valuable tokens of the segment users');
	tooltips.set(CHART_DATA_INSIGHT.SEGMENT_MODEL_PERSONA, 'The number of users with their correlated persona');
	tooltips.set(CHART_DATA_INSIGHT.SEGMENT_NFT_BOUGHT_SOLD, 'The number of users with their NFTs bought and sold');
	tooltips.set(CHART_DATA_INSIGHT.SEGMENT_NFT_TOP_BOUGHT, 'The top NFTs bought by USD value of the segment users');
	tooltips.set(CHART_DATA_INSIGHT.SEGMENT_NFT_TOP_SOLD, 'The top NFTs sold by USD value of the segment users');
	tooltips.set(CHART_DATA_INSIGHT.SEGMENT_NFT_HOLD_TIME, 'The number of users with their NFTs hold time');
	tooltips.set(CHART_DATA_INSIGHT.PROJECT_NFT_TOTAL_COUNTS, 'Includes Macro level stats on NFTs of the project');
	tooltips.set(CHART_DATA_INSIGHT.PROJECT_NFT_AVG_HOLD_TIME, 'Average hold time of NFTs of the project');
	tooltips.set(CHART_DATA_INSIGHT.SEGMENT_NET_WORTH_DISTRIBUTION, 'The net worth distribution of the segment users');
	tooltips.set(CHART_DATA_INSIGHT.PROJECT_ACTIVITIES, 'The on-chain activities of the users per-dApp');
	tooltips.set(CHART_DATA_INSIGHT.PROJECT_CATEGORIES, 'The on-chain activities of the users per-category');

	let finalResults = [];
	var idx = 0;
	for (let insight of insightOptions) {
		let friendly_name = insight;
		//console.log("friendly_name", friendly_name)
		if (friendly_name.includes('Segment ')) {
			friendly_name = friendly_name.replace('Segment ', '');
			//console.log("friendly_name -- after", friendly_name)
		}
		finalResults.push({
			id: idx++,
			name: insight,
			friendlyName: friendly_name,
			tooltip: tooltips.get(insight)
		})
	}

	return finalResults;
}

export function getAPIMapFromAPIName(apiName) {
	return API_MAP.find(api => api.api === apiName)
}

export function getFieldOptionsCustomMetric(schema, chartType, isDataField) {
	//Schema should contain an object called outputs as an array
	let labelOptions = []
	for (var i = 0; i < schema.outputs.length; i++) {
		let output = schema.outputs[i]
		labelOptions.push({
			label: output.name,
			field: output.name,
			type: output.type
		})
	}

	if (!isDataField && chartType === CHART_TYPE.LINE) {
		//if labelOptions doesn't include an object with field 'time' then we need to add it
		if (!labelOptions.find((option) => option.label === 'Time')) {
			labelOptions.push({
				label: 'Time',
				field: 'time',
				type: 'time'
			});
		}
	}

	let finalResults = [];
	let chartDefinition = getChartDefinition(chartType);
	var idx = 0;
	for (let label of labelOptions) {
		if (chartDefinition.data_requires_number && isDataField && label.type !== 'number') continue; // skip non-number fields
		finalResults.push({
			id: idx++,
			name: label.label,
			field: label.field,
		})
	}
	return finalResults;
}

export function getFieldOptions(insightOption, chartType, isDataField) {
	console.log("getField Options", insightOption);
	let labelOptions = []
	switch (insightOption) {
		case CHART_DATA_INSIGHT.COUNT_NEW_USERS:
			labelOptions = [{
				label: 'New User Count',
				field: 'new_users',
				type: 'number'
			}, {
				label: 'New Users Last 7 days',
				field: 'seven_day_total',
				type: 'number'
			}, {
				label: 'Time',
				field: 'day',
				type: 'string'
			}]
			break;
		case CHART_DATA_INSIGHT.RECOVERED_USERS:
			labelOptions = [{
				label: 'Recovered User Count',
				field: 'recovered_user_count',
				type: 'number'
			}, {
				label: 'Recovered Users Last 7 days',
				field: 'seven_day_total',
				type: 'number'
			}, {
				label: 'Time',
				field: 'day',
				type: 'string'
			}]
			break;
		case CHART_DATA_INSIGHT.COUNT:
			labelOptions = [{
				label: 'Count',
				field: 'data',
				type: 'number'
			}]
			break;
		case CHART_DATA_INSIGHT.SEGMENT_COUNT:
			labelOptions = [{
				label: 'Count',
				field: 'data',
				type: 'number'
			}]
			break;
		case CHART_DATA_INSIGHT.SEGMENT_USD_VALUE:
			labelOptions = [{
				label: 'USD Value',
				field: 'data',
				type: 'number'
			}]
			break;
		case CHART_DATA_INSIGHT.TOP_ETH_HOLDER:
			labelOptions = [{
				label: 'Wallet Address',
				field: 'wallet',
				type: 'string'
			}, {
				label: 'Quote',
				field: 'quote',
				type: 'number'
			}, {
				label: 'Ethereum Balance',
				field: 'eth_balance',
				type: 'number'
			}];
			break;
		case CHART_DATA_INSIGHT.PROJECT_NEW_USER_ORIGIN:
			labelOptions = [{
				label: 'Time',
				field: 'day',
				type: 'string'
			},
			{
				label: 'Activity Count',
				field: 'activity_count',
				type: 'number'
			},
			{
				label: 'DApp Name',
				field: 'dapp_name',
				type: 'string'
			},
			{
				label: 'User Count',
				field: 'user_count',
				type: 'number'
			},
			{
				label: 'Contracts Detected',
				field: 'contract_count',
				type: 'number'
			}];
			break;
		case CHART_DATA_INSIGHT.PROJECT_ACTIVITIES:
			labelOptions = [{
				label: 'Time',
				field: 'day',
				type: 'string'
			},
			{
				label: 'Activity Count',
				field: 'activity_count',
				type: 'number'
			},
			{
				label: 'DApp Name',
				field: 'dapp_name',
				type: 'string'
			},
			{
				label: 'User Count',
				field: 'user_count',
				type: 'number'
			},
			{
				label: 'Contracts Detected',
				field: 'contract_count',
				type: 'number'
			}];
			break;
		case CHART_DATA_INSIGHT.PROJECT_CATEGORIES:
			labelOptions = [{
				label: 'Time',
				field: 'day',
				type: 'string'
			},
			{
				label: 'Activity Count',
				field: 'activity_count',
				type: 'number'
			},
			{
				label: 'Category Name',
				field: 'category_name',
				type: 'string'
			},
			{
				label: 'User Count',
				field: 'user_count',
				type: 'number'
			},
			{
				label: 'Contracts Detected',
				field: 'contract_count',
				type: 'number'
			}];
			break;
		case CHART_DATA_INSIGHT.ACTIVITIES:
			labelOptions = [{
				label: 'Activity Count',
				field: 'count',
				type: 'number'
			}, {
				label: 'Category',
				field: 'category',
				type: 'string'
			}, {
				label: 'DApp Name',
				field: 'key',
				type: 'string'
			}, {
				label: 'DApp Label - Will show address if not labeled',
				field: 'label',
				type: 'string'
			},
			{
				label: 'User Count',
				field: 'user_count',
				type: 'number'
			}, {
				label: 'App Address',
				field: 'address',
				type: 'string'
			}];
			break;
		case CHART_DATA_INSIGHT.SEGMENT_DAPP_ACTIVITIES:
			labelOptions = [{
				label: 'Activity Count',
				field: 'count',
				type: 'number'
			}, {
				label: 'DApp',
				field: 'key',
				type: 'string'
			}, {
				label: 'User Count',
				field: 'user_count',
				type: 'number'
			}];
			break;
		case CHART_DATA_INSIGHT.SEGMENT_SMART_CONTRACT_ACTIVITIES:
			labelOptions = [{
				label: 'Activity Count',
				field: 'count',
				type: 'number'
			}, {
				label: 'Smart Contract',
				field: 'key',
				type: 'string'
			}, {
				label: 'User Count',
				field: 'user_count',
				type: 'number'
			}, {
				label: 'Category',
				field: 'category',
				type: 'string'
			}, {
				label: 'Dapp Name',
				field: 'dapp',
				type: 'string'
			}];
			break;
		case CHART_DATA_INSIGHT.SEGMENT_CATEGORY_ACTIVITIES:
			labelOptions = [{
				label: 'Activity Count',
				field: 'count',
				type: 'number'
			}, {
				label: 'Category',
				field: 'key',
				type: 'string'
			}, {
				label: 'User Count',
				field: 'user_count',
				type: 'number'
			}];
			break;
		case CHART_DATA_INSIGHT.PERSONA:
			labelOptions = [{
				label: 'Group Size',
				field: 'count',
				type: 'number'
			}, {
				label: 'Cluster Id',
				field: 'cluster',
				type: 'number'
			}];
			break;
		case CHART_DATA_INSIGHT.RETURNING_USERS_LAST_7_DAYS:
			labelOptions = [{
				label: 'Returning User Count Last 7 Days',
				field: 'data',
				type: 'number'
			}]
			break;
		case CHART_DATA_INSIGHT.RETURNING_USERS_LAST_1_DAYS:
			labelOptions = [{
				label: 'Returning User Count Last 24 Hours',
				field: 'data',
				type: 'number'
			}]
			break;
		case CHART_DATA_INSIGHT.RECOVERED_USERS_BY_DAY:
			labelOptions = [{
				label: 'Users Recovered From Dormant',
				field: 'data',
				type: 'number'
			}]
			break;
		case CHART_DATA_INSIGHT.SAVED_USERS_BY_DAY:
			labelOptions = [{
				label: 'Saved User Count',
				field: 'saved_user_count',
				type: 'number'
			}, {
				label: 'Saved Users Last 7 days',
				field: 'seven_day_total',
				type: 'number'
			}, {
				label: 'Time',
				field: 'day',
				type: 'string'
			}]
			break;
		case CHART_DATA_INSIGHT.TRANSACTIONS:
			labelOptions = [{
				label: 'Transaction Count',
				field: 'data',
				type: 'number'
			}]
			break;
		case CHART_DATA_INSIGHT.AVG_TOKEN_TRANSFER:
			labelOptions = [{
				label: 'Average Transfer Size Last 2 Years',
				field: 'data',
				type: 'number'
			}]
			break;
		case CHART_DATA_INSIGHT.MOST_ACTIVE_TIME:
			labelOptions = [{
				label: 'UTC Hour',
				field: 'data',
				type: 'number'
			}]
			break;
		case CHART_DATA_INSIGHT.USER_PERCENT_ACTIVITY:
			labelOptions = [{
				label: 'Activity Percentage',
				field: 'data',
				type: 'number'
			}]
			break;
		case CHART_DATA_INSIGHT.TOKEN_INFO:
			labelOptions = [{
				label: 'Supply Change',
				field: 'supply_diff',
				type: 'number'
			}, {
				label: 'Current Supply',
				field: 'new_supply',
				type: 'number'
			}, {
				label: 'Previous Supply',
				field: 'last_supply',
				type: 'number'
			}, {
				label: 'Token Holders Change',
				field: 'token_holders_diff',
				type: 'number'
			}, {
				label: 'Current Token Holders',
				field: 'new_token_holders',
				type: 'number'
			}, {
				label: 'Previous Token Holders',
				field: 'last_token_holders',
				type: 'number'
			}]
			break;
		case CHART_DATA_INSIGHT.NFTS:
			labelOptions = [{
				label: 'Wallet Count',
				field: 'wallet_count',
				type: 'number'
			}, {
				label: 'NFT Name',
				field: 'contract_name',
				type: 'string'
			}, {
				label: 'Contract Address',
				field: 'contract_address',
				type: 'string'
			}, {
				label: 'NFT Ticker',
				field: 'contract_ticker',
				type: 'string'
			}];
			break;
		case CHART_DATA_INSIGHT.SEGMENT_ACTIVITY_COUNT:
			labelOptions = [{
				label: 'All Time (Sum)',
				field: 'all_transactions',
				type: 'number'
			}, {
				label: 'Last 30 days (Sum)',
				field: 'last_30_count',
				type: 'number'
			}, {
				label: 'Last 7 days (Sum)',
				field: 'last_7_count',
				type: 'number'
			}, {
				label: 'Last 1 day (Sum)',
				field: 'last_1_count',
				type: 'number'
			}];
			break;
		case CHART_DATA_INSIGHT.SEGMENT_NFTS:
			labelOptions = [{
				label: 'Wallet Count',
				field: 'wallet_count',
				type: 'number'
			}, {
				label: 'NFT Name',
				field: 'contract_name',
				type: 'string'
			}, {
				label: 'Contract Address',
				field: 'contract_address',
				type: 'string'
			}, {
				label: 'NFT Ticker',
				field: 'contract_ticker',
				type: 'string'
			}, {
				label: 'Number of NFTs',
				field: 'nft_count',
				type: 'number'
			}];
			break;
		case CHART_DATA_INSIGHT.SEGMENT_NFT_HOLD_TIME:
			labelOptions = [{
				label: 'Average Hold Time In Days',
				field: 'data',
				type: 'number'
			}];
			break;
		case CHART_DATA_INSIGHT.SEGMENT_NFT_BOUGHT_SOLD:
			labelOptions = [{
				label: 'Time',
				field: 'day',
				type: 'string'
			},
			{
				label: 'Number of NFTs bought',
				field: 'num_bought',
				type: 'number'
			}, {
				label: 'Number of NFTs sold',
				field: 'num_sold',
				type: 'number'
			}, {
				label: 'Number of NFTs minted',
				field: 'num_minted',
				type: 'number'
			}, {
				label: 'Number of NFTs Bought on Opensea',
				field: 'num_bought_opensea',
				type: 'number'
			}];
			break;
		case CHART_DATA_INSIGHT.SEGMENT_NFT_TOP_BOUGHT:
			labelOptions = [{
				label: 'NFT Name',
				field: 'contract_name',
				type: 'string'
			}, {
				label: 'Contract Address',
				field: 'contract_address',
				type: 'string'
			}, {
				label: 'NFT Ticker',
				field: 'contract_ticker',
				type: 'string'
			}, {
				label: 'Total USD',
				field: 'total_usd',
				type: 'number'
			}, {
				label: 'Total Holders',
				field: 'total_holders',
				type: 'number'
			}];
			break;
		case CHART_DATA_INSIGHT.SEGMENT_NFT_TOP_SOLD:
			labelOptions = [{
				label: 'NFT Name',
				field: 'contract_name',
				type: 'string'
			}, {
				label: 'Contract Address',
				field: 'contract_address',
				type: 'string'
			}, {
				label: 'NFT Ticker',
				field: 'contract_ticker',
				type: 'string'
			}, {
				label: 'Total USD',
				field: 'total_usd',
				type: 'number'
			}, {
				label: 'Total Holders',
				field: 'total_holders',
				type: 'number'
			}];
			break;
		case CHART_DATA_INSIGHT.SEGMENT_COMMON_TOKENS:
			labelOptions = [{
				label: 'Wallet Count',
				field: 'wallet_count',
				type: 'number'
			}, {
				label: 'Contract Name',
				field: 'contract_name',
				type: 'string'
			}, {
				label: 'Contract Address',
				field: 'contract_address',
				type: 'string'
			}, {
				label: 'Token Ticker',
				field: 'contract_ticker',
				type: 'string'
			}, {
				label: 'Number of Tokens',
				field: 'token_count',
				type: 'number'
			}];
			break;
		case CHART_DATA_INSIGHT.SEGMENT_COMMON_TOKENS:
			labelOptions = [{
				label: 'Wallet Count',
				field: 'wallet_count',
				type: 'number'
			}, {
				label: 'Contract Name',
				field: 'contract_name',
				type: 'string'
			}, {
				label: 'Contract Address',
				field: 'contract_address',
				type: 'string'
			}, {
				label: 'Token Ticker',
				field: 'contract_ticker',
				type: 'string'
			}, {
				label: 'Number of Tokens',
				field: 'token_count',
				type: 'number'
			}, {
				label: 'Estimated USD Value',
				field: 'usd_value',
				type: 'number'
			}];
			break;
		case CHART_DATA_INSIGHT.SEGMENT_NET_WORTH_DISTRIBUTION:
			labelOptions = [{
				label: 'Net Worth Category',
				field: 'net_worth_category',
				type: 'string'
			}, {
				label: 'Total Net Worth In Category',
				field: 'total_net_worth',
				type: 'number'
			}, {
				label: 'Number of Users In Category',
				field: 'number_users',
				type: 'number'
			}];
			break;
		case CHART_DATA_INSIGHT.SEGMENT_MOST_VALUABLE_TOKENS:
			labelOptions = [{
				label: 'Wallet Count',
				field: 'wallet_count',
				type: 'number'
			}, {
				label: 'Contract Name',
				field: 'contract_name',
				type: 'string'
			}, {
				label: 'Contract Address',
				field: 'contract_address',
				type: 'string'
			}, {
				label: 'Token Ticker',
				field: 'contract_ticker',
				type: 'string'
			}, {
				label: 'Number of Tokens',
				field: 'token_count',
				type: 'number'
			}, {
				label: 'Estimated USD Value',
				field: 'usd_value',
				type: 'number'
			}];
			break;
		case CHART_DATA_INSIGHT.COMMON_TOKENS:
			labelOptions = [{
				label: 'Wallet Count',
				field: 'wallet_count',
				type: 'number'
			}, {
				label: 'Contract Name',
				field: 'contract_name',
				type: 'string'
			}, {
				label: 'Contract Address',
				field: 'contract_address',
				type: 'string'
			}, {
				label: 'Token Ticker',
				field: 'contract_ticker',
				type: 'string'
			}];
			break;
		case CHART_DATA_INSIGHT.SEGMENT_MODEL_PERSONA:
			labelOptions = [{
				label: 'Address Count',
				field: 'count',
				type: 'number'
			}, {
				label: 'Persona',
				field: 'cluster',
				type: 'number'
			}];
			break;
		case CHART_DATA_INSIGHT.UNIQUE_WALLETS:
			labelOptions = [{
				label: 'Unique Wallets',
				field: 'data',
				type: 'number'
			}]
			break;
		case CHART_DATA_INSIGHT.TOKEN_USD_VALUE:
			labelOptions = [{
				label: 'Token Holders USD Value',
				field: 'data',
				type: 'number'
			}];
			break;
		case CHART_DATA_INSIGHT.USD_VALUE:
			labelOptions = [{
				label: 'USD Value',
				field: 'data',
				type: 'number'
			}];
			break;
		case CHART_DATA_INSIGHT.PROJECT_NFT_AVG_HOLD_TIME:
			labelOptions = [{
				label: 'Average Hold Time (Days)',
				field: 'data',
				type: 'number'
			}];
			break;
		case CHART_DATA_INSIGHT.PROJECT_NFT_TOTAL_COUNTS:
			labelOptions = [{
				label: 'Time',
				field: 'day',
				type: 'string'
			},
			{
				label: 'Average USD Paid',
				field: 'avg_paid',
				type: 'number'
			},
			{
				label: 'Number of NFTs Burned',
				field: 'burn_count',
				type: 'number'
			},
			{
				label: 'Number of NFTs Burned Cumulative',
				field: 'burn_count_cumulative',
				type: 'number'
			},
			{
				label: 'Number of NFTs Minted',
				field: 'mint_count',
				type: 'number'
			},
			{
				label: 'Number of NFTs Minted Cumulative',
				field: 'mint_count_cumulative',
				type: 'number'
			},
			{
				label: 'Number of NFTs Sold',
				field: 'sold_count',
				type: 'number'
			},
			{
				label: 'Number of NFTs Sold Cumulative',
				field: 'sold_count_cumulative',
				type: 'number'
			},
			{
				label: 'Number of NFTs Swapped',
				field: 'swap_count',
				type: 'number'
			},
			{
				label: 'Number of NFTs Swapped Cumulative',
				field: 'swap_count_cumulative',
				type: 'number'
			},
			{
				label: 'Number of NFTs Transferred',
				field: 'transfer_count',
				type: 'number'
			},
			{
				label: 'Number of NFTs Transferred Cumulative',
				field: 'transfer_count_cumulative',
				type: 'number'
			},
			{
				label: 'Number of NFTs Bought on Opensea',
				field: 'opensea_count',
				type: 'number'
			},
			{
				label: 'Number of NFTs Bought on OpenSea Cumulative',
				field: 'opensea_count_cumulative',
				type: 'number'
			},
			{
				label: 'Number of NFTs Bought on Magic Eden',
				field: 'magiceden_count',
				type: 'number'
			},
			{
				label: 'Number of NFTs Bought on Magic Eden Cumulative',
				field: 'magiceden_count_cumulative',
				type: 'number'
			},
			{
				label: 'USD Sold Daily',
				field: 'usd_sold',
				type: 'number'
			},
			{
				label: 'USD Sold Cumulative',
				field: 'usd_sold_cumulative',
				type: 'number'
			},
			{
				label: 'Remaining Supply of NFTs (Mint - Burn)',
				field: 'remaining_supply',
				type: 'number'
			}];
			break;
		case CHART_DATA_INSIGHT.PROJECT_ACTIONS:
			labelOptions = [{
				label: 'Project Action',
				field: 'key',
				type: 'string'
			}, {
				label: 'Count',
				field: 'count',
				type: 'number'
			}];
			break;
	}

	//For Line charts we only support time as the label
	if (!isDataField && chartType === CHART_TYPE.LINE) {
		//if labelOptions doesn't include an object with field 'time' then we need to add it
		if (!labelOptions.find((option) => option.label === 'Time')) {
			labelOptions.push({
				label: 'Time',
				field: 'time',
				type: 'time'
			});
		}
	}

	let finalResults = [];
	let chartDefinition = getChartDefinition(chartType);
	var idx = 0;
	for (let label of labelOptions) {
		if (chartDefinition.data_requires_number && isDataField && label.type !== 'number') continue; // skip non-number fields
		finalResults.push({
			id: idx++,
			name: label.label,
			field: label.field,
		})
	}
	return finalResults;
}


export function getReportChartTypeList() {
	let chartTypeList = []
	let idx = 0;
	for (const [key, chartType] of Object.entries(CHART_LOOKUP)) {
		chartTypeList.push({
			id: idx++,
			name: CHART_LOOKUP[key].type
		})
	}
	return chartTypeList
}

export function getReportChartGroupListCustomMetric() {
	let chartGroupList = []
	chartGroupList.push(CHART_DATA_GROUP_TYPE.ALL_USERS);
	let finalResults = []
	//get number between 100 and 200
	let idx = 0;
	for (let chartGroup of chartGroupList) {
		finalResults.push({
			id: idx++,
			name: chartGroup,
			tooltip: 'This is a Custom Metric Defined by a Query'
		})
	}
	return finalResults
}

export function getReportChartGroupList(selectedInsight) {
	let chartGroupList = []
	console.log("Selected Insight: " + JSON.stringify(selectedInsight));
	switch (selectedInsight.name) {
		case CHART_DATA_INSIGHT.COUNT:
			chartGroupList.push(CHART_DATA_GROUP_TYPE.DORMANT_USERS, CHART_DATA_GROUP_TYPE.ACTIVE_USERS,
				CHART_DATA_GROUP_TYPE.AT_RISK_USERS, CHART_DATA_GROUP_TYPE.ALL_USERS, CHART_DATA_GROUP_TYPE.TOKEN_HOLDERS);
			break;
		case CHART_DATA_INSIGHT.RECOVERED_USERS:
			chartGroupList.push(CHART_DATA_GROUP_TYPE.ALL_USERS);
			break;
		case CHART_DATA_INSIGHT.COUNT_NEW_USERS:
			chartGroupList.push(CHART_DATA_GROUP_TYPE.NEW_USERS);
			break;
		case CHART_DATA_INSIGHT.TOP_ETH_HOLDER:
			chartGroupList.push(CHART_DATA_GROUP_TYPE.TOKEN_HOLDERS);
			break;
		case CHART_DATA_INSIGHT.PROJECT_NEW_USER_ORIGIN:
			chartGroupList.push(CHART_DATA_GROUP_TYPE.NEW_USERS);
			break;
		case CHART_DATA_INSIGHT.PROJECT_ACTIVITIES:
			chartGroupList.push(CHART_DATA_GROUP_TYPE.DORMANT_USERS, CHART_DATA_GROUP_TYPE.ACTIVE_USERS, CHART_DATA_GROUP_TYPE.NEW_USERS, CHART_DATA_GROUP_TYPE.AT_RISK_USERS);
			break;
		case CHART_DATA_INSIGHT.PROJECT_CATEGORIES:
			chartGroupList.push(CHART_DATA_GROUP_TYPE.DORMANT_USERS, CHART_DATA_GROUP_TYPE.ACTIVE_USERS);
			break;
		case CHART_DATA_INSIGHT.ACTIVITIES:
			chartGroupList.push(CHART_DATA_GROUP_TYPE.ACTIVE_USERS, CHART_DATA_GROUP_TYPE.NEW_USERS, CHART_DATA_GROUP_TYPE.AT_RISK_USERS,
				CHART_DATA_GROUP_TYPE.DORMANT_USERS);
			break;
		case CHART_DATA_INSIGHT.PERSONA:
			chartGroupList.push(CHART_DATA_GROUP_TYPE.ACTIVE_USERS);
			break;
		case CHART_DATA_INSIGHT.TOKEN_USD_VALUE:
			chartGroupList.push(CHART_DATA_GROUP_TYPE.TOKEN_HOLDERS);
			break;
		case CHART_DATA_INSIGHT.USD_VALUE:
			chartGroupList.push(CHART_DATA_GROUP_TYPE.ALL_USERS);
			break;
		case CHART_DATA_INSIGHT.AVG_TOKEN_TRANSFER:
			chartGroupList.push(CHART_DATA_GROUP_TYPE.TOKEN_HOLDERS);
			break;
		case CHART_DATA_INSIGHT.RETURNING_USERS_LAST_7_DAYS:
			chartGroupList.push(CHART_DATA_GROUP_TYPE.RETURNING_USERS);
			break;
		case CHART_DATA_INSIGHT.RETURNING_USERS_LAST_1_DAYS:
			chartGroupList.push(CHART_DATA_GROUP_TYPE.RETURNING_USERS);
			break;
		case CHART_DATA_INSIGHT.RECOVERED_USERS_BY_DAY:
			chartGroupList.push(CHART_DATA_GROUP_TYPE.ALL_USERS);
			break;
		case CHART_DATA_INSIGHT.SAVED_USERS_BY_DAY:
			chartGroupList.push(CHART_DATA_GROUP_TYPE.ALL_USERS);
			break;
		case CHART_DATA_INSIGHT.TRANSACTIONS:
			chartGroupList.push(CHART_DATA_GROUP_TYPE.ACTIVE_USERS);
			break;
		case CHART_DATA_INSIGHT.MOST_ACTIVE_TIME:
			chartGroupList.push(CHART_DATA_GROUP_TYPE.ALL_USERS);
			break;
		case CHART_DATA_INSIGHT.USER_PERCENT_ACTIVITY:
			chartGroupList.push(CHART_DATA_GROUP_TYPE.ACTIVE_USERS);
			break;
		case CHART_DATA_INSIGHT.COMMON_TOKENS:
			chartGroupList.push(CHART_DATA_GROUP_TYPE.ACTIVE_USERS);
			break;
		case CHART_DATA_INSIGHT.UNIQUE_WALLETS:
			chartGroupList.push(CHART_DATA_GROUP_TYPE.ALL_USERS);
			break;
		case CHART_DATA_INSIGHT.PROJECT_ACTIONS:
			chartGroupList.push(CHART_DATA_GROUP_TYPE.ALL_USERS);
			break;
		case CHART_DATA_INSIGHT.PROJECT_NFT_TOTAL_COUNTS:
			chartGroupList.push(CHART_DATA_GROUP_TYPE.ALL_USERS);
			break;
		case CHART_DATA_INSIGHT.PROJECT_NFT_AVG_HOLD_TIME:
			chartGroupList.push(CHART_DATA_GROUP_TYPE.ALL_USERS);
			break;
		case CHART_DATA_INSIGHT.NFTS:
			chartGroupList.push(CHART_DATA_GROUP_TYPE.ALL_USERS);
			break;
		case CHART_DATA_INSIGHT.TOKEN_INFO:
			chartGroupList.push(CHART_DATA_GROUP_TYPE.TOKEN_HOLDERS, CHART_DATA_GROUP_TYPE.ALL_USERS);
			break;
		case CHART_DATA_INSIGHT.SEGMENT_COUNT:
			chartGroupList.push(CHART_DATA_GROUP_TYPE.SEGMENT);
			break;
		case CHART_DATA_INSIGHT.SEGMENT_DAPP_ACTIVITIES:
			chartGroupList.push(CHART_DATA_GROUP_TYPE.SEGMENT);
			break;
		case CHART_DATA_INSIGHT.SEGMENT_SMART_CONTRACT_ACTIVITIES:
			chartGroupList.push(CHART_DATA_GROUP_TYPE.SEGMENT);
			break;
		case CHART_DATA_INSIGHT.SEGMENT_CATEGORY_ACTIVITIES:
			chartGroupList.push(CHART_DATA_GROUP_TYPE.SEGMENT);
			break;
		case CHART_DATA_INSIGHT.SEGMENT_TOKEN_USD_VALUE:
			chartGroupList.push(CHART_DATA_GROUP_TYPE.SEGMENT);
			break;
	}

	let tooltips = new Map();
	tooltips.set(CHART_DATA_GROUP_TYPE.ACTIVE_USERS, 'Wallets that have interacted with the project in the last 30 days');
	tooltips.set(CHART_DATA_GROUP_TYPE.NEW_USERS, 'Wallets with first project interaction in the last 7 days');
	tooltips.set(CHART_DATA_GROUP_TYPE.AT_RISK_USERS, 'Wallets whose last interaction with the project is 30 - 90 days ago');
	tooltips.set(CHART_DATA_GROUP_TYPE.DORMANT_USERS, 'Wallets whose last interaction with the project is > 90 days ago');
	tooltips.set(CHART_DATA_GROUP_TYPE.ALL_USERS, 'All unique wallets that ever interacted with the project');
	tooltips.set(CHART_DATA_GROUP_TYPE.TOKEN_HOLDERS, 'Wallets holding any tokens tracked for the project');
	tooltips.set(CHART_DATA_GROUP_TYPE.RETURNING_USERS, 'Wallets that interacted with the project before and have interacted again recently');
	tooltips.set(CHART_DATA_GROUP_TYPE.SEGMENT, 'All users in the segment');

	/*for (const [key, chartGroup] of Object.entries(CHART_DATA_GROUP_TYPE)) {
	  chartGroupList.push({
		id: idx++,
		name: CHART_DATA_GROUP_TYPE[key]
	  })
	}*/
	let finalResults = []
	//get number between 100 and 200
	let idx = 0;
	for (let chartGroup of chartGroupList) {
		finalResults.push({
			id: idx++,
			name: chartGroup,
			tooltip: tooltips.get(chartGroup)
		})
	}
	console.log('FINAL RESULTS: ' + finalResults)
	return finalResults
}

export function getReportTimeRange() {
	let timeRange = []
	let idx = 0;
	for (const [key, time] of Object.entries(CHART_TIME_RANGE)) {
		timeRange.push({
			id: idx++,
			name: CHART_TIME_RANGE[key]
		})
	}
	return timeRange
}

export function getChartSizes() {
	let chartSizes = []
	let idx = 0;
	for (const [key, size] of Object.entries(CHART_SIZE)) {
		chartSizes.push({
			id: idx++,
			name: CHART_SIZE[key]
		})
	}
	return chartSizes
}
