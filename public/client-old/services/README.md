## Add A New API in the Webapp backend and frontend

### Webapp Backend
Add a new model in the src/models/ folder, either using lb4 or referencing another model file
Update index.ts in the src/models/ folder to export the model for use
Add the model details to src/controller/metric.controller.js
Make sure the file is importing your newly created model file

The webapp backend is taking the data platform API and translating it into an webapp frontend consistent API.

### Webapp frontend
Update client/services/metric.js with the new API you created in the backend
Create a new "report" in /partials/analytics/(appropriate next folder if applicable) that calls the API in metric.js
Ensure the import on the report is importing the new API from metricjs