import { SegmentFactory } from '../../../src/segment-builder/segment-factory';
import { loadProjects, URL_DOMAIN } from '../utils/Utils';

export class SegmentBuilderDataLoader {

	static async loadProjects() {
		return await loadProjects();
	}

	static async loadAppInfo() {
		let response = await fetch(`${URL_DOMAIN}/app-info?network=ETH`, {
			method: 'GET',
			withCredentials: true,
			credentials: 'omit',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
			}
		});
		const data = await response.json();
		return data.body;
	}

	static getDapps(appInfo) {
		const uniqueDappNames = [];
		const uniqueDapps = [];
		appInfo.forEach((app) => {
			if(app.Name) {
				if (!uniqueDappNames.includes(app.Name)) {
					uniqueDappNames.push(app.Name);
					uniqueDapps.push({ name: app.Name, value: app.Name });
				}
			}
		});
		return uniqueDapps.sort((a, b) => a.name.localeCompare(b.name));
	}

	static getEventTypes() {
		return SegmentFactory.getSegmentTypes().filter((type) => {
			return SegmentFactory.createSegment(type.value).isSelectableEventType();
		});
	}

	static async getCustomEvents() {
		let response = await fetch(`${URL_DOMAIN}/segment/event`, {
			method: 'GET',
			withCredentials: true,
			credentials: 'omit',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
			}
		});
		const data = await response.json();
		//console.log(`custom events: ${JSON.stringify(data)}`);
		if (data.body && data.body.length) {
			return (data.body
				.map(i => ({ name: i, value: i }))
				.sort((a, b) => a.name.localeCompare(b.name))
			);
		}
		return [];
	}

	static async getWalletTags() {
		//TODO we need to move this to the backend
		return [
			{name: 'trader', value: 'trader'},
			{name: 'dex-user', value: 'dex-user'},
			{name: 'top-defi-user', value: 'top-defi-user'},
			{name: 'gamer', value: 'gamer'},
			{name: 'bridge-user', value: 'bridge-user'}
		]
	}

	static async getUTMCampaigns() {
		let response = await fetch(`${URL_DOMAIN}/metric/utm/campaigns`, {
			method: 'GET',
			withCredentials: true,
			credentials: 'omit',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
			}
		});
		const data = await response.json();
		console.log(`campaigns: ${JSON.stringify(data)}`);
		if (data.body && data.body.length) {
			return (data.body[0].data
				.map(i => ({ name: i.campaign_id || 'unnamed', value: i.campaign_id }))
				.sort((a, b) => a.name.localeCompare(b.name))
			);
		}
		return [];
	}

	static getCategories(data) {
		let uniqueCategories = [];
		data.forEach((item) => {
			if(item.Category) {
				const categoryName = item.Category.replace(/\r/g, '');
				if (!uniqueCategories.includes(categoryName)) {
					uniqueCategories.push(categoryName);
				}
			}
			else {
				console.log("item without category: " + JSON.stringify(item));
			}

		});
		return uniqueCategories.map((category) => ({ name: category, value: category }));
	}
}
