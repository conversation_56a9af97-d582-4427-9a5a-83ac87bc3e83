var testMode = 0;
import * as Utils from '../utils/Utils';

export async function getProjectById(id) {
	let response = {};
	let jsonresponse = {};
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/organizations/${id}`, {
			method: 'GET',
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
			}
		});
		jsonresponse = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return jsonresponse;
}

export async function getProjectByUUID(uuid) {
	let response = {};
	let jsonresponse = {};
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/projects?filter[where][uuid]=${uuid}`, {
			method: 'GET',
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
			}
		});
		jsonresponse = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return jsonresponse;
}

export async function updateProjectById(id, projectName, description) {
	var data = {
		name: projectName,
		organizationId: parseInt(localStorage.getItem('userOrgId')),
		description: description
	};

	let response = {};
	let jsonresponse = {};
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/projects/${id}`, {
			method: 'PUT',
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				"accept": "*/*",
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				"Content-Type": "application/json"
			},
			body: JSON.stringify(data),
		});
		jsonresponse = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return jsonresponse;
}

export async function getProjectsByOrgId(orgId) {
	let response = {};
	let jsonresponse = {};
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/projects?filter[where][organizationId]=${orgId}`, {
			method: 'GET',
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
			}
		});
		jsonresponse = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return jsonresponse;
}

export async function getProjectsConnectionsByOrgId(orgId) {
	let response = {};
	let jsonresponse = {};
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/projects?filter[include][]=dataConnections&filter[where][organizationId]=${orgId}`, {
			method: 'GET',
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
			}
		});
		jsonresponse = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return jsonresponse;
}

export async function getProjectSharingData(project_uuid) {
	let response = {};
	let jsonresponse = {};
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/admin/projects/${project_uuid}`, {
			method: 'GET',
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
			}
		});
		jsonresponse = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return jsonresponse;
}

export async function getAvailableOrgs() {
	let response = {};
	let jsonresponse = {};
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/admin/organizations`, {
			method: 'GET',
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
			}
		});
		jsonresponse = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return jsonresponse;
}

export async function addProjectForOrg(orgId, projectName, projectDescription, projectUUID) {
	var data = {
		name: projectName,
		description: projectDescription,
		organizationId: orgId,
		uuid: projectUUID,
		isAdmin: true
	};

	let response = {};
	let jsonresponse = {};
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/projects`, {
			method: "post",
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				"accept": "application/json",
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				"Content-Type": "application/json"
			},
			body: JSON.stringify(data),
		});
		jsonresponse = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return jsonresponse;
}

export async function addProjectShare(orgId, projectName, projectDescription, projectUUID) {
	var data = {
		name: projectName,
		description: projectDescription,
		organizationId: orgId,
		uuid: projectUUID
	};

	let response = {};
	let jsonresponse = {};
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/admin/projects`, {
			method: "post",
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				"accept": "application/json",
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				"Content-Type": "application/json"
			},
			body: JSON.stringify(data),
		});
		jsonresponse = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return jsonresponse;
}

export async function deleteProjectShare(projectId) {
	var data = {};

	let response = {};
	let jsonresponse = {};
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/admin/projects/${projectId}`, {
			method: "delete",
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				"accept": "*/*",
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(data),
		});
		jsonresponse = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return jsonresponse;
}

export async function deleteProjectById(id) {
	var data = {};

	let response = {};
	let jsonresponse = {};
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/projects/${id}`, {
			method: "delete",
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				"accept": "*/*",
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(data),
		});
		jsonresponse = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return jsonresponse;
}

export async function getConnectionsByProject(id, orgId) {
	if (testMode) {
		if (id == 1)
			return { "addresstype": "raleon-project", "lastblock": 0, "data": { "smart_contracts": [{ "id": "1", "name": "ExchangeProxy", "description": "Handles swaps that may happen through Super.", "address": "******************************************" }, { "id": "2", "name": "Deployer", "description": "Deployer SC for us.", "address": "******************************************" }], "version": 1, "tokens": [{ "id": "3", "name": "SUPER", "description": "Our projects governance token $SUPER", "address": "******************************************" }] }, "SK": "ANY", "address": "ebbcbcb1-1cf3-4450-baf5-dee789402ec6", "PK": "ebbcbcb1-1cf3-4450-baf5-dee789402ec6_raleon-project", "network": "ANY" }
		if (id == 2)
			return { "addresstype": "raleon-project", "lastblock": 0, "data": { "smart_contracts": [{ "id": "4", "name": "Registry", "description": "How new NFTs are registered.", "address": "******************************************" }, { "id": "4", "name": "Wallet", "description": "Where fee funds are deposited.", "address": "0xba100000625a3754423978a60c9317c58a4243lo" }, { "id": "4", "name": "Exchanger", "description": "How an NFT is paid for and transferred.", "address": "******************************************" }], "version": 1, "tokens": [{ "id": "5", "name": "Reward Token", "description": "Token we can use to optionally reward users.", "address": "******************************************" }] }, "SK": "ANY", "address": "ebbcbcb1-1cf3-4450-baf5-dee789402ec6", "PK": "ebbcbcb1-1cf3-4450-baf5-dee789402ec6_raleon-project", "network": "ANY" }
		else
			return { "addresstype": "raleon-project", "lastblock": 0, "data": { "smart_contracts": [{ "id": "6", "address": "******************************************" }, { "id": "7", "address": "******************************************" }, { "id": "8", "address": "******************************************" }], "version": 1, "tokens": [{ "id": "9", "address": "******************************************" }] }, "SK": "ANY", "address": "ebbcbcb1-1cf3-4450-baf5-dee789402ec6", "PK": "ebbcbcb1-1cf3-4450-baf5-dee789402ec6_raleon-project", "network": "ANY" }
	}

	let response = {};
	let jsonresponse = {};
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/data-connections?filter[where][project_uuid]=${id}`, {
			method: 'GET',
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
			}
		});
		jsonresponse = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return jsonresponse;
}

export async function addDataConnectionToProject(projId, projuuid, conName, conNet, conDesc, conAddress, conType, aggregate) {
	var data = {
		name: conName,
		type: conType,
		description: conDesc,
		address: conAddress,
		network: conNet,
		startDate: new Date(),
		aggregate: aggregate,
		project_uuid: projuuid
	};

	let response = {};
	let jsonresponse = {};
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/data-connections/`, {
			method: "post",
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				"accept": "application/json",
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(data),
		});
		jsonresponse = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return jsonresponse;
}

export async function updateDataConnectionById(id, projId, projectUUID, conName, conDesc, conAddress, conType) {
	var data = {
		name: conName || '',
		type: conType || '',
		description: conDesc || '',
		address: conAddress || '',
		startDate: new Date(),
		projectId: projId,
		project_uuid: projectUUID
	};

	let response = {};
	let jsonresponse = {};
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/data-connections/${id}`, {
			method: "put",
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				"accept": "*/*",
				"Content-Type": "application/json"
			},
			body: JSON.stringify(data),
		});
		jsonresponse = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return jsonresponse;
}

export async function deleteDataConnectionById(id) {
	var data = {};

	let response = {};
	let jsonresponse = {};
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/data-connections/${id}`, {
			method: "delete",
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				"accept": "*/*",
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(data),
		});
		jsonresponse = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return jsonresponse;
}

export async function getAvailableCustomMetrics() {
	let response = {};
	let jsonresponse = {};
	console.log("GETTING AVAILABLE Custom Metrics: " + localStorage.getItem("userOrgId"));
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/metric/custom-metric/available`, {
			method: 'GET',
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				"accept": "*/*",
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
			}
		});
		jsonresponse = await response.json();
		//console.log("CUSTOM METRIC RESPONSE: " + JSON.stringify(jsonresponse));
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}

	let finalResults = [];
	for (let i = 0; i < jsonresponse.body.length; i++) {
		finalResults.push({
			"name": `${jsonresponse.body[i].name}`,
			"id": i, //used for the dropdown
			"customMetricId": jsonresponse.body[i].id, //used for the db
			"uuid": jsonresponse.body[i].address,
			"query": jsonresponse.body[i].query,
			"schema": JSON.parse(jsonresponse.body[i].schema),
			"type": "custom-metric"
		})
	}

	//console.log("CUSTOM METRIC FINAL RESULTS: " + JSON.stringify(finalResults));

	return finalResults;
}

export async function getConfiguredAttributionConversions(utm_campaign) {
	let response = {};
	let jsonresponse = {};
	console.log("GETTING Conversion Events Configured: " + localStorage.getItem("userOrgId"));
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/attribution-campaigns?filter[where][orgid]=${localStorage.getItem("userOrgId")}&filter[where][utm_campaign]=${utm_campaign}`, {
			method: 'GET',
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				"accept": "*/*",
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
			}
		});
		jsonresponse = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}

	let idsFound = [];
	for (let i = 0; i < jsonresponse.length; i++) {
		idsFound.push({
			"id": jsonresponse[i].id
		})
	}

	return idsFound;
}

export async function getAvailableAudiences() {
	let finalResults = [];
		let response = {};
		let jsonresponse = {};
	const filter = `filter[where][orgid]=${localStorage.getItem("userOrgId")}&filter[where][status]=PUBLISHED`;
			try {
				let response = await fetch(`${Utils.URL_DOMAIN}/segments?${filter}`, {
					method: 'GET',
					withCreditentials: true,
					credentials: 'omit', // include, *same-origin, omit
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					},
				});
				let jsonresponse = await response.json();
				let startId = finalResults.length;
				for (let i = 0; i < jsonresponse.length; i++) {
					finalResults.push({
						"name": `Audience: ${jsonresponse[i].name}`,
						"id": i + startId, //used for the dropdown
						"dbId": jsonresponse[i].id,
						"uuid": jsonresponse[i].viewname,
						"viewname": jsonresponse[i].viewname,
						"organizationId": jsonresponse[i].orgid,
						"type": "segment"
					})
				}

			} catch (err) {
				console.log('getPRojects Error: ' + JSON.stringify(err));
			}
	return finalResults;
}

export async function getAvailableProjects(includeSegments, includeDataConnections) {
	let response = {};
	let jsonresponse = {};
	console.log("GETTING AVAILABLE PROJECTS: " + localStorage.getItem("userOrgId"));
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/projects?${includeDataConnections ? 'filter[include][]=dataConnections&' : ''}filter[where][organizationId]=${localStorage.getItem("userOrgId")}`, {
			method: 'GET',
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				"accept": "*/*",
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
			}
		});
		jsonresponse = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}

	let finalResults = [];
	for (let i = 0; i < jsonresponse.length; i++) {
		finalResults.push({
			"name": `Project: ${jsonresponse[i].name}`,
			"id": i, //used for the dropdown
			"dbId": jsonresponse[i].id, //used for the db
			"uuid": jsonresponse[i].uuid,
			"organizationId": jsonresponse[i].organizationId,
			"dataConnections": jsonresponse[i].dataConnections || [],
			"type": "project"
		})
	}
	//console.log('finalResults: ' + JSON.stringify(finalResults));


	if (includeSegments) {
		const filter = `filter[where][orgid]=${localStorage.getItem("userOrgId")}&filter[where][status]=PUBLISHED`;
		try {
			let response = await fetch(`${Utils.URL_DOMAIN}/segments?${filter}`, {
				method: 'GET',
				withCreditentials: true,
				credentials: 'omit', // include, *same-origin, omit
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
			});
			let jsonresponse = await response.json();
			let startId = finalResults.length;
			for (let i = 0; i < jsonresponse.length; i++) {
				finalResults.push({
					"name": `Audience: ${jsonresponse[i].name}`,
					"id": i + startId, //used for the dropdown
					"dbId": jsonresponse[i].id,
					"uuid": jsonresponse[i].viewname,
					"viewname": jsonresponse[i].viewname,
					"organizationId": jsonresponse[i].orgid,
					"type": "segment"
				})
			}

		} catch (err) {
			console.log('getPRojects Error: ' + JSON.stringify(err));
		}
	}

	return finalResults;
}

export async function getApplications() {
	try {
		let response = await fetch(`${Utils.URL_DOMAIN}/event-types?applicationFilter=true`, {
			method: 'GET',
			credentials: 'omit', // include, *same-origin, omit
			mode: 'cors',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${localStorage.getItem('token')}`,
			},
		});
		return await response.json();
	} catch (err) {
		return {
			error: err,
			success: false
		}
	}
}
