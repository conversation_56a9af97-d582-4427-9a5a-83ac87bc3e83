var testMode = 0;
import * as Utils from '../utils/Utils';

export async function getOrgById(id) {
	let response = {};
	let jsonresponse = {};
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/organizations/${id}`, {
			method: 'GET',
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json',
				'ngrok-skip-browser-warning': true
			}
		});
		jsonresponse = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return jsonresponse;
}

export async function getCurrentOrg() {
	let response = {};
	let jsonresponse = {};
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/organization/current`, {
			method: 'GET',
			withCreditentials: true,
			credentials: 'omit',
			mode: 'cors',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${localStorage.getItem('token')}`,
			}
		});
		jsonresponse = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return jsonresponse;
}

export async function updateOrgById(id, orgName, profilePicUrl, questAlbumArtUrl, questBotName) {
	var data = {
		name: orgName,
	};

	if (profilePicUrl) {
		data.profilePicUrl = profilePicUrl;
	}
	if (questAlbumArtUrl) {
		data.questAlbumArtUrl = questAlbumArtUrl;
	}
	if (questBotName) {
		data.questBotName = questBotName;
	}

	let response = {};
	let jsonresponse = {};
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/organizations/${id}`, {
			method: "PATCH",
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(data),
		});
		jsonresponse = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return jsonresponse;
}

export async function updateOrgInteraction(id, eventName, value, iData) {
	Object.keys(iData.org).forEach(function (key, index) {
		console.log('FINDING STUFF ' + iData.org[key]);
		if (key == eventName) {
			console.log('FINDING MATCH');
			iData.org[key] = value;
		}
	});

	var striData = JSON.stringify(iData);
	var data = {
		interactionData: striData
	};

	let response = {};
	let jsonresponse = {};
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/organizations/${id}`, {
			method: "PATCH",
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(data),
		});
		jsonresponse = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return jsonresponse;
}

export async function resetOrgInteractions(id) {
	var iData = {
		org:
		{
			attribution: null,
			custom_events: null,
			audiences: null,
			teammates: null
		}
	}

	var striData = JSON.stringify(iData);
	var data = {
		interactionData: striData
	};

	let response = {};
	let jsonresponse = {};
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/organizations/${id}`, {
			method: "PATCH",
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(data),
		});
		jsonresponse = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return jsonresponse;
}

export async function setOrgAttribution(id, attrStatus) {
	var data = {
		attribution: attrStatus
	};

	let response = {};
	let jsonresponse = {};
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/organizations/${id}`, {
			method: "PATCH",
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(data),
		});
		jsonresponse = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return jsonresponse;
}

export async function createEnvAndUser(envName, envUsername) {
	//Add env
	var data = {
		name: envName,
		dev: false,
		beta: false,
		attribution: false
	};

	let response = {};
	let jsonresponse = {};
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/onboard/organization`, {
			method: "post",
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				"accept": "application/json",
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				"Content-Type": "application/json"
			},
			body: JSON.stringify(data),
		});
		jsonresponse = await response.json();
	} catch (err) {
		console.log("Org Error: " + JSON.stringify(err));
		throw err;
	}

	//Add user for it
	var userData1 = {
		email: envUsername,
		password: 'raleonBillions!'
	};

	let userResponse = {};
	let userJsonResponse = {};
	userResponse = await fetch(`${Utils.URL_DOMAIN}/user`, {
		method: "post",
		withCreditentials: true,
		credentials: 'omit',
		headers: {
			"accept": "application/json",
			'Authorization': `Bearer ${localStorage.getItem('token')}`,
			'Access-Control-Allow-Origin': '*',
			"Content-Type": "application/json"
		},
		body: JSON.stringify(userData1),
	});
	userJsonResponse = await userResponse.json();

	var userData2 = {
		firstName: 'Raleon',
		lastName: 'Support',
		organizationId: jsonresponse.id,
		roles: ['admin', 'raleon-support']
	};

	let userResponseUpdate = {};
	userResponseUpdate = await fetch(`${Utils.URL_DOMAIN}/onboard/users/${userJsonResponse.id}`, {
		method: "PATCH",
		withCreditentials: true,
		credentials: 'omit',
		headers: {
			'Authorization': `Bearer ${localStorage.getItem('token')}`,
			"accept": "*/*",
			"Content-Type": "application/json"
		},
		body: JSON.stringify(userData2),
	});
}
