import { SegmentFactory } from '../../../src/segment-builder/segment-factory';
import { IncludeSmartContractsSegment } from '../../../src/segment-builder/segments/include-smart-contracts.segment';
import * as Utils from '../utils/Utils';

const URL_DOMAIN = Utils.URL_DOMAIN;

export const SEGMENT_INSTRUCTION_TYPE = {
	SOURCE_PROJECT_USERS: "source_project_users",
	FILTER_USERS_WITH_LAST_ACTIVITY: "filter_users_with_last_activity",
	METRIC_GMT_COMPARISON: "gmt_comparison",
	METRIC_TRANSACTION_COUNT_LAST_30: "transaction_count_last_30",
}

export const SEGMENT_STATUS = {
	DRAFT: 'DRAFT',
	PUBLISHED: 'PUBLISHED',
}

export function getPossibleInputs() {
	return [
		{
			name: "operator",
			type: "string",
			possibleValues: [">", "=", "<"],
		},
		{
			name: "value",
			type: "number",
		},
		{
			name: "valueString",
			type: "string",
		},
		{
			name: "projectid",
			type: "string",
		}
	]
}

const SOURCE_PROJECT_USERS = {
	type: SEGMENT_INSTRUCTION_TYPE.SOURCE_PROJECT_USERS,
	inputs: [
		"projectid"
	],
	output: {
		instruction: SEGMENT_INSTRUCTION_TYPE.SOURCE_PROJECT_USERS,
		"projectid": "projectid",
		data: {}
	}
}

const FILTER_USERS_WITH_LAST_ACTIVITY = {
	type: SEGMENT_INSTRUCTION_TYPE.FILTER_USERS_WITH_LAST_ACTIVITY,
	inputs: [
		"operator",
		"value",
		"projectid"
	],
	output: {
		instruction: SEGMENT_INSTRUCTION_TYPE.FILTER_USERS_WITH_LAST_ACTIVITY,
		"projectid": "projectid",
		data: {
			"operator": "operator",
			"value": "value"
		}
	}
}

const METRIC_GMT_COMPARISON = {
	type: SEGMENT_INSTRUCTION_TYPE.METRIC_GMT_COMPARISON,
	inputs: [
		"operator",
		"value",
	],
	output: {
		instruction: SEGMENT_INSTRUCTION_TYPE.METRIC_GMT_COMPARISON,
		data: {
			"operator": "operator",
			"value": "value"
		}
	}
}

const METRIC_TRANSACTION_COUNT_LAST_30 = {
	type: SEGMENT_INSTRUCTION_TYPE.METRIC_TRANSACTION_COUNT_LAST_30,
	inputs: [
		"operator",
		"value",
	],
	output: {
		instruction: SEGMENT_INSTRUCTION_TYPE.METRIC_TRANSACTION_COUNT_LAST_30,
		data: {
			"operator": "operator",
			"value": "value"
		}
	}
}

export const SEGMENT_INSTRUCTIONS = {
	[SEGMENT_INSTRUCTION_TYPE.METRIC_TRANSACTION_COUNT_LAST_30]: METRIC_TRANSACTION_COUNT_LAST_30,
	[SEGMENT_INSTRUCTION_TYPE.METRIC_GMT_COMPARISON]: METRIC_GMT_COMPARISON,
	[SEGMENT_INSTRUCTION_TYPE.FILTER_USERS_WITH_LAST_ACTIVITY]: FILTER_USERS_WITH_LAST_ACTIVITY,
	[SEGMENT_INSTRUCTION_TYPE.SOURCE_PROJECT_USERS]: SOURCE_PROJECT_USERS,
}

export function buildSmartContractInstruction() {
	const segment = SegmentFactory.createSegmentFromInstruction(
		IncludeSmartContractsSegment.INSTRUCTION_NAME,
		IncludeSmartContractsSegment.INSTRUCTION_NAME,
	);
	return segment.addRequiredParam({
		operator: '=',
		valueString: 'false',
	}).buildSegment();
}

export async function publishSegment(
	name,
	network,
	orgId,
	instructions,
	recordId,
	description
) {
	let finalQuery = {
		name: name,
		network: network,
		orgid: orgId,
		queries: instructions
	}

	let result;
	try {
		let response = await fetch(`${URL_DOMAIN}/segment`, {
			method: 'POST',
			credentials: 'omit', // include, *same-origin, omit
			mode: 'cors',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${localStorage.getItem('token')}`,
			},
			body: JSON.stringify(finalQuery),
		});
		result = await response.json();
	} catch (err) {
		return {
			error: err,
			success: false
		}
	}

	return result;
}

export async function setMetricsEnabled(viewname, metricsEnabled) {
	let payload = {
		viewname: viewname,
		metricsEnabled: metricsEnabled
	}

	let response = await fetch(`${URL_DOMAIN}/segment/segment-definition/metrics`, {
		method: 'POST',
		credentials: 'omit', // include, *same-origin, omit
		mode: 'cors',
		headers: {
			'Content-Type': 'application/json',
			Authorization: `Bearer ${localStorage.getItem('token')}`,
		},
		body: JSON.stringify(payload), // body data type must match "Content-Type" header
	});

	const result = await response.json();
	return result;
}

export async function previewSegment(name, network, orgId, instructions, queryId) {
	let finalQuery = {
		name: name,
		network: network,
		orgid: orgId,
		queries: instructions
	}

	if (queryId) {
		finalQuery.queryid = queryId;
	}

	console.log('previewSegment', finalQuery)

	let response = await fetch(`${URL_DOMAIN}/segment/preview`, {
		method: 'POST',
		credentials: 'omit', // include, *same-origin, omit
		mode: 'cors',
		headers: {
			'Content-Type': 'application/json',
			Authorization: `Bearer ${localStorage.getItem('token')}`,
		},
		body: JSON.stringify(finalQuery), // body data type must match "Content-Type" header
	});
	const result = await response.json();
	if (result.body && result.body.queryid && result.body.state !== 'DONE') {
		await Utils.delay(800);
		return await previewSegment(name, network, orgId, instructions, result.body.queryid);
	} else if (result.body && result.body.data) {
		return { walletIds: result.body.data, totalCount: result.body.count, hasMore: result.body.hasmore };
	} else {
		return { walletIds: [], totalCount: 0, hasMore: false }
	}
}

export async function executeSegment(segmentName, network, page, queryId, additionalOptions = { countonly: false, pagesize: 10 }) {
	let data = {
		name: segmentName,
		network: network,
		page: page,
		pagesize: additionalOptions.pagesize,
		table: 'default',
		countonly: additionalOptions.countonly
	}
	let params = `name=${data.name}&network=${data.network}&page=${data.page}&pagesize=${data.pagesize}&table=${data.table}&countonly=${data.countonly}`;
	if (queryId) {
		params += `&queryid=${queryId}`;
	}
	let response = await fetch(`${URL_DOMAIN}/segment?${params}`, {
		method: 'GET',
		credentials: 'omit', // include, *same-origin, omit
		mode: 'cors',
		headers: {
			'Authorization': `Bearer ${localStorage.getItem('token')}`,
			'Access-Control-Allow-Origin': '*',
			'Content-Type': 'application/json'
		},
	});
	let result = await response.json();
	if (result.body && result.body.queryid && result.body.state && result.body.state !== 'DONE') {
		await Utils.delay(1000);
		return await executeSegment(
			segmentName,
			network,
			page,
			queryId ? queryId : result.body.queryid,
			additionalOptions
		);
	} else {
		return result;
	}
}

export async function updateMetricsEnabledToDB(
	id,
	metricsEnabled
)
{
	console.log("updateMetricsEnabledToDB", id, metricsEnabled);
	let response = await fetch(`${URL_DOMAIN}/segments/${id}`, {
		method: 'PATCH',
		credentials: 'omit', // include, *same-origin, omit
		mode: 'cors',
		headers: {
			'Content-Type': 'application/json',
			Authorization: `Bearer ${localStorage.getItem('token')}`,
		},
		body: JSON.stringify({
			metricsEnabled: metricsEnabled
		})
	});
	console.log("updateMetricsEnabledToDB", response.status);
	return response;
	//return await response.json();
}

export async function saveSegmentToDB(
	name,
	network,
	orgId,
	instructions,
	totalCount,
	status,
	description,
	viewName,
	metricsEnabled
) {
	let segment = {
		name: name,
		network: network,
		orgid: orgId,
		queries: instructions,
		status: status || SEGMENT_STATUS.DRAFT,
		description: description || '',
		viewname: viewName || '',
		metricsEnabled: metricsEnabled
	}

	if (totalCount) {
		segment.addressCount = totalCount;
	}
	let response = await fetch(`${URL_DOMAIN}/segments`, {
		method: 'POST',
		credentials: 'omit', // include, *same-origin, omit
		mode: 'cors',
		headers: {
			'Content-Type': 'application/json',
			Authorization: `Bearer ${localStorage.getItem('token')}`,
		},
		body: JSON.stringify(segment), // body data type must match "Content-Type" header
	});
	return await response.json();
}

export async function deleteSegment(segment) {
	if (segment.status === SEGMENT_STATUS.PUBLISHED) {
		let urlParams = `?name=${segment.viewname}&network=${segment.network}`;
		let response = await fetch(`${URL_DOMAIN}/segment${urlParams}`, {
			method: 'DELETE',
			credentials: 'omit', // include, *same-origin, omit
			mode: 'cors',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${localStorage.getItem('token')}`,
			},
		});
		console.log(`mike response ${JSON.stringify(response.status)}`);
		if (response.status !== 200) {
			//throw new Error('An error occurred while deleting the segment');
			console.log('An error occurred while deleting the segment', response);
		}
	}
	await deleteSegmentFromDB(segment.id);
	return {statusCode: 204, success: true};
}

export async function deleteSegmentFromDB(segmentId) {
	let response = await fetch(`${URL_DOMAIN}/segments/${segmentId}`, {
		method: 'DELETE',
		credentials: 'omit', // include, *same-origin, omit
		mode: 'cors',
		headers: {
			'Content-Type': 'application/json',
			Authorization: `Bearer ${localStorage.getItem('token')}`,
		},
	});
	if (response.status !== 204) {
		throw new Error('An error occurred while deleting the segment from the database.');
	}
}

export async function getSegments() {
	try {
		let response = await fetch(`${URL_DOMAIN}/all-segments`, {
			method: 'GET',
			credentials: 'omit', // include, *same-origin, omit
			mode: 'cors',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${localStorage.getItem('token')}`,
			},
		});
		return await response.json();
	} catch (err) {
		return {
			error: err,
			success: false
		}
	}
}

export async function getPublishedSegments() {
	try {
		let response = await fetch(`${URL_DOMAIN}/segment-list`, {
			method: 'GET',
			credentials: 'omit', // include, *same-origin, omit
			mode: 'cors',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${localStorage.getItem('token')}`,
			},
		});
		return await response.json();
	} catch (err) {
		return {
			error: err,
			success: false
		}
	}
}

export async function getSegmentById(segmentId) {
	try {
		let response = await fetch(`${URL_DOMAIN}/segment/${segmentId}`, {
			method: 'GET',
			credentials: 'omit', // include, *same-origin, omit
			mode: 'cors',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${localStorage.getItem('token')}`,
			},
		});
		return await response.json();
	} catch (err) {
		return {
			error: err,
			success: false
		}
	}
}

export async function updateSegment(segmentId, data) {
	try {
		let response = await fetch(`${URL_DOMAIN}/segments/${segmentId}`, {
			method: 'PATCH',
			credentials: 'omit', // include, *same-origin, omit
			mode: 'cors',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${localStorage.getItem('token')}`,
			},
			body: JSON.stringify(data),
		});
		return response;
	} catch (err) {
		return {
			error: err,
			success: false
		}
	}
}

export async function getWalletSegmentInfo(addresses, network, options) {
	let data = {
		addresses: addresses,
		network: network,
		...options,
		includeDataFromEventType: options.includeDataFromEventType ? options.includeDataFromEventType.toString() : ''
	}
	//console.log("DATA: " + JSON.stringify(data))
	if (addresses.length > 0) {
		let response = await fetch(`${URL_DOMAIN}/wallets-overview`, {
			method: 'POST',
			credentials: 'omit', // include, *same-origin, omit
			mode: 'cors',
			body: JSON.stringify(data),
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
			},
		});
		const result = await response.json();
		return result;
	}
}

export async function getWalletSegmentDownload(audience, queryExecutionId, options) {
	let data = {
		audience: audience,
		queryExecutionId: queryExecutionId,
		...options,
		includeDataFromEventType: options.includeDataFromEventType ? options.includeDataFromEventType.toString() : ''
	}
	//console.log("DATA: " + JSON.stringify(data))
	if (audience != null) {
		let response = await fetch(`${URL_DOMAIN}/segment/download`, {
			method: 'POST',
			credentials: 'omit', // include, *same-origin, omit
			mode: 'cors',
			body: JSON.stringify(data),
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
			},
		});
		const result = await response.json();
		return result;
	} else {
		return {
			error: 'No audience provided',
			success: false
		}
	}
}

export async function getWalletSegments(address) {
	let response = await fetch(`${URL_DOMAIN}/segment/wallet/segments/${address}`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
      }
	});
	return await response.json();
}
