import * as Utils from '../utils/Utils';

const URL_DOMAIN = Utils.URL_DOMAIN;

export async function getCustomEvents(countOnly) {
	const query = `&latest=true&limit=10`;
	try {
		let response = await fetch(`${URL_DOMAIN}/metric/event?countOnly=${countOnly}`, {
			method: 'GET',
			credentials: 'omit', // include, *same-origin, omit
			mode: 'cors',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${localStorage.getItem('token')}`,
			},
		});
		return await response.json();
	} catch (err) {
		return {
			error: err,
			success: false
		}
	}
}

export async function getCustomEventTypes() {
	try {
		let response = await fetch(`${URL_DOMAIN}/event-types/`, {
			method: 'GET',
			credentials: 'omit', // include, *same-origin, omit
			mode: 'cors',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${localStorage.getItem('token')}`,
			},
		});
		return await response.json();
	} catch (err) {
		return {
			error: err,
			success: false
		}
	}
}

export async function getCustomEventsSankey(options) {
	try {
		let response = await fetch(`${URL_DOMAIN}/event-sankey/?options=${encodeURIComponent(JSON.stringify(options))}`, {
			method: 'GET',
			credentials: 'omit', // include, *same-origin, omit
			mode: 'cors',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${localStorage.getItem('token')}`,
			},
		});
		const data = await response.json();

		return data.body;
	} catch (err) {
		return {
			error: err,
			success: false
		}
	}
}

export async function getCustomEventByDay() {
	try {
		let response = await fetch(`${URL_DOMAIN}/event/events-by-day`, {
			method: 'GET',
			credentials: 'omit', // include, *same-origin, omit
			mode: 'cors',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${localStorage.getItem('token')}`,
			},
		});
		return await response.json();
	} catch (err) {
		return {
			error: err,
			success: false
		}
	}
}
