import * as Utils from '../utils/Utils';

export async function prioritizeIngestionForView(viewName) {
	const [balanceResult, transactionResult] = await Promise.all([
		prioritizeBalanceIngestion(viewName, ''),
		prioritizeTransactionIngestion(viewName, ''),
	]);

	if (balanceResult.statusCode == 200 && transactionResult.statusCode == 200) {
		return {statusCode: 200, message: 'Ingestion prioritized successfully'};
	}
	return {statusCode: 500, message: 'Error prioritizing ingestion'};
}

export async function prioritizeIngestionForProject(projectId) {
	const [balanceResult, transactionResult] = await Promise.all([
		prioritizeBalanceIngestion('', projectId),
		prioritizeTransactionIngestion('', projectId),
	]);
	if (balanceResult.statusCode == 200 && transactionResult.statusCode == 200) {
		return {statusCode: 200, message: 'Ingestion prioritized successfully'};
	}
	return {statusCode: 500, message: 'Error prioritizing ingestion'};
}

async function prioritizeBalanceIngestion(viewName, projectId) {
	return await prioritizeIngestion('balances', viewName, projectId);
}

async function prioritizeTransactionIngestion(viewName, projectId) {
	return await prioritizeIngestion('transactions', viewName, projectId);
}

export async function prioritizeSmartContractForProject(projectId) {
	const [smartContractResult] = await Promise.all([
		prioritizeSmartContractIngestion('', projectId),
		prioritizeNFTIngestion('', projectId),
		prioritizeTokenIngestion('', projectId),
	]);
	if (smartContractResult.statusCode == 200) {
		return {statusCode: 200, message: 'Ingestion prioritized successfully'};
	}
	return {statusCode: 500, message: 'Error prioritizing ingestion'};
}

async function prioritizeSmartContractIngestion(viewName, projectId) {
	return await prioritizeIngestion('smart-contracts', viewName, projectId);
}

async function prioritizeNFTIngestion(viewName, projectId) {
	return await prioritizeIngestion('nfts', viewName, projectId);
}

async function prioritizeTokenIngestion(viewName, projectId) {
	return await prioritizeIngestion('tokens', viewName, projectId);
}

async function prioritizeIngestion(url, viewName, projectId) {
	let response = {};
	let jsonresponse = {};
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/${url}`, {
			method: 'POST',
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({
				viewName: viewName,
				projectId: projectId
			})
		});
		jsonresponse = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return jsonresponse;
}
