import { SegmentFactory } from '../../../src/segment-builder/segment-factory';
import { TransactionCountSegment } from '../../../src/segment-builder/segments/transaction-count.segment';
import { ProjectUsersSegment } from '../../../src/segment-builder/segments/project-users.source.segment';
import { SubjectType } from '../../../src/segment-builder/segment.abstract';

export class DefaultSegmentBuilder {

	projectId;

	constructor(projectId) {
		this.projectId = projectId;
  	}

	buildDefaultSegments() {
		return [
			this.buildNewUserSegment(),
			this.buildActiveUserSegment(),
			this.buildDormantUserSegment(),
			this.buildAtRiskUserSegment()
		];
	}

	buildSourceSegment() {
		const sourceSegment = SegmentFactory.createSegmentFromInstruction(
			ProjectUsersSegment.INSTRUCTION_NAME,
			0
		).setSubject({
			type: SubjectType.PROJECT.value,
			value: this.projectId
		});
		return sourceSegment.buildSegment();
	}

	buildNewUserSegment() {
		const sourceSegment = this.buildSourceSegment();

		const newUserStartSegment = SegmentFactory.createSegmentFromInstruction(
			TransactionCountSegment.INSTRUCTION_NAME,
			1
		).setSubject({
			type: SubjectType.PROJECT.value,
			value: this.projectId
		}).addRequiredParam({
			operator: ">",
			value: "0"
		}).setTime({
			start: 0,
			end: 7
		}).buildSegment();

		const newUsersEndSegment = SegmentFactory.createSegmentFromInstruction(
			TransactionCountSegment.INSTRUCTION_NAME,
			2
		).setSubject({
			type: SubjectType.PROJECT.value,
			value: this.projectId
		}).addRequiredParam({
			operator: "<",
			value: "1"
		}).setTime({
			start: 8,
			end: 380
		}).buildSegment();

		console.log(`New user segment: ${JSON.stringify(newUserStartSegment)}`);
		return {
			name: 'New Users',
			description: 'Users who have made a transaction in the last 7 days',
			instructions: [sourceSegment, newUserStartSegment, newUsersEndSegment]
		};
	}

	buildActiveUserSegment() {
		const sourceSegment = this.buildSourceSegment();

		const activeUserSegment = SegmentFactory.createSegmentFromInstruction(
			TransactionCountSegment.INSTRUCTION_NAME,
			1
		).setSubject({
			type: SubjectType.PROJECT.value,
			value: this.projectId
		}).addRequiredParam({
			operator: ">",
			value: "0"
		}).setTime({
			start: 0,
			end: 30
		}).buildSegment();

		console.log(`Active user segment: ${JSON.stringify(activeUserSegment)}`);
		return {
			name: 'Active Users',
			description: 'Users who have made a transaction in the last 30 days',
			instructions: [sourceSegment, activeUserSegment]
		};
	}

	buildDormantUserSegment() {
		const sourceSegment = this.buildSourceSegment();
		const dormantUserSegment = SegmentFactory.createSegmentFromInstruction(
			TransactionCountSegment.INSTRUCTION_NAME,
			1
		).setSubject({
			type: SubjectType.PROJECT.value,
			value: this.projectId
		}).addRequiredParam({
			operator: "=",
			value: "0"
		}).setTime({
			start: 0,
			end: 90
		}).buildSegment();

		console.log(`Dormant user segment: ${JSON.stringify(dormantUserSegment)}`);
		return {
			name: 'Dormant Users',
			description: 'Users who have not made a transaction in the last 90 days',
			instructions: [sourceSegment, dormantUserSegment]
		};
	}

	buildAtRiskUserSegment() {
		const sourceSegment = this.buildSourceSegment();
		const atRiskUserStartSegment = SegmentFactory.createSegmentFromInstruction(
			TransactionCountSegment.INSTRUCTION_NAME,
			1
		).setSubject({
			type: SubjectType.PROJECT.value,
			value: this.projectId
		}).addRequiredParam({
			operator: "=",
			value: "0"
		}).setTime({
			start: 0,
			end: 30
		}).buildSegment();

		const atRiskUserEndSegment = SegmentFactory.createSegmentFromInstruction(
			TransactionCountSegment.INSTRUCTION_NAME,
			1
		).setSubject({
			type: SubjectType.PROJECT.value,
			value: this.projectId
		}).addRequiredParam({
			operator: ">",
			value: "0"
		}).setTime({
			start: 30,
			end: 90
		}).buildSegment();

		console.log(`At risk user segment: ${JSON.stringify(atRiskUserStartSegment)}`);
		return {
			name: 'At Risk Users',
			description: 'Users who have not made a transaction in the last 30 days but have made a transaction in the last 90 days',
			instructions: [sourceSegment, atRiskUserStartSegment, atRiskUserEndSegment]
		};
	}
}
