import * as Utils from '../utils/Utils';

const URL_DOMAIN = Utils.URL_DOMAIN;

class CoversionEventsService {
	constructor(conversion_event_id) {
		this.conversion_event_id = conversion_event_id;
		this.info = {};
		this.logs = [];
	}

	async getConversionEventById() {
		let jsonresponse = {};
		try {
			const response = await fetch(`${URL_DOMAIN}/conversion-events/${this.conversion_event_id}`, {
				method: 'GET',
				withCreditentials: true,
				credentials: 'omit',
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Access-Control-Allow-Origin': '*',
					'Content-Type': 'application/json'
				}
			});
			jsonresponse = await response.json();
			return jsonresponse;
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
			return {
				error: true,
				message: err.message
			}
		}
	}

	async getConverionsBySource() {
		let jsonresponse = {};
		try {
			const response = await fetch(`${URL_DOMAIN}/conversion-events/${this.conversion_event_id}/conversion-event-custom/UTM_BY_SOURCE`, {
				method: 'GET',
				withCreditentials: true,
				credentials: 'omit',
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Access-Control-Allow-Origin': '*',
					'Content-Type': 'application/json'
				}
			});
			jsonresponse = await response.json();
			return jsonresponse;
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
			return {
				error: true,
				message: err.message
			}
		}
	}

	async getConverionsByContent() {
		let jsonresponse = {};
		try {
			const response = await fetch(`${URL_DOMAIN}/conversion-events/${this.conversion_event_id}/conversion-event-custom/UTM_BY_CONTENT`, {
				method: 'GET',
				withCreditentials: true,
				credentials: 'omit',
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Access-Control-Allow-Origin': '*',
					'Content-Type': 'application/json'
				}
			});
			jsonresponse = await response.json();
			return jsonresponse;
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
			return {
				error: true,
				message: err.message
			}
		}
	}

	async getConverionsByDay() {
		let jsonresponse = {};
		try {
			const response = await fetch(`${URL_DOMAIN}/conversion-events/${this.conversion_event_id}/conversion-event-custom/UTM_CONVERSION_BY_DAY`, {
				method: 'GET',
				withCreditentials: true,
				credentials: 'omit',
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Access-Control-Allow-Origin': '*',
					'Content-Type': 'application/json'
				}
			});
			jsonresponse = await response.json();
			return jsonresponse;
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
			return {
				error: true,
				message: err.message
			}
		}
	}

	async getConversionTimeFrames() {
		let jsonresponse = {};
		try {
			const response = await fetch(`${URL_DOMAIN}/conversion-events/${this.conversion_event_id}/conversion-event-custom/UTM_CONVERSION_LAST_DAYS`, {
				method: 'GET',
				withCreditentials: true,
				credentials: 'omit',
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Access-Control-Allow-Origin': '*',
					'Content-Type': 'application/json'
				}
			});
			jsonresponse = await response.json();
			return jsonresponse;
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
			return {
				error: true,
				message: err.message
			}
		}
	}

	async getConversionEventInfo() {
		let jsonresponse = {};
		try {
			const response = await fetch(`${URL_DOMAIN}/conversion-events/${this.conversion_event_id}/conversion-event-info`, {
				method: 'GET',
				withCreditentials: true,
				credentials: 'omit',
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Access-Control-Allow-Origin': '*',
					'Content-Type': 'application/json'
				}
			});
			jsonresponse = await response.json();
			return jsonresponse;
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
			return {
				error: true,
				message: err.message
			}
		}
	}

	async getConversionEventLogs() {
		let jsonresponse = {};
		try {
			const response = await fetch(`${URL_DOMAIN}/conversion-events/${this.conversion_event_id}/conversion-event-logs`, {
				method: 'GET',
				withCreditentials: true,
				credentials: 'omit',
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Access-Control-Allow-Origin': '*',
					'Content-Type': 'application/json'
				}
			});
			jsonresponse = await response.json();
			return jsonresponse;
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
			return {
				error: true,
				message: err.message
			}
		}
	}
}
export { CoversionEventsService };

