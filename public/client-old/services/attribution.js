import * as metrics from './metrics';

class AttributionService {
	constructor(specificCampaign) {
		this.campaignId = specificCampaign;
	}


	async getTotalViews() {
		let res = await metrics.getTotalViews(this.campaignId);
		let count = res.body[0].data;
		return count;
	}

	async getTotalConnections() {
		let res = await metrics.getUTMConnectionCount(this.campaignId);
		let count = res.body[0].data;
		return count;
	}

	async getNewEngagers(projectid) {
		let res = await metrics.getUTMNewEngagers(this.campaignId, projectid);
		let count = res.body[0].data;
		return count;
	}

}

export { AttributionService };

