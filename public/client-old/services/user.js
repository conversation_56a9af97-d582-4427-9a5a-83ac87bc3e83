
import { local } from 'd3';
import * as Utils from '../utils/Utils';
import * as orgService from './organization';

const URL_DOMAIN = Utils.URL_DOMAIN;

export async function setUserInfo(userInfo) {
  localStorage.setItem('userInfo', JSON.stringify(userInfo));
  localStorage.setItem('userOrgId', userInfo.organizationId);
  localStorage.setItem(
    'firstName',
    userInfo.firstName != null ? userInfo.firstName : 'Profile',
  );
  localStorage.setItem(
    'lastName',
    userInfo.lastName != null ? userInfo.lastName : '',
  );
  localStorage.setItem(
    'email',
    userInfo.email != null ? userInfo.email : '',
  );
  localStorage.setItem('avatarColors', userInfo.avatarColors);

  if (userInfo.avatarColors == null) {
    var colorList = [
      { from: 'from-ralocean-700', to: 'to-ralocean-200' },
      { from: 'from-ralocean-500', to: 'to-ralocean-300' },
      { from: 'from-ralpurple-300', to: 'to-ralapple-500' },
      { from: 'from-ralpurple-500', to: 'to-ralgranite-500' },
      { from: 'from-ralapple-500', to: 'to-ralocean-500' },
    ];

    var selectedColor = Math.floor(
      Math.random() * (colorList.length - 0) + 0,
    );
    let avatarResult = await updateUserAvatar(
      userInfo.id,
      JSON.stringify(colorList[selectedColor]),
    );
    localStorage.setItem(
      'avatarColors',
      JSON.stringify(colorList[selectedColor]),
    );
  }
  if (window.amplitude) {
    amplitude.getInstance().setUserId(userInfo.id);
    amplitude.getInstance().setGroup('orgId', userInfo.organizationId);
    amplitude.getInstance().logEvent('LOGIN');
  }

  let orgResult = await orgService.getOrgById(parseInt(userInfo.organizationId));
  localStorage.setItem('orgName', orgResult.name != null ? orgResult.name : false);
  localStorage.setItem('devEnabled', orgResult.dev != null ? orgResult.dev : false);
  localStorage.setItem('betaEnabled', orgResult.dev != null ? orgResult.beta : false);
  localStorage.setItem('selfService', orgResult.selfService != null ? orgResult.selfService : false);
  localStorage.setItem('offChainEnabled', orgResult.attribution != null ? orgResult.attribution : false);
}

export async function setUserInfoSignin(userInfo) {

	//console.log('USER INFO ' + JSON.stringify(userInfo));
	localStorage.setItem('userInfo', JSON.stringify(userInfo));
	localStorage.setItem('userOrgId', userInfo.organizationId);
	localStorage.setItem(
		'firstName',
		userInfo.firstName != null ? userInfo.firstName : 'Profile',
	);
	localStorage.setItem(
		'lastName',
		userInfo.lastName != null ? userInfo.lastName : '',
	);
	localStorage.setItem(
		'email',
		userInfo.email != null ? userInfo.email : '',
	);
	window.statsig.updateUser({
		email: userInfo.email,
	}).then(() => {
		var event = new Event('statsigUpdate');
		document.dispatchEvent(event);
	})
	localStorage.setItem('avatarColors', userInfo.avatarColors);

	if (userInfo.avatarColors == null) {
		var colorList = [
			{ from: 'from-ralocean-700', to: 'to-ralocean-200' },
			{ from: 'from-ralocean-500', to: 'to-ralocean-300' },
			{ from: 'from-ralpurple-300', to: 'to-ralapple-500' },
			{ from: 'from-ralpurple-500', to: 'to-ralgranite-500' },
			{ from: 'from-ralapple-500', to: 'to-ralocean-500' },
		];

		var selectedColor = Math.floor(
			Math.random() * (colorList.length - 0) + 0,
		);
		let avatarResult = await updateUserAvatar(
			userInfo.id,
			JSON.stringify(colorList[selectedColor]),
		);
		localStorage.setItem(
			'avatarColors',
			JSON.stringify(colorList[selectedColor]),
		);
	}
	amplitude.getInstance().setUserId(userInfo.id);
	amplitude.getInstance().setGroup('orgId', userInfo.organizationId);
	let support = false;
	for (let i = 0; i < userInfo.roles.length; i++) {
		if (userInfo.roles[i] == 'raleon-support') {
			support = true;
			amplitude.getInstance().setGroup('Role', 'raleon-support');
		}
	}

	localStorage.setItem('raleonSupport', support.toString());

	amplitude.getInstance().setGroup('email', userInfo.email);
	amplitude.getInstance().logEvent('LOGIN');

	const orgService = await import('../services/organization.js');
	let orgResult = await orgService.getOrgById(parseInt(userInfo.organizationId));
	localStorage.setItem('orgName', orgResult.name != null ? orgResult.name : 'Amazing Project');
	localStorage.setItem('devEnabled', orgResult.dev != null ? orgResult.dev : false);
	localStorage.setItem('betaEnabled', orgResult.beta != null ? orgResult.beta : false);
	localStorage.setItem('selfService', orgResult.selfService != null ? orgResult.selfService : false);
	localStorage.setItem('snippetVerified', orgResult.snippetVerified != null ? orgResult.snippetVerified : false);
	localStorage.setItem('selfServiceUpgradeRequested', orgResult.selfServiceUpgradeRequested != null ? orgResult.selfServiceUpgradeRequested : false);
	localStorage.setItem('onboardingComplete', orgResult.onboardingComplete != null ? orgResult.onboardingComplete : false);
	localStorage.setItem('offChainEnabled', orgResult.attribution != null ? orgResult.attribution : false);

	amplitude.getInstance().setGroup('orgName', orgResult.name);
	amplitude.getInstance().setGroup('betaEnabled', orgResult.beta);
	amplitude.getInstance().setGroup('devEnabled', orgResult.dev);
	console.log(`mike orgResult: ${JSON.stringify(orgResult)}`)
	let iData = JSON.parse(orgResult.interactionData);
	if (iData != null) {
		localStorage.setItem('interactionData', orgResult.interactionData);
	} else {
		await orgService.resetOrgInteractions(parseInt(userInfo.organizationId));
		let orgResultUpdate = await orgService.getOrgById(parseInt(userInfo.organizationId));
		localStorage.setItem('interactionData', orgResultUpdate.interactionData);
		iData = JSON.parse(orgResultUpdate.interactionData);
	}

	localStorage.setItem('custom_events', iData.org.custom_events != null ? true : false);
	localStorage.setItem('audiences', iData.org.audiences != null ? true : false);
	localStorage.setItem('teammates', iData.org.teammates != null ? true : false);
}

export async function userLoginToken(sessionToken) {
	let response = {};
	let jsonresponse = {};
	try {
		response = await fetch(`${URL_DOMAIN}/users/login/token`, {
			method: 'POST',
			credentials: 'omit',
			mode: 'cors',
			headers: {
				'Content-Type': 'application/json',
				'Access-Control-Allow-Origin': '*',
				'Authorization': `Bearer ${sessionToken}`
			},
		});
		jsonresponse = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return jsonresponse;
}

export async function userLogin(email, password) {
  let response = {};
  let jsonresponse = {};
  let loginPayload = {
    email: email,
    password: password
  };
  try {
    response = await fetch(`${URL_DOMAIN}/users/login`, {
      method: 'POST',
      credentials: 'omit', // include, *same-origin, omit
      mode: 'cors',
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      },
      body: JSON.stringify(loginPayload), // body data type must match "Content-Type" header
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
  }
  return jsonresponse;
}

export async function createUser() {
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/users`, {
      method: 'POST',
      credentials: 'omit', // include, *same-origin, omit
      mode: 'cors',
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      },
      body: JSON.stringify({}), // body data type must match "Content-Type" header
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
  }
  return jsonresponse;
}

export async function isLoggedIn() {
  //There may be a better way to do this, but here we just check if the call is successful
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/users/who-am-i`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      mode: 'cors',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
	localStorage.removeItem('token');
    console.log("Error: " + JSON.stringify(err));
    return false;
  }

  return true;
}

export async function getUserInfo() {
  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${URL_DOMAIN}/users/who-am-i`, {
      method: 'GET',
      withCreditentials: true,
      credentials: 'omit',
      mode: 'cors',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json',
		'ngrok-skip-browser-warning': true
      }
    });
    jsonresponse = await response.json();
  } catch (err) {
	localStorage.removeItem('token');
    console.log("Error: " + JSON.stringify(err));
    return {
      error: true,
      message: 'Error getting user info'
    }
  }
  //Schema for this should include "id", "organizationId" (Why is that first letter capital?), email, firstname, etc
  return jsonresponse;
}


export async function finishResetPassword(resetKey, password, confirmPassword) {
  let response;
  try {
    response = await fetch(`${URL_DOMAIN}/users/reset-password/finish`, {
      method: 'PUT',
      mode: 'cors',
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        resetKey,
        password,
        confirmPassword
      })
    });
    response = await response.json();
    console.log(`resetting pw: ${response}`);
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
  }
  return response;
}

export async function initResetPassword(email) {
  let response;
  try {
    response = await fetch(`${URL_DOMAIN}/users/reset-password/init`, {
      method: 'POST',
      mode: 'cors',
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ email })
    });
    response = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
  }
  return response;
}

export async function updateUserFLName(userId, firstName, lastName) {
  var data = {
    firstName: firstName,
    lastName: lastName
  };

  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${Utils.URL_DOMAIN}/users/${userId}`, {
      method: 'PATCH',
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        "accept": "*/*",
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        "Content-Type": "application/json"
      },
      body: JSON.stringify(data),
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
  }
  return jsonresponse;
}

export async function updateUserAvatar(userId, colors) {
  var data = {
    avatarColors: colors
  };

  let response = {};
  let jsonresponse = {};
  try {
    response = await fetch(`${Utils.URL_DOMAIN}/users/${userId}`, {
      method: "PATCH",
      withCreditentials: true,
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Access-Control-Allow-Origin': '*',
        "accept": "*/*",
        "Content-Type": "application/json"
      },
      body: JSON.stringify(data),
    });
    jsonresponse = await response.json();
  } catch (err) {
    console.log("Error: " + JSON.stringify(err));
  }
  return jsonresponse;
}
