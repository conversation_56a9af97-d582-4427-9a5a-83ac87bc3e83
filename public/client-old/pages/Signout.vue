<template>
   <main class="bg-white">
     <div class="relative flex">
     </div>
   </main>
 </template>

 <script>

 export default {
   name: 'SignOut',
   data() {
     return {
       email: '',
       password: '',
       loginError: false,
     };
   },
   async mounted() {
	localStorage.removeItem('areCampaigns', '');
      localStorage.removeItem('token');
      this.$router.push('/chats');
     const userService = await import('../services/user.js');
   },
   methods: {
   },
 };
 </script>
