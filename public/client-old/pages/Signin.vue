<template>
	<main class="bg-white">
		<div class="relative flex">
			<!-- Content -->
			<div class="w-full md:w-1/2">
				<div class="min-h-screen h-full flex flex-col after:flex-1 z-50">
					<div class="flex-1">
						<div class="
	                flex
	                items-center
	                justify-between
	                h-16
	                px-4
	                sm:px-6
	                lg:px-8
	              ">
							<!-- Logo -->
							<router-link class="block" to="/">
								<img src="../images/raleon-logo-whitebg.jpg" width="64" height="64" />
							</router-link>
						</div>
					</div>
					<div class="max-w-sm mx-auto px-4 py-8">
						<h1 class="text-3xl text-slate-800 font-bold mb-6">
							Welcome, Fren, You Rock! 🏆
						</h1>
						<!-- Form -->
						<div class="flex items-center p-4 mb-4 w-full text-gray-500 bg-red-100 border border-red-400 rounded-lg shadow"
							role="alert" v-if="loginError">
							<div
								class="inline-flex flex-shrink-0 justify-center items-center w-8 h-8 text-red-500 bg-red-100 rounded-lg text-red-700">
								<svg aria-hidden="true" class="w-5 h-5" fill="#f56565" viewBox="0 0 20 20"
									xmlns="http://www.w3.org/2000/svg">
									<path fill-rule="evenodd"
										d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
										clip-rule="evenodd"></path>
								</svg>
								<span class="sr-only">Error icon</span>
							</div>
							<div class="ml-3 text-sm font-normal">Your username and/or password is incorrect.</div>
						</div>

						<form @submit.prevent="userLogin">
							<div class="space-y-4">
								<div>
									<label class="block text-sm font-medium mb-1" for="email">Email Address</label>
									<input id="email" class="
										form-input
										w-full
										focus:bg-gradient-to-r
										focus:from-ralpurple-500
										focus:to-ralocean-500
										" type="email" v-model="email" />
								</div>
								<div>
									<label class="block text-sm font-medium mb-1" for="password">Password</label>
									<input id="password" class="form-input w-full" type="password" autoComplete="on"
										v-model="password" />
								</div>
							</div>
							<div class="flex items-center justify-between mt-6">
								<button id="login-button" class="
	                    btn
	                    bg-indigo-500
	                    hover:bg-indigo-600
	                    text-white
	                    w-full
	                  " type="submit" @click.stop="userLogin">
									Sign In
								</button>
							</div>
							<div class="mt-3 text-center">
								<router-link class="text-sm underline hover:text-indigo-600" to="/reset-password">
									Forgot Password?
								</router-link>
							</div>
						</form>
						<!-- Footer -->
						<div class="pt-5 mt-3 border-t border-slate-200">
							<div class="text-sm">
								Have a question? Message us on
								<a href="https://msng.link/o/?RaleonHQ=tg"
									class="font-medium text-indigo-500 hover:text-indigo-600" target="_blank">Telegram</a>
								so we can connect and help!
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="hidden md:block absolute top-0 bottom-0 right-0 md:w-1/2" aria-hidden="true">
				<img class="object-cover object-center w-full h-full" src="../images/r-login-bg.png" width="760"
					height="1024" alt="Authentication" />
			</div>
		</div>
	</main>
</template>

<script>

import ResetPassword from './ResetPassword.vue'
import * as Utils from '../utils/Utils';
import * as userService from '../services/user.js'
const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	props: { redirect: String },
	name: 'Signin',
	data() {
		return {
			email: '',
			password: '',
			loginError: false,
		};
	},
	async mounted() {
		const sessionToken = this.$route.query?.session_id;
		if (sessionToken) {
			const {token} = await userService.userLoginToken(sessionToken);

			localStorage.setItem('token', token);
			let userInfo = await userService.getUserInfo();
			await userService.setUserInfoSignin(userInfo);
			this.$router.push('/onboard');
			return;
		}

		console.log("REDIRECT", this.redirect);
		let currentToken = localStorage.getItem('token');
		console.log("currentToken", currentToken)
		if (currentToken != undefined) {
			// lets verify this token is valid
			let userInfo = await userService.getUserInfo();
			if (
				(userInfo != undefined && userInfo.error == true) ||
				userInfo.error != undefined
			) {
				//We need to reset the token and stay here
				localStorage.removeItem('token');
			} else {
				//We are good to go
				if(this.redirect == 'docs') {
					this.docLogin();
				}
				else {
					this.$router.push('/chats');
				}

			}
		}
	},
	methods: {
		async docLogin() {
			let response = {};
			let jsonresponse = {};
			try {
				console.log("Trying request to /users/doc-login", `${URL_DOMAIN}/users/doc-login`)
				response = await fetch(`${URL_DOMAIN}/users/doc-login`, {
					method: 'GET',
					withCreditentials: true,
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Access-Control-Allow-Origin': '*',
						'Content-Type': 'application/json'
					}
				});
				console.log("Response from /users/doc-login", response)
				jsonresponse = await response.json();
				console.log("DOCS DOCS DOCS", jsonresponse)
				if(jsonresponse.docs_url)
				{
					this.dev_doc_url = `${jsonresponse.docs_url}`;
					window.location.replace(this.dev_doc_url);
				}

				console.log("DOCS DOCS DOCS", this.dev_doc_url);
			} catch (err) {
				console.log("Error: " + JSON.stringify(err));
			}
		},
		async userLogin() {
			this.loginError = false;
			const loginResult = await userService.userLogin(
				this.email,
				this.password,
			);
			console.log(loginResult);
			if (loginResult.token) {
				localStorage.setItem('token', loginResult.token);
				let userInfo = await userService.getUserInfo();
				await userService.setUserInfoSignin(userInfo);
				if(this.redirect == 'docs') {
					this.docLogin();
				}
				else {
					this.$router.push('/chats');
				}
			} else {
				this.loginError = true;
			}
		},
		getCookie(name) {
			console.log(`cookies: ${document.cookie}`)
			const value = `; ${document.cookie}`;
			const parts = value.split(`; ${name}=`);
			if (parts.length === 2) return parts.pop().split(';').shift();
		}
	},
};
</script>
