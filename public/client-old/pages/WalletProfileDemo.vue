<template>
  <div class="flex h-screen overflow-hidden">

    <!-- Sidebar -->
    <Sidebar :sidebarOpen="sidebarOpen" @close-sidebar="sidebarOpen = false" />

    <!-- Content area -->
    <div class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">

      <!-- Site header -->
      

      <main>
        <!-- Avatar -->
        <div class="relative h-64 bg-slate-200">
          <img class="object-cover h-full w-full" src="../images/profile_banner.png" width="979" height="230" alt="Profile background" />

          <div class="absolute top-0 left-0 px-6 py-4 mt-3">
            <img class="rounded-lg drop-shadow border border-ralpurple-700" src="../images/web3-wallet-profile.png" width="128" height="128" />
            <h4 class="mb-3 mt-4 text-normal font-semibold tracking-tight text-ralcloud-500">{{$route.params.address}}</h4>
            <button class="btn-xs border-slate-200 hover:border-slate-400 text-ralcloud-500 hover:text-ralcloud-700">Nickname</button>
          </div>
        </div>

        <div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">
          <!-- Page header -->
          <div class="sm:flex sm:justify-between sm:items-center mb-8">

            <!-- Left: Title -->
            <div class="mb-4 sm:mb-0">
              <div class="flex flex-wrap justify-center sm:justify-start space-x-4">
                <div class="flex items-center">
                  😁
                  <span class="text-sm font-medium whitespace-nowrap text-slate-500 ml-2 mr-2">Web3 Power User</span>
                    <Tooltip size="lg" bg="dark" position="right">
                      <div class="text-sm font-medium text-slate-200">This is an estimate based on Raleon's Insights.</div>
                    </Tooltip>

                  <div class="text-xs inline-flex font-medium bg-slate-300 text-granite-600 rounded-full text-center px-2.5 py-1 ml-3">DEX User</div>

                  <div class="text-xs inline-flex font-medium bg-slate-300 text-granite-600 rounded-full text-center px-2.5 py-1 ml-3">Casual NFT Collector</div>

                  <div class="text-xs inline-flex font-medium bg-slate-300 text-granite-600 rounded-full text-center px-2.5 py-1 ml-3">Loyal</div>
                  <button class="btn-xs border-slate-200 hover:border-slate-300 text-ralpurple-500 ml-3">Add tag</button>
                </div>
              </div>
            </div>

            <!-- Right: Actions -->
            <!--
            <div class="grid grid-flow-col sm:auto-cols-max justify-start sm:justify-end gap-2">
              <button class="btn bg-indigo-500 hover:bg-indigo-600 text-white">Add to Campaign</button>
            </div>
            -->

          </div>

          <div class="relative mb-8">
            <div class="absolute bottom-0 w-full h-px bg-slate-200" aria-hidden="true"></div>
            <ul class="relative text-sm font-medium flex flex-nowrap -mx-4 sm:-mx-6 lg:-mx-8 overflow-x-scroll no-scrollbar" id="walletTabs" data-tabs-toggle="#walletTabContent" role="tablist">
              <li class="mr-6 last:mr-0 first:pl-4 sm:first:pl-6 lg:first:pl-8 last:pr-4 sm:last:pr-6 lg:last:pr-8">
                <a class="block pb-3 text-indigo-500 whitespace-nowrap border-b-2 border-indigo-500" href="#0" id="overview-tab" data-tabs-target="#overviewTab" role="tab">Overview</a>
              </li>
              <li class="mr-6 last:mr-0 first:pl-4 sm:first:pl-6 lg:first:pl-8 last:pr-4 sm:last:pr-6 lg:last:pr-8">
                <a class="block pb-3 text-slate-500 hover:text-slate-600 whitespace-nowrap hover:border-b-2 hover:border-indigo-500" href="#0" id="community-tab" data-tabs-target="#communityTab" role="tab">Community Activity</a>
              </li>
              <li class="mr-6 last:mr-0 first:pl-4 sm:first:pl-6 lg:first:pl-8 last:pr-4 sm:last:pr-6 lg:last:pr-8">
                <a class="block pb-3 text-slate-500 hover:text-slate-600 whitespace-nowrap hover:border-b-2 hover:border-indigo-500" href="#0">Audiences</a>
              </li>
              <li class="mr-6 last:mr-0 first:pl-4 sm:first:pl-6 lg:first:pl-8 last:pr-4 sm:last:pr-6 lg:last:pr-8">
                <a class="block pb-3 text-slate-500 hover:text-slate-600 whitespace-nowrap hover:border-b-2 hover:border-indigo-500" href="#0">Identities</a>
              </li>
            </ul>
          </div>

          <!-- Cards -->
          <div class="grid grid-cols-12 gap-6" id="walletTabContent">

            <!-- QUICK metrics along the top -->
            <ProfileCurrentBalance :walletAddress="$route.params.address" infoLabel="Based on ETH" />

            <QuickMetric title="Revenue Value" amount="$1,532" infoLabel="Money earned for the project" isLoaded="true" />

            <QuickMetric title="User Status" amount="Active User" isLoaded="true" />

            <QuickMetric title="User Type" amount="Active - top 5%" isLoaded="true" />

            <ProfileSummary :walletAddress="$route.params.address" />

            <ProfilePopularDapps :walletAddress="$route.params.address" />

            <ProfileEventHistory :walletAddress="$route.params.address" />

            <ProfileCurrentBalanceByType :walletAddress="$route.params.address" />

            <ProfileActivityByHour :walletAddress="$route.params.address" />

          </div>
        </div>
      </main>

    </div>

  </div>
</template>

<script>
import { ref } from 'vue'

import Datepicker from '../components/Datepicker.vue'
import FilterButton from '../components/DropdownFilter.vue'
import QuickMetric from '../components/QuickMetric.vue'
import Tooltip from '../components/Tooltip.vue'
import Header from '../partials/Header.vue'
import Sidebar from '../partials/Sidebar.vue'

  import ProfileActivityByHour from '../partials/analytics/Wallet/ProfileActivityByHour.vue'
import ProfileAllTimeActivityByDapp from '../partials/analytics/Wallet/ProfileAllTimeActivityByDapp.vue'
import ProfileAllTimeActivityByNetwork from '../partials/analytics/Wallet/ProfileAllTimeActivityByNetwork.vue'
import ProfileAssetsHeld from '../partials/analytics/Wallet/ProfileAssetsHeld.vue'
import ProfileCurrentBalance from '../partials/analytics/Wallet/ProfileCurrentBalance.vue'
import ProfileCurrentBalanceByType from '../partials/analytics/Wallet/ProfileCurrentBalanceByType.vue'
import ProfileEventHistory from '../partials/analytics/Wallet/ProfileEventHistory.vue'
import ProfilePopularDAO from '../partials/analytics/Wallet/ProfilePopularDAO.vue'
import ProfilePopularDapps from '../partials/analytics/Wallet/ProfilePopularDapps.vue'
import ProfileRelatedWallets from '../partials/analytics/Wallet/ProfileRelatedWallets.vue'
import ProfileSummary from '../partials/analytics/Wallet/ProfileSummary.vue'
import ProfileTokenHoldDuration from '../partials/analytics/Wallet/ProfileTokenHoldDuration.vue'
import ProfileTokenInflow from '../partials/analytics/Wallet/ProfileTokenInflow.vue'
import ProfileTokenOutflow from '../partials/analytics/Wallet/ProfileTokenOutflow.vue'

  export default {
    name: 'WalletProfile',
    components: {
      Sidebar,
      Header,
      Datepicker,
      FilterButton,
      Tooltip,
      QuickMetric,
      ProfileAllTimeActivityByNetwork,
      ProfileCurrentBalanceByType,
      ProfileCurrentBalance,
      ProfileActivityByHour,
      ProfileSummary,
      ProfileRelatedWallets,
      ProfilePopularDAO,
      ProfilePopularDapps,
      ProfileAssetsHeld,
      ProfileAllTimeActivityByDapp,
      ProfileTokenHoldDuration,
      ProfileTokenOutflow,
      ProfileTokenInflow,
      ProfileEventHistory
    },
    props: ['address'],
    setup() {

    const sidebarOpen = ref(true)

      return {
        sidebarOpen
      }
    }
  }
</script>
