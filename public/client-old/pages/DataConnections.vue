﻿<template>
	<div class="flex h-screen overflow-hidden">
		<!-- Sidebar -->
		<Sidebar :sidebarOpen="sidebarOpen" @close-sidebar="sidebarOpen = false" />

		<!-- Content area -->
		<div class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
			<!-- Site header -->

			<main>
				<div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">
					<!-- Page header -->
					<div class="sm:flex sm:justify-between sm:items-center mb-8">
						<!-- Left: Title -->
						<div class="mb-4 sm:mb-0">
							<h1 class="text-2xl md:text-3xl text-slate-800 font-bold">
								Projects ✨
							</h1>
						</div>

						<!-- Right: Actions  -->
						<div class="
							grid grid-flow-col
							sm:auto-cols-max
							justify-start
							sm:justify-end
							gap-2
						">
							<div class="m-1.5">
								<button v-if="isAdmin()" class="
									btn
									bg-indigo-500
									hover:bg-indigo-600
									text-white
									disabled:bg-indigo-600
									disabled:text-slate-400
									disabled:cursor-not-allowed
									mr-2
									disabled" aria-controls="add-data-modal" @click.stop="openProjectShareModal()">
									<svg class="w-4 h-4 fill-current opacity-50 shrink-0" viewBox="0 0 16 16">
										<path
											d="M15 7H9V1c0-.6-.4-1-1-1S7 .4 7 1v6H1c-.6 0-1 .4-1 1s.4 1 1 1h6v6c0 .6.4 1 1 1s1-.4 1-1V9h6c.6 0 1-.4 1-1s-.4-1-1-1z" />
									</svg>
									<span class="hidden xs:block ml-2">Share Project</span>
								</button>
								<button class="
								btn
								bg-indigo-500
								hover:bg-indigo-600
								text-white
								disabled:bg-indigo-600
								disabled:text-slate-400
								disabled:cursor-not-allowed
								mr-2
							" aria-controls="add-data-modal" @click.stop="setupEventStreamProjectModalOpen = true">
									<svg v-if="!isCreatingEventStream" class="w-4 h-4 fill-current opacity-50 shrink-0"
										viewBox="0 0 16 16">
										<path
											d="M15 7H9V1c0-.6-.4-1-1-1S7 .4 7 1v6H1c-.6 0-1 .4-1 1s.4 1 1 1h6v6c0 .6.4 1 1 1s1-.4 1-1V9h6c.6 0 1-.4 1-1s-.4-1-1-1z" />
									</svg>
									<svg v-if="isCreatingEventStream"
										class="animate-spin w-4 h-4 fill-current shrink-0 mr-2" viewBox="0 0 16 16">
										<path
											d="M8 16a7.928 7.928 0 01-3.428-.77l.857-1.807A6.006 6.006 0 0014 8c0-3.309-2.691-6-6-6a6.006 6.006 0 00-5.422 8.572l-1.806.859A7.929 7.929 0 010 8c0-4.411 3.589-8 8-8s8 3.589 8 8-3.589 8-8 8z" />
									</svg>
									<span class="xs:block ml-2">Setup Event Stream Users</span>
								</button>
								<button class="
									btn
									bg-indigo-500
									hover:bg-indigo-600
									text-white
									disabled:bg-indigo-600
									disabled:text-slate-400
									disabled:cursor-not-allowed
									disabled" aria-controls="add-data-modal" @click.stop="addProjectModelOpen = true">
									<svg class="w-4 h-4 fill-current opacity-50 shrink-0" viewBox="0 0 16 16">
										<path
											d="M15 7H9V1c0-.6-.4-1-1-1S7 .4 7 1v6H1c-.6 0-1 .4-1 1s.4 1 1 1h6v6c0 .6.4 1 1 1s1-.4 1-1V9h6c.6 0 1-.4 1-1s-.4-1-1-1z" />
									</svg>
									<span class="hidden xs:block ml-2">Add Project</span>
								</button>

								<!-- MIKE Setup Event Stream Users Modal -->
								<ModalBasic
									id="event-stream-project-modal"
									:modalOpen="setupEventStreamProjectModalOpen"
									@close-modal="setupEventStreamProjectModalOpen = false"
									title="Setup Event Stream Users">
									<div class="px-5 py-4">
										<div class="text-sm mb-5">
											<form>
												<div class="space-y-4">
													<div>
														<label class="block text-sm font-medium mb-1" for="name">Name</label>
														<input
															id="wallet-address"
															class="form-input w-full"
															type="text"
															placeholder="Type something you will recognize..."
															v-model="projectName"
														/>
													</div>
													<div v-if="applications && applications.length > 0">
														<label
															class="block text-sm font-medium mb-1"
															for="app-list"
														>Application</label>
														<DropdownFull
															:clearOnClick="true"
															:options="applications"
															:selectedId="applicationId"
															@change="projectApplicationChanged"
														/>
													</div>
													<div class="flex w-124">
														<div class="flex items-center"></div>
													</div>
												</div>
											</form>
										</div>
									</div>
									<!-- Modal footer -->
									<div class="px-5 py-4 border-t border-slate-200">
										<div class="flex flex-wrap justify-end space-x-2">
											<button
												class="btn-sm border-slate-200 hover:border-slate-300 text-slate-600"
												@click.stop="() => { projectName = ''; setupEventStreamProjectModalOpen = false; }">
												Cancel
											</button>
											<button
												class="btn-sm bg-indigo-500 hover:bg-indigo-600 text-white disabled:bg-indigo-600 disabled:text-slate-400 disabled:cursor-not-allowed"
												:disabled="isCreatingEventStream"
												@click="setupEventStreamProject()">
												<svg v-if="!isCreatingEventStream" class="w-4 h-4 fill-current opacity-50 shrink-0" viewBox="0 0 16 16">
													<path d="M15 7H9V1c0-.6-.4-1-1-1S7 .4 7 1v6H1c-.6 0-1 .4-1 1s.4 1 1 1h6v6c0 .6.4 1 1 1s1-.4 1-1V9h6c.6 0 1-.4 1-1s-.4-1-1-1z" />
												</svg>
												<svg v-if="isCreatingEventStream" class="animate-spin w-4 h-4 fill-current shrink-0 mr-2" viewBox="0 0 16 16">
													<path d="M8 16a7.928 7.928 0 01-3.428-.77l.857-1.807A6.006 6.006 0 0014 8c0-3.309-2.691-6-6-6a6.006 6.006 0 00-5.422 8.572l-1.806.859A7.929 7.929 0 010 8c0-4.411 3.589-8 8-8s8 3.589 8 8-3.589 8-8 8z" />
												</svg>
												<span class="xs:block ml-2">Create Event Stream Project</span>
											</button>
										</div>
									</div>
								</ModalBasic>

								<!-- Start Add Data -->
								<ModalBasic id="add-data-modal" :modalOpen="addDataModelOpen"
									@close-modal="addDataModelOpen = false" title="Add New Data Connection">
									<!-- Modal content -->
									<div class="px-5 py-4">
										<div class="mb-5">
											<div>
												<label class="block text-sm font-medium mb-1" for="name">Name of Data
													Connection</label>
												<input id="wallet-address" class="form-input w-full" type="text"
													placeholder="Type something you will recognize..."
													v-model="activeData.name" />
											</div>
										</div>

										<div class="text-sm mb-5">
											<form>
												<div class="space-y-4">
													<label class="block text-sm font-medium mb-1" for="role">What Type
														of Data Do You Want to Add?</label>
													<select id="role" class="form-select w-full"
														v-model="activeData.type">
														<option value="smart-contract" selected>
															Smart Contract
														</option>
														<option value="token">Token</option>
														<option value="nft">NFT</option>
													</select>
													<div>
														<label class="block text-sm font-medium mb-1">Network</label>
														<select id="network_type" class="form-select w-full"
															v-model="activeData.network">
															<option value="ETH" selected>
																Ethereum
															</option>
															<option value="POLY">Polygon</option>
															<option value="ARB">Arbitrum One</option>
														</select>
													</div>
													<div>
														<label class="block text-sm font-medium mb-1" for="name">Wallet Address</label>
														<input id="wallet-address" class="form-input w-full" type="text"
															placeholder="0x000000000" v-model="activeData.address" />
													</div>
													<div v-if="activeData.type == 'smart-contract'">
														<label class="block text-sm font-medium mb-1" for="aggregate-internal-events">Aggregate Internal Events</label>
														<select id="aggregate-internal-events" class="form-select w-full" v-model="activeData.aggregate">
															<option value="no" selected>No</option>
															<option value="yes">Yes</option>
														</select>
													</div>
													<div>
														<label class="block text-sm font-medium mb-1"
															for="large">Description</label>
														<input id="large" class="form-input w-full px-4 py-3"
															type="text" v-model="activeData.description" />
													</div>
												</div>
											</form>
										</div>
									</div>
									<!-- Modal footer -->
									<div class="px-5 py-4 border-t border-slate-200">
										<div class="flex flex-wrap justify-end space-x-2">
											<button class="
												btn-sm
												border-slate-200
												hover:border-slate-300
												text-slate-600
												" @click.stop="addDataModelOpen = false">
												Cancel
											</button>
											<button class="
												btn-sm
												bg-indigo-500
												hover:bg-indigo-600
												text-white
												" @click.stop="addDataConnection()">
												Add Data
											</button>
										</div>
									</div>
								</ModalBasic>
								<!-- End -->
								<!-- Start Share Project -->
								<ModalBasic id="share-project-modal" :modalOpen="shareProjectModelOpen"
									@close-modal="shareProjectModelOpen = false" title="Share Project">
									<!-- Modal content -->
									<div class="px-5 py-4">
										<div class="text-sm mb-5">
											<form>
												<div class="space-y-4">
													<span class="text-base ml-3 mt-2">Select an org to share with:  </span>
													<DropdownSegmentMenu class="relative inline-flex ml-3 mt-1"
													:title="selectedOrg.name || 'Select Org'">
													<li v-for="org in this.availableOrgs" :key="org.id"
														class="flex items-center w-full hover:bg-slate-50 py-1 px-3 cursor-pointer" :class="org.id === selectedOrg.id && 'text-indigo-500'" @click="selectOrg(org)">
														<a class="font-medium text-sm text-slate-600 hover:text-slate-800 flex py-1 px-3">{{org.name}}</a>
													</li>
												</DropdownSegmentMenu>
												</div>
											</form>
										</div>
										<!-- Form Actions -->
										<div class="px-5 py-4 border-t border-slate-200">
											<div class="flex flex-wrap justify-end space-x-2">
												<button class="
													btn-sm
													border-slate-200
													hover:border-slate-300
													text-slate-600
												" @click.stop="shareProjectModelOpen = false">
													Cancel
												</button>
												<button class="btn-sm bg-indigo-500 hover:bg-indigo-600 text-white"
													@click.stop="createProjectShare()">
													Share
												</button>
											</div>
										</div>
										<!-- Table -->
										<div class="overflow-x-auto">
											<table class="table-auto w-full">
											<!-- Table header -->
											<thead class="
												text-xs
												font-semibold
												uppercase
												text-slate-500
												bg-slate-50
												border-t border-b border-slate-200
											">
												<tr>
													<th class="
														px-2
														first:pl-5
														last:pr-5
														py-3
														whitespace-nowrap
													">
														<div class="font-semibold text-left">Status</div>
													</th>
													<th class="
														px-2
														first:pl-5
														last:pr-5
														py-3
														whitespace-nowrap
													">
														<div class="font-semibold text-left">Org Name</div>
													</th>
													<th class="
														px-2
														first:pl-5
														last:pr-5
														py-3
														whitespace-nowrap
													">
														<div class="font-semibold text-left">Project</div>
													</th>
													<th v-if="activeProject.isAdmin" class="
														px-2
														first:pl-5
														last:pr-5
														py-3
														whitespace-nowrap
													">
														<div class="font-semibold text-left">Actions</div>
													</th>
												</tr>
											</thead>
											<tbody class="text-sm divide-y divide-slate-200">
												<!-- Table body -->
												<tr v-for="item in this.projectSharingData" :key="item.id">
													<td class="
														px-2
														first:pl-5
														last:pr-5
														py-3
														whitespace-nowrap
														w-px
													">
														<div class="flex items-center">
																<svg class="
																	w-3
																	h-3
																	shrink-0
																	fill-current
																	text-emerald-500
																	mr-2
																	" viewBox="0 0 12 12">
																	<path
																		d="M10.28 1.28L3.989 7.575 1.695 5.28A1 1 0 00.28 6.695l3 3a1 1 0 001.414 0l7-7A1 1 0 0010.28 1.28z" />
																</svg>
																<span>Sharing</span>
														</div>

													</td>
													<td class="
														px-2
														first:pl-5
														last:pr-5
														py-3
														whitespace-nowrap
														w-px
													">
														<div class="flex items-center">
															<div class="font-medium text-sky-500">
																{{item.organization.name}}
															</div>
														</div>
													</td>
													<td class="
														px-2
														first:pl-5
														last:pr-5
														py-3
														whitespace-nowrap
													">
														<div class="font-medium text-slate-800">
															{{item.name}}
														</div>
													</td>
													<td v-if="activeProject.isAdmin" class="
														px-2
														first:pl-5
														last:pr-5
														py-3
														whitespace-nowrap
														w-px
													">
														<div class="space-x-1">
															<button class="
																text-rose-500
																hover:text-rose-600
																rounded-full" @click.stop="removeProjectShare(item.id)">
																<span class="sr-only">Delete</span>
																<svg class="w-8 h-8 fill-current" viewBox="0 0 32 32">
																	<path d="M13 15h2v6h-2zM17 15h2v6h-2z" />
																	<path
																		d="M20 9c0-.6-.4-1-1-1h-6c-.6 0-1 .4-1 1v2H8v2h1v10c0 .6.4 1 1 1h12c.6 0 1-.4 1-1V13h1v-2h-4V9zm-6 1h4v1h-4v-1zm7 3v9H11v-9h10z" />
																</svg>
															</button>
														</div>
													</td>
											</tr>
										</tbody>
										</table>
									</div>
									</div>
								</ModalBasic>
								<!-- End -->
							</div>
						</div>
					</div>
					<!-- Start Edit Data -->
					<ModalBasic id="edit-data-modal" :modalOpen="editDataModelOpen"
						@close-modal="editDataModelOpen = false" :title="editDataTitle">
						<!-- Modal content -->
						<div class="px-5 py-4">
							<div class="mb-5">
								<div>
									<label class="block text-sm font-medium mb-1" for="name">Name of Data
										Connection</label>
									<input id="wallet-address" class="form-input w-full" type="text"
										placeholder="Type something you will recognize..." v-model="activeData.name" />
								</div>
							</div>

							<div class="text-sm mb-5">
								<form>
									<div class="space-y-4">
										<label class="block text-sm font-medium mb-1" for="role">What Type of Data Do
											You Want to Add?</label>
										<select id="role" class="form-select w-full">
											<option value="smart-contract" selected>
												Smart Contract
											</option>
											<option value="token">Token</option>
										</select>
										<div>
											<label class="block text-sm font-medium mb-1" for="email">Network</label>
											<input id="network" class="
												form-input
												w-full
												bg-slate-300
												text-slate-900
												py-2
												px-4
												rounded
												opacity-50
												cursor-not-allowed
												" type="text" value="Ethereum" disabled />
										</div>
										<div>
											<label class="block text-sm font-medium mb-1" for="name">Wallet
												Address</label>
											<input id="wallet-address" class="form-input w-full" type="text"
												placeholder="0x000000000" v-model="activeData.address" />
										</div>
										<div>
											<label class="block text-sm font-medium mb-1"
												for="large">Description</label>
											<input id="large" class="form-input w-full px-4 py-3" type="text"
												v-model="activeData.description" />
										</div>
									</div>
								</form>
							</div>
						</div>
						<!-- Modal footer -->
						<div class="px-5 py-4 border-t border-slate-200">
							<div class="flex flex-wrap justify-end space-x-2">
								<button class="
									btn-sm
									border-slate-200
									hover:border-slate-300
									text-slate-600
								" @click.stop="clearDataEditModal()">
									Cancel
								</button>
								<button class="btn-sm bg-indigo-500 hover:bg-indigo-600 text-white"
									@click.stop="updateDataConnection()">
									Save Changes
								</button>
							</div>
						</div>
					</ModalBasic>
					<!-- End -->

					<!-- Start Delete Data -->
					<ModalBlank id="delete-data-modal" :modalOpen="deleteDataModelOpen"
						@close-modal="deleteDataModelOpen = false">
						<div class="p-5 flex space-x-4">
							<!-- Icon -->
							<div class="
								w-10
								h-10
								rounded-full
								flex
								items-center
								justify-center
								shrink-0
								bg-rose-100
								">
								<svg class="w-4 h-4 shrink-0 fill-current text-rose-500" viewBox="0 0 16 16">
									<path
										d="M8 0C3.6 0 0 3.6 0 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8zm0 12c-.6 0-1-.4-1-1s.4-1 1-1 1 .4 1 1-.4 1-1 1zm1-3H7V4h2v5z" />
								</svg>
							</div>
							<!-- Content -->
							<div>
								<!-- Modal header -->
								<div class="mb-2">
									<div class="text-lg font-semibold text-slate-800">
										{{deleteDataTitle}}
									</div>
								</div>
								<!-- Modal content -->
								<div class="text-sm mb-10">
									<div class="space-y-2">
										<p>
											Are you sure you want to remove {{deleteDataName}}? This
											will stop all automated tracking of {{deleteDataName}}.
										</p>
									</div>
								</div>
								<!-- Modal footer -->
								<div class="flex flex-wrap justify-end space-x-2">
									<button class="
										btn-sm
										border-slate-200
										hover:border-slate-300
										text-slate-600
										" @click.stop="deleteDataModelOpen = false">
										Cancel
									</button>
									<button class="btn bg-rose-500 hover:bg-rose-600 text-white"
										@click="confirmDataDelete()">
										Remove Connection
									</button>
								</div>
							</div>
						</div>
					</ModalBlank>
					<!-- End -->
					<!-- Start Monitor Data -->
					<ModalBasic id="monitor-data-modal" :modalOpen="monitorDataModelOpen"
						@close-modal="monitorDataModelOpen = false" title="Address Information">
						<div class="p-5 flex space-x-4">
							<div v-show="!metricsLoading">
								<div class="text-sm font-medium" v-if="addressMetrics.hasOwnProperty('latestEvent')">
									Latest Event: {{ addressMetrics.latestEvent }}
								</div>
								<div class="text-sm font-medium" v-if="addressMetrics.hasOwnProperty('earliestEvent')">
									Earliest Event: {{ addressMetrics.earliestEvent }}
								</div>
								<div class="text-sm font-medium" v-if="addressMetrics.hasOwnProperty('totalEvents')">
									Total Events: {{ addressMetrics.totalEvents }}
								</div>
								<div class="text-sm font-medium" v-if="addressMetrics.hasOwnProperty('totalDecodedEvents')">
									Total Decoded Events: {{ addressMetrics.totalDecodedEvents }}
								</div>
								<div class="text-sm font-medium" v-if="addressMetrics.hasOwnProperty('totalNonDecodedEvents')">
									Total Non-decoded Events: {{ addressMetrics.totalNonDecodedEvents }}
								</div>
								<div class="text-sm font-medium" v-if="addressMetrics.hasOwnProperty('uniqueAddresses')">
									Unique Addresses: {{ addressMetrics.uniqueAddresses }}
								</div>
								<div class="text-sm font-medium" v-if="addressMetrics.hasOwnProperty('numberOfHolders')">
									Number of Holders: {{ addressMetrics.numberOfHolders }}
								</div>
								<div class="text-sm font-medium" v-if="addressMetrics.hasOwnProperty('lastPull')">
									Latest Pull: {{ addressMetrics.lastPull }}
								</div>
								<div class="text-sm font-medium" v-if="addressMetrics.hasOwnProperty('hasMetadata')">
									Has Metadata: {{ addressMetrics.hasMetadata }}
								</div>
							</div>
							<div v-show="metricsLoading" class="w-full justify-center content-center mt-7">
								<div class="text-xs text-slate-400 flex justify-center content-center">Loading address information...</div>
								<div class="area">
									<div class="loading">
										<span></span>
									</div>
								</div>
							</div>

						</div>
					</ModalBasic>
					<!-- End -->
					<!-- Stop Tracking Project -->
					<ModalBlank id="stop-tracking-modal" :modalOpen="stopTrackingModalOpen"
						@close-modal="stopTrackingModalOpen = false">
						<div class="p-5 flex space-x-4">
							<!-- Icon -->
							<div class="
								w-10
								h-10
								rounded-full
								flex
								items-center
								justify-center
								shrink-0
								bg-rose-100
								">
								<svg class="w-4 h-4 shrink-0 fill-current text-rose-500" viewBox="0 0 16 16">
									<path
										d="M8 0C3.6 0 0 3.6 0 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8zm0 12c-.6 0-1-.4-1-1s.4-1 1-1 1 .4 1 1-.4 1-1 1zm1-3H7V4h2v5z" />
								</svg>
							</div>
							<!-- Content -->
							<div>
								<!-- Modal header -->
								<div class="mb-2">
									<div class="text-lg font-semibold text-slate-800">
										{{stopTrackingProjectTitle}}
									</div>
								</div>
								<!-- Modal content -->
								<div class="text-sm mb-10">
									<div class="space-y-2">
										<p>
											Are you sure you want to stop tracking
											{{currentProjectName}}? This will pause all data
											connections from automatically collecting additional
											information.
										</p>
										<p class="text-xs mt-5">
											Note: If you stop tracking, it does not remove all data
											tracked from the project up to this point. To remove data,
											you must remove the Project entirely.
										</p>
									</div>
								</div>
								<!-- Modal footer -->
								<div class="flex flex-wrap justify-end space-x-2">
									<button class="
										btn-sm
										border-slate-200
										hover:border-slate-300
										text-slate-600
										" @click.stop="stopTrackingModalOpen = false">
										Cancel
									</button>
									<button class="btn bg-rose-500 hover:bg-rose-600 text-white"
										@click="stopTrackingProject()">
										Stop Tracking
									</button>
								</div>
							</div>
						</div>
					</ModalBlank>
					<!-- End -->

					<!-- Start Project Data -->
					<ModalBasic id="add-project-modal" :modalOpen="addProjectModelOpen"
						@close-modal="addProjectModelOpen = false" title="Add New Project">
						<!-- Modal content -->
						<div class="px-5 py-4">
							<div class="text-sm mb-5">
								<form>
									<div class="space-y-4">
										<div>
											<label class="block text-sm font-medium mb-1" for="name">Name</label>
											<input id="wallet-address" class="form-input w-full" type="text"
												placeholder="Type something you will recognize..."
												v-model="projectName" />
										</div>
										<div>
											<label class="block text-sm font-medium mb-1"
												for="large">Description</label>
											<input id="large" class="form-input w-full px-4 py-3" type="text"
												v-model="projectDescription" />
										</div>
										<div class="flex w-124">
											<div class="flex items-center"></div>
										</div>
									</div>
								</form>
							</div>
						</div>
						<!-- Modal footer -->
						<div class="px-5 py-4 border-t border-slate-200">
							<div class="flex flex-wrap justify-end space-x-2">
								<button class="
									btn-sm
									border-slate-200
									hover:border-slate-300
									text-slate-600
								" @click.stop="() => { projectName = ''; addProjectModelOpen = false }">
									Cancel
								</button>
								<button class="btn-sm bg-indigo-500 hover:bg-indigo-600 text-white"
									@click="addProject()">
									Add Project
								</button>
							</div>
						</div>
					</ModalBasic>
					<!-- End -->

					<!-- Edit Project Data -->
					<ModalBasic id="edit-project-modal" :modalOpen="editProjectModelOpen"
						@close-modal="editProjectModelOpen = false" :title="editProjectTitle">
						<!-- Modal content -->
						<div class="px-5 py-4">
							<div class="text-sm mb-5">
								<form>
									<div class="space-y-4">
										<div v-if="activeProject">
											<label class="block text-sm font-medium mb-1" for="name">Name</label>
											<input id="wallet-address" class="form-input w-full" type="text"
												placeholder="Type something you will recognize..."
												v-model="activeProject.name" />
										</div>
										<div v-if="activeProject">
											<label class="block text-sm font-medium mb-1"
												for="large">Description</label>
											<input id="large" class="form-input w-full px-4 py-3" type="text"
												v-model="activeProject.description" />
										</div>
									</div>
								</form>
							</div>
						</div>
						<!-- Modal footer -->
						<div class="px-5 py-4 border-t border-slate-200">
							<div
								v-if="activeProject"
								class="flex flex-wrap justify-end space-x-2">
								<input id="delete-confirm" class="form-input" type="text"
									placeholder="Type DELETE to confirm" v-bind:class="{
										hidden: activeProject.showDelete == false,
										block: activeProject.showDelete == true,
									}" v-model="activeProject.deleteCheck" />
								<button class="
									btn
									border-slate-200
									hover:border-slate-300
									text-rose-500
								" @click="confirmProjectDelete()">
									<svg class="w-4 h-4 fill-current shrink-0" viewBox="0 0 16 16">
										<path
											d="M5 7h2v6H5V7zm4 0h2v6H9V7zm3-6v2h4v2h-1v10c0 .6-.4 1-1 1H2c-.6 0-1-.4-1-1V5H0V3h4V1c0-.6.4-1 1-1h6c.6 0 1 .4 1 1zM6 2v1h4V2H6zm7 3H3v9h10V5z" />
									</svg>
									<span class="ml-2">Delete</span>
								</button>
								<button class="
									btn-sm
									border-slate-200
									hover:border-slate-300
									text-slate-600
								" @click.stop="clearEditProjectModal()">
									Cancel
								</button>
								<button class="btn-sm bg-indigo-500 hover:bg-indigo-600 text-white"
									@click="updateProject()">
									Save Changes
								</button>
							</div>
						</div>
					</ModalBasic>
					<!-- End Edit Project Data -->

			<!-- Create New Env -->
			<ModalBasic
			  id="create-env-modal"
			  :modalOpen="createEnvModalOpen"
			  @close-modal="createEnvModalOpen = false"
			  title="Create New Environment"
			>
			  <!-- Modal content -->
			  <div class="px-5 py-4">
				<div class="text-sm mb-5">
				  <form>
					<div class="space-y-4">
					  <div>
						<label class="block text-sm font-medium mb-1" for="name"
						  >Environment Name</label
						>
						<input
						  id="wallet-address"
						  class="form-input w-full"
						  type="text"
						  placeholder="Type something you will recognize..."
						  v-model="envName"
						/>
					  </div>
					  <div>
						<label class="block text-sm font-medium mb-1" for="large"
						  >Username (Email)</label
						>
						<input
						  id="large"
						  class="form-input w-full px-4 py-3"
						  type="text"
						  v-model="envUsername"
						/>
					  </div>
					</div>
				  </form>
				</div>
			  </div>
			  <!-- Modal footer -->
			  <div class="px-5 py-4 border-t border-slate-200">
				<div class="flex flex-wrap justify-end space-x-2">
				  <button
					class="
					  btn-sm
					  border-slate-200
					  hover:border-slate-300
					  text-slate-600
					"
					@click.stop="createEnvModalOpen = false;"
				  >
					Cancel
				  </button>
				  <button
					class="btn-sm bg-indigo-500 hover:bg-indigo-600 text-white"
					@click="createEnvironment()"
				  >
					Create Environment
				  </button>
				</div>
			  </div>
			</ModalBasic>
			<!-- End Create New Env -->

					<!-- Project Tabs -->
					<div class="relative mb-8">
						<div class="absolute bottom-0 w-full h-px bg-slate-200" aria-hidden="true"></div>
						<ul class="
							relative
							text-sm
							font-medium
							flex flex-nowrap
							-mx-4
							sm:-mx-6
							lg:-mx-8
							overflow-x-scroll
							no-scrollbar
						">
							<li v-for="p in projectList" :key="p.id" :item="p" class="
								mr-6
								last:mr-0
								first:pl-4
								sm:first:pl-6
								lg:first:pl-8
								last:pr-4
								sm:last:pr-6
								lg:last:pr-8
								" v-on:click="toggleTabs(p.tabO)">
								<span v-bind:class="{
									'block pb-3 text-indigo-500 whitespace-nowrap border-b-2 border-indigo-500':
										openTab === p.tabO,
								}" class="
									flex
									block
									pb-3
									text-slate-500
									hover:text-slate-600
									whitespace-nowrap
									hover:border-b-2 hover:border-indigo-500
									cursor-pointer
								">{{p.name}}</span>
							</li>
						</ul>
					</div>

					<RefreshDataCard v-if="activeProject && activeProject.id"
						:card-text="`Refreshing <strong>${activeProject.name}'s</strong> data temporarily rushes data enrichment.`"
						:done-refreshing="doneRefreshingData" :error-refreshing="errorRefreshingData" :show-smart-contract-button="true"
						:done-smart-contract-refreshing="doneSmartContractRefreshingData" :error-smart-contract-refreshing="errorSmartContractRefreshingData"
						button-text="Pull Latest Wallet Transactions/Balances" @refresh-data-clicked="startProjectIngestion"
						@refresh-smart-contract-clicked="startSmartContractIngestion" @create-env="createEnvModalOpen = true" />



					<!-- Project Contents -->
					<div v-for="p in projectList" :key="p.id" :item="p" v-bind:class="{
						hidden: openTab !== p.tabO,
						'block animate-fadein-content ': openTab === p.tabO,
					}">
						<div class="
							mb-8
							bg-white
							shadow-lg
							rounded-sm
							border border-slate-200
							relative
							px-5
							py-4
						">
							<div class="sm:flex sm:justify-between sm:items-center">
								<div class="mb-4 sm:mb-0">
									<h1 class="text-xl md:text-xl text-slate-800 font-bold">
										{{p.name}}
									</h1>
								</div>
								<div v-if="activeProject.isAdmin" class="
									grid grid-flow-col
									sm:auto-cols-max
									justify-start
									sm:justify-end
									gap-2
								">
									<button class="
										btn
										border-slate-200
										hover:border-slate-300
										text-rose-500
										disabled:border-slate-300
										disabled:bg-slate-100
										disabled:text-slate-400
										disabled:cursor-not-allowed
										" v-if="isActivated" @click.stop="stopTrackingModalOpen = true" disabled>
										Stop Tracking
									</button>
									<button class="
										btn
										border-slate-200
										hover:border-slate-300
										text-emerald-500
										" v-if="isActivated == false" @click.stop="startTrackingProject()">
										Start Tracking
									</button>
									<!-- <button class="
										btn
										bg-white
										border-slate-200
										hover:border-slate-300
										text-slate-600
										disabled:border-slate-300
										disabled:bg-slate-100
										disabled:text-slate-400
										disabled:cursor-not-allowed
										" @click.stop="editProjectModelOpen = true">
										<svg class="w-4 h-4 fill-current text-slate-300 shrink-0" viewBox="0 0 16 16">
											<path
												d="M11.7.3c-.4-.4-1-.4-1.4 0l-10 10c-.2.2-.3.4-.3.7v4c0 .6.4 1 1 1h4c.3 0 .5-.1.7-.3l10-10c.4-.4.4-1 0-1.4l-4-4zM4.6 14H2v-2.6l6-6L10.6 8l-6 6zM12 6.6L9.4 4 11 2.4 13.6 5 12 6.6z" />
										</svg>
									</button> -->
								</div>
							</div>
							<div class="mb-0">
								<div class="text-sm mb-4">
									<span class="text-xs" v-if="isActivated">Tracking since: {{
										trackingStartDate
									}}</span>
									<br>
									<span class="text-xs">Project Identifier: {{
										this.activeProject.uuid
									}}</span>
									<p class="text-slate-800">{{p.description}}</p>
								</div>
							</div>
						</div>

						<div class="sm:flex sm:justify-between sm:items-center mb-8">
							<div class="text-sm mb-2">
								<h2 class="text-xl font-semibold">
									Data Being Automatically Tracked for {{p.name}}
								</h2>
							</div>
							<div class="
								grid grid-flow-col
								sm:auto-cols-max
								justify-start
								sm:justify-end
								gap-2
								">
								<div class="mr-1.5">
									<button class="
										btn
										flex
										border-slate-200
										hover:border-slate-300
										text-indigo-500
										disabled:border-slate-200
										disabled:bg-slate-100
										disabled:text-slate-400
										disabled:cursor-not-allowed
										shadow-none
										" @click.stop="openAddDataConnectionModal()" :disabled="isActivated" v-if="activeProject.isAdmin">
										<span class="hidden xs:block mr-2">Add Data</span>
										<Tooltip size="lg" bg="dark" position="left" v-if="isActivated">
											<div class="text-sm font-medium text-slate-200">
												Data can only be added while tracking is not in process.
											</div>
										</Tooltip>
									</button>
								</div>
							</div>
						</div>

						<!-- Data Connections for a project -->
						<div class="
							bg-white
							shadow-lg
							rounded-sm
							border border-slate-200
							relative
						">
							<header class="px-5 py-4">
								<div class="text-sm mb-2">
									<!-- <h3 class="font-medium text-slate-800">Project Data Summary</h3> -->
									<div class="mt-4 sm:mb-0">
										<DataSummary :wallets="p.addressCount" :smartContracts="p.smartContractCount"
											networks="1" :tokens="p.tokenCount" />
									</div>
								</div>
							</header>
							<div>
								<!-- Table -->
								<div class="overflow-x-auto">
									<table class="table-auto w-full">
										<!-- Table header -->
										<thead class="
											text-xs
											font-semibold
											uppercase
											text-slate-500
											bg-slate-50
											border-t border-b border-slate-200
										">
											<tr>
												<th class="
													px-2
													first:pl-5
													last:pr-5
													py-3
													whitespace-nowrap
												">
													<div class="font-semibold text-left">Status</div>
												</th>
												<th class="
													px-2
													first:pl-5
													last:pr-5
													py-3
													whitespace-nowrap
												">
													<div class="font-semibold text-left">Name</div>
												</th>
												<th class="
													px-2
													first:pl-5
													last:pr-5
													py-3
													whitespace-nowrap
												">
													<div class="font-semibold text-left">Type</div>
												</th>
												<th class="
													px-2
													first:pl-5
													last:pr-5
													py-3
													whitespace-nowrap
												">
													<div class="font-semibold text-left">Address</div>
												</th>
												<th class="
													px-2
													first:pl-5
													last:pr-5
													py-3
													whitespace-nowrap
												">
													<div class="font-semibold text-left">Description</div>
												</th>
												<th class="
													px-2
													first:pl-5
													last:pr-5
													py-3
													whitespace-nowrap
												">
													<div class="font-semibold text-left">Network</div>
												</th>
												<th v-if="activeProject.isAdmin" class="
													px-2
													first:pl-5
													last:pr-5
													py-3
													whitespace-nowrap
												">
													<div class="font-semibold text-left">Actions</div>
												</th>
											</tr>
										</thead>
										<tbody class="text-sm divide-y divide-slate-200">
											<!-- Table body -->
											<tr v-for="item in p.dataList" :key="item.id">
												<td class="
													px-2
													first:pl-5
													last:pr-5
													py-3
													whitespace-nowrap
													w-px
												">
													<div v-if="activeProject.isAdmin" class="flex items-center">
														<button class="
														btn-sm
														border-slate-200
														hover:border-slate-300
														shadow-sm
														flex
														items-center
													">
															<svg class="
																w-3
																h-3
																shrink-0
																fill-current
																text-emerald-500
																mr-2
																" viewBox="0 0 12 12">
																<path
																	d="M10.28 1.28L3.989 7.575 1.695 5.28A1 1 0 00.28 6.695l3 3a1 1 0 001.414 0l7-7A1 1 0 0010.28 1.28z" />
															</svg>
															<span>Active</span>
														</button>
													</div>
													<div v-if="!activeProject.isAdmin" class="flex items-center">
														<svg class="
															w-3
															h-3
															shrink-0
															fill-current
															text-emerald-500
															mr-2
															" viewBox="0 0 12 12">
															<path
																d="M10.28 1.28L3.989 7.575 1.695 5.28A1 1 0 00.28 6.695l3 3a1 1 0 001.414 0l7-7A1 1 0 0010.28 1.28z" />
														</svg>
														<span>Active</span>
													</div>
												</td>
												<td class="
													px-2
													first:pl-5
													last:pr-5
													py-3
													whitespace-nowrap
													w-px
												">
													<div class="flex items-center">
														<div class="font-medium text-sky-500">
															<a href="#">{{item.name}}</a>
														</div>
													</div>
												</td>
												<td class="
													px-2
													first:pl-5
													last:pr-5
													py-3
													whitespace-nowrap
												">
													<div>{{cleanItemType(item.type)}}</div>
												</td>
												<td class="
													px-2
													first:pl-5
													last:pr-5
													py-3
													whitespace-nowrap
												">
													<div class="font-medium text-slate-800">
														{{item.address}}
														<button @click="loadAddressInformation(item.address, item.type)"
														class="btn-xs hover:border-slate-300 place-self-end py-1 px-2">
														<img src="../images/info.svg" width="16" height="16" class="download-link-color" />
													</button>
													</div>

												</td>
												<td class="
													px-2
													first:pl-5
													last:pr-5
													py-3
													whitespace-nowrap
												">
													<div class="font-medium text-slate-800">
														{{item.description}}
													</div>
												</td>
												<td class="
													px-2
													first:pl-5
													last:pr-5
													py-3
													whitespace-nowrap
												">
													<div class="font-medium text-slate-800">
														{{item.network}}
													</div>
												</td>
												<td v-if="activeProject.isAdmin" class="
													px-2
													first:pl-5
													last:pr-5
													py-3
													whitespace-nowrap
													w-px
												">
													<div class="space-x-1">
														<button class="
															text-slate-400
															hover:text-slate-500
															rounded-full" @click.stop="loadDataConnectionToEdit(p.dataList, item.id)">
															<span class="sr-only">Edit</span>
															<svg class="w-8 h-8 fill-current" viewBox="0 0 32 32">
																<path
																	d="M19.7 8.3c-.4-.4-1-.4-1.4 0l-10 10c-.2.2-.3.4-.3.7v4c0 .6.4 1 1 1h4c.3 0 .5-.1.7-.3l10-10c.4-.4.4-1 0-1.4l-4-4zM12.6 22H10v-2.6l6-6 2.6 2.6-6 6zm7.4-7.4L17.4 12l1.6-1.6 2.6 2.6-1.6 1.6z" />
															</svg>
														</button>
														<button class="
															text-rose-500
															hover:text-rose-600
															rounded-full" @click.stop="loadDataConnectionToDelete(p.dataList, item.id)">
															<span class="sr-only">Delete</span>
															<svg class="w-8 h-8 fill-current" viewBox="0 0 32 32">
																<path d="M13 15h2v6h-2zM17 15h2v6h-2z" />
																<path
																	d="M20 9c0-.6-.4-1-1-1h-6c-.6 0-1 .4-1 1v2H8v2h1v10c0 .6.4 1 1 1h12c.6 0 1-.4 1-1V13h1v-2h-4V9zm-6 1h4v1h-4v-1zm7 3v9H11v-9h10z" />
															</svg>
														</button>
													</div>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
					<!-- END P Contents -->
				</div>
			</main>
		</div>
	</div>
</template>

<script>
import { ref } from 'vue';
import { getProjectUniqueWallets } from '../services/metrics';
import {
	addDataConnectionToProject,
	addProjectForOrg,
	addProjectShare,
	deleteProjectShare,
	deleteDataConnectionById,
	deleteProjectById,
	getProjectsConnectionsByOrgId,
	getConnectionsByProject,
	updateDataConnectionById,
	updateProjectById,
	getApplications,
	getProjectSharingData,
	getAvailableOrgs
} from '../services/project';

import { createEnvAndUser } from '../services/organization';

import * as Utils from '../utils/Utils';

const URL_DOMAIN = Utils.URL_DOMAIN;

import FilterButton from '../components/DropdownFilter.vue';
import DropdownSegmentMenu from '../components/DropdownSegmentMenu.vue';
import ModalAction from '../components/ModalAction.vue';
import ModalBasic from '../components/ModalBasic.vue';
import ModalBlank from '../components/ModalBlank.vue';
import ModalCookies from '../components/ModalCookies.vue';
import Notification from '../components/Notification.vue';
import PaginationNumeric from '../components/PaginationNumeric.vue';
import SearchForm from '../components/SearchForm.vue';
import Tooltip from '../components/Tooltip.vue';
import RefreshDataCard from '../components/support/RefreshDataCard.vue';
import ConnectionCard from '../partials/connections/ConnectionCard.vue';
import DataSummary from '../partials/DataSummary.vue';
import Header from '../partials/Header.vue';
import Sidebar from '../partials/Sidebar.vue';
import { formatThousands } from '../utils/Utils';
import { prioritizeIngestionForProject,prioritizeSmartContractForProject } from '../services/data-ingestion';
import DropdownFull from '../components/DropdownFull.vue';


export default {
	name: 'Data Connections',
	components: {
		Sidebar,
		Header,
		SearchForm,
		FilterButton,
		ConnectionCard,
		PaginationNumeric,
		DataSummary,
		ModalBasic,
		ModalCookies,
		ModalBlank,
		ModalAction,
		Notification,
		Tooltip,
		RefreshDataCard,
		DropdownFull,
		DropdownSegmentMenu
	},
	setup() {
		const addDataModelOpen = ref(false);
		const editDataModelOpen = ref(false);
		const deleteDataModelOpen = ref(false);
		const monitorDataModelOpen = ref(false);
		const metricsLoading = ref(false);

		const addProjectModelOpen = ref(false);
		const editProjectModelOpen = ref(false);
		const stopTrackingModalOpen = ref(false);

		const createEnvModalOpen = ref(false);
		const setupEventStreamProjectModalOpen = ref(false);

		const shareProjectModelOpen = ref(false);

		const notificationInfoOpen = ref(true);
		const sidebarOpen = ref(false);

		const devEnabled = localStorage.getItem('devEnabled');
		const betaEnabled = localStorage.getItem('betaEnabled');

		return {
			sidebarOpen,
			addDataModelOpen,
			addProjectModelOpen,
			editProjectModelOpen,
			editDataModelOpen,
			deleteDataModelOpen,
			monitorDataModelOpen,
			metricsLoading,
			stopTrackingModalOpen,
			notificationInfoOpen,
			devEnabled,
			betaEnabled,
			createEnvModalOpen,
			setupEventStreamProjectModalOpen,
			shareProjectModelOpen
		};
	},
	data() {
		return {
			projectName: '',
			projectDescription: '',
			projectApplication: '',
			activeProject: {
				name: '',
				description: '',
				id: '',
				connections: [],
				uniqueWallets: 0,
				activationDate: null,
				isAdmin: false
			},
			activeData: { type: 'smart-contract', network: 'ETH' },
			addressMetrics: {
				earliestEvent: '--',
				latestEvent: '--',
				uniqueAddresses: '--',
				totalEvents: '--',
				totalDecodedEvents: '--',
				totalNonDecodedEvents: '--',
				hasMetadata: '--',
				lastPull: '--',
				numberOfHolders: '--'
			},
			projectList: [],
			openTab: 0,
			doneRefreshingData: false,
			errorRefreshingData: false,
			doneSmartContractRefreshingData: false,
			errorSmartContractRefreshingData: false,
			isCreatingEventStream: false,
			envName: '',
			envUsername: '',
			applications: [],
			selectedApplication: '',
			applicationId: '',
			projectSharingData:[],
			availableOrgs:[],
			selectedOrg: {id: '', name: ''},
		};
	},
	computed: {
		currentProjectName() {
			return this.activeProject.name;
		},
		stopTrackingProjectTitle() {
			return 'Stop Tracking ' + this.activeProject.name + '?';
		},
		editProjectTitle() {
			if(this.activeProject)
				return 'Edit Project: ' + this.activeProject.name;
			else
				return 'Edit Project';
		},
		editDataTitle() {
			if(this.activeData.name)
				return 'Edit Data Connection: ' + this.activeData.name;
			else
				return 'Edit Data Connection';
		},
		deleteDataTitle() {
			return 'Remove ' + this.activeData.name + '?';
		},
		deleteDataName() {
			return this.activeData.name;
		},
		isActivated() {
			if (this.activeProject.activationDate != null) {
				return true;
			} else {
				return false;
			}
		},
		trackingStartDate() {
			var d = new Date(this.activeProject.activationDate);
			if (this.activeProject.activationDate != null)
				return d.getMonth() + '/' + d.getDay() + '/' + d.getFullYear();
			else return null;
		},
	},
	methods: {
		async setupEventStreamProject() {
			this.isCreatingEventStream = true;
			let url = `/onboard/event-stream-project?projectName=${this.projectName}`;
			if (this.selectedApplication) {
				url = url.concat(`&applicationFilter=${this.selectedApplication}`);
			}
			const response = await fetch(`${URL_DOMAIN}${url}`, {
				method: 'POST',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify({}),
			});
			const data = await response.json();
			this.setupEventStreamProjectModalOpen = false;
			this.isCreatingEventStream = false;
			this.refreshProjects();

		},
		cleanItemType(type) {
			if (type == 'smart_contract' || type == 'smart-contract') return 'Smart Contract';
			else if (type == 'token') return 'Token';
			else if (type == 'nft') return 'NFT';
			else return '';
		},
		clearEditProjectModal() {
			this.activeProject.showDelete = false;
			this.activeProject.deleteCheck = '';
			this.editProjectModelOpen = false;
		},
		clearAddProject() {
			this.projectName = '';
		},
		clearDataEditModal() {
			this.activeData.showDelete = false;
			this.activeData.deleteCheck = false;
			this.editDataModelOpen = false;
		},
		openAddProjectModal() {
			this.addProjectModelOpen = true;
		},
		async addProject() {
			let r = await addProjectForOrg(
				parseInt(localStorage.getItem('userOrgId')),
				this.projectName,
				this.projectDescription,
				Utils.uuidv4()
			);
			this.addProjectModelOpen = false;
			this.refreshProjects();
			this.clearAddProject();
		},
		isAdmin() {
			let orgId = parseInt(localStorage.getItem('userOrgId'));
			if((orgId == 1 || orgId == 5) && this.activeProject.isAdmin) {
				return true;
			} else {
				return false;
			}
		},
		async updateProject() {
			console.log('updating project', this.activeProject.id, this.activeProject.name, this.activeProject.description);
			let r = await updateProjectById(
				this.activeProject.id,
				this.activeProject.name,
				this.activeProject.description,
			);
			console.log('Update from project by proj ID' + JSON.stringify(r));
			this.editProjectModelOpen = false;
		},
		confirmProjectDelete() {
			if (
				this.activeProject.showDelete &&
				this.activeProject.deleteCheck === 'DELETE'
			) {
				this.deleteProject();
				this.activeProject.showDelete = false;
				this.editProjectModelOpen = false;
			}
			this.activeProject.showDelete = true;
		},
		selectOrg(org) {
			this.selectedOrg = org;
		},
		async createProjectShare() {
			await addProjectShare(
				this.selectedOrg.id,
				this.activeProject.name,
				this.activeProject.description,
				this.activeProject.uuid
			);
			this.projectSharingData = await getProjectSharingData(this.activeProject.uuid);
		},
		async removeProjectShare(projectId) {
			await deleteProjectShare(projectId);
			this.projectSharingData = await getProjectSharingData(this.activeProject.uuid);
		},
		async openProjectShareModal() {
			this.projectSharingData = await getProjectSharingData(this.activeProject.uuid);
			this.availableOrgs = await getAvailableOrgs();
			this.shareProjectModelOpen = true;
		},
		async deleteProject() {
			let r = await deleteProjectById(this.activeProject.id);
			console.log('Delete from project by proj ID' + JSON.stringify(r));
			this.refreshProjects();
		},
		async refreshProjects() {
			if (this.projectList.length > 0)
				this.projectList.splice(0, this.projectList.length);

			const projResult = await getProjectsConnectionsByOrgId(
				localStorage.getItem('userOrgId'),
			);
			console.log(
				'Data retrieved from projects by org API ' + JSON.stringify(projResult),
			);

			var i = 1;
			var newList = [];

			for (const proj of projResult) {
				var dataCon = [];
				var smartContractTotal = 0;
				var tokenTotal = 0;
				var nftTotal = 0;
				if (proj.dataConnections && proj.dataConnections.length) {
					for (const c of proj.dataConnections) {
						//normalzing to smart-contract, early ones were registered as _ instead of -
						if (c.type == 'smart_contract' || c.type == 'smart-contract') {
							var newConnection = {
								id: c.id,
								name: c.name,
								address: c.address,
								description: c.description,
								type: 'smart-contract',
								startDate: c.startDate,
								network: c.network || '',
								showDelete: false,
								aggregate: c.aggregate
							};
							dataCon.push(newConnection);
							smartContractTotal++;
						} else if (c.type == 'token') {
							var newConnection = {
								id: c.id,
								name: c.name,
								address: c.address,
								description: c.description,
								type: 'token',
								startDate: c.startDate,
								network: c.network || '',
								showDelete: false,
							};
							dataCon.push(newConnection);
							tokenTotal++;
						} else if (c.type == 'nft') {
							var newConnection = {
								id: c.id,
								name: c.name,
								address: c.address,
								description: c.description,
								type: 'nft',
								startDate: c.startDate,
								network: c.network || '',
								showDelete: false,
							};
							dataCon.push(newConnection);
							nftTotal++;
						}
					}
				}

				var newProject = {
					name: proj.name,
					id: proj.id,
					uuid: proj.uuid,
					description: proj.description,
					activationDate: proj.activationDate,
					orgId: proj.organizationid,
					tabO: i,
					dataList: dataCon,
					showData: false,
					smartContractCount: smartContractTotal,
					tokenCount: tokenTotal,
					nftCount: nftTotal,
					addressCount: 0,
					isAdmin: proj.isAdmin,
				};

				newList.push(newProject);
				i++;
			}

			console.log("REFRESHING, current active is " + JSON.stringify(this.activeProject));

			if(this.activeProject)
				this.activeProject = newList.find(x => x.id === this.activeProject.id);

			if(!this.activeProject || !this.activeProject.uuid)
				this.activeProject = newList[0]; //tab 0 is always the first in the list

			for (var i = 0; i < newList.length; i++) {
				this.projectList.push(newList[i]);
				this.loadUniqueWallets(this.projectList.length - 1);
			}

			let tabId = null;
			if(this.activeProject)
				tabId = this.projectList.find(x => x.id === this.activeProject.id)?.tabO;

			if (tabId) {
				this.toggleTabs(tabId);
			}

			if (location.search) {
				const params = new URLSearchParams(location.search);
				const projectId = params.get('projectId');

				const tabId = this.projectList.find(x => x.uuid === projectId).tabO;

				if (tabId) {
					this.toggleTabs(tabId);
				}
			}

		},
		toggleTabs(tabNumber) {
			this.openTab = tabNumber;
			this.setActiveProject(tabNumber);
		},
		setActiveProject(tabNumber) {
			var self = this; //Not sure why we need this trick to be able to set activeProject
			this.projectList.forEach(function (proj) {
				if (proj.tabO === tabNumber) {
					self.activeProject = proj;
					self.activeProject.showDelete = false;
				}
			});
		},
		async startProjectIngestion() {
			const result = await prioritizeIngestionForProject(this.activeProject.uuid);
			if (result.statusCode == 200) {
				this.doneRefreshingData = true;
			} else {
				this.errorRefreshingData = true;
			}
		},
		async startSmartContractIngestion() {
			const result = await prioritizeSmartContractForProject(this.activeProject.uuid);
			if (result.statusCode == 200) {
				this.doneSmartContractRefreshingData = true;
			} else {
				this.errorSmartContractRefreshingData = true;
			}
		},
		async startTrackingProject() {
			this.activeProject.activationDate = new Date();

			console.log(JSON.stringify(this.activeProject))
			let trackProjectData = {
				projectid: this.activeProject.id,
				uuid: this.activeProject.uuid,
				name: this.activeProject.name,
				smartcontracts: [],
				tokens: [],
				nfts: []
			}

			for (const data of this.activeProject.dataList) {
				if (data.type == 'smart-contract') {
					trackProjectData.smartcontracts.push({
						address: data.address,
						network: data.network,
						aggregate: data.aggregate
					})
				} else if (data.type == 'token') {
					trackProjectData.tokens.push({
						address: data.address,
						network: data.network
					})
				} else if (data.type == 'nft') {
					trackProjectData.nfts.push({
						address: data.address,
						network: data.network
					})
				}
			}

			const response = await fetch(`${URL_DOMAIN}/onboard/track-project`, {
				method: 'POST',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify(trackProjectData),
			});
			const data = await response.json();
			this.refreshProjects();
		},
		async getAddressInformation(address, type) {
			try {
				const response = await fetch(`${URL_DOMAIN}/monitoring/${type}`, {
					method: 'POST',
					withCreditentials: true,
					credentials: 'omit',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Access-Control-Allow-Origin': '*',
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({address: address})
				});
				this.addressMetrics = await response.json();
			} catch (e) {
				console.log(e);
			}

			this.metricsLoading = false;
		},
		loadAddressInformation(address, type) {
			this.monitorDataModelOpen = true;
			this.metricsLoading = true;
			this.getAddressInformation(address, type);
		},
		stopTrackingProject() {
			//Run an API call to stop tracking the project
			this.activeProject.activationDate = null;
			this.stopTrackingModalOpen = false;
		},
		loadDataConnectionToEdit(dataList, dataId) {
			for (var i = 0; i < dataList.length; i++) {
				if (dataList[i].id == dataId) this.activeData = dataList[i];
			}
			this.editDataModelOpen = true;
		},
		openAddDataConnectionModal() {
			this.activeData = {
				name: '',
				network: '',
				description: '',
				address: '',
				type: '',
				aggregate: 'no',
			};
			this.addDataModelOpen = true;
		},
		loadDataConnectionToDelete(dataList, dataId) {
			for (var i = 0; i < dataList.length; i++) {
				if (dataList[i].id == dataId) this.activeData = dataList[i];
			}
			this.deleteDataModelOpen = true;
		},
		async addDataConnection() {
			let r = await addDataConnectionToProject(
				this.activeProject.id,
				this.activeProject.uuid,
				this.activeData.name,
				this.activeData.network,
				this.activeData.description,
				this.activeData.address,
				this.activeData.type,
				this.activeData.aggregate == 'no' ? false : true,
			);
			this.addDataModelOpen = false;
			this.refreshProjects();
			this.clearDataEditModal();
		},
		async updateDataConnection() {
			let r = await updateDataConnectionById(
				this.activeData.id,
				this.activeProject.id,
				this.activeProject.uuid,
				this.activeData.name,
				this.activeData.description,
				this.activeData.address,
				this.activeData.type,
				this.activeData.aggregate == 'no' ? false : true,
			);
			this.refreshProjects();
			this.editDataModelOpen = false;
			this.clearDataEditModal();
		},
		confirmDataDelete() {
			//if(this.activeData.showDelete && this.activeData.deleteCheck === 'DELETE') {
			this.deleteDataConnection();
			this.activeData.showDelete = false;
			this.deleteDataModelOpen = false;
			//}
			this.activeData.showDelete = true;
		},
		async deleteDataConnection() {
			let r = await deleteDataConnectionById(this.activeData.id);
			this.refreshProjects();
			this.editDataModelOpen = false;
			this.clearDataEditModal();
		},
		async loadUniqueWallets(projectIndex) {
			const result = await getProjectUniqueWallets(
				'NONE',
				this.projectList[projectIndex].uuid,
			);

			//console.log("Project list result is " + JSON.stringify(result));

			if (result == null || result.body[0] == undefined || result.body[0].data == undefined)
				this.projectList[projectIndex].addressCount = 0;
			else
				this.projectList[projectIndex].addressCount = formatThousands(parseInt(result.body[0].data));
		},
		showCreateEnv() {
			console.log("WTF");
			this.createEnvModalOpen = true;
		},
		async createEnvironment() {
			if(this.envUsername != '' && this.envUsername != null && this.envName != '' && this.envName != null) {

				let envRe = await createEnvAndUser(this.envName, this.envUsername);

			}
			this.createEnvModalOpen = false;
		},
		projectApplicationChanged(applicationId) {
			this.applicationId = applicationId;
			this.selectedApplication = this.applications.find(x => x.id == applicationId)?.name;
		}
	},
	async mounted() {
		amplitude.getInstance().logEvent('PROJECTS_VIEWED');

		this.refreshProjects();

		const applications = await getApplications();
		this.applications = applications?.body.map((x, i) => ({ id: i, name: x }));
		console.log(`mike apps: ${JSON.stringify(applications)}`);

		this.$forceUpdate();
	},
};
</script>
