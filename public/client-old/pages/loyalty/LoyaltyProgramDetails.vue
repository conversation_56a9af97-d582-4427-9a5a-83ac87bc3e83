<template>
	<div class="flex h-screen overflow-hidden">
		<!-- Sidebar -->
		<Sidebar :sidebarOpen="sidebarOpen" @close-sidebar="sidebarOpen = false" />
		<div class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
			<div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">
				<div class="p-6 bg-white">
					<div class="mb-6">
						<label for="program-name" class="block text-sm font-medium text-gray-700">Program Name</label>
						<input v-model="loyaltyProgram.name" type="text" id="program-name"
							class="mt-1 p-2 w-full border rounded">
					</div>

					<div class="mb-6">
						<label class="block text-sm font-medium text-gray-700">
							<input type="checkbox" v-model="loyaltyProgram.active">
							Active
						</label>
					</div>

					<button @click="saveProgramDetails" class="btn bg-indigo-500 hover:bg-indigo-600 text-white">
						Save
					</button>

					<div class="mb-6">
						<h2 class="text-xl font-bold">Currencies</h2>
						<ul>
							<li v-for="currency in loyaltyProgram.loyaltyCurrencies" :key="currency.id">
								{{ currency.name }}: {{ currency.conversionToUSD }} tokens for $1
							</li>
						</ul>
						<button @click="openModal" class="btn bg-green-500 hover:bg-green-600 text-white">
							Add Currency
						</button>
					</div>

					<!-- Modal for adding currency -->
					<div v-if="showModal" class="fixed z-10 inset-0 overflow-y-auto">
						<!-- ... modal design ... -->
						<div class="bg-white p-4 rounded">
							<h3 class="text-lg mb-4">Add Currency</h3>
							<div class="mb-4">
								<label for="currency-name" class="block text-sm font-medium text-gray-700">Currency
									Name</label>
								<input v-model="newCurrency.name" type="text" id="currency-name"
									class="mt-1 p-2 w-full border rounded">
							</div>
							<div class="mb-4">
								<label for="tokens-value" class="block text-sm font-medium text-gray-700">Tokens for
									$1</label>
								<input v-model="newCurrency.conversionToUSD" type="number" id="tokens-value"
									class="mt-1 p-2 w-full border rounded">
							</div>
							<button @click="addCurrency" class="btn bg-indigo-500 hover:bg-indigo-600 text-white">
								Save Currency
							</button>
							<button @click="showModal = false" class="btn bg-red-500 hover:bg-red-600 text-white ml-4">
								Close
							</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import Sidebar from "../../partials/Sidebar.vue";
import * as Utils from '../../utils/Utils';
export default {
	data() {
		return {
			loyaltyProgram: {
				name: '',
				active: false,
			},
			showModal: false,
			newCurrency: {
				name: '',
				conversionToUSD: 0
			}
		};
	},
	components: {
		Sidebar
	},
	methods: {
		async saveProgramDetails() {
			const URL_DOMAIN = Utils.URL_DOMAIN;
			try {
				const response = await fetch(`${URL_DOMAIN}/loyalty-programs/${this.$route.params.id}`, {
					method: 'PATCH',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({
						name: this.loyaltyProgram.name,
						active: this.loyaltyProgram.active
					})
				});
				if (response.status == 200) {
					console.log('Successfully updated loyalty program');
					this.$router.push('/path_to_loyalty_programs_list'); // navigate back to the list page or any other page as needed
				} else {
					console.error('Failed to update loyalty program');
				}
			} catch (err) {
				console.error("Error updating loyalty program:", err);
			}
		},
		openModal() {
			this.showModal = true;
			console.log("Trying to open modal")
		},
		async addCurrency() {
			const URL_DOMAIN = Utils.URL_DOMAIN;
			try {
				const response = await fetch(`${URL_DOMAIN}/loyalty-programs/${this.loyaltyProgram.id}/loyalty-currencies`, {
					method: 'POST',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Content-Type': 'application/json'
					},
					body: JSON.stringify(this.newCurrency)
				});
				if (response.status == 200) {
					const currency = await response.json();
					this.loyaltyProgram.loyaltyCurrencies.push(currency);
					this.newCurrency = { name: '', conversionToUSD: 0 }; // Reset new currency data
					this.showModal = false; // Close modal
				} else {
					console.error('Failed to add currency');
				}
			} catch (err) {
				console.error("Error adding currency:", err);
			}
		}
	},
	async mounted() {
		const URL_DOMAIN = Utils.URL_DOMAIN;
		try {
			const response = await fetch(`${URL_DOMAIN}/loyalty-programs/${this.$route.params.id}`, {
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`
				}
			});
			if (response.status == 200) {
				this.loyaltyProgram = await response.json();
			} else {
				console.error('Failed to fetch loyalty program details');
			}
		} catch (err) {
			console.error("Error fetching loyalty program details:", err);
		}
	}
}
</script>
