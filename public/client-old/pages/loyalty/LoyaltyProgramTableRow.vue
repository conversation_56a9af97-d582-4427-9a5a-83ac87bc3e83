<template>
	<tr :id="`${loyaltyprogram.name}`">
		<td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
			<div class="flex items-center text-slate-800">
				<div class="font-medium text-slate-800">
					<router-link :to="`/loyalty/programs/${loyaltyprogram.id}`"
						class="text-ralpurple-500 hover:text-ralpurple-700 cursor-pointer">
						{{ loyaltyprogram.name }}
					</router-link>
				</div>
			</div>
		</td>
		<td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap text-center">
			<div class="inline-flex font-medium bg-amber-100 text-amber-600 rounded-full text-center px-2.5 py-0.5"
				v-if="!loyaltyprogram.active">Not Active</div>
			<div class="inline-flex font-medium bg-emerald-500 text-white rounded-full text-center px-2.5 py-0.5"
				v-if="loyaltyprogram.active">Active</div>
		</td>
		<td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
			<div class="flex justify-end">
				<button :data-cy="`${loyaltyprogram.name.replaceAll(' ', '')}-delete`"
					class="btn my-auto ml-4 w-8 h-8 border-slate-200 hover:border-slate-300"
					@click.stop="deleteProgramClicked">
					<svg class="w-4 h-4 fill-slate-300 hover:fill-rose-500 shrink-0" viewBox="0 0 16 16">
						<path
							d="M5 7h2v6H5V7zm4 0h2v6H9V7zm3-6v2h4v2h-1v10c0 .6-.4 1-1 1H2c-.6 0-1-.4-1-1V5H0V3h4V1c0-.6.4-1 1-1h6c.6 0 1 .4 1 1zM6 2v1h4V2H6zm7 3H3v9h10V5z" />
					</svg>
				</button>
			</div>
		</td>
	</tr>
</template>

<script>
export default {
	name: 'LoyaltyProgramTableRow',
	props: ['loyaltyprogram'],
	emits: ['clicked:loyaltyprogram', 'delete:loyaltyprogram'],
	components: {},
	computed: {

	},
	methods: {
		loyaltyProgramClicked() {
			this.$emit('clicked:loyaltyprogram', this.loyaltyprogram.id);
		},
		deleteProgramClicked() {
			this.$emit('delete:loyaltyprogram', this.loyaltyprogram.id);
		}
	}
}
</script>
