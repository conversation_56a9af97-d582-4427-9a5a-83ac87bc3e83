<template>
	<div class="flex h-screen overflow-hidden">
		<!-- Sidebar -->
		<Sidebar :sidebarOpen="sidebarOpen" @close-sidebar="sidebarOpen = false" />

		<!-- Content area -->
		<div class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
			<div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">

				<!-- Page header -->
				<div class="sm:flex sm:justify-between sm:items-center mb-8">

					<!-- Left: Title -->
					<div class="mb-4 sm:mb-0">
						<h1 class="text-2xl md:text-3xl text-slate-800 font-bold">Loyalty Programs</h1>
						<p class="text-sm mt-1 text-slate-400">Loyalty Programs Define How Users Earn and Redeem.</p>

						<span class="text-sm mt-1 text-slate-400">
							<a class="text-ralpurple-500 hover:text-ralpurple-700" href="/support/starter-segments">
								<svg xmlns="http://www.w3.org/2000/svg" height="16" width="16" viewBox="0 0 48 48"
									class="inline-flex mr-1">
									<g fill="#212121">
										<path
											d="M45,2H11a1,1,0,0,0-1,1V41a3,3,0,0,1-6,0V26H7a1,1,0,0,0,0-2H3a1,1,0,0,0-1,1V41a5.006,5.006,0,0,0,5,5H41a5.006,5.006,0,0,0,5-5V3A1,1,0,0,0,45,2ZM38,36H18a1,1,0,0,1,0-2H38a1,1,0,0,1,0,2Zm0-8H18a1,1,0,0,1,0-2H38a1,1,0,0,1,0,2Zm1-9a1,1,0,0,1-1,1H18a1,1,0,0,1-1-1V11a1,1,0,0,1,1-1H38a1,1,0,0,1,1,1Z"
											fill="#6635E6"></path>
									</g>
								</svg>
								How to get started
							</a>
						</span>
					</div>

					<!-- Right: Actions  -->
					<div class="grid grid-flow-col sm:auto-cols-max justify-start sm:justify-end gap-2">
						<button id="add-loyalty-program-button" data-cy="add-loyalty-prg-button"
							class="btn bg-indigo-500 hover:bg-indigo-600 text-white" @click.stop="addLoyaltyProgram()">
							<span class="hidden xs:block">Add Loyalty Program</span>
						</button>
					</div>
				</div>
			</div>
			<!-- Segment Table -->
			<div class="bg-white shadow-lg rounded-sm border border-slate-200 relative">
				<header class="px-5 py-4">
					<h2 class="font-semibold text-slate-800">{{ loyaltyPrograms.length }} Loyalty Programs</h2>
				</header>
				<div class="rounded-sm border border-slate-200">
					<div class="overflow-x-auto">
						<table class="table-auto w-full divide-y divide-slate-200" id="segment-table">
							<thead class="text-xs font-semibold uppercase text-slate-500 bg-slate-50">
								<tr>
									<th class="px-2 first:pl-5 last:pr-5 py-3">
										<div class="flex items-center">
											<div class="">Name</div>
										</div>
									</th>
									<th class="px-2 first:pl-5 last:pr-5 py-3">
										<div class="text-center">Enabled</div>
									</th>
								</tr>
							</thead>
							<tbody class="text-sm">
								<LoyaltyProgramTableRow v-for="loyaltyprogram in loyaltyPrograms" :key="loyaltyprogram.id"
									:loyaltyprogram="loyaltyprogram"
									@clicked:loyaltyprogram="loadLoyaltyProgram(loyaltyprogram)"
									@delete:loyaltyprogram="deleteLoyaltyProgram(loyaltyprogram)" />
							</tbody>
						</table>
						<!-- Add Loyalty Program Modal -->
						<div v-if="showModal" class="fixed z-10 inset-0 overflow-y-auto" aria-labelledby="modal-title"
							role="dialog" aria-modal="true">
							<div
								class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
								<div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
									@click="showModal = false"></div>

								<span class="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>

								<div
									class="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
									<div>
										<h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
											Add Loyalty Program
										</h3>
										<div class="mt-2">
											<input v-model="newLoyaltyProgramName" type="text" placeholder="Program Name"
												class="p-2 w-full border rounded">
										</div>
									</div>
									<div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
										<button type="button" class="btn bg-indigo-500 hover:bg-indigo-600 text-white"
											@click="addNewLoyaltyProgram">
											Add
										</button>
										<button type="button" class="btn bg-gray-400 hover:bg-gray-500 text-white mr-2"
											@click="showModal = false">
											Cancel
										</button>
									</div>
								</div>
							</div>
						</div>

					</div>
				</div>
				<!-- End -->
			</div>
		</div>
	</div>
</template>

<script>
import Sidebar from "../../partials/Sidebar.vue";
import LoyaltyProgramTableRow from "./LoyaltyProgramTableRow.vue";
import * as Utils from '../../utils/Utils';

export default {
	name: 'LoyaltyProgramTable',
	components: {
		Sidebar,
		LoyaltyProgramTableRow
	},
	data() {
		return {
			loyaltyPrograms: [
			],
			showModal: false,
			newLoyaltyProgramName: '',
		}
	},
	methods: {
		addLoyaltyProgram() {
			console.log('addLoyaltyProgram')
			this.showModal = true;
		},
		loadLoyaltyProgram(loyaltyProgram) {
			console.log('loadLoyaltyProgram', loyaltyProgram)
		},
		async deleteLoyaltyProgram(loyaltyProgram) {
			console.log('deleteLoyaltyProgram', loyaltyProgram)
			const URL_DOMAIN = Utils.URL_DOMAIN;
			try {
				const response = await fetch(`${URL_DOMAIN}/loyalty-programs/${loyaltyProgram.id}`, {
					method: 'DELETE',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Access-Control-Allow-Origin': '*',
						'Content-Type': 'application/json'
					}
				});
				if (response.status == 200 || response.status == 204) {
					this.loyaltyPrograms = await this.getLoyaltyPrograms(); // refresh the list
				} else {
					console.error('Failed to delete loyalty program');
				}
			} catch (err) {
				console.error("Error deleting loyalty program:", err);
			}
		},
		async getLoyaltyPrograms() {
			const URL_DOMAIN = Utils.URL_DOMAIN;
			let jsonresponse = {};
			try {
				const response = await fetch(`${URL_DOMAIN}/loyalty-programs`, {
					method: 'GET',
					withCreditentials: true,
					credentials: 'omit',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Access-Control-Allow-Origin': '*',
						'Content-Type': 'application/json'
					}
				});
				jsonresponse = await response.json();
				console.log("jsonresponse: " + JSON.stringify(jsonresponse));
				return jsonresponse;
			} catch (err) {
				console.log("Error: " + JSON.stringify(err));
				return {
					error: true,
					message: err.message
				}
			}
		},
		async addNewLoyaltyProgram() {
			const URL_DOMAIN = Utils.URL_DOMAIN;
			try {
				const response = await fetch(`${URL_DOMAIN}/loyalty-programs`, {
					method: 'POST',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Access-Control-Allow-Origin': '*',
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({
						name: this.newLoyaltyProgramName,
						active: false
					})
				});
				if (response.status == 200) {
					this.newLoyaltyProgramName = ''; // reset the name input
					this.showModal = false; // close the modal
					this.loyaltyPrograms = await this.getLoyaltyPrograms(); // refresh the list
				} else {
					console.error('Failed to add loyalty program');
				}
			} catch (err) {
				console.error("Error adding loyalty program:", err);
			}
		}
	},
	async mounted() {
		this.isLoading = true;
		await Promise.all([
			this.loyaltyPrograms = await this.getLoyaltyPrograms(),
		]);
		this.isLoading = false;
	},
}
</script>

<style scoped>
/* You can add styling specific to this component here */
</style>
