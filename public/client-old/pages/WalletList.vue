<template>
  <div class="flex h-screen overflow-hidden">

    <!-- Sidebar -->
    <Sidebar :sidebarOpen="sidebarOpen" @close-sidebar="sidebarOpen = false" />

    <!-- Content area -->
    <div class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">

      <!-- Site header -->
      

      <main>
        <div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">

          <!-- Page header -->
          <div class="sm:flex sm:justify-between sm:items-center mb-8">

            <!-- Left: Title -->
            <div class="mb-4 sm:mb-0">
              <h1 class="text-2xl md:text-3xl text-slate-800 font-bold">{{ chartName }} <span
                  class="text-xs text-slate-400">(Viewing Wallet List)</span></h1>

              <div class="text-left mt-2">
                <ul class="inline-flex flex-wrap text-sm font-medium">
                  <li class="flex items-left">
                    <a class="text-slate-500 hover:text-indigo-500" :href="'/dashboard/' + dashId">{{ dashboardName }}</a>
                    <svg class="h-4 w-4 fill-current text-slate-400 mt-1 ml-2 mr-2 mb-2" viewBox="0 0 16 16">
                      <path d="M6.6 13.4L5.2 12l4-4-4-4 1.4-1.4L12 8z" />
                    </svg>
                  </li>
                  <li class="flex items-left text-slate-400">
                    {{ chartName }}
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <div class="bg-white shadow-lg rounded-sm border border-slate-200 relative">

            <header v-if="!isLoaded" class="grid grid-flow-col grid-cols-2 sm:auto-cols-max px-5 py-4">
              <h2 class="col-span-1 justify-start text-sm font-semibold text-slate-800">{{this.isSegmentWidget ? '': 'Project: '}}<span
                  class="text-sm text-slate-600 font-medium">{{ projectName }}</span></h2>
            </header>

            <div class="rounded-sm border border-slate-200">
              <div class="overflow-x-auto">
                <div
                  class="bg-white p-2 sm:p-4 sm:h-64 rounded-2xl shadow-lg flex flex-col sm:flex-row gap-5 select-none"
                  v-if="!isLoaded">
                  <div class="flex flex-col flex-1 gap-5 sm:p-2">
                    <div class="flex flex-1 flex-col gap-3">
                      <div class="bg-gray-200 w-full animate-pulse h-5 rounded-2xl"></div>
                      <div class="bg-gray-200 w-full animate-pulse h-5 rounded-2xl"></div>
                      <div class="bg-gray-200 w-full animate-pulse h-5 rounded-2xl"></div>
                      <div class="bg-gray-200 w-full animate-pulse h-5 rounded-2xl"></div>
                      <div class="bg-gray-200 w-full animate-pulse h-5 rounded-2xl"></div>
                    </div>
                  </div>
                </div>
                <div class="py-5 text-center text-sm text-slate-800" v-if="isLoaded && !dataAvailable">
                  Wallet Data Not Available
                </div>

                <table class="table-auto w-full divide-y divide-slate-200" v-if="isLoaded && dataAvailable">
                  <tr>
                    <td class="grid grid-flow-col grid-cols-2 sm:auto-cols-max px-5 py-4 px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
                      <div class="font-medium text-slate-800">Addresses ({{ walletCount }})</div>
                      <button
                        v-if="dataAvailable"
                        @click="downloadWalletList()"
                        class="btn-xs border-slate-200 hover:border-slate-300 place-self-end py-1 px-2">
                        <img src="../images/download-icon.svg" width="16" height="16" class="download-link-color" />
                      </button>
                    </td>
                  </tr>
                  <tbody class="text-sm">
                    <tr v-for="wallet in walletList" :key="wallet.id" :wallet="wallet">
                      <td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
                        <div class="font-medium text-slate-800"><a :href="'/wallet/' + wallet.address"
                            class="text-ralpurple-500">{{ wallet.address }}</a></div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <!-- End -->
          </div>

          <div class="px-6 py-4" v-if="dataAvailable">
            <PaginationNumeric2 :pageCount="Math.ceil(walletCount / 10)" @page="fetchPage" />
          </div>
        </div>
      </main>

    </div>

  </div>
</template>

<script>
import { ref } from 'vue'
import PaginationNumeric2 from '../components/PaginationNumeric2.vue'
import SearchForm from '../components/SearchForm.vue'
import Header from '../partials/Header.vue'
import Sidebar from '../partials/Sidebar.vue'
import { getDashboardById, getWidgetById } from '../services/dashboard'
import { getMetricProof, getMetricProofDownload } from '../services/metrics'
import { getProjectByUUID } from '../services/project'

export default {
  name: 'WalletList',
  components: {
    Sidebar,
    Header,
    SearchForm,
    PaginationNumeric2
  },
  props: ['widgetId', 'metricData'],
  data() {
    return {
      walletList: [],
      widget: {},
      dashboardName: 'Dashboard',
      dashId: -1,
      chartName: 'Chart',
      projectName: 'Loading Wallet Info...',
      isLoaded: false,
      isSegmentWidget: false,
      dataAvailable: false,
      walletCount: 0,
    }
  },
  setup() {

    const sidebarOpen = ref(false)

    return {
      sidebarOpen
    }
  },
  methods: {
    async fetchPage(page) {
      const metricData = JSON.parse(this.metricData);
      this.walletCount = metricData.data;
      const data = await getMetricProof(
        this.getNetwork(metricData.address_network_metric, metricData.metric),
        metricData.address,
        metricData.metric,
        metricData.date_processed,
        page
      );

      if (data.statusCode == 404) {
        this.dataAvailable = false;
        this.isLoaded = true;
        return;
      }
      this.walletList = data.body.map(item => {
        return { address: item, persona: 'Web3 Casual', status: "Status??" }
      });
      this.dataAvailable = true;
    },
    async downloadWalletList() {
      const metricData = JSON.parse(this.metricData);

      const data = await getMetricProofDownload(
        this.getNetwork(metricData.address_network_metric, metricData.metric),
        metricData.address,
        metricData.metric,
        metricData.date_processed
      );

      if (data.statusCode == 404) {
        this.dataAvailable = false;
        this.isLoaded = true;
        return;
      }

      window.location.href = data.body;
    },
    getNetwork(addressNetworkMetric, metric) {
      const addressNetwork = addressNetworkMetric.substring(
        0,
        (addressNetworkMetric.length - 1) - metric.length
      ).split('_');
      return addressNetwork[addressNetwork.length - 1];
    },
  },
  async mounted() {
    let widgetRes = await getWidgetById(this.widgetId);
    this.widget = widgetRes;
    this.isSegmentWidget = JSON.parse(this.widget.data).datasource == 'segment';

    if (widgetRes != null) {
      this.chartName = widgetRes.name;

      let dashboardRes = await getDashboardById(widgetRes.dashboardId);

      if (dashboardRes != null) {
        this.dashboardName = dashboardRes.name;
        this.dashId = dashboardRes.id
      } else {
        this.dashboardName = "Unknown Dashboard";
        this.dashId = 0;
      }

      if (!this.isSegmentWidget) {
        let projRes = await getProjectByUUID(widgetRes.projectId);

        if (projRes != null) {
          this.projectName = projRes[0].name;
        } else {
          this.projectName = "Unknown Project";
        }
      }
    }

    await this.fetchPage(1);
    this.isLoaded = true;
  }
}
</script>

<style>
  .download-link-color {
    filter: invert(30%) sepia(73%) saturate(6620%) hue-rotate(252deg) brightness(91%) contrast(98%);
  }
</style>
