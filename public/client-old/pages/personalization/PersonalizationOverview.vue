<template>
  <div class="flex h-screen overflow-hidden">

    <!-- Sidebar -->
    <Sidebar :sidebarOpen="sidebarOpen" @close-sidebar="sidebarOpen = false" />

    <!-- Content area -->
    <div class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">

      <!-- Site header -->
      

      <main>
        <div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">

          <!-- Page header -->
          <div class="sm:flex sm:justify-between sm:items-center mb-8">

            <!-- Left: Title -->
            <div class="mb-4 sm:mb-0">
              <h1 class="text-2xl md:text-3xl text-slate-800 font-bold">Personalization Overview</h1>
            </div>

            <!-- Right: Actions  -->
            <div class="grid grid-flow-col sm:auto-cols-max justify-start sm:justify-end gap-2">
              <button class="btn bg-indigo-500 hover:bg-indigo-600 text-white" aria-controls="add-data-modal" @click.stop="addDataModelOpen = true">
                <span class="hidden xs:block ml-2">Add Experience</span>
              </button>
            </div>
          </div>

          <div class="bg-white shadow-lg rounded-sm border border-slate-200 relative">
            <header class="px-5 py-4">
              <h2 class="font-semibold text-slate-800">Experiences <span class="text-slate-400 font-medium">2</span></h2>
            </header>
                <div class="rounded-sm border border-slate-200">
                  <div class="overflow-x-auto">
                    <table class="table-auto w-full divide-y divide-slate-200">
                      <PersonalizationTable
                        v-for="experience in experiences"
                        :key="experience.id"
                        :experience="experience"
                      />
                    </table>
                  </div>
                </div>
                <!-- End -->
          </div>
        </div>
      </main>

    </div>

  </div>
</template>

<script>
import { ref } from 'vue'
import Notification from '../../components/Notification.vue'
import PaginationNumeric from '../../components/PaginationNumeric.vue'
import SearchForm from '../../components/SearchForm.vue'
import Header from '../../partials/Header.vue'
import PersonalizationTable from '../../partials/personalization/PersonalizationTable.vue'
import Sidebar from '../../partials/Sidebar.vue'

export default {
  name: 'PersonalizationOverview',
  components: {
    Sidebar,
    Header,
    SearchForm,
    PaginationNumeric,
    PersonalizationTable,
    Notification
  },
  data() {
    return {
      experiences: []
    }
  },
  setup() {

    const sidebarOpen = ref(false)

    const notificationInfoOpen = ref(true)

    return {
      sidebarOpen,
      notificationInfoOpen
    }
  },
  async mounted() {
    var orgId = 1
    this.experiences = [{"name":"New Users L7","id":1,"experiences_ran":"503", "lift": "78%", "createdDate":"2022-06-27T15:31:51.363Z"},{"name":"Active Users L30","id":2,"experiences_ran":"20", "lift": "78%", "createdDate":"2022-06-27T15:31:51.363Z"},{"name":"At Risk Users","id":3,"experiences_ran":"1,240", "lift": "78%", "createdDate":"2022-06-27T15:31:51.363Z"}]
  }
}
</script>
