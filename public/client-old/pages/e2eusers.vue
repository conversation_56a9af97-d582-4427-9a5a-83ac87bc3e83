<template>
  <div class="flex h-screen overflow-hidden">
    <!-- Sidebar -->
    <Sidebar :sidebarOpen="sidebarOpen" @close-sidebar="sidebarOpen = false" />

    <!-- Content area -->
    <div
      class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden"
    >
      <!-- Site header -->

      <main>
        <div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">
          <!-- Page header -->
          <div class="sm:flex sm:justify-between sm:items-center mb-8">
            <!-- Left: Title -->
            <div class="mb-4 sm:mb-0">
              <h1 class="text-2xl md:text-3xl">
                🧭 <span class="font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-ralpurple-500 to-ralocean-500 hover:animate-gradient-x">{{ projectName }} User Activity</span>
              </h1>
            </div>

            <!-- Right: Actions -->
            <div
              class="
                grid grid-flow-col
                sm:auto-cols-max
                justify-start
                sm:justify-end
                gap-2
              "
            >
              <button
                class="
                  btn
                  bg-white
                  border-slate-200
                  hover:border-slate-300
                  text-slate-600
                "
              >
                <svg
                  class="w-4 h-4 fill-current text-slate-500 shrink-0"
                  viewBox="0 0 16 16"
                >
                  <path
                    d="M11.7.3c-.4-.4-1-.4-1.4 0l-10 10c-.2.2-.3.4-.3.7v4c0 .6.4 1 1 1h4c.3 0 .5-.1.7-.3l10-10c.4-.4.4-1 0-1.4l-4-4zM4.6 14H2v-2.6l6-6L10.6 8l-6 6zM12 6.6L9.4 4 11 2.4 13.6 5 12 6.6z"
                  />
                </svg>
                <span class="ml-2">Edit</span>
              </button>
            </div>
          </div>

          <!-- Cards -->
          <div class="grid grid-cols-12 gap-6">
            <!-- QUICK metrics along the top -->

            <PercentOfWalletActivity :projectId="projectId" />

            <MostActiveTime :projectId="projectId" />

            <TotalTokenHoldersUSD :projectId="projectId" />

            <HighestValueWalletETH :projectId="projectId" />

            <NewUserActivity :projectId="projectId" />

            <AtRiskTopActivityByCategory :projectId="projectId" />

            <AtRiskTopActivity :projectId="projectId" />

            <UserCommonTokens :projectId="projectId" />

            <UserActivityPersona :projectId="projectId" />

            <PersonaPie :projectId="projectId" />

          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue';
import Datepicker from '../components/Datepicker.vue';
import QuickMetric from '../components/QuickMetric.vue';
import Header from '../partials/Header.vue';
import Sidebar from '../partials/Sidebar.vue';

import BestBridgeSource from '../partials/analytics/BestBridgeSource.vue';
import HolderTypeOverTime from '../partials/analytics/HolderTypeOverTime.vue';
import InflowOutflowTable from '../partials/analytics/InflowOutflowTable.vue';
import InsightsFeed from '../partials/analytics/InsightsFeed.vue';
import InteractionsByType from '../partials/analytics/InteractionsByType.vue';
import MostPopularBridge from '../partials/analytics/MostPopularBridge.vue';
import NewCustomerOverTime from '../partials/analytics/NewCustomerOverTime.vue';
import PersonaPie from '../partials/analytics/PersonaPie.vue';
import AtRiskTopActivity from '../partials/analytics/Project/AtRiskTopActivity.vue';
import AtRiskTopActivityByCategory from '../partials/analytics/Project/AtRiskTopActivityByCategory.vue';
import UserActivityPersona from '../partials/analytics/UserActivityPersona.vue';
import UserDappActivity from '../partials/analytics/UserDappActivity.vue';
import UserLostChart from '../partials/analytics/UserLostChart.vue';
import WalletUsage from '../partials/analytics/UserMostPopularWallet.vue';
import UserPopularDapps from '../partials/analytics/UserPopularDapps.vue';

import UserCommonTokens from '../partials/analytics/Project/CommonTokensHeld.vue';
import HighestValueWalletETH from '../partials/analytics/Project/HighestValueWalletETH.vue';
import MostActiveTime from '../partials/analytics/Project/MostActiveTime.vue';
import NewUserActivity from '../partials/analytics/Project/NewUserActivity.vue';
import TotalTokenHoldersUSD from '../partials/analytics/Project/TotalTokenHoldersUSD.vue';

//Real
import PercentOfWalletActivity from '../partials/analytics/Project/PercentOfWalletActivity.vue';

export default {
  name: 'e2eusers',
  props: ['projectId'],
  components: {
    Sidebar,
    Header,
    Datepicker,
    InteractionsByType,
    InflowOutflowTable,
    UserDappActivity,
    UserCommonTokens,
    UserActivityPersona,
    UserPopularDapps,
    HolderTypeOverTime,
    MostPopularBridge,
    BestBridgeSource,
    UserLostChart,
    QuickMetric,
    NewCustomerOverTime,
    WalletUsage,
    PercentOfWalletActivity,
    InsightsFeed,
    PersonaPie,
    TotalTokenHoldersUSD,
    HighestValueWalletETH,
    MostActiveTime,
    AtRiskTopActivity,
    AtRiskTopActivityByCategory,
    NewUserActivity
  },
  data() {
    return {
      dataTest: { "title": "Test Title", "infoLabel": "test info", "apiName": "getProjectAtRiskActivities", "inputs": ["category", "count"], "XAxisInputLabel": "category"}
    }
  },
  setup() {
    const sidebarOpen = ref(false);
    const projectName = 'CowSwap';

    return {
      sidebarOpen,
      projectName,
    };
  },
};
</script>
