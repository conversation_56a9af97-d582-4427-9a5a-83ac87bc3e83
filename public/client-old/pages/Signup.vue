<template>
  <main class="bg-white">

    <div class="relative flex">

      <!-- Content -->
      <div class="w-full md:w-1/2">
        <div class="min-h-screen h-full flex flex-col after:flex-1">

          <div class="flex-1">
            <div class="flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8">
              <!-- Logo -->
              <router-link class="block" to="/">
                <img src="../images/raleon-logo-whitebg.jpg" width="64" height="64" />
              </router-link>
            </div>
          </div>

          <div class="max-w-sm mx-auto px-4 py-8">
            <h1 class="text-3xl text-slate-800 font-bold mb-2">Link Your Account ✨</h1>
            <!-- Form -->
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium mb-1" for="email">Email Address
                  <span class="text-rose-500">*</span>
                </label>
                <input v-if="!selfService" v-model="v$.email.$model" id="email" class="form-input w-full disabled:border-slate-300 disabled:text-slate-400 disabled:cursor-not-allowed" disabled type="email" />
				<input v-if="selfService" v-model="v$.email.$model" id="email" class="form-input w-full disabled:border-slate-300 disabled:text-slate-400 disabled:cursor-not-allowed" type="email" />
                <div class="input-errors" v-for="error of v$.email.$errors" :key="error.$uid">
                  <div class="error-msg text-center text-sm font-small text-rose-400 pt-2">{{ error.$message }}</div>
                </div>
              </div>
              <div v-if="selfService">
                <label class="block text-sm font-medium mb-1" for="organizationName">Project Name
                  <span class="text-rose-500">*</span>
                </label>
                <input v-model="v$.organizationName.$model" id="organizationName" class="form-input w-full" type="text" />
                <div class="input-errors" v-for="error of v$.organizationName.$errors" :key="error.$uid">
                  <div class="error-msg text-center text-sm font-small text-rose-400 pt-2">{{ error.$message }}</div>
                </div>
              </div>
              <div v-if="!selfService && !isSecondaryAccount">
                <label class="block text-sm font-medium mb-1" for="firstName">First Name
                  <span class="text-rose-500">*</span>
                </label>
                <input v-model="v$.firstName.$model" id="firstName" class="form-input w-full" type="text" />
                <div class="input-errors" v-for="error of v$.firstName.$errors" :key="error.$uid">
                  <div class="error-msg text-center text-sm font-small text-rose-400 pt-2">{{ error.$message }}</div>
                </div>
              </div>
              <div v-if="!selfService && !isSecondaryAccount">
                <label class="block text-sm font-medium mb-1" for="lastName">Last Name
                  <span class="text-rose-500">*</span>
                </label>
                <input v-model="v$.lastName.$model" id="lastName" class="form-input w-full" type="text" />
                <div class="input-errors" v-for="error of v$.lastName.$errors" :key="error.$uid">
                  <div class="error-msg text-center text-sm font-small text-rose-400 pt-2">{{ error.$message }}</div>
                </div>
              </div>
			  <div v-if="!isSecondaryAccount">
				<label class="block text-sm font-medium" for="password">Password
					<span class="text-rose-500">*</span>
				</label>
				<div class="relative" style="margin-top: 0.25rem;">
					<input v-model="v$.password.$model" id="password" class="form-input w-full" type="password" autoComplete="on" />
					<img
					v-if="!showPassword"
					id="pwEye"
					class="ml-1 absolute mt-1 hover:cursor-pointer top-0 right-0"
					src="../images/password-show.svg"
					width="40"
					height="40"
					alt="Password hidden"
					@click.stop="showHidePw" />

					<img
					v-if="showPassword"
					id="pwEye"
					class="ml-1 absolute mt-1 hover:cursor-pointer top-0 right-0"
					src="../images/password-hide.svg"
					width="40"
					height="40"
					alt="Password hidden"
					@click.stop="showHidePw" />
					<div class="input-errors" v-for="error of v$.password.$errors" :key="error.$uid">
						<div class="error-msg text-center text-sm font-small text-rose-400 pt-2">{{ error.$message }}</div>
					</div>
			  </div>
			  </div>
			  <div v-if="!isSecondaryAccount">
				<label class="block text-sm font-medium mb-1" for="passwordConfirm">Confirm Password
					<span class="text-rose-500">*</span>
				</label>
				<div class="relative" style="margin-top: 0.25rem;">
					<input v-model="v$.passwordConfirm.$model" id="passwordConfirm" class="form-input w-full" type="password" autoComplete="on" />
					<img
					v-if="!showConfirmPassword"
					id="confirmPwEye"
					class="ml-1 absolute mt-1 hover:cursor-pointer top-0 right-0"
					src="../images/password-show.svg"
					width="40"
					height="40"
					alt="Password hidden"
					@click.stop="showHidePw" />

					<img
					v-if="showConfirmPassword"
					id="confirmPwEye"
					class="ml-1 absolute mt-1 hover:cursor-pointer top-0 right-0"
					src="../images/password-hide.svg"
					width="40"
					height="40"
					alt="Password hidden"
					@click.stop="showHidePw" />
					<div class="input-errors" v-for="error of v$.passwordConfirm.$errors" :key="error.$uid">
					<div class="error-msg text-center text-sm font-small text-rose-400 pt-2">{{ error.$message }}</div>
					</div>
				</div>
            </div>
			</div>
            <div class="mt-2" v-if="signupError">
              <div class="error-msg text-center text-sm font-small text-rose-400 pt-2">{{signupError}}</div>
            </div>
            <div class="flex items-center justify-between mt-4">
              <button
                  class="btn bg-indigo-500 hover:bg-indigo-600 text-white whitespace-nowrap disabled:border-slate-300 disabled:bg-slate-100 disabled:text-slate-400 disabled:cursor-not-allowed w-full"
                  :disabled="signupDisabled"
                  @click.stop="signup">
                <svg v-if="signingUp" class="animate-spin w-4 h-4 fill-current shrink-0 mr-2" viewBox="0 0 16 16">
                  <path d="M8 16a7.928 7.928 0 01-3.428-.77l.857-1.807A6.006 6.006 0 0014 8c0-3.309-2.691-6-6-6a6.006 6.006 0 00-5.422 8.572l-1.806.859A7.929 7.929 0 010 8c0-4.411 3.589-8 8-8s8 3.589 8 8-3.589 8-8 8z" />
                </svg>
                Link
            </button>
            </div>
            <!-- Footer -->
            <!-- <div class="pt-5 mt-6 border-t border-slate-200">
              <div class="text-sm">
                Have an account?
                <router-link class="font-medium text-indigo-500 hover:text-indigo-600" to="/signin">Sign In</router-link>
              </div>
            </div> -->
          </div>

        </div>
      </div>

      <!-- Image -->
      <div class="hidden md:block absolute top-0 bottom-0 right-0 md:w-1/2" aria-hidden="true">
        <img class="object-cover object-center w-full h-full" src="../images/r-login-bg.png" width="760" height="1024"
          alt="Authentication" />
      </div>
    </div>

  </main>
</template>

<script>

import { useVuelidate } from '@vuelidate/core'
import { required, email, minLength, sameAs, helpers } from '@vuelidate/validators'
import { ref } from 'vue'
import { acceptInvite, selfService } from '../services/invite';
import { userLogin, getUserInfo, setUserInfo } from '../services/user';
import { Crisp } from "crisp-sdk-web";

export default {
  name: 'Signup',
  data() {
    return {
      email: '',
      firstName: '',
      lastName: '',
      password: '',
	  organizationName: '',
      passwordConfirm: '',
      inviteCode: '',
      showPassword: false,
      showConfirmPassword: false,
      emailOptIn: false,
      signingUp: false,
      signupError: '',
    }
  },
  setup() {
    return {
      v$: useVuelidate(),
    }
  },
  computed: {
    selfService() {
      return !this.inviteCode;
    },
    signupDisabled() {
      return this.v$.$invalid || this.signingUp;
    },
	isSecondaryAccount() {
		return location?.search?.includes('isSecondaryAccount=true');
	}
  },
  methods: {
    async login() {
      const loginResult = await userLogin(this.email, this.password);

      if (loginResult.token) {
        localStorage.setItem('token', loginResult.token);
        let userInfo = await getUserInfo();
        await setUserInfo(userInfo);
        this.$router.push(this.selfService ? '/chats' : '/chats');
      } else {
        console.error('Error logging in');
      }
    },
    async signup() {
      this.signingUp = true;
      const { email, firstName, lastName, password, inviteCode, organizationName } = this;

	  if (this.selfService) {
		try {
			await selfService({ email, password, firstName, lastName, organizationName });
			let userInfo = await getUserInfo();
			await setUserInfo(userInfo);
			localStorage.setItem('email', email);
			localStorage.setItem('selfService', 'true');
			localStorage.setItem('snippetVerified', 'false');

			localStorage.setItem('onboardingComplete', 'false');
			await this.login(this.email, this.password);
			this.signingUp = false;

		} catch (e) {
			this.signupError = e.message;
			this.signingUp = false;
		}

		return;
	  }

      try {
        await acceptInvite({ email, firstName, lastName, password, inviteCode}, this.isSecondaryAccount);
		if (this.isSecondaryAccount) {
			this.signingUp = false;
			this.$router.push('/ai-strategist/planning');
		} else {
			await this.login(this.email, this.password);
		}
        this.signingUp = false;
      } catch (e) {
        this.signupError = e.message;
        this.signingUp = false;
      }
    },
    showHidePw(event) {
      if (event.target.id == 'pwEye') {
        this.showPassword = !this.showPassword;
        document.getElementById('password').setAttribute('type', `${this.showPassword ? 'text' : 'password'}`);
      } else if (event.target.id == 'confirmPwEye') {
        this.showConfirmPassword = !this.showConfirmPassword;
        document.getElementById('passwordConfirm').setAttribute('type', `${this.showConfirmPassword ? 'text' : 'password'}`);
      }
    },
  },
  async mounted() {
    if (this.$route.query.email) {
      this.email = decodeURIComponent(this.$route.query.email);

      if (!this.$route.query['invite-code']) throw new Error('Invite Code must be specified');

      this.inviteCode = decodeURIComponent(this.$route.query['invite-code']);
    }
  },
  validations() {
    return {
      email: {
        required: helpers.withMessage('Email is required', required),
        email: helpers.withMessage('Email is not valid', email)
      },
      organizationName: this.selfService ? { required: helpers.withMessage('First Name is required', required) } : {},
      firstName: this.selfService || this.isSecondaryAccount ? {} : { required: helpers.withMessage('First Name is required', required) },
      lastName: this.selfService || this.isSecondaryAccount ? {} : { required: helpers.withMessage('Last Name is required', required) },
      password: this.isSecondaryAccount ? {} : {
        required: helpers.withMessage('Password is required.', required),
        minLength: helpers.withMessage('Passwords must be at least 8 characters.', minLength(8)),
      },
      passwordConfirm: this.isSecondaryAccount ? {} :  {
        required: helpers.withMessage('Password is required.', required),
        minLength: helpers.withMessage('Passwords must be at least 8 characters.', minLength(8)),
        sameAsPassword: helpers.withMessage('Passwords do not match.', sameAs(this.password)),
      },
    }
  },
}
</script>
