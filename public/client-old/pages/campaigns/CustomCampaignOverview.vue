<template>
  <div class="flex h-screen overflow-hidden">

    <!-- Sidebar -->
    <Sidebar :sidebarOpen="sidebarOpen" @close-sidebar="sidebarOpen = false" />

    <!-- Content area -->
    <div class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
      
      <!-- Site header -->
      

      <main>
        <div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">

          <!-- Page header -->
          <div class="sm:flex sm:justify-between sm:items-center mb-8">

            <!-- Left: Title -->
            <div class="mb-4 sm:mb-0">
              <h1 class="text-2xl md:text-3xl text-slate-800 font-bold">Campaign Tracker</h1>
            </div>

            <!-- <div>Reports: Growth over last 30. Event Type by Impact. # Number of campaigns over X period have generated Y growth. Success by type. IE: Webinars drive the most</div> -->

            <!-- Right: Actions  -->
            <div class="grid grid-flow-col sm:auto-cols-max justify-start sm:justify-end gap-2">
              <button class="btn bg-indigo-500 hover:bg-indigo-600 text-white" aria-controls="add-data-modal" @click.stop="addDataModelOpen = true">
                <span class="hidden xs:block ml-2">Add Event</span>
              </button>
            </div>            

          </div>

          <CampaignTable />

        </div>
      </main>

    </div> 

  </div>
</template>

<script>
import { ref } from 'vue'
import Sidebar from '../../partials/Sidebar.vue'
import Header from '../../partials/Header.vue'
import SearchForm from '../../components/SearchForm.vue'
import PaginationNumeric from '../../components/PaginationNumeric.vue'
import CampaignTable from '../../partials/campaigns/CampaignTable.vue'

import Image01 from '../../images/user-64-01.jpg'
import Image02 from '../../images/user-64-02.jpg'
import Image03 from '../../images/user-64-03.jpg'
import Image04 from '../../images/user-64-04.jpg'
import Image05 from '../../images/user-64-05.jpg'
import Image06 from '../../images/user-64-06.jpg'
import Image07 from '../../images/user-64-07.jpg'
import Image08 from '../../images/user-64-08.jpg'
import Image09 from '../../images/user-64-09.jpg'
import Image10 from '../../images/user-64-10.jpg'
import Image11 from '../../images/user-64-11.jpg'
import Image12 from '../../images/user-64-12.jpg'

export default {
  name: 'CampaignOverview',
  components: {
    Sidebar,
    Header,
    SearchForm,
    PaginationNumeric,
    CampaignTable,
  },
  setup() {

    const sidebarOpen = ref(false)

    return {
      sidebarOpen
    }  
  }
}
</script>