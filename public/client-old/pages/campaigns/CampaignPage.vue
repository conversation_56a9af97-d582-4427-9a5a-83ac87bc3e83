<template>
	<div class="flex h-screen overflow-hidden bg-white">
		<Sidebar :sidebarOpen="sidebarOpen" @close-sidebar="sidebarOpen = false" />

		<div class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
			<main>

				<div class="flex flex-row snippet-banner bg-ralprimary-main justify-center items-center" v-if="isSnippetWarningVisible">
					<h1 class="mr-4">Snippet May Not Be Installed</h1>
					<p class="mr-4">Snippet must be installed for this {{  questOrCampaign }} to be visible on your site</p>
					<a class="text-white" href="/snippet">Verify Now</a>

				</div>

				<div class="px-6 sm:px-6 lg:px-12 py-8 w-full max-w-9xl mx-auto">


					<!-- Page header -->
					<div class="flex sm:justify-between sm:items-center mb-4">
						<div v-if="!campaign.name" class="bg-gray-200 w-1/4 animate-pulse h-5 rounded-2xl"></div>
						<h1 class="text-2xl md:text-3xl text-ralblack-primary font-bold" data-cy="campaign-name">
							{{ campaign.name }}
						</h1>
						<div class="flex">
							<button v-if="campaign.status" class="cursor-text mr-4 rounded-md py-1 px-6 text-sm" :class="{
								'bg-ralgray-light': campaign.status === 'Draft',
								'text-ralblack-primary': campaign.status === 'Draft',
								'bg-raltable-ring-secondary': campaign.status === 'Running',
								'text-ralprimary-dark': campaign.status === 'Running',
								'bg-ralsuccess-light': campaign.status === 'Completed',
								'text-ralsuccess-dark': campaign.status === 'Completed',
							}">
								{{ campaign.status }}
							</button>
							<button v-if="editButtonName"
								class="text-ralprimary-light border border-ralprimary-light font-bold text-xs py-2 px-4 rounded-button ml-4"
								@click="navigateToEditCampaign" data-cy="edit-campaign">
								{{ editButtonName }}
							</button>
						</div>
					</div>
					<div class="border-b border-ralwhite-line"></div>

					<div class="flex flex-col pt-10">
						<div class="flex">
							<img src="../../images/insights.svg" alt="Performance" class="mr-2" />
							<h1 class="text-lg md:text-xl font-bold text-ralblack-primary">Performance Metrics</h1>
						</div>

						<div class="flex mt-6 rounded-xl bg-raltable-ring-secondary">
							<div
								class="flex flex-col justify-center m-2 mr-0 pl-3 h-36 w-auto basis-1/3 bg-white rounded-xl">
								<img class="h-6 w-6" src="../../images/user-views.svg" />
								<div class="text-overline uppercase pt-2">Total Views</div>
								<div v-if="metricsLoading"
									class="bg-gray-300 w-8 h-8 rounded-xl text-center mt-2 mb-4 animate-pulse">
									<span class="pt-2 text-2xl text-gray-100 animate-pulse">#</span>
								</div>
								<h2 v-if="!metricsLoading" class="pt-1 text-2xl font-normal">{{
									this.formatNumber(this.totalViews) }}</h2>
							</div>

							<div class="flex flex-col justify-center m-2 pl-3 h-36 w-auto basis-1/3 bg-white rounded-xl">
								<img class="h-6 w-6" src="../../images/user-check.svg" />
								<div class="text-overline uppercase pt-2">Unique Views</div>
								<div v-if="metricsLoading"
									class="bg-gray-300 w-8 h-8 rounded-xl text-center mt-2 mb-4 animate-pulse">
									<span class="pt-2 text-2xl text-gray-100 animate-pulse">#</span>
								</div>
								<h2 v-if="!metricsLoading" class="pt-1 text-2xl font-normal">{{
									this.formatNumber(this.uniqueViews) }}</h2>
							</div>

							<div
								class="flex flex-col justify-center m-2 ml-0 pl-3 h-36 w-auto basis-1/3 bg-white rounded-xl">
								<img class="h-5 w-5" src="../../images/percent.svg" />
								<div class="text-overline uppercase pt-2">Engagement</div>
								<div v-if="metricsLoading"
									class="bg-gray-300 w-8 h-8 rounded-xl text-center mt-2 mb-4 animate-pulse">
									<span class="pt-2 text-2xl text-gray-100 animate-pulse">%</span>
								</div>
								<h2 v-if="!metricsLoading" class="pt-1 text-2xl font-normal">
									{{ getEngagementPercentage }}
								</h2>
							</div>
						</div>
					</div>

					<div class="flex flex-col pt-10" v-if="this.isNativeQuest">
						<div class="flex">
							<img src="../../images/star-ribbon.svg" alt="Performance" class="mr-2" />
							<h1 class="text-lg md:text-xl font-bold text-ralblack-primary">Quest Metrics</h1>
						</div>

						<div class="flex mt-6 rounded-xl bg-raltable-ring-tertiary">
							<div
								class="flex flex-col justify-center m-2 mr-0 pl-3 h-36 w-auto basis-1/4 bg-white rounded-xl">
								<img class="h-6 w-6" src="../../images/check-ralpurple.svg" />
								<div class="text-overline uppercase pt-2">Completions</div>
								<div v-if="metricsLoading"
									class="bg-gray-300 w-8 h-8 rounded-xl text-center mt-2 mb-4 animate-pulse">
									<span class="pt-2 text-2xl text-gray-100 animate-pulse">#</span>
								</div>
								<h2 v-if="!metricsLoading" class="pt-1 text-2xl font-normal">{{
									this.formatNumber(this.questCompletedNumber) }}</h2>
							</div>
							<div
								class="flex flex-col justify-center m-2 mr-0 pl-3 h-36 w-auto basis-1/4 bg-white rounded-xl">
								<img class="h-6 w-6" src="../../images/anchor.svg" />
								<div class="text-overline uppercase pt-2">Started But Not Completed</div>
								<div v-if="metricsLoading"
									class="bg-gray-300 w-8 h-8 rounded-xl text-center mt-2 mb-4 animate-pulse">
									<span class="pt-2 text-2xl text-gray-100 animate-pulse">%</span>
								</div>
								<h2 v-if="!metricsLoading" class="pt-1 text-2xl font-normal">{{
									this.formatNumber(this.startedPercentage) }}%</h2>
							</div>

							<div
								class="flex flex-col justify-center m-2 mr-0 pl-3 h-36 w-auto basis-1/4 bg-white rounded-xl">
								<img class="h-5 w-5" src="../../images/percent.svg" />
								<div class="text-overline uppercase pt-2">Conversion</div>
								<div v-if="metricsLoading"
									class="bg-gray-300 w-8 h-8 rounded-xl text-center mt-2 mb-4 animate-pulse">
									<span class="pt-2 text-2xl text-gray-100 animate-pulse">%</span>
								</div>
								<h2 v-if="!metricsLoading" class="pt-1 text-2xl font-normal">{{
									this.formatNumber(this.conversionRate) }}%</h2>
							</div>

							<div class="flex flex-col justify-center m-2 pl-3 h-36 w-auto basis-1/4 bg-white rounded-xl">
								<img class="h-6 w-6" src="../../images/list.svg" />
								<div class="text-overline uppercase pt-2">Action Completion</div>
								<div class="flex">
									<div v-if="metricsLoading"
										class="bg-gray-300 w-8 h-8 rounded-xl text-center mt-2 mb-4 animate-pulse">
										<span class="pt-2 text-2xl text-gray-100 animate-pulse">%</span>
									</div>
									<h2 v-if="!metricsLoading" class="pt-1 text-2xl font-normal">{{
										this.formatNumber(this.questActionEngagementPercentage) }}%</h2>
									<span class="flex items-center ml-auto mr-2 relative">
										<img class="h-5 w-5" src="../../images/checklist.svg" />
										<span class="ml-1">{{ questActionCount }}</span>
										<span class="ml-1 text-xs text-ralprimary-light cursor-default"
											@mouseover="showQuestActionTooltip = true"
											@mouseleave="showQuestActionTooltip = false">
											<span>Expand</span>
											<template v-if="!metricsLoading && showQuestActionTooltip">
												<div
													class="absolute z-10 bottom-10 right-0 w-64 p-4 text-sm leading-tight text-ralprimary-light transform bg-white rounded-lg drop-shadow-xl">
													<span class="flex text-ralblack-primary">
														<img class="h-5 w-5" src="../../images/checklist.svg" />
														<span class="mb-2 ml-2">Actions</span>
													</span>
													<div class="border-b border-ralwhite-line mb-2"></div>
													<span v-if="!metricsLoading"
														v-for="(action, i) of this.questActionEngagement"
														class="pt-1 text-sm font-normal">
														<span class="flex items-center">
															<img v-if="i === 0" class="h-4 w-4"
																src="../../images/box-one.svg" />
															<img v-else-if="i === 1" class="h-4 w-4"
																src="../../images/box-two.svg" />
															<img v-else-if="i === 2" class="h-4 w-4"
																src="../../images/box-three.svg" />
															<span class="ml-2 text-ralblack-primary">{{ action.name
															}}</span>
															<span class="ml-auto text-ralprimary-dark">
																{{ this.formatNumber(action.value) }}%
															</span>
														</span>
													</span>
												</div>
											</template>
										</span>
									</span>
								</div>
							</div>
						</div>
					</div>

					<!-- Discord Metrics -->
					<div class="flex flex-col pt-10" v-if="this.hasDiscordAction">
						<div class="flex">
							<img src="../../images/star.svg" alt="Performance" class="mr-2" />
							<h1 class="text-lg md:text-xl font-bold text-ralblack-primary">Quest Action Details</h1>
						</div>

						<div class="flex mt-6 rounded-xl bg-raltable-ring-secondary">
							<div
								class="flex flex-col justify-center m-2 mr-0 pl-3 h-36 w-auto basis-1/3 bg-white rounded-xl">
								<img class="h-6 w-6" src="../../images/check-ralpurple.svg" />
								<div class="text-overline uppercase pt-2">Joined Discord Server</div>
								<div v-if="metricsLoading"
									class="bg-gray-300 w-8 h-8 rounded-xl text-center mt-2 mb-4 animate-pulse">
									<span class="pt-2 text-2xl text-gray-100 animate-pulse">#</span>
								</div>
								<h2 v-if="!metricsLoading" class="pt-1 text-2xl font-normal">{{
									this.formatNumber(this.discordActionCompletedWallets?.length || 0) }}</h2>
								<span
									class="flex items-center ml-auto mr-2 relative hover:cursor-pointer"
									@click.stop="downloadDiscordCsv">
									<img class="h-5 w-5" src="../../images/download.svg" />
								</span>
							</div>
						</div>
					</div>


					<div class="pt-8 border-b border-ralwhite-line"></div>

					<div class="flex flex-col pt-10">
						<div class="flex">
							<img src="../../images/user-group.svg" alt="Audience" class="mr-2" />
							<h1 class="text-lg md:text-xl font-bold text-ralblack-primary">Audience</h1>
						</div>

						<RaleonTable class="mt-3" :column-headers="this.columnHeaders" :row-data="this.rowData"
							:is-loading="isLoading">
						</RaleonTable>
					</div>
				</div>
			</main>

			<NewCampaignModal v-if="showModal" :show="showModal" :campaign="campaign" :audiences="audiences"
				@close="showModal = false" @create="getCampaign" />

		</div>
	</div>
</template>

<script>
import { ref } from 'vue'
import Sidebar from "../../partials/Sidebar.vue"
import RaleonTable from '../component/RaleonTable.vue'
import NewCampaignModal from './NewCampaignModal.vue'
import * as Utils from '../../utils/Utils';
import { CAMPAIGN_TYPES } from '../campaign-builder/building-blocks/campaign-types';
const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	name: 'CampaignPage',
	props: [],
	emits: [],
	components: {
		Sidebar,
		RaleonTable,
		NewCampaignModal
	},
	setup() {
		const sidebarOpen = ref(false);
		return {
			sidebarOpen,
		}
	},
	data() {
		return {
			isLoading: false,
			metricsLoading: false,
			isSnippetWarningVisible: false,
			campaign: {},
			columnHeaders: [
				{
					name: 'Wallet Address',
					tooltip: 'Address of the wallet in this audience',
					value: 'walletAddress',
				},
				{
					name: 'Status',
					tooltip: 'Has this wallet converted?',
					value: 'status',
				},
			],
			rowData: [],
			totalViews: 0,
			uniqueViews: 0,
			conversionRate: 0,
			engagementPercentage: '',
			startedPercentage: 0,
			questActionEngagement: [],
			questActionEngagementPercentage: 0,
			questCompletedNumber: 0,
			discordActionCompletedWallets: [],
			showModal: false,
			showQuestActionTooltip: false,
			audiences: [],
			discordActionId: '',
			walletDiscordInfo: [],
		}
	},
	mounted() {
		this.isLoading = true;
		this.getCampaign();
		this.getMetrics();
		this.getAudiences().then(() => this.isLoading = false);
		amplitude.getInstance().logEvent('CAMPAIGN_ANALYTICS');
	},
	computed: {
		editButtonName() {
			if (this.campaign?.type === CAMPAIGN_TYPES.NativeQuest.NAME) {
				return 'Edit Quest';
			} else if (this.campaign?.type === CAMPAIGN_TYPES.ActionPrompt.NAME) {
				return 'Edit Action Prompt';
			}
		},
		questOrCampaign() {
			if (this.campaign?.type === CAMPAIGN_TYPES.NativeQuest.NAME) {
				return 'Quest';
			} else if (this.campaign?.type === CAMPAIGN_TYPES.ActionPrompt.NAME) {
				return 'Action Prompt';
			}
		},
		questActionCount() {
			return this.campaign?.quests[0]?.goals?.length || 0;
		},
		isNativeQuest() {
			return this.campaign.type === CAMPAIGN_TYPES.NativeQuest.NAME;
		},
		isActionPrompt() {
			return this.campaign.type === CAMPAIGN_TYPES.ActionPrompt.NAME;
		},
		hasDiscordAction() {
			const hasDiscord = this.isNativeQuest && this.campaign?.quests[0]?.goals?.some(goal => goal.type === 'JoinDiscordServer');
			this.discordActionId = hasDiscord ? this.campaign?.quests[0]?.goals?.find(goal => goal.type === 'JoinDiscordServer')?.id : '';
			return hasDiscord;
		},
		getEngagementPercentage() {
			if (this.isNativeQuest) {
				if (this.engagementPercentage == 0 || this.engagementPercentage == 'N/A') {
					return 'N/A';
				} else {
					return `${this.formatNumber(this.engagementPercentage)}%`;
				}
			} else {
				return `${this.formatNumber(this.conversionRate)}%`;
			}
		}
	},
	methods: {
		async getCampaign() {
			const campaignId = this.$attrs.id;
			const response = await fetch(`${URL_DOMAIN}/campaigns/${campaignId}`, {
				method: 'GET',
				credentials: 'omit', // include, *same-origin, omit
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
			});
			this.campaign = await response.json();
			this.showModal = false;

			if (this.campaign.status == 'Running' && localStorage.getItem('snippetVerified') != 'true') {
				this.isSnippetWarningVisible = true;
			}
		},
		async getMetrics() {
			this.metricsLoading = true;
			const campaignId = this.$attrs.id;
			const response = await fetch(`${URL_DOMAIN}/campaigns/${campaignId}/metrics`, {
				method: 'GET',
				credentials: 'omit', // include, *same-origin, omit
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
			});

			const metricsContainer = await response.json();
			const metrics = metricsContainer[0]?.metrics;

			if (this.isActionPrompt) {
				this.totalViews = metrics.find(x => x.metricName === 'total_views').metricValue;
				this.uniqueViews = metrics.find(x => x.metricName === 'unique_views').metricValue;
				this.conversionRate = metrics.find(x => x.metricName === 'conversion_rate').metricValue;
				this.metricsLoading = false;
			} else if (this.isNativeQuest) {
				this.totalViews = metrics.find(x => x.metricName === 'total_views')?.metricValue;
				this.uniqueViews = metrics.find(x => x.metricName === 'unique_views')?.metricValue;
				this.engagementPercentage = metrics.find(x => x.metricName === 'engagement_percentage')?.metricValue;
				this.questCompletedNumber = metrics.find(x => x.metricName === 'quest_completed')?.metricValue;
				this.conversionRate = metrics.find(x => x.metricName === 'conversion_rate')?.metricValue;
				this.startedPercentage = metrics.find(x => x.metricName === 'started_but_not_completed')?.metricValue;
				if (!this.startedPercentage) {
					this.startedPercentage = 0;
				}

				const questActions = this.campaign?.quests[0]?.goals;
				const uniqueViewCount = parseInt(this.uniqueViews);
				this.questActionEngagement = metrics
					.filter(x => x.metricName.startsWith('addresses_') && x.metricName.endsWith('_completed'))
					.map(x => ({
						name: questActions.find(y => y.id == x.metricName.split('_')[1])?.name,
						value: uniqueViewCount !== 0 ? (x.metricValue.length / uniqueViewCount) * 100 : 0,
					}))
					.sort((a, b) => b.value - a.value);

				this.questActionEngagementPercentage = this.questActionEngagement.length > 0
					? (this.questActionEngagement.reduce(
						(acc, curr) => acc + curr.value, 0
						) / this.questActionEngagement.length)
					: 0;

				if (this.discordActionId) {
					const discordActionMetric = metrics.filter(x => x.metricName == `addresses_${this.discordActionId}_completed`)?.[0] || {};
					this.discordActionCompletedWallets = discordActionMetric.metricValue;
					await this.getDiscordInfoWallets();
				}

				this.metricsLoading = false;
			}

			const userJourneys = metrics.find(x => x.metricName === 'journeys')?.metricValue;
			const identities = userJourneys?.map(x => {
				const identities = x.raleonUser.raleonUserIdentities;
				const status = x.status;
				if (!identities || identities.length === 0)
					return [];
				return identities.map(y => ({
					...y,
					converted: x.status === 'completed',
					user: x.raleonUser
				}));
			}).flat();

			const wallets = identities?.filter(x => x.identityType === 'address') || [];

			this.rowData = Array.from(wallets).map((wallet) => ({
				walletAddress: {
					value: wallet.identityValue,
				},
				status: {
					value: wallet.converted ? 'Converted' : 'Viewed',
					color: this.getStatusTextColor(wallet.converted ? 'Converted' : 'Viewed')
				},
				href: wallet.identityValue.startsWith('anonymous') ? undefined : `/wallet/${wallet.identityValue}`,
				hrefStyle: 'mr-auto ml-6',
				target: '_blank'
			}));
		},
		getStatusTextColor(status) {
			const additionalStyles = 'rounded-2xl px-2.5 py-1.5 min-w-full text-center'
			switch (status) {
				case 'Converted':
					return `text-ralsuccess-dark bg-ralsuccess-light ${additionalStyles}`
				case 'Viewed':
					return `text-ralwarning-dark bg-ralwarning-light ${additionalStyles}`
				default:
					return `text-ralinfo-dark bg-ralinfo-light ${additionalStyles}`
			}
		},
		formatNumber(num) {
			if (!num) {
				num = 0;
			}
			return Utils.formatNumber(num);
		},
		formatPercent(pct) {
			return Utils.formatNumber((pct * 100).toFixed(0));
		},
		async getDiscordInfoWallets() {
			const response = await fetch(`${URL_DOMAIN}/campaigns/discord-info`, {
				method: 'POST',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify(this.discordActionCompletedWallets),
			});
			const discordInfo = await response.json();
			this.walletDiscordInfo = discordInfo;
			console.log(JSON.stringify(this.walletDiscordInfo));
		},
		convertToCSV(objArray) {
			const array = typeof objArray !== 'object' ? JSON.parse(objArray) : objArray;
			let str = `${Object.keys(array[0]).join()},\r\n`;

			for (let i = 0; i < array.length; i++) {
				let line = '';
				for (let index in array[i]) {
					if (line !== '') line += ',';
					line += array[i][index];
				}
				str += `${line}\r\n`;
			}
			return str;
		},
		async downloadDiscordCsv() {
			console.log(`downloading`)
			const csvData = this.convertToCSV(this.walletDiscordInfo);
			const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
			const url = URL.createObjectURL(blob);

			let link = document.createElement('a');
			link.setAttribute('href', url);
			link.setAttribute('download', 'discord-user-data.csv');
			link.style.visibility = 'hidden';
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);
		},
		async getAudiences() {
			const response = await fetch(`${URL_DOMAIN}/segment-list`, {
				method: 'GET',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
			});
			const audiences = await response.json();

			if (audiences.length > 0) {
				this.audiences = audiences
					.sort((a, b) => a.name.localeCompare(b.name))
			}
		},
		navigateToEditCampaign() {
			if (this.campaign?.type == CAMPAIGN_TYPES.NativeQuest.NAME) {
				this.$router.push(
					`/QuestEditor?id=${this.$attrs.id}&quest_id=${this.campaign?.quests?.[0]?.id}`
				);
			} else {
				this.$router.push(`/campaign-builder?id=${this.$attrs.id}`);
			}
		}
	}
}

</script>

<style>

.snippet-banner {
  padding: 20px;
  text-align: center;
}

.snippet-banner h1 {
  color: #ffffff;
  font-size: 1em;
  font-weight: bold;
}

.snippet-banner p {
  color: #ffffff;
  font-size: 1em;
}

.snippet-banner a {
  background-color: #60A5FA;
  padding-left: 1em;
  padding-right: 1em;
  line-height: 2em;
  height: 2em;
  border: none;
  border-radius: 1em;
  cursor: pointer;
}
</style>
