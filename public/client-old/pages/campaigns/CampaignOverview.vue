<template>
	<div class="flex h-screen overflow-hidden">
		<!-- Sidebar -->
		<Sidebar :sidebarOpen="sidebarOpen" @close-sidebar="sidebarOpen = false" />

		<!-- Content area -->
		<div class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">

			<main>
				<div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">
					<!-- Page header -->
					<div class="sm:flex sm:justify-between sm:items-center mb-8">
						<!-- Left: Title -->
						<div class="mb-4 sm:mb-0">
							<h1 class="text-2xl md:text-3xl text-slate-800 font-bold">
								Attribution
							</h1>
							<p class="text-sm mt-1 text-slate-400">Drive the right business results by connecting the dots
								between your off-chain activity and your on-chain results.<br >
							<a href="https://docs.raleon.io/docs/setting-up-off-chain-data-for-marketing-attribution" class="text-ralprimary-main">📖 Learn more about attribution and how to use it.</a>
							</p>
						</div>

						<!-- Right: Actions  -->
						<div class="grid grid-flow-col sm:auto-cols-max justify-start sm:justify-end gap-2">

						<button
							class="ml-auto btn border-slate-200 hover:border-slate-300 text-slate-600"
							aria-controls="add-data-modal"
							@click.stop="linkBuilderModelOpen = true"
						>
							<span class="hidden xs:block">Attribution Link Builder</span>
						</button>

						</div>
					</div>

					<div class="mb-5" v-if="getProjects().length && !attributionCTA">
						<label class="block text-sm font-medium mb-1" for="role">Which project would you like to
							use?</label>
						<DropdownFull :options="getProjects()" v-on:change="projectChanged" :selectedId="selectedProjectId"
							:clearOnClick="true" />
					</div>

					<!-- Campaign Table -->
					<div v-if="attributionCTA == false" class="bg-white shadow-lg rounded-sm border border-slate-200">
						<header class="px-5 py-4">
							<h2 class="flex font-semibold text-slate-800">
								Campaign Results
								<span class="text-slate-400 font-medium ml-2" v-if="isLoaded">{{
									campaignCount
								}}</span>
							</h2>
						</header>
						<div class="rounded-sm border border-slate-200">
							<div class="overflow-x-auto">
								<div class="flex flex-col flex-1 gap-5 sm:p-2" v-if="!isLoaded">
									<div class="
		                      bg-gray-200
		                      w-full
		                      animate-pulse
		                      h-6
		                      rounded-2xl
		                      mt-2
		                    "></div>
									<div class="bg-gray-200 w-full animate-pulse h-6 rounded-2xl"></div>
									<div class="bg-gray-200 w-full animate-pulse h-6 rounded-2xl"></div>
								</div>

								<div v-if="isLoaded">
									<table class="table-auto w-full divide-y divide-slate-200">
										<thead class="
		                        text-xs
		                        font-semibold
		                        uppercase
		                        text-slate-500
		                        bg-slate-50
		                        border-t border-b border-slate-200
		                      ">
											<tr>
												<th class="
		                            px-2
		                            first:pl-5
		                            last:pr-5
		                            py-3
		                            whitespace-nowrap
		                          ">
													<div class="flex items-center">
														<div class="">Name</div>
													</div>
												</th>
												<th class="
		                            px-2
		                            first:pl-5
		                            last:pr-5
		                            py-3
		                            whitespace-nowrap
		                          ">
													<div class="text-center">Status</div>
												</th>
												<th class="
		                            px-2
		                            first:pl-5
		                            last:pr-5
		                            py-3
		                            whitespace-nowrap
		                          ">
													<div class="text-center">Total Views</div>
												</th>
												<th class="
		                            px-2
		                            first:pl-5
		                            last:pr-5
		                            py-3
		                            whitespace-nowrap
		                          ">
													<div class="text-center">Connections</div>
												</th>
												<th class="
		                            px-2
		                            first:pl-5
		                            last:pr-5
		                            py-3
		                            whitespace-nowrap
		                          ">
													<div class="text-center">Conversions</div>
												</th>
												<!-- <th
                          class="
                            px-2
                            first:pl-5
                            last:pr-5
                            py-3
                            whitespace-nowrap
                          "
                        >
                          <div class="text-center">Revenue</div>
                        </th> -->
											</tr>
										</thead>
										<tbody class="text-sm">

											<div class="bg-white p-2 sm:p-4 sm:h-64 rounded-2xl shadow-lg flex flex-col sm:flex-row gap-5 select-none"
												v-if="marketingActivities.length <= 0">
												<div class="flex flex-col flex-1 gap-5 sm:p-2">
													<div class="flex flex-1 flex-col gap-3">
														<div class="bg-gray-200 w-full animate-pulse h-5 rounded-2xl"></div>
														<div class="bg-gray-200 w-full animate-pulse h-5 rounded-2xl"></div>
														<div class="bg-gray-200 w-full animate-pulse h-5 rounded-2xl"></div>
														<div class="bg-gray-200 w-full animate-pulse h-5 rounded-2xl"></div>
														<div class="bg-gray-200 w-full animate-pulse h-5 rounded-2xl"></div>
													</div>
												</div>
											</div>

											<AttributionTable v-for="attrib in marketingActivities" :key="attrib.id"
												:attribution="attrib" :isLoadingAdditionalData="isLoadingAdditionalData"
												v-else-if="marketingActivities.length > 0" />
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>


					<!-- Empty State -->
					<div class="max-w-4xl m-auto mt-16" v-if="(attributionCTA && initialLoad)">
						<div class="text-center px-4">
							<div class="
		                    inline-flex
		                    items-center
		                    justify-center
		                    rounded-lg
		                    bg-gradient-to-t
		                    from-ralcloud-300
		                    to-ralcloud-700
		                    mb-6
		                  " style="width: 402px; height: 268px;">
								<svg width="402" height="268" viewBox="0 0 900 600" fill="none"
									xmlns="http://www.w3.org/2000/svg">
									<path fill="transparent" d="M0 0h900v600H0z" />
									<path clip-rule="evenodd"
										d="M588.179 387.428h42.605c11.759 0 21.303 9.543 21.303 21.302v42.605c0 11.759-9.544 21.302-21.303 21.302h-42.605c-11.759 0-21.302-9.543-21.302-21.302V408.73c0-11.759 9.543-21.302 21.302-21.302zM289.945 89.194h42.605c11.759 0 21.302 9.543 21.302 21.302v42.605c0 11.759-9.543 21.302-21.302 21.302h-42.605c-11.759 0-21.302-9.543-21.302-21.302v-42.605c0-11.759 9.543-21.302 21.302-21.302z"
										stroke="#E1E4E5" stroke-width="8" stroke-linecap="round" stroke-linejoin="round" />
									<path clip-rule="evenodd"
										d="M439.062 238.311h42.605c11.759 0 21.303 9.543 21.303 21.302v42.605c0 11.759-9.544 21.303-21.303 21.303h-42.605c-11.759 0-21.302-9.544-21.302-21.303v-42.605c0-11.759 9.543-21.302 21.302-21.302z"
										stroke="#E1E4E5" stroke-width="12" stroke-linecap="round" stroke-linejoin="round" />
									<path
										d="M439.062 131.798h170.42c23.539 0 42.605 19.066 42.605 42.605v63.908c0 23.539-19.066 42.604-42.605 42.604h-42.605m-85.21 149.118H311.248c-23.54 0-42.605-19.066-42.605-42.605V323.52c0-23.539 19.065-42.604 42.605-42.604h42.604"
										stroke="#E1E4E5" stroke-width="8" stroke-linecap="round" stroke-linejoin="round" />
									<circle cx="467.383" cy="280.993" r="78.733" fill="#666AF6" stroke="#666AF6"
										stroke-width="11.812" stroke-linecap="round" stroke-linejoin="round" />
									<path
										d="m475.715 251.834-16.663 58.321m-18.756-20.832-16.656-16.656 16.656-16.666m54.159 49.987 16.665-16.665-16.665-16.656"
										stroke="#fff" stroke-width="9.45" stroke-linecap="round" stroke-linejoin="round" />
									<rect x="692.86" y="131.702" width="158.467" height="7.35" rx="3.675" fill="#E1E4E5" />
									<rect x="669.726" y="299.423" width="158.467" height="7.35" rx="3.675" fill="#E1E4E5" />
									<rect x="97.163" y="206.887" width="158.467" height="7.35" rx="3.675" fill="#E1E4E5" />
									<rect x="89.066" y="445.166" width="158.467" height="7.35" rx="3.675" fill="#E1E4E5" />
									<rect x="692.86" y="169.873" width="158.467" height="7.35" rx="3.675" fill="#E1E4E5" />
									<rect x="669.726" y="337.593" width="158.467" height="7.35" rx="3.675" fill="#E1E4E5" />
									<rect x="97.163" y="245.058" width="158.467" height="7.35" rx="3.675" fill="#E1E4E5" />
									<rect x="89.066" y="483.337" width="158.467" height="7.35" rx="3.675" fill="#E1E4E5" />
									<path
										d="M764.964 231.931c15.739-3.729 32.386 9.619 32.386 9.619s-8.884 19.386-24.628 23.103c-15.739 3.729-32.381-9.607-32.381-9.607s8.884-19.386 24.623-23.115z"
										fill="url(#a)" />
									<circle cx="207.865" cy="321.348" r="15.854" fill="#666AF6" />
									<circle r="11.903" transform="matrix(1 0 0 -1 840.096 236.43)" fill="#666AF6" />
									<circle r="11.903" transform="matrix(1 0 0 -1 584.467 186.693)" fill="#666AF6" />
									<circle r="22.725" transform="matrix(1 0 0 -1 326.936 505.194)" fill="#666AF6" />
									<circle r="12.683" transform="matrix(1 0 0 -1 609.537 430.377)" fill="#E1E4E5" />
									<circle r="10.821" transform="matrix(1 0 0 -1 439.955 494.447)" fill="#E1E4E5" />
									<circle r="10.575" transform="scale(-1 1) rotate(-75 -106.451 688.544)"
										fill="#E1E4E5" />
									<circle r="14.068" transform="matrix(1 0 0 -1 729.916 108.021)" fill="#E1E4E5" />
									<ellipse rx="10.821" ry="8.657" transform="matrix(1 0 0 -1 59.403 293.31)"
										fill="#E1E4E5" />
									<circle r="15.545" transform="scale(-1 1) rotate(-75 -45.443 183.402)" fill="#E1E4E5" />
									<path
										d="M129.51 372.474h-.283c-1.677-23.752-19.341-24.117-19.341-24.117s19.479-.38 19.479-27.825c0 27.445 19.478 27.825 19.478 27.825s-17.657.365-19.333 24.117zm14.285-231.006h-.205c-1.217-17.696-14.04-17.968-14.04-17.968s14.14-.284 14.14-20.73c0 20.446 14.139 20.73 14.139 20.73s-12.817.272-14.034 17.968z"
										fill="#E1E4E5" />
									<defs>
										<linearGradient id="a" x1="824.233" y1="222.66" x2="686.453" y2="287.752"
											gradientUnits="userSpaceOnUse">
											<stop stop-color="#fff" />
											<stop offset="1" stop-color="#EEE" />
										</linearGradient>
									</defs>
								</svg>
							</div>
							<h2 class="text-2xl text-slate-800 font-bold mb-2">
								Setup Raleon Marketing Snippet
							</h2>
							<div class="mb-6 text-left">
								<h3 class="text-lg font-medium">Step 1: Install snippet</h3>
								<p>Drop the following code snippet into the head tag of your website and dapp. <br />Want more assistance? You can <a href="https://docs.raleon.io/docs/raleon-snippet-installation" class="text-ralprimary-main">watch a video on how to install the snippet</a>.</p>
								<pre class="bg-white rounded-md mt-4 whitespace-pre-wrap overflow-x-auto p-4">
<code ref="codeBlock">
&lt;script&gt;
{{snippet_code_sample}}
&lt;/script&gt;
</code>
								</pre>

								<h3 class="text-lg font-medium mt-8">Step 2: Hook into wallet connections</h3>
								<p>With the snippet installed, just add this one line anywhere a wallet connection is made, replacing "CONNECTED WALLET ADDRESS" with the actual wallet adderss variable. That's it! Wait about 5 minutes for your data to start flowing.</p>
								<pre class="bg-white rounded-md mt-4 whitespace-pre-wrap overflow-x-auto p-4">
<code>
&lt;script&gt;
{{snippet_api_call}}
&lt;/script&gt;
</code>
								</pre>
							</div>
						</div>
					</div>
					<!-- End -->
				</div>
			</main>
		</div>
	</div>

					<!-- Start Attrib Link Builder -->
					<ModalBasic id="link-builder-modal" :modalOpen="linkBuilderModelOpen"
						@close-modal="linkBuilderModelOpen = false" title="Generate Web3 Attribution Link">
						<!-- Modal content -->
						<div class="px-5 py-4">
							<div class="text-xs">
								<a href="https://docs.raleon.io/docs/best-practices-for-using-web3-attribution" class="text-ralprimary-main" target="_blank">🔗 Learn best practices of creating attribution links</a>
							</div>
							<div class="text-sm mb-5 mt-4">
								<form>
									<div class="space-y-4">
										<div>
											<label class="block text-sm font-medium mb-1" for="name">What URL are you sending users to? <span class="text-red-600">*</span></label>
											<input id="wallet-address" class="form-input w-full" type="text"
												placeholder="https://www..."
												v-model="linkURL" />
										</div>
										<div>
											<label class="block text-sm font-medium mb-1"
												for="large">What's the name of your marketing activity? <span class="text-red-600">*</span></label>
											<input id="large" class="form-input w-full px-4 py-3" type="text"
												placeholder="Twitter Space #2, Launch"
												v-model="linkCampaign" />
										</div>
										<div>
											<label class="block text-sm font-medium mb-1"
												for="large">What's the source of your marketing activity? <span class="text-red-600">*</span></label>
											<input id="large" class="form-input w-full px-4 py-3" type="text"
												placeholder="Twitter, Discord, Email"
												v-model="linkSource" />
										</div>
										<div>
											<label class="block text-sm font-medium mb-1"
												for="large">What type of content was this?</label>
											<input id="large" class="form-input w-full px-4 py-3" type="text"
												placeholder="Twitter thread, Discord post, etc."
												v-model="linkContent" />
										</div>
										<div class="break-words">
											<div class="bg-gray-200 rounded-lg p-4 mt-2 text-sm">
												{{linkBuilt}}
											</div>
										</div>
									</div>
								</form>
							</div>
						</div>
						<!-- Modal footer -->
						<div class="px-5 py-4 border-t border-slate-200">
							<div class="flex flex-wrap justify-end space-x-2">
								<button class="
									btn-sm
									border-slate-200
									hover:border-slate-300
									text-slate-600
								" @click.stop="() => { linkBuilderModelOpen = false }">
									Cancel
								</button>
								<button class="btn-sm bg-indigo-500 hover:bg-indigo-600 text-white"
									@click="copyURL()">
									Copy Attribution Link
								</button>
							</div>
						</div>
					</ModalBasic>
					<!-- End -->
</template>

<script>
import { ref } from 'vue';
import Notification from '../../components/Notification.vue';
import PaginationNumeric from '../../components/PaginationNumeric.vue';
import SearchForm from '../../components/SearchForm.vue';
import AttributionTable from '../../partials/campaigns/AttributionTable.vue';
import Header from '../../partials/Header.vue';
import Sidebar from '../../partials/Sidebar.vue';
import { MarketingService } from '../../services/marketing.js';
import { getOrgById, setOrgAttribution } from '../../services/organization';
import DropdownFull from '../../components/DropdownFull.vue';
import { getAvailableProjects } from '../../services/project';
import ModalBasic from '../../components/ModalBasic.vue';

export default {
	name: 'CampaignOverview',
	components: {
		Sidebar,
		Header,
		SearchForm,
		PaginationNumeric,
		AttributionTable,
		Notification,
		DropdownFull,
		ModalBasic
	},
	setup() {
		const sidebarOpen = ref(false);
		const notificationInfoOpen = ref(true);
		const orgId = localStorage.getItem('userOrgId');
		const linkBuilderModelOpen = ref(false);

		const snippet_code_sample = `
		var script = document.createElement( "script" )
		script.onload = function() {
			let paramData: any = {
			orgId: &lt;your_org_idd&gt;
			applicationId: '', //Utilize this to manage event scope (examples v1, v2, or dev, prod, etc)
			pageLocation: 'landing-page', //Default event sent when someone visits the page
			enableActionPrompts: true/false, //Will enable/disable action prompts
			enableQuests: true/false, //Will enable/disable quests

			//Optional, used to inject UI into a sepcific dom element
			//questDomId: chatPopupDivRefs.chatPopupDiv1.current?.id,
			};
			setupRaleonParametersV2(paramData);
		};

		script.src = 'https://dqpqjbq51w8fz.cloudfront.net/raleon_snippet.min.js';
		document.getElementsByTagName( "head" )[0].appendChild( script );
                      `;

    const snippet_api_call = `	raleon.walletConnected(<CONNECTED_WALLET_ADDRESS>)`;

		return {
			sidebarOpen,
			notificationInfoOpen,
			snippet_code_sample,
			snippet_api_call,
			orgId,
			linkBuilderModelOpen
		};
	},
	computed: {
		isLoaded() {
			return this.marketingActivities.length > 0;
		},
		campaignCount() {
			return this.marketingActivities.length || 0;
		},
		linkBuilt() {
			if(this.linkURL != null) {
				let newURL = this.linkURL;

				if(this.linkSource != null)
					newURL += "?utm_source=" + this.cleanupUTM(this.linkSource);

				if(this.linkCampaign != null)
					newURL += "&utm_campaign=" + this.cleanupUTM(this.linkCampaign);

				if(this.linkContent != null)
					newURL += "&utm_content=" + this.cleanupUTM(this.linkContent);

				return newURL;
			}
			else
				return "Waiting for URL...";
		}
	},
	data() {
		return {
			marketingActivities: [],
			allCampaignServiceInstances: [],
			orgId: 1,
			attributionDataLoad: false,
			initialLoad: false,
			attributionCTA: true,
			projectList: [],
			isLoadingAdditionalData: true,
			selectedProjectId: localStorage.campaignOverviewSelectedProjectId,
			linkURL: null,
			linkSource: null,
			linkCampaign: null,
			linkContent: null
		};
	},
	methods: {
		async copyURL() {
			try {
				await navigator.clipboard.writeText(this.linkBuilt);
				this.linkBuilderModelOpen = false;
			} catch($e) {
				console.log('Cannot copy');
			}
		},
		async setAttributionFeature() {
			if (!this.attributionDataLoad || this.attributionCTA) {
				let setResult = await setOrgAttribution(
					this.orgId,
					true
				);

				this.attributionCTA = false;
				this.attributionDataLoad = true;
			}
		},
		cleanupUTM(utmString) {
			if(utmString != null) {
				utmString = utmString.toLowerCase();
				utmString = utmString.replace(/\s+/g, '')
				utmString = utmString.replace(/[^\w\s]/gi, '');
			}
			return utmString;
		},
		getProjects: function () {
			return this.projectList;
		},
		projectChanged: function (value) {
			localStorage.campaignOverviewSelectedProjectId = this.selectedProjectId = value;

			this.loadMarketingCampaign();
		},
		loadMarketingCampaign: function () {
			const selectedProjectUuid = this.projectList.find(x => x.id == this.selectedProjectId).uuid;
			let mainMarketingCampaign = new MarketingService(
				selectedProjectUuid,
				current_data => {
					//Current Data is an array of captured data thus far
					this.marketingActivities = [];
					for (let i = 0; i < current_data.length; i++) {
						this.marketingActivities.push({
							id: current_data[i].id,
							name: current_data[i].name,
							status: current_data[i].status,
							connections: current_data[i].connections,
							conversion: current_data[i].conversion,
							totalViews: current_data[i].totalViews,
						});
					}

					this.marketingActivities.sort((a, b) => {
						if (!a.name || !b.name) return 0;
						const aName = a.name.toLowerCase();
						const bName = b.name.toLowerCase();
						return aName > bName ? 1 : aName < bName ? -1 : 0;
					});
					//console.log('New Data: ' + JSON.stringify(this.marketingActivities));
				},
				undefined,
				() => this.isLoadingAdditionalData = false
			);
			mainMarketingCampaign.getCampaigns(); //This will kick off the request if none are in progress.
		}
	},
	async mounted() {
		amplitude.getInstance().logEvent('CAMPAIGN_OVERVIEW_VIEWED');

		this.orgId = localStorage.getItem('userOrgId');

		const [orgResult, allProjects] = await Promise.all([
			getOrgById(
				`${localStorage.getItem('userOrgId')}`,
			),
			getAvailableProjects(true, true)
		]);
		this.projectList = allProjects.filter(x => x.dataConnections && x.dataConnections.length);

		this.isLoadingAdditionalData = true;

		if (!this.selectedProjectId) {
			this.selectedProjectId = this.projectList[0].id;
			localStorage.campaignOverviewSelectedProjectId = this.selectedProjectId;
		}

		if (orgResult.attribution)
			this.attributionCTA = false;
		else
			this.attributionCTA = true;

		this.initialLoad = true;
		this.loadMarketingCampaign();
	},
	watch: {
		marketingActivities(oldActivities, newActivities) {
			if (newActivities.length > 0) {
				this.setAttributionFeature();
			}
		}
	}
};
</script>
