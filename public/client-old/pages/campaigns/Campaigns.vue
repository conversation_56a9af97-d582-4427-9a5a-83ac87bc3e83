<template>

	<div class="flex h-screen overflow-hidden bg-white">
		<Sidebar :sidebarOpen="sidebarOpen" @close-sidebar="sidebarOpen = false" />


		<div class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
			<div class="flex flex-row snippet-banner bg-ralprimary-main justify-center items-center" v-if="isSnippetWarningVisible">
				<h1 class="mr-4">Snippet May Not Be Installed</h1>
				<p class="mr-4">Snippet must be installed for Action Prompts to be visible on your site</p>
				<a class="text-white" href="/snippet">Verify Now</a>
			</div>
			<main>
				<div class="px-6 sm:px-6 lg:px-12 py-8 w-full max-w-9xl mx-auto">

				<!-- Page header -->
					<div class="flex sm:justify-between sm:items-center mb-4">
						<h1 class="text-2xl md:text-3xl text-slate-800 font-bold">My Action Prompts</h1>
						<div class="flex">
							<button
								class="bg-gradient-button-primary text-white font-bold text-xs py-2 px-4 rounded-button"
								data-cy="create-new-campaign"
								@click.prevent="this.$router.push('/campaign-builder')">
								+ Create New
							</button>
							<button
								class="text-white bg-ralbutton-primary-light-deactivated font-bold text-xs py-2 px-4 rounded-button ml-2 cursor-not-allowed"
								disabled>
								Start with a Template
							</button>
						</div>
					</div>
					<div class="border-b border-ralwhite-line"></div>

				</div>

				<!-- Empty State -->
				<div class="max-w-2xl m-auto mt-16" v-if="this.rowData.length == 0 && this.isLoading == false">
					<div class="text-center px-4">
						<div class="
							inline-flex
							items-center
							justify-center
							w-16
							h-16
							rounded-full
							bg-gradient-to-t
							from-ralcloud-300
							to-ralcloud-700
							mb-4
						">
						<img src="../../images/sidebar-icons/zap.svg">
						</div>
						<h2 class="text-2xl text-slate-800 font-bold mb-2">
							You haven't created any action prompts yet!
						</h2>
						<div class="mb-6">
							Need some help?
							<a href="https://docs.raleon.io/docs/get-started-with-raleon-engage" class="text-ralpurple-700 font-bold">Learn about campaigns in Raleon.</a>
						</div>
						<button class="
							btn-xs
							bg-ralpurple-500
							hover:bg-ralpurple-600
							text-white
							"
							aria-controls="add-report-modal" data-cy="create-new-campaign"
							@click.prevent="this.$router.push('/campaign-builder')">
							<span class="hidden xs:block">+ Create New Campaign</span>
						</button>
					</div>
				</div>
				<!-- End -->

				<RaleonTable
					class="px-6 sm:px-6 lg:px-12 w-full max-w-9xl mx-auto"
					:column-headers="this.columnHeaders"
					:row-data="this.rowData"
					:is-loading="this.isLoading"
					@deleteRowClicked="showDeleteModal"
					v-else-if="this.rowData.length > 0 && this.isLoading == false">
				</RaleonTable>
			</main>

			<NewCampaignModal
				v-if="showModal"
				:audiences="audiences"
				:show="showModal"
				@close="showModal = false"
				@create="createCampaign"
			/>

			<DeleteModal
				v-if="showDeleteModalProp"
				:show="showDeleteModalProp"
				:header="deleteModalData.header"
				:confirm-text-main="deleteModalData.confirmTextMain"
				:confirm-text-secondary="deleteModalData.confirmTextSecondary"
				:delete-button-text="deleteModalData.deleteButtonText"
				:is-deleting="deleteModalData.isDeleting"
				@close-modal="showDeleteModalProp = false"
				@delete-clicked="deleteCampaign"
				@cancel-clicked="hideAndResetDeleteModal"
			/>
		</div>

	</div>

</template>

<script>
	import { ref } from 'vue'
	import Sidebar from "../../partials/Sidebar.vue"
	import RaleonTable from '../component/RaleonTable.vue'
	import DeleteModal from '../component/DeleteModal.vue'
	import NewCampaignModal from './NewCampaignModal.vue'
	import * as Utils from '../../utils/Utils';
	import { CAMPAIGN_TYPES } from '../campaign-builder/building-blocks/campaign-types'

	const URL_DOMAIN = Utils.URL_DOMAIN;

	export default {
		name: 'Campaigns',
		props: [],
		emits: [],
		components: {
			Sidebar,
			RaleonTable,
			NewCampaignModal,
			DeleteModal,
		},
		setup() {
			const sidebarOpen = ref(false);
			return {
				sidebarOpen,
			}
		},
		data() {
			return {
				isLoading: false,
				showModal: false,
				showDeleteModalProp: false,
				campaignToDelete: {},
				deleteModalData: {
					header: '',
					confirmTextMain: '',
					confirmTextSecondary: '',
					deleteButtonText: '',
					isDeleting: false,
				},
				audiences: [],
				columnHeaders: [
					{
						name: 'Name',
						tooltip: undefined,
						value: 'campaignName',
					},
					{
						name: 'Type',
						tooltip: undefined,
						value: 'campaignType',
					},
					{
						name: 'Unique Views',
						tooltip: 'Number of unique identities that viewed the campaign',
						value: 'uniqueViews',
					},
					{
						name: 'Engagement',
						tooltip: 'Percentage calculated as the number of engagement events divided by the number of unique views',
						value: 'engagement',
					},
					{
						name: 'Status',
						tooltip: undefined,
						value: 'status',
					},
				],
				rowData: []
			}
		},
		computed: {
			isSnippetWarningVisible() {
				return  localStorage.getItem('snippetVerified') != 'true' &&  this.rowData?.length;
			}
		},
		methods: {
			async getCampaigns() {
				const response = await fetch(`${URL_DOMAIN}/campaigns`, {
					method: 'GET',
					credentials: 'omit', // include, *same-origin, omit
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					},
				});

				const campaigns = await response.json();
				return campaigns?.filter(campaign => campaign.type === CAMPAIGN_TYPES.ActionPrompt.NAME) || [];
			},
			async getCampaignsMetrics() {
				const response = await fetch(`${URL_DOMAIN}/campaigns/metrics`, {
					method: 'GET',
					credentials: 'omit', // include, *same-origin, omit
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					},
				});

				return await response.json();
			},
			async generateRowData() {
				const campaigns = await this.getCampaigns();

				this.rowData = campaigns.map(campaign => {
					return {
						id: campaign.id,
						campaignName: campaign.name,
						campaignType: CAMPAIGN_TYPES[campaign.type]?.FRIENDLY_NAME,
						status: new CellData(campaign.status, this.getStatusTextColor(campaign.status)),
						href: `/loyalty/campaign/${campaign.id}`,
						target: '_self',
						hrefStyle: 'ml-auto',
						showDelete: campaign.status == 'Draft',
						uniqueViews: '-',
						engagement: '-',
					}
				});

				this.updateMetrics();
			},
			async updateMetrics() {
				const metricContainers = await this.getCampaignsMetrics();
				this.rowData = this.rowData.map(row => {
					const metrics = Array.isArray(metricContainers) ? metricContainers.find(x => x.campaignId === row.id)?.metrics : [];
					const engagementPercentMetric = row.campaignType == CAMPAIGN_TYPES.NativeQuest.NAME ? 'engagement_percentage' : 'conversion_rate';
					const matchingMetric = metrics?.find(x => x.metricName === engagementPercentMetric);
					let engagement = 'N/A';
					if (matchingMetric) {
						const value = matchingMetric.metricValue;
						if (value == 'N/A') return;
						engagement = Number(value).toFixed(2) + ' %';
					}
					return {
						...row,
						uniqueViews: metrics?.find(x => x.metricName === 'unique_views')?.metricValue || 0,
						engagement
					}
				});
			},
			async createCampaign() {
				await this.generateRowData();
				this.showModal = false;
			},
			showDeleteModal(row) {
				this.campaignToDelete = row;
				this.deleteModalData = {
					header: 'Remove Campaign',
					confirmTextMain: `Are you sure you want to remove Campaign "${row.campaignName}"?`,
					confirmTextSecondary: 'This action cannot be undone.',
					deleteButtonText: 'Remove Campaign',
					isDeleting: false,
				};
				this.showDeleteModalProp = true;
			},
			hideAndResetDeleteModal() {
				this.campaignToDelete = {};
				this.deleteModalData = {
					header: '',
					confirmTextMain: '',
					confirmTextSecondary: '',
					deleteButtonText: '',
					isDeleting: false,
				};
				this.showDeleteModalProp = false;
			},
			async deleteCampaign() {
				this.deleteModalData.isDeleting = true;
				await fetch(`${URL_DOMAIN}/campaigns/${this.campaignToDelete.id}`, {
					method: 'DELETE',
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					},
				});
				this.hideAndResetDeleteModal();
				this.generateRowData();
			},
			async getAudiences() {
				const response = await fetch(`${URL_DOMAIN}/segment-list`, {
					method: 'GET',
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					},
				});
				const audiences = await response.json();

				if (audiences.length > 0) {
					this.audiences = audiences
						.sort((a, b) => a.name.localeCompare(b.name))
				}
			},
			getStatusTextColor(status) {
				const additionalStyles = 'rounded-2xl px-2.5 py-1.5 min-w-full text-center'
				switch (status) {
					case 'Draft':
						return `text-ralblack-primary bg-ralgray-light ${additionalStyles}`
					case 'Running':
						return `text-ralprimary-main bg-raltable-ring-secondary ${additionalStyles}`
					case 'Completed':
						return `text-ralsuccess-dark bg-ralsuccess-light ${additionalStyles}`
					default:
						return `text-ralinfo-dark bg-ralinfo-light ${additionalStyles}`
				}
			}
		},
		async mounted() {
			this.isLoading = true;
			await Promise.all([
				this.generateRowData(),
				this.getAudiences(),
			]);
			this.isLoading = false;
		},
	}

	class CellData {
		constructor(value, color, icon) {
			this.value = value;
			this.color = color;
			this.icon = icon;
		}
	}
</script>
