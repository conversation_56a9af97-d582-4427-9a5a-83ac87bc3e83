<template>
	<ModalBlank
		id="newCampaignModal"
		:modal-open="show"
		@close-modal="$emit('close')">
		<div class="p-5 space-x-4">
			<div>
				<div class="mb-2 text-lg font-semibold text-slate-800">
					{{ this.isEditMode ? 'Edit Campaign' : 'New Campaign' }}
				</div>
				<!-- Modal content -->
				<div class="mt-5 mb-10 space-y-2">
					<div class="flex flex-col space-y-3">
						<label for="nameInput" class="text-sm font-bold">Name</label>
						<input
							id="nameInput"
							class="form-input w-full"
							type="text"
							data-cy="name-input"
							v-model="v$.name.$model"
							placeholder="Enter campaign name"/>
						<div class="input-errors" v-for="error of v$.name.$errors" :key="error.$uid">
							<div class="error-msg text-center text-sm font-small text-rose-400 pt-2">{{ error.$message }}</div>
						</div>

						<label for="startDateInput" class="text-sm font-bold">Start Date</label>
						<input
							id="startDateInput"
							class="form-input w-full"
							type="date"
							v-model="v$.startDate.$model"
							data-cy="start-date-input"
							placeholder="Enter start date"/>
						<div class="input-errors" v-for="error of v$.startDate.$errors" :key="error.$uid">
							<div class="error-msg text-center text-sm font-small text-rose-400 pt-2">{{ error.$message }}</div>
						</div>

						<label for="endDateInput" class="text-sm font-bold">End Date</label>
						<input
							id="endDateInput"
							class="form-input w-full"
							type="date"
							v-model="v$.endDate.$model"
							data-cy="end-date-input"
							placeholder="Enter end date"/>
						<div class="input-errors" v-for="error of v$.endDate.$errors" :key="error.$uid">
							<div class="error-msg text-center text-sm font-small text-rose-400 pt-2">{{ error.$message }}</div>
						</div>

						<label for="audiences" class="text-sm font-bold">Target Audience</label>
						<select
							id="audiences"
							class="form-select w-full"
							data-cy="audience-select"
							v-model="v$.selectedAudience.$model">
							<option disabled value="">Please Select an Audience</option>
							<option
								v-for="audience of this.audiences"
								:key="audience.id"
								:value="audience">
								{{ audience.name }}
							</option>
						</select>

						<label for="contentHeader" class="text-sm font-bold">Content Header</label>
						<input
							id="contentHeader"
							class="form-input w-full"
							type="text"
							data-cy="content-header-input"
							v-model="contentHeader"
							placeholder="Enter a header for the action prompt"/>

						<label for="contentHeaderImg" class="text-sm font-bold">Content Header Image Url</label>
						<input
							id="contentHeaderImg"
							class="form-input w-full"
							type="text"
							data-cy="content-header-img-input"
							v-model="contentHeaderImgUrl"
							placeholder="Enter an image url for the action prompt"/>

						<label for="contentMessage" class="text-sm font-bold">Content Message</label>
						<input
							id="contentMessage"
							class="form-input w-full"
							type="text"
							data-cy="content-message-input"
							v-model="contentMessage"
							placeholder="Enter a message for the action prompt"/>

						<label for="contentCloseMessage" class="text-sm font-bold">Content Close Message</label>
						<input
							id="contentCloseMessage"
							class="form-input w-full"
							type="text"
							data-cy="content-close-message-input"
							v-model="contentCloseMessage"
							placeholder="Enter a close message for the action prompt"/>

						<label for="contentButtonText" class="text-sm font-bold">Content Button Text</label>
						<input
							id="contentButtonText"
							class="form-input w-full"
							type="text"
							data-cy="content-button-text-input"
							v-model="contentButtonText"
							placeholder="Enter text to appear on the action prompt button"/>

						<label for="contentButtonUrl" class="text-sm font-bold">Content Button Url</label>
						<input
							id="contentButtonUrl"
							class="form-input w-full"
							type="text"
							data-cy="content-button-url-input"
							v-model="contentButtonUrl"
							placeholder="Enter a url to navigate the user on button click"/>
					</div>
				</div>

				<!-- Modal footer -->
				<div class="flex flex-wrap justify-end space-x-2">
					<button
						class="btn-sm border-slate-200 hover:border-slate-300 disabled:border-slate-300 disabled:bg-slate-100 disabled:text-slate-400 disabled:cursor-not-allowed"
						@click.stop="cancel">
						Cancel
					</button>
					<button
						class="btn bg-ralpurple-500 hover:bg-ralpurple-600 text-white disabled:border-slate-300 disabled:bg-slate-100 disabled:text-slate-400 disabled:cursor-not-allowed"
						:disabled="createDisabled"
						data-cy="create-campaign-button"
						@click.stop="createOrUpdateCampaign">
						<svg v-if="createInProgress" class="animate-spin w-4 h-4 fill-current shrink-0 mr-2"
							viewBox="0 0 16 16">
							<path
								d="M8 16a7.928 7.928 0 01-3.428-.77l.857-1.807A6.006 6.006 0 0014 8c0-3.309-2.691-6-6-6a6.006 6.006 0 00-5.422 8.572l-1.806.859A7.929 7.929 0 010 8c0-4.411 3.589-8 8-8s8 3.589 8 8-3.589 8-8 8z" />
						</svg>
						{{ this.campaign ? 'Update Campaign' : 'Create Campaign' }}
					</button>
				</div>
			</div>
		</div>
	</ModalBlank>
</template>

<script>
import ModalBlank from '../../components/ModalBlank.vue'
import { useVuelidate } from '@vuelidate/core'
import { required, helpers } from '@vuelidate/validators'
import * as Utils from '../../utils/Utils';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	name: 'NewCampaignModal',
	components: {
	  ModalBlank,
	},
	props: ['show', 'audiences', 'campaign'],
	emits: ['close', 'create'],
	setup() {
		return {
			v$: useVuelidate()
		}
	},
	data() {
		return {
			name: '',
			startDate: this.defaultStartDate(),
			endDate: this.defaultEndDate(),
			selectedAudience: {},
			contentHeader: '',
			contentMessage: '',
			contentHeaderImgUrl: '',
			contentCloseMessage: '',
			contentButtonText: '',
			contentButtonUrl: '',
			createInProgress: false,
		}
	},
	computed: {
		createDisabled() {
			return this.v$.$invalid || this.createInProgress;
		},
		isEditMode() {
			return !!(this.campaign?.id);
		},
	},
	methods: {
		defaultStartDate() {
			let tzOffset = (new Date()).getTimezoneOffset() * 60000;
			return new Date(Date.now() - tzOffset).toISOString().substring(0, 10);
		},
		defaultEndDate() {
			const today = new Date();
			const nextWeek = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 7);
			return nextWeek.toISOString().substring(0, 10);
		},
		async createOrUpdateCampaign() {
			if (this.isEditMode) {
				await this.updateCampaign();
			} else {
				await this.createCampaign();
			}
		},
		async createCampaign() {
			this.createInProgress = true;
			const response = await fetch(`${URL_DOMAIN}/campaigns/${this.selectedAudience.id}`, {
				method: 'POST',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify({
					name: this.name,
					startDate: new Date(this.startDate).toISOString(),
					endDate: new Date(this.endDate).toISOString(),
					priority: 1,
					quests: [{
						name: this.name,
						content: {
							header: this.contentHeader,
							message: this.contentMessage,
							headerImageUrl: this.contentHeaderImgUrl,
							closeMessage: this.contentCloseMessage,
							buttonText: this.contentButtonText,
							buttonUrl: this.contentButtonUrl,
						}
					}]
				}),
			});
			const data = await response.json();

			if (data.error) {
				console.log(data.error);
			} else {
				this.$emit('create');
				this.createInProgress = false;
			}
		},
		async updateCampaign() {
			this.createInProgress = true;
			try {
				await fetch(`${URL_DOMAIN}/campaigns/${this.campaign.id}`, {
					method: 'PATCH',
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					},
					body: JSON.stringify({
						name: this.name,
						startDate: new Date(this.startDate).toISOString(),
						endDate: new Date(this.endDate).toISOString(),
						priority: 1,
						segments: [{ id: this.selectedAudience.id }],
						quests: [{
							id: this.campaign.quests[0].id || undefined,
							campaignId: this.campaign.id || undefined,
							name: this.name,
							content: {
								header: this.contentHeader,
								message: this.contentMessage,
								headerImageUrl: this.contentHeaderImgUrl,
								closeMessage: this.contentCloseMessage,
								buttonText: this.contentButtonText,
								buttonUrl: this.contentButtonUrl,
							}
						}]
					}),
				});
			} catch (error) {
				console.log(error);
			}

			this.$emit('create');
			this.createInProgress = false;
		},
		cancel() {
			this.$emit('close');
		},

	},
	async mounted() {
		if (this.campaign) {
			this.name = this.campaign.name;
			this.startDate = this.campaign.startDate.split('T')[0];
			this.endDate = this.campaign.endDate.split('T')[0];

			const content = this.campaign.quests[0].content || {};
			if (content !== {}) {
				this.contentHeader = content.header;
				this.contentHeaderImgUrl = content.headerImageUrl;
				this.contentMessage = content.message;
				this.contentCloseMessage = content.closeMessage;
				this.contentButtonText = content.buttonText;
				this.contentButtonUrl = content.buttonUrl;
			}
		}

		if (this.audiences && this.audiences.length > 0) {
			this.selectedAudience = this.campaign ?
				this.audiences.find(aud => aud.id === this.campaign.segments[0].id) :
				this.audiences[0];
		}
	},
	validations() {
		return {
			name: {
				required: helpers.withMessage('Name is required', required),
      		},
			startDate: {
				required: helpers.withMessage('Start date is required', required),
				minValue: helpers.withMessage(
					'Start date must be in MM/DD/YYYY format, \n less than the end date, \n and greater than or equal to today',
					value => {
						if (!this.isEditMode) {
							return value >= this.defaultStartDate() && value <= this.endDate
						}
						return true;
					}
				)
			},
			endDate: {
				required: helpers.withMessage('End date is required', required),
				minValue: helpers.withMessage(
					'End date must be in MM/DD/YYYY format, \n greater than the start date, \n and greater than or equal to today',
					value => {
						if (!this.isEditMode) {
							return value >= this.defaultStartDate();
						}
						return true;
					}
				),
			},
			selectedAudience: {
				required: helpers.withMessage('Target Audience is required', required),
			}
		}
	},
}
</script>
