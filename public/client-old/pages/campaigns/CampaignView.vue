<template>
	<div class="flex h-screen overflow-hidden">
		<!-- Sidebar -->
		<Sidebar :sidebarOpen="sidebarOpen" @close-sidebar="sidebarOpen = false" />

		<!-- Content area -->
		<div class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">

			<main>
				<div v-if="!newConversionSystemActive && readyToShowStencils"
					class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">
					<!-- Page header -->
					<div class="sm:flex sm:justify-between sm:items-center mb-8">
						<!-- Left: Title -->
						<div class="mb-4 border-slate-400 sm:mb-0">
							<h1 class="text-2xl md:text-3xl text-slate-800 font-bold">
								{{ campaignId }}
							</h1>

							<div class="text-left mt-2">
								<ul class="inline-flex flex-wrap text-sm font-medium">
									<li class="flex items-left">
										<a class="text-slate-500 hover:text-ralpurple-500"
											href="/campaigns/Overview">Campaigns</a>
										<svg class="h-4 w-4 fill-current text-slate-400 mt-1 ml-2 mr-2 mb-2"
											viewBox="0 0 16 16">
											<path d="M6.6 13.4L5.2 12l4-4-4-4 1.4-1.4L12 8z" />
										</svg>
									</li>
									<li class="flex items-left text-slate-400">
										{{ campaignId }}
									</li>
								</ul>
							</div>
						</div>

						<!-- Right: Actions  -->
						<div class="
										                grid grid-flow-col
										                sm:auto-cols-max
										                justify-start
										                sm:justify-end
										                gap-2
										              ">
						</div>
					</div>


					<div class="mb-5" v-if="getProjects().length">
						<label class="block text-sm font-medium mb-1" for="role">Which project would you like to
							use?</label>
						<DropdownFull :options="getProjects()" v-on:change="projectChanged" :selectedId="selectedProjectId"
							:clearOnClick="true" />
					</div>

					<div class="grid grid-cols-12 gap-6 mt-8">
						<CampaignNumberChart campaignId="1" :widgetData="widget1" editMenu="false"
							:isLoaded="!isLoadingAdditionalData" />
						<CampaignNumberChart campaignId="1" :widgetData="widget2" editMenu="false"
							:isLoaded="!isLoadingAdditionalData" />
						<CampaignNumberChart campaignId="1" :widgetData="widget3" editMenu="false"
							:isLoaded="!isLoadingAdditionalData" />
						<CampaignNumberChart campaignId="1" :widgetData="widget4" editMenu="false"
							:isLoaded="!isLoadingAdditionalData" />
						<CampaignBarChart campaignId="1" :widgetData="widget5" :chartData="barChartData" editMenu="false"
							:isLoaded="!isLoadingAdditionalData" />
						<CampaignBarChart campaignId="1" :widgetData="widget7" :chartData="barChartData" editMenu="false"
							:isLoaded="!isLoadingAdditionalData" />

						<CampaignLineChart campaignId="1" :widgetData="widget6" editMenu="false"
							:isLoaded="!isLoadingAdditionalData" />

						<CampaignLineChart campaignId="1" :widgetData="widget8" editMenu="false"
							:isLoaded="!isLoadingAdditionalData" />
					</div>

					<div class="
										              bg-white
										              shadow-lg
										              rounded-sm
										              border border-slate-200
										              relative
										              mt-8
										            ">
						<header v-if="!isLoadingAdditionalData" class="px-5 py-4">
							<h2 class="font-semibold text-slate-800">
								Last 10 Wallet Interactions
							</h2>
						</header>
						<div v-if="!isLoadingAdditionalData" class="rounded-sm border border-slate-200">
							<div class="overflow-x-auto">
								<table class="table-auto w-full divide-y divide-slate-200">
									<thead class="
										                      text-xs
										                      font-semibold
										                      uppercase
										                      text-slate-500
										                      bg-slate-50
										                      border-t border-b border-slate-200
										                    ">
										<tr>
											<th class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
												<div class="flex items-center">
													<div class="">Wallet</div>
												</div>
											</th>
											<th class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
												<div class="text-left">Customer Type</div>
											</th>
											<!--
                        <th class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
                          <div class="text-center"># Visits</div>
                        </th>
                        -->
											<th class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
												<div class="text-center"># Wallet Connections</div>
											</th>
											<th class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
												<div class="text-center"># On-Chain Conversions</div>
											</th>
											<th class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
												<div class="text-left">Revenue</div>
											</th>
											<th class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
												<div class="text-left">Best Channel(s)</div>
											</th>
											<th class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
												<div class="text-left">Wallet Persona</div>
											</th>
										</tr>
									</thead>
									<tbody class="text-sm">
										<CampaignViewTable v-for="wallet in campaignActivity" :key="wallet.id"
											:wallet="wallet" />
									</tbody>
								</table>
							</div>
						</div>

						<div v-if="isLoadingAdditionalData" class="relative pl-8 pr-8 pt-4 h-72 overflow-hidden">
							<div class="bg-gray-200 w-full animate-pulse h-4 rounded-2xl mb-6"></div>
							<div class="bg-gray-200 w-full h-full rounded-xl animate-pulse">
								<div class="flex justify-center align-middle animate-pulse pt-8">
									<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="h-36 w-36"
										stroke="#FFFFFF " stroke-width="1">
										<path fill="#FFFFFF" />
										<path fill="#FFFFFF"
											d="M4 8h16V5H4v3zm10 11v-9h-4v9h4zm2 0h4v-9h-4v9zm-8 0v-9H4v9h4zM3 3h18a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1z" />
									</svg>
								</div>
							</div>
						</div>
						<!-- End -->
					</div>

				</div>
				<div v-if="newConversionSystemActive && readyToShowStencils"
					class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">
					<div class="sm:flex sm:justify-between sm:items-center mb-8">
						<!-- Left: Title -->
						<div class="mb-4 border-slate-400 sm:mb-0">
							<h1 class="text-2xl md:text-3xl text-slate-800 font-bold">
								{{ campaignId }}
							</h1>

							<div class="text-left mt-2">
								<ul class="inline-flex flex-wrap text-sm font-medium">
									<li class="flex items-left">
										<a class="text-slate-500 hover:text-ralpurple-500"
											href="/campaigns/Overview">Campaigns</a>
										<svg class="h-4 w-4 fill-current text-slate-400 mt-1 ml-2 mr-2 mb-2"
											viewBox="0 0 16 16">
											<path d="M6.6 13.4L5.2 12l4-4-4-4 1.4-1.4L12 8z" />
										</svg>
									</li>
									<li class="flex items-left text-slate-400">
										{{ campaignId }}
									</li>
								</ul>
							</div>
						</div>
					</div>
					<div class="mt-8">
						<div class="flex justify-between items-center">
							<h2 class="text-xl font-semibold mb-4">Conversion Information</h2>
							<button @click="showInfo = !showInfo">
								{{ showInfo ? 'Collapse' : 'Expand' }}
							</button>
						</div>
						<div v-if="showInfo">
							<div>
								<span class="font-medium">Conversion Type:</span> {{
									conversionEventDefinition.conversiontype }}
							</div>
							<div>
								<span class="font-medium">Source Type:</span> {{ conversionEventDefinition.sourcetype }}
							</div>
							<div>
								<span class="font-medium">Attribution Minutes:</span> {{
									conversionEventDefinition.attributionminutes }}
							</div>
							<div class="mt-4">
								<span class="font-medium">Conversion Information:</span>
								<pre
									class="bg-gray-200 rounded p-2">{{ JSON.stringify(conversionEventDefinition.conversiondata, null, 2) }}</pre>
							</div>
							<div class="mt-4">
								<span class="font-medium">Source Information:</span>
								<pre
									class="bg-gray-200 rounded p-2">{{ JSON.stringify(conversionEventDefinition.sourcedata, null, 2) }}</pre>
							</div>
						</div>
					</div>
					<div class="grid grid-cols-12 gap-6 mt-8">
						<CampaignNumberChart campaignId="1" :widgetData="widget1" editMenu="false"
							:isLoaded="widget1.isLoaded" />
						<CampaignNumberChart campaignId="1" :widgetData="widget2" editMenu="false"
							:isLoaded="widget2.isLoaded" />
						<CampaignNumberChart campaignId="1" :widgetData="widget3" editMenu="false"
							:isLoaded="widget3.isLoaded" />
						<CampaignNumberChart campaignId="1" :widgetData="widget4" editMenu="false"
							:isLoaded="widget4.isLoaded" />
						<CampaignNumberChart campaignId="1" :widgetData="widget5" editMenu="false"
							:isLoaded="widget5.isLoaded" />
						<CampaignNumberChart campaignId="1" :widgetData="widget6" editMenu="false"
							:isLoaded="widget6.isLoaded" />
						<CampaignNumberChart campaignId="1" :widgetData="widget7" editMenu="false"
							:isLoaded="widget7.isLoaded" />
						<CampaignNumberChart campaignId="1" :widgetData="widget8" editMenu="false"
							:isLoaded="widget8.isLoaded" />
						<CampaignBarChart campaignId="1" :widgetData="widget9" :chartData="barChartData" editMenu="false"
							:isLoaded="widget9.isLoaded" />
						<CampaignBarChart campaignId="1" :widgetData="widget10" :chartData="barChartData" editMenu="false"
							:isLoaded="widget10.isLoaded" />
						<CampaignLineChart campaignId="1" :widgetData="widget11" editMenu="false"
							:isLoaded="widget11.isLoaded" />
					</div>
					<div class="mt-8">
						<div class="scrollable-table">
							<table class="min-w-full divide-y divide-gray-200 w-full">
								<thead class="bg-gray-50">
									<tr>
										<th v-for="(value, key) in conversionLogs[0]" :key="key"
											class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
											{{ key }}
										</th>
									</tr>
								</thead>
								<tbody class="bg-white divide-y divide-gray-200">
									<tr v-for="(log, index) in conversionLogs" :key="index">
										<td v-for="(value, key) in log" :key="key" class="px-6 py-4 whitespace-nowrap">
											{{ value }}
										</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>


				</div>
			</main>
		</div>
	</div>
</template>

<style scoped>
.scrollable-table {
	max-height: 400px;
	overflow-x: auto;
	overflow-y: auto;
	display: inline-block;
	width: 100%;
}
</style>

<script>
import moment from 'moment';
import _ from 'underscore';
import { ref } from 'vue';
import Notification from '../../components/Notification.vue';
import PaginationNumeric from '../../components/PaginationNumeric.vue';
import SearchForm from '../../components/SearchForm.vue';
import CampaignViewTable from '../../partials/campaigns/CampaignViewTable.vue';
import Header from '../../partials/Header.vue';
import Sidebar from '../../partials/Sidebar.vue';

import CampaignBarChart from '../../partials/analytics/Campaign/CampaignBarChart.vue';
import CampaignLineChart from '../../partials/analytics/Campaign/CampaignLineChart.vue';
import CampaignNumberChart from '../../partials/analytics/Campaign/CampaignNumberChart.vue';
import { MarketingService } from '../../services/marketing.js';
import { AttributionService } from '../../services/attribution.js';
import { CoversionEventsService } from '../../services/conversion-events.js';
import { tailwindConfig } from '../../utils/Utils';
import DropdownFull from '../../components/DropdownFull.vue';
import { getAvailableProjects, getConfiguredAttributionConversions } from '../../services/project';

export default {
	name: 'CampaignOverview',
	props: ['campaignId'],
	components: {
		Sidebar,
		Header,
		SearchForm,
		PaginationNumeric,
		Notification,
		CampaignNumberChart,
		CampaignBarChart,
		CampaignLineChart,
		CampaignViewTable,
		DropdownFull,
	},
	setup() {
		const sidebarOpen = ref(false);
		const notificationInfoOpen = ref(true);

		return {
			sidebarOpen,
			notificationInfoOpen,
		};
	},
	data() {
		return {
			marketingActivities: [],
			widget1: {},
			widget2: {},
			widget3: {},
			widget4: {},
			widget5: {},
			widget6: {},
			widget7: {},
			widget8: {},
			widget9: {},
			widget10: {},
			widget11: {},
			widget12: {},
			conversionEventDefinition: {},
			conversionLogs: [],
			showInfo: false,
			barChartData: {
				labels: [],
				datasets: [
					{
						label: '',
						backgroundColor: '#f87979',
						borderColor: '#f87979',
						data: [],
					},
				],
			},
			orgId: 1,
			projectList: [],
			isLoadingAdditionalData: true,
			newConversionSystemActive: false,
			readyToShowStencils: false,
			selectedProjectId: localStorage.campaignOverviewSelectedProjectId
		};
	},
	methods: {
		buildChartDataForBar(connectionData, conversionData) {
			let datasets = [];
			let labels = [];
			let viewCount = [];
			let dataConnect = [];
			let dataConversion = [];
			console.log("connectionData", JSON.stringify(connectionData));
			for (var i = 0; i < connectionData.length; i++) {
				labels.push(connectionData[i].view_source);
				viewCount.push(connectionData[i].view_source_count);
				dataConnect.push(connectionData[i].connection_source_count);
				dataConversion.push(connectionData[i].conversion_source_count);
			}

			var newDatasetView = {
				label: 'Views',
				data: viewCount,
				backgroundColor: [
					tailwindConfig().theme.colors.lime[400],
					tailwindConfig().theme.colors.lime[400],
					tailwindConfig().theme.colors.lime[400],
				],
				hoverBackgroundColor: [
					tailwindConfig().theme.colors.lime[500],
					tailwindConfig().theme.colors.lime[500],
					tailwindConfig().theme.colors.lime[500],
				],
				hoverBorderColor: tailwindConfig().theme.colors.white,
			};
			datasets.push(newDatasetView);

			var newDataset = {
				label: 'Connections',
				data: dataConnect,
				backgroundColor: [
					tailwindConfig().theme.colors.sky[400],
					tailwindConfig().theme.colors.sky[400],
					tailwindConfig().theme.colors.sky[400],
				],
				hoverBackgroundColor: [
					tailwindConfig().theme.colors.sky[500],
					tailwindConfig().theme.colors.sky[500],
					tailwindConfig().theme.colors.sky[500],
				],
				hoverBorderColor: tailwindConfig().theme.colors.white,
			};
			datasets.push(newDataset);

			var newDataset2 = {
				label: 'Conversions',
				data: dataConversion,
				backgroundColor: [
					tailwindConfig().theme.colors.emerald[600],
					tailwindConfig().theme.colors.emerald[600],
					tailwindConfig().theme.colors.emerald[600],
				],
				hoverBackgroundColor: [
					tailwindConfig().theme.colors.emerald[700],
					tailwindConfig().theme.colors.emerald[700],
					tailwindConfig().theme.colors.emerald[700],
				],
				hoverBorderColor: tailwindConfig().theme.colors.white,
			};
			datasets.push(newDataset2);
			return {
				labels,
				datasets,
			};
		},
		buildChartDataForBarContent(contentData) {
			let datasets = [];
			let labels = [];
			let viewCount = [];
			console.log("contentData", JSON.stringify(contentData));
			for (var i = 0; i < contentData.length; i++) {
				labels.push(contentData[i].content);
				viewCount.push(contentData[i].content_count);
			}

			var newDatasetView = {
				label: 'Content Events',
				data: viewCount,
				backgroundColor: [
					tailwindConfig().theme.colors.sky[400],
					tailwindConfig().theme.colors.sky[400],
					tailwindConfig().theme.colors.sky[400],
				],
				hoverBackgroundColor: [
					tailwindConfig().theme.colors.sky[500],
					tailwindConfig().theme.colors.sky[500],
					tailwindConfig().theme.colors.sky[500],
				],
				hoverBorderColor: tailwindConfig().theme.colors.white,
			};
			datasets.push(newDatasetView);
			console.log("labels", JSON.stringify(labels));
			console.log("datasets", JSON.stringify(datasets));
			return {
				labels,
				datasets,
			};
		},
		buildChartDataForImpressions(impressionsByDay) {
			let labels = [];
			for (let index = 0; index < impressionsByDay.length; index++) {
				labels.push(impressionsByDay[index].date);
			}

			let impressions = [];
			for (let index = 0; index < labels.length; index++) {
				impressions.push(impressionsByDay[index].count);
			}

			let finalLabels = [];
			for (let i = 0; i < labels.length; i++) {
				finalLabels.push(moment(labels[i]).format('MM-DD-YY'));
			}

			let chartData = {
				labels: finalLabels,
				datasets: [
					// Blue line
					{
						label: 'Events',
						data: impressions,
						borderColor: tailwindConfig().theme.colors.indigo[400],
						fill: false,
						borderWidth: 2,
						tension: 0,
						pointRadius: 0,
						pointHoverRadius: 3,
						pointBackgroundColor: tailwindConfig().theme.colors.indigo[400],
						clip: 20,
					}
				],
			};
			return chartData;
		},
		buildChartDataForVolume(connectionsByDay, conversionsByDay) {
			console.log("connectionsByDay", JSON.stringify(connectionsByDay));
			let labels = [];
			for (let index = 0; index < connectionsByDay.length; index++) {
				labels.push(connectionsByDay[index].date);
			}

			for (let index = 0; index < conversionsByDay.length; index++) {
				labels.push(conversionsByDay[index].date);
			}

			labels = _.uniq(labels);

			let datasets = [];
			//Conversions
			let dataConversions = [];
			let dataConnections = [];
			for (let index = 0; index < labels.length; index++) {
				let found = false;
				for (let i = 0; i < conversionsByDay.length; i++) {
					if (conversionsByDay[i].date == labels[index]) {
						found = true;
						dataConversions.push(conversionsByDay[i].count);
						break;
					}
				}
				if (!found) {
					dataConversions.push(0);
				}

				//Connections
				found = false;
				for (let i = 0; i < connectionsByDay.length; i++) {
					if (connectionsByDay[i].date == labels[index]) {
						found = true;
						dataConnections.push(connectionsByDay[i].count);
						break;
					}
				}

				if (!found) {
					dataConnections.push(0);
				}
			}

			let finalLabels = [];
			for (let i = 0; i < labels.length; i++) {
				finalLabels.push(moment(labels[i]).format('MM-DD-YY'));
			}

			let chartData = {
				labels: finalLabels,
				datasets: [
					// Blue line
					{
						label: 'Conversions',
						data: dataConversions,
						borderColor: tailwindConfig().theme.colors.indigo[400],
						fill: false,
						borderWidth: 2,
						tension: 0,
						pointRadius: 0,
						pointHoverRadius: 3,
						pointBackgroundColor: tailwindConfig().theme.colors.indigo[400],
						clip: 20,
					},
					// emerald line
					{
						label: 'Connections',
						data: dataConnections,
						borderColor: tailwindConfig().theme.colors.emerald[500],
						fill: false,
						borderWidth: 2,
						tension: 0,
						pointRadius: 0,
						pointHoverRadius: 3,
						pointBackgroundColor: tailwindConfig().theme.colors.emerald[500],
						clip: 20,
					},
				],
			};
			return chartData;
		},
		buildListOfWallets(walletInfo) {
			this.campaignActivity = [];
			for (var i = 0; i < walletInfo.length; i++) {
				this.campaignActivity.push({
					id: i,
					address: walletInfo[i].address,
					visits: 0,
					connections: walletInfo[i].connections,
					conversions: walletInfo[i].conversions,
					type: walletInfo[i].new_user == 'true' ? 'New' : 'Returning',
					revenue: 0,
					channels: walletInfo[i].source,
					persona: 'TBD',
				});
			}
			this.$forceUpdate();
		},
		getProjects: function () {
			return this.projectList;
		},
		projectChanged: function (value) {
			localStorage.campaignOverviewSelectedProjectId = this.selectedProjectId = value;

			this.loadMarketingCampaign();
		},
		loadMarketingCampaign: function () {
			this.isLoadingAdditionalData = true;
			const selectedProjectUuid = this.projectList.find(x => x.id == this.selectedProjectId).uuid;
			let mainMarketingCampaign = new MarketingService(
				selectedProjectUuid,
				current_data => {
					//Current Data is an array of captured data thus far
					if (current_data.length > 0) {
						this.widget1.display = `${current_data[0].totalViews}`;
						this.widget1.isLoaded = true;

						this.widget2.display = `${current_data[0].newEngagers}`;
						this.widget2.isLoaded = true;

						this.widget3.display = current_data[0].connections;
						this.widget3.isLoaded = true;

						this.widget4.display = `${current_data[0].conversion}`;
						this.widget4.isLoaded = true;

						// this.widget4.display = `$${current_data[0].revenue}`;
						// this.widget4.isLoaded = true;

						if (current_data[0].sources.length > 0 && this.widget5.isLoaded == false) {
							this.widget5.chartData = this.buildChartDataForBar(
								current_data[0].sources,
							);
							this.widget5.isLoaded = true;
						}

						console.log("Connection Info: ", current_data[0].connectionsByDay, current_data[0].conversionsByDay)

						//if(current_data[0].connectionsByDay.length > 0 && current_data[0].conversionsByDay > 0 && this.widget6.isLoaded == false){
						console.log("Loading Volume");
						this.widget6.chartData = this.buildChartDataForVolume(
							current_data[0].connectionsByDay,
							current_data[0].conversionsByDay,
						);
						this.widget6.isLoaded = true;
						//}

						if (current_data[0].content.length > 0 && this.widget7.isLoaded == false) {
							this.widget7.chartData = this.buildChartDataForBarContent(
								current_data[0].content,
							);
							this.widget7.isLoaded = true;
						}

						if (current_data[0].impressionsByDay.length > 0 && this.widget8.isLoaded == false) {
							this.widget8.chartData = this.buildChartDataForImpressions(
								current_data[0].impressionsByDay
							);
							this.widget8.isLoaded = true;
						}

						this.buildListOfWallets(current_data[0].walletInfo);
					}
				},
				this.campaignId,
				() => this.isLoadingAdditionalData = false
			);

			this.campaignActivity = [];
		},
		async activateOldConversionSystem() {
			this.orgId = localStorage.getItem('userOrgId');


			const allProjects = await getAvailableProjects(true, true);
			this.projectList = allProjects.filter(x => x.dataConnections && x.dataConnections.length);
			if (!this.selectedProjectId) {
				this.selectedProjectId = this.projectList[0].id;
				localStorage.campaignOverviewSelectedProjectId = this.selectedProjectId;
			}

			this.widget1 = {
				title: 'Total Views',
				type: 'NUMBER',
				id: 99,
				apiName: 'total-views',
				infoLabel: '',
				projectId: 99,
				display: '..',
				size: '3',
				inputs: 'widgetData.inputs',
				XAxisInputLabel: 'widgetData.XAxisInputLabel',
				inputSummary: 'widgetData.inputSummary',
				time: 'widgetData.time',
				isLoaded: false,
			};

			this.widget2 = {
				title: 'New Engagements',
				type: 'NUMBER',
				id: 99,
				apiName: 'new-engagers',
				infoLabel: '',
				projectId: 99,
				display: '..',
				size: '3',
				inputs: 'widgetData.inputs',
				XAxisInputLabel: 'widgetData.XAxisInputLabel',
				inputSummary: 'widgetData.inputSummary',
				time: 'widgetData.time',
				isLoaded: false,
			};

			this.widget3 = {
				title: 'Wallet Connections',
				type: 'NUMBER',
				id: 99,
				apiName: 'CONNECTIONS',
				display: '..',
				infoLabel: '',
				projectId: 99,
				size: '3',
				inputs: 'widgetData.inputs',
				XAxisInputLabel: 'widgetData.XAxisInputLabel',
				inputSummary: 'widgetData.inputSummary',
				time: 'widgetData.time',
				isLoaded: false,
			};

			this.widget4 = {
				title: 'On-Chain Conversion %',
				type: 'NUMBER',
				id: 99,
				apiName: 'CONVERSIONS',
				infoLabel: '',
				projectId: 99,
				display: '..',
				size: '3',
				inputs: 'widgetData.inputs',
				XAxisInputLabel: 'widgetData.XAxisInputLabel',
				inputSummary: 'widgetData.inputSummary',
				time: 'widgetData.time',
				isLoaded: false,
			};

			this.widget5 = {
				title: 'Volume by Source',
				type: 'BAR',
				id: 99,
				apiName: 'REVENUE',
				infoLabel: 'nothing',
				projectId: 99,
				size: '3',
				inputs: 'widgetData.inputs',
				XAxisInputLabel: 'widgetData.XAxisInputLabel',
				inputSummary: 'widgetData.inputSummary',
				time: 'widgetData.time',
				isLoaded: false,
			};

			this.widget6 = {
				title: 'Volume by Activity',
				type: 'LINE',
				id: 99,
				apiName: 'REVENUE',
				infoLabel: 'nothing',
				projectId: 99,
				size: '3',
				inputs: 'widgetData.inputs',
				XAxisInputLabel: 'widgetData.XAxisInputLabel',
				inputSummary: 'widgetData.inputSummary',
				time: 'widgetData.time',
				isLoaded: false,
			};

			this.widget7 = {
				title: 'Volume by Content',
				type: 'BAR',
				id: 99,
				apiName: 'REVENUE',
				infoLabel: 'nothing',
				projectId: 99,
				size: '3',
				inputs: 'widgetData.inputs',
				XAxisInputLabel: 'widgetData.XAxisInputLabel',
				inputSummary: 'widgetData.inputSummary',
				time: 'widgetData.time',
				isLoaded: false,
			};
			this.widget8 = {
				title: 'Events By Day',
				type: 'LINE',
				id: 99,
				apiName: 'REVENUE',
				infoLabel: 'nothing',
				projectId: 99,
				size: '3',
				inputs: 'widgetData.inputs',
				XAxisInputLabel: 'widgetData.XAxisInputLabel',
				inputSummary: 'widgetData.inputSummary',
				time: 'widgetData.time',
				isLoaded: false
			};

			this.loadMarketingCampaign();
		},
		initializeTopChartsNewSystem() {
			this.widget1 = {
				title: 'Total Unique Users',
				type: 'NUMBER',
				id: 99,
				infoLabel: '',
				projectId: 99,
				display: '..',
				size: '3',
				inputs: 'widgetData.inputs',
				XAxisInputLabel: 'widgetData.XAxisInputLabel',
				inputSummary: 'widgetData.inputSummary',
				time: 'widgetData.time',
				isLoaded: false,
			};

			this.widget2 = {
				title: 'Total Users Converted',
				type: 'NUMBER',
				id: 99,
				infoLabel: '',
				projectId: 99,
				display: '..',
				size: '3',
				inputs: 'widgetData.inputs',
				XAxisInputLabel: 'widgetData.XAxisInputLabel',
				inputSummary: 'widgetData.inputSummary',
				time: 'widgetData.time',
				isLoaded: false,
			};

			this.widget3 = {
				title: 'Total Conversions',
				type: 'NUMBER',
				id: 99,
				display: '..',
				infoLabel: '',
				projectId: 99,
				size: '3',
				inputs: 'widgetData.inputs',
				XAxisInputLabel: 'widgetData.XAxisInputLabel',
				inputSummary: 'widgetData.inputSummary',
				time: 'widgetData.time',
				isLoaded: false,
			};

			this.widget4 = {
				title: 'Conversion %',
				type: 'NUMBER',
				id: 99,
				infoLabel: '',
				projectId: 99,
				display: '..',
				size: '3',
				inputs: 'widgetData.inputs',
				XAxisInputLabel: 'widgetData.XAxisInputLabel',
				inputSummary: 'widgetData.inputSummary',
				time: 'widgetData.time',
				isLoaded: false,
			};

			this.widget5 = {
				title: 'Total Views',
				type: 'NUMBER',
				id: 99,
				infoLabel: '',
				projectId: 99,
				display: '..',
				size: '3',
				inputs: 'widgetData.inputs',
				XAxisInputLabel: 'widgetData.XAxisInputLabel',
				inputSummary: 'widgetData.inputSummary',
				time: 'widgetData.time',
				isLoaded: false,
			};

			this.widget6 = {
				title: 'Total Connections',
				type: 'NUMBER',
				id: 99,
				infoLabel: '',
				projectId: 99,
				display: '..',
				size: '3',
				inputs: 'widgetData.inputs',
				XAxisInputLabel: 'widgetData.XAxisInputLabel',
				inputSummary: 'widgetData.inputSummary',
				time: 'widgetData.time',
				isLoaded: false,
			};

			this.widget7 = {
				title: 'Conversions Last 7 days',
				type: 'NUMBER',
				id: 99,
				display: '..',
				infoLabel: '',
				projectId: 99,
				size: '3',
				inputs: 'widgetData.inputs',
				XAxisInputLabel: 'widgetData.XAxisInputLabel',
				inputSummary: 'widgetData.inputSummary',
				time: 'widgetData.time',
				isLoaded: false,
			};

			this.widget8 = {
				title: 'Coversions Last 30 days',
				type: 'NUMBER',
				id: 99,
				infoLabel: '',
				projectId: 99,
				display: '..',
				size: '3',
				inputs: 'widgetData.inputs',
				XAxisInputLabel: 'widgetData.XAxisInputLabel',
				inputSummary: 'widgetData.inputSummary',
				time: 'widgetData.time',
				isLoaded: false,
			};

			this.widget9 = {
				title: 'Volume by Source',
				type: 'BAR',
				id: 99,
				infoLabel: 'nothing',
				projectId: 99,
				size: '3',
				inputs: 'widgetData.inputs',
				XAxisInputLabel: 'widgetData.XAxisInputLabel',
				inputSummary: 'widgetData.inputSummary',
				time: 'widgetData.time',
				isLoaded: false,
			};

			this.widget10 = {
				title: 'Volume by Content',
				type: 'BAR',
				id: 99,
				infoLabel: 'nothing',
				projectId: 99,
				size: '3',
				inputs: 'widgetData.inputs',
				XAxisInputLabel: 'widgetData.XAxisInputLabel',
				inputSummary: 'widgetData.inputSummary',
				time: 'widgetData.time',
				isLoaded: false,
			};

			this.widget11 = {
				title: 'Conversions By Day',
				type: 'LINE',
				id: 99,
				infoLabel: 'nothing',
				projectId: 99,
				size: '3',
				inputs: 'widgetData.inputs',
				XAxisInputLabel: 'widgetData.XAxisInputLabel',
				inputSummary: 'widgetData.inputSummary',
				time: 'widgetData.time',
				isLoaded: false,
			};
		},
		async loadConversionEventInfo(conversionService) {
			const conversionInfo = await conversionService.getConversionEventInfo();
			this.widget1.display = `${conversionInfo.total_users}`;
			this.widget1.isLoaded = true;

			this.widget2.display = `${conversionInfo.total_converted}`;
			this.widget2.isLoaded = true;

			this.widget3.display = `${conversionInfo.total_conversions}`;
			this.widget3.isLoaded = true;

			this.widget4.display = `${conversionInfo.percent_conversions.toFixed(2)}`;
			this.widget4.isLoaded = true;
		},
		async loadConversionsBySource(conversionService) {
			const conversionsBySource = await conversionService.getConverionsBySource();

			if (conversionsBySource.length > 0) {
				let datasets = [];
				let labels = [];
				let dataConversion = [];
				console.log("conversionsBySource", JSON.stringify(conversionsBySource));
				for (var i = 0; i < conversionsBySource.length; i++) {
					labels.push(conversionsBySource[i].utm_source);
					dataConversion.push(conversionsBySource[i].conversions);
				}

				var newDataset = {
					label: 'Conversions',
					data: dataConversion,
					backgroundColor: [
						tailwindConfig().theme.colors.emerald[600],
						tailwindConfig().theme.colors.emerald[600],
						tailwindConfig().theme.colors.emerald[600],
					],
					hoverBackgroundColor: [
						tailwindConfig().theme.colors.emerald[700],
						tailwindConfig().theme.colors.emerald[700],
						tailwindConfig().theme.colors.emerald[700],
					],
					hoverBorderColor: tailwindConfig().theme.colors.white,
				};
				datasets.push(newDataset);

				this.widget9.chartData = {
					labels,
					datasets,
				}
				this.widget9.isLoaded = true;
			}
		},
		async loadConversionsByContent(conversionService) {
			const conversionsByContent = await conversionService.getConverionsByContent();

			if (conversionsByContent.length > 0) {
				let datasets = [];
				let labels = [];
				let dataConversion = [];
				console.log("conversionsByContent", JSON.stringify(conversionsByContent));
				for (var i = 0; i < conversionsByContent.length; i++) {
					labels.push(conversionsByContent[i].utm_content);
					dataConversion.push(conversionsByContent[i].conversions);
				}

				var newDataset = {
					label: 'Conversions',
					data: dataConversion,
					backgroundColor: [
						tailwindConfig().theme.colors.emerald[600],
						tailwindConfig().theme.colors.emerald[600],
						tailwindConfig().theme.colors.emerald[600],
					],
					hoverBackgroundColor: [
						tailwindConfig().theme.colors.emerald[700],
						tailwindConfig().theme.colors.emerald[700],
						tailwindConfig().theme.colors.emerald[700],
					],
					hoverBorderColor: tailwindConfig().theme.colors.white,
				};
				datasets.push(newDataset);

				this.widget10.chartData = {
					labels,
					datasets,
				}
				this.widget10.isLoaded = true;
			}
		},
		async loadTotalViews(attributionService) {
			const totalViews = await attributionService.getTotalViews()
			this.widget5.display = `${totalViews}`;
			this.widget5.isLoaded = true;
		},
		async loadTotalConnections(attributionService) {
			const totalConnections = await attributionService.getTotalConnections()
			this.widget6.display = `${totalConnections}`;
			this.widget6.isLoaded = true;
		},
		async loadConversionTimeFrames(conversionService) {
			const conversionTimeFrames = await conversionService.getConversionTimeFrames();
			this.widget7.display = `${conversionTimeFrames.last_7_days || 0}`;
			this.widget7.isLoaded = true;

			this.widget8.display = `${conversionTimeFrames.last_30_days || 0}`;
			this.widget8.isLoaded = true;
		},
		async getConversionInfoById(conversionService) {
			const conversionObj = await conversionService.getConversionEventById();
			this.conversionEventDefinition = conversionObj;
		},
		async getConversionLogs(conversionService) {
			const conversionLogs = await conversionService.getConversionEventLogs();
			this.conversionLogs = conversionLogs;
		},
		async loadConversionsByDay(conversionService) {
			const conversionsByDay = await conversionService.getConverionsByDay();
			let labels = [];
			for (let index = 0; index < conversionsByDay.length; index++) {
				labels.push(conversionsByDay[index].day);
			}

			let conversions = [];
			for (let index = 0; index < labels.length; index++) {
				conversions.push(conversionsByDay[index].conversions);
			}

			// Insert a point with a value of 0 before the first event
			labels.unshift(moment(labels[0]).subtract(1, 'days').toDate());
			conversions.unshift(0);

			let finalLabels = [];
			for (let i = 0; i < labels.length; i++) {
				finalLabels.push(moment(labels[i]).format('MM-DD-YY'));
			}

			let chartData = {
				labels: finalLabels,
				datasets: [
					// Blue line
					{
						label: 'Conversions',
						data: conversions,
						borderColor: tailwindConfig().theme.colors.indigo[400],
						fill: false,
						borderWidth: 2,
						tension: 0,
						pointRadius: 0,
						pointHoverRadius: 3,
						pointBackgroundColor: tailwindConfig().theme.colors.indigo[400],
						clip: 20,
					}
				],
			};

			this.widget11.chartData = chartData;
			this.widget11.isLoaded = true;
		},
		async activateNewConversionSystem(conversion_id) {
			let conversionService = new CoversionEventsService(conversion_id);
			this.initializeTopChartsNewSystem();

			const allProjects = await getAvailableProjects(true, true);
			this.projectList = allProjects.filter(x => x.dataConnections && x.dataConnections.length);

			if (!this.selectedProjectId) {
				this.selectedProjectId = this.projectList[0].id;
				localStorage.campaignOverviewSelectedProjectId = this.selectedProjectId;
			}

			let attService = new AttributionService(this.campaignId);
			//const selectedProjectUuid = this.projectList.find(x => x.id == this.selectedProjectId).uuid;
			this.loadConversionEventInfo(conversionService);
			this.loadTotalViews(attService);
			this.loadTotalConnections(attService);
			this.loadConversionsBySource(conversionService);
			this.loadConversionsByContent(conversionService);
			this.loadConversionTimeFrames(conversionService);
			this.loadConversionsByDay(conversionService);
			this.getConversionInfoById(conversionService);
			this.getConversionLogs(conversionService);

			//this.widget7.display = `${newEngagers}`;
			//this.widget7.isLoaded = true;
		}
	},
	async mounted() {
		this.readyToShowStencils = false;
		amplitude.getInstance().logEvent('CAMPAIGN_VIEW_VIEWED');
		console.log('campaignId: ' + this.campaignId);
		let result = await getConfiguredAttributionConversions(this.campaignId);
		this.readyToShowStencils = true;
		console.log("RESULT: ", result);

		if (result.length > 0) {
			console.log("NEW CONVERSION SYSTEM ACTIVATING");
			this.newConversionSystemActive = true;
			await this.activateNewConversionSystem(result[0].id);
		}
		else {
			console.log("OLD CONVERSION SYSTEM ACTIVATING");
			this.newConversionSystemActive = false;
			await this.activateOldConversionSystem();
		}
	},
};
</script>
