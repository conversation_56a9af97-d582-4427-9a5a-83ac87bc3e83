<template>
	<div class="mb-6 text-center">
		<h1 class="text-xl font-semibold">Enable Customer Accounts</h1>
		<p class="text-md mt-2">Step 2 of 3</p>
	</div>

	<!-- Card -->
	<div class="rounded-lg shadow-lg bg-white p-6 w-full md:max-w-3xl">

		<!-- For mobile view -->
		<div class="block md:hidden mb-4">
			<video autoplay loop muted inline>
				<source src="../../images/EnableCustomerAccounts.mp4" type="video/mp4">
			</video>
		</div>

		<div class="flex flex-col md:flex-row justify-between items-start">

			<!-- GIF (desktop view) -->
			<div class="hidden md:block mr-6 flex-shrink-0 w-3/5">
				<video autoplay loop muted inline>
					<source src="../../images/EnableCustomerAccounts.mp4" type="video/mp4">
				</video>
			</div>

			<!-- Text and Button -->
			<div class="flex-1">
				<h2 class="text-xl mb-2 font-bold">Enable Customer Accounts</h2>
				<p class="text-sm mb-4">In order to let your customers earn points and rewards, you will need to enable Customer Accounts for your store.</p>
				<a
					:href="this.customerAccountsUrl"
					target="_blank"
					class="text-xs font-semibold mb-4 inline-block text-white px-4 py-2 rounded bg-ralsuccess-dark"
				>
					<div class="flex items-center">
						<img src="../../images/external-link-out-white.svg" class="h-5 w-5 mr-2" />
						Enable Customer Accounts
					</div>
				</a>
			</div>
		</div>

		<!-- Next Step Button -->
		<div class="mt-6 text-right flex justify-end">
			<button
				class="bg-gradient-button-primary font-semibold text-white text-xs py-2 px-4 rounded-button"
				@click="nextStep">
				<div class="flex items-center">
					<div class="uppercase mr-2">Next Step</div>
					<img src="../../images/arrow-right.svg" class="h-5 w-5" />
				</div>
			</button>
		</div>
	</div>
</template>

<script>
	export default {
		name: 'Step2',
		props: ['orgExternalDomain'],
		computed: {
			customerAccountsUrl() {
				const subdomain = this.orgExternalDomain?.replace('.myshopify.com', '');
				return `https://admin.shopify.com/store/${subdomain}/settings/customer_accounts`
			}
		},
		methods: {
			nextStep() {
				this.$emit('next-step');
			}
		},
	}
</script>
