<template>
	<div class="mb-6 text-center">
		<h1 class="text-xl font-semibold">Setup your loyalty bot branding</h1>
		<p class="text-md mt-2">Step 1 of 3</p>
	</div>


	<!-- Card -->
	<div class="rounded-lg shadow-lg bg-white p-6 w-full md:max-w-3xl" v-if="!loyaltyBotCustomized">

		<!-- For mobile view -->
		<div class="block md:hidden mb-4">
			<img src="../../images/LoyaltyBotPreview.png" />
		</div>

		<div class="flex flex-col md:flex-row justify-center items-start">

			<!-- GIF (desktop view) -->
			<div class="hidden md:block mr-6 flex-shrink-0 w-3/5">
				<img src="../../images/LoyaltyBotPreview.png" />
			</div>

			<!-- Text and Button -->
			<div class="flex-1">
				<h2 class="text-xl mb-2 font-bold">Your Custom Loyalty Bot</h2>
				<p class="text-sm mb-4"><PERSON><PERSON><PERSON> adds a small button to the bottom of your website. We call it the "loyalty bot". Customers can click on it to access your loyalty program and to get nudges that drive engagement</p>


                <section>
                    <div class="sm:flex sm:items-center space-y-4 sm:space-y-0 sm:space-x-4 mt-5">
                      <div class="">
                        <label class="block text-sm font-medium mb-1" for="name" >Launch Text</label>
						<div class="text-sm">Text displayed in the minimized chat bot bubble.</div>
                        <input id="launchText" class="form-input w-full" type="text" v-model="launchText" />
                      </div>
                    </div>
                </section>
                <section>
                    <div class="sm:flex sm:items-center space-y-4 sm:space-y-0 sm:space-x-4 mt-5">
                      <div class="">
                        <label class="block text-sm font-medium mb-1" for="name" >Brand Color</label>
                        <input id="launchText" class="form-input w-full" type="color" v-model="brandColor" style="height: 3em" />
                      </div>
                    </div>
                </section>
				<section>
                    <div class="sm:flex sm:items-center space-y-4 sm:space-y-0 sm:space-x-4 mt-5">
                      <div class="">
                        <label class="block text-sm font-medium mb-1" for="name" >Launch Avatar Icon</label>
						<img v-if="avatarIconUrl" :src="avatarIconUrl" style="max-width: 50px; max-height: 50px; border-radius: 100% ;"/>
						<a v-if="avatarIconUrl" @click="avatarIconUrl = ''" class="ml-4" style="text-decoration: underline; cursor: pointer;">
							Replace
						</a>
						<form v-if="!avatarIconUrl">
							<input
								type="file"
								class="form-input w-full"
								:placeholder="`Enter URL or upload image`"
								@change="uploadFile($event, '/chat-graph/image').then(url => this.avatarIconUrl = url);"
							/>
							<i>For best results, upload a 256x256 image</i>
						</form>
                      </div>
                    </div>
				</section>

				<div class="text-sm italic mt-5">You can change this later.</div>
			</div>
		</div>

		<!-- Next Button (Step 1.5) -->
		<div class="mt-6 text-right flex justify-end" v-if="!loyaltyBotCustomized">
			<button
				class="bg-gradient-button-primary font-semibold text-white text-xs py-2 px-4 rounded-button"
				@click="next">
				<div class="flex items-center">
					<div class="uppercase mr-2">Next</div>
					<img src="../../images/arrow-right.svg" class="h-5 w-5" />
				</div>
			</button>
		</div>
	</div>

	<!-- Card -->
	<div class="rounded-lg shadow-lg bg-white p-6 w-full md:max-w-3xl" v-if="loyaltyBotCustomized">

		<!-- For mobile view -->
		<div class="block md:hidden mb-4">
			<video autoplay loop muted inline>
				<source src="../../images/EnableAppEmbed.mp4" type="video/mp4">
			</video>
		</div>

		<div class="flex flex-col md:flex-row justify-center items-start">

			<!-- GIF (desktop view) -->
			<div class="hidden md:block mr-6 flex-shrink-0 w-3/5">
				<video autoplay loop muted inline>
					<source src="../../images/EnableAppEmbed.mp4" type="video/mp4">
				</video>
			</div>

			<!-- Text and Button -->
			<div class="flex-1">
				<h2 class="text-xl mb-2 font-bold">Enable app embed</h2>
				<p class="text-sm mb-4">App embed is required for Loyalty Bot to show up properly on your store.</p>
				<p class="text-sm mb-4">Activate it automatically using the button below.</p>
				<p class="text-sm mb-4">Click the button below to navigate to our app embed and click Save to enable.</p>
				<a
					:href="this.appEmbedUrl"
					target="_blank"
					class="text-xs font-semibold mb-4 inline-block text-white px-4 py-2 rounded bg-ralsuccess-dark"
				>
					<div class="flex items-center">
						<img src="../../images/external-link-out-white.svg" class="h-5 w-5 mr-2" />
						Enable app embed
					</div>
				</a>
			</div>
		</div>

		<!-- Next Step Button -->
		<div class="mt-6 text-right flex justify-end" v-if="loyaltyBotCustomized">
			<button
				class="bg-gradient-button-primary font-semibold text-white text-xs py-2 px-4 rounded-button"
				@click="nextStep">
				<div class="flex items-center">
					<div class="uppercase mr-2">Next Step</div>
					<img src="../../images/arrow-right.svg" class="h-5 w-5" />
				</div>
			</button>
		</div>
	</div>
</template>

<script>
	import { URL_DOMAIN } from '../../utils/Utils';

	export default {
		name: 'Step1',
		props: ['orgExternalDomain'],
		data() {
			return {
				loyaltyBotCustomized: false,
				launchText: "Rewards Waiting",
				brandColor: "#6568FE",
				avatarIconUrl: 'https://raleon.io/wp-content/uploads/2023/06/raleon-white-icon.png'
			}
		},
		computed: {
			appEmbedUrl() {
				return `https://${this.orgExternalDomain}/admin/themes/current/editor?context=apps&activateAppId=ccee7757-a6f8-4bff-a402-3bbfc767bed2/app-embed`
			}
		},
		methods: {
			async next() {

				const response = await fetch(`${URL_DOMAIN}/onboard/customize-loyalty-bot`, {
					method: 'POST',
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					},
					body: JSON.stringify({
						launchText: this.launchText,
						brandColor: this.brandColor,
						avatarUrl: this.avatarIconUrl
					})
				});

				this.loyaltyBotCustomized = true;
			},

			async uploadFile(event, endpointPart) {
				const file = event.target.files[0];
				if (!endpointPart) {
					throw new Error('No endpoint to upload file to');
				}

				const endpoint = endpointPart.startsWith('/')
					? `${URL_DOMAIN}${endpointPart}`
					: endpointPart;

				const formData = new FormData();
				formData.append('file', file);

				const response = await fetch(endpoint, {
					method: 'POST',
					credentials: 'include',
					headers: {
						Authorization: endpoint.startsWith(URL_DOMAIN) ? `Bearer ${localStorage.token}` : undefined
					},
					body: formData
				});
				const result = await response.text();

				return result;
			},
			nextStep() {
				this.$emit('next-step');
			}
		},
	}
</script>
