<template>
	<div class="h-screen flex">

		<!-- Sidebar -->
		<Sidebar :sidebarOpen="sidebarOpen" @close-sidebar="sidebarOpen = false" class="h-full" />

		<!-- Main Content -->
		<div class="flex-1 flex flex-col justify-center items-center mt-auto mb-auto overflow-y-auto">
			<div class="flex-1 p-4">
				<Step1 v-if="currentStep === 1" @next-step="nextStep" :org-external-domain="orgExternalDomain" />

				<Step2 v-if="currentStep === 2" @next-step="nextStep" :org-external-domain="orgExternalDomain" />

				<Step3 v-if="currentStep === 3" @next-step="nextStep" :org-external-domain="orgExternalDomain" />

				<!-- Dots indicating steps -->
				<div class="mt-4 mb-6 flex justify-center space-x-2">
					<div :class="dotClass(1)"></div>
					<div :class="dotClass(2)"></div>
					<div :class="dotClass(3)"></div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import Sidebar from "../../partials/Sidebar.vue";
import Step1 from "./Step1.vue";
import Step2 from "./Step2.vue";
import Step3 from "./Step3.vue";
import { getOrgById } from "../../services/organization";

export default {
	name: 'Onboard',
	components: {
		Sidebar,
		Step1,
		Step2,
		Step3,
	},
	data() {
		return {
			currentStep: 1,
			orgExternalDomain: '',
		}
	},
	methods: {
		nextStep() {
			if (this.currentStep < 3) {
				this.currentStep++;
			}
		},
		dotClass(step) {
			return {
				'w-4 h-4 bg-gray-300 rounded-full': true,
				'bg-gray-700': this.currentStep == step,
			};
		},
	},
	async mounted() {
		this.orgId = localStorage.getItem('userOrgId')
		const result = await getOrgById(this.orgId);
		this.orgExternalDomain = result.externalDomain;
		console.log(`domain = ${this.orgExternalDomain}`);
	},
}
</script>

<style scoped>
.min-h-screen {
	min-height: 100vh;
}
</style>
