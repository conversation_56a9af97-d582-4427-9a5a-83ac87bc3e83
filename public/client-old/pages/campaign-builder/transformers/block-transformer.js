export function blockToCampaign(block) {
	return {
		name: block.campaignName || block.props?.title,
		type: block.campaignType?.NAME || block.props?.subtitle,
		description: block.campaignDescription || block.props?.description,
		priority: block.campaignPriority || block.props?.priority,
		category: block.campaignCategory || block.props?.category || '',
		hiddenUntilComplete: block.campaignHiddenUntilComplete || false,
		externalId: block.props?.blockId,
	};
}

export function blockToSegment(block) {
	return {
		id: block.selectedSegment?.id,
		name: block.props?.title,
		addressCount: block.props?.subtitle,
		externalId: block.props?.blockId,
	};
}

export function blockToContent(block) {
	return {
		header: block.selectedPromptTitle || '',
		headerImageUrl: block.selectedPromptImageUrl || '',
		message: block.selectedPromptSubtitle || '',
		buttonText: block.selectedPromptButtonText || '',
		buttonUrl: block.selectedPromptButtonUrl || '',
		closeMessage: block.selectedPromptCloseMessage || '',
		css: block.selectedPromptCss || {},
		externalId: block.props?.blockId,
	};
}

export function blockToGoals(block) {
	const goals = block.actions?.map(action => {
		return {
			type: action.selectedAction?.goalType,
			requiredData: action.selectedAction?.configSchema,
			externalId: action.externalId,
			name: action.selectedAction?.friendlyName,
			content: {
				message: action.selectedAction?.contentConfigSchema?.message?.value,
				buttonText: action.selectedAction?.contentConfigSchema?.buttonText?.value,
				buttonUrl: action.selectedAction?.contentConfigSchema?.buttonUrl?.value,
				header: action.selectedAction?.contentConfigSchema?.header?.value,
				headerImageUrl: '',
				closeMessage: '',
				css: {},
				goalId: action.id,
			}
		};
	});

	let blockData = {
		id: block.id || undefined,
		externalId: block.props?.blockId,
	}

	if (goals?.length) {
		blockData.goals = goals;
	}

	return blockData;
}

export function blockToReward(block, rewardTypes) {
	const rewardTypeData = Object.values(rewardTypes).find(
		rewardTypeMetadata => rewardTypeMetadata.rewardType === block.selectedReward?.rewardType
	);
	const type = rewardTypeData?.rewardType;
	const receiptType = rewardTypeData?.receiptType;

	const configData = {};
	for (const input of (block?.inputData || [])) {
		configData[input.configKey] = input.value;
	}

	return {
		id: block.id || undefined,
		name: block.actionTitle,
		configData,
		type,
		receiptType,
		externalId: block.props?.blockId,
	}
}

export function blockToCampaignLaunch(block) {
	return {
		startDate: block.selectedStartDate ?
			new Date(block.selectedStartDate).toISOString() :
			defaultStartDate(),
		endDate: block.selectedEndDate ?
			new Date(block.selectedEndDate).toISOString() :
			defaultEndDate(),
		launchExternalId: block.props?.blockId,
	};
}

function defaultStartDate() {
	let tzOffset = (new Date()).getTimezoneOffset() * 60000;
	return new Date(Date.now() - tzOffset).toISOString().substring(0, 10);
}

function defaultEndDate() {
	const today = new Date();
	const nextWeek = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 7);
	return nextWeek.toISOString().substring(0, 10);
}
