import CampaignBlock from '../building-blocks/CampaignBlock.vue';
import AudienceBlock from '../building-blocks/AudienceBlock.vue';
import ActionPromptBlock from '../building-blocks/ActionPromptBlock.vue';
import ActionBlock from '../building-blocks/ActionBlock.vue';
import RewardBlock from '../building-blocks/RewardBlock.vue';
import LaunchBlock from '../building-blocks/LaunchBlock.vue';
import { CAMPAIGN_TYPES } from '../building-blocks/campaign-types';
import { v4 } from 'uuid';

export function campaignToBlocksTransformer(campaign, goalTypes, rewardTypes, emptySegmentBlockId) {
	//Blocks are pushed in order
	const blocks = [];
	const campaignBlock = campaignToBlock(campaign);
	blocks.push(campaignBlock);

	let segmentBlock;

	if (campaign.segments && campaign.segments.length) {
		segmentBlock = segmentToBlock(campaign, campaignBlock.props.blockId);
		blocks.push(segmentBlock);
	} else {
		segmentBlock = emptySegmentToBlock(campaign, campaignBlock.props.blockId, emptySegmentBlockId);
		blocks.push(segmentBlock);
	}

	if (campaign.quests && campaign.quests.length) {
		let actionPromptBlock;
		let actionBlock;
		let rewardBlock;
		if (campaign.quests[0].content) {
			actionPromptBlock = contentToBlock(
				campaign.quests[0].content,
				segmentBlock.props.blockId,
			);
			blocks.push(actionPromptBlock);
		}

		if (campaign.quests[0].goals) {
			actionBlock = goalsToBlock(
				campaign.quests[0].goals,
				segmentBlock.props.blockId,
				goalTypes,
			);
			blocks.push(actionBlock);
		}

		if (campaign.quests[0].rewards) {
			rewardBlock = rewardToBlock(
				campaign.quests[0].rewards[0],
				segmentBlock.props.blockId,
				rewardTypes
			);
			blocks.push(rewardBlock);
		}

		if (campaign.startDate && campaign.endDate) {
			blocks.push(
				launchBlockTransformer({
					startDate: campaign.startDate,
					endDate: campaign.endDate,
					launchExternalId: campaign.launchExternalId
				}, actionPromptBlock?.props.blockId || actionBlock?.props.blockId)
			);
		}
	}

	return blocks;
}

function campaignToBlock(campaign) {
	return {
		props: {
			blockId: campaign.externalId || getNewBlockId(),
			tooltipText: 'Add an Audience',
			title: campaign.name || 'Untitled Campaign',
			subtitle: campaign.type || CAMPAIGN_TYPES.ActionPrompt.NAME,
			description: campaign.description || '',
			category: campaign.category || '',
			priority: campaign.priority || 0,
			hiddenUntilComplete: campaign.hiddenUntilComplete || false,
			status: campaign.status || 'Draft',
			startDate: campaign.startDate ? new Date(campaign.startDate).toISOString() : null,
			endDate: campaign.endDate ? new Date(campaign.endDate).toISOString() : null,
			dbCampaignType: CAMPAIGN_TYPES[campaign.type] || CAMPAIGN_TYPES.ActionPrompt,
		},
		type: CampaignBlock.name,
	}
}

function segmentToBlock(campaign, parentBlockId) {
	const segment = campaign.segments?.[0];
	const campaignType = campaign.type;

	let tooltipText = 'Add an Action Prompt';
	if (campaignType == CAMPAIGN_TYPES.NativeQuest.NAME) {
		tooltipText = 'Add a Quest Action';
	}
	return {
		props: {
			blockId: segment.externalId || getNewBlockId(),
			tooltipText,
			parentBlockId: parentBlockId,
			title: segment.name || 'Audience',
			subtitle: segment.addressCount,
		},
		type: AudienceBlock.name,
	}
}

function emptySegmentToBlock(campaign, parentBlockId, emptySegmentBlockId) {
	const campaignType = campaign.type;

	let tooltipText = 'Add an Action Prompt';
	if (campaignType == CAMPAIGN_TYPES.NativeQuest.NAME) {
		tooltipText = 'Add a Quest Action';
	}
	return {
		props: {
			blockId: emptySegmentBlockId || getNewBlockId(),
			tooltipText,
			parentBlockId: parentBlockId,
			title: 'Audience',
			subtitle: '',
		},
		type: AudienceBlock.name,
	}
}

function contentToBlock(actionPrompt, parentBlockId) {
	return {
		props: {
			blockId: actionPrompt.externalId || getNewBlockId(),
			parentBlockId: parentBlockId,
			tooltipText: '🚀 Launch your Campaign!',
			title: actionPrompt.header,
			subtitle: actionPrompt.type || '',
			promptTitle: actionPrompt.header,
			promptImageUrl: actionPrompt.headerImageUrl,
			promptSubtitle: actionPrompt.message,
			promptButtonText: actionPrompt.buttonText,
			promptUrl: actionPrompt.buttonUrl,
			promptCloseMessage: actionPrompt.closeMessage,
			promptCss: actionPrompt.css,
		},
		type: ActionPromptBlock.name,
	}
}

export function goalsToBlock(goals, parentBlockId, goalTypes) {
	return {
		props: {
			blockId: goals[0]?.externalId || getNewBlockId(),
			parentBlockId: parentBlockId,
			tooltipText: '🏅 Specify a Reward',
			goalData: goals,
			title: goals[0]?.name,
		},
		type: ActionBlock.name,
	}
}

export function rewardToBlock(reward, parentBlockId, rewardTypes) {
	return {
		props: {
			blockId: reward.externalId || getNewBlockId(),
			parentBlockId: parentBlockId,
			tooltipText: '🚀 Launch your Campaign!',
			rewardData: reward.configData,
			rewardType: rewardTypes.find(x => x.rewardType === reward.type),
			title: reward.name,
		},
		type: RewardBlock.name,
	}
}
function launchBlockTransformer(launch, parentBlockId) {
	return {
		props: {
			blockId: launch.launchExternalId || getNewBlockId(),
			parentBlockId: parentBlockId,
			title: 'Launch',
			subtitle: '🚀',
			startDate: launch.startDate.split('T')[0],
			endDate: launch.endDate.split('T')[0],
		},
		type: LaunchBlock.name,
	}
}

function getNewBlockId() {
	return v4();
}
