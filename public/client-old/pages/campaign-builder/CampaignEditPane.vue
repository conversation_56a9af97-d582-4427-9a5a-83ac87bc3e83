<template>

	<transition name="slide" appear>
		<div
			v-if="isOpen"
			id="campaign-edit-pane"
			class="drawer flex flex-col z-[100] bg-white right-0 absolute shadow-md shadow-slate-300 inset-y-0 min-w-[310px] w-1/2 md:w-1/2 lg:w-[38%] xl:w-1/3 overflow-scroll"
			:class="{ open: isOpen }"
			:style="{ position: 'fixed' }"
			@click.stop>

			<div class="px-6 sm:px-6 lg:px-12 py-8 w-full max-w-9xl mx-auto">

				<div class="flex sm:justify-between sm:items-center mb-2">
					<h1 class="text-xl md:text-xl text-slate-800 font-bold">{{ title }}</h1>
					<img src="./icons/close.svg" class="cursor-pointer" @click="$emit('closeEditPane')" />
				</div>

				<div class="border-b border-ralwhite-line"></div>

				<div class="mt-4">
					<slot />
				</div>

			</div>
		</div>
	</transition>
</template>

<style>
	.slide-enter {
		opacity: 0;
	}

	.slide-enter-active {
		animation: slide-in .75s ease-out forwards;
	}

	.slide-leave-active {
		animation: slide-out .75s ease-in forwards;
	}

	@keyframes slide-in {
		from {
			transform: translateX(110%);
		}
		to {
			transform: translateX(0);
		}
	}

	@keyframes slide-out {
		from {
			transform: translateX(0);
		}
		to {
			transform: translateX(110%);
		}
	}
</style>

<script>
	export default {
		name: 'CampaignEditPane',
		props: ['title', 'selectedBlock', 'isOpen', 'fixed'],
		emits: ['blockUpdated', 'closeEditPane'],
	}
</script>
