<template>
	<div class="mt-24">
		<button
			class="rounded-full bg-ralblack-secondary p-2 ml-3 mr-auto"
			@click.stop="$emit('addBlock')">
			<img src="./icons/plus.svg" style="min-width: 24px; min-height: 24px"/>
		</button>
		<div
			class="add-new-tooltip text-white text-left text-xs whitespace-nowrap min-h-max bg-ralprimary-main rounded-lg ml-3 mt-4"
			v-html="text">
		</div>
	</div>
</template>

<style>
	.add-new-tooltip {
		position: relative;
		font-weight: bold;
		font-size: 24px;
		border: 5px solid #5A16C9;
		padding: 10px 15px;
		background-color: #5A16C9;
		margin: 2em auto;
		text-align: center;
	}
	div.add-new-tooltip::before {
		content: '';
		position: absolute;
		display: block;
		width: 0px;
		left: 15%;
		top: 0;
		border: 8px solid transparent;
		border-top: 0;
		border-bottom: 8px solid #5A16C9;
		transform: translate(-50%, calc(-100% - 5px));
	}
</style>

<script>
	export default {
		name: 'AddNew',
		props: ['text'],
		emits: ['addBlock'],
	}
</script>

