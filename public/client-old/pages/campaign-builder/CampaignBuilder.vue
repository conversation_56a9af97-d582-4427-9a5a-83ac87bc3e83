<template>
	<div id="campaign-builder" class="flex h-screen overflow-hidden bg-white">
		<Sidebar :sidebarOpen="sidebarOpen" @close-sidebar="sidebarOpen = false" />
		<ToastStatus :status="toastStatus" :text="toastStatusText" @clear-status="clearToastStatus()" />

		<div class="relative flex flex-col flex-1 overflow-y-auto" @mousedown.left="startDrag" @mousemove="drag"
			@mouseup="stopDrag" @mouseleave="stopDrag">
			<main>
				<!-- Page header -->
				<div class="w-full max-w-9xl">
					<div class="flex sm:justify-between sm:items-center mb-4">
						<div class="flex flex-col pt-6 ml-12">
							<h1 class="text-3xl text-slate-800 font-bold tracking-wide">{{ campaignName }}</h1>
							<span class="text-sm tracking-wide">Action Prompt Creator</span>
						</div>
						<button v-if="campaignStatus" class="cursor-text mt-1 ml-12 rounded-md py-1 px-4 text-sm uppercase"
							:class="{
									'bg-ralgray-light': campaignStatus === 'Draft' || campaignStatus === 'Campaign Not Saved',
									'text-ralblack-primary': campaignStatus === 'Draft' || campaignStatus === 'Campaign Not Saved',
									'bg-raltable-ring-secondary': campaignStatus === 'Running',
									'text-ralprimary-dark': campaignStatus === 'Running',
									'bg-ralsuccess-light': campaignStatus === 'Completed',
									'text-ralsuccess-dark': campaignStatus === 'Completed',
								}">
							{{ campaignStatus }}
						</button>
						<div class="flex ml-auto">
							<button
								class="text-ralprimary-light [&:not(:disabled)]:border border-ralprimary-light font-bold text-xs py-2 px-4 rounded-button mt-2 ml-4 mr-[68px] disabled:text-slate-400 disabled:bg-slate-200 disabled:cursor-not-allowed"
								@click.prevent="this.$router.push(`/campaign/${campaign.id}`)" :disabled="!campaign.id"
								data-cy="view-analytics">
								View Analytics
								<img v-if="campaign.id" class="ml-1 mt-[1px] float-right"
									src="./icons/arrow-north-east.svg" />
							</button>
						</div>
					</div>
					<div class="border-b border-ralwhite-line"></div>
				</div>
			</main>

			<!-- Block Data -->
			<div class="canvas-bg bg-ralwhite-secondary h-full">
				<div id="block-container" class="flex items-center h-full overflow-scroll">
					<component v-if="isLoaded" v-for="(block, i) in this.campaignStore.blocks" :id="block.props.blockId"
						:is="block.type" :key="block.props.blockId" :selected-block-id="selectedBlockId"
						v-bind="block.props" :ref="el => blocks[i] = el" @block-selected="selectBlock(blocks[i].blockId)"
						@block-updated="saveBlockState(blocks[i])" @save-campaign="saveCampaign"
						@cancel-save="selectedBlockId = null" @close-edit-pane="selectedBlockId = null"
						@add-block="addChildBlock" />

					<AddNew
						v-if="this.campaignStore.blocks.length > 0 && this.campaignStore.blocks[this.campaignStore.blocks.length - 1].props.tooltipText"
						:text="this.campaignStore.blocks[this.campaignStore.blocks.length - 1].props.tooltipText"
						@add-block="addChildBlock" />

				</div>
			</div>
		</div>
	</div>
</template>


<style>
#campaign-builder {
	font-family: 'Open Sans', sans-serif;
}

.canvas-bg {
	background-image: radial-gradient(rgba(32, 32, 32, 0.25) 10%, transparent 5%);
	background-position: 0 0, 12px 12px;
	background-size: 24px 24px;
}

.disable-select {
	user-select: none;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
}
</style>


<script>
import { ref } from 'vue'
import { useCampaignStore } from './stores/campaign-store';
import { mapStores } from 'pinia';
import Sidebar from "../../partials/Sidebar.vue"
import BaseBlock from "./building-blocks/BaseBlock.vue"
import CampaignBlock from './building-blocks/CampaignBlock.vue'
import AudienceBlock from './building-blocks/AudienceBlock.vue'
import ActionPromptBlock from './building-blocks/ActionPromptBlock.vue'
import ActionBlock from './building-blocks/ActionBlock.vue'
import RewardBlock from './building-blocks/RewardBlock.vue'
import LaunchBlock from './building-blocks/LaunchBlock.vue'
import CampaignEditPane from './CampaignEditPane.vue'
import AddNew from "./AddNew.vue";
import ToastStatus from '../component/ToastStatus.vue';
import { CAMPAIGN_TYPES } from "./building-blocks/campaign-types";
import { v4 } from 'uuid';

export default {
	name: 'CampaignBuilder',
	props: [],
	emits: [],
	components: {
		Sidebar,
		BaseBlock,
		CampaignBlock,
		AudienceBlock,
		ActionPromptBlock,
		ActionBlock,
		RewardBlock,
		LaunchBlock,
		AddNew,
		ToastStatus,
		CampaignEditPane,
	},
	data() {
		return {
			sidebarOpen: false,
			selectedBlockId: '',
			blocks: ref([]),
			toastStatus: '',
			toastStatusText: '',
			isLoaded: false,
			dragging: false,
			initialMousePosition: { x: 0, y: 0 },
			initialScrollPosition: { x: 0, y: 0 },
		}
	},
	computed: {
		...mapStores(useCampaignStore, ['campaignStore']),
		campaign() {
			return this.campaignStore.campaign;
		},
		campaignName() {
			return this.campaignStore.campaign.name || 'Untitled Campaign';
		},
		campaignStatus() {
			return this.campaignStore.campaign.status || 'Campaign Not Saved';
		},
		campaignType() {
			return this.campaignStore.campaign.type || CAMPAIGN_TYPES.ActionPrompt.FRIENDLY_NAME;
		},
	},
	methods: {
		startDrag(event) {
			console.log('startDrag');
			this.dragging = true;
			this.initialMousePosition = { x: event.clientX, y: event.clientY };
			this.initialScrollPosition = {
				x: event.target.scrollLeft,
				y: event.target.scrollTop,
			};
			event.target.classList.add('disable-select');
		},
		drag(event) {
			if (!this.dragging) return;
			const deltaX = this.initialMousePosition.x - event.clientX;
			const deltaY = this.initialMousePosition.y - event.clientY;
			event.target.scrollLeft = this.initialScrollPosition.x + deltaX;
			event.target.scrollTop = this.initialScrollPosition.y + deltaY;
		},
		stopDrag(event) {
			this.dragging = false;
			event.target.classList.remove('disable-select');
		},
		getNewBlockId() {
			return v4();
		},
		addChildBlock() {
			this.campaignStore.saveBlockState(this.blocks);
			const newBlock = this.getNewBlock();
			this.campaignStore.addBlock(newBlock);
			this.selectedBlockId = newBlock.props.blockId;

			this.$nextTick(() => {
				const el = document.getElementById('block-container')
				el.scrollTo({
					top: 0,
					left: 42069,
					behavior: 'smooth'
				});
			});
		},
		selectBlock(blockId) {
			this.campaignStore.saveBlockState(this.blocks);
			this.selectedBlockId = blockId;
			this.updatePadding();
		},
		saveBlockState() {
			this.campaignStore.saveBlockState(this.blocks);
		},
		async saveCampaign() {
			this.validateBlocks();

			this.campaignStore.saveBlockState(this.blocks);
			try {
				const newCampaign = await this.campaignStore.saveCampaign();
				console.log(`Campaign Saved: ${JSON.stringify(newCampaign)}`)
				if (newCampaign && newCampaign.id) {
					this.$route.query = { ...this.$route.query, id: newCampaign.id };
					history.replaceState(null, null, '?' + new URLSearchParams(this.$route.query).toString());
					this.setToastStatus('success', 'Campaign Saved');
				} else {
					this.setToastStatus('success', 'Campaign Updated');
				}
			} catch (e) {
				console.log(`Error Saving Campaign: ${e.message}`)
				this.setToastStatus('fail', `Error Saving Campaign: ${e.message}`);
			}
			this.selectedBlockId = null;

		},
		validateBlocks() {
			this.blocks.forEach(block => {
				if (block.v$ && block.v$.$invalid) {
					this.selectedBlockId = null;
					this.setToastStatus('fail', `${block.type} is missing required data.`);
					throw new Error(`${block.type} is missing required data.`);
				}
			})
		},
		getDefaultBlock() {
			return {
				type: 'CampaignBlock',
				props: {
					tooltipText: 'Choose New Audience',
					blockId: this.getNewBlockId(),
					title: 'Untitled Campaign',
				}
			}
		},
		getNewBlock() {
			const lastBlock = this.blocks[this.blocks.length - 1];
			if (lastBlock.supportedChildBlocks.length > 1) {
				const blockType = CAMPAIGN_TYPES[this.campaignType]?.BLOCK;
				return {
					type: blockType,
					props: {
						parentBlockId: lastBlock.blockId,
						blockId: this.getNewBlockId()
					}
				};
			}
			return {
				type: lastBlock.supportedChildBlocks[0],
				props: {
					parentBlockId: lastBlock.blockId,
					tooltipText: 'Add an action',
					blockId: this.getNewBlockId()
				}
			};
		},
		setToastStatus(result, text) {
			this.toastStatus = result;
			this.toastStatusText = text;
		},
		clearToastStatus() {
			this.toastStatus = '';
		},
		updatePadding() {
			let width = 600;
			const editPane = document.getElementById('campaign-edit-pane');
			if (editPane) {
				width = editPane.offsetWidth + 100;
			}
			document.getElementById('block-container').style.paddingRight = `${width}px`;
			this.$nextTick(() => {
				const el = document.getElementById('block-container')
				el.scrollTo({
					top: 0,
					left: 42069,
					behavior: 'smooth'
				});
			});
		},
	},
	async mounted() {
		if (this.$route.query.id) {
			await this.campaignStore.loadCampaign(this.$route.query.id);
			this.selectedBlockId = this.campaignStore.blocks[0].props.blockId;
		} else {
			this.campaignStore.loadRewardTypes().catch();
			this.campaignStore.loadGoalTypes().catch();
			const defaultBlock = this.getDefaultBlock();
			this.campaignStore.addBlock(defaultBlock);
			this.selectedBlockId = defaultBlock.props.blockId;
		}
		this.$nextTick(() => {
			this.updatePadding();
		});
		this.isLoaded = true;
	},
	unmounted() {
		this.campaignStore.$reset();
	},
}

</script>
