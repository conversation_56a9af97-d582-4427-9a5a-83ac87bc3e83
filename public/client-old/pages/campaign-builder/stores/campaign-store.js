import { defineStore } from 'pinia'
import {
	fetchCampaign,
	createCampaign,
	updateCampaign,
	fetchRewardTypes,
	fetchGoalTypes,
	deleteGoals,
} from '../campaign-service'
import { campaignToBlocksTransformer } from '../transformers/campaign-transformer'
import {
	blockToCampaign,
	blockToSegment,
	blockToContent,
	blockToGoals,
	blockToReward,
	blockToCampaignLaunch
} from '../transformers/block-transformer'

export const useCampaignStore = defineStore('campaign', {
	state: () => {
		return {
			campaign: {},
			rewardTypes: [],
			goalTypes: [],
			goalsToRemove: [],
			emptySegmentBlockId: '',
		}
	},
	getters: {
		blocks(state) {
			return campaignToBlocksTransformer(
				state.campaign,
				state.goalTypes,
				this.rewardTypes,
				this.emptySegmentBlockId
			);
		},
	},
	actions: {
		async loadRewardTypes() {
			this.rewardTypes = await fetchRewardTypes();
		},
		async loadGoalTypes() {
			this.goalTypes = await fetchGoalTypes();
		},
		async loadCampaign(campaignId) {
			await this.loadRewardTypes();
			await this.loadGoalTypes();
			this.campaign = await fetchCampaign(campaignId);
		},
		async saveCampaign() {
			const campaignToSave = JSON.parse(JSON.stringify(this.campaign));
			let response;
			if (campaignToSave.id) {
				response = await updateCampaign(campaignToSave);
			} else {
				delete campaignToSave.segments;
				response = await createCampaign(campaignToSave, this.campaign.segments[0].id);
			}
			if (this.goalsToRemove.length) {
				await deleteGoals(this.goalsToRemove);
				this.goalsToRemove = [];
			}
			this.mapResponse(response);

			return response;
		},
		mapResponse(response) {
			this.campaign = {
				...response,
				segments: [...response.segments],
				quests: [...response.quests],
			}

			if (response.quests[0].content) {
				this.campaign.quests[0].content = {
					...response.quests[0].content
				}
			}
			if (response.quests[0].goals) {
				this.campaign.quests[0].goals = [...response.quests[0].goals]
			}
			if (response.quests[0].rewards) {
				this.campaign.quests[0].rewards = {
					...response.quests[0].rewards
				}
			}
		},
		addBlock(block) {
			switch (block.type) {
				case 'CampaignBlock':
					this.addCampaignBlock(block);
					break;
				case 'AudienceBlock':
					this.addAudienceBlock(block);
					break;
				case 'ActionPromptBlock':
					this.addActionPromptBlock(block);
					break;
				case 'ActionBlock':
					this.addActionBlock(block);
					break;
				case 'RewardBlock':
					this.addRewardBlock(block);
					break;
				case 'LaunchBlock':
					this.addLaunchBlock(block);
				default:
					break;
			}
		},
		saveBlockState(allBlocks) {
			allBlocks.forEach(block => {
				if (block && !block.props) {
					block.props = block.$props;
				}
				if (block) {
					this.addBlock(block)
				}
			});
		},
		addCampaignBlock(block) {
			const mappedCampaign = blockToCampaign(block);
			this.campaign = {
				...this.campaign,
				...mappedCampaign,
			};
		},
		addAudienceBlock(block) {
			const mappedSegment = blockToSegment(block);
			if (this.campaign.segments && this.campaign.segments.length) {
				this.campaign.segments[0] = {
					...this.campaign.segments[0],
					...mappedSegment,
				};
			} else {
				this.campaign.segments = [mappedSegment];
				if (!this.emptySegmentBlockId) {
					this.emptySegmentBlockId = mappedSegment.externalId;
				}
			}

		},
		addActionPromptBlock(block) {
			const mappedContent = blockToContent(block);
			if (this.campaign.quests && this.campaign.quests.length) {
				this.campaign.quests = [{
					...this.campaign.quests[0],
					content: {
						...this.campaign.quests[0].content,
						...mappedContent,
					},
					name: this.campaign.name
				}];
			} else {
				this.campaign.quests = [{
					name: this.campaign.name,
					content: mappedContent
				}];
			}
		},
		addActionBlock(block) {
			const mappedGoalBlock = blockToGoals(block);
			const goals = mappedGoalBlock.goals;

			if (this.campaign.quests && this.campaign.quests.length) {
				const newGoals = [];
				for (const goal of goals) {
					const matchingGoal = this.campaign.quests[0].goals?.find(x => x.externalId === goal.externalId);
					const matchinGoalIndex = this.campaign.quests[0].goals?.findIndex(x => x.externalId === goal.externalId);
					if (!goal.id) {
						goal.id = matchingGoal?.id;
					}
					if (!goal.content?.id) {
						goal.content.id = matchingGoal?.content?.id;
						goal.content.goalId = matchingGoal?.content?.goalId;
					}

					let newGoal = goal;
					if (this.campaign.quests[0].goals[matchinGoalIndex]) {
						newGoal = {
							...this.campaign.quests[0].goals[matchinGoalIndex],
							...goal,
						};
					}
					newGoals.push(newGoal);
				}
				this.campaign.quests = [{
					...this.campaign.quests[0],
					goals: newGoals,
					name: this.campaign.name
				}];
			} else {
				this.campaign.quests = [{
					name: this.campaign.name,
					goals: [mappedGoalBlock],
				}];
			}
		},
		addRewardBlock(block) {
			const mappedReward = blockToReward(block, this.rewardTypes);
			if (this.campaign.quests && this.campaign.quests.length) {
				this.campaign.quests = [{
					...this.campaign.quests[0],
					rewards: [mappedReward],
					name: this.campaign.name
				}];
			} else {
				this.campaign.quests = [{
					name: this.campaign.name,
					rewards: [mappedReward]
				}];
			}
		},
		addLaunchBlock(block) {
			const mappedLaunch = blockToCampaignLaunch(block);
			this.campaign = {
				...this.campaign,
				...mappedLaunch,
			};
		},
	},
})






