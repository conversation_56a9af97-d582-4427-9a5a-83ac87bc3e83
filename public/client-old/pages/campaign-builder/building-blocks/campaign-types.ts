export enum CAMPAIGN_ACTION_BLOCKS {
	ActionPrompt = 'ActionPromptBlock',
	NativeQuest = 'ActionBlock',
}

export interface CampaignType {
	FRIENDLY_NAME: string;
	NAME: string;
	BLOCK: CAMPAIGN_ACTION_BLOCKS;
}

export type CampaignTypes = {
	[key in string]: CampaignType;
};

export const CAMPAIGN_TYPES: CampaignTypes = {
	ActionPrompt: {
		FRIENDLY_NAME: '🎬 Action Prompt',
		NAME: 'ActionPrompt',
		BLOCK: CAMPAIGN_ACTION_BLOCKS.ActionPrompt,
	},
	NativeQuest: {
		FRIENDLY_NAME: '⚔️ Embedded Quest',
		NAME: 'NativeQuest',
		BLOCK: CAMPAIGN_ACTION_BLOCKS.NativeQuest,
	},
}
