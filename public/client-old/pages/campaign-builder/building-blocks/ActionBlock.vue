<template>
	<div v-if="!hidden">
		<BaseBlock
			:id="blockId"
			title="Action"
			:subtitle="actionSubtitle || '&nbsp;'"
			:block-id="blockId"
			:parent-block-id="parentBlockId"
			:selected-block-id="selectedBlockId"
			:required-inputs-missing="missingRequiredInputs"
			@block-selected="$emit('blockSelected')"
		>
			<img src="../icons/bolt.svg" />
		</BaseBlock>
	</div>

	<CampaignEditPane
		:is-open="selectedBlockId == blockId"
		title="Configure Actions"
		:fixed="fixed"
		@close-edit-pane="$emit('closeEditPane')">
		<div class="mt-4" v-for="(action, i) of this.actions">
			<label class="block text-sm font-medium mb-1" for="whenAction">
				<span v-if="i == 0">When</span>
				<span v-if="i > 0">and When</span>
				<span class="text-rose-500 ml-1">*</span>
				<span v-if="i > 0" class="float-right">
					<img src="../icons/close.svg" class="cursor-pointer" @click="removeAction(i)" />
				</span>
			</label>
			<DropdownSegmentMenu
				class="relative inline-flex w-full mb-4"
				:title="action.selectedAction ? action.selectedAction.friendlyName : 'Select an Action'">
				<li
					v-for="a of availableActions"
					class="flex items-center text-sm w-full hover:bg-ralprimary-highlighted py-1 px-3 cursor-pointer"
					@click="actionSelected(action, a)">
					{{a.friendlyName}}
				</li>
			</DropdownSegmentMenu>

			<template v-for="input of actionInputData(action)">
				<label class="block text-sm font-medium mb-1">
					{{ input.friendlyName }}
					<span class="text-rose-500">*</span>
				</label>
				<input
					type="text"
					class="form-input w-full mb-4"
					:placeholder="`Enter ${input.friendlyName}`"
					v-model="input.value"
					@input="inputUpdated"
				/>
			</template>

		</div>
		<div
			v-if="actions[0]?.selectedAction && actions.length < 3"
			class="text-ralbutton-primary-light-active-active cursor-pointer text-xs"
			@click="addAction">
			+ Action
		</div>


	</CampaignEditPane>
</template>

<script>
	import BaseBlock from "./BaseBlock.vue";
	import CampaignEditPane from "../CampaignEditPane.vue";
	import DropdownSegmentMenu from "../../../components/DropdownSegmentMenu.vue";
	import { blockProps } from './block-props';
	import { useCampaignStore } from '../stores/campaign-store';
	import { v4 } from 'uuid';

	export default {
		name: 'ActionBlock',
		props: [
			...blockProps,
			'goalData',
			'hidden',
			'fixed'
		],
		emits: [],
		components: {
			BaseBlock,
			CampaignEditPane,
			DropdownSegmentMenu,
		},
		computed: {
			availableActions() {
				return Object.values(this.campaignStore?.goalTypes || {});
			},
			actionSubtitle() {
				return this.actions.map(x => x.selectedAction?.friendlyName).join(' + ');
			}
		},
		setup() {
			const campaignStore = useCampaignStore();
			return { campaignStore };
		},
		data() {
			return {
				type: 'ActionBlock',
				supportedChildBlocks: [
					'RewardBlock',
				],
				actions: [{
					externalId: v4(),
				}],
				missingRequiredInputs: true,
				v$: { $invalid: true }
			}
		},
		methods: {
			addAction() {
				this.actions.push({
					externalId: v4(),
				});
			},
			removeAction(index) {
				if (this.actions[index].id) {
					this.campaignStore.goalsToRemove.push(this.actions[index].id);
				}
				this.actions.splice(index, 1);
				this.inputUpdated();
			},
			actionInputData(action) {
				if (!action.selectedAction) return [];
				let contentInputs = {};
				for (let input in action.selectedAction.contentConfigSchema) {
					if (!action.selectedAction.contentConfigSchema[input].omitFromCB) {
						contentInputs[input] = action.selectedAction.contentConfigSchema[input];
					}
				}

				return {
					...action.selectedAction?.configSchema,
					...contentInputs,
				};
			},

			actionSelected(action, selectedAction) {
				const actionType = this.availableActions.find(a => a.goalType === selectedAction.goalType);
				const actionTypeCopy = JSON.parse(JSON.stringify(actionType));
				action.selectedAction = {
					...actionTypeCopy,
					configSchema: actionTypeCopy.configSchema,
					contentConfigSchema: actionTypeCopy.contentConfigSchema,
				};
				this.inputUpdated();
			},
			inputUpdated() {
				this.validateRequiredInputs();
				this.$emit('blockUpdated', this);
			},
			validateRequiredInputs() {
				for (let action of this.actions) {
					if (!action.selectedAction || JSON.stringify(action.selectedAction) === '{}') {
						this.missingRequiredInputs = true;
						this.v$.$invalid = true;
						return;
					}

					for (let input in action.selectedAction.configSchema) {
						if (!action.selectedAction.configSchema[input].value) {
							this.missingRequiredInputs = true;
							this.v$.$invalid = true;
							return;
						}
					}

					for (let input in action.selectedAction.contentConfigSchema) {
						const inputSchema = action.selectedAction.contentConfigSchema[input];
						if (!inputSchema.value && !inputSchema.omitFromCB) {
							this.missingRequiredInputs = true;
							this.v$.$invalid = true;
							return;
						}
					}
				}
				this.missingRequiredInputs = false;
				this.v$.$invalid = false;
			},
		},
		mounted() {
			if (this.goalData && !this.actions[0]?.selectedAction) {
				this.actions = this.goalData.map(x => {
					if (x.type) {
						const actionType = this.availableActions.find(a => a.goalType === x.type);
						const actionTypeCopy = JSON.parse(JSON.stringify(actionType));
						const action = {
							...x,
							selectedAction: {
								...actionTypeCopy,
								configSchema: x.requiredData,
								contentConfigSchema: actionTypeCopy.contentConfigSchema,
							}
						}

						for (let prop in action.selectedAction.contentConfigSchema) {
							for (let contentProp in x.content) {
								if (contentProp == prop) {
									action.selectedAction.contentConfigSchema[prop].value = x.content[contentProp];
								}
							}
						}
						return action;
					}
					return x;
				});
			}
			this.validateRequiredInputs();

			if (this.hidden) {
				this.inputUpdated();
			}
		}

	}
</script>
