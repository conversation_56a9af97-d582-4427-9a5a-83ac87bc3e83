<template>
	<BaseBlock
		:id="blockId"
		:title="title"
		:subtitle="subtitle || '&nbsp;'"
		:block-id="blockId"
		:parent-block-id="parentBlockId"
		:selected-block-id="selectedBlockId"
		:required-inputs-missing="missingRequiredInputs"
		@block-selected="$emit('blockSelected')"
	>
		<img src="../icons/bolt.svg" />
	</BaseBlock>

	<CampaignEditPane
		:is-open="selectedBlockId == blockId"
		:title="title"
		@close-edit-pane="$emit('closeEditPane')">
		<div class="flex flex-col mt-4">
			<CampaignPromptPreview
				:title="selectedPromptTitle || 'Title'"
				:image-url="selectedPromptImageUrl || ''"
				:subtitle="selectedPromptSubtitle || 'Subtitle'"
				:button-text="selectedPromptButtonText || 'Button Text'"
				:cancel-text="selectedPromptCloseMessage || 'Cancel Message'"
				:url="selectedPromptButtonUrl || 'https://www.google.com'"
				:css="selectedPromptCss || {}"
				@css-updated="(data) => promptCss = data"
				@block-updated="$emit('blockUpdated')"
			/>

			<label class="block text-sm font-medium mb-1" for="promptTitle">
				Title
				<span class="text-rose-500">*</span>
			</label>
			<input
				id="promptTitle"
				type="text"
				class="form-input w-full mb-4"
				placeholder="Enter a title"
				v-model="v$.selectedPromptTitle.$model"
			/>
			<div class="input-errors" v-for="error of v$.selectedPromptTitle.$errors" :key="error.$uid">
				<div class="error-msg text-center text-sm font-small text-rose-400 pt-2">{{ error.$message }}</div>
			</div>

			<label class="block text-sm font-medium mb-1" for="promptImageUrl">Image URL</label>
			<input
				id="promptImageUrl"
				type="url"
				class="form-input w-full mb-4"
				placeholder="Optionally add an image URL"
				v-model="selectedPromptImageUrl"
			/>

			<label class="block text-sm font-medium mb-1" for="promptSubtitle">Subtitle</label>
			<input
				id="promptSubtitle"
				type="text"
				class="form-input w-full mb-4"
				placeholder="Enter a subtitle"
				v-model="selectedPromptSubtitle"
			/>

			<label class="block text-sm font-medium mb-1" for="promptButtonText">
				Button Text
				<span class="text-rose-500">*</span>
			</label>
			<input
				id="promptButtonText"
				type="text"
				class="form-input w-full mb-4"
				placeholder="Enter text for the button"
				v-model="v$.selectedPromptButtonText.$model"
			/>
			<div class="input-errors" v-for="error of v$.selectedPromptButtonText.$errors" :key="error.$uid">
				<div class="error-msg text-center text-sm font-small text-rose-400 pt-2">{{ error.$message }}</div>
			</div>

			<label class="block text-sm font-medium mb-1" for="promptUrl">
				Button URL
				<span class="text-rose-500">*</span>
			</label>
			<input
				id="promptUrl"
				type="text"
				class="form-input w-full mb-4"
				placeholder="Enter a URL for the button"
				v-model="v$.selectedPromptButtonUrl.$model"
			/>
			<div class="input-errors" v-for="error of v$.selectedPromptButtonUrl.$errors" :key="error.$uid">
				<div class="error-msg text-center text-sm font-small text-rose-400 pt-2">{{ error.$message }}</div>
			</div>

			<label class="block text-sm font-medium mb-1" for="promptCloseMessage">
				Close Message
				<span class="text-rose-500">*</span>
			</label>
			<input
				id="promptCloseMessage"
				type="text"
				class="form-input w-full mb-4"
				placeholder="Enter text for the cancel message"
				v-model="v$.selectedPromptCloseMessage.$model"
			/>
			<div class="input-errors" v-for="error of v$.selectedPromptCloseMessage.$errors" :key="error.$uid">
				<div class="error-msg text-center text-sm font-small text-rose-400 pt-2">{{ error.$message }}</div>
			</div>

		</div>
	</CampaignEditPane>

</template>

<script>
	import { ref } from 'vue';
	import BaseBlock from "./BaseBlock.vue";
	import CampaignEditPane from "../CampaignEditPane.vue";
	import CampaignPromptPreview from "../prompt-preview/CampaignPromptPreview.vue";
	import { useCampaignStore } from '../stores/campaign-store';
	import { useVuelidate } from '@vuelidate/core'
	import { required, helpers } from '@vuelidate/validators'
	import { blockProps } from './block-props';
	import { CAMPAIGN_TYPES } from './campaign-types';

	export default {
		name: 'ActionPromptBlock',
		props: [
			...blockProps,
			'promptTitle',
			'promptImageUrl',
			'promptSubtitle',
			'promptButtonText',
			'promptCloseMessage',
			'promptUrl',
			'promptCss',
		],
		emits: [],
		components: {
			BaseBlock,
			CampaignEditPane,
			CampaignPromptPreview,
		},
		computed: {
			title() {
				return this.actionPromptTitle;
			},
			missingRequiredInputs() {
				return !this.selectedPromptTitle ||
					!this.selectedPromptButtonText ||
					!this.selectedPromptCloseMessage ||
					!this.selectedPromptButtonUrl;
			},
		},
		setup() {
			const campaignStore = useCampaignStore();
			const actionPromptTitle = ref(
				CAMPAIGN_TYPES[campaignStore.campaign.type]?.FRIENDLY_NAME || 'Action Prompt'
			);

			return {
				campaignStore,
				actionPromptTitle,
			}
		},
		data() {
			return {
				type: 'ActionPromptBlock',
				supportedChildBlocks: ['LaunchBlock'],
				selectedPromptTitle: this.promptTitle || '',
				selectedPromptImageUrl: this.promptImageUrl || '',
				selectedPromptSubtitle: this.promptSubtitle || '',
				selectedPromptButtonText: this.promptButtonText || '',
				selectedPromptCloseMessage: this.promptCloseMessage || '',
				selectedPromptButtonUrl: this.promptUrl || '',
				selectedPromptCss: this.promptCss || {},
				v$: useVuelidate(),
			}
		},
		validations() {
			return {
				selectedPromptTitle: {
					required: helpers.withMessage('Title is required', required)
				},
				selectedPromptButtonText: {
					required: helpers.withMessage('Button text is required', required)
				},
				selectedPromptCloseMessage: {
					required: helpers.withMessage('Close message is required', required)
				},
				selectedPromptButtonUrl: {
					required: helpers.withMessage('Button URL is required', required)
				},
			}
		},
	}
</script>
