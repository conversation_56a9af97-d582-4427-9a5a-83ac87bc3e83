<template>
	<div v-if="!hidden">
		<BaseBlock
			:id="blockId"
			:title="rewardTitle || 'Reward'"
			:subtitle="rewardSubtitle || '&nbsp;'"
			:block-id="blockId"
			:parent-block-id="parentBlockId"
			:selected-block-id="selectedBlockId"
			:required-inputs-missing="missingRequiredInputs"
			@block-selected="$emit('blockSelected')"
		>
			<img src="../icons/star.svg" />
		</BaseBlock>
	</div>

	<CampaignEditPane
		:is-open="selectedBlockId == blockId"
		:title="rewardTitle || 'Untitled Reward'"
		:fixed="fixed"
		@close-edit-pane="$emit('closeEditPane')">
		<div class="mt-4">
			<label class="block text-sm font-medium mb-1" for="whenReward">
				When
				<span class="text-rose-500">*</span>
			</label>
			<DropdownSegmentMenu
				class="relative inline-flex w-full mb-4"
				:title="selectedReward.friendlyName || 'Select a Reward'">
				<li
					v-for="reward of availableRewards"
					class="flex items-center text-sm w-full hover:bg-ralprimary-highlighted py-1 px-3 cursor-pointer"
					:style="{ 'font-style': this.isRewardEnabled(reward) ? 'initial' : 'italic', 'color': this.isRewardEnabled(reward) ? 'initial' : 'lightgrey', 'cursor': this.isRewardEnabled(reward) ? 'initial' : 'not-allowed' }"
					@click="this.isRewardEnabled(reward) ? rewardSelected(reward) : undefined">
					{{reward.friendlyName}}
				</li>
				<li v-if="isSelfService" class="pl-4 pt-2 pr-4 pb-2">
					<button
						style="width: 100%"
						id="upgrade-button-reward-block"
						class="btn bg-indigo-500 hover:bg-indigo-600 text-white disabled:bg-indigo-600 disabled:text-slate-400 disabled:cursor-not-allowed disabled"
						@click.stop="requestUpgrade(); $emit('closeEditPane');">
						<span class="hidden xs:block">Upgrade to Unlock More</span>
					</button>
				</li>
			</DropdownSegmentMenu>
		</div>

		<div class="mt-4" v-if="availableTemplates">
			<label class="block text-sm font-medium mb-1" for="whenReward">
				Template
			</label>
			<DropdownSegmentMenu
				class="relative inline-flex w-full mb-4"
				:title="'Select a Template'">
				<li
					v-for="template of availableTemplates"
					class="flex items-center text-sm w-full hover:bg-ralprimary-highlighted py-1 px-3 cursor-pointer"
					@click="applyTemplate(template)">
					{{template[templateKey]}}
				</li>
			</DropdownSegmentMenu>
		</div>
<!--
		<label v-if="availableTemplates" class="block text-sm font-medium mb-1 flex">
			Template
		</label>
		<select v-if="availableTemplates" @change="applyTemplate($event)">
			<option value="" selected="selected">--</option>
			<option v-for="template in availableTemplates" :value="template[templateKey]">{{template[templateKey]}}</option>
		</select> -->


		<template v-for="input of inputData">
			<label class="block text-sm font-medium mb-1 flex">
				{{ input.friendlyName }}
				<span class="text-rose-500" v-if='!input.optional'>*</span>
				<img v-if="input.description" :id="`tooltip-trigger-${input.friendlyName.replace(/\s/g, '')}`"
					src="../../../images/help-circle.svg" class="pl-3 tooltip-trigger" alt="help" />

				<div :id="`tooltip-body-${input.friendlyName?.replace(/\s/g, '')}`" role="tooltip"
					class="tooltip-body" style="">
					{{input.description}}
					<div class="tooltip-arrow" data-popper-arrow></div>
				</div>
			</label>
			<input
				v-if="isInputVisible(input)"
				:type="input.inputType"
				class="form-input w-full mb-4"
				:placeholder="`Enter ${input.friendlyName}`"
				v-model="input.value"
				@input="inputUpdated"
			/>
			<span
				v-if="input.inputType === 'uri' && input.fileUploadEndpoint && !isFileInputVisible(input) && !input.value"
				@click="setFileInputVisible(input, true)"
				style="position: absolute;
						right: 3.5rem;
						margin-top: 8px;
						font-size: 0.8em;
						text-decoration: underline;
						cursor: pointer;"
			>
				Upload a File
			</span>
			<form
				v-if="isFileInputVisible(input)">
				<input
					type="file"
					class="form-input w-full mb-4"
					:placeholder="`Enter ${input.friendlyName}`"
					@change="uploadFile($event, input)"
				/>
			</form>
		</template>
	</CampaignEditPane>

</template>

<script>
	import BaseBlock from "./BaseBlock.vue";
	import CampaignEditPane from "../CampaignEditPane.vue";
	import DropdownSegmentMenu from "../../../components/DropdownSegmentMenu.vue";
	import ModalBasic from "../../../components/ModalBasic.vue";
	import { blockProps } from './block-props';
	import { useCampaignStore } from '../stores/campaign-store';


	import { URL_DOMAIN } from '../../../utils/Utils';

	import { createPopper } from '@popperjs/core';

	export default {
		name: 'RewardBlock',
		props: [
			...blockProps,
			'rewardData',
			'rewardType',
			'hidden',
			'fixed'
		],
		emits: [],
		components: {
			BaseBlock,
			CampaignEditPane,
			DropdownSegmentMenu,
			ModalBasic,
		},
		computed: {
			inputType() {
				this.rewardType.configSchema
			},
			isSelfService() {
				return localStorage.getItem('selfService') == 'true';
			},
			inputData() {
				if (JSON.stringify(this.selectedReward) == '{}' && this.rewardData && this.rewardType) {
					this.selectedReward = this.rewardType;
					const configSchema = this.rewardType.configSchema;
					const configValues = this.rewardData;

					const combined = Object.values(configSchema).map(x => ({...x}));
					for (const configKey of Object.keys(configValues)) {
						const match = combined.find(x => x.configKey === configKey);
						if (!match) {
							combined.push({ configKey, value: configValues[configKey]});
							continue;
						}

						match.value = configValues[configKey];
					}

					return combined;
				}
				if (this.availableRewards.length) {
					const selectedReward = this.availableRewards.find(reward =>
						reward.rewardType === this.selectedReward?.rewardType
					);

					if (selectedReward) {
						const inputs = Object.values(selectedReward.configSchema).map(x => ({...x}));
						const configValues = this.rewardData;

						for (const input of inputs) {
							if (input.value === undefined) {
								input.value = configValues[input.configKey] !== undefined
									? configValues[input.configKey]
									: !input.optional
										? input.defaultValue || input.defaultValueTemplate
										: undefined;
							}
						}

						return inputs;
					}
				}
				return [];
			},
			rewardTitle() {
				return this.selectedReward?.friendlyName;
			},
			availableRewards() {
				return  Object.values(this.campaignStore?.rewardTypes || {});
			},
			availableTemplates() {
				return this.rewardType?.configTemplateKey
					? this.rewardType?.configTemplates
					: undefined;
			},
			templateKey() {
				return this.rewardType?.configTemplateKey;
			}
		},
		data() {
			return {
				type: 'RewardBlock',
				supportedChildBlocks: [
					'LaunchBlock',
				],
				selectedReward: {},
				rewardSubtitle: '',
				missingRequiredInputs: true,
				v$: { $invalid: true },
				showFileInput: {},

			}
		},
		methods: {
			isRewardEnabled(reward) {
				return !this.isSelfService || reward.rewardType === 'award-xp'; //  || reward.rewardType === 'raleon-standard-nft-reward-v1'
			},
			rewardSelected(reward) {
				this.selectedReward = reward;
				this.$emit('rewardSelected', this.selectedReward);
				Object.keys(this.rewardData).forEach(x => this.rewardData[x] = undefined);
				this.inputUpdated();

				this.$nextTick(() => this.setupTooltips());
			},
			inputUpdated() {
				this.validateRequiredInputs();
				this.rewardSubtitle = this.inputData[Object.keys(this.inputData)[0]]?.value;
				this.$emit('blockUpdated', this);
				this.$forceUpdate();
			},
			validateRequiredInputs() {
				if (!this.selectedReward || JSON.stringify(this.selectedReward) === '{}') {
					this.missingRequiredInputs = true;
					this.v$.$invalid = true;
					return;
				}
				const inputs = this.inputData;
				for (let input in inputs) {
					if (inputs[input].optional || inputs[input].omitFromBuilder) {
						continue;
					}

					if (!inputs[input].value) {
						this.missingRequiredInputs = true;
						this.v$.$invalid = true;
						return;
					}
				}
				this.missingRequiredInputs = false;
				this.v$.$invalid = false;
			},
			setupTooltips() {
				const triggers = document.querySelectorAll('.tooltip-trigger');
				triggers.forEach(trigger => {
					const triggerId = trigger.getAttribute('id');
					const tooltipId = triggerId.replace('-trigger-', '-body-');
					const tooltip = document.querySelector(`#${tooltipId}`);

					const popperInstance = createPopper(trigger, tooltip, {
						placement: 'top',
						modifiers: [
							{
								name: 'offset',
								options: {
									offset: [0, 8],
								},
							},
						],
					});

					function show() {
						// Make the tooltip visible
						tooltip.setAttribute('data-show', '');

						// Enable the event listeners
						popperInstance.setOptions((options) => ({
							...options,
							modifiers: [
								...options.modifiers,
								{ name: 'eventListeners', enabled: true },
							],
						}));

						// Update its position
						popperInstance.update();
					}

					function hide() {
						// Hide the tooltip
						tooltip.removeAttribute('data-show');

						// Disable the event listeners
						popperInstance.setOptions((options) => ({
							...options,
							modifiers: [
								...options.modifiers,
								{ name: 'eventListeners', enabled: false },
							],
						}));
					}

					const showEvents = ['mouseenter', 'focus'];
					const hideEvents = ['mouseleave', 'blur'];

					showEvents.forEach((event) => {
						trigger.addEventListener(event, show);
					});

					hideEvents.forEach((event) => {
						trigger.addEventListener(event, hide);
					});
				});
			},

			async requestUpgrade() {
				const response = await fetch(`${URL_DOMAIN}/onboard/self-service/upgrade`, {
					method: 'POST',
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					}
				});

				if (response.ok && response.status >= 200 && response.status < 300) {
					localStorage.setItem('selfServiceUpgradeRequested', true);
				}
			},
			isInputVisible(input) {
				return !this.showFileInput[input.configKey];
			},
			isFileInputVisible(input) {
				return this.showFileInput[input.configKey];
			},
			setFileInputVisible(input, visible) {
				this.showFileInput[input.configKey] = visible;
			},
			async uploadFile(event, input) {
				const file = event.target.files[0];
				if (!input.fileUploadEndpoint) {
					throw new Error('No endpoint to upload file to');
				}

				const endpoint = input.fileUploadEndpoint.startsWith('/')
					? `${URL_DOMAIN}${input.fileUploadEndpoint}`
					: endpoint;

				const formData = new FormData();
				formData.append('file', file);

				const response = await fetch(input.fileUploadEndpoint, {
					method: input.fileUploadMethod || 'POST',
					credentials: 'include',
					headers: {
						Authorization: endpoint.startsWith(URL_DOMAIN) ? `Bearer ${localStorage.token}` : undefined
					},
					body: formData
				});
				const result = await response.text();

				this.setFileInputVisible(input, false);
				input.value = result;
			},
			getTemplateSelectValue(template, key) {
				return template[key];
			},
			applyTemplate(template) { //event) {
				// const templateValue = event?.target?.value;
				// if (!templateValue) {
				// 	return;
				// }

				const match = template; // this.availableTemplates?.find(x => x[this.templateKey] === templateValue);
				if (!match) {
					return;
				}

				this.$emit('templateSelected', match);
				Object.keys(match).forEach(x => {
					const templateValue = match[x];
					this.inputData.find(y => y.configKey === x).value = templateValue;
				});
				this.inputUpdated();
			}
		},
		setup() {
			const campaignStore = useCampaignStore();
			return {
				campaignStore,
			}
		},
		async mounted() {
			this.rewardSubtitle = this.inputData[Object.keys(this.inputData)[0]]?.value || '';
			this.validateRequiredInputs();

			if (this.hidden) {
				this.inputUpdated();
			}
		}

	}
</script>

<style>
.tooltip-body {
	display: none;
}

.tooltip-body[data-show] {
	display: inline;
}

.tooltip-body {
	background: #333;
	color: white;
	font-weight: bold;
	padding: 4px 8px;
	font-size: 13px;
	border-radius: 4px;
	display: none;
}

.tooltip-body[data-show] {
	display: block;
}

.tooltip-arrow,
.tooltip-arrow::before {
	position: absolute;
	width: 8px;
	height: 8px;
	background: inherit;
}

.tooltip-arrow {
	visibility: hidden;
	padding-left: 5px;
}

.tooltip-arrow::before {
	visibility: visible;
	content: '';
	transform: rotate(45deg);
}

.tooltip-body[data-popper-placement^='top']>#arrow {
	bottom: -4px;
}

.tooltip-body[data-popper-placement^='bottom']>#arrow {
	top: -4px;
}

.tooltip-body[data-popper-placement^='left']>#arrow {
	right: -4px;
}

.tooltip-body[data-popper-placement^='right']>#arrow {
	left: -4px;
}
</style>
