# Adding a New Block

### Creating the block
- Create the block file. Copy an existing one.
- Add `extends: BaseBlock` to the component export.
- The `data()` function must include the following in the return:
	- `type` this is the name of your component.
	- `supportedChildBlocks: []` an array of child blocks that can follow this one.
- Use `<BaseBlock>` and pass the necessary props. Be sure to add the block selected event listener `@block-selected="$emit('blockSelected')"`
- Use `<CampaignEditPane>` and include the necessary inputs or elements to show in the edit pane for this block. The elements are slotted into the pane so include them within the `<CampaignEditPane>` tags.


### Transformers
- Update `campaign-store` with an `addBlock` function for the new block.
	- In the `addBlock` function, define how the data should be transformed from the UI into the campaign state property. This property mirrors the database models.
- In `db-to-blocks.js` update `campaignToBlocksTransformer` to add the new block to the block array, in the order it should be added, under the conditions it should be added. This is mapping the database representation of the block to the UI representation of the block.
	- Ensure the parentBlockId is passed in or set on the props.
- When transforming, be sure to set an externalId on the object instance in the store and map it to the blockId. This allows us to keep track of each block and know which block(s) have been updated.


### Update CampaignBuilder
`CampaignBuilder.vue` dynamically renders the blocks based on their types (component names).
- Import the new block in CampaignBuilder and add it to the `components` property.


### Block Form Validation
- Use vuelidate for form validation where applicable. If the form is dynamic, it may not be possible to use vuelidate.
- When it's not possible to use vuelidate
  - Declare a variable in data `v$: { $invalid: true }`
  - Where you do form validation, set the value accordingly `this.v$.$invalid = false`
