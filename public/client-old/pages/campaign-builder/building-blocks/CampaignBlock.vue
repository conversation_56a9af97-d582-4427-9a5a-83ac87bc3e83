<template>
	<BaseBlock
		:id="blockId"
		:title="campaignName || title"
		:subtitle="campaignType.FRIENDLY_NAME || ''"
		:block-id="blockId"
		:parent-block-id="parentBlockId"
		:selected-block-id="selectedBlockId"
		:required-inputs-missing="missingRequiredInputs"
		@block-selected="$emit('blockSelected')"
	>
		<img src="../icons/campaign.svg" />
	</BaseBlock>

	<CampaignEditPane
		title="Campaign Settings"
		:is-open="selectedBlockId == blockId"
		@close-edit-pane="$emit('closeEditPane')">
		<div class="mt-4">
			<label class="block text-sm font-medium mb-1" for="campaignName">
				Campaign Name
				<span class="text-rose-500">*</span>
			</label>
			<input
				id="campaignName"
				type="text"
				class="form-input w-full"
				placeholder="Campaign Name"
				v-model="v$.campaignName.$model"
			/>
			<div class="input-errors" v-for="error of v$.campaignName.$errors" :key="error.$uid">
				<div class="error-msg text-center text-sm font-small text-rose-400 pt-2">{{ error.$message }}</div>
			</div>

			<label class="block text-sm font-medium mt-6 mb-1" for="campaignType">Campaign Type</label>
			<DropdownSegmentMenu
				class="relative inline-flex w-full"
				:title="campaignType.FRIENDLY_NAME || 'Select a Campaign Type'"
			>
				<li
					v-for="type of campaignTypes"
					class="flex items-center text-sm w-full hover:bg-ralprimary-highlighted py-1 px-3 cursor-pointer"
					@click="campaignType = type">
					{{type.FRIENDLY_NAME}}
				</li>
			</DropdownSegmentMenu>

			<label class="block text-sm font-medium mt-6 mb-1" for="campaignDescription">
				Description
			</label>
			<input
				id="campaignDescription"
				type="text"
				class="form-input w-full"
				placeholder="Enter a description"
				v-model="campaignDescription"
			/>

			<label class="block text-sm font-medium mt-6 mb-1" for="campaignCategory">
				Category
			</label>
			<input
				id="campaignCategory"
				type="text"
				class="form-input w-full"
				placeholder="Enter a category"
				v-model="campaignCategory"
			/>

			<label
				class="block text-sm font-medium mt-6 mb-1"
				for="campaignPriority">
				Priority
			</label>
			<input
				id="campaignPriority"
				type="number"
				class="form-input w-full"
				placeholder="Enter a priority (Highest number is highest priority)"
				v-model="campaignPriority"
			/>

			<label
				v-if="this.isNativeQuest"
				class="block text-sm font-medium mt-6 mb-1"
				for="campaignHiddenUntilComplete">
				Hide until completed?
			</label>
			<input
				v-if="this.isNativeQuest"
				id="campaignHiddenUntilComplete"
				type="checkbox"
				class="form-input"
				v-model="campaignHiddenUntilComplete"
			/>
		</div>

	</CampaignEditPane>

</template>

<style scoped>
	.ease-out {
		transition: all 0.3s ease-out;
	}
</style>

<script>
	import BaseBlock from "./BaseBlock.vue";
	import AudienceBlock from "./AudienceBlock.vue";
	import CampaignEditPane from "../CampaignEditPane.vue";
	import DropdownSegmentMenu from "../../../components/DropdownSegmentMenu.vue";
	import { useVuelidate } from '@vuelidate/core'
	import { required, helpers } from '@vuelidate/validators'
	import { blockProps } from "./block-props";
	import { CAMPAIGN_TYPES } from "./campaign-types";

	export default {
		name: 'CampaignBlock',
		props: [
			...blockProps,
			'description',
			'category',
			'priority',
			'hiddenUntilComplete',
			'dbCampaignType',
		],
		emits: [],
		components: {
			BaseBlock,
			AudienceBlock,
			CampaignEditPane,
			DropdownSegmentMenu,
		},
		data() {
			const campaignTypes = [
				CAMPAIGN_TYPES.ActionPrompt,
				// CAMPAIGN_TYPES.NativeQuest,
			];

			return {
				type: 'CampaignBlock',
				campaignName: '',
				campaignDescription: '',
				campaignCategory: '',
				supportedChildBlocks: ['AudienceBlock'],
				v$: useVuelidate(),
				campaignTypes,
				campaignType: this.dbCampaignType || '',
			}
		},
		computed: {
			missingRequiredInputs() {
				return !this.campaignName ||
					JSON.stringify(this.campaignType) === '{}';
			},
			isNativeQuest() {
				return this.campaignType.NAME == CAMPAIGN_TYPES.NativeQuest.NAME;
			},
		},
		watch: {
			campaignName() {
				this.$emit('blockUpdated');
			},
			campaignType() {
				this.$emit('blockUpdated');
			},
			campaignDescription() {
				this.$emit('blockUpdated');
			},
			campaignCategory() {
				this.$emit('blockUpdated');
			},
			campaignPriority() {
				this.$emit('blockUpdated');
			},
			campaignHiddenUntilComplete() {
				this.$emit('blockUpdated');
			},
		},
		methods: {},
		mounted() {
			if (this.title && this.title !== 'Untitled Campaign') {
				this.campaignName = this.title;
			}

			if (this.description) {
				this.campaignDescription = this.description;
			}

			if (this.category) {
				this.campaignCategory = this.category;
			}

			if (this.priority) {
				this.campaignPriority = this.priority;
			}

			if (this.hiddenUntilComplete) {
				this.campaignHiddenUntilComplete = this.hiddenUntilComplete;
			}
		},
		validations() {
			return {
				campaignName: {
					required: helpers.withMessage('Campaign Name is required', required)
				},
				campaignType: {
					required: helpers.withMessage('Campaign Type is required', required)
				}
			}
		}
	}
</script>
