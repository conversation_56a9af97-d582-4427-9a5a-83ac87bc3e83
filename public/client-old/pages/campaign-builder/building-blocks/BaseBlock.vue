<template>
	<div
		v-if="blockId"
		@click.stop="$emit('blockSelected')"
		class="flex box-border border border-ralgray-main bg-white max-h-[120px] min-w-[200px] rounded-2xl p-4 ml-10 cursor-pointer"
		:class="blockOutline">
		<div v-if="title" class="flex flex-col w-full rounded-lg justify-center items-start text-ralblack-primary text-md py-4">
			<div class="flex flex-row w-full">
				<div class="rounded-full bg-ralblack-secondary p-2 mr-2">
					<slot />
				</div>
				<div
					v-if="requiredInputsMissing"
					class="self-start ml-auto text-red-500 text-2xl"
				>*</div>
			</div>
			<div class="font-semibold mt-2">
				{{title}}
			</div>
			<div class="text-ralgray-main font-normal text-xs">
				{{subtitle}}
			</div>
		</div>

			<svg
				v-if="blockId && parentBlockId"
				width="40"
				height="120"
				style="margin-left: -223px;margin-top: -16px"
			>
				<defs>
					<marker :id="`arrow-${blockId}`" markerWidth="13" markerHeight="13" orient="auto" refY="6" refX="2">
						<path d="M2,2 L2,11 L10,6 L2,2" style="fill: rgb(146, 84, 246);"></path>
					</marker>
				</defs>

				<path d="M0,60 L30,60" :style='`stroke: rgb(146, 84, 246);stroke-width: 1.25px;fill: none;marker-end: url(&quot;#arrow-${blockId}&quot;);`'></path>
			</svg>

	</div>
</template>

<script>

	import Sidebar from "../../../partials/Sidebar.vue";
	import AddNew from "../AddNew.vue";
	import { blockProps } from './block-props';

	export default {
		name: 'BaseBlock',
		props: [...blockProps],
		emits: ['blockSelected', 'blockUpdated', 'closeEditPane', 'saveCampaign', 'cancelSave'],
		components: {
			Sidebar,
			AddNew,
		},
		data() {
			return {

				sidebarOpen: false,
				childBlocks: [],
				line: {},
				lineElement: {},
				showArrow: false
			}
				},
		computed: {
			blockOutline() {
				if (this.blockId && this.blockId === this.selectedBlockId) {
					return 'outline outline-offset outline-2 outline-ralprimary-light'
				}
			}
		},
		methods: {
			getParentBlock() {
				return this.parentBlock;
			},
			editBlock() {
				this.$emit('editBlock', this);
			},
		}
	}
</script>
