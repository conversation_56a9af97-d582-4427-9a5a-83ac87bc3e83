<template>
	<BaseBlock
		:id="blockId"
		:title="title"
		:subtitle="subtitle || '&nbsp;'"
		:block-id="blockId"
		:parent-block-id="parentBlockId"
		:selected-block-id="selectedBlockId"
		:required-inputs-missing="missingRequiredInputs"
		@block-selected="$emit('blockSelected')"
		class="mr-8"
	>
		<img src="../icons/rocket-launch.svg" />
	</BaseBlock>

	<CampaignEditPane
		:is-open="selectedBlockId == blockId"
		title="Launch Configuration"
		@close-edit-pane="editPaneClosed">
		<div class="flex flex-col">
			<label class="block text-sm font-medium mb-1" for="startDate">
				Start Date
				<span class="text-rose-500">*</span>
			</label>
			<input
				id="startDate"
				type="date"
				class="form-input w-full mb-4"
				placeholder="Enter a start date"
				v-model="v$.selectedStartDate.$model"
			/>
			<div class="input-errors" v-for="error of v$.selectedStartDate.$errors" :key="error.$uid">
				<div class="error-msg text-center text-sm font-small text-rose-400 pt-2">{{ error.$message }}</div>
			</div>

			<label class="block text-sm font-medium mb-1" for="endDate">
				End Date
				<span class="text-rose-500">*</span>
			</label>
			<input
				id="endDate"
				type="date"
				class="form-input w-full mb-4"
				placeholder="Enter an end date"
				v-model="v$.selectedEndDate.$model"
			/>
			<div class="input-errors" v-for="error of v$.selectedEndDate.$errors" :key="error.$uid">
				<div class="error-msg text-center text-sm font-small text-rose-400 pt-2">{{ error.$message }}</div>
			</div>

			<button
				class="mt-4 btn-lg [&:not(:disabled)]:bg-gradient-button-primary text-white font-bold text-xs py-2 px-4 rounded-button disabled:bg-slate-200 disabled:text-slate-400 disabled:cursor-not-allowed"
				@click="saveCampaign"
				:disabled="isSaving">
				<svg v-if="isSaving" class="animate-spin w-4 h-4 fill-current shrink-0 mr-2" viewBox="0 0 16 16">
					<path d="M8 16a7.928 7.928 0 01-3.428-.77l.857-1.807A6.006 6.006 0 0014 8c0-3.309-2.691-6-6-6a6.006 6.006 0 00-5.422 8.572l-1.806.859A7.929 7.929 0 010 8c0-4.411 3.589-8 8-8s8 3.589 8 8-3.589 8-8 8z" />
				</svg>
				Save
			</button>

			<button
				class="mt-4 btn-lg bg-white border-ralgray-main text-ralgray-main font-bold text-xs py-2 px-4 rounded-button"
				@click="$emit('cancelSave')">
				Cancel
			</button>
		</div>
	</CampaignEditPane>
</template>

<script>
	import BaseBlock from "./BaseBlock.vue";
	import AddNew from "../AddNew.vue";
	import CampaignEditPane from "../CampaignEditPane.vue";
	import { useVuelidate } from '@vuelidate/core'
	import { required, helpers } from '@vuelidate/validators'
	import { blockProps } from "./block-props";

	export default {
		name: 'LaunchBlock',
		props: [...blockProps],
		emits: ['saveCampaign', 'cancelSave'],
		components: {
			AddNew,
			BaseBlock,
			CampaignEditPane
		},
		computed: {
			missingRequiredInputs() {
				return this.v$.selectedStartDate.$invalid || this.v$.selectedEndDate.$invalid;
			}
		},
		data() {
			return {
				type: 'LaunchBlock',
				selectedStartDate: this.startDate,
				selectedEndDate: this.endDate,
				isSaving: false,
				supportedChildBlocks: [],
				v$: useVuelidate(),
			}
		},
		methods: {
			async saveCampaign() {
				this.isSaving = true;
				await this.$nextTick();
				this.$emit('saveCampaign');
				setTimeout(() => {
					this.isSaving = false;
				}, 500);
			},
			editPaneClosed() {
				this.$emit('closeEditPane');
				this.isSaving = false;
			}
		},
		watch: {
			selectedStartDate() {
				this.$emit('blockUpdated');
			},
			selectedEndDate() {
				this.$emit('blockUpdated');
			},
		},
		validations() {
		return {
			selectedStartDate: {
				required: helpers.withMessage('Start date is required', required),
			},
			selectedEndDate: {
				required: helpers.withMessage('End date is required', required),
			},
		}
	},
	}
</script>
