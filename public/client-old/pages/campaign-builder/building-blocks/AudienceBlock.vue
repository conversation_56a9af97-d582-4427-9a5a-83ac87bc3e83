<template>
	<BaseBlock
		:id="blockId"
		:title="selectedSegment.name || 'Audience'"
		:subtitle="addressCount || subtitle || 'Select an Audience'"
		:block-id="blockId"
		:parent-block-id="parentBlockId"
		:selected-block-id="selectedBlockId"
		:required-inputs-missing="!selectedSegment || !selectedSegment.id"
		@block-selected="$emit('blockSelected')"
	>
		<img src="../icons/person.svg" />
	</BaseBlock>

	<CampaignEditPane
		:is-open="selectedBlockId == blockId"
		title="Manage Audience"
		@close-edit-pane="$emit('closeEditPane')">
		<div class="mt-4">
			<label class="block text-sm font-medium mb-1" for="audience">
				Audience
				<span class="text-rose-500">*</span>
			</label>
			<DropdownSegmentMenu
				class="relative inline-flex w-full"
				:title="selectedSegment.name || 'Select an Audience'">
				<li
					v-for="segment of segments"
					class="flex items-center text-sm w-full hover:bg-ralprimary-highlighted py-1 px-3 cursor-pointer"
					:key="segment.id"
					@click="setSelectedSegment(segment)">
					{{segment.name}}
				</li>
			</DropdownSegmentMenu>
			<div class="input-errors" v-for="error of v$.selectedSegment.$errors" :key="error.$uid">
				<div class="error-msg text-center text-sm font-small text-rose-400 pt-2">{{ error.$message }}</div>
			</div>
		</div>
	</CampaignEditPane>
</template>

<script>
	import BaseBlock from "./BaseBlock.vue";
	import CampaignEditPane from "../CampaignEditPane.vue";
	import DropdownSegmentMenu from "../../../components/DropdownSegmentMenu.vue";
	import { getPublishedSegments } from '../../../services/segmentbuilder'
	import { useVuelidate } from '@vuelidate/core'
	import { required, helpers } from '@vuelidate/validators'
	import { useCampaignStore } from '../stores/campaign-store';
	import { blockProps } from './block-props'
	import { CAMPAIGN_TYPES } from "./campaign-types";

	export default {
		name: 'AudienceBlock',
		props: [...blockProps],
		emits: [],
		components: {
			BaseBlock,
			CampaignEditPane,
			DropdownSegmentMenu,
		},
		computed: {
			addressCount() {
				if (this.selectedSegment?.id === -1) {
					return 'All Users';
				}
				const count = this.segments.find(segment => segment.name === this.selectedSegment.name)?.addressCount;
				return count ? `${count.toLocaleString()} users` : '';
			},
			tooltipText() {
				switch (this.campaignStore.campaign?.type) {
					case CAMPAIGN_TYPES.ActionPrompt.NAME:
						return 'Add an Action Prompt';
					case CAMPAIGN_TYPES.NativeQuest.NAME:
						return 'Add a Quest Action';
					default:
						break;
				}
			},
		},
		setup() {
			const campaignStore = useCampaignStore();
			return {
				campaignStore,
			}
		},
		data() {
			return {
				type: 'AudienceBlock',
				segments: [],
				selectedSegment: {},
				supportedChildBlocks: [
					'ActionPromptBlock',
					'ActionBlock'
				],
				v$: useVuelidate(),
			}
		},
		methods: {
			setSelectedSegment(segment) {
				this.selectedSegment = {
					id: segment.id,
					name: segment.name,
				};
				this.$emit('blockUpdated');
			},
		},
		async mounted() {
			const segments = await getPublishedSegments();
			this.segments = segments;
			const emptyAudience = {
				id: -1,
				name: 'Everyone',
			};
			this.segments.splice(0, 0, emptyAudience);

			if (this.title) {
				const matchingSegment = this.segments.find(segment => segment.name === this.title);
				if (matchingSegment) {
					 this.selectedSegment = {
						 id: matchingSegment.id,
						 name: matchingSegment.name,
					 };
				} else if (this.campaignStore.emptySegmentBlockId) {
					this.selectedSegment = emptyAudience;
				}
			}
		},
		validations() {
			return {
				selectedSegment: {
					required: helpers.withMessage('Audience is required', required)
				}
			}
		}
	}
</script>
