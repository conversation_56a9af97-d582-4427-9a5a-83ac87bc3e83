import { URL_DOMAIN } from '../../utils/Utils';

export async function fetchCampaign(campaignId) {
	let response = await fetch(`${URL_DOMAIN}/campaigns/${campaignId}`, {
		method: 'GET',
		withCredentials: true,
		credentials: 'omit',
		headers: {
			'Authorization': `Bearer ${localStorage.getItem('token')}`,
			'Access-Control-Allow-Origin': '*',
			'Content-Type': 'application/json'
		}
	});
	return await response.json();
}

export async function createCampaign(campaignData, segmentId) {
	if (!segmentId) {
		throw new Error('Audience is required. Please be sure to select an Audience.');
	}
	let response = await fetch(`${URL_DOMAIN}/campaigns/${segmentId}`, {
		method: 'POST',
		withCredentials: true,
		credentials: 'omit',
		headers: {
			'Authorization': `Bearer ${localStorage.getItem('token')}`,
			'Access-Control-Allow-Origin': '*',
			'Content-Type': 'application/json'
		},
		body: JSON.stringify(campaignData)
	});
	return await response.json();
}

export async function updateCampaign(campaignData) {
	const response = await fetch(`${URL_DOMAIN}/campaigns/${campaignData.id}`, {
		method: 'PATCH',
		withCredentials: true,
		credentials: 'omit',
		headers: {
			'Authorization': `Bearer ${localStorage.getItem('token')}`,
			'Access-Control-Allow-Origin': '*',
			'Content-Type': 'application/json'
		},
		body: JSON.stringify(campaignData)
	});
	return await response.json();
}

export async function fetchRewardTypes() {
	let response = await fetch(`${URL_DOMAIN}/rewards/types/all`, {
		method: 'GET',
		withCredentials: true,
		credentials: 'omit',
		headers: {
			'Authorization': `Bearer ${localStorage.getItem('token')}`,
			'Access-Control-Allow-Origin': '*',
			'Content-Type': 'application/json'
		}
	});
	return await response.json();
}

export async function fetchGoalTypes() {
	let response = await fetch(`${URL_DOMAIN}/goals/types/all`, {
		method: 'GET',
		withCredentials: true,
		credentials: 'omit',
		headers: {
			'Authorization': `Bearer ${localStorage.getItem('token')}`,
			'Access-Control-Allow-Origin': '*',
			'Content-Type': 'application/json'
		}
	});
	return await response.json();
}

export async function deleteGoals(goalIds) {
	await Promise.all(
		goalIds?.map(async (goalId) => {
			let repsonse = await fetch(`${URL_DOMAIN}/goals/${goalId}`, {
				method: 'DELETE',
				withCredentials: true,
				credentials: 'omit',
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Access-Control-Allow-Origin': '*',
					'Content-Type': 'application/json'
				}
			});
			return await repsonse.text();
		})
	);
}
