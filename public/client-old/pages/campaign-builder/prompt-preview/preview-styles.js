export const previewStyles = {
	container: {
		"display": "flex",
		"flex-direction": "column",
		"font-family": "Trebuchet MS",
		"align-items": "center",
		"gap": "0.5rem",
		"border-radius": "30px",
		"padding": "16px",
		"min-width": "300px",
		"width": "fit-content",
		"max-width": "380px",
		"height": "fit-content",
		"maxHeight": "300px",
		"color": "white",
		"background-color": "rgb(30,19,50)",
		"background-image": "radial-gradient(rgba(255, 255, 255, 0.05) 10%, transparent 20%)",
		"background-position": "0 0, 50px 50px",
		"background-size": "20px 20px"
	},
	header: {
		"font-size": "18px",
		"font-weight": "600"
	},
	headerImage: {
		"text-align": "center",
		"margin-bottom": "0",
		"font-size": "18px",
		"max-height": "9em",
	},
	message: {
		"text-align": "center",
		"font-size": "15px",
		"font-weight": "400",
		"margin-bottom": "0.5em"
	},
	button: {
		"min-width": "100%",
		"font-size": "18px",
		"border-radius": "0.3em",
		"border": "none",
		"height": "48px",
		"background": "linear-gradient(261.82deg, rgb(90, 22, 201) -5.43%, rgb(42, 38, 63) 109.81%)",
		"cursor": "pointer"
	},
	closeMessage: {
		"font-size": "14px",
		"cursor": "pointer",
		"text-align": "center"
	}
}
