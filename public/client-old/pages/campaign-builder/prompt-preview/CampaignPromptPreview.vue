<template>
	<span class="mb-4 flex justify-center" v-html="template"></span>
	<span class="flex">
		<label class="block text-sm font-medium mt-2" for="colorPicker">Choose Color</label>
		<img class="ml-1 mt-2" src="../icons/palette.svg" />
	</span>
	<input
		id="colorPicker"
		type="color"
		class="bg-white w-full mb-2 rounded-full cursor-pointer"
		v-model="colors"
	/>

	<label class="block text-sm font-medium mt-2 mb-1" for="colorAdjustment">Color Adjustment For</label>
	<DropdownSegmentMenu
		class="relative inline-flex w-full mb-4"
		:title="colorAdjustment"
	>
		<li
			v-for="selection in colorAdjustmentSelections"
			class="flex items-center text-sm w-full hover:bg-ralprimary-highlighted py-1 px-3 cursor-pointer"
			@click="colorAdjustment = selection">
			{{selection}}
		</li>
	</DropdownSegmentMenu>
</template>

<script>

	import { QuestTemplateBuilder } from '../../../../../src/quests/quest-template-builder'
	import DropdownSegmentMenu from "../../../components/DropdownSegmentMenu.vue";
	import { previewStyles } from './preview-styles'

	export default {
		name: 'CampaignPromptPreview',
		props: ['title', 'subtitle', 'imageUrl', 'buttonText', 'cancelText', 'url', 'css'],
		emits: ['cssUpdated', 'blockUpdated'],
		components: {
			DropdownSegmentMenu,
		},
		data() {
			const defaultColor = (this.css && this.css.container && this.css.container['background-color'])
				? this.css.container['background-color']
				: '#1E1332';
			return {
				template: '',
				colors: defaultColor,
				colorAdjustment: 'Background',
				defaultCss: previewStyles,
				colorAdjustmentSelections: [
					'Background',
					'Foreground/All Text',
					'Title',
					'Subtitle',
					'Button Text',
					'Button Background',
					'Cancel Message',
				]
			}
		},
		computed: {},
		methods: {
			generateTemplate() {
				const content = {
					header: this.title,
					headerImageUrl: this.imageUrl,
					message: this.subtitle,
					buttonText: this.buttonText,
					closeMessage: this.cancelText,
					url: this.url,
					css: this.css,
				}
				this.template = QuestTemplateBuilder.buildTemplateWithoutClickActions(content);
			},
			updateColors() {
				const newColor = this.colors.hex || this.colors;
				switch (this.colorAdjustment) {
					case 'Background':
						this.css.container['background-color'] = newColor;
						break;
					case 'Foreground/All Text':
						this.removeAllColorsFromText();
						this.css.container['color'] = newColor;
						break;
					case 'Title':
						this.css.header['color'] = newColor;
						break;
					case 'Subtitle':
						this.css.message['color'] = newColor;
						break;
					case 'Button Text':
						this.css.button['color'] = newColor;
						break;
					case 'Button Background':
						delete this.css.button['background'];
						this.css.button['background-color'] = newColor;
						break;
					case 'Cancel Message':
						this.css.closeMessage['color'] = newColor;
						break;
					default:
						break;
				}
				this.generateTemplate();
			},
			removeAllColorsFromText() {
				[
					'header',
					'message',
					'button',
					'closeMessage'
				].forEach(id => delete this.css[id].color);
			},
			setColorAdjustmentSelection(selection) {
				this.colorAdjustment = selection;
			}
		},
		mounted() {
			if (!this.css || JSON.stringify(this.css) == '{}') {
				this.css.container = previewStyles.container;
				this.css.header = previewStyles.header;
				this.css.message = previewStyles.message;
				this.css.button = previewStyles.button;
				this.css.closeMessage = previewStyles.closeMessage;
			} else {
				delete this.css.container['bottom'];
				delete this.css.container['left'];
				delete this.css.container['position'];
			}
			this.generateTemplate();
		},
		watch: {
			$props: {
				handler() {
					this.updateColors();
					this.$emit('blockUpdated')
				},
				deep: true,
			},
			colors() {
				this.updateColors();
			},
		}
	}
</script>
<style>
	input[type="color"] {
		-webkit-appearance: none;
		border: none;
	}
</style>
