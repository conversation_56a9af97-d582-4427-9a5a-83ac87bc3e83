<template>
  <div class="flex h-screen overflow-hidden">

    <!-- Sidebar -->
    <Sidebar :sidebarOpen="sidebarOpen" @close-sidebar="sidebarOpen = false" />

    <!-- Content area -->
    <div class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden bg-white">

      <!-- Site header -->
      

      <main>

        <!-- Search area -->
        <div class="relative flex flex-col items-center justify-center px-4 sm:px-6 lg:px-8 py-8 lg:py-16 bg-indigo-500 overflow-hidden">
          <!-- Glow -->
          <div class="absolute pointer-events-none" aria-hidden="true">
            <svg width="512" height="512" xmlns="http://www.w3.org/2000/svg">
              <defs>
                <radialGradient cx="50%" cy="50%" fx="50%" fy="50%" r="50%" id="ill-a">
                  <stop stop-color="#FFF" offset="0%" />
                  <stop stop-color="#FFF" stop-opacity="0" offset="100%" />
                </radialGradient>
              </defs>
              <circle style="mix-blend-mode:overlay" cx="588" cy="650" r="256" transform="translate(-332 -394)" fill="url(#ill-a)" fill-rule="evenodd" opacity=".48" />
            </svg>
          </div>
          <!-- Illustration -->
          <div class="absolute pointer-events-none" aria-hidden="true">
            <img src="../../images/profile_banner.png" width="1380" height="361" />
          </div>
          <div class="relative w-full max-w-2xl mx-auto text-center">
            <div class="mb-5">
              <h1 class="text-2xl md:text-3xl text-white font-bold">Raleon Insights</h1>
            </div>

                <div class="text-center">
                  <ul class="inline-flex flex-wrap text-sm font-medium">
                    <li class="flex items-center">
                      <a class="text-slate-300 hover:text-indigo-500" href="/support">Support</a>
                      <svg class="h-4 w-4 fill-current text-slate-400 mx-3" viewBox="0 0 16 16">
                        <path d="M6.6 13.4L5.2 12l4-4-4-4 1.4-1.4L12 8z" />
                      </svg>
                    </li>
                    <li class="flex items-center">
                      <a class="text-slate-300 hover:text-indigo-500" href="#">Raleon Insights</a>
                    </li>
                  </ul>
                </div>
          </div>
        </div>

        <div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">

          <!-- Sections -->
          <div class="space-y-8">

            <div>
              <h2 class="text-ralgranite-500 text-xl">Overview</h2>
              <p class="leading-relaxed">Raleon's Insights engine utilizes the unique behavioral details we general on-chain and off-chain to enrich the data within your organization. The data enrichment is sometimes predictive in nature, and other times derived in nature based on the behavioral graph we've established for wallets.</p>
              <br />
              <p class="leading-relaxed">While we have a number of Insights within Raleon today, there are two that most often have questions asked about them: <a href="#stickiness" class="text-ralpurple-500">User Engagement</a> and <a href="#persona" class="text-ralpurple-500">Persona's</a>. We will cover those two in more depth in this document.</p>
            </div>

            <div>
              <h2 class="text-ralgranite-500 text-xl">Use Cases</h2>
              <p class="leading-relaxed">Insights allow you to quickly get to details that are impactful to your web3 company or community, like what kinds of categories your new users are coming from.</p>
              <br />
              <p class="leading-relaxed">Insights allow you to gain perspective into your user base that you may not have been able to have had before, or that would have taken a lot of effort, like our predictive Persona model, or stickiness metric.</p>
              <br />
              <p class="leading-relaxed">At Raleon, we're about transparency and speed, so we will also provide the wallet-enriched data to your analytics teams, allowing you to do even more exciting things for your web3 company or community.</p>
            </div>
<a id="stickiness"></a>
            <div>
              <h2 class="text-ralgranite-500 text-xl">Stickiness / User Engagement Insight</h2>
              <p class="leading-relaxed">One of the most important things to understand for any web3 company or community is how sticky your dApp or protocol is with your users. While what constitutes a "good" stickiness will vary based on the project, the concept is still the same - your power users or super users should be your stickiest and most engaged.</p>
              <br />
              <p class="leading-relaxed">User Engagement or Stickiness is the idea of how much time or activity your users are spending with your project. While again, it varies per project, generally speaking a project with higher stickiness/user engagement is going to have less churn and more success. Or stated differently, if your project has a group of users who use your project very often, that's a group you want generally to emulate. Your stickiest users are usually your power users - the ones that give the best feedback, tell the most people, and have the lowest chance of leaving (churn). As a result, understanding who those users are, how many you have, and how to convert more into them becomes very valuable to project growth.</p>
              <br />
              <p class="leading-relaxed">So what is <span class="font-semibold">Stickiness within Raleon?</span> It is a measure, across all your active users, of the percentage of activity they spend with you as compared to their overall activity. In other words, if your <span class="font-semibold">User Percent Activity (what it's called within the chart)</span> is 8%, that means that 92% of your users time is spent with different projects. Whether that's a low or high number will be based on what kind of project you have, and benchmarked against your competitors.</p>
            </div>

            <div>
              <h2 class="text-ralgranite-500 text-xl">Creating a Stickiness Chart</h2>
              <p class="leading-relaxed">If you would like to see the stickiness of your project, below is an example of creating a line-chart trending your stickiness.</p>
              <ul class="leading-relaxed list-disc ml-6 mt-4">
                <li class="mb-2">
                  Navigate to a Dashboard
                </li>
                <li class="mb-2">
                  Click the "Add Chart" button in the top right corner
                </li>
                <li class="mb-2">
                  Select the Project whose data you want to use for this Chart
                </li>
                <li class="mb-2">
                  Click the Line chart graphic for creating a line chart
                </li>
                <li class="mb-2">
                  Select "Medium" for the size chart we are making
                </li>
                <li class="mb-2">
                  Select "User Percent Activity" for the type of data we are looking for
                </li>
                <li class="mb-2">
                  Select "Active Users" for the group of addresses we want to use
                </li>
                <li class="mb-2">
                  Select "Time" for the label we want to use
                </li>
                <li class="mb-2">
                  Select "Activity Percentage" for what we want to use as the value
                </li>
                <li class="mb-2">
                  Select "Last 7 Days" for what we want to use as the value. Since this is a generated insight, you may only see details going so far back. If you would like to see further back in time, feel free to reach out to us and we can run the insights on more historic data.
                </li>
                <li class="mb-2">
                  Name the chart and click "Add Report"
                </li>
              </ul>
            </div>
<a id="persona"></a>
            <div>
              <h2 class="text-ralgranite-500 text-xl">Persona</h2>
              <p class="leading-relaxed">Part of understanding your users is understanding what kind of behavioral classification they fit into. This gives you an idea into what kinds of users you have, helping you determine what features to prioritize, whether you're gaining or lising the right kind of users, and so on.</p>
              <br />
              <p class="leading-relaxed">The Persona insight within Raleon is a predictive model that's based on more than half a billion interactions tracked to date. The following are the 4 groupings of personas we have today:</p>
              <ul class="leading-relaxed list-disc ml-6 mt-4 mb-4">
                <li class="mb-2">
                  <span class="font-semibold">Web3 Casual User: </span> These are wallets that tend to have much lower activity overall. They've likely had moderate exposure to some aspects of web3, such a a dex, maybe having a low-value NFT, or participating in an LP. Very rarely have they done something in all categories. Occassionally, a web3 casual may have been a previously highly active user that's now gone dormant (we will likely break this out into an additional cluster down the line).
                </li>
                <li class="mb-2">
                  <span class="font-semibold">Web3 Power User:</span> These are wallets that tend to have medium to high level of activity, and could expect to be well versed in most things Web3. The majority of them have had some level of NFT activity, some even holding blue chip or other valuable NFT's. Many, have engaged in some kind of LP activity, and are found across multiple chains.
                </li>
                <li class="mb-2">
                  <span class="font-semibold">Traders: </span> These are web3 traders. All of them have had DEX interactions, and have a high level of activity. Many of them have done lots of trades at one point or another, with a high level of volume and/or activity. Most have some level of valuable NFT and have engaged in LP activities.
                </li>
                <li class="mb-2">
                  <span class="font-semibold">NFT Experts: </span> NFT Experts, as you might suspect, have mastered the NFT world. They all have NFT collections, and the vast majority if not all are holding valuable NFT's (blue chip or other high valued NFTs). They've all engaged with a DEX, although they're not really considered DEX Traders. These tend to be higher activity users, and they all have done something on OpenSea.
                </li>
              </ul>
              <p class="leading-relaxed">You may now be wondering - how in the world did we come to these conclusions? With a goal of transparency, we will pull back the curtain a little on our Persona V1. We ran a model across 6 million wallets with an initial set of features that looked at activity (txns) levels over time, NFT holdings, token holdings, and a few other factors. We then broke those down, analyzing them looking at:</p>
              <ul class="leading-relaxed list-disc ml-6 mt-4 mb-4">
                <li class="mb-2">
                  <span class="font-semibold">DEX / Trader-Like Activity: </span> These are wallets that tend to have much lower activity overall. They've likely had moderate exposure to some aspects of web3, such a a dex, maybe having a low-value NFT, or participating in an LP. Very rarely have they done something in all categories. Occassionally, a web3 casual may have been a previously highly active user that's now gone dormant (we will likely break this out into an additional cluster down the line).
                </li>
                <li class="mb-2">
                  <span class="font-semibold">NFT Collections:</span> This was broken down into whether they had NFT's, and based on floors, what value of NFTs they had.
                </li>
                <li class="mb-2">
                  <span class="font-semibold">Chain Participation: </span> How many chains was the wallet participating on. We do not yet get down into the details of what level of participation on other chains yet.
                </li>
                <li class="mb-2">
                  <span class="font-semibold">Activity Overall: </span> Activity, over specific time horizons of a user.
                </li>
                <li class="mb-2">
                  <span class="font-semibold">Liquidity Provider Activity: </span> This was both a look at what % of a segment engaged in LP activity (being a provider/part of a pool), along with how much (are they a part of many pools). To start, this is looking specifically at Uniswap and Sushiswap.
                </li>
                <li class="mb-2">
                  <span class="font-semibold">Holdings: </span> While not quite as valuable as other indicators, we did look at holdings, both in total tokens held and an estimation of value of the tokens held.
                </li>
              </ul>
            </div>

            <div>
              <h2 class="text-ralgranite-500 text-xl">Creating a Persona Chart</h2>
              <p class="leading-relaxed">If you would like to see your Persona make-up, you below is an example of creating a Pie Chart for Personas.</p>
              <ul class="leading-relaxed list-disc ml-6 mt-4">
                <li class="mb-2">
                  Navigate to a Dashboard
                </li>
                <li class="mb-2">
                  Click the "Add Chart" button in the top right corner
                </li>
                <li class="mb-2">
                  Select the Project whose data you want to use for this Chart
                </li>
                <li class="mb-2">
                  Click the Pie Chart graphic for creating a pie chart
                </li>
                <li class="mb-2">
                  Select "Medium" for the size chart we are making
                </li>
                <li class="mb-2">
                  Select "Persona" for the type of data we are looking for
                </li>
                <li class="mb-2">
                  Select "Active Users" for the group of addresses we want to use
                </li>
                <li class="mb-2">
                  Select "Cluster Id" for the label we want to use (this will show "Web3 Casual", and so on)
                </li>
                <li class="mb-2">
                  Select "Group Size" for what we want to use as the value
                </li>
                <li class="mb-2">
                  Name the chart and click "Add Report"
                </li>
              </ul>
            </div>

          </div>

        </div>
      </main>

    </div>

  </div>
</template>

<script>
import { ref } from 'vue'
import Header from '../../partials/Header.vue'
import Sidebar from '../../partials/Sidebar.vue'

export default {
  name: 'DashboardHelp',
  components: {
    Sidebar,
    Header,
  },
  setup() {

    const sidebarOpen = ref(false)

    return {
      sidebarOpen,
    }
  }
}
</script>
