<template>
  <div class="flex h-screen overflow-hidden">

    <!-- Sidebar -->
    <Sidebar :sidebarOpen="sidebarOpen" @close-sidebar="sidebarOpen = false" />

    <!-- Content area -->
    <div class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden bg-white">

      <main>

        <!-- Search area -->
        <div class="relative flex flex-col items-center justify-center px-4 sm:px-6 lg:px-8 py-8 lg:py-16 bg-indigo-500 overflow-hidden">
          <!-- Glow -->
          <div class="absolute pointer-events-none" aria-hidden="true">
            <svg width="512" height="512" xmlns="http://www.w3.org/2000/svg">
              <defs>
                <radialGradient cx="50%" cy="50%" fx="50%" fy="50%" r="50%" id="ill-a">
                  <stop stop-color="#FFF" offset="0%" />
                  <stop stop-color="#FFF" stop-opacity="0" offset="100%" />
                </radialGradient>
              </defs>
              <circle style="mix-blend-mode:overlay" cx="588" cy="650" r="256" transform="translate(-332 -394)" fill="url(#ill-a)" fill-rule="evenodd" opacity=".48" />
            </svg>
          </div>
          <!-- Illustration -->
          <div class="absolute pointer-events-none" aria-hidden="true">
            <img src="../../images/profile_banner.png" width="1380" height="361" />
          </div>
          <div class="relative w-full max-w-2xl mx-auto text-center">
            <div class="mb-5">
              <h1 class="text-2xl md:text-3xl text-white font-bold">What Can We Help You With?</h1>
            </div>
          </div>
        </div>

        <div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">

          <!-- Sections -->
          <div class="space-y-8">

            <!-- Popular Topics -->
            <div>
              <!-- Grid -->
              <div class="grid sm:grid-cols-2 lg:grid-cols-4 lg:sidebar-expanded:grid-cols-2 xl:sidebar-expanded:grid-cols-4 gap-6">

                <!--
                <div class="bg-slate-100 rounded-sm text-center p-5">
                  <div class="flex flex-col h-full">
                    <div class="grow mb-2">
                      <div class="inline-flex w-12 h-12 rounded-full bg-indigo-400">
                        <svg class="w-12 h-12" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                          <defs>
                            <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="icon1-a">
                              <stop stop-color="#FFF" offset="0%" />
                              <stop stop-color="#A5B4FC" offset="100%" />
                            </linearGradient>
                          </defs>
                          <g fill-rule="nonzero" fill="none">
                            <path d="M19.236 21.995h-3.333c-.46 0-.833.352-.833.786v9.428c0 .434.373.786.833.786h4.167V22.78c0-.434-.374-.786-.834-.786Z" fill="#4F46E5" opacity=".88" />
                            <path d="M34.234 20.073a2.393 2.393 0 0 0-.735-.116h-5v-2.609c0-3.325-2.157-4.297-3.298-4.347a.828.828 0 0 0-.611.24.888.888 0 0 0-.257.63v4.032L21 22.077v10.924h10.19c1.1.005 2.073-.744 2.392-1.842l2.308-7.826a2.711 2.711 0 0 0-.181-1.988 2.528 2.528 0 0 0-1.475-1.272Z" fill="url(#icon1-a)" transform="translate(-.93 -.005)" />
                          </g>
                        </svg>
                      </div>
                      <h3 class="text-lg font-semibold text-slate-800 mb-1">Getting Started</h3>
                      <div class="text-sm">Lorem ipsum dolor sit consectetur adipiscing elit sed do.</div>
                    </div>
                    <div>
                      <a class="text-sm font-medium text-indigo-500 hover:text-indigo-600" href="#0">Explore -&gt;</a>
                    </div>
                  </div>
                </div>
                -->

                <!-- Item -->
                <div class="bg-slate-100 rounded-sm text-center p-5">
                  <div class="flex flex-col h-full">
                    <div class="grow mb-2">
                      <!-- Icon -->
                      <div class="inline-flex w-12 h-12 rounded-full bg-indigo-400">
                    <svg class="shrink-0 h-6 w-6" viewBox="0 0 24 24">
                      <path
                        class="fill-currenttext-indigo-500"
                        d="M12 0C5.383 0 0 5.383 0 12s5.383 12 12 12 12-5.383 12-12S18.617 0 12 0z"
                      />
                      <path
                        class="fill-currenttext-indigo-600"
                        d="M12 3c-4.963 0-9 4.037-9 9s4.037 9 9 9 9-4.037 9-9-4.037-9-9-9z"
                      />
                      <path
                        class="fill-current text-indigo-200"
                        d="M12 15c-1.654 0-3-1.346-3-3 0-.462.113-.894.3-1.285L6 6l4.714 3.301A2.973 2.973 0 0112 9c1.654 0 3 1.346 3 3s-1.346 3-3 3z"
                      />
                    </svg>
                      </div>
                      <!-- Content -->
                      <h3 class="text-lg font-semibold text-slate-800 mb-1">Dashboards & Charts</h3>
                      <div class="text-sm">Learn the basics of using Dashboards and Charts.</div>
                    </div>
                    <!-- Link -->
                    <div>
                      <a class="text-sm font-medium text-indigo-500 hover:text-indigo-600" href="/support/starter-dashboards">Learn More -&gt;</a>
                    </div>
                  </div>
                </div>


                <!-- Item -->
                <div class="bg-slate-100 rounded-sm text-center p-5">
                  <div class="flex flex-col h-full">
                    <div class="grow mb-2">
                      <!-- Icon -->
                      <div class="inline-flex w-12 h-12 rounded-full bg-indigo-400">
                        <svg class="w-12 h-12" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                          <defs>
                            <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="icon3-a">
                              <stop stop-color="#FFF" offset="0%" />
                              <stop stop-color="#A5B4FC" offset="100%" />
                            </linearGradient>
                          </defs>
                          <g fill-rule="nonzero" fill="none">
                            <circle fill="url(#icon3-a)" cx="30.5" cy="17.5" r="4.5" />
                            <circle fill="#4F46E5" opacity=".88" cx="17.5" cy="17.5" r="4.5" />
                            <circle fill="#4F46E5" opacity=".88" cx="30.5" cy="30.5" r="4.5" />
                            <circle fill="url(#icon3-a)" cx="17.5" cy="30.5" r="4.5" />
                          </g>
                        </svg>
                      </div>
                      <!-- Content -->
                      <h3 class="text-lg font-semibold text-slate-800 mb-1">Off-Chain Events</h3>
                      <div class="text-sm">Learn about how to setup off-chain events to track product interactions or marketing events.</div>
                    </div>
                    <!-- Link -->
                    <div>
                      <a class="text-sm font-medium text-indigo-500 hover:text-indigo-600" href="/support/marketing-attribution">Learn more -&gt;</a>
                    </div>
                  </div>
                </div>

                <!-- Item -->
                <div class="bg-slate-100 rounded-sm text-center p-5">
                  <div class="flex flex-col h-full">
                    <div class="grow mb-2">
                      <!-- Icon -->
                      <div class="inline-flex w-12 h-12 rounded-full bg-indigo-400">
                        <svg class="w-12 h-12" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                          <defs>
                            <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="icon7-a">
                              <stop stop-color="#FFF" offset="0%" />
                              <stop stop-color="#A5B4FC" offset="100%" />
                            </linearGradient>
                          </defs>
                          <g transform="translate(-.186 -.042)" fill-rule="nonzero" fill="none">
                            <circle fill="#554FE8" cx="20" cy="26.993" r="10" />
                            <circle fill="url(#icon7-a)" cx="25.122" cy="24" r="11" />
                            <path fill="#6366F1" opacity=".72" d="m26.255 22.605-1.569-3.586-.922 3.804-3.735.327 3.26 2.195-.864 3.91 2.927-2.626 3.339 2.078-1.53-3.7 2.83-2.729z" />
                          </g>
                        </svg>
                      </div>
                      <!-- Content -->
                      <h3 class="text-lg font-semibold text-slate-800 mb-1">Raleon Insights</h3>
                      <div class="text-sm">Learn about the unique insights our enrichment engine generates, such as Persona.</div>
                    </div>
                    <!-- Link -->
                    <div>
                      <a class="text-sm font-medium text-indigo-500 hover:text-indigo-600" href="/support/insights">Learn more -&gt;</a>
                    </div>
                  </div>
                </div>

                <!-- Item -->
                <div class="bg-slate-100 rounded-sm text-center p-5">
                  <div class="flex flex-col h-full">
                    <div class="grow mb-2">
                      <!-- Icon -->
                      <div class="inline-flex w-12 h-12 rounded-full bg-indigo-400">
                        <svg class="w-12 h-12" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                          <defs>
                            <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="icon8-a">
                              <stop stop-color="#FFF" offset="0%" />
                              <stop stop-color="#A5B4FC" offset="100%" />
                            </linearGradient>
                          </defs>
                          <g fill-rule="nonzero" fill="none">
                            <path d="M18.648 35.069c.232.217.46.322.798.31.337-.012.558-.132.775-.365l1.95-2.094c1.028.414 2.504.813 4.076.758 1.798-.063 3.688-.692 5.426-2.441 3.686-3.956 4.05-12.862 4.038-13.199-.012-.337-.136-.67-.368-.888-.233-.217-.574-.317-.91-.306-.338.012-9.405 1.23-12.875 4.953-2.168 2.327-2.41 5.037-1.883 7.27l6.61-3.946-7.804 8.378a1.206 1.206 0 0 0 .167 1.57Z" fill="#554FE8" />
                            <path d="M.353 17.663c.225.224.45.337.787.337s.562-.113.786-.337l2.024-2.026c1.011.45 2.473.9 4.046.9 1.799 0 3.71-.562 5.508-2.25C17.326 10.462 18 1.575 18 1.237c0-.337-.112-.675-.337-.9C17.438.113 17.1 0 16.763 0c-.337 0-9.441.9-13.038 4.5-2.248 2.25-2.585 4.95-2.136 7.2l6.744-3.712-8.093 8.1a1.206 1.206 0 0 0 .113 1.575Z" fill="url(#icon8-a)" transform="rotate(13 -42.427 83.827)" />
                          </g>
                        </svg>
                      </div>
                      <!-- Content -->
                      <h3 class="text-lg font-semibold text-slate-800 mb-1">Wallet Traits</h3>
                      <div class="text-sm">Learn about the Trait Tags our behavioral engine generates and applies to Wallets.</div>
                    </div>
                    <!-- Link -->
                    <div>
                      <a class="text-sm font-medium text-indigo-500 hover:text-indigo-600" href="/support/traits">Learn more -&gt;</a>
                    </div>
                  </div>
                </div>

                <!-- Item -->
                <div class="bg-slate-100 rounded-sm text-center p-5">
                  <div class="flex flex-col h-full">
                    <div class="grow mb-2">
                      <!-- Icon -->
                      <div class="inline-flex w-12 h-12 rounded-full bg-indigo-400">
                        <svg class="shrink-0 h-6 w-6" viewBox="0 0 24 24">
                      <path class="fill-current text-indigo-500"
                        d="M18.974 8H22a2 2 0 012 2v6h-2v5a1 1 0 01-1 1h-2a1 1 0 01-1-1v-5h-2v-6a2 2 0 012-2h.974zM20 7a2 2 0 11-.001-3.999A2 2 0 0120 7zM2.974 8H6a2 2 0 012 2v6H6v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5H0v-6a2 2 0 012-2h.974zM4 7a2 2 0 11-.001-3.999A2 2 0 014 7z" />
                      <path class="fill-current text-indigo-300"
                        d="M12 6a3 3 0 110-6 3 3 0 010 6zm2 18h-4a1 1 0 01-1-1v-6H6v-6a3 3 0 013-3h6a3 3 0 013 3v6h-3v6a1 1 0 01-1 1z" />
                    </svg>
                      </div>
                      <!-- Content -->
                      <h3 class="text-lg font-semibold text-slate-800 mb-1">Audiences</h3>
                      <div class="text-sm">Learn about Audiences, and how to use them to get deeper user insights and drive targeted engagements.</div>
                    </div>
                    <!-- Link -->
                    <div>
                      <a class="text-sm font-medium text-indigo-500 hover:text-indigo-600" href="/support/starter-segments">Learn more -&gt;</a>
                    </div>
                  </div>
                </div>

				                <!-- Item -->
				<div class="bg-slate-100 rounded-sm text-center p-5">
                  <div class="flex flex-col h-full">
                    <div class="grow mb-2">
                      <!-- Icon -->
                      <div class="inline-flex w-12 h-12 rounded-full bg-indigo-400">
                        <svg class="shrink-0 h-6 w-6" viewBox="0 0 24 24">
                      <path class="fill-current text-indigo-500"
                        d="M18.974 8H22a2 2 0 012 2v6h-2v5a1 1 0 01-1 1h-2a1 1 0 01-1-1v-5h-2v-6a2 2 0 012-2h.974zM20 7a2 2 0 11-.001-3.999A2 2 0 0120 7zM2.974 8H6a2 2 0 012 2v6H6v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5H0v-6a2 2 0 012-2h.974zM4 7a2 2 0 11-.001-3.999A2 2 0 014 7z" />
                      <path class="fill-current text-indigo-300"
                        d="M12 6a3 3 0 110-6 3 3 0 010 6zm2 18h-4a1 1 0 01-1-1v-6H6v-6a3 3 0 013-3h6a3 3 0 013 3v6h-3v6a1 1 0 01-1 1z" />
                    </svg>
                      </div>
                      <!-- Content -->
                      <h3 class="text-lg font-semibold text-slate-800 mb-1">Developer Docs</h3>
                      <div class="text-sm">View API documenation, integration docs, and more.</div>
                    </div>
                    <!-- Link -->
                    <div>
                      <a class="text-sm font-medium text-indigo-500 hover:text-indigo-600" target="_blank" :href="dev_doc_url">View Now -&gt;</a>
                    </div>
                  </div>
                </div>

              </div>
            </div>

          </div>

        </div>
      </main>

    </div>

  </div>
</template>

<script>
import { ref } from 'vue'
import Header from '../../partials/Header.vue'
import Sidebar from '../../partials/Sidebar.vue'
import * as Utils from '../../utils/Utils';
const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	name: 'Hub',
	components: {
		Sidebar,
		Header,
	},
	setup() {
		const sidebarOpen = ref(false)

		return {
		sidebarOpen,
		}
	},
	data() {
		return {
			dev_doc_url: 'https://docs.raleon.io',
		}
	},
	async mounted() {
		let response = {};
		let jsonresponse = {};
		try {
			console.log("Trying request to /users/doc-login", `${URL_DOMAIN}/users/doc-login`)
			response = await fetch(`${URL_DOMAIN}/users/doc-login`, {
				method: 'GET',
				withCreditentials: true,
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Access-Control-Allow-Origin': '*',
					'Content-Type': 'application/json'
				}
			});
			console.log("Response from /users/doc-login", response)
			jsonresponse = await response.json();
			console.log("DOCS DOCS DOCS", jsonresponse)
			if(jsonresponse.docs_url)
				this.dev_doc_url = `${jsonresponse.docs_url}`;
			console.log("DOCS DOCS DOCS", this.dev_doc_url);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}
	}
}
</script>
