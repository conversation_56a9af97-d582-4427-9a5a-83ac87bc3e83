<template>
  <div class="flex h-screen overflow-hidden">

    <!-- Sidebar -->
    <Sidebar :sidebarOpen="sidebarOpen" @close-sidebar="sidebarOpen = false" />

    <!-- Content area -->
    <div class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden bg-white">

      <!-- Site header -->
      

      <main>

        <!-- Search area -->
        <div class="relative flex flex-col items-center justify-center px-4 sm:px-6 lg:px-8 py-8 lg:py-16 bg-indigo-500 overflow-hidden">
          <!-- Glow -->
          <div class="absolute pointer-events-none" aria-hidden="true">
            <svg width="512" height="512" xmlns="http://www.w3.org/2000/svg">
              <defs>
                <radialGradient cx="50%" cy="50%" fx="50%" fy="50%" r="50%" id="ill-a">
                  <stop stop-color="#FFF" offset="0%" />
                  <stop stop-color="#FFF" stop-opacity="0" offset="100%" />
                </radialGradient>
              </defs>
              <circle style="mix-blend-mode:overlay" cx="588" cy="650" r="256" transform="translate(-332 -394)" fill="url(#ill-a)" fill-rule="evenodd" opacity=".48" />
            </svg>
          </div>
          <!-- Illustration -->
          <div class="absolute pointer-events-none" aria-hidden="true">
            <img src="../../images/profile_banner.png" width="1380" height="361" />
          </div>
          <div class="relative w-full max-w-2xl mx-auto text-center">
            <div class="mb-5">
              <h1 class="text-2xl md:text-3xl text-white font-bold">Trait Tags</h1>
            </div>

                <div class="text-center">
                  <ul class="inline-flex flex-wrap text-sm font-medium">
                    <li class="flex items-center">
                      <a class="text-slate-300 hover:text-indigo-500" href="/support">Support</a>
                      <svg class="h-4 w-4 fill-current text-slate-400 mx-3" viewBox="0 0 16 16">
                        <path d="M6.6 13.4L5.2 12l4-4-4-4 1.4-1.4L12 8z" />
                      </svg>
                    </li>
                    <li class="flex items-center">
                      <a class="text-slate-300 hover:text-indigo-500" href="#">Trait Tags</a>
                    </li>
                  </ul>
                </div>
          </div>
        </div>

        <div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">

          <!-- Sections -->
          <div class="space-y-8">

            <div>
              <h2 class="text-ralgranite-500 text-xl">Overview</h2>
              <p class="leading-relaxed">Raleon uses AI to determine particular traits across all the activities of a wallet - these traits provide visibility into the kind of behavior you could expect the wallet to perform. These traits are then tagged on to a wallet, providing better clarity about the wallet for analysis, internal data science, and segmentation within Raleon.</p>
            </div>

            <div>
              <h2 class="text-ralgranite-500 text-xl">Use Cases</h2>
              <p class="leading-relaxed">Traits could help you target your next set of airdrop users, ensuring they're the most engaged users of a DEX, for example. </p>
              <br />
              <p class="leading-relaxed">Traits can help you understand whether users have just experimented in a particular area, or are active participants, such as in web3 gaming.</p>
              <br />
              <p class="leading-relaxed">You can use traits to determine what shared behaviors exist across users you've recovered from Dormant, to improve your retention.</p>
            </div>
<a id="traits"></a>
            <div>
              <h2 class="text-ralgranite-500 text-xl">How Do Traits Work?</h2>
              <p class="leading-relaxed">Raleon's Insight's AI runs traits on a monthly basis, automatically refreshing the trait tags of wallets. Any new wallets discovered by Raleon's data platform will be evaluated outside of the monthly window.</p>
              <br />
              <p class="leading-relaxed">The behaviors of wallets are constantly changing, as a result, every trait tag on a wallet has two states: active and inactive. The implication of these states are important:</p>
              <ul class="leading-relaxed list-disc ml-6 mt-4">
                <li class="mb-2">
                  <span class="font-semibold">Active</span> - A Trait tag that is active means that its criteria was true within the last 30 days.
                </li>
                <li class="mb-2">
                  <span class="font-semibold">Inactive</span> - A Trait tag that is inactive means that its criteria was true at one point, but has not been true recently (in the last 30 days).
                </li>
              </ul>
            </div>

            <div>
              <h2 class="text-ralgranite-500 text-xl">What Traits Are Being Tracked?</h2>
              <p class="leading-relaxed">Raleon automatically tracks special, global traits that are available to all users. If you want to know what criteria a trait tag gets applied with, mousing over the triat will provide a tooltip with those details. The following are currently the list of traits being tracked in Raleon (list updated frequently):</p>
              <ul class="leading-relaxed list-disc ml-6 mt-4">
                <li class="mb-2">
                  <span class="font-semibold">Trader</span> - Top 25% of user activity on a DEX or DEX aggregator.
                </li>
                <li class="mb-2">
                  <span class="font-semibold">DEX user</span> - Represents a high degree of Exchange usage, not including Centralized Exchanges.
                </li>
                <li class="mb-2">
                  <span class="font-semibold">Top DeFi user</span> - Represents a high degree of DeFi related behaviors.
                </li>
                <li class="mb-2">
                  <span class="font-semibold">Gamer</span> - Represents behaviors that would indicate the user is a regular gamer.
                </li>
                <li class="mb-2">
                  <span class="font-semibold">Bridge User</span> - Represents behaviors that show common bridge usage.
                </li>
              </ul>
            </div>

          </div>

        </div>
      </main>

    </div>

  </div>
</template>

<script>
import { ref } from 'vue'
import Header from '../../partials/Header.vue'
import Sidebar from '../../partials/Sidebar.vue'

export default {
  name: 'TraitHelp',
  components: {
    Sidebar,
    Header,
  },
  setup() {

    const sidebarOpen = ref(false)

    return {
      sidebarOpen,
    }
  }
}
</script>
