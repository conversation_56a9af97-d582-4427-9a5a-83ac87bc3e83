<template>
  <div class="flex h-screen overflow-hidden">

    <!-- Sidebar -->
    <Sidebar :sidebarOpen="sidebarOpen" @close-sidebar="sidebarOpen = false" />

    <!-- Content area -->
    <div class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden bg-white">

      <!-- Site header -->


      <main>

        <!-- Search area -->
        <div class="relative flex flex-col items-center justify-center px-4 sm:px-6 lg:px-8 py-8 lg:py-16 bg-indigo-500 overflow-hidden">
          <!-- Glow -->
          <div class="absolute pointer-events-none" aria-hidden="true">
            <svg width="512" height="512" xmlns="http://www.w3.org/2000/svg">
              <defs>
                <radialGradient cx="50%" cy="50%" fx="50%" fy="50%" r="50%" id="ill-a">
                  <stop stop-color="#FFF" offset="0%" />
                  <stop stop-color="#FFF" stop-opacity="0" offset="100%" />
                </radialGradient>
              </defs>
              <circle style="mix-blend-mode:overlay" cx="588" cy="650" r="256" transform="translate(-332 -394)" fill="url(#ill-a)" fill-rule="evenodd" opacity=".48" />
            </svg>
          </div>
          <!-- Illustration -->
          <div class="absolute pointer-events-none" aria-hidden="true">
            <img src="../../images/profile_banner.png" width="1380" height="361" />
          </div>
          <div class="relative w-full max-w-2xl mx-auto text-center">
            <div class="mb-5">
              <h1 class="text-2xl md:text-3xl text-white font-bold">Off-Chain Events</h1>
            </div>

                <div class="text-center">
                  <ul class="inline-flex flex-wrap text-sm font-medium">
                    <li class="flex items-center">
                      <a class="text-slate-300 hover:text-indigo-500" href="/support">Support</a>
                      <svg class="h-4 w-4 fill-current text-slate-400 mx-3" viewBox="0 0 16 16">
                        <path d="M6.6 13.4L5.2 12l4-4-4-4 1.4-1.4L12 8z" />
                      </svg>
                    </li>
                    <li class="flex items-center">
                      <a class="text-slate-300 hover:text-indigo-500" href="#">Off-Chain Events</a>
                    </li>
                  </ul>
                </div>
          </div>
        </div>

        <div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">

          <!-- Sections -->
          <div class="space-y-8">

            <div>
              <h2 class="text-ralgranite-500 text-xl">Overview</h2>
              <p class="leading-relaxed">It’s very difficult to connect activities that happen off-chain to the activity that occurs on-chain for a given wallet. Our Off-Chain Events, connected into our Relationship Platform, alleviate this pain in a simple and automated way, giving you visibility into the results of your off-chain activities.</p>
              <img src="../../images/help/CampaignExample.png" class="pb-4 pt-4">
              <p class="leading-relaxed">Our Off-Chain Event engine can be used for tracking details in an app, sending in emails, custom events, or marketing attribution.</p>
            </div>

            <div>
              <h2 class="text-ralgranite-500 text-xl">Use Cases</h2>
              <p class="leading-relaxed">You have the e-mail of a user that you want to have connected to a wallet so it gains all the benefit within Raleon.</p>

              <p class="leading-relaxed">You want to track product usage (either directly from our events, or by sending data from Mixpanel) and see how it correlates to on-chain activity.</p>

              <p class="leading-relaxed">You want to track how your Twitter ads are performing by understanding how many users who clicked on an ad actually connect a wallet, and how many actually do something on-chain such as make a trade or buy an NFT.</p>
              <br />
              <p class="leading-relaxed">You want to see how effective your discord or telegram activity is at getting users to do something on-chain like mint a new NFT or try a new feature.</p>
              <br />
              <p class="leading-relaxed">You want to compare whether Twitter, Discord, Telegram, or growth content drives the best conversions when you send out marketing materials to it.</p>
              <br />
              <p class="leading-relaxed">Below is an example of how a customer journey might look with Raleon:</p>
              <img src="../../images/help/AttributionExample.png" class="pt-4 pb-4" />
            </div>

            <div>
              <h2 class="text-ralgranite-500 text-xl">How Does The Off-Chain Event Engine Work?</h2>
              <p class="leading-relaxed">Assuming you are setup with a Raleon account, you will need to add our off-chain event snippet to each page of your site that a user may navigate to. Once the snippet is on your dApp or protocol, it will track when a "Connect Wallet" action occurs. When it does, there are two core things that get sent to Raleon's attribution engine:</p>
              <br />
              <ul class="leading-relaxed list-disc ml-6 mt-4">
                <li class="mb-2">The Wallet Address that connected</li>
                <li class="mb-2">The UTM details that were the result of your marketing or community efforts. This can be from discord, an ad on twitter, and so on.</li>
              </ul>
              <br />
              <p class="leading-relaxed">We have designed our off-chain eventing engine to be very privacy friendly. As a result, it is only receieving the bare-minimum needed to provide behavioral visibility for attribution, preserving the privacy of your users.</p>
              <br />
              <p class="leading-relaxed">Once you have our snippet added to your dApp or protocol, we’ll start automatically tracking campaigns we detect. The result is all activity on your website after a wallet is connected, and subsequent activity on-chain, will be automatically analyzed by Raleon and our attribution algorithm. This will give you visibility into what kinds of activity and on-chain conversion results your efforts are really getting you.</p>
            </div>

            <div>
              <h2 class="text-ralgranite-500 text-xl">How to Install</h2>
			  <br/>
			  <h2 class="leading-relaxed text-lg">
				Here's a <a class="underline text-ralprimary-main" target="_blank" href="https://www.loom.com/share/d038cc764eb74fa292bdb83f47542690">quick 📹</a> on how to install the snippet.
			  </h2>
			  <br/>
              <p class="leading-relaxed">First, add the following snippet within the head tags of your page. It already includes your org specific ID.</p>
                <pre>
                  <code>
                    &lt;script&gt;
                    {{snippet_code_sample}}
                    &lt;/script&gt;
                  </code>
                </pre>
            <p class="leading-relaxed">Then, wherever you manage a wallet connecting, add the following line of code after a wallet has connected. This allows us to know what wallet connected.</p>
              <pre>
                <code>
                  {{snippet_api_call}}
                </code>
              </pre>
            </div>

            <div>
              <h2 class="text-ralgranite-500 text-xl">Off-Chain Custom Events</h2>
              <p class="leading-relaxed">Now that you've installed the code snippet, you can begin tracking custom events on wallets that have connected to your protocol or dApp.</p>
              <p class="leading-relaxed">An example of a custom event would be <strong>email</strong>. Let's say your dApp requires KYC. When a user fills out KYC and attaches their wallet, you can send an event to raleon to attribute this user's wallet address with their email.</p>
              <pre>
                <code>
                  {{email_custom_event_snippet}}
                </code>
              </pre>
              <p class="leading-relaxed mb-6">This can be done for any event you would like to track. Once you've created a few custom events, you can create segments within Raleon and filter by these events.</p>
              <table class="table-fixed divide-y divide-slate-200" style="margin-bottom: 4rem">
                <thead class="text-xs font-semibold uppercase text-slate-500 bg-slate-50 border-t border-b border-slate-200">
                  <tr class="border-b border-slate-300 mb-2">
                    <th class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap text-left" width="150">Parameter</th>
                    <th class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap text-left" width="150">Required?</th>
                    <th class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap text-left" width="150">Description</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
                      <p class="leading-relaxed font-semibold">event name</p>
                    </td>
                    <td>
                      <span class="italic font-xs">Yes</span>
                    </td>
                    <td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap" width="450">
                      <p class="leading-relaxed">This first parameter is the name of the custom event. Pass this in as a lower-kebab-case string and we'll make it look good for you in in Raleon.</p>
                    </td>
                  </tr>
                  <tr>
                    <td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
                      <p class="leading-relaxed font-semibold">wallet address</p>
                    </td>
                    <td>
                      <span class="italic font-xs">Yes</span>
                    </td>
                    <td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap" width="450">
                      <p class="leading-relaxed">The wallet address to associate the custom event with.</p>
                    </td>
                  </tr>
                  <tr>
                    <td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
                      <p class="leading-relaxed font-semibold">data</p>
                    </td>
                    <td>
                      <span class="italic font-xs">No</span>
                    </td>
                    <td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap" width="450">
                      <p class="leading-relaxed">The data associated with this custom event.</p>
                      <p class="leading-relaxed">If you're sending an <strong>email</strong> custom event, this should be the email address. This can be a string, number, or JSON.</p>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <div>
              <h2 class="text-ralgranite-500 text-xl">Off-Chain Marketing Event Properties</h2>
              <p class="leading-relaxed">Below are a list of attributes our attribution API currently supports tracking.</p>
              <br />
              <table class="table-auto divide-y divide-slate-200">
                <thead class="text-xs
                        font-semibold
                        uppercase
                        text-slate-500
                        bg-slate-50
                        border-t border-b border-slate-200">
                  <tr class="border-b border-slate-300 mb-2">
                    <th class="
                            px-2
                            first:pl-5
                            last:pr-5
                            py-3
                            whitespace-nowrap"
                           width="150">Parameter</th>
                    <th class="
                            px-2
                            first:pl-5
                            last:pr-5
                            py-3
                            whitespace-nowrap" width="150">Description</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
                      <p class="leading-relaxed font-semibold">utm_campaign</p>
                    </td>
                    <td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap" width="450">
                      <p class="leading-relaxed">The campaign name that was passed into the URL that took a user to your project's site. For Example: LaunchCampaign"</p>
                      <br />
                      <span class="italic font-xs">This is required.</span>
                    </td>
                  </tr>

                  <tr>
                    <td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
                      <p class="leading-relaxed font-semibold">utm_source</p>
                    </td>
                    <td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap" width="450">
                      <p class="leading-relaxed">The source that was passed into the URL that took a user to your project’s site. For Example: "Twitter", "Medium", "Discord"</p>
                      <br />
                      <span class="italic font-xs">This is required.</span>
                    </td>
                  </tr>

                  <tr>
                    <td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
                      <p class="leading-relaxed font-semibold">utm_medium</p>
                    </td>
                    <td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap" width="450">
                      <p class="leading-relaxed">The medium that was passed into the URL that took a user to your project’s site. This is the type of link. For Example: "Image", "CPC", "discord-announcement"</p>
                    </td>
                  </tr>

                  <tr>
                    <td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
                      <p class="leading-relaxed font-semibold">utm_content</p>
                    </td>
                    <td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap" width="450">
                      <p class="leading-relaxed">The content name that was passed into the URL that took a user to your project’s site. For Example: "text-ad", "meme", "imageA"</p>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <div>
              <h2 class="text-ralgranite-500 text-xl">Testing Raleon's Attribution Engine</h2>
              <p class="leading-relaxed">Once you have installed the code snippet and hooked into the wallet connect event, we recommend running a test to make sure everything is installed correctly. Running a test is simple, just complete the following steps:</p>
              <ul class="leading-relaxed list-decimal ml-6 mt-4">
                <li class="mb-2">Open up your projects website with the following URL: [your projects URL]?utm_campaign=RaleonTest&utm_source=raleon
                </li>
                <li class="mb-2">Once on your project's site, click "Connect Wallet"</li>
              </ul>
              <p class="leading-relaxed">That's it! It may take 5 to 10 minutes for the event to run through our attribution engine. After waiting 5 to 10 minutes, head to <a href="/campaigns/Overview" class="text-ralpurple-500">Campaigns</a>. If everything is installed correctly, your test campaign should show up.</p>
            </div>
          </div>

        </div>
      </main>

    </div>

  </div>
</template>

<script>
import { ref } from 'vue';
import Header from '../../partials/Header.vue';
import Sidebar from '../../partials/Sidebar.vue';

export default {
  name: 'CampaignHelp',
  components: {
    Sidebar,
    Header,
  },
  setup() {
    amplitude.getInstance().logEvent('CAMPAIGN_GET_STARTED_DOC');
    const orgId = localStorage.getItem('userOrgId');

    const sidebarOpen = ref(false);
    const snippet_code_sample = `
		var script = document.createElement( "script" )
		script.onload = function() {
			let paramData: any = {
			orgId: &lt;your_org_idd&gt;
			applicationId: '', //Utilize this to manage event scope (examples v1, v2, or dev, prod, etc)
			pageLocation: 'landing-page', //Default event sent when someone visits the page
			enableActionPrompts: true/false, //Will enable/disable action prompts
			enableQuests: true/false, //Will enable/disable quests

			//Optional, used to inject UI into a sepcific dom element
			//questDomId: chatPopupDivRefs.chatPopupDiv1.current?.id,
			};
			setupRaleonParametersV2(paramData);
		};

		script.src = 'https://dqpqjbq51w8fz.cloudfront.net/raleon_snippet.min.js';
		document.getElementsByTagName( "head" )[0].appendChild( script );
                      `;

    const snippet_api_call = `raleon.walletConnected(<CONNECTED_WALLET_ADDRESS>)`;
    const email_custom_event_snippet =
    `raleon.registerEvent('email', '******************************************', '<EMAIL>')
                  raleon.registerEvent('minted-nft, '******************************************', '<NFT_Address>')`;
    return {
      sidebarOpen,
      snippet_code_sample,
      snippet_api_call,
      email_custom_event_snippet,
    }
  }
}
</script>
