<template>
  <div class="flex h-screen overflow-hidden">

    <!-- Sidebar -->
    <Sidebar :sidebarOpen="sidebarOpen" @close-sidebar="sidebarOpen = false" />

    <!-- Content area -->
    <div class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden bg-white">

      <!-- Site header -->
      

      <main>

        <!-- Search area -->
        <div class="relative flex flex-col items-center justify-center px-4 sm:px-6 lg:px-8 py-8 lg:py-16 bg-indigo-500 overflow-hidden">
          <!-- Glow -->
          <div class="absolute pointer-events-none" aria-hidden="true">
            <svg width="512" height="512" xmlns="http://www.w3.org/2000/svg">
              <defs>
                <radialGradient cx="50%" cy="50%" fx="50%" fy="50%" r="50%" id="ill-a">
                  <stop stop-color="#FFF" offset="0%" />
                  <stop stop-color="#FFF" stop-opacity="0" offset="100%" />
                </radialGradient>
              </defs>
              <circle style="mix-blend-mode:overlay" cx="588" cy="650" r="256" transform="translate(-332 -394)" fill="url(#ill-a)" fill-rule="evenodd" opacity=".48" />
            </svg>
          </div>
          <!-- Illustration -->
          <div class="absolute pointer-events-none" aria-hidden="true">
            <img src="../../images/profile_banner.png" width="1380" height="361" />
          </div>
          <div class="relative w-full max-w-2xl mx-auto text-center">
            <div class="mb-5">
              <h1 class="text-2xl md:text-3xl text-white font-bold">Dashboard & Charts</h1>
            </div>

                <div class="text-center">
                  <ul class="inline-flex flex-wrap text-sm font-medium">
                    <li class="flex items-center">
                      <a class="text-slate-300 hover:text-indigo-500" href="/support">Support</a>
                      <svg class="h-4 w-4 fill-current text-slate-400 mx-3" viewBox="0 0 16 16">
                        <path d="M6.6 13.4L5.2 12l4-4-4-4 1.4-1.4L12 8z" />
                      </svg>
                    </li>
                    <li class="flex items-center">
                      <a class="text-slate-300 hover:text-indigo-500" href="#">Dashboard & Charts</a>
                    </li>
                  </ul>
                </div>
          </div>
        </div>

        <div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">

          <!-- Sections -->
          <div class="space-y-8">

            <div>
              <h2 class="text-ralgranite-500 text-xl">Overview</h2>
              <img src="../../images/help/DashExample.png" />
              <p class="leading-relaxed">Raleon Dashboards allow you to view all your most important metrics and analysis at a galnce. You can easily add charts and insights from one or multiple projects, track retention, growth, and more on a regular basis.</p>
            </div>

            <div>
              <h2 class="text-ralgranite-500 text-xl">Use Cases</h2>
              <p class="leading-relaxed">Dashboards can be used to keep everyone across your team, or multiple teams, aligned to your company's most important metrics and KPIs.</p>
              <br />
              <p class="leading-relaxed">Dashboards can be used to track high level metrics, or show the deeper behavioral details of a user segment.</p>
              <br />
              <p class="leading-relaxed">Additionally, a dashboard can even be used to track competitors, if their project information is loaded in.</p>
            </div>

            <div>
              <h2 class="text-ralgranite-500 text-xl">Navigating Your Dashboards</h2>
              <img src="../../images/help/DashDirectory.png" />
              <p class="leading-relaxed">Your dashboards can be easily accessed via the Dashboard directory that's in the left-hand navigation.</p>
              <br />
              <p class="leading-relaxed">Dashboards under the "Shared" listing are viewable by all users within your organization. Dashboards under the "Private" listing are only visible to you. You can change the shared status of a dashboard at any time within the Dashboard's settings.</p>
            </div>

            <div>
              <h2 class="text-ralgranite-500 text-xl">Creating a New Dashboard</h2>
              <img src="../../images/help/AddDash.gif" />
              <p class="leading-relaxed">Creating a dashboard is simple. As you can see above, simply expand your Dashboard directory, and then click the "+ New Dashboard" button.</p>
              <br />
              <img src="../../images/help/RenameDash.gif" />
              <p class="leading-relaxed">Once you've clicked "+ New Dashboard", you will immediately be taken to your new, untitled dashboard. You can choose to rename it by using the settings menu, present in the top right corner of the dashboard, as shown above.</p>
            </div>

            <div>
              <h2 class="text-ralgranite-500 text-xl">Adding Charts to Dashboards</h2>
              <p class="leading-relaxed">To add a chart to your dashboard, you may click the "Add Chart" button in the top right corner of the chart. This will open up an optionals dialog where you can specify the details about your chart.</p>
            </div>

            <div>
              <h2 class="text-ralgranite-500 text-xl">User Segments & Address Groups for Charts</h2>
              <p>To make it fast and easy to start reporting on addresses that matter, our platform curates data into specific user segments and address groups. These groups specify the data, from the selected project, to be used as part of the chart.</p>
              <ul class="leading-relaxed list-disc ml-6 mt-4">
                <li class="mb-2">
                  <span class="font-semibold">New Users: </span> Wallets with a first-time interaction with a smart contract in the selected project in the last 7 days.
                </li>
                <li class="mb-2">
                  <span class="font-semibold">Active Users:</span> Wallets that have interacted with a smart contract in the selected project in the last 30 days.
                </li>
                <li class="mb-2">
                  <span class="font-semibold">At Risk Users: </span> Wallets whose last interaction with a smart contract in the selected project is more than 30 days ago, but less than 90 days ago.
                </li>
                <li class="mb-2">
                  <span class="font-semibold">Dormant Users: </span> Wallets whose last interaction with a smart contract in the selected project is more than 90 days ago.
                </li>
                <li class="mb-2">
                  <span class="font-semibold">Token Holders: </span> Wallets that are holding any tokens that are specified to be tracked in the selected project.
                </li>
                <li class="mb-2">
                  <span class="font-semibold">All Users: </span> All unique wallets that have ever interacted with the smart contracts specified in the selected project.
                </li>
              </ul>
            </div>

            <div>
              <h2 class="text-ralgranite-500 text-xl">Data Groups Available in Charts</h2>
              <p class="leading-relaxed">To make understanding, actioning, and reporting on on-chain data easier, our platform automatically generates insights and groups them for you. The type of data you have access to when adding a chart is going to be dependent on the type of chart and address group you want to use, such as a line chart type on active user groups. Different data groups are available to different chart types and address groups. Below are a list of the current data groups:</p>
              <ul class="list-decimal ml-6 leading-relaxed mt-4">
                <li class="mb-2">
                  <span class="font-semibold ">Activities: </span> The activity data grouping represents insights around the kinds of interactions a group of addresses do on-chain. The insights available within activities are currently:
                  <ul class="list-disc ml-4 leading-relaxed mt-2">
                    <li class="mb-2"><span class="font-semibold">Activity Count:</span> This is the number of interactions made.</li>
                    <li class="mb-2"><span class="font-semibold">Categpry:</span>This applies Raleon's insights engine to determine what categories wallets are interacting with on-chain. For example: Exchange, NFT Marketplace, Bridges, and so on.</li>
                    <li class="mb-2"><span class="font-semibold">dApp:</span> This applies Raleon's insights engine to determine what dApps or protocols a wallet is engaging with specifically. For example: Uniswap, CowSwap, and so on.</li>
                    <li class="mb-2"><span class="font-semibold">User Count:</span>This is the count of unique users within an activity.</li>
                    <li class="mb-2"><span class="font-semibold">Time:</span>This would be the period of time over which activity occurs.</li>
                  </ul>
                </li>
                <li class="mb-2">
                  <span class="font-semibold">Persona: </span> A predictive, behavioral classification of the kind of user that has interacted with a smart contract in the project.
                </li>
                <li class="mb-2">
                  <span class="font-semibold">Common Tokens: </span> The most common tokens held by active users of the selected project.
                </li>
                <li class="mb-2">
                  <span class="font-semibold">Project Actions: </span> On-chain interactions users have users have had with the selected project.
                </li>
                <li class="mb-2">
                  <span class="font-semibold">Transactions: </span> The total number of transactions across active users for the selected project.
                </li>
                <li class="mb-2">
                  <span class="font-semibold">Total: </span> Unique count of users based on the address group for the selected project.
                </li>
                <li class="mb-2">
                  <span class="font-semibold">Most Active Time: </span> The most active UTC time of a project's users, currently based on all users.
                </li>
                <li class="mb-2">
                  <span class="font-semibold">User Percent Activity: </span> The engagement or stickiness of users with the selected project, as compared to all their other on-chain activity. For example: An egagement of 8% would mean that 92% of the projects users spend their time elsewhere.
                </li>
                <li class="mb-2">
                  <span class="font-semibold">Top ETH Holder: </span> The address and amount with the highest ETH value across all token holders. Token holders would be any wallet that is holding a token that is specified to be tracked by the selected project.
                </li>
                <li class="mb-2">
                  <span class="font-semibold">Unique Wallets: </span> Count of unique wallets that have interacted with the selected project.
                </li>
                <li class="mb-2">
                  <span class="font-semibold">Token USD Value: </span> USD value, based on ETH, across all token holders for the specified project. IE: Who is your biggest whale of your token holders.
                </li>
                <li class="mb-2">
                  <span class="font-semibold">Average Token Transfers: </span> The average size of token transfers made by users of the selected project, whose transfers contain tokens being tracked for the selected project. This is currently averaged over the last 2 years.
                </li>
                <li class="mb-2">
                  <span class="font-semibold">Returning Users Last 7 Days: </span> In the last 7 days, the number of users that have interacted with and previously interacted with a smart contract being tracked in the selected project.
                </li>
                <li class="mb-2">
                  <span class="font-semibold">Returning Users Last 1 Day: </span> In the last 24 hours, the number of users that have interacted with and previously interacted with a smart contract being tracked in the selected project.
                </li>
              </ul>
              <br />
              <p class="leading-relaxed">You will see the current list of data groups and insights continue to grow over time, so be sure to look out for product updates!</p>
            </div>

            <div>
              <h2 class="text-ralgranite-500 text-xl">An Example</h2>
              <p class="leading-relaxed">If you wanted to add a chart to your dashboard that answered the question of "What categories are my dormant/lost users going to?" you would create a chart with the following:</p>
              <ul class="list-disc ml-6 leading-relaxed mt-4 mb-4">
                <li class="mb-2">Select project whose data you want to use</li>
                <li class="mb-2">Line Chart</li>
                <li class="mb-2">Medium size</li>
                <li class="mb-2">Activities is the data we are looking for</li>
                <li class="mb-2">Dormant users are the addresses we want to use</li>
                <li class="mb-2">Category is what we want to use as the label</li>
                <li class="mb-2">User count is what we want to use as the value</li>
                <li class="mb-2">Date range we'll specify as Last 7 days</li>
              </ul>
              <p class="leading-relaxed">Your chart should look something like the following:</p>
              <img src="../../images/help/LostUserExample.png" />
            </div>
          </div>

        </div>
      </main>

    </div>

  </div>
</template>

<script>
import { ref } from 'vue'
import Header from '../../partials/Header.vue'
import Sidebar from '../../partials/Sidebar.vue'

export default {
  name: 'DashboardHelp',
  components: {
    Sidebar,
    Header,
  },
  setup() {

    const sidebarOpen = ref(false)

    return {
      sidebarOpen,
    }
  }
}
</script>
