<template>
  <div class="flex h-screen overflow-hidden">

    <!-- Sidebar -->
    <Sidebar :sidebarOpen="sidebarOpen" @close-sidebar="sidebarOpen = false" />

    <!-- Content area -->
    <div class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden bg-white">

      <!-- Site header -->
      

      <main>

        <!-- Search area -->
        <div class="relative flex flex-col items-center justify-center px-4 sm:px-6 lg:px-8 py-8 lg:py-16 bg-indigo-500 overflow-hidden">
          <!-- Glow -->
          <div class="absolute pointer-events-none" aria-hidden="true">
            <svg width="512" height="512" xmlns="http://www.w3.org/2000/svg">
              <defs>
                <radialGradient cx="50%" cy="50%" fx="50%" fy="50%" r="50%" id="ill-a">
                  <stop stop-color="#FFF" offset="0%" />
                  <stop stop-color="#FFF" stop-opacity="0" offset="100%" />
                </radialGradient>
              </defs>
              <circle style="mix-blend-mode:overlay" cx="588" cy="650" r="256" transform="translate(-332 -394)" fill="url(#ill-a)" fill-rule="evenodd" opacity=".48" />
            </svg>
          </div>
          <!-- Illustration -->
          <div class="absolute pointer-events-none" aria-hidden="true">
            <img src="../../images/profile_banner.png" width="1380" height="361" />
          </div>
          <div class="relative w-full max-w-2xl mx-auto text-center">
            <div class="mb-5">
              <h1 class="text-2xl md:text-3xl text-white font-bold">Audience Builder</h1>
            </div>

                <div class="text-center">
                  <ul class="inline-flex flex-wrap text-sm font-medium">
                    <li class="flex items-center">
                      <a class="text-slate-300 hover:text-indigo-500" href="/support">Support</a>
                      <svg class="h-4 w-4 fill-current text-slate-400 mx-3" viewBox="0 0 16 16">
                        <path d="M6.6 13.4L5.2 12l4-4-4-4 1.4-1.4L12 8z" />
                      </svg>
                    </li>
                    <li class="flex items-center">
                      <a class="text-slate-300 hover:text-indigo-500" href="#">Audiences</a>
                    </li>
                  </ul>
                </div>
          </div>
        </div>

        <div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">

          <!-- Sections -->
          <div class="space-y-8">

            <div>
              <h2 class="text-ralgranite-500 text-xl">Overview</h2>
              <p class="leading-relaxed">Audiences in Raleon allow you to learn about your users, grow your ideal customers, and track it all automatically.</p>
            </div>

            <div>
              <h2 class="text-ralgranite-500 text-xl">Use Cases</h2>
              <p class="leading-relaxed">Audiences can be used to identify who your super users are.</p>
              <br />
              <p class="leading-relaxed">Audiences can be used to understand how many users you're losing to a competitor.</p>
              <br />
              <p class="leading-relaxed">Audiences can be used to understand what your competitors are doing.</p>
              <br />
              <p class="leading-relaxed">Audiences can be used to identify what customers may be a growth opportunity. For example, you create an audience for users of your project whose net worth is above a certain threshold, and who hold a moonbird. Using our off-chain events, you could then connect it to their e-mail for marketing purposes.</p>
              <br />
              <p class="leading-relaxed">As you can imagine, there are a lot of benefits and flexibility to an audience in Raleon.</p>
            </div>

            <div>
              <h2 class="text-ralgranite-500 text-xl">How Does Audience Builder Work?</h2>
              <p class="leading-relaxed">You can think of an audience as a filter that's applied to the wallets that are associated with the project you build an audience for.</p>
              <br />
              <p class="leading-relaxed">For Example:</p>
              <p class="leading-relaxed">If you have a project in Raleon that has 20,000 unique wallets, you can apply a filter to look for wallets who have interacted with your project in the last 30 days. The result may be out of the 20,000 unique wallets, 5,000 of them have interacted with your project in the last 30 days.</p>
              <br />
              <p class="leading-relaxed">Audiences also apply Raleon's automated tracking and insights. What that means is when you publish a new audience, the audience is dynamic, not static. In other words, because Aadiences are dynamic, today the audience mentioned above might show 5,000 wallets, tomorrow it might show 5,500 wallets. All of this is tracked within Raleon, so not only do you get an automated, rich perspective of what an audience looks like, you can also see how it changes over time.</p>
            </div>

            <div>
              <h2 class="text-ralgranite-500 text-xl">How to Create an Audience </h2>
              <img src="../../images/help/SegmentAdd.gif" class="pb-4 pt-4">
              <p class="leading-relaxed">Creating an audience in Raleon is simple. First, you will navigate to "Audiences" in the product. Next, you can either click on an existing audience, or click "Add Audience"</p>

            <img src="../../images/help/segbuilder.jpg" class="pb-4 pt-4">
            <p class="leading-relaxed">After you've clicked "Add New Audience" you will be taken to the Audience Builder. There's a few areas to be aware of in the Audience Builder.</p>
              <ul class="leading-relaxed list-decimal ml-6 mt-4">
                <li class="mb-2">The Definition tab is where you will create the filters for your audience</li>
                <li class="mb-2">When you're ready to preview the results of your audience, you can do so by clicking "Save & Preview" in the publishing pane</li>
                <li class="mb-2">You can do a deep dive on any wallet by clicking on it</li>
                <li class="mb-2">When you're ready to see a full list of the wallets, you may click "Publish"</li>
              </ul>
              <br />
              <p class="leading-relaxed">As you can see below, once in the audience builder, the rest is easy! You can add multiple filters, hit "Save & Preview" when you want to see how many wallets apply to your audience. You can do this any number of times to experiment and get to just the right audience to track.</p>
              <img src="../../images/help/segmentuse.gif" class="pb-4 pt-4">
            </div>

            <div>
              <h2 class="text-ralgranite-500 text-xl">What is a Draft or Published Audience?</h2>

              <p class="leading-relaxed">Every audience has a status of either Draft or Published.</p>
              <br />
              <p class="leading-relaxed">A "Draft" audience is one whose definition can continue to be edited, and you can run additional previews on.</p>
              <br />
              <p class="leading-relaxed">A "Published" is a audience that becomes locked, meaning its definition cannot be changed. Once an audience is published, the full list of wallets that apply to it are generated, and that audience becomes automatically tracked.</p>
              <br />
              <p class="leading-relaxed">In the future, published audiences will also be able to be used in charts, where draft audiences will not.</p>
              <br />
              <p class="leading-relaxed">If you want to edit a published audience, you may use the "Duplicate" button. Duplicate will create a copy of the audience in a draft state, allowing you to make any adjustments you would like and publish.</p>
              <br />
            </div>

            <div>
              <h2 class="text-ralgranite-500 text-xl">What Are Event Types and Targets in an Audience?</h2>

              <p class="leading-relaxed">Event Types and Targets are part of what drives the intelligence of an audience. We regularly are adding new Event Types to audiences, to allow for deeper audience analysis.</p>

              <p class="leading-relaxed">Below are the list of Event Types we currently support:</p>
              <ul class="leading-relaxed list-disc ml-6 mt-4">
                <li class="mb-2"><span class="font-semibold">First Interaction</span> - This is the first time a wallet has interacted with the project the audience is related to</li>
                <li class="mb-2"><span class="font-semibold">Transaction Count</span> - The number of transactions a wallet has had with a particular target.</li>
                <li class="mb-2"><span class="font-semibold">USD Net Worth</span> - The estimated net worth of a wallet that has interacted with the project the audience is related to</li>
                <li class="mb-2"><span class="font-semibold">Token Count</span> - The number of tokens, by quantity, of a wallet that has interacted with the project the audience is related to</li>
                <li class="mb-2"><span class="font-semibold">Off-Chain Event</span> - If you're using our Off-Chain Event API, any custom events you send to Raleon are immediately available in Audiences.</li>
              </ul>
              <br />
              <p class="leading-relaxed">Below are a list of Targets we currently support:</p>
              <ul class="leading-relaxed list-disc ml-6 mt-4">
                <li class="mb-2"><span class="font-semibold">Raleon Project</span> - This allows you to target wallets in a particular Raleon project list</li>
                <li class="mb-2"><span class="font-semibold">Category</span> - This uses Raleon's intelligence to lets you check activity within particular on-chain categories</li>
                <li class="mb-2"><span class="font-semibold">dApp</span> - This uses Raleon's intelligence to let you check the activity within a particular dApp</li>
                <li class="mb-2"><span class="font-semibold">Address</span> - There may be cases where you want to target a particular NFT, Token, or Smart Contract. Address lets you explicitly target an on-chain address to see if a list of wallets have interacted with it.</li>
              </ul>
            </div>

            <div>
              <h2 class="text-ralgranite-500 text-xl">Example Audiences</h2>

              <p class="leading-relaxed">Sometimes the best way to get a feel for how to create an audience is to see a few examples. Below are a few common audience examples.</p>
              <br />
              <img src="../../images/help/seg1.png" class="pb-4 pt-4">
              <p class="leading-relaxed">First interaction in the last 7 days with our project in Raleon, called “Example Project”</p>

              <img src="../../images/help/seg2.png" class="pb-4 pt-4">
              <p class="leading-relaxed">We want to understand active users, which we define as active with our project in the last 30 days.</p>

              <img src="../../images/help/seg3.png" class="pb-4 pt-4">
              <p class="leading-relaxed">Has interacted with our project in Raleon in the last 30 days and has interacted with a Exchanges in the last 7 days.</p>

              <img src="../../images/help/seg4.png" class="pb-4 pt-4">
              <p class="leading-relaxed">Has at least one moonbird and a net worth above $50,000.</p>

              <img src="../../images/help/seg5.png" class="pb-4 pt-4">
              <p class="leading-relaxed">Users who have transacted with your project once last month, and once this month.</p>
            </div>

          </div>

        </div>
      </main>

    </div>

  </div>
</template>

<script>
import { ref } from 'vue';
import Header from '../../partials/Header.vue';
import Sidebar from '../../partials/Sidebar.vue';

export default {
  name: 'CampaignHelp',
  components: {
    Sidebar,
    Header,
  },
  setup() {
    amplitude.getInstance().logEvent('HELP_SEGMENT_DOC');

    return {
      sidebarOpen
    }
  }
}
</script>
