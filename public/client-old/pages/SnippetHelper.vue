<template>
	<div class="flex h-screen overflow-hidden bg-white">
		<Sidebar :sidebarOpen="sidebarOpen" @close-sidebar="sidebarOpen = false" />

		<div class="scroll-container relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
			<main class="bg-white">
				<div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">
					<div class="sm:flex sm:justify-between sm:items-center mb-8">
						<!-- Left: Title -->
						<div class="mb-4 sm:mb-0">
							<h1 class="text-2xl md:text-3xl text-slate-800 font-bold">
								Snippet Tester
							</h1>
							<p class="text-sm mt-1 text-slate-400">Check to make sure the Raleon snippet is installed and configured on your site correctly.<br >
							<a href="https://docs.raleon.io/docs/raleon-snippet-installation" class="text-ralprimary-main">📖 Learn more about the snippet and how to use it.</a>
							</p>
						</div>
					</div>


					<div class="mb-20">
						<!-- <div class="p-5 pl-0" style="font-weight: bold; font-size: 2em">Snippet Tester</div> -->
						<div class="p-5">
							<label for="url-input" class="mr-4">Enter URL:</label>
							<input id="url-input" type="text" v-model="url"  @keydown.enter="openURL()" class="mr-4" />
							<button @click="openURL" class="bg-gradient-button-primary text-white font-bold text-xs py-2 px-4 ml-2 rounded-button">Verify</button>
						</div>
						<div class="box m-5">
							<div v-for="item in checkerItems" :key="item.id" class="item">
							<label class="custom-checkbox" :class="{ 'failed': item.status === 'failed' }">
								<input type="checkbox" :id="item.id" v-model="item.status" @change="updateStatus(item)">
								<span class="checkmark">
									<span v-if="item.status === 'failed'" class="x-mark"></span>
								</span>
							</label>
							<label :for="item.id">{{ item.text }}</label>
							<div v-if="item.status === 'loading'" class="spinner"></div>
							</div>
						</div>
						<div class="p-5" v-if="failureTitle">
							<div class="p-5" style="font-weight: bold; font-size: 1.5em; background-color: rgb(243, 76, 76); color: white">{{ failureTitle }}</div>
							<p class="mb-5" style="font-weight: bold">{{  failureText }}</p>
							<ul>
								<li v-for="item in failureTips">{{ item }}</li>
							</ul>
						</div>
						<div class="p-5" v-if="successTitle">
							<div class="p-5" style="font-weight: bold; font-size: 1.5em; background-color: rgb(64, 213, 114);">{{ successTitle }}</div>
							<p class="mb-5" style="font-weight: bold">{{  successText }}</p>
						</div>
					</div>
				</div>

				<article class="prose lg:prose-xl mx-auto p-20">


					<h2 class="font-bold text-xl mb-5">Installing The Snippet</h2>
					<p class="mb-4">
						It's very simple to install the snippet. To make things even easier, we have a <a href="https://www.loom.com/share/d038cc764eb74fa292bdb83f47542690" class="text-blue-500 underline">short video</a> on performing the installation.
					</p>

					<h3 class="font-bold text-lg mb-3">Add the following snippet within the head tags of your page:</h3>
					<pre class="p-2 bg-gray-200 text-sm rounded mb-4"><code>
	&lt;script&gt;
		var script = document.createElement( "script" )
		script.onload = function() {
			let paramData: any = {
			orgId: &lt;your_org_idd&gt;
			applicationId: '', //Utilize this to manage event scope (examples v1, v2, or dev, prod, etc)
			pageLocation: 'landing-page', //Default event sent when someone visits the page
			enableActionPrompts: true/false, //Will enable/disable action prompts
			enableQuests: true/false, //Will enable/disable quests

			//Optional, used to inject UI into a sepcific dom element
			//questDomId: chatPopupDivRefs.chatPopupDiv1.current?.id,
			};
			setupRaleonParametersV2(paramData);
		};

		script.src = 'https://dqpqjbq51w8fz.cloudfront.net/raleon_snippet.min.js';
		document.getElementsByTagName( "head" )[0].appendChild( script );

&lt;/scriptd&gt;
					</code></pre>

					<p class="mb-4">
						Then, wherever you manage a wallet connecting, add the following line of code after a wallet has connected. This allows us to know what wallet connected and run our identity resolution.
					</p>

					<pre class="p-2 bg-gray-200 text-sm rounded mb-4"><code>
					raleon.walletConnected(&lt;CONNECTED_WALLET_ADDRESS&gt;)
					</code></pre>

					<h1 class="font-bold text-2xl mb-5">What Is The Raleon Snippet?</h1>
					<p class="mb-4">
						The Raleon Snippet is a very small library that enables all of the off-chain functionality of Raleon on your app or website. Without the Raleon snippet, the following capabilities will not work:
					</p>
					<ul class="list-disc list-inside mb-4">
						<li>Attribution</li>
						<li>Custom Events</li>
						<li>Campaigns</li>
						<li>Identity Resolution</li>
					</ul>
					<p class="mb-4">
						The snippet is designed to be lightweight, privacy friendly, and easy to install.
					</p>

					<h2 class="font-bold text-xl mb-5">What Does The Snippet Track?</h2>
					<p class="mb-4">
						We've intentionally designed the snippet to be privacy friendly. As a result, it does not track anything that would require explicit consent on your site. For instance, it is not tracking details such as IP, device type, browser, and so on.
					</p>
				</article>

			</main>
		</div>
	</div>
</template>

<script>
import { ref } from 'vue'
import * as Utils from '../utils/Utils';
import Sidebar from "../partials/Sidebar.vue"
const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
  name: 'SnippetHelper',
  setup() {
	const sidebarOpen = ref(false);
	return {
		sidebarOpen,
	}
  },
  components: {
	Sidebar
  },
  data() {
    return {
		url: '',
		checkerItems: [{
			id: 'installed',
			text: 'Snippet Installed',
			status: false,
			subtext: '',
		}, {
			id: 'configured',
			text: 'Snippet Configured',
			status: false,
			subtext: ''
		}, {
			id: 'working',
			text: 'Snippet Functioning',
			status: false,
			subtext: ''
		}],
		failureTitle: '',
		failureText: '',
		failureTips: [],
		successTitle: '',
		successText: ''
	};
  },
  async mounted() {},
  methods: {
	async openURL() {
		if (this.url) {
			if (!this.url.startsWith('https://')) {
				this.url = `https://${this.url}`;
			}
			this.failureText = '';
			this.failureTitle = '';
			this.failureTips = '';
			this.successText = '';
			this.successTitle = '';
			this.checkerItems.forEach(x => x.status = false);

			// Open the URL in a new tab
			const adjustedURL = new URL(this.url);
			adjustedURL.hash += '&raleon-snippet-autoverify';
			let newTab = window.open(adjustedURL, '_blank', 'width=50,height=50');

			this.checkerItems.find(x => x.id === 'installed').status = 'loading';

			await new Promise((resolve, reject) => {

				window.addEventListener("message", (event) => {
					if (event.data !== 'installed') {
						return;
					}

						clearTimeout(timeout);
						this.checkerItems.find(x => x.id === 'installed').status = true;
						this.checkerItems.find(x => x.id === 'configured').status = 'loading';
						// clearInterval(interval);

						resolve();
				});

				// const interval = setInterval(() => {
				// 	if (newTab.closed) {
				// 		clearTimeout(timeout);
				// 		this.checkerItems.find(x => x.id === 'installed').status = true;
				// 		this.checkerItems.find(x => x.id === 'configured').status = 'loading';
				// 		clearInterval(interval);

				// 		resolve();
				// 	}
				// }, 500);

				const timeout = setTimeout(() => {
					// clearInterval(interval);

					newTab.close();

					const item = this.checkerItems.find(x => x.id === 'installed');
					item.status = 'failed';
					this.failureTitle = 'Timed Out Waiting For Snippet';
					this.failureText = `Please check the page to make sure the snippet is correctly installed.

					Here are a few common things to check:
					`;
					this.failureTips = [
						'Make sure the URL entered above corresponds with the page(s) that the snippet was installed on',
						'Clear all browser caches related to the page being tested',
						'Clear all server-side caches related to the page being tested',
						'Ensure that the snippet was copied and pasted correctly, and that all quotes and other special characters are correct',
						'Check to see if securitiy policies (like Content Security Policy) are blocking the script from either loading or running',
						'Check to see if there are any errors in the browser console that mention raleon',
					]

					reject();
				}, 10000);
			});


			await new Promise((resolve, reject) => {

				window.addEventListener("message", (event) => {
					if (!event.data.startsWith('configured-')) {
						return;
					}

					clearTimeout(timeout);

					const orgId = event.data.split('-')[1];

					if (orgId != localStorage.getItem('userOrgId')) {

						const item = this.checkerItems.find(x => x.id === 'configured');
						item.status = 'failed';
						this.failureTitle = 'Configuration Appears Invalid';
						this.failureText = `Please check the page to make sure the snippet is correctly installed.

						Here are a few common things to check:
						`;
						this.failureTips = [
							'Ensure that <YOUR_ORG_ID> was replaced in the snippet',
							`Make sure the org id matches your org id (${localStorage.getItem('userOrgId')}, as a number, not a string)`
						]

						return reject();
					}

						this.checkerItems.find(x => x.id === 'installed').status = true;
						this.checkerItems.find(x => x.id === 'configured').status = 'loading';
						// clearInterval(interval);

						resolve();
				});

				// const interval = setInterval(() => {
				// 	if (newTab.closed) {
				// 		clearTimeout(timeout);
				// 		this.checkerItems.find(x => x.id === 'installed').status = true;
				// 		this.checkerItems.find(x => x.id === 'configured').status = 'loading';
				// 		clearInterval(interval);

				// 		resolve();
				// 	}
				// }, 500);

				const timeout = setTimeout(() => {
					// clearInterval(interval);

					newTab.close();

					const item = this.checkerItems.find(x => x.id === 'configured');
					item.status = 'failed';

					this.failureTitle = 'Timed Out Checking Configuration';
					this.failureText = `Please check the page to make sure the snippet is correctly installed.

					Here are a few common things to check:
					`;
					this.failureTips = [
						'Make sure the script is calling setupRaleonParameters()',
						'Check to see if there are any errors in the browser console that mention raleon',
						'Try again in a few minutes',
					]

					reject();
				}, 10000);
				});


			this.checkerItems.find(x => x.id === 'configured').status = true;
			this.checkerItems.find(x => x.id === 'working').status = 'loading';
			const interval2 = setInterval(async () => {
				const response = await fetch(`${Utils.URL_DOMAIN}/onboard/snippet-config-check`, {
					method: "get",
					withCreditentials: true,
					credentials: 'omit',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						"accept": "application/json",
						"Content-Type": "application/json"
					}
				});
				const result = await response.json();

				if (result == true) {
					clearTimeout(timeout2);
					this.checkerItems.find(x => x.id === 'working').status = true;
					clearInterval(interval2);

					localStorage.setItem('snippetVerified', true);

					this.successTitle = `Snippet Test Succeeded`
					this.successText = `It appears the snippet is installed, configured, and working. Start registering custom events to power analytics, or drive engagement with quests and campaigns`;
				}
			}, 500);

			const timeout2 = setTimeout(() => {
				clearInterval(interval2);

				newTab.close();

				const item = this.checkerItems.find(x => x.id === 'working');
				item.status = 'failed';
				this.failureTitle = 'Timed Out Testing Snippet Functionality';
				this.failureText = `Please check the page to make sure the snippet is correctly installed.

				Here are a few common things to check:
				`;
				this.failureTips = [
					'Check to see if there are any errors in the browser console that mention raleon',
					'Try again in a few minutes',
				]

				reject();
			}, 10000);

		}
	}
  },
};
</script>


<style scoped>
.box {
  width: 500px;
  height: auto;
  border: 1px solid black;
  padding: 20px;
}
.item {
  margin-bottom: 10px;
}
.custom-checkbox {
  position: relative;
  padding-left: 35px;
  cursor: pointer;
  user-select: none;
}
.custom-checkbox input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}
.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 25px;
  width: 25px;
  background-color: #eee;
  border-radius: 50%;
}
.custom-checkbox input:checked ~ .checkmark {
  background-color: #6635E6;
}
.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}
.custom-checkbox input:checked ~ .checkmark:after {
  display: block;
}
.custom-checkbox .checkmark:after {
  left: 9px;
  top: 5px;
  width: 8px;
  height: 14px;
  border: solid white;
  border-width: 0 3px 3px 0;
  transform: rotate(45deg);
}

.custom-checkbox.failed .checkmark {
  background-color: red;
}
.custom-checkbox.failed .checkmark:after {
  left: 10px;
  top: 7px;
  width: 5px;
  height: 10px;
  border-width: 3px;
  transform: rotate(45deg);
}
.spinner {
  /* adjust this to style your spinner */
  width: 20px;
  height: 20px;
  border: 5px solid #f3f3f3;
  border-top: 5px solid #3498db;
  border-radius: 50%;
  animation: spin 2s linear infinite;
   	 	 margin-top: -19px;
    margin-left: 3px;
}
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
.x-mark {
  position: relative;
}
.x-mark:before, .x-mark:after {
  position: absolute;
  left: 10px;
  top: 5px;
  content: ' ';
  height: 16px;
  width: 5px;
  background-color: white;
}
.x-mark:before {
  transform: rotate(45deg);
}
.x-mark:after {
  transform: rotate(-45deg);
}

ul {
	list-style-type: square !important;
	list-style: inside;;
}
</style>
