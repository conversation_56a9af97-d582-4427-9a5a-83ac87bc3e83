
<template>
	<div class="flex h-screen overflow-hidden bg-white">
		<!-- Sidebar -->
		<Sidebar :sidebarOpen="sidebarOpen" @close-sidebar="sidebarOpen = false" />

		<!-- Content area -->
		<div class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
			<main>
				<div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">
					<!-- Page header -->
					<div class="sm:flex sm:justify-between sm:items-center mb-6">
						<!-- Left: Title -->

						<div v-if="!isLoaded"></div>
						<!-- Ensures the header row items don't jump around when new things load in -->
						<EditableHeader v-if="isLoaded" :header-text="dashboardName"
							@updated-header="(header) => {dashboardName = header; this.updateDashboard();}" />
						<!-- Right: Actions -->
						<div class="
	                grid grid-flow-col
	                sm:auto-cols-max
	                justify-start
	                sm:justify-end
	                gap-2
	              ">
							<button class="
						btn-sm bg-gradient-button-primary text-white font-bold text-xs py-2 px-4 rounded-button hover:bg-ralprimary-main
	                " aria-controls="add-report-modal" @click.stop="createNewReport" v-if="(devEnabled == 'true' || raleonSupport == 'true') && !isSelfServiceAccount">
								<svg class="w-4 h-4 fill-current opacity-50 shrink-0" viewBox="0 0 16 16">
									<path
										d="M15 7H9V1c0-.6-.4-1-1-1S7 .4 7 1v6H1c-.6 0-1 .4-1 1s.4 1 1 1h6v6c0 .6.4 1 1 1s1-.4 1-1V9h6c.6 0 1-.4 1-1s-.4-1-1-1z" />
								</svg>
								<span class="hidden xs:block ml-2">Add Chart</span>
							</button>
							<div>
								<DropdownEditMenu class="
	                    relative
	                    inline-flex
	                    hover:border
	                    ml-2
	                    border border-slate-100
	                    hover:border-slate-300
	                    rounded-lg
	                  " align="right">
									<li>
										<a class="
											font-medium
											text-sm text-slate-600
											hover:text-slate-800
											flex
											py-1
											px-3
										" href="#" @click.stop="editDashboardModelOpen = true">Settings</a>
									</li>
									<li>
										<a class="
											font-medium
											text-sm text-slate-600
											hover:text-slate-800
											flex
											py-1
											px-3
										" href="#" @click.stop="exportDashboard">Export Dashboard</a>
									</li>
									<li>
										<a class="
											font-medium
											text-sm text-rose-500
											hover:text-rose-600
											flex
											py-1
											px-3
										" href="#" @click.stop="deleteDashboardModelOpen = true">Remove</a>
									</li>
								</DropdownEditMenu>
							</div>
						</div>
					</div>
					<div class="border-b border-ralwhite-line"></div>

					<ChartEditPane :is-open="chartEditPaneToggle" :title="title" @close-edit-pane="$emit('closeEditPane')">
					</ChartEditPane>

					<!-- Start Use Template -->
					<ModalBasic title="Import Dashboard" id="use-template-modal" :modalOpen="useTemplateModelOpen"
						@close-modal="useTemplateModelOpen = false">
						<!-- Modal tabs -->
						<div>
							<!-- First tab -->
							<div name="Import Text">
								<div class="px-5 py-4">
									<div class="text-sm mb-5">
										<div class="space-y-4">
											<div class="mb-5">
												<label class="block text-sm font-medium mb-1" for="role">What data source would you like to use with this template?</label>
												<DropdownFull :clearOnClick="true" :options="getProjects(true)"
													v-on:change="projectChanged" :selectedId="currentProjectIndex" />
											</div>
										</div>
									</div>
								</div>
								<div class="px-5 py-4">
									<div class="text-sm mb-5">
										<div class="space-y-4">
											<div class="mb-5">
												<label class="block text-sm font-medium mb-1" for="role">Import
													JSON:</label>
												<textarea
													class="block w-full border-gray-300 rounded-md shadow-sm mt-1 focus:border-indigo-500 focus:ring-indigo-500 focus:ring-opacity-50"
													rows="5" v-model="importText"></textarea>
											</div>
										</div>
									</div>
								</div>
								<!-- Modal footer -->
								<div class="px-5 py-4">
									<div class="flex flex-wrap justify-end space-x-2">
										<button class="
											btn-sm
											border-slate-200
											hover:border-slate-300
											text-slate-600
										" @click.stop="cancelUseTemplate()">
											Cancel
										</button>
										<button class="btn-sm bg-indigo-500 hover:bg-indigo-600 text-white"
											@click="importTemplate()">
											Import
										</button>
									</div>
								</div>
							</div>
						</div>
					</ModalBasic>

					<!-- Start Data Source Selection -->
					<ModalBasic id="use-template-modal" title="Creating Dashboard from Template" :modalOpen="selectDataSourceOpen"
						@close-modal="selectDataSourceOpen = false">
						<!-- Modal tabs -->
						<div>
							<!-- First tab -->
							<div name="Use Template">
								<div class="px-5 py-4">
									<div class="text-sm mb-5">
										<div class="space-y-4">
											<div class="mb-5">
												<label class="block text-sm font-medium mb-1" for="role">What data source would you like to use with this template?</label>
												<DropdownFull :clearOnClick="true" :options="templateImportList"
													v-on:change="importProjectChanged" :selectedId="currentProjectIndex" />
											</div>
										</div>
									</div>
								</div>
								<!-- Modal footer -->
								<div class="px-5 py-4">
									<div class="flex flex-wrap justify-end space-x-2">
										<button class="
											btn-sm
											border-slate-200
											hover:border-slate-300
											text-slate-600
										" @click.stop="cancelUseTemplate()">
											Cancel
										</button>
										<button class="btn-sm bg-indigo-500 hover:bg-indigo-600 text-white"
											@click="importTemplate()">
											Select Data Source
										</button>
									</div>
								</div>
							</div>
						</div>
					</ModalBasic>

					<ModalBasic id="use-template-modal" :modalOpen="exportTemplateOpen"
						@close-modal="exportTemplateOpen = false" title="Export Dashboard: JSON Structure">
						<textarea
							class="block w-full border-gray-300 h-50vh rounded-md shadow-sm mt-1 focus:border-indigo-500 focus:ring-indigo-500 focus:ring-opacity-50"
							rows="5" v-model="exportText"></textarea>
					</ModalBasic>

					<!-- End Use Template -->

					<div class="">
						<ModalReport id="edit-report-modal" :modalOpen="editReportModelOpen"
							@close-modal="editReportModelOpen = false" title="Let's Work on a Chart">
							<!-- Modal content -->
							<div class="px-10 py-4">
								<div class="mb-5">
									<label :class="{
											'text-md': currentProjectIndex == -1,
											'text-sm': currentProjectIndex != -1,
										}" class="block font-medium mb-1" for="role">Where do you want the data to come from?</label>
									<DropdownFull :clearOnClick="true" :options="getProjects()" v-on:change="projectChanged"
										:selectedId="currentProjectIndex" :overrideName="currentProjectOverrideName" />
								</div>

								<transition enter-active-class="transition ease-in-out duration-200 transform"
									enter-from-class="opacity-0 -translate-y-2" enter-to-class="opacity-100 translate-y-0"
									leave-active-class="transition ease-out duration-100" leave-from-class="opacity-100"
									leave-to-class="opacity-0">
									<div class="mb-5" v-show="currentProjectIndex != -1">
										<label class="block font-medium mb-1" for="role" :class="{
												'text-md': currentReport == -1,
												'text-sm': currentReport != -1,
											}">How do you want to view your data?</label>
										<div class="grid grid-cols-7 rounded-md shadow-sm" role="group">
											<button v-for="chart in getAvailableChartTypes()" :key="chart.id" type="button"
												:class="{
														'border border-indigo-400':
															chart.id === currentReport,
														'border border-slate-200': chart.id !== currentReport,
													}" @click="chartTypeChanged(chart.id)" class="
												inline-flex
												items-center
												m-1
												text-sm
												font-medium
												bg-white
												px-4
												py-6
												rounded
												hover:border-indigo-400
												duration-150
												ease-in-out
												shadow-sm
												">
												<!-- https://remixicon.com/ -->
												<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
													class="h-16 w-24 text-slate-600" stroke="currentColor" stroke-width="1"
													v-if="chart.name === 'Line'">
													<path fill="currentColor" />
													<path fill="currentColor"
														d="M5 3v16h16v2H3V3h2zm15.293 3.293l1.414 1.414L16 13.414l-3-2.999-4.293 4.292-1.414-1.414L13 7.586l3 2.999 4.293-4.292z" />
												</svg>
												<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
													class="h-16 w-24 text-slate-600" stroke="currentColor" stroke-width="1"
													v-if="chart.name === 'Bar'">
													<path fill="currentColor" />
													<path fill="currentColor"
														d="M3 12h2v9H3v-9zm16-4h2v13h-2V8zm-8-6h2v19h-2V2z" />
												</svg>
												<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
													class="h-16 w-24 text-slate-600" stroke="currentColor" stroke-width="1"
													v-if="chart.name === 'Bar Horizontal'">
													<path fill="currentColor" />
													<path fill="currentColor"
														d="M12 3v2H3V3h9zm4 16v2H3v-2h13zm6-8v2H3v-2h19z" />
												</svg>
												<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
													class="h-16 w-24 text-slate-600" stroke="currentColor" stroke-width="1"
													v-if="chart.name === 'Donut'">
													<path fill="currentColor" />
													<path fill="currentColor"
														d="M11 2.05v2.012C7.054 4.554 4 7.92 4 12c0 4.418 3.582 8 8 8 1.849 0 3.55-.627 4.906-1.68l1.423 1.423C16.605 21.153 14.4 22 12 22 6.477 22 2 17.523 2 12c0-5.185 3.947-9.449 9-9.95zM21.95 13c-.2 2.011-.994 3.847-2.207 5.328l-1.423-1.422c.86-1.107 1.436-2.445 1.618-3.906h2.013zM13.002 2.05c4.724.469 8.48 4.226 8.95 8.95h-2.013c-.451-3.618-3.319-6.486-6.937-6.938V2.049z" />
												</svg>
												<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
													class="h-16 w-24 text-slate-600" stroke="currentColor" stroke-width="1"
													v-if="chart.name === 'Table'">
													<path fill="currentColor" />
													<path fill="currentColor"
														d="M4 8h16V5H4v3zm10 11v-9h-4v9h4zm2 0h4v-9h-4v9zm-8 0v-9H4v9h4zM3 3h18a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1z" />
												</svg>
												<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
													class="h-16 w-24 text-slate-600" stroke="currentColor" stroke-width="1"
													v-if="chart.name === 'Number'">
													<path fill="none" d="M0 0h24v24H0z" />
													<path fill="currentColor"
														d="M16 7.5a4 4 0 1 0-8 0H6a6 6 0 1 1 10.663 3.776l-7.32 8.723L18 20v2H6v-1.127l9.064-10.802A3.982 3.982 0 0 0 16 7.5z" />
												</svg>
												<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
													class="h-16 w-24 text-slate-600" stroke="currentColor" stroke-width="1"
													v-if="chart.name === 'Pie'">
													<path fill="currentColor" />
													<path fill="currentColor"
														d="M12 22C6.477 22 2 17.523 2 12c0-4.478 2.943-8.268 7-9.542v2.124A8.003 8.003 0 0 0 12 20a8.003 8.003 0 0 0 7.418-5h2.124c-1.274 4.057-5.064 7-9.542 7zm9.95-9H11V2.05c.329-.033.663-.05 1-.05 5.523 0 10 4.477 10 10 0 .337-.017.671-.05 1zM13 4.062V11h6.938A8.004 8.004 0 0 0 13 4.062z" />
												</svg>
											</button>
										</div>
									</div>
								</transition>
								<transition enter-active-class="transition ease-in-out duration-200 transform"
									enter-from-class="opacity-0 -translate-y-2" enter-to-class="opacity-100 translate-y-0"
									leave-active-class="transition ease-out duration-100" leave-from-class="opacity-100"
									leave-to-class="opacity-0">
									<div class="mb-5" v-show="currentReport != -1">
										<label class="block font-medium mb-1" for="role" :class="{
												'text-md': currentChartSize == -1,
												'text-sm': currentChartSize != -1,
											}">What size chart are you making?</label>
										<ul class="
	                        nav nav-pills
	                        flex flex-col
	                        md:flex-row
	                        flex-wrap
	                        list-none
	                        pl-0
	                        mb-4
	                      " id="insights-options" role="tablist">
											<li v-for="chartSize in getAvailableChartSizes()" class="nav-item"
												@click="chartSizeChanged(chartSize.id)" role="presentation"
												:key="chartSize.id">
												<button class="
	                            inline-flex
	                            items-center
	                            m-1
	                            py-2
	                            px-4
	                            text-sm
	                            font-medium
	                            border-2
	                            bg-transparent
	                            text-slate-600
	                            rounded-l-lg rounded-r-lg
	                            hover:border-indigo-400
	                            duration-150
	                            ease-in-out
	                            focus:z-10
	                          " :class="{
	                          		'border border-indigo-400':
	                          			chartSize.id === currentChartSize,
	                          		'border border-slate-200':
	                          			chartSize.id !== currentChartSize,
	                          	}" id="pills-home-tab" data-bs-toggle="pill" data-bs-target="#pills-home" role="tab"
													aria-controls="{{chartSize.name}}" aria-selected="true">
													{{ chartSize.name }}
												</button>
											</li>
										</ul>
									</div>
								</transition>

								<transition enter-active-class="transition ease-in-out duration-200 transform"
									enter-from-class="opacity-0 -translate-y-2" enter-to-class="opacity-100 translate-y-0"
									leave-active-class="transition ease-out duration-100" leave-from-class="opacity-100"
									leave-to-class="opacity-0">
									<div class="mb-5" v-show="currentChartSize != -1">
										<label class="block text-sm font-medium mb-1" for="role">What type of data are you
											looking for?</label>
										<ul class="
	                        nav nav-pills
	                        flex flex-col
	                        md:flex-row
	                        flex-wrap
	                        list-none
	                        pl-0
	                        mb-4
	                      " id="insights-options" role="tablist">
											<li v-for="insight in getInsightOptionsBasedOnSelections()" class="nav-item"
												@click="dimensionChanged(insight.id)" role="presentation" :key="insight.id">
												<div class="relative" @mouseenter="insightsHover(insight.id)"
													@mouseleave="chartHovering[insight.id] = false; chartOpenTip[insight.id] = false">
													<button class="
	                            inline-flex
	                            items-center
	                            m-1
	                            py-2
	                            px-4
	                            text-sm
	                            font-medium
	                            border-2
	                            bg-transparent
	                            text-slate-600
	                            rounded-l-lg rounded-r-lg
	                            hover:border-indigo-400
	                            duration-150
	                            ease-in-out
	                            focus:z-10
	                          " :class="
	                          	{
	                          		'border border-indigo-400':
	                          		insight.id === currentDimension,
	                          			'border border-slate-200':
	                          		insight.id !== currentDimension,
	                          	                          	                          	                          }
	                          " role="tab" aria-controls="{{insight.friendlyName}}" aria-selected="true">
														{{ insight.friendlyName }}
													</button>
													<!-- Tooltip in chart -->
													<div
														class="z-10 absolute bottom-full left-1/2 transform -translate-x-1/2">
														<transition
															enter-active-class="transition ease-out duration-200 transform"
															enter-from-class="opacity-0 -translate-y-2"
															enter-to-class="opacity-100 translate-y-0"
															leave-active-class="transition ease-out duration-200"
															leave-from-class="opacity-100" leave-to-class="opacity-0">
															<div v-show="chartOpenTip[insight.id]"
																class="rounded overflow-hidden min-w-56 p-3 mt-2 bg-slate-800 bg-white border border-slate-200 shadow-lg">
																<div class="text-sm font-medium text-slate-200">
																	{{ insight.tooltip }}
																</div>
															</div>
														</transition>
													</div>
													<!-- end tooltip -->
												</div>
											</li>
										</ul>
									</div>
								</transition>

								<transition enter-active-class="transition ease-in-out duration-200 transform"
									enter-from-class="opacity-0 -translate-y-2" enter-to-class="opacity-100 translate-y-0"
									leave-active-class="transition ease-out duration-100" leave-from-class="opacity-100"
									leave-to-class="opacity-0">
									<div class="mb-5" v-show="currentDimension != -1 && isEditWidgetAProject">
										<label class="block text-sm font-medium mb-1" for="role">What group of addresses do
											you want to use?</label>
										<DropdownFull :options="getAvailableDataGroups()" v-on:change="dataGroupChanged"
											:selectedId="currentGroup" />
									</div>
								</transition>

								<transition enter-active-class="transition ease-in-out duration-200 transform"
									enter-from-class="opacity-0 -translate-y-2" enter-to-class="opacity-100 translate-y-0"
									leave-active-class="transition ease-out duration-100" leave-from-class="opacity-100"
									leave-to-class="opacity-0">
									<div class="mb-5" v-show="
										(isEditWidgetAProject && currentChartDefinition.labels_needed && currentGroup != -1) ||
											(currentDimension != - 1 && !isEditWidgetAProject && currentChartDefinition.labels_needed)
									">
										<label class="block text-sm font-medium mb-1" for="role">Which field do you want to
											use as the label?</label>
										<DropdownFull :clearOnClick="true" :options="getAvailableFieldOptionsLabels()"
											v-on:change="labelOptionChanged" :selectedId="currentLabelOption" />
									</div>
								</transition>

								<transition enter-active-class="transition ease-in-out duration-200 transform"
									enter-from-class="opacity-0 -translate-y-2" enter-to-class="opacity-100 translate-y-0"
									leave-active-class="transition ease-out duration-100" leave-from-class="opacity-100"
									leave-to-class="opacity-0" v-for="data in currentNeededDataPoints" :key="data.id">
									<div class="mb-5" v-show="
										(((!currentChartDefinition.labels_needed &&
											currentGroup != -1) || currentLabelOption != -1) && isEditWidgetAProject) ||
											((!currentChartDefinition.labels_needed && !isEditWidgetAProject && currentChartSize != -1 && currentDimension != -1) || currentLabelOption != -1)
									">
										<label class="block text-sm font-medium mb-1" for="role">Which field do you want to
											use as the value?</label>
										<DropdownFull :clearOnClick="true" :options="getAvailableFieldOptionsData()"
											v-on:change="dataOptionChanged($event, data.id)"
											:selectedId="currentDataOption[data.id]" />
									</div>
								</transition>

								<div class="mb-5" v-show="
									currentChartDefinition.time_supported &&
										currentChartSize != -1 &&
										currentChartSize != -1 &&
										(currentGroup != -1 || !isEditWidgetAProject)
								">
									<label class="block text-sm font-medium mb-1" for="role">What Type of Time Range Do you Want?</label>
									<DropdownFull :clearOnClick="true" :options="timeSelectionOptions"
										v-on:change="toggleTimeSelection" :selectedId="currentTimeSelectionId"/>

									<div v-if="timeSelectionType === this.timeSelectionOptions[0].name">
										<label class="block text-sm font-medium mb-1" for="role">What date range do you want
											to see the data over?</label>
										<DropdownFull :clearOnClick="true" :options="getAvailableTimeRanges()"
											v-on:change="timeChanged" :selectedId="currentTime" />
									</div>

									<div v-else>
										<label class="block text-sm font-medium mb-1" for="stop">Select Your Date Range</label>
										<Datepicker v-model="dateRange" />
									</div>
								</div>

								<transition enter-active-class="transition ease-in-out duration-200 transform"
									enter-from-class="opacity-0 -translate-y-2" enter-to-class="opacity-100 translate-y-0"
									leave-active-class="transition ease-out duration-100" leave-from-class="opacity-100"
									leave-to-class="opacity-0">
									<div class="mb-5"
										v-show="(currentGroup != -1 && isEditWidgetAProject) || (currentDimension != -1 && currentChartSize != -1 && currentChartSize != -1 && !isEditWidgetAProject)">
										<label class="block text-sm font-medium mb-1" for="name">What do you want to call
											this chart?</label>
										<input id="widget-name" class="form-input w-full" type="text"
											placeholder="Enter Name For Chart" v-model="currentWidgetName" />
									</div>
								</transition>
							</div>
							<!-- Modal footer -->
							<div class="px-5 py-4 border-t border-slate-200">
								<div class="flex flex-wrap justify-end space-x-2">
									<button class="
	                      btn-sm
	                      border-slate-200
	                      hover:border-slate-300
	                      text-slate-600
	                    " @click.stop="editReportModelOpen = false">
										Cancel
									</button>
									<button class="
	                      btn-sm
	                      bg-indigo-500
	                      hover:bg-indigo-600
	                      text-white
	                      disabled:bg-indigo-100
	                    " :disabled="
	                    	(((currentGroup == -1 && isEditWidgetAProject) ||
	                    		currentDimension == -1 ||
	                    		(currentChartDefinition.labels_needed &&
	                    			currentLabelOption == -1) ||
	                    		currentDataOption.length == 0 ||
	                    		currentDataOption[0] == -1))
	                    " @click.stop="bundleAndSaveReport">
										{{ reportModalLabel }}
									</button>
								</div>
							</div>
						</ModalReport>

						<ModalBasic id="view-api-modal" :modalOpen="viewAPIModalOpen"
							@close-modal="viewAPIModalOpen = false" title="API call for this metric">
							<div v-if="apiData" class="modal-body">
								<div class="copy-message">URL Copied to Clipboard!</div>
								<div class="api-url-box">
									<textarea readonly v-model="apiData"></textarea>
								</div>
							</div>
						</ModalBasic>

						<!-- Edit Dashboard -->
						<ModalBasic id="edit-dashboard-modal" :modalOpen="editDashboardModelOpen"
							@close-modal="editDashboardModelOpen = false" title="Edit Your Dashboard Details">
							<!-- Modal content -->
							<div class="px-5 py-4">
								<div class="text-sm mb-5">
									<form>
										<div class="space-y-4">
											<div>
												<label class="block text-sm font-medium mb-1" for="name">Name</label>
												<input id="wallet-address" class="form-input w-full" type="text"
													placeholder="Type something you will recognize..."
													v-model="dashboardName" />
											</div>
											<div>
												<label class="block text-sm font-medium mb-1"
													for="large">Description</label>
												<input id="large" class="form-input w-full px-4 py-3" type="text"
													v-model="dashboardDescription" />
											</div>

											<div>
												<label class="block text-sm font-medium mb-1" for="large">Make this
													dashboard visible to everyone in your org?</label>
												<div class="m-3 w-24">
													<div class="flex items-center mt-5">
														<div class="form-switch">
															<input type="checkbox" id="dashboardPrivacy" class="sr-only"
																v-model="dashboardPrivacy" true-value="Shared"
																false-value="Private" v-if="permissionCheck" />
															<input type="checkbox" id="dashboardPrivacy" class="sr-only"
																v-model="dashboardPrivacy" true-value="Shared"
																false-value="Private" disabled v-if="!permissionCheck" />
															<label class="bg-slate-400" for="dashboardPrivacy">
																<span class="bg-white shadow-sm" aria-hidden="true"></span>
																<span class="sr-only">Toggle</span>
															</label>
														</div>
														<div class="text-sm text-slate-400 italic ml-2">{{ dashboardPrivacy
															}}
														</div>
													</div>
												</div>
											</div>

											<div class="pt-4">
												<label class="block text-xs italic" for="large">Created by
													{{ dashboardOwnerName }}</label>
											</div>
										</div>
									</form>
								</div>
							</div>
							<!-- Modal footer -->
							<div class="px-5 py-4 border-t border-slate-200">
								<div class="flex flex-wrap justify-end space-x-2">
									<button class="
	                      btn-sm
	                      border-slate-200
	                      hover:border-slate-300
	                      text-slate-600
	                    " @click.stop="clearEditDashboard()">
										Cancel
									</button>
									<button class="btn-sm bg-indigo-500 hover:bg-indigo-600 text-white"
										@click="updateDashboard()">
										Save Changes
									</button>
								</div>
							</div>
						</ModalBasic>
						<!-- End Edit Dashboard Data -->

						<ModalBlank id="delete-dashboard-modal" :modalOpen="deleteDashboardModelOpen"
							@close-modal="deleteDashboardModelOpen = false">
							<div class="p-5 flex space-x-4">
								<!-- Icon -->
								<div class="
	                    w-10
	                    h-10
	                    rounded-full
	                    flex
	                    items-center
	                    justify-center
	                    shrink-0
	                    bg-rose-100
	                  ">
									<svg class="w-4 h-4 shrink-0 fill-current text-rose-500" viewBox="0 0 16 16">
										<path
											d="M8 0C3.6 0 0 3.6 0 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8zm0 12c-.6 0-1-.4-1-1s.4-1 1-1 1 .4 1 1-.4 1-1 1zm1-3H7V4h2v5z" />
									</svg>
								</div>
								<!-- Content -->
								<div>
									<!-- Modal header -->
									<div class="mb-2">
										<div class="text-lg font-semibold text-slate-800">
											{{ deleteDashboardTitle }}
										</div>
									</div>
									<!-- Modal content -->
									<div class="text-sm mb-10">
										<div class="space-y-2">
											<p>
												Are you sure you want to remove your dashboard "{{
												dashboardName
												}}"? This cannot be undone.
											</p>
										</div>
									</div>
									<!-- Modal footer -->
									<div class="flex flex-wrap justify-end space-x-2">
										<button class="
	                        btn-sm
	                        border-slate-200
	                        hover:border-slate-300
	                        text-slate-600
	                      " @click.stop="deleteDashboardModelOpen = false">
											Cancel
										</button>
										<button class="btn bg-rose-500 hover:bg-rose-600 text-white"
											@click="deleteDashboard()">
											Remove Dashboard
										</button>
									</div>
								</div>
							</div>
						</ModalBlank>
						<!-- End Delete Dashboard -->

						<!-- Start Delete Widget -->
						<ModalBlank id="delete-widget-modal" :modalOpen="deleteWidgetModelOpen"
							@close-modal="deleteWidgetModelOpen = false">
							<div class="p-5 flex space-x-4">
								<!-- Icon -->
								<div class="
	                    w-10
	                    h-10
	                    rounded-full
	                    flex
	                    items-center
	                    justify-center
	                    shrink-0
	                    bg-rose-100
	                  ">
									<svg class="w-4 h-4 shrink-0 fill-current text-rose-500" viewBox="0 0 16 16">
										<path
											d="M8 0C3.6 0 0 3.6 0 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8zm0 12c-.6 0-1-.4-1-1s.4-1 1-1 1 .4 1 1-.4 1-1 1zm1-3H7V4h2v5z" />
									</svg>
								</div>
								<!-- Content -->
								<div>
									<!-- Modal header -->
									<div class="mb-2">
										<div class="text-lg font-semibold text-slate-800">
											Remove Chart?
										</div>
									</div>
									<!-- Modal content -->
									<div class="text-sm mb-10">
										<div class="space-y-2">
											<p>
												Are you sure you want to remove this chart? This cannot
												be undone.
											</p>
										</div>
									</div>
									<!-- Modal footer -->
									<div class="flex flex-wrap justify-end space-x-2">
										<button class="
	                        btn-sm
	                        border-slate-200
	                        hover:border-slate-300
	                        text-slate-600
	                      " @click.stop="deleteWidgetModelOpen = false">
											Cancel
										</button>
										<button class="btn bg-rose-500 hover:bg-rose-600 text-white"
											@click="deleteWidget()">
											Remove Chart
										</button>
									</div>
								</div>
							</div>
						</ModalBlank>
						<!-- End Delete Dashboard -->

						<!-- Cards -->
						<div class="grid grid-cols-12 gap-6 mt-8" v-if="widgetCount > 0">
							<UniversalLister v-for="widget in widgetList" :key="widget.key" :widget="widget"
								:ribbonBadge="getRibbonBadge(widget)" :isMissingDataSource="getIsMissingDataSource(widget)"
								:ribbonBadgeHref="getRibbonBadgeHref(widget)" :hoverable="true"
								@top-edit-Widget="editWidget" @top-remove-Widget="removeWidget"
								@top-refresh-Widget="refreshWidget" @top-view-Api="viewAPI" />
						</div>

						<!-- Empty State -->
						<div class="m-auto mt-4" v-else-if="widgetCount == 0 && initialLoad">

							<div class="text-left">
								<h1 class="text-xl mb-4">Create dashboard from a template</h1>
								<p class="text-md text-gray-600">Using Raleon team's expertise and common trends, we've pre-created dashboard templates ready for your use. Looking for a more personalized dashboard? We're just a message away and ready to assist.</p>
							</div>

							<div class="mt-8" v-for="template in templates" :key="template.id">
								<div class="relative overflow-hidden rounded-lg border border-ralprimary-ultralight shadow-md hover:cursor-pointer hover:shadow-2xl transition duration-500 flex items-center group mt-4"
								@click.stop="useTemplateFromList(template.id)" v-if="(!selfService && areAnyProjectsWithConnections) || template.dataSource == 'audience'">
									<div class="flex-none p-4">
										<img class="h-32 object-cover rounded-lg" :src="getTemplateImage(template.image)" alt="Image description" />
									</div>
									<div class="p-6 flex-grow">
										<h2 class="mb-2 font-semibold">{{template.title}}</h2>
										<p class="text-sm text-gray-600 mb-4">{{template.description}}</p>
										<p class="text-xs text-yellow-700 italic mb-2" v-if="template.info != ''">{{ template.info}}</p>
										<div class="text-xs bg-ralinfo-light text-ralinfo-dark font-semibold py-1 px-4 rounded-full inline-block lowercase">
											{{template.dataSource}}
										</div>
									</div>
								</div>

							</div>

							<div class="text-center px-4 mt-8">
								<button class="
	                    btn-xs
	                    border-slate-200
	                    hover:border-ralgranite-300
	                    text-ralgranite-300
	                    hover:text-ralgranite-400
	                    bg-white
	                    ml-4
	                  " @click.stop="useTemplateModelOpen = true">
									Import a Dashboard
								</button>
							</div>
						</div>
						<!-- End -->
					</div>
				</div>
			</main>
		</div>
	</div>


	<ModalBasic id="snippet-modal" :fitContent="true" :modalOpen="isSnippetModalOpen" @close-modal="isSnippetModalOpen = false" title="Snippet">
		<div class="flex flex-col">
            <!-- Modal content -->
            <div class="px-5 py-4 flex-grow" style="min-width: 80vw; min-height: 80vh">
              <h1 style="font-size: 5em; font-weight: bold; text-align: center">Hook It All Up</h1>
			  <h4 style="font-size: 3em;">Benefits go brrr</h4>
			  <p>Now that you've got your first dashboard, integrate the Raleon snippet in order to get live event-driven data from your site right in your dashboards</p>
			  <img src="https://i.giphy.com/media/np0ar0Kk1nOBor3kku/giphy.webp"/>
            </div>
            <!-- Modal footer -->
            <div class="px-5 py-4 border-t border-slate-200">
              <div class="flex flex-wrap justify-end space-x-2">
                <button class="btn-sm border-slate-200 hover:border-slate-300 text-slate-600" @click.stop="isSnippetModalOpen = false">Cancel</button>
                <a class="btn-sm bg-indigo-500 hover:bg-indigo-600 text-white" href="/snippet">Get Started</a>
              </div>
            </div>
		</div>
	</ModalBasic>
</template>


<style>
.modal-body {
	padding: 24px;
}

.api-url-box {
	display: flex;
	align-items: center;
	margin-bottom: 16px;
}

.api-url-box textarea {
	width: 100%;
	height: 120px;
	/* Adjust this value to make the textarea taller */
	border: none;
	background-color: #f1f1f1;
	padding: 8px;
	font-family: monospace;
	overflow-x: auto;
	resize: none;
}

.copy-btn {
	margin-left: 16px;
	padding: 8px;
	background-color: #4CAF50;
	color: white;
	border: none;
	border-radius: 4px;
	cursor: pointer;
}

.copy-btn:hover {
	background-color: #3e8e41;
}

.copy-message {
	background-color: #4CAF50;
	color: white;
	padding: 8px;
	border-radius: 4px;
	text-align: center;
	margin-bottom: 16px;
}
</style>

<script>
import { ref } from 'vue';
import DropdownEditMenu from '../components/DropdownEditMenu.vue';
//Import our Universal components ahead of time
//Then iterate over our list, using an IF to load the right component, passing in its respective details
import DropdownFull from '../components/DropdownFull.vue';
import DropdownMulti from '../components/DropdownMulti.vue';
import EditableHeader from '../components/EditableHeader.vue';
import ModalAction from '../components/ModalAction.vue';
import ModalBasic from '../components/ModalBasic.vue';
import ModalBlank from '../components/ModalBlank.vue';
import ModalCookies from '../components/ModalCookies.vue';
import ModalReport from '../components/ModalReport.vue';
import Header from '../partials/Header.vue';
import Sidebar from '../partials/Sidebar.vue';
import UniversalLister from '../partials/UniversalLister.vue';
import ChartEditPane from '../partials/analytics/ChartEditPane.vue';
import Datepicker from '../components/Datepicker.vue'
import {
	deleteDashboardById,
	getDashboardById,
	getDashboardsByOrgId,
	getWidgetsByDashboardId,
	updateDashboardById
} from '../services/dashboard';
import { getAvailableProjects, getAvailableCustomMetrics, getAvailableAudiences } from '../services/project';
import {
	CHART_DATA_GROUP_TYPE,
	CHART_TYPE,
	getAPIMapFromAPIName,
	getAPIURL,
	getChartDefinition,
	getChartSizes,
	getFieldOptions,
	getFieldOptionsCustomMetric,
	getInsightOptionsByChart,
	getReportChartGroupList,
	getReportChartGroupListCustomMetric,
	getReportChartTypeList,
	getReportTimeRange
} from '../services/reportbuilder.js';
import {
	getWidgetSize,
	copyTextToClipboard,
	getWidgetSizeFromNumber,
	dashboardTemplates,
	URL_DOMAIN,
	uuidv4
} from '../utils/Utils';

//Images for dashboard templates
import template_projectoverview from '../images/template_projectoverview.png'
import template_behaviorinsights from '../images/template_behaviorinsights.png'
import template_retention from '../images/template_retention.png'
import template_walletinsights from '../images/template_walletinsights.png'
import template_potentialusers from '../images/template_potentialusers.png'
import template_whaleanalysis from '../images/template_projectoverview.png'
import template_nftlist from '../images/template_nftlist.png'

export default {
	name: 'e2eusers',
	props: ['dashboardId'],
	components: {
		Sidebar,
		Header,
		DropdownFull,
		DropdownMulti,
		DropdownEditMenu,
		EditableHeader,
		ModalBasic,
		ModalCookies,
		ModalBlank,
		ModalAction,
		UniversalLister,
		ModalReport,
		ChartEditPane,
		Datepicker,
		template_projectoverview,
		template_behaviorinsights,
		template_retention,
		template_walletinsights,
		template_potentialusers,
		template_whaleanalysis,
		template_nftlist,
	},
	setup() {
		const sidebarOpen = ref(false);
		const editReportModelOpen = ref(false);
		const editDashboardModelOpen = ref(false);
		const viewAPIModalOpen = ref(false);
		const deleteDashboardModelOpen = ref(false);
		const deleteWidgetModelOpen = ref(false);
		const useTemplateModelOpen = ref(false);
		const exportTemplateOpen = ref(false);
		const apiData = ref("");

		const chartEditPaneToggle = ref(false);
		const selectDataSourceOpen = ref(false);

		const devEnabled = localStorage.getItem('devEnabled');
		const raleonSupport = localStorage.getItem('raleonSupport');

		return {
			sidebarOpen,
			editReportModelOpen,
			editDashboardModelOpen,
			viewAPIModalOpen,
			apiData,
			deleteDashboardModelOpen,
			deleteWidgetModelOpen,
			devEnabled,
			useTemplateModelOpen,
			selectDataSourceOpen,
			raleonSupport
		};
	},
	data() {
		return {
			timeSelectionType: 'Dynamic',
			currentTimeSelectionId: 1,
			timeSelectionOptions: [
				{ id: 1, name: 'Dynamic' },
				{ id: 2, name: 'Fixed (Start and Stop)' }
			],
			dateRange: null,
			templateSelection: {},
			templateSelectionId: -1,
			chartOpenTip: [],
			chartHovering: [],
			dashboardName: '',
			dashboardDescription: '',
			dashboardPrivacy: 'Private',
			dashboardShared: 0,
			dashboardOwnerId: -1,
			dashboardOwnerName: '',
			widgetList: [],
			initialLoad: false,
			currentReport: -1,
			currentEditStep: 0,
			currentGroup: -1,
			currentDimension: -1,
			currentTime: 0,
			currentChartSize: -1,
			inEditMode: false,
			widgetEditId: 0,
			currentProjectId: '',
			selectedProject: {},
			currentProjectIndex: -1,
			currentProjectOverrideName: '',
			currentWidgetName: '',
			isEditWidgetAProject: false,
			areAnyProjectsWithConnections: false,
			isSnippetModalOpen: false,
			exportText: '',
			insightOptions: [
				{
					id: 0,
					name: 'NONE',
					friendlyName: 'NONE',
				},
			],
			currentLabelOption: -1,
			currentDataOption: [],
			currentAvailableTimeOptions: getReportTimeRange(),
			currentAvailableChartSizes: getChartSizes(),
			currentAvailableChartTypes: getReportChartTypeList(),
			currentAvailableChartGroups: [],
			currentAvailableFieldOptionsData: [
				{
					id: 0,
					name: 'NONE',
				},
			],
			currentAvailableFieldOptionsLabel: [
				{
					id: 0,
					name: 'NONE',
				},
			],
			currentChartDefinition: getChartDefinition(CHART_TYPE.BAR),
			currentNeededDataPoints: [],

			reportProjectList: [
				{
					id: 0,
					name: 'SuperRare',
				},
			],
			templateImage: '',
			templateImportType: '',
			templateImportList: [],
			projectsOnlyList: [],
			audienceOnlyList: [],
			customMetrics: [],
			importText: '',
			templates: dashboardTemplates
		};
	},
	computed: {
		selfService() {
			return location.search.includes('self-service=true');
		},
		isSelfServiceAccount() {
			return localStorage.getItem('selfService') == 'true';
		},
		templateDescription() {
			if (this.templateSelection.description == null)
				return '';
			else
				return this.templateSelection.description;
		},
		permissionCheck() {
			let userInfo = JSON.parse(localStorage.getItem('userInfo'));
			if (userInfo.id != this.dashboardOwnerId)
				return false;
			else
				return true;
		},
		widgetCount() {
			if (this.widgetList.length > 0) return this.widgetList.length;
			else return 0;
		},
		deleteDashboardTitle() {
			return 'Remove ' + this.dashboardName + '?';
		},
		reportModalLabel() {
			amplitude.getInstance().logEvent('SAVE_WIDGET');
			if (this.inEditMode) return 'Save Changes';
			else return 'Add Report';
		},
	},
	methods: {
		getTemplateImage(imageName) { //Yes, I know this is janky. vue made me do it.
			if(imageName == 'template_projectoverview')
				return template_projectoverview;
			else if(imageName == 'template_behaviorinsights')
				return template_behaviorinsights;
			else if(imageName == 'template_retention')
				return template_retention;
			else if(imageName == 'template_walletinsights')
				return template_walletinsights;
			else if(imageName == 'template_potentialusers')
				return template_potentialusers;
			else if(imageName == 'template_whaleanalysis')
				return template_whaleanalysis;
			else if(imageName == 'template_nftlist')
				return template_nftlist;
		},
		toggleTimeSelection(selection) {
			const selectedOption = this.timeSelectionOptions.find(option => option.id === selection);
    		this.timeSelectionType = selectedOption.name;
			this.currentTimeSelectionId = selection;
		},
		async updateDashboard() {
			amplitude.getInstance().logEvent('UPDATE_DASHBOARD');

			if (this.dashboardPrivacy == 'Shared')
				this.dashboardShared = 1;
			else
				this.dashboardShared = 0;

			const result = await updateDashboardById(
				this.dashboardId,
				this.dashboardName || '🎁 Untitled',
				this.dashboardDescription,
				this.dashboardShared
			);
			this.editDashboardModelOpen = false;
			this.$root.$refs.Sidebar.getDashboardList();
		},
		clearEditDashboard() {
			this.editDashboardModelOpen = false;
		},
		async deleteDashboard() {
			amplitude.getInstance().logEvent('DELETE_DASHBOARD');
			const result = await deleteDashboardById(this.dashboardId);
			this.deleteDashboardModelOpen = false;

			//Delete widgets from this dash
			const wresult = await getWidgetsByDashboardId(this.dashboardId);

			for (var i = 0; i < wresult.length; i++) {
				let url = `${URL_DOMAIN}/widgets/${wresult[i].id}`;
				// Default options are marked with *
				const response = await fetch(url, {
					method: 'DELETE',
					credentials: 'omit',
					headers: {
						Authorization: `Bearer ${localStorage.getItem('token')}`,
						'Content-Type': 'application/json',
					},
				});
			}

			//Take us to our first dashboard in the list
			const dashboardResult = await getDashboardsByOrgId(
				`${localStorage.getItem('userOrgId')}`,
			);

			var firstDash = -1;
			if (dashboardResult[0].id >= 0) firstDash = dashboardResult[0].id;

			this.$router.push({
				name: 'UniversalDashboard',
				params: { dashboardId: firstDash },
			});
			this.$root.$refs.Sidebar.getDashboardList();
		},
		async viewAPI(apiURL) {
			console.log('Viewing API: ' + apiURL);
			this.apiData = apiURL;
			copyTextToClipboard(apiURL);
			this.viewAPIModalOpen = true;
		},
		async refreshWidget(val) {
			console.log('Refreshing Widget: ' + val);

			let refreshWidgetData = {};
			for (var i = 0; i < this.widgetList.length; i++) {
				if (this.widgetList[i].id == val) {
					console.log('Found Widget To Edit');
					refreshWidgetData = this.widgetList[i];
					console.log('WidgetEdition: ' + JSON.stringify(refreshWidgetData));
				}
			}

			if (!refreshWidgetData.datasource) {
				console.log('No Data Source, Skipping Refresh');
				return;
			}

			let audience = refreshWidgetData.projectId;
			console.log('Audience/projectId: ' + audience);

			let res = {};
			if (refreshWidgetData.datasource == 'segment' || refreshWidgetData.datasource == 'project') {
				let apiName = refreshWidgetData.apiName;
				let apiInfo = getAPIMapFromAPIName(apiName);
				console.log('API Info: ' + JSON.stringify(apiInfo));
				if (!apiInfo || !apiInfo.definition) {
					console.log('No API Info, Skipping Refresh');
					return;
				}

				let url = `${URL_DOMAIN}/metric/refresh/${apiInfo.definition}/${audience}`;
				let metricRefreshResponse = await fetch(url, {
					method: 'GET',
					credentials: 'omit', // include, *same-origin, omit
					headers: {
						Authorization: `Bearer ${localStorage.getItem('token')}`,
						'Content-Type': 'application/json',
					}
				});
				res = await metricRefreshResponse.json();
			}
			else if (refreshWidgetData.datasource == 'custom-metrics') {
				let url = `${URL_DOMAIN}/metric/refresh/CUSTOM_METRIC/${refreshWidgetData.customMetricId}`;
				let metricRefreshResponse = await fetch(url, {
					method: 'GET',
					credentials: 'omit', // include, *same-origin, omit
					headers: {
						Authorization: `Bearer ${localStorage.getItem('token')}`,
						'Content-Type': 'application/json',
					}
				});
				res = await metricRefreshResponse.json();
			}



			console.log('Refresh Response: ' + JSON.stringify(res));
		},
		editWidget(val) {
			amplitude.getInstance().logEvent('EDIT_WIDGET');
			this.editReportModelOpen = true;
			this.inEditMode = true;
			console.log('Editing Widget: ' + val);
			this.widgetEditId = val;

			let widgetEditing = {};
			for (var i = 0; i < this.widgetList.length; i++) {
				if (this.widgetList[i].id == val) {
					console.log('Found Widget To Edit');
					widgetEditing = this.widgetList[i];
					console.log('WidgetEdition: ' + JSON.stringify(widgetEditing));
				}
			}

			//Need to figure out a better way to manage this, instead of needing to fix strings
			let chartType =
				widgetEditing.type.charAt(0).toUpperCase() +
				widgetEditing.type.slice(1).toLowerCase();
			let chartSize = widgetEditing.size;
			let widgetProjectId = widgetEditing.projectId;
			let datasource = widgetEditing.datasource;
			console.log('Datasource: ' + datasource);
			if (datasource == 'segment') {
				console.log('Segment Datasource');
				this.isEditWidgetAProject = false;
			}
			else if (datasource == 'project') {
				console.log('Project Datasource');
				this.isEditWidgetAProject = true;
			}
			else if (datasource == undefined) {
				this.isEditWidgetAProject = true;
				console.log("DEFAULTING TO PROJECT METRIC")
			}

			console.log("CURRENT PROJECT INDEX: " + this.currentProjectIndex);
			this.currentProjectOverrideName = 'Missing Data Source';
			for (var i = 0; i < this.reportProjectList.length; i++) {
				console.log(i, this.reportProjectList[i].name);
				if (this.reportProjectList[i].uuid == widgetProjectId) {
					console.log('Project Name: ' + this.reportProjectList[i].name);
					console.log('UUID: ' + this.reportProjectList[i].uuid);
					this.currentProjectId = this.reportProjectList[i].uuid;
					this.currentProjectIndex = this.reportProjectList[i].id;
					this.currentProjectOverrideName = '';
					this.selectedProject = this.reportProjectList[i];
					console.log('Current Project Index: ' + this.currentProjectIndex);
					break;
				}
			}
			console.log('ChartType: ' + chartType);
			for (let i = 0; i < this.currentAvailableChartTypes.length; i++) {
				console.log(
					'Checkign Chart Type: ' + this.currentAvailableChartTypes[i].name,
				);
				if (
					this.currentAvailableChartTypes[i].name.toUpperCase() ==
					chartType.toUpperCase()
				) {
					console.log(
						'Setting currentReport to ' + this.currentAvailableChartTypes[i].id,
					);
					this.currentReport = this.currentAvailableChartTypes[i].id;
				}
			}

			for (let i = 0; i < this.currentAvailableChartSizes.length; i++) {
				if (
					getWidgetSize(this.currentAvailableChartSizes[i].name) == chartSize
				) {
					this.currentChartSize = this.currentAvailableChartSizes[i].id;
				}
			}

			let apiName = widgetEditing.apiName;
			let apiInfo = getAPIMapFromAPIName(apiName);
			console.log('API Info: ' + JSON.stringify(apiInfo));
			if (apiInfo != undefined) {
				if (datasource != 'custom-metrics') {
					console.log("This is NOT a custom metric datasource");
					console.log("The current dimension is: " + this.currentDimension);
					console.log("Current Insight Options: " + JSON.stringify(this.insightOptions[this.currentDimension]));
					console.log("Is this a project? " + this.isEditWidgetAProject);
					this.insightOptions = getInsightOptionsByChart(this.currentReport, this.isEditWidgetAProject);
				}
				else {
					let finalResults = [];
					for (var i = 0; i < this.customMetrics.length; i++) {
						finalResults.push({
							id: i,
							name: this.customMetrics[i].name,
							friendlyName: this.customMetrics[i].name,
							customMetricId: this.customMetrics[i].uuid,
							tooltip: 'This is a custom metric, that is specific to this environment'
						})
					}
					this.insightOptions = finalResults;
					//this.currentAvailableChartGroups = getReportChartGroupListCustomMetric();
				}
				console.log("INSIGHT OPTIONS: " + JSON.stringify(this.insightOptions));
				this.insightOptions.forEach(option => {
					if (datasource != 'custom-metrics') {
						console.log("CHECKING CHECKING", option.name, apiInfo.insight)
						if (option.name == apiInfo.insight) {
							this.currentDimension = option.id;
						}
					}
					else {
						console.log("CHECKING CHECKING", option, widgetEditing)
						if (option.customMetricId == widgetEditing.customMetricId) {
							this.currentDimension = option.id;
						}
					}
				});

				if (datasource != 'custom-metrics') {
					this.currentAvailableChartGroups = getReportChartGroupList(
						this.insightOptions[this.currentDimension],
					);
				}
				else {
					this.currentAvailableChartGroups = getReportChartGroupListCustomMetric();
				}


				console.log('Current Dimension: ' + this.currentDimension);
				console.log('Current Insight Options: ' + JSON.stringify(this.insightOptions));

				console.log(
					'currentAvailableChartGroups: ' +
					JSON.stringify(this.currentAvailableChartGroups),
				);
				this.currentAvailableChartGroups.forEach(option => {
					if (option.name == apiInfo.group) {
						console.log('Setting currentGroup to ' + option.id);
						this.currentGroup = option.id;
					}
				});
			}

			let widgetLabel = widgetEditing.XAxisInputLabel;
			let widgetInputs = widgetEditing.inputs;
			this.currentChartDefinition = getChartDefinition(
				this.currentAvailableChartTypes[this.currentReport].name,
			);

			console.log('In Edit Mode reset calling, Dimension: ' + this.currentDimension);
			this.resetDataFields();

			console.log('Widget Label: ' + widgetLabel);
			if (widgetLabel != undefined) {
				this.currentAvailableFieldOptionsLabel.forEach(option => {
					console.log('Checking Label: ' + option.field);
					if (option.field === widgetLabel) {
						this.currentLabelOption = option.id;
						console.log('Found Label Field: ' + this.currentLabelOption);
					}
				});
			}

			if (widgetInputs != undefined) {
				this.currentNeededDataPoints = [];
				this.currentDataOption = [];
				for (var i = 0; i < widgetInputs.length; i++) {
					this.currentNeededDataPoints.push({
						id: i,
					});
					this.currentDataOption.push(-1);
					this.currentAvailableFieldOptionsData.forEach(option => {
						console.log('comparing ' + option.field + ' to ' + widgetInputs[i]);
						if (option.field === widgetInputs[i]) {
							console.log('Found Field: ' + option.id);
							this.currentDataOption[i] = option.id;
						}
					});
				}
			}

			//Time
			if (this.currentChartDefinition.time_supported) {

				if(widgetEditing.time) {
					this.timeSelectionType = this.timeSelectionOptions[0].name;
					this.currentTimeSelectionId = this.timeSelectionOptions[0].id;
					this.currentAvailableTimeOptions.forEach(option => {
						if (option.name == widgetEditing.time) {
							this.currentTime = option.id;
						}
					});
				}
				else {
					this.timeSelectionType = this.timeSelectionOptions[1].name;
					this.currentTimeSelectionId = this.timeSelectionOptions[1].id;
					const startDate = new Date(widgetEditing.starttime);
  					const endDate = new Date(widgetEditing.endtime);
					this.dateRange = [startDate, endDate];
				}

			}

			this.currentWidgetName = widgetEditing.title;
			console.log('Current Project Index: ' + this.currentProjectIndex);
			console.log("Ending Update Load");
			this.$forceUpdate();
		},
		removeWidget(val) {
			this.widgetEditId = val;
			this.deleteWidgetModelOpen = true;
		},
		async deleteWidget() {
			amplitude.getInstance().logEvent('DELETE_WIDGET');
			console.log('Removing Widget: ' + this.widgetEditId);

			let url = `${URL_DOMAIN}/widgets/${this.widgetEditId}`;

			// Default options are marked with *
			const response = await fetch(url, {
				method: 'DELETE',
				credentials: 'omit', // include, *same-origin, omit
				headers: {
					Authorization: `Bearer ${localStorage.getItem('token')}`,
					'Content-Type': 'application/json',
				},
			});
			//const json = await response.json();

			//Remove the widget from the list
			this.widgetList = this.widgetList.filter(
				widget => widget.id !== this.widgetEditId,
			);
			this.widgetEditId = 0;
			this.deleteWidgetModelOpen = false;
		},
		createNewReport() {
			this.currentReport = -1;
			this.currentGroup = -1;
			this.currentDimension = -1;
			this.currentLabelOption = -1;
			this.currentTime = 0;
			this.currentChartSize = -1;
			this.currentWidgetName = '';
			amplitude.getInstance().logEvent('CREATE_NEW_WIDGET');
			this.inEditMode = false;
			this.editReportModelOpen = true;
		},
		projectChanged(value) {
			this.missingSource = false;
			this.currentProjectIndex = value;
			amplitude.getInstance().logEvent('CHANGED_PROJECT');
			console.log("PROJECT CHANGED: " + value);
			let currentProjectType = this.selectedProject.type;
			for (var i = 0; i < this.reportProjectList.length; i++) {
				if (this.reportProjectList[i].id == value) {
					console.log('Project Name: ' + this.reportProjectList[i].name);
					console.log('UUID: ' + this.reportProjectList[i].uuid);
					this.currentProjectId = this.reportProjectList[i].uuid;
					this.selectedProject = this.reportProjectList[i];
					this.isEditWidgetAProject = this.reportProjectList[i].type == 'project';
				}
			}
			if (this.selectedProject.type != currentProjectType && this.currentReport >= 0) {
				this.chartTypeChanged(this.currentReport)
				this.currentDimension = -1;
			}
		},
		importProjectChanged(value) {
			this.missingSource = false;
			this.currentProjectIndex = value;
			let currentProjectType = this.selectedProject.type;
			for (var i = 0; i < this.templateImportList.length; i++) {
				if (this.templateImportList[i].id == value) {
					this.currentProjectId = this.templateImportList[i].uuid;
					this.selectedProject = this.templateImportList[i];
					this.isEditWidgetAProject = this.templateImportList[i].type == this.templateImportType;
				}
			}
			if (this.selectedProject.type != currentProjectType && this.currentReport >= 0) {
				this.chartTypeChanged(this.currentReport)
				this.currentDimension = -1;
			}
		},
		chartTypeChanged(value) {
			amplitude.getInstance().logEvent('CHANGED_CHART');
			this.currentReport = value;
			this.currentGroup = -1;
			this.currentChartDefinition = getChartDefinition(
				this.currentAvailableChartTypes[value].name,
			);
			this.currentNeededDataPoints = [];
			this.currentDataOption = [];

			for (var i = 0; i < this.currentChartDefinition.number_dimension; i++) {
				this.currentNeededDataPoints.push({
					id: i,
				});
				this.currentDataOption.push(-1);
			}

			console.log("Updating Insight Options By Chart", this.selectedProject.type)
			if (this.selectedProject.type != 'custom-metrics') {
				this.insightOptions = getInsightOptionsByChart(this.currentReport, this.isEditWidgetAProject);
			}
			else {
				let finalResults = [];
				for (var i = 0; i < this.customMetrics.length; i++) {
					finalResults.push({
						id: i,
						name: this.customMetrics[i].name,
						friendlyName: this.customMetrics[i].name,
						tooltip: 'This is a custom metric, that is specific to this environment'
					})
				}
				this.insightOptions = finalResults;
			}


			console.log("Setting currentReport to " + value, "this.currentReport = " + this.currentReport, "Do they equal: " + (value === this.currentReport));
			this.resetDataFields();
		},
		dataGroupChanged(value) {
			amplitude.getInstance().logEvent('CHANGED_DATA_GROUP');
			this.currentGroup = value;
			console.log(this.insightOptions);
			this.resetDataFields();
		},
		dimensionChanged(value) {
			console.log("Dimension Changed to " + value);
			this.currentDimension = value;
			this.currentGroup = -1;
			this.currentLabelOption = -1
			this.resetDataFields();
			this.currentAvailableChartGroups = getReportChartGroupList(
				this.insightOptions[this.currentDimension],
			);
		},
		timeChanged(value) {
			this.currentTime = value;
		},
		labelOptionChanged(value) {
			this.currentLabelOption = value;
		},
		dataOptionChanged(value, idx) {
			if (this.currentDataOption.length <= idx) {
				this.currentDataOption.push(value);
			} else {
				this.currentDataOption[idx] = value;
			}
		},
		getProjects() {
			return this.reportProjectList;
		},
		chartSizeChanged(value) {
			this.currentChartSize = value;
		},
		getAvailableChartTypes() {
			return this.currentAvailableChartTypes;
		},
		getAvailableDataGroups() {
			return this.currentAvailableChartGroups;
		},
		getInsightOptionsBasedOnSelections() {
			return this.insightOptions;
		},
		getAvailableTimeRanges() {
			return this.currentAvailableTimeOptions;
		},
		getAvailableFieldOptionsLabels() {
			return this.currentAvailableFieldOptionsLabel;
		},
		getAvailableFieldOptionsData() {
			//We need to filter down to the selected name because the index is filtered by the selected report
			return this.currentAvailableFieldOptionsData;
		},
		getAvailableChartSizes() {
			return this.currentAvailableChartSizes;
		},
		copyToClipboard(text) {
			copyTextToClipboard(text);
		},
		resetDataFields() {
			if (this.currentDimension < 0) return;
			console.log("Actually Resetting", this.insightOptions.length, this.currentDimension, this.selectedProject.type);
			if (this.insightOptions.length > this.currentDimension) {
				if (this.selectedProject.type != 'custom-metrics') {
					this.currentAvailableFieldOptionsLabel = getFieldOptions(
						this.insightOptions[this.currentDimension].name,
						this.currentAvailableChartTypes[this.currentReport].name,
						false,
					);

					this.currentAvailableFieldOptionsData = getFieldOptions(
						this.insightOptions[this.currentDimension].name,
						this.currentAvailableChartTypes[this.currentReport].name,
						true,
					);
				}
				else {
					this.currentAvailableFieldOptionsLabel = getFieldOptionsCustomMetric(
						this.customMetrics[this.currentDimension].schema,
						this.currentAvailableChartTypes[this.currentReport].name,
						false,
					);

					this.currentAvailableFieldOptionsData = getFieldOptionsCustomMetric(
						this.customMetrics[this.currentDimension].schema,
						this.currentAvailableChartTypes[this.currentReport].name,
						true,
					);
				}

			}
			else {
				this.currentDimension = -1;
			}
		},
		async bundleAndSaveReport() {
			amplitude.getInstance().logEvent('SAVE_WIDGET');
			console.log('bundleAndSaveReport');
			let chartType = this.currentAvailableChartTypes[this.currentReport].name;
			let chartSize =
				this.currentAvailableChartSizes[this.currentChartSize].name;
			let insightName = this.insightOptions[this.currentDimension].name;
			let groupName = this.isEditWidgetAProject ? this.currentAvailableChartGroups[this.currentGroup].name : CHART_DATA_GROUP_TYPE.SEGMENT;
			let name = this.currentWidgetName;
			if (name == '' || name.includes('Untitled')) {
				name = `${insightName}-${groupName}`;
			}
			let api = this.selectedProject.type != 'custom-metrics' ? getAPIURL(groupName, insightName) : 'custom-metric';
			console.log(name);
			console.log('api: ' + api);
			if (api == null) {
				console.log('No API URL found');
				amplitude.getInstance().logEvent('ERROR_NO_API_IN_SAVE_WIDGET');
				return;
			}
			let reportPayload = {};
			reportPayload.type = chartType;
			reportPayload.size = chartSize;
			reportPayload.name = name;
			reportPayload.label = name;
			reportPayload.projectId = this.currentProjectId;
			reportPayload.dashboardId = parseInt(this.dashboardId);

			//Building Data Payload
			let data = {};
			console.log(this.selectedProject.type, "THIS IS THE TYPE OF PROJECT")
			if (this.selectedProject.type == 'custom-metrics') {
				data.customMetricId = this.customMetrics[this.currentDimension].uuid;
				console.log("THIS IS THE ADDRESS", data.customMetricId)
				console.log("This is the metric we are looking at", this.customMetrics[this.currentDimension]);
			}
			data.apiMethod = api;
			if (this.currentChartDefinition.labels_needed) {
				data.XAxisInputLabel =
					this.currentAvailableFieldOptionsLabel[this.currentLabelOption].field;
				console.log(
					'We need labels',
					this.currentLabelOption,
					this.currentAvailableFieldOptionsLabel[this.currentLabelOption].field,
					JSON.stringify(this.currentAvailableFieldOptionsLabel),
				);
			}
			let inputs = [];
			for (var i = 0; i < this.currentDataOption.length; i++) {
				if (this.currentAvailableFieldOptionsData[this.currentDataOption[i]]) {
					inputs.push(
						this.currentAvailableFieldOptionsData[this.currentDataOption[i]]
							.field
					);
				}
			}
			if (this.currentChartDefinition.time_supported) {
				console.log("Setting Time", this.timeSelectionType);
				if (this.timeSelectionType === this.timeSelectionOptions[0].name) {
					data.time = this.currentAvailableTimeOptions[this.currentTime].name;
				} else if (this.timeSelectionType === this.timeSelectionOptions[1].name && this.dateRange) {
					console.log("Setting Start and Stop Times", this.dateRange[0], this.dateRange[1])
					data.starttime = this.dateRange[0].toISOString();
					data.endtime = this.dateRange[1].toISOString();
				}
			}
			data.inputs = inputs;
			data.datasource = this.selectedProject.type != 'custom-metrics' ? (this.isEditWidgetAProject ? 'project' : 'segment') : 'custom-metrics';
			reportPayload.data = JSON.stringify(data);

			console.log(JSON.stringify(reportPayload));

			let url = `${URL_DOMAIN}/widgets`;

			// Default options are marked with *
			let response = {};
			if (!this.inEditMode) {
				response = await fetch(url, {
					method: 'POST',
					credentials: 'omit', // include, *same-origin, omit
					headers: {
						Authorization: `Bearer ${localStorage.getItem('token')}`,
						'Content-Type': 'application/json',
					},
					body: JSON.stringify(reportPayload), // body data type must match "Content-Type" header
				});
				let res = await response.json();
				let widgetData = reportPayload;
				widgetData.id = res.id;
				this.addWidget(widgetData);
			} else {
				response = await fetch(`${url}/${this.widgetEditId}`, {
					method: 'PUT',
					credentials: 'omit', // include, *same-origin, omit
					headers: {
						Authorization: `Bearer ${localStorage.getItem('token')}`,
						'Content-Type': 'application/json',
					},
					body: JSON.stringify(reportPayload), // body data type must match "Content-Type" header
				});
				let widgetData = reportPayload;
				widgetData.id = this.widgetEditId;
				//Find item in array based on id and replace it with new item
				this.updateWidget(widgetData, this.widgetEditId);
			}

			this.editReportModelOpen = false;
		},
		updateWidget(data, id) {
			var widget = data;
			var widgetData = JSON.parse(widget.data);
			var w = {
				title: widget.name,
				type: widget.type.toUpperCase(),
				key: uuidv4(),
				id: widget.id,
				apiName: widgetData.apiMethod,
				infoLabel: widget.label,
				projectId: this.currentProjectId,
				size: getWidgetSize(widget.size),
				inputs: widgetData.inputs,
				XAxisInputLabel: widgetData.XAxisInputLabel,
				inputSummary: widgetData.inputSummary,
				time: widgetData.time,
				starttime: widgetData.starttime,
				endtime: widgetData.endtime,
				customMetricId: widgetData.customMetricId,
				datasource: widgetData.datasource,
			};

			for (var i = 0; i < this.widgetList.length; i++) {
				if (this.widgetList[i].id == id) {
					this.widgetList[i] = w;
				}
			}

			this.$forceUpdate();
		},
		addWidget(data) {
			var widget = data;
			var widgetData = JSON.parse(widget.data);
			var w = {
				title: widget.name,
				key: uuidv4(),
				type: widget.type.toUpperCase(),
				id: widget.id,
				apiName: widgetData.apiMethod,
				infoLabel: widget.label,
				projectId: this.currentProjectId,
				size: getWidgetSize(widget.size),
				inputs: widgetData.inputs,
				XAxisInputLabel: widgetData.XAxisInputLabel,
				inputSummary: widgetData.inputSummary,
				time: widgetData.time,
				starttime: widgetData.starttime,
				endtime: widgetData.endtime,
				customMetricId: widgetData.customMetricId,
				datasource: widgetData.datasource,
			};
			this.widgetList.push(w);
			this.refreshWidget(data.id);
			this.$forceUpdate();
		},
		getTemplates() {

			let templateList = [
				{ name: "Project Overview", id: 0, description: "Project Overview provides an overview of the selected project, showing details such as New, Active, At Risk, and Dormant Users. It will also give you a starting point for trending activity across interactions over time, wallet share, and so on." },
				{ name: "User Activity Overview", id: 1, description: "User Activity Overview provides a more detailed understanding of the kinds of users that make up the selected project. Details like what personas are involved, most popular dApps or categories, and so on." }
			];

			return templateList;
		},
		templateChanged(value) {
			console.log("SELECTION ID " + this.templateSelectionId);
			let templateList = this.getTemplates();

			for (var i = 0; i < templateList.length; i++) {
				if (templateList[i].id == value) {
					console.log("Found the matching template " + JSON.stringify(templateList[i]));
					this.templateSelection = templateList[i]
				}
			}
		},
		getRibbonBadge(widget) {
			const dataSource = this.reportProjectList.find(x => x.uuid === widget.key || x.uuid === widget.projectId);
			const name = (dataSource || { name: widget.name }).name;

			if (!name) {
				return 'Missing Data Source';
			}

			return name;
		},
		getIsMissingDataSource(widget) {
			const dataSource = this.reportProjectList.find(x => x.uuid === widget.key || x.uuid === widget.projectId);
			const name = (dataSource || { name: widget.name }).name;

			if (!name) {
				return true;
			}

			return false;

		},
		getRibbonBadgeHref(widget) {
			const dataSource = this.reportProjectList.find(x => x.uuid === widget.key || x.uuid === widget.projectId);

			if (!dataSource) {
				console.log("No data source found for widget " + JSON.stringify(widget));
				return '';
			}

			return widget.datasource === 'segment' ? `/build-segment?id=${dataSource.dbId}` : `/connections?projectId=${dataSource.uuid}`;
		},
		cancelUseTemplate() {
			this.useTemplateModelOpen = false;
			this.templateSelection = null;
			this.templateSelectionId = -1;
			this.currentProjectIndex = -1;
			this.currentProjectOverrideName = '';
			this.currentProjectId = '';
			this.selectDataSourceOpen = false;
		},
		async exportDashboard() {
			let templateData = [];
			for (var i = 0; i < this.widgetList.length; i++) {
				var widget = this.widgetList[i];
				var widgetData = {
					name: widget.title,
					type: widget.type,
					size: getWidgetSizeFromNumber(widget.size),
					label: widget.infoLabel,
					data: JSON.stringify({
						apiMethod: widget.apiName,
						inputs: widget.inputs,
						XAxisInputLabel: widget.XAxisInputLabel,
						inputSummary: widget.inputSummary,
						time: widget.time,
						customMetricId: widget.customMetricId,
						datasource: widget.datasource,
					})
				};
				templateData.push(widgetData);
			}
			this.exportText = JSON.stringify(templateData);
			console.log(this.exportText)
			this.exportTemplateOpen = true;
			copyTextToClipboard(this.exportText);
		},
		async importTemplate() {
			amplitude.getInstance().logEvent('DASHBOARD_CREATE_FROM_TEMPLATE');
			this.selectDataSourceOpen = false;
			let templateJSON = {};
			try {
				templateJSON = JSON.parse(this.importText)
				console.log(templateJSON)
				// Do something with the imported data
			} catch (error) {
				this.errorMessage = 'Invalid JSON data'
				console.log(error)
			}

			let url = `${URL_DOMAIN}/widgets`;

			for (let i = 0; i < templateJSON.length; i++) {
				var widget = {
					name: templateJSON[i].name,
					type: templateJSON[i].type,
					size: templateJSON[i].size,
					label: templateJSON[i].label,
					data: templateJSON[i].data,
					projectId: this.currentProjectId,
					dashboardId: parseInt(this.dashboardId),
				};

				let response = await fetch(url, {
					method: 'POST',
					credentials: 'omit', // include, *same-origin, omit
					headers: {
						Authorization: `Bearer ${localStorage.getItem('token')}`,
						'Content-Type': 'application/json',
					},
					body: JSON.stringify(widget), // body data type must match "Content-Type" header
				});
				let res = await response.json();
				let widgetData = widget;
				widgetData.id = res.id;
				this.addWidget(widgetData);
			}
			this.cancelUseTemplate();
		},
		async useTemplateFromList(templateID) {
			amplitude.getInstance().logEvent('DASHBOARD_SELECT_TEMPLATE');

			this.selectDataSourceOpen = true;

			let matchingTemplate = dashboardTemplates.find(template => template.id === templateID);

			this.templateImportType = matchingTemplate.dataSource; //Used in template creation

			if(matchingTemplate.dataSource == 'project')
				this.templateImportList = this.projectsOnlyList;
			else if(matchingTemplate.dataSource == 'audience')
				this.templateImportList = this.audienceOnlyList;
			else
				this.templateImportList = this.projectsOnlyList;

			console.log("Found matching template for" + matchingTemplate.title);

			if (this.selfService) {
				setTimeout(() => {
					this.isSnippetModalOpen = true
					this.$forceUpdate();
				}, 5000);
			}

			this.importText = matchingTemplate.template;
		},
		async useTemplate() {
			let templateJSON = null;
			if (this.templateSelection.id == 0) {
				//templateJSON = templateProjectOverview;
			}
			else if (this.templateSelection.id == 1) {
				//templateJSON = templateUserActivityOverview;
			}

			let url = `${URL_DOMAIN}/widgets`;

			for (let i = 0; i < templateJSON.length; i++) {
				var widget = {
					name: templateJSON[i].name,
					type: templateJSON[i].type,
					size: templateJSON[i].size,
					label: templateJSON[i].label,
					data: templateJSON[i].data,
					projectId: this.currentProjectId,
					dashboardId: parseInt(this.dashboardId),
				};

				let response = await fetch(url, {
					method: 'POST',
					credentials: 'omit', // include, *same-origin, omit
					headers: {
						Authorization: `Bearer ${localStorage.getItem('token')}`,
						'Content-Type': 'application/json',
					},
					body: JSON.stringify(widget), // body data type must match "Content-Type" header
				});
				let res = await response.json();
				let widgetData = widget;
				widgetData.id = res.id;
				this.addWidget(widgetData);
			}
			this.cancelUseTemplate();
		},
		insightsHover(insightId) {
			//console.log("Receivde insightId " + insightId);
			this.chartHovering[insightId] = true;
			setTimeout(() => { this.chartOpenTip[insightId] = this.chartHovering[insightId] }, 1000)
		},
		closeEditPane() {
			this.chartEditPaneToggle = false;
		}
	},
	async mounted() {
		this.isLoaded = false;
		amplitude.getInstance().logEvent('UNIVERSAL_DASHBOARD_MOUNTED');

		this.projectsOnlyList = (await getAvailableProjects(false, true)).filter(x => x.dataConnections?.length);
		this.areAnyProjectsWithConnections = this.projectsOnlyList.length;
		this.audienceOnlyList = await getAvailableAudiences(true, false);

		this.reportProjectList = (await getAvailableProjects(true, true)); // .filter(x => x.dataConnections?.length);
		let totalProjects = this.reportProjectList.length;
		this.customMetrics = await getAvailableCustomMetrics();
		this.reportProjectList = this.reportProjectList.filter(x => (x.dataConnections && x.dataConnections.length) || x.type === 'segment');
		if (this.customMetrics.length > 0) {
			this.reportProjectList.push({ name: 'Custom Metrics', uuid: 'custom-metrics', id: totalProjects + 1, type: 'custom-metrics' });
		}

		//Lets add an option for custom metrics if there are any
		//console.log("Report Project List " + JSON.stringify(this.reportProjectList));
		if (this.reportProjectList.length == 1) {
			this.currentProjectId = this.reportProjectList[0].uuid;
			this.currentProjectIndex = this.reportProjectList[0].id;
		}

		//Init Chart
		const dashboardResult = await getDashboardById(this.dashboardId);
		console.log(
			'Data retrieved from dashboard: ' + JSON.stringify(dashboardResult),
		);
		const widgetResult = await getWidgetsByDashboardId(this.dashboardId);
		console.log(
			'Data retrieved from dashboard with widgets: ' +
			JSON.stringify(widgetResult),
		);

		if (dashboardResult != null) {
			this.dashboardName = dashboardResult.name == 'Untitled' ? '' : dashboardResult.name;

			//Get owner details
			this.dashboardOwnerId = dashboardResult.ownerId;

			let url = `${URL_DOMAIN}/users/` + this.dashboardOwnerId;
			let userFetch = await fetch(url, {
				method: 'GET',
				credentials: 'omit', // include, *same-origin, omit
				headers: {
					Authorization: `Bearer ${localStorage.getItem('token')}`,
					'Content-Type': 'application/json',
				}
			});

			let userResponse = await userFetch.json();

			console.log("USER API " + JSON.stringify(userResponse));

			if (userResponse != null) {
				this.dashboardOwnerName = userResponse.firstName + ' ' + userResponse.lastName;
			}

			this.dashboardShared = dashboardResult.shared;
			if (this.dashboardShared == 1)
				this.dashboardPrivacy = 'Shared';
			else
				this.dashboardPrivacy = 'Private';

		} else {
			this.dashboardName = 'Unable to Locate Dashboard';
		}
		this.isLoaded = true;

		if (widgetResult != null) {
			widgetResult.forEach(widget => {
				var widgetData = JSON.parse(widget.data); //Convert our widget data to JSON and hydrade more below
				console.log('Getting api name ' + widgetData.apiMethod);
				var w = {
					title: widget.name,
					type: widget.type.toUpperCase(),
					id: widget.id,
					key: uuidv4(),
					apiName: widgetData.apiMethod,
					infoLabel: widget.label,
					projectId: widget.projectId,
					size: getWidgetSize(widget.size),
					inputs: widgetData.inputs,
					XAxisInputLabel: widgetData.XAxisInputLabel,
					inputSummary: widgetData.inputSummary,
					customMetricId: widgetData.customMetricId,
					time: widgetData.time,
					starttime: widgetData.starttime,
					endtime: widgetData.endtime,
					datasource: widgetData.datasource,
				};
				this.widgetList.push(w);
			});
		}

		this.initialLoad = true;

		this.$forceUpdate();
	},
};
</script>
