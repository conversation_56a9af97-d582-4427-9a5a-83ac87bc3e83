<template>

	<div class="flex h-screen overflow-hidden bg-white">
		<Sidebar :sidebarOpen="sidebarOpen" @close-sidebar="sidebarOpen = false" />

		<div class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
			<main>
				<div class="px-6 sm:px-6 lg:px-12 py-8 w-full max-w-9xl mx-auto">

				<!-- Page header -->
					<div class="flex sm:justify-between sm:items-center mb-4">
						<h1 class="text-2xl md:text-3xl text-slate-800 font-bold">My Affiliates</h1>
						<div class="flex">
							<button
								class="bg-gradient-button-primary text-white font-bold text-xs py-2 px-4 rounded-button"
								data-cy="create-new-campaign"
								@click.prevent="addAffiliateModalOpen = true">
								+ Add New Affiliate
							</button>
						</div>
					</div>
					<div class="border-b border-ralwhite-line"></div>

				</div>

				<RaleonTable
					class="px-6 sm:px-6 lg:px-12 w-full max-w-9xl mx-auto"
					:column-headers="this.columnHeaders"
					:row-data="this.rowData"
					:is-loading="this.isLoading"
					>
				</RaleonTable>
			</main>
		</div>
			<!-- Create New Env -->
			<ModalBasic
			  id="create-affiliate-modal"
			  :modalOpen="addAffiliateModalOpen"
			  @close-modal="addAffiliateModalOpen = false"
			  title="Add New Affiliate"
			>
			  <!-- Modal content -->
			  <div class="px-5 py-4">
				<div class="text-sm mb-5">
				  <form>
					<div class="space-y-4">
					  <div>
						<label class="block text-sm font-medium mb-1" for="name"
						  >Affiliate Name</label
						>
						<input
						  id="wallet-address"
						  class="form-input w-full"
						  type="text"
						  placeholder="Type something you will recognize..."
						  v-model="affiliateName"
						/>
					  </div>
					  <div>
						<label class="block text-sm font-medium mb-1" for="large"
						  >Affiliate Code</label
						>
						<label class="block text-xs mb-1" for="large"
						  >This will be the code that shows up in their shareable link.</label
						>
						<input
						  id="large"
						  class="form-input w-full px-4 py-3"
						  type="text"
						  v-model="affiliateCode"
						/>
					  </div>
					  <div>
						<label class="block text-sm font-medium mb-1" for="large"
						  >Activation Date</label
						>
						<label class="block text-xs mb-1" for="large"
						  >When do you want their code to be live?</label
						>
						<input
						  id="large"
						  class="form-input w-full px-4 py-3"
						  type="text"
						  v-model="affiliateStart"
						/>
					  </div>
					  <div>
						<label class="block text-sm font-medium mb-1" for="large"
						  >Activation End Date</label
						>
						<label class="block text-xs mb-1" for="large"
						  >When do you want their code to be disabled?</label
						>
						<input
						  id="large"
						  class="form-input w-full px-4 py-3"
						  type="checkbox"
						  v-model="affiliateEnd"
						/>
					  </div>
					</div>
				  </form>
				</div>
			  </div>
			  <!-- Modal footer -->
			  <div class="px-5 py-4 border-t border-slate-200">
				<div class="flex flex-wrap justify-end space-x-2">
				  <button
					class="
					  btn-sm
					  border-slate-200
					  hover:border-slate-300
					  text-slate-600
					"
					@click.stop="addAffiliateModalOpen = false;"
				  >
					Cancel
				  </button>
				  <button
					class="btn-sm bg-indigo-500 hover:bg-indigo-600 text-white"
					@click="addAffiliate()"
				  >
					Add Affiliate
				  </button>
				</div>
			  </div>
			</ModalBasic>
			<!-- End Add New Affiliate -->
	</div>

</template>

<script>
	import { ref } from 'vue'
	import Sidebar from "../../partials/Sidebar.vue"
	import RaleonTable from '../component/RaleonTable.vue'
	import DeleteModal from '../component/DeleteModal.vue'
	import ModalBasic from '../../components/ModalBasic.vue';
	import * as Utils from '../../utils/Utils';

	const URL_DOMAIN = Utils.URL_DOMAIN;

	export default {
		name: 'Affiliates',
		props: [],
		emits: [],
		components: {
			Sidebar,
			RaleonTable,
			DeleteModal,
			ModalBasic,
		},
		setup() {
			const sidebarOpen = ref(false);
			const addAffiliateModalOpen = ref(false);
			return {
				sidebarOpen,
				addAffiliateModalOpen,
			}
		},
		data() {
			return {
				isLoading: false,
				showModal: false,
				showDeleteModalProp: false,
				affiliateName: null,
				affiliateCode: null,
				campaignToDelete: {},
				deleteModalData: {
					header: '',
					confirmTextMain: '',
					confirmTextSecondary: '',
					deleteButtonText: '',
					isDeleting: false,
				},
				columnHeaders: [
					{
						name: 'Affiliate Name',
						tooltip: undefined,
						value: 'affiliateName',
					},
					{
						name: 'Affiliate Code',
						tooltip: undefined,
						value: 'affiliateCode',
					},
					{
						name: 'Revenue',
						tooltip: 'Number of unique identities that viewed the campaign',
						value: 'revenue',
					},
					{
						name: 'Unique Views',
						tooltip: 'Number of unique identities that viewed the campaign',
						value: 'uniqueViews',
					},
					{
						name: 'Status',
						tooltip: undefined,
						value: 'status',
					},
				],
				rowData: [
					{
						affiliateName: 'Cobie',
						affiliateCode: 'UPONLY',
						revenue: '$2,450',
						uniqueViews: '1,102',
						status: 'Active'
				 	},
					 {
						affiliateName: 'Tetranode',
						affiliateCode: 'TETRA',
						revenue: '$5,450',
						uniqueViews: '3,102',
						status: 'Active'
				 	}
				]
			}
		},
		computed: {},
		methods: {

		},
		async mounted() {

		},
	}
</script>
