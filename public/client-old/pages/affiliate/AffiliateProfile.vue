<template>

	<div class="flex h-screen overflow-hidden bg-white">
		<Sidebar :sidebarOpen="sidebarOpen" @close-sidebar="sidebarOpen = false" />

		<div class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
			<main>
				<div class="px-6 sm:px-6 lg:px-12 py-8 w-full max-w-9xl mx-auto">

				<!-- Page header -->
					<div class="flex sm:justify-between sm:items-center mb-4">
						<div class="block">
							<h1 class="text-2xl md:text-3xl text-slate-800 font-bold">Cobie</h1>
							<h2 class="text-xs text-gray-500">Profile</h2>
						</div>
						<div class="flex">
							<button
								class="bg-gradient-button-primary text-white font-bold text-xs py-2 px-4 rounded-button"
								data-cy="create-new-campaign"
								@click.prevent="addAffiliateModalOpen = true">
								Pay Affiliate
							</button>
						</div>
					</div>
					<div class="border-b border-ralwhite-line"></div>

				</div>

				<div class="px-6 sm:px-6 lg:px-12 w-full max-w-9xl mx-auto">
					<div class="flex flex-col mb-8">
						<!--
						<div class="flex">
							<img
								src="../../images/insights.svg"
								alt="Performance"
								class="mr-2"/>
							<h1 class="text-lg md:text-xl font-bold text-ralblack-primary">Performance Metrics</h1>
						</div>
						-->

						<div class="flex rounded-xl bg-raltable-ring-secondary">
							<div class="flex flex-col justify-center m-2 mr-0 pl-3 h-36 w-auto basis-1/3 bg-white rounded-xl">
								<img
									class="h-6 w-6"
									src="../../images/user-views.svg"/>
								<div class="text-overline uppercase pt-2">Affiliate Link</div>
								<h2 class="pt-1 text-xl font-normal"><a href="www.matcha.com/ral?=GOLD">www.uponlypodcast.com/ral?=UPONLY</a></h2>
							</div>

							<div class="flex flex-col justify-center m-2 pl-3 h-36 w-auto basis-1/3 bg-white rounded-xl">
								<img
									class="h-6 w-6"
									src="../../images/user-check.svg"/>
								<div class="text-overline uppercase pt-2">Active Until</div>
								<h2 class="pt-1 text-2xl font-normal">1/1/2024</h2>
							</div>

							<div class="flex flex-col justify-center m-2 ml-0 pl-3 h-36 w-auto basis-1/3 bg-white rounded-xl">
								<img
									class="h-5 w-5"
									src="../../images/percent.svg"/>
								<div class="text-overline uppercase pt-2">Payout Wallet</div>
								<h2 class="pt-1 text-2xl font-normal">0x000..23a4 <span class="text-xs text-ralprimary-dark">change</span></h2>
							</div>
						</div>
					</div>

					<div class="flex sm:justify-between sm:items-center mb-4">
						<div class="flex">
							<img
								src="../../images/user-group.svg"
								alt="Audience"
								class="mr-2"/>
							<h1 class="text-lg md:text-xl font-bold text-ralblack-primary">Goals</h1>
						</div>
						<button
							class="bg-white border border-ralprimary-light text-ralprimary-light font-bold text-xs py-2 px-4 rounded-button"
							data-cy="create-new-campaign"
							@click.prevent="addAffiliateModalOpen = true">
							+ Add Goal
						</button>
					</div>

						<RaleonTable
							class="mt-3"
							:column-headers="this.columnHeaders"
							:row-data="this.rowData"
							:is-loading="isLoading">
						</RaleonTable>
				</div>
			</main>
		</div>
			<!-- Create New Env -->
			<ModalBasic
			  id="create-affiliate-modal"
			  :modalOpen="addAffiliateModalOpen"
			  @close-modal="addAffiliateModalOpen = false"
			  title="Add New Goal"
			>
			  <!-- Modal content -->
			  <div class="px-5 py-4">
				<div class="text-sm mb-5">
				  <form>
					<div class="space-y-4">
					  <div>
						<label class="block text-sm font-medium mb-1" for="name"
						  >Affiliate Name</label
						>
						<input
						  id="wallet-address"
						  class="form-input w-full"
						  type="text"
						  placeholder="Type something you will recognize..."
						  v-model="affiliateName"
						/>
					  </div>
					  <div>
						<label class="block text-sm font-medium mb-1" for="large"
						  >Affiliate Code</label
						>
						<label class="block text-xs mb-1" for="large"
						  >This will be the code that shows up in their unique URL.</label
						>
						<input
						  id="large"
						  class="form-input w-full px-4 py-3"
						  type="text"
						  v-model="affiliateCode"
						/>
					  </div>
					</div>
				  </form>
				</div>
			  </div>
			  <!-- Modal footer -->
			  <div class="px-5 py-4 border-t border-slate-200">
				<div class="flex flex-wrap justify-end space-x-2">
				  <button
					class="
					  btn-sm
					  border-slate-200
					  hover:border-slate-300
					  text-slate-600
					"
					@click.stop="addAffiliateModalOpen = false;"
				  >
					Cancel
				  </button>
				  <button
					class="btn-sm bg-indigo-500 hover:bg-indigo-600 text-white"
					@click="addAffiliate()"
				  >
					Add Affiliate
				  </button>
				</div>
			  </div>
			</ModalBasic>
			<!-- End Add New Affiliate -->
	</div>

</template>

<script>
	import { ref } from 'vue'
	import Sidebar from "../../partials/Sidebar.vue"
	import RaleonTable from '../component/RaleonTable.vue'
	import DeleteModal from '../component/DeleteModal.vue'
	import ModalBasic from '../../components/ModalBasic.vue';
	import * as Utils from '../../utils/Utils';

	const URL_DOMAIN = Utils.URL_DOMAIN;

	export default {
		name: 'AffiliateProfile',
		props: [],
		emits: [],
		components: {
			Sidebar,
			RaleonTable,
			DeleteModal,
			ModalBasic,
		},
		setup() {
			const sidebarOpen = ref(false);
			const addAffiliateModalOpen = ref(false);
			return {
				sidebarOpen,
				addAffiliateModalOpen,
			}
		},
		data() {
			return {
				isLoading: false,
				showModal: false,
				showDeleteModalProp: false,
				affiliateName: null,
				affiliateCode: null,
				campaignToDelete: {},
				deleteModalData: {
					header: '',
					confirmTextMain: '',
					confirmTextSecondary: '',
					deleteButtonText: '',
					isDeleting: false,
				},
				columnHeaders: [
					{
						name: 'Conversion Event',
						tooltip: undefined,
						value: 'affiliateName',
					},
					{
						name: 'Description',
						tooltip: undefined,
						value: 'affiliateCode',
					},
					{
						name: 'Payout / Event',
						tooltip: 'Number of unique identities that viewed the campaign',
						value: 'revenue',
					},
					{
						name: 'Reveue Generated',
						tooltip: 'Number of unique identities that viewed the campaign',
						value: 'uniqueViews',
					},
					{
						name: 'Status',
						tooltip: undefined,
						value: 'status',
					},
				],
				rowData: [
					{
						affiliateName: 'Website Visit',
						affiliateCode: 'User comes to target website',
						revenue: '$0.01',
						uniqueViews: '$200',
						status: 'Active'
				 	},
					 {
						affiliateName: 'Trade > $50',
						affiliateCode: 'Make a trade with <project> for $50 or greater.',
						revenue: '$5.00',
						uniqueViews: '$3,102',
						status: 'Active'
				 	}
				]
			}
		},
		computed: {},
		methods: {

		},
		async mounted() {

		},
	}
</script>
