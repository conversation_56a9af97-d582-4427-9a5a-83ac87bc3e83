<template>
  <div class="flex h-screen overflow-hidden">

    <!-- Sidebar -->
    <Sidebar :sidebarOpen="sidebarOpen" @close-sidebar="sidebarOpen = false" @open-Add-Dashboard-Modal="openAddDashboardModal" />

    <!-- Content area -->
    <div class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
      
      <!-- Site header -->
      
      <main>
        <div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">

          <!-- Page header -->
          <div class="sm:flex sm:justify-between sm:items-center mb-8">
          
            <!-- Left: Title -->
            <div class="mb-4 sm:mb-0">
              <h1 class="text-2xl md:text-3xl text-slate-800 font-bold">DeFi Protocol Overview</h1>
            </div>
        
            <!-- Right: Actions -->
            <div class="grid grid-flow-col sm:auto-cols-max justify-start sm:justify-end gap-2">

              <!-- Datepicker built with flatpickr -->
              <Datepicker align="right" />
                
              <button class="btn bg-white border-slate-200 hover:border-slate-300 text-slate-600">
                  <svg class="w-4 h-4 fill-current text-slate-500 shrink-0" viewBox="0 0 16 16">
                      <path d="M11.7.3c-.4-.4-1-.4-1.4 0l-10 10c-.2.2-.3.4-.3.7v4c0 .6.4 1 1 1h4c.3 0 .5-.1.7-.3l10-10c.4-.4.4-1 0-1.4l-4-4zM4.6 14H2v-2.6l6-6L10.6 8l-6 6zM12 6.6L9.4 4 11 2.4 13.6 5 12 6.6z" />
                  </svg>
                  <span class="ml-2">Edit</span>
              </button>
            </div>
          
          </div>

          <!-- Modals -->
          
          <!-- Start -->
          <ModalBasic id="choose-data-modal" :modalOpen="chooseDataModelOpen" @close-modal="chooseDataModelOpen = false" title="Choose Data Source">
            <!-- Modal content -->
            <div class="px-5 py-4">
              <div class="mb-5">
                <label class="block text-sm font-medium mb-1" for="role">Select From Available Data Connections</label>
                <select id="role" class="form-select w-full">
                  <option value="smart_contract">ENS Registry</option>
                </select>
              </div>
              <button class="btn border-slate-200 hover:border-slate-300 text-indigo-500">
                <a href="/connections">Add Data Connection</a>
              </button>
            </div>
            <!-- Modal footer -->
            <div class="px-5 py-4 border-t border-slate-200">
              <div class="flex flex-wrap justify-end space-x-2">
                <button class="btn-sm border-slate-200 hover:border-slate-300 text-slate-600" @click.stop="chooseDataModelOpen = false">Cancel</button>
                <button class="btn-sm bg-indigo-500 hover:bg-indigo-600 text-white">Save</button>
              </div>
            </div>
          </ModalBasic>
          <!-- End -->

          <!-- Start -->
          <ModalBasic id="add-data-modal" :modalOpen="addDashboardModelOpen" @close-modal="addDashboardModelOpen = false" title="Add New Dashboard">
            <!-- Modal content -->
            <div class="px-5 py-4">
              <div class="mb-5">
                <label class="block text-sm font-medium mb-1" for="role">Start from Template</label>
                <select id="role" class="form-select w-full">
                  <option value="smart_contract">DeFI</option>
                  <option value="token">Game</option>
                  <option value="token">Exchange</option>
                  <option value="token">NFT</option>
                  <option value="token">Custom</option>
                </select>
              </div>

              <div class="text-sm mb-5">
                <form>
                  <div class="space-y-4">
                    <div>
                      <label class="block text-sm font-medium mb-1" for="name">Name</label>
                      <input id="wallet-address" class="form-input w-full" type="text" placeholder="Name of your dashboard" />
                    </div>
                  </div>
                </form>
              </div>
            </div>
            <!-- Modal footer -->
            <div class="px-5 py-4 border-t border-slate-200">
              <div class="flex flex-wrap justify-end space-x-2">
                <button class="btn-sm border-slate-200 hover:border-slate-300 text-slate-600" @click.stop="addDashboardModelOpen = false">Cancel</button>
                <button class="btn-sm bg-indigo-500 hover:bg-indigo-600 text-white">Add</button>
              </div>
            </div>
          </ModalBasic>
          <!-- End -->
          
          <!-- Cards -->
          <div class="grid grid-cols-12 gap-6">

            <!-- QUICK metrics along the top -->
            <QuickMetric title="TVL" amount="184000000" label="$" />

            <QuickMetric title="Total Transactions L30" amount="20200" />

            <QuickMetric title="Unique Traders All Time" amount="42300" />

            <QuickMetric title="Avg Daily Trade Volume" amount="18300" label="$"/>

            <!-- Transactions by DEX/CEX -->
            <TransactionVolumeAll />

            <!-- Recent Activity/Notification -->
            <InsightsFeed />

            <CountryActivity />

            <NewVsReturningUser />

            <!-- Doughnut chart (Interactions by Type) -->
            <InteractionsByType />

            <!-- Report card (Swap Inflow) -->
            <SwapInflow />

            <!-- Report card (Swap Outflow) -->
            <AnalyticsCardSwapOutflow />

            <WhaleHoldingsTable />

            <SetupState @open-Choose-Data="openChooseDataModal"/>
          </div>
        </div>
<!--
<ul>
  <li>All Time High + Date</li>
  <li>Current Price</li>
  <li>% from ATH</li>
  <li>Price chart</li>
  <li>Trading volume ($0.16M)</li>
</ul>
-->
      </main>

    </div> 

  </div>
</template>

<script>
  import { ref } from 'vue'

  import Sidebar from '../partials/Sidebar.vue'
  import Header from '../partials/Header.vue'
  import Datepicker from '../components/Datepicker.vue'
  import FilterButton from '../components/DropdownFilter.vue'
  import QuickMetric from '../components/QuickMetric.vue'

  import ModalBasic from '../components/ModalBasic.vue'
  import ModalCookies from '../components/ModalCookies.vue'
  import ModalBlank from '../components/ModalBlank.vue'
  import ModalAction from '../components/ModalAction.vue'
  import DropdownClassic from '../components/DropdownClassic.vue'

  import TransactionVolumeAll from '../partials/analytics/TransactionVolumeAll.vue'
  import InsightsFeed from '../partials/analytics/InsightsFeed.vue'
  import SwapInflow from '../partials/analytics/SwapInflow.vue'
  import InteractionsByType from '../partials/analytics/InteractionsByType.vue'
  import AnalyticsCardSwapOutflow from '../partials/analytics/AnalyticsCardSwapOutflow.vue'
  import InflowOutflowTable from '../partials/analytics/InflowOutflowTable.vue'
  import MarketTrends from '../partials/analytics/MarketTrends.vue'
  import NewVsReturningUser from '../partials/analytics/NewVsReturningUser.vue'
  import CountryActivity from '../partials/analytics/CountryActivity.vue'
  import WhaleHoldingsTable from '../partials/analytics/WhaleHoldingsTable.vue'
  import SetupState from '../partials/analytics/SetupState.vue'

  export default {
    name: 'e2e',
    components: {
      Sidebar,
      Header,
      Datepicker,
      FilterButton,
      QuickMetric,
      ModalBasic,
      ModalCookies,
      ModalBlank,
      ModalAction,
      DropdownClassic,
      TransactionVolumeAll,
      SwapInflow,
      InteractionsByType,
      InsightsFeed,
      AnalyticsCardSwapOutflow,
      InflowOutflowTable,
      MarketTrends,
      NewVsReturningUser,
      CountryActivity,
      WhaleHoldingsTable,
      SetupState,
    },
    setup() {

    const sidebarOpen = ref(true)
    const chooseDataModelOpen = ref(false)
    const addDashboardModelOpen = ref(false)

      return {
        sidebarOpen,
        chooseDataModelOpen,
        addDashboardModelOpen,
      }
    },
    methods: {
      openChooseDataModal() {
        this.chooseDataModelOpen = true
      },
      
      openAddDashboardModal() {
        console.log("We emitted")
        this.addDashboardModelOpen = true
      }
    }
  }
</script>