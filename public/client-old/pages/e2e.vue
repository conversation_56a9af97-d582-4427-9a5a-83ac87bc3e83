<template>
  <div class="flex h-screen overflow-hidden">
    <!-- Sidebar -->
    <Sidebar :sidebarOpen="sidebarOpen" @close-sidebar="sidebarOpen = false"
      @open-Add-Dashboard-Modal="openAddDashboardModal" />

    <!-- Content area -->
    <div class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">

		<div class="flex flex-row snippet-banner bg-ralprimary-main justify-center items-center" v-if="isSnippetWarningVisible">
			<h1 class="mr-4">Snippet May Not Be Installed</h1>
			<p class="mr-4">Snippet must be installed for Quest<PERSON> to be visible on your site</p>
			<a class="text-white" href="/snippet">Verify Now</a>

		</div>

      <!-- Site header -->


      <main>
        <div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">
          <!-- Page header -->
          <div class="sm:flex sm:justify-between sm:items-center mb-8">
            <!-- Left: Title -->
            <div class="mb-4 sm:mb-0">
              <h1 class="text-2xl md:text-3xl text-slate-800 font-bold">
                🚀 {{orgName}} Homebase
              </h1>
            </div>
          </div>

          <!-- Cards -->
          <Banner :open="infoBanner" class="mb-6">
            <div class="text-l flex font-semibold text-indigo-500">
              Hello {{ userName }} <span class="animate-waving-hand">👋 </span>!
            </div>
          </Banner>

          <div class="space-y-8 mt-8">
              <!-- Rich Table Row with Accordion -->
              <div class="bg-white shadow-lg rounded-sm border border-slate-200 p-5">
                <h2 class="text-xl md:text-1xl text-slate-800 font-bold mb-4">Setup Raleon and start boosting your marketing efforts</h2>

                <!-- Start 1 -->

                <!--
                <div class="rounded-sm border border-slate-200 hover:shadow-lg hover:border-ralocean-300">
                  <table class="table-auto">
                    <tr class="cursor-pointer" @click.prevent="descriptionOpen1 = !descriptionOpen1">
                      <td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap" style="width: 100%">
                        <div class="flex items-center text-slate-800">
                          <div class="ring-offset-2 ring w-5 h-5 ring-ralapple-500 bg-ralapple-500 rounded-full mr-4" v-if="teamCheck" />
                          <div class="peer w-5 h-5 bg-white border border-slate-200 text-indigo-500 rounded-full mr-4" v-if="!teamCheck" />
                          <div class="font-medium text-slate-800">Add your teammates to Raleon</div>
                        </div>
                      </td>
                      <td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap w-px">
                        <div class="flex items-center">
                          <button class="text-slate-400 hover:text-slate-500 transform" :class="{ 'rotate-180': descriptionOpen1 }" @click.prevent="descriptionOpen1 = !descriptionOpen1" :aria-expanded="descriptionOpen1" :aria-controls="`description-1`">
                            <span class="sr-only">Menu</span>
                            <svg class="w-12 h-8 fill-current" viewBox="0 0 32 32">
                              <path d="M16 20l-5.4-5.4 1.4-1.4 4 4 4-4 1.4 1.4z" />
                            </svg>
                          </button>
                        </div>
                      </td>
                    </tr>

                    <tr id="`description-1`" role="region" :class="!descriptionOpen1 && 'hidden'">
                      <td colspan="2" class="px-2 first:pl-5 last:pr-5 py-3">
                        <div class="bg-slate-50 p-3 -mt-3">
                          <div class="text-sm mb-3">
                            <div class="font-medium text-slate-800 mb-1">Desc Title</div>
                            <div>Desc Body</div>
                          </div>
                          <button class="btn-xs bg-indigo-500 hover:bg-indigo-600 text-white">Approve</button>
                        </div>
                      </td>
                    </tr>
                  </table>
                </div>

                -->
                <!-- End -->
                <!-- Start 2 -->
                <div class="rounded-sm border border-slate-200 mt-4 hover:shadow-lg hover:border-ralocean-300">

                  <table class="table-auto">
                    <tr  class="cursor-pointer" @click.prevent="descriptionOpen2 = !descriptionOpen2">
                      <td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap" style="width: 100%">
                        <div class="flex items-center text-slate-800">
                          <div class="ring-offset-2 ring w-5 h-5 ring-ralapple-500 bg-ralapple-500 rounded-full mr-4" v-if="audienceCheck" />
                          <div class="peer w-5 h-5 bg-white border border-slate-200 text-indigo-500 rounded-full mr-4" v-if="!audienceCheck" />
                          <div class="font-medium text-slate-800">Create your first Audience for deeper user insights and user targeting</div>
                        </div>
                      </td>
                      <td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap w-px">
                        <div class="flex items-center">
                          <button class="text-slate-400 hover:text-slate-500 transform" :class="{ 'rotate-180': descriptionOpen2 }" @click.prevent="descriptionOpen2 = !descriptionOpen2" :aria-expanded="descriptionOpen2" :aria-controls="`description-2`">
                            <span class="sr-only">Menu</span>
                            <svg class="w-12 h-8 fill-current" viewBox="0 0 32 32">
                              <path d="M16 20l-5.4-5.4 1.4-1.4 4 4 4-4 1.4 1.4z" />
                            </svg>
                          </button>
                        </div>
                      </td>
                    </tr>
                    <!--
                    Example of content revealing when clicking the button on the right side:
                    Note that you must set a "colspan" attribute on the <td> element,
                    and it should match the number of columns in your table
                    -->
                    <tr id="`description-2`" role="region" :class="!descriptionOpen2 && 'hidden'">
                      <td colspan="2" class="px-2 first:pl-5 last:pr-5 py-3">
                        <div class="p-3 -mt-3">
                          <div class="text-sm mb-3">
                            <div class="content-right"><img src="../images/AudienceSampleOnboarding.png" width="350" height="233" class="pb-4 pl-4" style="float: right;" /></div>
                            <div class="mb-4">Audiences are a key way to generate user insights and targeted engagements.</div>
                            <div>You can think of audiences as a way to apply advanced filters to lists of wallets, which you can then use in your dashboards or campaigns. You can <a class="text-sm font-medium text-indigo-500 hover:text-indigo-600" href="/support/starter-segments">learn about audiences</a> from our support section, or <span class="text-sm font-medium text-indigo-500 hover:text-indigo-600 cursor-pointer" @click.stop="addSegment()">create a new audience</span>.</div>
                          </div>
                          <button
                            class="btn-xs bg-ralpurple-500 hover:bg-ralpurple-600 text-white"
                            aria-controls="add-report-modal"
                            @click.stop="addSegment()"
                          >
                            <span class="hidden xs:block">Add An Audience</span>
                          </button>
                        </div>
                      </td>
                    </tr>
                  </table>
                  </div>
                  <!-- End -->
                <!-- Start 3 -->
                <div class="rounded-sm border border-slate-200 mt-4 hover:shadow-lg hover:border-ralocean-300">

                  <table class="table-auto">
                    <tr  class="cursor-pointer" @click.prevent="descriptionOpen3 = !descriptionOpen3">
                      <td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap" style="width: 100%">
                        <div class="flex items-center text-slate-800">
                          <div class="ring-offset-2 ring w-5 h-5 ring-ralapple-500 bg-ralapple-500 rounded-full mr-4" v-if="attributionCheck" />
                          <div class="peer w-5 h-5 bg-white border border-slate-200 text-indigo-500 rounded-full mr-4" v-if="!attributionCheck" />
                          <div class="font-medium text-slate-800">Set up web3 attribution to understand your marketing efforts</div>
                        </div>
                      </td>
                      <td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap w-px">
                        <div class="flex items-center">
                          <button class="text-slate-400 hover:text-slate-500 transform" :class="{ 'rotate-180': descriptionOpen3 }" @click.prevent="descriptionOpen2 = !descriptionOpen3" :aria-expanded="descriptionOpen3" :aria-controls="`description-3`">
                            <span class="sr-only">Menu</span>
                            <svg class="w-12 h-8 fill-current" viewBox="0 0 32 32">
                              <path d="M16 20l-5.4-5.4 1.4-1.4 4 4 4-4 1.4 1.4z" />
                            </svg>
                          </button>
                        </div>
                      </td>
                    </tr>
                    <!--
                    Example of content revealing when clicking the button on the right side:
                    Note that you must set a "colspan" attribute on the <td> element,
                    and it should match the number of columns in your table
                    -->
                    <tr id="`description-3`" role="region" :class="!descriptionOpen3 && 'hidden'">
                      <td colspan="2" class="px-2 first:pl-5 last:pr-5 py-3">
                        <div class="p-3 -mt-3">
                          <div class="text-sm mb-3">
                            <div class="content-right"><img src="../images/NewUserCampaign.png" width="350" height="233" class="pb-4 pl-4" style="float: right;" /></div>
                            <div class="mb-4">Our attribution gives you visibility into the top of your marketing funnel and how it drives return-on-investment.</div>
                            <div class="mb-4">You no longer have to wonder whether discord or twitter drives better results on-chain because you can now see it with data. Use these valuable insights to understand where you should spend your valuable marketing time and dollars.</div>
                          </div>
                          <button
                            class="btn-xs bg-ralpurple-500 hover:bg-ralpurple-600 text-white"
                            aria-controls="add-report-modal"
                            @click.stop="gotoAttribution()"
                          >
                            <span class="hidden xs:block">Set Up Attribution</span>
                          </button>
                        </div>
                      </td>
                    </tr>
                  </table>
                </div>
                <!-- End -->
                <!-- Start 4 -->
                <div class="rounded-sm border border-slate-200 mt-4 hover:shadow-lg hover:border-ralocean-300">
                <table class="table-auto">
                  <tr class="cursor-pointer" @click.prevent="descriptionOpen4 = !descriptionOpen4">
                    <td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap" style="width: 100%">
                      <div class="flex items-center text-slate-800">
                        <div class="ring-offset-2 ring w-5 h-5 ring-ralapple-500 bg-ralapple-500 rounded-full mr-4" v-if="customEventCheck == true" />
                        <div class="peer w-5 h-5 bg-white border border-slate-200 text-indigo-500 rounded-full mr-4" v-if="!customEventCheck" />
                        <div class="font-medium text-slate-800">Set up custom events to connect your off-chain data to wallets for greater visibility</div>
                      </div>
                    </td>
                    <td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap w-px">
                      <div class="flex items-center">
                        <button class="text-slate-400 hover:text-slate-500 transform" :class="{ 'rotate-180': descriptionOpen4 }" @click.prevent="descriptionOpen4 = !descriptionOpen4" :aria-expanded="descriptionOpen4" :aria-controls="`description-4`">
                          <span class="sr-only">Menu</span>
                          <svg class="w-12 h-8 fill-current" viewBox="0 0 32 32">
                            <path d="M16 20l-5.4-5.4 1.4-1.4 4 4 4-4 1.4 1.4z" />
                          </svg>
                        </button>
                      </div>
                    </td>
                  </tr>
                  <!--
                  Example of content revealing when clicking the button on the right side:
                  Note that you must set a "colspan" attribute on the <td> element,
                  and it should match the number of columns in your table
                  -->
                  <tr id="`description-4`" role="region" :class="!descriptionOpen4 && 'hidden'">
                    <td colspan="2" class="px-2 first:pl-5 last:pr-5 py-3">
                      <div class="p-3 -mt-3">
                        <div class="text-sm mb-3">
                          <div class="mb-4">Custom Events are a great way to connect your off-chain data, like product actions or email, with the wallet that's performing them. This allows you to understand your users at a deeper level, leading to higher return-on-investment.</div>
                          <div class="mb-4">Setting up custom events should take you 5 minutes or less. You can <span class="text-sm font-medium text-indigo-500 hover:text-indigo-600 cursor-pointer" @click.stop="viewCustomEvent()">view any of your custom events</span> observed by Raleon. Even better, all of your custom event insights are available in our Audience targeting.</div>
                          <button
                            class="btn-xs bg-ralpurple-500 hover:bg-ralpurple-600 text-white"
                            aria-controls="add-report-modal"
                            @click.stop="gotoAttribution()"
                          >
                            <span class="hidden xs:block">Set Up Custom Events</span>
                          </button>
                        </div>
                      </div>
                    </td>
                  </tr>
                </table>
                </div>
                <!-- End -->
              </div>

              <div class="bg-white shadow-lg rounded-sm border border-slate-200 p-5">
                <h2 class="text-xl md:text-1xl text-slate-800 font-bold mb-4">Your Dashboards</h2>
                <ul class="ml-4">
                  <router-link v-for="dash in sharedDashboardList" :key="dash.id" :to="{
                    name: 'UniversalDashboard',
                    params: {dashboardId: dash.id},
                  }" custom v-slot="{href, navigate, isExactActive}">
                    <li class="mb-3" :class="isExactActive && 'border-r-3 border-indigo-500'">
                      <a class="
                          block
                          text-ralpurple-500
                          hover:text-ralpurple-300
                          transition
                          duration-150
                          truncate
                        " :class="isExactActive && '!text-indigo-500'" :href="href" @click="navigate">
                        <span class="
                            text-base
                            font-normal
                            lg:opacity-0 lg:sidebar-expanded:opacity-100
                            2xl:opacity-100
                            duration-200
                          ">{{ dash.name }}</span>
                      </a>
                    </li>
                  </router-link>
                </ul>
                <ul class="ml-4" v-if="personalDashCount > 0">
                  <router-link v-for="dash in personalDashboardList" :key="dash.id" :to="{
                    name: 'UniversalDashboard',
                    params: {dashboardId: dash.id},
                  }" custom v-slot="{href, navigate, isExactActive}">
                    <li class="mb-3" :class="isExactActive && 'border-r-3 border-indigo-500'">
                      <a class="
                          block
                          text-ralpurple-500
                          hover:text-ralpurple-300
                          transition
                          duration-150
                          truncate
                        " :class="isExactActive && '!text-indigo-500'" :href="href" @click="navigate">
                        <span class="
                            text-base
                            font-normal
                            lg:opacity-0 lg:sidebar-expanded:opacity-100
                            2xl:opacity-100
                            duration-200
                          ">{{ dash.name }}</span>
                      </a>
                    </li>
                  </router-link>
                </ul>
              </div>
          </div>

        </div>
      </main>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue';

import Banner from '../components/Banner.vue';
import Datepicker from '../components/Datepicker.vue';
import FilterButton from '../components/DropdownFilter.vue';
import QuickMetric from '../components/QuickMetric.vue';
import Header from '../partials/Header.vue';
import Sidebar from '../partials/Sidebar.vue';
import { getDashboardsByOrgId, getPersonalDashboardsByOrgId } from '../services/dashboard';

import DropdownFull from '../components/DropdownFull.vue';
import DropdownMulti from '../components/DropdownMulti.vue';
import ModalAction from '../components/ModalAction.vue';
import ModalBasic from '../components/ModalBasic.vue';
import ModalBlank from '../components/ModalBlank.vue';
import ModalCookies from '../components/ModalCookies.vue';

import AnalyticsCardSwapOutflow from '../partials/analytics/AnalyticsCardSwapOutflow.vue';
import BestBridgeSource from '../partials/analytics/BestBridgeSource.vue';
import CountryActivity from '../partials/analytics/CountryActivity.vue';
import InflowOutflowTable from '../partials/analytics/InflowOutflowTable.vue';
import InsightsFeed from '../partials/analytics/InsightsFeed.vue';
import MarketTrends from '../partials/analytics/MarketTrends.vue';
import NewCustomerOverTime from '../partials/analytics/NewCustomerOverTime.vue';
import SetupState from '../partials/analytics/SetupState.vue';
import SwapInflow from '../partials/analytics/SwapInflow.vue';
import TransactionVolumeAll from '../partials/analytics/TransactionVolumeAll.vue';
import WhaleHoldingsTable from '../partials/analytics/WhaleHoldingsTable.vue';

//real
import ActiveUserInteractions from '../partials/analytics/Project/ActiveUserInteractions.vue';
import ActiveUsers from '../partials/analytics/Project/ActiveUsers.vue';
import AtRiskUsers from '../partials/analytics/Project/AtRiskUsers.vue';
import AvgTokenTransferSize from '../partials/analytics/Project/AvgTokenTransfer.vue';
import ChurnRate from '../partials/analytics/Project/ChurnRate.vue';
import ChurnRisk from '../partials/analytics/Project/ChurnRisk.vue';
import DormantUsers from '../partials/analytics/Project/DormantUsers.vue';
import InteractionsByType from '../partials/analytics/Project/InteractionsByType.vue';
import NewCustvsLostCust from '../partials/analytics/Project/NewCustvsLostCust.vue';
import NewUsers from '../partials/analytics/Project/NewUsers.vue';
import TotalTokenHolders from '../partials/analytics/Project/TotalTokenHolders.vue';
import UniqueWallets from '../partials/analytics/Project/UniqueWallets.vue';
import { getAvailableProjects } from '../services/project';
import * as Utils from '../utils/Utils';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
  name: 'e2e',
  //props: ['projectId'],
  components: {
    Sidebar,
    Header,
    Datepicker,
    DropdownFull,
    DropdownMulti,
    FilterButton,
    QuickMetric,
    ModalBasic,
    ModalCookies,
    ModalBlank,
    ModalAction,
    TransactionVolumeAll,
    SwapInflow,
    InteractionsByType,
    InsightsFeed,
    AnalyticsCardSwapOutflow,
    InflowOutflowTable,
    MarketTrends,
    CountryActivity,
    WhaleHoldingsTable,
    SetupState,
    BestBridgeSource,
    DormantUsers,
    ActiveUsers,
    AtRiskUsers,
    NewUsers,
    UniqueWallets,
    ActiveUserInteractions,
    NewCustvsLostCust,
    Banner,
    AvgTokenTransferSize,
    TotalTokenHolders,
    NewCustomerOverTime,
    ChurnRate,
    ChurnRisk,
  },
  data() {
    return {
      sharedDashboardList: [],
      personalDashboardList: [],
	  campaigns: [],
    };
  },
  computed: {

	isSnippetWarningVisible() {
		return  localStorage.getItem('snippetVerified') != 'true' &&  this.campaigns?.length;
	},
    userName() {
      console.log('Name: ' + localStorage.getItem('firstName'));
      return (
        localStorage.getItem('firstName') +
        ' ' +
        localStorage.getItem('lastName')
      );
    },
    teamCheck() {
      if(localStorage.getItem('teammates') == 'true')
        return true;
      else
        return false;
    },
    attributionCheck() {
      if(localStorage.getItem('offChainEnabled') == 'true')
        return true;
      else
        return false;
    },
    customEventCheck() {
      if(localStorage.getItem('custom_events') == 'true') {
        return true;
      }
      else
        return false;
    },
    audienceCheck() {
      if(localStorage.getItem('audiences') == 'true')
        return true;
      else
        return false;
    },
    orgName() {
      return localStorage.getItem('orgName');
    },
    personalDashCount() {
      return this.personalDashboardList.length;
    }
  },
  setup() {
    const sidebarOpen = ref(true);
    const infoBanner = ref(true);

    const descriptionOpen1 = ref(false)
    const descriptionOpen2 = ref(false)
    const descriptionOpen3 = ref(false)
    const descriptionOpen4 = ref(false)
    const descriptionOpen5 = ref(false)

    return {
      sidebarOpen,
      infoBanner,
      descriptionOpen1,
      descriptionOpen2,
      descriptionOpen3,
      descriptionOpen4,
      descriptionOpen5,
    };
  },
  methods: {
		addSegment() {
			this.$router.push('/overview-segment');
		},
    gotoAttribution() {
      this.$router.push('/support/marketing-attribution');
    },
    viewCustomEvent() {
      this.$router.push('/custom-events');
    },
  },
  async mounted() {


	fetch(`${URL_DOMAIN}/campaigns`, {
		method: 'GET',
		credentials: 'omit', // include, *same-origin, omit
		mode: 'cors',
		headers: {
			'Content-Type': 'application/json',
			Authorization: `Bearer ${localStorage.getItem('token')}`,
		},
	}).then(async response => {
		this.campaigns = await response.json();
	}).catch();


    //Shared
    this.sharedDashboardList = [];
    const dashboardResult = await getDashboardsByOrgId(
      `${localStorage.getItem('userOrgId')}`,
    );
    /*console.log(
      'Data retrieved from shared dashboards for homepage: ' + JSON.stringify(dashboardResult),
    );*/

    if (dashboardResult.length > 0) {
      for (var i = 0; i < dashboardResult.length; i++) {
        this.sharedDashboardList.push({
          name: dashboardResult[i].name,
          id: dashboardResult[i].id,
        });
      }
    }

    let userInfo = JSON.parse(localStorage.getItem('userInfo'));
    //Personal
    this.personalDashboardList = [];
    const personalDashboardResult = await getPersonalDashboardsByOrgId(
      `${localStorage.getItem('userOrgId')}`, userInfo.id
    );
    /*console.log(
      'Data retrieved from personal dashboards: ' + JSON.stringify(personalDashboardResult),
    );*/

    if (personalDashboardResult.length > 0) {
      for (var i = 0; i < personalDashboardResult.length; i++) {
        this.personalDashboardList.push({
          name: personalDashboardResult[i].name,
          id: personalDashboardResult[i].id,
        });
      }
    }
  },
};
</script>
