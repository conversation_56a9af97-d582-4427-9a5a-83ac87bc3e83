<template>
	<ModalBlank
		id="delete-modal"
		:modal-open="this.show"
		@close-modal="$emit('closeModal')">

		<div class="p-5 flex flex-col space-x-4">
			<!-- Icon -->
			<div class="rounded-full flex justify-start shrink-0">
				<svg class="w-4 h-4 mt-1.5 mr-2 shrink-0 fill-current text-rose-500" viewBox="0 0 16 16">
					<path
						d="M8 0C3.6 0 0 3.6 0 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8zm0 12c-.6 0-1-.4-1-1s.4-1 1-1 1 .4 1 1-.4 1-1 1zm1-3H7V4h2v5z" />
				</svg>
				<span class="mb-2 text-lg font-semibold text-slate-800">
					{{ this.header }}
				</span>
			</div>
			<!-- Modal content -->
			<div class="text-sm mb-10">
				<div class="space-y-2">
					<p>
						{{ this.confirmTextMain }}
					</p>
					<p>
						{{ this.confirmTextSecondary }}
					</p>
				</div>
			</div>
			<!-- Modal footer -->
			<div class="flex flex-wrap justify-end space-x-2">
				<button :disabled="this.isDeleting"
					class="btn-sm border-slate-200 hover:border-slate-300 disabled:border-slate-300 disabled:bg-slate-100 disabled:text-slate-400 disabled:cursor-not-allowed"
					@click.stop="$emit('cancelClicked')">
					Cancel
				</button>
				<button
					class="btn bg-rose-500 hover:bg-rose-600 text-white disabled:border-slate-300 disabled:bg-slate-100 disabled:text-slate-400 disabled:cursor-not-allowed"
					:disabled="this.isDeleting" @click="$emit('deleteClicked')"
					data-cy="delete-button">
					<svg
						v-if="this.isDeleting"
						class="animate-spin w-4 h-4 fill-current shrink-0 mr-2"
						viewBox="0 0 16 16">
						<path
							d="M8 16a7.928 7.928 0 01-3.428-.77l.857-1.807A6.006 6.006 0 0014 8c0-3.309-2.691-6-6-6a6.006 6.006 0 00-5.422 8.572l-1.806.859A7.929 7.929 0 010 8c0-4.411 3.589-8 8-8s8 3.589 8 8-3.589 8-8 8z" />
					</svg>
					{{ this.deleteButtonText }}
				</button>
			</div>
		</div>
	</ModalBlank>
</template>

<script>
	import ModalBlank from '../../components/ModalBlank.vue'

	export default {
		name: 'DeleteModal',
		props: [
			'show',
			'header',
			'confirmTextMain',
			'confirmTextSecondary',
			'deleteButtonText',
			'isDeleting',
		],
		emits: ['closeModal', 'cancelClicked', 'deleteClicked'],
		components: { ModalBlank },
	}
</script>
