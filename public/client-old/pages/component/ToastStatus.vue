<template>
	<div id="toast-success"
		class="z-50 flex items-center p-4 mb-4 w-full max-w-xs text-gray-500 bg-white rounded-lg shadow absolute bottom-5 right-5"
		:class="{ 'cursor-pointer': clickable }"
		role="alert" v-if="localStatus === 'success'" @click="handleClick">
		<div
			class="inline-flex flex-shrink-0 justify-center items-center w-8 h-8 text-green-500 bg-green-100 rounded-lg dark:bg-green-800 dark:text-green-200">
			<svg aria-hidden="true" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"
				xmlns="http://www.w3.org/2000/svg">
				<path fill-rule="evenodd"
					d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
					clip-rule="evenodd"></path>
			</svg>
			<span class="sr-only">Check icon</span>
		</div>
		<div class="ml-3 text-sm font-normal">{{ text }}</div>
		<button type="button"
			class="ml-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex h-8 w-8"
			data-dismiss-target="#toast-success" aria-label="Close" @click.stop="setStatusOff()">
			<span class="sr-only">Close</span>
			<svg aria-hidden="true" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"
				xmlns="http://www.w3.org/2000/svg">
				<path fill-rule="evenodd"
					d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
					clip-rule="evenodd"></path>
			</svg>
		</button>
	</div>

	<div id="toast-success"
		class="z-50 flex items-center p-4 mb-4 w-full max-w-xs text-gray-500 bg-white rounded-lg shadow absolute bottom-5 right-5"
		:class="{ 'cursor-pointer': clickable }"
		role="alert" v-if="localStatus === 'fail'" @click="handleClick">
		<div
			class="inline-flex flex-shrink-0 justify-center items-center w-8 h-8 text-red-500 bg-red-100 rounded-lg dark:bg-red-800 dark:text-red-200">
			<svg aria-hidden="true" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"
				xmlns="http://www.w3.org/2000/svg">
				<path fill-rule="evenodd"
					d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
					clip-rule="evenodd"></path>
			</svg>
			<span class="sr-only">Error icon</span>
		</div>
		<div class="ml-3 text-sm font-normal">{{ text }}</div>
		<button type="button"
			class="ml-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex h-8 w-8"
			data-dismiss-target="#toast-success" aria-label="Close" @click.stop="setStatusOff()">
			<span class="sr-only">Close</span>
			<svg aria-hidden="true" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"
				xmlns="http://www.w3.org/2000/svg">
				<path fill-rule="evenodd"
					d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
					clip-rule="evenodd"></path>
			</svg>
		</button>
	</div>
</template>

<script>
	export default {
		name: 'ToastStatus',
		props: {
			status: String,
			text: String,
			hideTimeout: {
				type: Number,
				default: 5000
			},
			clickable: {
				type: Boolean,
				default: false
			}
		},
		emits: ['clear-status', 'click'],
		computed: {
			hasClickListener() {
				return this.$attrs && this.$attrs.onClick;
			}
		},
		methods: {
			setStatusOff() {
				this.localStatus = '';
				this.$emit('clear-status');
			},
			handleClick() {
				this.$emit('click');
			}
		},
		data() {
			return {
				localStatus: ''
			}
		},
		watch: {
			status(newStatus, oldStatus) {
				console.log("New status is " + newStatus)
				this.localStatus = newStatus;
				if (this.hideTimeout > 0 && newStatus) {
					setTimeout(() => { this.setStatusOff(); }, this.hideTimeout);
				}
			}
		}
	}
</script>
