<template>
  <div class="flex h-screen overflow-hidden">

    <!-- Sidebar -->
    <Sidebar :sidebarOpen="sidebarOpen" @close-sidebar="sidebarOpen = false" />

    <!-- Content area -->
    <div class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden bg-white">

      <!-- Site header -->
      

      <main>
        <div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">

          <!-- Page header -->
          <div class="mb-8">
            <h1 class="text-2xl md:text-3xl text-slate-800 font-bold">Skeleton Loaders</h1>
          </div>

          <div class="border-t border-slate-200">

            <!-- Components -->
            <div class="space-y-8 mt-8">

              <div>
                <h2 class="text-2xl text-slate-800 font-bold mb-6">Loader Examples</h2>
                <p>Skeleton Loaders are brief stencils that appear in areas of the page where a load is required. Skeleton loaders should be used when loading in anything that's less than 5 seconds. If it's greater than 5 seconds, a heavier loader needs to be used. The intention of the stencil in the loader is that is gives an indication of the kind of data that will be loaded once complete.</p>
                <br />
                <div class="flex flex-wrap items-center -m-1.5">
                  <div class="m-1.5 w-full">
                    <h3 class="text-xl text-slate-600">Table</h3>
                        <div class="flex flex-col flex-1 gap-5 sm:p-2">
                          <div class="flex flex-1 flex-col gap-3">
                            <div class="bg-gray-200 w-full animate-pulse h-5 rounded-2xl"></div>
                            <div class="bg-gray-200 w-full animate-pulse h-5 rounded-2xl"></div>
                            <div class="bg-gray-200 w-full animate-pulse h-5 rounded-2xl"></div>
                            <div class="bg-gray-200 w-full animate-pulse h-5 rounded-2xl"></div>
                            <div class="bg-gray-200 w-full animate-pulse h-5 rounded-2xl"></div>
                          </div>
                        </div>
                  </div>
                </div>
              </div>

              <div>
                <div class="m-1.5">
                  <h3 class="text-xl text-slate-600">A small length number</h3>
                    <div class="bg-gray-200 w-8 h-8 rounded-xl text-center ml-4 mt-4 mb-4 animate-pulse">
                      <span class="pt-2 text-2xl text-gray-100 animate-pulse">#</span>
                    </div>
                </div>
              </div>

              <div>
                <div class="m-1.5">
                  <h3 class="text-xl text-slate-600">A shorter lengthed tag or label</h3>
                    <div class="bg-gray-200 w-12 h-6 rounded-xl text-center ml-4 mt-4 mb-4 animate-pulse"></div>
                </div>
              </div>

              <div>
                <div class="m-1.5">
                  <h3 class="text-xl text-slate-600">Charts (Specific)</h3>
                    <div class="relative pl-8 pr-8 pt-4 h-72 overflow-hidden">
                      <div class="bg-gray-200 w-full animate-pulse h-4 rounded-2xl mb-6"></div>
                      <div class="bg-gray-200 w-full h-full rounded-xl">
                        <div class="flex justify-center align-middle animate-pulse pt-8">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 24 24"
                            class="h-36 w-36"
                            stroke="#FFFFFF "
                            stroke-width="1"
                          >
                            <path fill="#FFFFFF" />
                            <path
                              fill="#FFFFFF"
                              d="M3 12h2v9H3v-9zm16-4h2v13h-2V8zm-8-6h2v19h-2V2z"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
              </div>

              <div>
                <div class="m-1.5">
                  <h3 class="text-xl text-slate-600">Chart (Generic)</h3>
                    <div class="flex flex-col flex-1 gap-5 sm:p-2">
                      <div class="bg-gray-200 w-full animate-pulse h-4 rounded-2xl"></div>
                      <div class="h-24 w-full rounded-xl bg-gray-200 animate-pulse"></div>
                    </div>
                </div>
              </div>
              </div>

          </div>

        </div>
      </main>

    </div>

  </div>
</template>

<script>
import { ref } from 'vue'
import Sidebar from '../../partials/Sidebar.vue'
import Header from '../../partials/Header.vue'

export default {
  name: 'ButtonPage',
  components: {
    Sidebar,
    Header,
  },
  setup() {

    const sidebarOpen = ref(false)

    return {
      sidebarOpen,
    }
  }
}
</script>
