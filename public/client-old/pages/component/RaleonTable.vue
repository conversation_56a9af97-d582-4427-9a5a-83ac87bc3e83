<template>
	<div class="flex flex-col h-full">
		<div class="flex flex-col">
			<div class="bg-raltable-ring-primary rounded-2xl h-full p-4">
				<table id="raleon-table" class="table-auto w-full text-xs">
					<thead>
						<tr>
							<th v-for="header in columnHeaders" :key="header.value"
								class="px-4 py-2 text-left text-ralprimary-dark font-semibold first:rounded-tl-2xl last:rounded-tr-2xl">
								<div class="flex">
									<span>{{header.name}}</span>
									<img v-if="header.tooltip" :id="`tooltip-trigger-${header.name.replace(/\s/g, '')}`"
										src="../../images/help-circle.svg" class="pl-3 tooltip-trigger" alt="help" />

									<div :id="`tooltip-body-${header.name.replace(/\s/g, '')}`" role="tooltip"
										class="tooltip-body" style="">
										{{header.tooltip}}
										<div class="tooltip-arrow" data-popper-arrow></div>
									</div>
								</div>
							</th>
							<th class="px-4 py-2">

							</th>
						</tr>
					</thead>

					<tbody class="bg-white" v-if="!isLoading">
						<tr v-for="row in rowData" :key="row">
							<td v-for="(column, index) in this.columnHeaderValues" :key="column"
								class="px-4 py-4 first:font-bold group">

								<a v-if="row.href && index === 0" class="flex w-fit" :href="row.href"
									:target="row.target || '_blank'">
									<span
										class="first:hover:underline first:hover:text-ralprimary-main group first:hover:cursor-pointer"
										:class="{[row[column]?.color]: (!!row[column]?.color)}">
										{{row[column].value ? row[column].value : row[column]}}
										<img v-if="row.href && index == 0 && row.target == '_blank'"
											src="../../images/external-link-out.svg"
											class="invisible group-hover:visible w-3 h-3 float-right"
											style="margin-top: 3px" :class="getHrefStyle(row)" />
									</span>
								</a>
								<div v-if="!row.href || index !== 0" class="flex">
									<span v-if="!allowHtml" :class="{[row[column]?.color]: (!!row[column]?.color)}">
										{{ row[column].value ? row[column].value : row[column] }}
									</span>
									<span v-else v-html="row[column].value ? row[column].value : row[column]"
										:class="{[row[column]?.color]: (!!row[column]?.color)}"></span>
								</div>
							</td>
							<td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
								<div v-if="row.showDelete" class="flex justify-end">
									<button id="raleon-table-delete-row"
										class="btn my-auto ml-4 w-8 h-8 border-slate-200 hover:border-slate-300"
										@click.stop="$emit('delete-row-clicked', row)">
										<svg class="w-4 h-4 fill-slate-300 hover:fill-rose-500 shrink-0"
											viewBox="0 0 16 16">
											<path
												d="M5 7h2v6H5V7zm4 0h2v6H9V7zm3-6v2h4v2h-1v10c0 .6-.4 1-1 1H2c-.6 0-1-.4-1-1V5H0V3h4V1c0-.6.4-1 1-1h6c.6 0 1 .4 1 1zM6 2v1h4V2H6zm7 3H3v9h10V5z" />
										</svg>
									</button>
								</div>
							</td>
						</tr>
					</tbody>


				</table>
				<div class="bg-white p-2 sm:p-4 sm:h-64 flex flex-col sm:flex-row gap-5 select-none" v-if="isLoading">
					<div class="flex flex-col flex-1 gap-5 sm:p-2">
						<div class="flex flex-1 flex-col gap-5">
							<div class="bg-gray-200 w-full animate-pulse h-5 rounded-2xl"></div>
							<div class="bg-gray-200 w-full animate-pulse h-5 rounded-2xl"></div>
							<div class="bg-gray-200 w-full animate-pulse h-5 rounded-2xl"></div>
							<div class="bg-gray-200 w-full animate-pulse h-5 rounded-2xl"></div>
							<div class="bg-gray-200 w-full animate-pulse h-5 rounded-2xl"></div>
							<div class="bg-gray-200 w-full animate-pulse h-5 rounded-2xl"></div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>


<script>

import { createPopper } from '@popperjs/core';

export default {
	name: 'RaleonTable',
	props: {
		columnHeaders: Array,
		rowData: Array,
		showDelete: Boolean,
		isLoading: Boolean,
		allowHtml: {
			type: Boolean,
			default: false
		}
	},
	emits: ['delete-row-clicked'],
	data() {
		return {
			icons: {}
		}
	},
	components: {},
	computed: {
		columnHeaderValues() {
			return this.columnHeaders.map(header => header.value);
		},
	},
	methods: {
		getIcon(iconName) {
			console.log('getting icons');
			return this.icons[`../../images/table-icons/${iconName}`];
		},
		navigate(href) {
			if (href.startsWith('http')) {
				window.open(href, '_blank');
				return;
			}
			let route = this.$router.resolve({ path: href });
			window.open(route.href, '_blank');
		},
		getHrefStyle(row) {
			return row.hrefStyle || '';
		},
		setupTooltips() {
			const triggers = document.querySelectorAll('.tooltip-trigger');
			triggers.forEach(trigger => {
				const triggerId = trigger.getAttribute('id');
				const tooltipId = triggerId.replace('-trigger-', '-body-');
				const tooltip = document.querySelector(`#${tooltipId}`);

				const popperInstance = createPopper(trigger, tooltip, {
					placement: 'top',
					modifiers: [
						{
							name: 'offset',
							options: {
								offset: [0, 8],
							},
						},
					],
				});

				function show() {
					// Make the tooltip visible
					tooltip.setAttribute('data-show', '');

					// Enable the event listeners
					popperInstance.setOptions((options) => ({
						...options,
						modifiers: [
							...options.modifiers,
							{ name: 'eventListeners', enabled: true },
						],
					}));

					// Update its position
					popperInstance.update();
				}

				function hide() {
					// Hide the tooltip
					tooltip.removeAttribute('data-show');

					// Disable the event listeners
					popperInstance.setOptions((options) => ({
						...options,
						modifiers: [
							...options.modifiers,
							{ name: 'eventListeners', enabled: false },
						],
					}));
				}

				const showEvents = ['mouseenter', 'focus'];
				const hideEvents = ['mouseleave', 'blur'];

				showEvents.forEach((event) => {
					trigger.addEventListener(event, show);
				});

				hideEvents.forEach((event) => {
					trigger.addEventListener(event, hide);
				});
			});
		}
	},
	async beforeCreate() {
		console.log('before create');
		this.icons = import.meta.glob('../../images/table-icons/*.svg', {
			as: 'raw',
			eager: true,
		});
	},
	async mounted() {
		this.setupTooltips();
	},
}
</script>

<style>
.tooltip-body {
	display: none;
}

.tooltip-body[data-show] {
	display: inline;
}

.tooltip-body {
	background: #333;
	color: white;
	font-weight: bold;
	padding: 4px 8px;
	font-size: 13px;
	border-radius: 4px;
	display: none;
}

.tooltip-body[data-show] {
	display: block;
}

.tooltip-arrow,
.tooltip-arrow::before {
	position: absolute;
	width: 8px;
	height: 8px;
	background: inherit;
}

.tooltip-arrow {
	visibility: hidden;
	padding-left: 5px;
}

.tooltip-arrow::before {
	visibility: visible;
	content: '';
	transform: rotate(45deg);
}

.tooltip-body[data-popper-placement^='top']>#arrow {
	bottom: -4px;
}

.tooltip-body[data-popper-placement^='bottom']>#arrow {
	top: -4px;
}

.tooltip-body[data-popper-placement^='left']>#arrow {
	right: -4px;
}

.tooltip-body[data-popper-placement^='right']>#arrow {
	left: -4px;
}
</style>
