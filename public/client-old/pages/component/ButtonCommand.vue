<template>
	<div class="flex rounded-md bg-white border border-ralprimary-ultralight mt-8 hover:shadow-xl transition duration-500">
		<div class="cursor-move w-4"></div>
		<div class="flex flex-col w-full">
		<div class="flex justify-between items-center p-4">
			<div>
			<input class="text-lg font-bold bg-white border-0 outline-0 outline-none p-2 rounded-md hover:bg-slate-200 transition-colors duration-500" v-model="commandTitle"
			:class="{ 'text-slate-800 border-none outline-0': isEditingTitle, 'text-slate-600 italic': !isEditingTitle }"
			@focus="isEditingTitle = true"
			@blur="isEditingTitle = false; updateDetails()"
			/>
			</div>
			<div>
				<select v-model="selectedType" class="rounded-md" @change="changeType">
					<option v-for="command in commandsAvailable" :key="command.id" :value="command.value">
						{{ command.name }}
					</option>
				</select>
			</div>
		</div>
		<div class="p-4 flex-grow">
			<textarea class="w-full h-full bg-white border-0 focus:outline-0 placeholder-slate-600 p-2 rounded-md hover:bg-slate-200 transition-colors duration-500" v-model="bodyText"
			:class="{ 'text-slate-800 border-none focus:outline-0': isEditing, 'text-slate-600 italic': !isEditing }"
			@focus="isEditing = true"
			@blur="isEditing = false; updateDetails()"
			placeholder="Type your message here..."
			>
			></textarea>
		</div>

		<div class="flex flex-col space-y-4">
			<div class="flex items-center space-x-4" v-for="(r, index) in buttonList" :key="index">
				<input type="text" placeholder="Button text" class="text-lg bg-white border-0 outline-0 outline-none italic focus:outline-0 focus:text-slate-600 focus:font-normal p-2 rounded-md hover:bg-slate-200 transition-colors duration-500" v-model="r.text"  @change="updateDetails()" />

				<select v-model="r.nextId" class="flex-1 px-4 py-2 border rounded w-1/6"  @change="updateDetails()">
					<option v-for="com in commandList" :key="com.id" :value="com.id">
						{{ com.name }}
					</option>
				</select>
				<button class="px-2 py-2 text-red-500 border border-red-500 rounded" @click="removeButton(index)">
				<i class="fas fa-trash"></i>
				</button>
			</div>

		<button @click="addButton">Add button</button>
		</div>
		<div class="flex justify-between items-center p-4">
			<div class="flex items-center">

			</div>
			<div class="flex items-center">
			<!-- <button @click="duplicate"><svg xmlns="http://www.w3.org/2000/svg" height="21" viewBox="0 -960 960 960" width="21"><path d="M180-81q-24 0-42-18t-18-42v-603h60v603h474v60H180Zm120-120q-24 0-42-18t-18-42v-560q0-24 18-42t42-18h440q24 0 42 18t18 42v560q0 24-18 42t-42 18H300Zm0-60h440v-560H300v560Zm0 0v-560 560Z"/></svg></button> -->
			<button @click="deleteCommand" class="ml-4"><svg xmlns="http://www.w3.org/2000/svg" height="21" viewBox="0 -960 960 960" width="21" fill="#A7ADB7"><path d="M261-120q-24.75 0-42.375-17.625T201-180v-570h-41v-60h188v-30h264v30h188v60h-41v570q0 24-18 42t-42 18H261Zm438-630H261v570h438v-570ZM367-266h60v-399h-60v399Zm166 0h60v-399h-60v399ZM261-750v570-570Z"/></svg></button>
			</div>
		</div>
		</div>
	</div>
  </template>

  <script>
  import { ref } from 'vue'
  import Sidebar from '../../partials/Sidebar.vue'
  import Header from '../../partials/Header.vue'

  export default {
	name: 'MultipleChoice',
	props: ['commandList', 'compId', 'commandsAvailable', 'hydrate', 'sender'],
	components: {
	  Sidebar,
	  Header,
	},
	setup() {

	  const sidebarOpen = ref(false)

	  return {
		sidebarOpen,
	  }
	},
	data() {
		return {
			isEditing: false,
				isEditingTitle: false,
				commandTitle: 'Multiple Choice ' + this.compId,
				bodyText: '',
				selectedType: "simple-button",
				selectedNextComponent: null,
				buttonList: [],
				btnId: 0,
		}
	},
	async mounted() {
		console.log("Created component with ID " + this.compId);
		if(this.hydrate != null) {
			this.loadScript(this.hydrate);
		}
		if (!this.buttonList.length) {
			this.buttonList.push({
				name: '',
				nextId: '',
			})
		}
		this.updateDetails(); //For creating the list
	},
	methods: {
		deleteCommand() {
			this.$emit('deleteRequested', this);
		},
		changeType() {
			let typeInfo = {id: this.compId, newType: this.selectedType};
			this.$emit('updateType', typeInfo);
		},
		updateDetails() {
			console.log("This ran" + this.commandTitle);
			let updatedInfo = {
				id: this.compId,
				title: this.commandTitle,
				type: this.selectedType
			};
			this.$emit('updateDetails', updatedInfo);
		},
		addButton() {
			this.buttonList.push({
				text: '',
				nextId: '',
			})
		},
		removeButton(index) {
      		this.buttonList.splice(index, 1);

			this.updateDetails();
    	},
		loadScript(qs) {
			this.bodyText = qs.data.message;

			console.log("Length of buttons is: " + qs.data.buttons.length);

			qs.data.buttons.forEach(btn => {
				if(btn.text != null) {
					this.buttonList.push({
					text: btn.text,
					nextId: btn.onClick
					});
				}
			});
			this.updateDetails();
		},
		generateSyntax() {

			let buttonSyntax = '';
			this.buttonList.map((button, index) => {

				let isLastItem = index === this.buttonList.length - 1;

				if(isLastItem) {
					buttonSyntax += `
					{
						"text": ${JSON.stringify(button.text || '')},
						"onClick": ${JSON.stringify(button.nextId || null)}
					}`;
				} else {
					buttonSyntax += `
					{
						"text": ${JSON.stringify(button.text || '')},
						"onClick": ${JSON.stringify(button.nextId || null)}
					},`;
				}

			})
			return `
				{
					"type": "simple-button",
					"id": `+this.compId+`,
					"data": {
						"message": ${JSON.stringify(this.bodyText || '')},
						"sender": ${JSON.stringify(this.sender || '')},
						"buttons": [`
							+buttonSyntax+
						`]
					},
					"next": {
						"condition": "event"
					}
				}
				`;
		}
	}
  }
  </script>
