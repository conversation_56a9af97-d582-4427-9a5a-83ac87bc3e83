<template>
	<div class="flex rounded-md bg-white border border-ralprimary-ultralight mt-8 hover:shadow-xl transition duration-500">
		<div class="cursor-move w-4"></div>
		<div class="flex flex-col w-full">
		<div class="flex justify-between items-center p-4">
			<div>
			<input class="text-lg font-bold bg-white border-0 outline-0 outline-none p-2 rounded-md hover:bg-slate-200 transition-colors duration-500" v-model="commandTitle"
			:class="{ 'text-slate-800 border-none outline-0 bg-slate-200': isEditingTitle, 'text-slate-600 italic': !isEditingTitle }"
			@focus="isEditingTitle = true"
			@blur="isEditingTitle = false; updateDetails()"
			/>
			</div>
			<div>
				<select v-model="selectedType" class="rounded-md" @change="changeType">
					<option v-for="command in commandsAvailable" :key="command.id" :value="command.value">
						{{ command.name }}
					</option>
				</select>
			</div>
		</div>
		<div class="p-4 flex-grow">
			<h4 class="font-bold">What Quest Actions would you like to use?</h4>
			<span class="text-xs block">Quest Actions are what a user is required to complete before gaining their reward.</span>
<!--
			<select >
				<option value="1">Campaign 1</option>
			</select> -->


			<div class="flex items-start space-between flex-col" v-if="!isClone">
				<button @click="selectedBlockId = 'action'" class="bg-ralprimary-text-white light border border-ralprimary-light font-bold text-xs py-2 px-4 rounded-button m-4">Configure Actions</button>
				<button @click="selectedBlockId = 'reward'" class="bg-ralprimary-text-white light border border-ralprimary-light font-bold text-xs py-2 px-4 rounded-button m-4 mt-0">Configure Reward</button>

				<ActionBlock
					v-if="campaignReady"
					:hidden="true"
					:fixed="true"
					:goalData="actionData"
					:selectedBlockId="selectedBlockId"
					:blockId="'action'"
					@click="selectedBlockId = 'action'"
					@closeEditPane="selectedBlockId = ''"
					@blockUpdated="(block) => this.actionBlock = block"

					style="display:none"

				></ActionBlock>
				<RewardBlock
					v-if="campaignReady"
					:hidden="true"
					:fixed="true"
					:rewardData="rewardData"
					:rewardType="rewardType"
					:selectedBlockId="selectedBlockId"
					:blockId="'reward'"
					@click="selectedBlockId = 'reward'"
					@closeEditPane="selectedBlockId = ''"
					@rewardSelected="(type) => rewardType = type"
					@templateSelected="(template) => rewardData = {...template}"
					@blockUpdated="(block) => this.rewardBlock = block"

					style="display:none"

				></RewardBlock>
			</div>

			<h4 class="font-bold mt-8">When the Quest is completed, what should the user be sent to next?</h4>
			<div class="flex items-center">
			<span class="mr-2">Next</span>
				<select v-model="selectedNextComponent" class="rounded-md"  @change="updateDetails()">
					<option v-for="com in commandList" :key="com.id" :value="com.id">
						{{ com.name }}
					</option>
				</select>
			</div>

		</div>
		<div class="flex justify-between items-center p-4">
			<div class="flex items-center">

			</div>
			<!-- <div class="flex items-center">
				<span class="mr-2">Delay</span>
				<input type="number"
				placeholder="(in seconds, optional)"
				class="text-lg bg-white border-0 outline-0 outline-none italic focus:outline-0 focus:text-slate-600 focus:font-normal p-2 rounded-md hover:bg-slate-200 transition-colors duration-500"
				@change="updateDetails()"
				v-model="delay" />
			</div>
			<div class="flex-grow"></div> -->
			<div class="flex items-center">
			<!-- <button @click="duplicate"><svg xmlns="http://www.w3.org/2000/svg" height="21" viewBox="0 -960 960 960" width="21"><path d="M180-81q-24 0-42-18t-18-42v-603h60v603h474v60H180Zm120-120q-24 0-42-18t-18-42v-560q0-24 18-42t42-18h440q24 0 42 18t18 42v560q0 24-18 42t-42 18H300Zm0-60h440v-560H300v560Zm0 0v-560 560Z"/></svg></button> -->
			<button @click="deleteCommand" class="ml-4"><svg xmlns="http://www.w3.org/2000/svg" height="21" viewBox="0 -960 960 960" width="21" fill="#A7ADB7"><path d="M261-120q-24.75 0-42.375-17.625T201-180v-570h-41v-60h188v-30h264v30h188v60h-41v570q0 24-18 42t-42 18H261Zm438-630H261v570h438v-570ZM367-266h60v-399h-60v399Zm166 0h60v-399h-60v399ZM261-750v570-570Z"/></svg></button>
			</div>
		</div>
		</div>
	</div>


	<!--<CampaignEditPane
		:is-open="isRewardEditorOpen"
		:title="rewardTitle || 'Untitled Reward'"
		@close-edit-pane="isRewardEditorOpen = false">
		<div class="mt-4">
			<label class="block text-sm font-medium mb-1" for="whenReward">
				When
				<span class="text-rose-500">*</span>
			</label>
			<DropdownSegmentMenu
				class="relative inline-flex w-full mb-4"
				:title="selectedReward?.friendlyName || 'Select a Reward'">
				<li
					v-for="reward of availableRewards"
					class="flex items-center text-sm w-full hover:bg-ralprimary-highlighted py-1 px-3 cursor-pointer"
					@click="rewardSelected(reward)">
					{{reward.friendlyName}}
				</li>
			</DropdownSegmentMenu>
		</div>

		<div class="mt-4" v-if="availableTemplates">
			<label class="block text-sm font-medium mb-1" for="whenReward">
				Template
			</label>
			<DropdownSegmentMenu
				class="relative inline-flex w-full mb-4"
				:title="'Select a Template'">
				<li
					v-for="template of availableTemplates"
					class="flex items-center text-sm w-full hover:bg-ralprimary-highlighted py-1 px-3 cursor-pointer"
					@click="applyTemplate(template)">
					{{template[templateKey]}}
				</li>
			</DropdownSegmentMenu>
		</div>

		<label v-if="availableTemplates" class="block text-sm font-medium mb-1 flex">
			Template
		</label>
		<select v-if="availableTemplates" @change="applyTemplate($event)">
			<option value="" selected="selected">--</option>
			<option v-for="template in availableTemplates" :value="template[templateKey]">{{template[templateKey]}}</option>
		</select>


		<template v-for="input of inputData">
			<label class="block text-sm font-medium mb-1 flex">
				{{ input.friendlyName }}
				<span class="text-rose-500" v-if='!input.optional'>*</span>
				<img v-if="input.description" :id="`tooltip-trigger-${input.friendlyName.replace(/\s/g, '')}`"
					src="../../images/help-circle.svg" class="pl-3 tooltip-trigger" alt="help" />

				<div :id="`tooltip-body-${input.friendlyName.replace(/\s/g, '')}`" role="tooltip"
					class="tooltip-body" style="">
					{{input.description}}
					<div class="tooltip-arrow" data-popper-arrow></div>
				</div>
			</label>
			<input
				v-if="isInputVisible(input)"
				:type="input.inputType"
				class="form-input w-full mb-4"
				:placeholder="`Enter ${input.friendlyName}`"
				v-model="input.value"
				@input="inputUpdated"
			/>
			<span
				v-if="input.inputType === 'uri' && input.fileUploadEndpoint && !isFileInputVisible(input) && !input.value"
				@click="setFileInputVisible(input, true)"
				style="position: absolute;
						right: 3.5rem;
						margin-top: 8px;
						font-size: 0.8em;
						text-decoration: underline;
						cursor: pointer;"
			>
				Upload a File
			</span>
			<form
				v-if="isFileInputVisible(input)">
				<input
					type="file"
					class="form-input w-full mb-4"
					:placeholder="`Enter ${input.friendlyName}`"
					@change="uploadFile($event, input)"
				/>
			</form>
		</template>
	</CampaignEditPane> -->
  </template>

  <script>
  import { ref } from 'vue'
  import Sidebar from '../../partials/Sidebar.vue'
  import Header from '../../partials/Header.vue'
  import CampaignEditPane from '../campaign-builder/CampaignEditPane.vue';
  import DropdownSegmentMenu from '../../components/DropdownSegmentMenu.vue';
  import RewardBlock from '../campaign-builder/building-blocks/RewardBlock.vue';
  import ActionBlock from '../campaign-builder/building-blocks/ActionBlock.vue';
import { fetchRewardTypes } from '../campaign-builder/campaign-service';
import { useCampaignStore } from '../campaign-builder/stores/campaign-store';
import { uuidv4 } from '../../utils/Utils';
import { blockToGoals, blockToReward } from '../campaign-builder/transformers/block-transformer';
import { goalsToBlock, rewardToBlock } from '../campaign-builder/transformers/campaign-transformer';


  export default {
	name: 'ShowQuestCommand',
	props: ['commandList', 'compId', 'commandsAvailable', 'hydrate', 'rewardTypes', 'generateNewIds'],
	components: {
	  Sidebar,
	  Header,
	  CampaignEditPane,
	  DropdownSegmentMenu,
	  RewardBlock,
	  ActionBlock
	},
	setup() {

	  const sidebarOpen = ref(false)

	  return {
		sidebarOpen,
	  }
	},
	data() {
		return {
			isEditing: false,
			campaignReady: false,
			isEditingTitle: false,
			commandTitle: 'Quest Actions ' + this.compId,
			selectedType: "show-quest-info",
			selectedNextComponent: null,
			isRewardEditorOpen: false,
			isActionEditorOpen: false,
			// selectedReward: null,
			// availableRewards: [],
			campaignStore: null,
			rewardBlock: null,
			rewardData: {},
			rewardType: null,
			selectedBlockId: 'null',

			actionBlock: null,
			actionData: [{
				externalId: uuidv4()
			}],
			delay: null
		}
	},
	computed: {
		isClone() {
			return this.commandList.find(x => x.props?.rewardTypes)?.props?.compId !== this.compId;
		},
	},
	async mounted() {
		console.log("Created component with ID " + this.compId);
		if(this.hydrate != null) {
			this.loadScript(this.hydrate);
		}
		// fetchRewardTypes().then(result => this.availableRewards = result);
		this.updateDetails(); //For creating the list
	},
	methods: {
		deleteCommand() {
			this.$emit('deleteRequested', this);
		},
		openActionsEditor() {
			this.isActionEditorOpen = true;
		},
		openRewardEditor() {
			this.isRewardEditorOpen = true;
		},
		changeType() {
			let typeInfo = {id: this.compId, newType: this.selectedType};
			this.$emit('updateType', typeInfo);
		},
		// rewardSelected(reward) {
		// 	this.selectedReward = reward;
		// 	Object.keys(this.rewardData).forEach(x => this.rewardData[x] = undefined);
		// 	this.inputUpdated();

		// 	this.$nextTick(() => this.setupTooltips());
		// },
		updateDetails() {
			console.log("This ran" + this.commandTitle);
			let updatedInfo = {
				id: this.compId,
				title: this.commandTitle,
				type: this.selectedType
			};
			this.$emit('updateDetails', updatedInfo);
			this.campaignReady = true;
		},
		loadScript(qs) {
			this.points = qs.data.xp;
			this.season = qs.data.season;
			this.selectedNextComponent = qs.next.id ;
			if (this.generateNewIds) {
				qs.data?.actions?.goals?.forEach(x => x.externalId = uuidv4());

				if (qs.data?.reward?.externalId) {
					qs.data.reward.externalId = uuidv4();
				}
			}
			this.rewardData = qs.data?.reward?.configData || {};
			this.rewardType = this.rewardTypes.find(x => x.rewardType === qs.data?.reward?.type);
			this.actionData = qs.data?.actions?.goals;
			this.delay = qs.next.delay || null;
			this.campaignStore = useCampaignStore();
			this.updateDetails();
		},
		generateSyntax() {
			return `
			{
				"type": "show-quest-info",
				"id": `+this.compId+`,
				"data": {
					"onComplete": ${JSON.stringify(this.selectedNextComponent || null)},
					"onRewardReady": 0,
					"onRewardClaimed": 0
					${!this.isClone
						? `,"reward": ${JSON.stringify(this.generateReward())},
							"actions": ${JSON.stringify(this.generateActions())}`
						: '' }
				},
				"next": {
					"id": ${JSON.stringify(this.selectedNextComponent || null)},
					"condition": "event"
				}
			}
				`;
		},
		generateReward() {
			const reward = blockToReward(this.rewardBlock, this.rewardTypes);

			if (this.generateNewIds && reward?.externalId) {
				reward.externalId = uuidv4();
			}

			return reward;
		},
		generateActions() {
			const actions = blockToGoals(this.actionBlock);

			if (this.generateNewIds) {
				actions?.goals?.forEach(x => x.externalId = uuidv4());
			}

			return actions;
		}
	}
  }
  </script>
