<template>
	<div class="flex h-screen overflow-hidden">

		<!-- Sidebar -->
		<Sidebar :sidebarOpen="sidebarOpen" @close-sidebar="sidebarOpen = false" />

		<!-- Content area -->
		<div class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">

			<!-- Site header -->
			<main>
				<div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">
					<!-- Page header -->
					<div v-if="isLoaded" class="flex sm:justify-between sm:items-center mb-8">

						<!-- Left: Title -->
						<EditableHeader
							:header-text="segmentName"
							@updated-header="updateSegmentName"
						/>

						<div class="flex self-end">
							<DropdownEditMenu
								data-cy="segment-edit-segment"
								class="relative inline-flex hover:border ml-2 border border-slate-100 hover:border-slate-300">
								<li>
									<a
										data-cy="segment-open-edit-segment"
										class="font-medium text-sm text-slate-600 hover:text-slate-800 flex py-1 px-3 cursor-pointer"
										@click.stop="openEditSegment()">Settings</a>
								</li>
							</DropdownEditMenu>
						</div>
					</div>

					<!-- Edit Segment -->
					<ModalBasic id="edit-segment-modal" :modalOpen="editSegmentModelOpen"
						@close-modal="editSegmentModelOpen = false" title="Edit Segment Details">
						<!-- Modal content -->
						<div class="px-5 py-4">
							<div class="text-sm mb-5">
								<form>
									<div class="space-y-4">
										<div>
											<label class="block text-sm font-medium mb-1" for="segment-name-input">Name</label>
											<input data-cy="segment-name-input" class="form-input w-full" type="text"
												placeholder="Type something you will recognize..." v-model="dirtySegment.name" />
										</div>
										<div>
											<label class="block text-sm font-medium mb-1" for="segment-description-input">Description</label>
											<input id="segment-description-input" class="form-input w-full px-4 py-3" type="text"
												v-model="dirtySegment.description" />
										</div>

										<div class="pt-4">
											<label class="block text-xs italic" for="large">Created by {{segmentOwnerName}}</label>
										</div>
									</div>
								</form>
							</div>
						</div>
						<!-- Modal footer -->
						<div class="px-5 py-4 border-t border-slate-200">
							<div class="flex flex-wrap justify-end space-x-2">
								<button class="
							 btn-sm
							 border-slate-200
							 hover:border-slate-300
							 text-slate-600
							" @click.stop="clearEditSegment()">
									Cancel
								</button>
								<button
									data-cy="segment-save-changes"
									class="btn-sm bg-indigo-500 hover:bg-indigo-600 text-white"
									@click="updateSegment()">
									Save Changes
								</button>
							</div>
						</div>
					</ModalBasic>
					<!-- End Edit Data -->

					<Notification :open="notificationInfoOpen" class="mb-6" v-if="isPublished">
						<div class="font-medium text-slate-800 mb-1">
							This segment has been published and cannot be edited. To edit it, you may <span
								class="text-ralpurple-500 cursor-pointer" @click="duplicateSegment()">duplicate the segment</span> and
							publish it as new.
						</div>
					</Notification>

					<RefreshDataCard
						v-if="isPublished"
						class="mb-6"
						:card-text="`Refreshing <strong>${segmentName}'s</strong> data temporarily rushes data enrichment.`"
						:done-refreshing="doneRefreshingData"
						:error-refreshing="errorRefreshingData"
						:show-smart-contract-button="false"
						button-text="Pull Latest Data"
						@refresh-data-clicked="startSegmentIngestion">
					</RefreshDataCard>

					<!-- Tabs -->
					<div class="relative mb-8">
						<div class="absolute bottom-0 w-full h-px bg-slate-200" aria-hidden="true"></div>
						<ul class="
								relative
								text-sm
								font-medium
								flex flex-nowrap
								-mx-4
								sm:-mx-6
								lg:-mx-8
								overflow-x-scroll
								no-scrollbar
							">
							<li class="
									mr-6
									last:mr-0
									first:pl-4
									sm:first:pl-6
									lg:first:pl-8
									last:pr-4
									sm:last:pr-6
									lg:last:pr-8
								" v-on:click="toggleTabs(0)">
								<span v-bind:class="{
									'block pb-3 text-indigo-500 whitespace-nowrap border-b-2 border-indigo-500':
										openTab === 0,
								}" class="
										flex
										block
										pb-3
										text-slate-500
										hover:text-slate-600
										whitespace-nowrap
										hover:border-b-2 hover:border-indigo-500
										cursor-pointer
									">Definition</span>
							</li>
							<li class="
									mr-6
									last:mr-0
									first:pl-4
									sm:first:pl-6
									lg:first:pl-8
									last:pr-4
									sm:last:pr-6
									lg:last:pr-8
								" v-on:click="toggleTabs(1)">
								<span class="flex block pb-3 text-slate-500 whitespace-nowrap cursor-pointer" v-bind:class="{
										'block pb-3 text-indigo-500 whitespace-nowrap border-b-2 border-indigo-500':
											openTab === 1,
										'cursor-not-allowed text-slate-300': isPublished == false,
										'hover:border-b-2 hover:border-indigo-500 hover:text-slate-600': isPublished == true
								}">
									<span>{{walletTabDisplay}}</span>
								</span>
							</li>
						</ul>
					</div>

					<!-- tab content -->
					<div v-bind:class="{
						'hidden': openTab !== 0,
						'block animate-fadein-content ': openTab === 0,
					}">

						<div class="grid grid-cols-12 gap-4">
							<div class="col-span-8 bg-white shadow-lg rounded-sm border border-slate-200 relative pb-2">
								<header class="flex px-5 py-4 border-b border-slate-200">

									<span class="text-base ml-3 mt-2">Contains all wallets from </span>
									<DropdownSegmentMenu class="relative inline-flex ml-3 mt-1"
										:title="selectedProject.name || 'Select Project'" :disabled="isPublished" v-slot="scope">
										<li v-for="project in projects" :key="project.id"
											class="flex items-center w-full hover:bg-slate-50 py-1 px-3 cursor-pointer"
											:class="project.id === selectedProject.id && 'text-indigo-500'" @click="selectProject(project)">
											<a
												class="font-medium text-sm text-slate-600 hover:text-slate-800 flex py-1 px-3">{{project.name}}</a>
										</li>
									</DropdownSegmentMenu>

									<!-- Include Smart Contracts -->
									<div class="flex items-center ml-8 mt-1">
										<div class="form-switch">
											<input type="checkbox" id="toggle1" class="sr-only" v-model="includeSmartContracts" />
											<label class="bg-slate-400" for="toggle1">
												<span class="bg-white shadow-sm" aria-hidden="true"></span>
												<span class="sr-only">Include Smart Contracts</span>
											</label>
										</div>
										<div class="text-sm text-slate-400 italic ml-2">{{includeSmartContractsDisplay}}</div>
									</div>

									<div class="flex items-center ml-8 mt-1">
										<div class="form-switch">
											<input type="checkbox" id="toggle2" class="sr-only" v-model="metricsEnabled" @change="handleMetricsToggle" />
											<label class="bg-slate-400" for="toggle2">
												<span class="bg-white shadow-sm" aria-hidden="true"></span>
												<span class="sr-only">Metrics Enabled</span>
											</label>
										</div>
										<div class="text-sm text-slate-400 italic ml-2">{{includeMetricsEnabledDisplay}}</div>
									</div>
								</header>

								<Instruction v-for="i in instructions" :key="i.id" :id="i.id" @remove-Instruction="removeInstruction"
									:projects="projects" :is-disabled="JSON.stringify(selectedProject) == '{}'"
									:position="getPosition(i.id)" :instruction-data="i" :isNowPublished="isPublished"
									@update:instruction="updateInstruction($event)" />

								<button
									v-if="!isPublished"
									data-cy="segment-add-instruction-button"
									class="btn-xs border-slate-200 hover:border-ralgranite-300 text-ralgranite-300 hover:text-ralgranite-400-white mt-2 ml-4 mb-4"
									@click.stop="addInstruction()">
									And wallets who
								</button>
							</div>

							<div class="col-span-4 bg-slate-50 shadow-lg rounded-sm border border-slate-200 relative max-h-92">
								<SegmentPreview
									:wallets="previewData.walletIds"
									:count="previewData.totalCount"
									:hasMore="previewData.hasMore"
									:isCreateSegmentReady="disableCreateSegmentButton"
									:isPreviewLoading="previewLoading"
									:isPublishLoading="publishLoading"
									:isNowPublished="isPublished"
									@run-preview="previewSegment()"
									@run-segment="publishSegment()"
									@run-duplicate="duplicateSegment()" />
							</div>
						</div>

					</div>

					<div v-bind:class="{
						hidden: openTab !== 1,
						'block animate-fadein-content ': openTab === 1,
					}">
						<SegmentWalletList
							v-if="this.isPublished"
							:segment-name="segmentName"
							:segment-id="recordId"
							:segment-view-name="this.segmentViewName"
						/>
					</div>
					<!-- end tab content -->
				</div>
			</main>
		</div>
	</div>
</template>

<script>
import { ref } from 'vue';
import { SegmentFactory } from '../../../../src/segment-builder/segment-factory';
import { SubjectType } from '../../../../src/segment-builder/segment.abstract';
import Datepicker from '../../components/Datepicker.vue';
import DropdownClassic from '../../components/DropdownClassic.vue';
import DropdownEditMenu from '../../components/DropdownEditMenu.vue';
import DropdownSegmentMenu from '../../components/DropdownSegmentMenu.vue';
import ModalBasic from '../../components/ModalBasic.vue';
import EditableHeader from '../../components/EditableHeader.vue';
import ModalBlank from '../../components/ModalBlank.vue';
import Notification from '../../components/Notification.vue';
import PaginationNumeric from '../../components/PaginationNumeric.vue';
import SearchForm from '../../components/SearchForm.vue';
import RefreshDataCard from '../../components/support/RefreshDataCard.vue';
import Header from '../../partials/Header.vue';
import PersonalizationTable from '../../partials/personalization/PersonalizationTable.vue';
import Sidebar from '../../partials/Sidebar.vue';
import { prioritizeIngestionForView } from '../../services/data-ingestion';
import { SegmentBuilderDataLoader } from '../../services/segment-builder-data-loader';
import {
	buildSmartContractInstruction,
	previewSegment,
	setMetricsEnabled,
	publishSegment,
	saveSegmentToDB,
	updateMetricsEnabledToDB,
	SEGMENT_STATUS,
	updateSegment,
	getSegmentById,
} from '../../services/segmentbuilder';
import { isEmpty, uuidv4 } from '../../utils/Utils';
import Instruction from './Instruction.vue';
import SegmentPreview from './SegmentPreview.vue';
import SegmentWalletList from './SegmentWalletList.vue';

let orgId;

export default {
	name: 'SegmentBuilder',
	emits: ['removeInstruction'],
	props: ['segmentData'],
	components: {
    Sidebar,
    Header,
    SearchForm,
    PaginationNumeric,
    PersonalizationTable,
    Notification,
    DropdownSegmentMenu,
    DropdownClassic,
    DropdownEditMenu,
    Datepicker,
    Instruction,
    SegmentPreview,
    SegmentWalletList,
    ModalBasic,
    ModalBlank,
	EditableHeader,
    RefreshDataCard
},
	data() {
		return {
			projects: [],
			includeSmartContracts: true,
			metricsEnabled: false,
			selectedProject: {},
			rootInstruction: { id: 0 },
			instructions: [],
			instructionCount: 0,
			isLoaded: false,
			segmentName: '',
			segmentDescription: '',
			segmentOwnerName: '',
			segmentViewName: '',
			dbSegment: {},
			dirtySegment: {},
			previewLoading: false,
			publishLoading: false,
			previewData: {},
			openTab: 0,
			isPublished: false,
			deleteSegmentModelOpen: false,
			editSegmentModelOpen: false,
			recordId: null, // segment id in postgres database
			doneRefreshingData: false,
			errorRefreshingData: false,
		}
	},
	computed: {
		instructionCount() {
			if (this.instructions != null)
				return this.instructions.length - 1;
		},
		disableCreateSegmentButton() {
			if (isEmpty(this.selectedProject)) return true;
			if (this.instructions.length < 1) return false;
			let lastInstruction = this.instructions[this.instructions.length - 1];

			let requiresSubject = true;
			if (lastInstruction.instruction) {
				requiresSubject = SegmentFactory.createSegmentFromInstruction(
					lastInstruction.instruction,
					lastInstruction.id,
					orgId
				).getPossibleSubjects().length > 0;
			}
			return !lastInstruction.instruction || (isEmpty(lastInstruction.subject) && requiresSubject);
		},
		includeSmartContractsDisplay() {
			return `Smart Contracts ${this.includeSmartContracts ? 'Included' : 'Excluded'}`;
		},
		includeMetricsEnabledDisplay() {
			return `Metrics ${this.metricsEnabled ? 'Enabled' : 'Disabled'}`;
		},
		walletTabDisplay() {
			return `Wallet Results${this.isPublished ? '' : ' (Publish First)'}`;
		}
	},
	methods: {
		async handleMetricsToggle() {
			let result = await setMetricsEnabled(this.segmentViewName, this.metricsEnabled);
			console.log("Metrics Enabled Result: ", JSON.stringify(result));

			console.log("Lets grab actual write response");
			if(result?.body?.metricsEnabled != undefined) {
				console.log("Found metricsEnabled in response body")
				this.metricsEnabled = result.body.metricsEnabled;
			}

			console.log("Lets save to DB", this.metricsEnabled)
			if(this.recordId) {
				let saveToDB = await updateMetricsEnabledToDB(this.recordId, this.metricsEnabled);
				console.log("Save to DB Result: ", saveToDB);
			}
		},
		openEditSegment() {
			amplitude.getInstance().logEvent('SEGMENT_SETTINGS');
			this.dirtySegment = { 'name': this.segmentName, 'description': this.segmentDescription };
			this.editSegmentModelOpen = true;
		},
		clearEditSegment() {
			this.dirtySegment = {};
			this.editSegmentModelOpen = false;
		},
		async updateSegmentName(name) {
			this.segmentName = name;
			if (!name.trim() || name.trim() == 'Untitled') {
				this.segmentName = 'Untitled';
			}
			if (this.recordId) {
				await updateSegment(
					this.recordId,
					{ name: this.segmentName, description: this.segmentDescription }
				);
			}
		},
		async updateSegment() {
			this.segmentName = this.dirtySegment.name;
			this.segmentDescription = this.dirtySegment.description;
			if (this.recordId) {
				await updateSegment(
					this.recordId,
					{ name: this.segmentName, description: this.segmentDescription }
				);
			}
			this.editSegmentModelOpen = false;
		},
		deleteSegment() {
			this.deleteSegmentModelOpen = false;
		},
		toggleTabs(tabNumber) {
			if (this.isPublished) {
				this.openTab = tabNumber;
			}
		},
		getPosition(id) {
			if (this.instructions != null) {
				if (this.instructions[this.instructions.length - 1].id == id) {
					return 'last';
				}
			}
		},
		removeInstruction(id) {
			amplitude.getInstance().logEvent('SEGMENT_REMOVE_INSTRUCTION');
			this.instructions.splice(this.instructions.findIndex(i => i.id == id), 1);
		},
		addInstruction() {
			amplitude.getInstance().logEvent('SEGMENT_ADD_INSTRUCTION');
			this.instructions.push({ id: uuidv4() });
		},
		timeSelection(toggle) {
			if (toggle == null) {
				this.time = false;
			} else {
				this.time = toggle;
			}
		},
		selectProject(project) {
			amplitude.getInstance().logEvent('SEGMENT_PROJECT_CHANGE');
			this.selectedProject = project;
			const sourceSegment = SegmentFactory.createSegment(
				'ProjectUsersSegment',
				this.rootInstruction.id
			);
			this.rootInstruction = sourceSegment.setSubject({
				type: SubjectType.PROJECT.value,
				value: project.id
			}).buildSegment();
		},
		updateInstruction(instruction) {
			const idx = this.instructions.findIndex(i => i.id === instruction.id);
			this.instructions[idx] = instruction;
		},
		getInstructionsToBuild() {
			this.instructions = this.instructions.filter(i => !!i.instruction);
			const instructions = [this.rootInstruction, ...this.instructions];
			if (!this.includeSmartContracts) {
				instructions.push(buildSmartContractInstruction());
			}
			return instructions;
		},
		async duplicateSegment() {
			amplitude.getInstance().logEvent('SEGMENT_DUPLICATED');
			let dbResult = await saveSegmentToDB(
				this.segmentName + ' Copy 1',
				'ETH',
				orgId,
				this.getInstructionsToBuild(),
				undefined,
				SEGMENT_STATUS.DRAFT,
				this.segmentDescription,
				this.metricsEnabled
			);
			this.recordId = dbResult.id;

			this.segmentLoader(JSON.stringify(dbResult));
		},
		async publishSegment() {
			amplitude.getInstance().logEvent('SEGMENT_PUBLISHED');
			this.publishLoading = true;

			const instructionsToBuild = this.getInstructionsToBuild();

			const result = await publishSegment(
				this.segmentName,
				'ETH',
				orgId,
				instructionsToBuild,
				this.recordId,
				this.segmentDescription
			);
			console.log("Publish Segment Result: ", JSON.stringify(result));
			if (this.recordId) {
				let viewname = result.body.view_name; //view_name is returned via the api publishSegment
				this.segmentViewName = viewname;
				await updateSegment(
					this.recordId,
					{
						name: this.segmentName,
						description: this.segmentDescription,
						queries: this.getInstructionsToBuild(),
						status: SEGMENT_STATUS.PUBLISHED,
						viewname: viewname
					}
				);
				await setMetricsEnabled(result.body.view_name, this.metricsEnabled);
			} else {
				console.log("No recordId found, saving to DB")
				const dbResult = await saveSegmentToDB(
					this.segmentName,
					'ETH',
					orgId,
					instructionsToBuild,
					undefined,
					SEGMENT_STATUS.PUBLISHED,
					this.segmentDescription,
					result.body.view_name,
					this.metricsEnabled
				);
				this.recordId = dbResult.id;
				let viewname = result.body.view_name;
				this.segmentViewName = viewname;
				console.log("Viewname: ", viewname);

			}
			this.isPublished = true;
			this.toggleTabs(1);
			this.publishLoading = false;
		},
		async previewSegment() {
			amplitude.getInstance().logEvent('SEGMENT_PREVIEW');
			this.previewLoading = true;

			const instructionsToBuild = this.getInstructionsToBuild();
			console.log("Instructions to build: ", JSON.stringify(instructionsToBuild));

			this.previewData = await previewSegment(
				this.segmentName,
				'ETH',
				orgId,
				instructionsToBuild
			);

			if (this.recordId) {
				await updateSegment(
					this.recordId,
					{
						name: this.segmentName,
						description: this.segmentDescription,
						queries: instructionsToBuild,
						status: SEGMENT_STATUS.DRAFT,
						addressCount: Number(this.previewData.totalCount) || 0,
					}
				);
			} else {
				const dbResult = await saveSegmentToDB(
					this.segmentName,
					'ETH',
					orgId,
					instructionsToBuild,
					Number(this.previewData.totalCount) || 0,
					'',
					this.segmentDescription,
					this.metricsEnabled
				);
				this.recordId = dbResult.id;
			}

			this.previewLoading = false;
		},
		async segmentLoader(loadSegmentData) {
			amplitude.getInstance().logEvent('SEGMENT_LOADED');
			if(localStorage.getItem('audiences') == 'false') {
				const orgService = await import('../../services/organization.js');
				await orgService.updateOrgInteraction(localStorage.getItem('userOrgId'), 'audiences', Date.now(), JSON.parse(localStorage.getItem('interactionData')));
				localStorage.setItem('audiences', true);
			}

			this.projects = await SegmentBuilderDataLoader.loadProjects();
			if (loadSegmentData) {
				const data = JSON.parse(loadSegmentData);
				this.isPublished = data.status === SEGMENT_STATUS.PUBLISHED;
				if (this.isPublished) {
					this.toggleTabs(1);
				}

				const includeSmartContractIdx = data.queries.findIndex(i => i.instruction === 'filter_users_smart_contract');
				if (includeSmartContractIdx !== -1) {
					data.queries.splice(includeSmartContractIdx, 1);
				} else {
					this.includeSmartContracts = true;
				}
				this.segmentName = data.name;
				this.recordId = data.id;
				if (data.viewname) {
					this.segmentViewName = data.viewname;
				}

				console.log("Segment Data: ", JSON.stringify(data));
				if(data.metricsEnabled == undefined) {
					this.metricsEnabled = true;
				}
				else {
					this.metricsEnabled = data.metricsEnabled;
				}

				this.selectProject(this.projects.find(p => p.id === data.queries[0].subject.value));
				this.instructions = data.queries.slice(1);
			} else {
				//this.instructions.push({ id: 1 });
				this.selectProject(this.projects[0]);
			}
			this.isLoaded = true;
		},
		async startSegmentIngestion() {
			if (!this.dbSegment.viewname) {
				console.error('No viewname found for segment');
				return;
			}

			const result = await prioritizeIngestionForView(this.dbSegment.viewname);
			if (result.statusCode == 200) {
				this.doneRefreshingData = true;
			} else {
				this.errorRefreshingData = true;
			}
		},
	},
	setup() {
		const sidebarOpen = ref(false)
		const notificationInfoOpen = ref(true)

		return {
			sidebarOpen,
			notificationInfoOpen
		}
	},
	watch: {
		recordId(val) {
			let queryString = this.$route.query;
			this.$route.query = {...queryString, id: val};
			history.replaceState(null, null, '?' + new URLSearchParams(this.$route.query).toString());
		}
	},
	async mounted() {
		orgId = Number(localStorage.getItem('userOrgId')) || 1;

		if (this.$route.query.id) {
			const segment = await getSegmentById(this.$route.query.id);
			if (segment.length) {
				this.dbSegment = segment[0];
				this.segmentViewName = this.dbSegment.viewname;
				this.segmentLoader(JSON.stringify(this.dbSegment));
			}
		} else {
			this.segmentLoader(this.segmentData);
		}
	}
}
</script>

<style>
	[contenteditable]:focus {
		outline: 0px solid transparent;
	}
</style>

