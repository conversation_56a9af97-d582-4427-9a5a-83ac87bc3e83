<style>
.warning-box img {
	filter:  invert(45%) sepia(55%) saturate(5635%) hue-rotate(27deg) brightness(87%) contrast(94%);
}
</style>
<template>
	<div class="flex h-screen overflow-hidden">

		<!-- Sidebar -->
		<Sidebar :sidebarOpen="sidebarOpen" @close-sidebar="sidebarOpen = false" />

		<!-- Content area -->
		<div class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">

			<main>
				<div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">

					<!-- Page header -->
					<div class="sm:flex sm:justify-between sm:items-center mb-8">

						<!-- Left: Title -->
						<div class="mb-4 sm:mb-0">
							<h1 class="text-2xl md:text-3xl text-slate-800 font-bold">Audiences</h1>
							<p class="text-sm mt-1 text-slate-400">Use Audiences to learn about your users, identify your ideal
								customers, and track it all automatically.</p>

							<span class="text-sm mt-1 text-slate-400" v-if="segments.length > 0 ">
								<a class="text-ralpurple-500 hover:text-ralpurple-700" href="/support/starter-segments">
									<svg xmlns="http://www.w3.org/2000/svg" height="16" width="16" viewBox="0 0 48 48"
										class="inline-flex mr-1">
										<g fill="#212121">
											<path
												d="M45,2H11a1,1,0,0,0-1,1V41a3,3,0,0,1-6,0V26H7a1,1,0,0,0,0-2H3a1,1,0,0,0-1,1V41a5.006,5.006,0,0,0,5,5H41a5.006,5.006,0,0,0,5-5V3A1,1,0,0,0,45,2ZM38,36H18a1,1,0,0,1,0-2H38a1,1,0,0,1,0,2Zm0-8H18a1,1,0,0,1,0-2H38a1,1,0,0,1,0,2Zm1-9a1,1,0,0,1-1,1H18a1,1,0,0,1-1-1V11a1,1,0,0,1,1-1H38a1,1,0,0,1,1,1Z"
												fill="#6635E6"></path>
										</g>
									</svg>
									How to get started
								</a>
							</span>
						</div>

						<!-- Right: Actions  -->
						<div class="grid grid-flow-col sm:auto-cols-max justify-start sm:justify-end gap-2">
							<button
								v-if="segments.length > 0"
								id="add-audience-button"
								data-cy="add-audience-button"
								class="btn bg-indigo-500 hover:bg-indigo-600 text-white"
								@click.stop="addSegment()">
								<span class="hidden xs:block">Add Audience</span>
							</button>
						</div>

					</div>

					<div class="bg-white shadow rounded-sm-border border-slate-200 relative mt-5 mb-5 p-5" v-if="isSelfService">
						<h1 class="text-l text-slate-800 font-bold">Free Tier Usage</h1>
						<div class="flex flex-col">
							<div class="w-full bg-gray-300 h-4 rounded m-5" style="height: 1.5em; width: auto">
								<div class="h-full rounded bg-indigo-500" style="color: white; font-weight: bold; text-align: center" :style="{ 'width': selfServiceTierUsagePercent }"> &nbsp;{{ selfServiceTierUsagePercent }}</div>
							</div>
							<p class="text-sm mt-1 text-slate-400">You have used {{ selfServiceTierUsage }} / 2 free custom audiences.
								<a href="#" id="upgrade-link" class="text-ralpurple-500 hover:text-ralpurple-700" @click="requestUpgrade()">Upgrade Now</a> to increase the limit.
							</p>

						</div>
					</div>


					<!-- Segment Table -->
					<div class="bg-white shadow-lg rounded-sm border border-slate-200 relative" v-if="segments.length > 0">
						<header class="px-5 py-4">
							<h2 class="font-semibold text-slate-800">{{segmentCount}} {{segmentTableLabel}}</h2>
						</header>
						<div class="rounded-sm border border-slate-200">
							<div class="overflow-x-auto">
								<table class="table-auto w-full divide-y divide-slate-200" id="segment-table">
									<thead class="text-xs font-semibold uppercase text-slate-500 bg-slate-50">
										<tr>
											<th class="px-2 first:pl-5 last:pr-5 py-3">
												<div class="flex items-center">
													<div class="">Name</div>
												</div>
											</th>
											<th class="px-2 first:pl-5 last:pr-5 py-3">
												<div class="text-center">Status</div>
											</th>
											<th class="px-2 first:pl-5 last:pr-5 py-3">
												<div class="text-center">Address Count</div>
											</th>
											<th class="px-2 first:pl-5 last:pr-5 py-3">
												<div class="text-center">Description</div>
											</th>
											<th class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
												<div class="text-left"></div>
											</th>
										</tr>
									</thead>
									<tbody class="text-sm">
										<SegmentTable v-for="segment in segments" :key="segment.id" :segment="segment"
											@clicked:segment="loadSegment" @delete:segment="confirmDeleteSegment(segment)" />
									</tbody>
								</table>
							</div>
						</div>
						<!-- End -->

						<ModalBlank id="delete-segment-modal" :modal-open="deleteSegmentModelOpen"
							@close-modal="deleteSegmentModelOpen = false">
							<div class="p-5 flex space-x-4">
								<!-- Icon -->
								<div class="w-10 h-10 rounded-full flex items-center justify-center shrink-0">
									<svg class="w-4 h-4 shrink-0 fill-current text-rose-500" viewBox="0 0 16 16">
										<path
											d="M8 0C3.6 0 0 3.6 0 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8zm0 12c-.6 0-1-.4-1-1s.4-1 1-1 1 .4 1 1-.4 1-1 1zm1-3H7V4h2v5z" />
									</svg>
								</div>
								<div>
									<div class="mb-2">
										<div class="text-lg font-semibold text-slate-800">
											Remove {{ segmentToDelete.name }}
										</div>
									</div>
									<!-- Modal content -->
									<div class="text-sm mb-10">
										<div class="space-y-2">
											<p>
												Are you sure you want to remove the Audience "{{segmentToDelete.name}}"?
											</p>
											<p>This cannot be undone and will cause any charts using this audience to no longer show results.
											</p>
										</div>
									</div>
									<div class="warning-box bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 mb-10 rounded relative" v-if="areDependentWidgets">
										<div class="space-y-2">
											<strong class="flex">
												<img class="mr-3" src="../../images/warning-icon.svg" />
												Data from this audience is used in charts currently on your Dashboards
											</strong>
											<p>If you continue, the data in those charts will no longer update.</p>
											<p>You can update those charts to reference a different Audience by selecting "Edit" for each chart.</p>
										</div>
									</div>
									<!-- Modal footer -->
									<div class="flex flex-wrap justify-end space-x-2">
										<button :disabled="isDeleting"
											class="btn-sm border-slate-200 hover:border-slate-300 disabled:border-slate-300 disabled:bg-slate-100 disabled:text-slate-400 disabled:cursor-not-allowed"
											@click.stop="deleteSegmentModelOpen = false">
											Cancel
										</button>
										<button
											data-cy="confirm-delete-segment"
											class="btn bg-rose-500 hover:bg-rose-600 text-white disabled:border-slate-300 disabled:bg-slate-100 disabled:text-slate-400 disabled:cursor-not-allowed"
											:disabled="isDeleting" @click="deleteSegment()">
											<svg v-if="isDeleting" class="animate-spin w-4 h-4 fill-current shrink-0 mr-2"
												viewBox="0 0 16 16">
												<path
													d="M8 16a7.928 7.928 0 01-3.428-.77l.857-1.807A6.006 6.006 0 0014 8c0-3.309-2.691-6-6-6a6.006 6.006 0 00-5.422 8.572l-1.806.859A7.929 7.929 0 010 8c0-4.411 3.589-8 8-8s8 3.589 8 8-3.589 8-8 8z" />
											</svg>
											Remove Audience
										</button>
									</div>
								</div>
							</div>
						</ModalBlank>
					</div>
					<!-- /Segment Table -->

					<!-- Empty State -->
					<div class="max-w-2xl m-auto mt-16" v-else-if="segments.length <= 0 && initialLoad">
						<div class="text-center px-4">
							<div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-t from-ralcloud-300 to-ralcloud-700 mb-4">
								<svg class="up od" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
									<defs>
										<linearGradient x1="50%" y1="0%" x2="50%" y2="100%">
											<stop stop-color="#FFF" offset="0%"></stop>
											<stop stop-color="#A5B4FC" offset="100%"></stop>
										</linearGradient>
									</defs>
									<g fill-rule="nonzero" fill="none">
										<circle fill="url(#icon3-a)" cx="30.5"/>
										<circle fill="#4F46E5" opacity=".88" cx="17.5" cy="17.5" r="4.5"/>
										<circle fill="#4F46E5" opacity=".88" cx="30.5" cy="30.5" r="4.5"/>
										<circle fill="url(#icon3-a)" cx="17.5" cy="30.5" r="4.5"/>
									</g>
								</svg>
							</div>
							<h2 class="text-2xl text-slate-800 font-bold mb-2">
								Add an audience to get started
							</h2>
							<div class="mb-6">
								Need some help?
								<a href="/support/starter-segments" class="text-ralpurple-700 font-bold">Learn the basics</a> of Audiences.
							</div>
							<button
								class="btn-xs bg-ralpurple-500 hover:bg-ralpurple-600 text-white"
								aria-controls="add-report-modal"
								@click.stop="addSegment()"
							>
								<span class="hidden xs:block">Add Audience</span>
							</button>
							<button
								class="btn-xs border-slate-200 hover:border-ralgranite-300 text-ralgranite-300 hover:text-ralgranite-400 bg-white ml-4"
								@click.stop="useTemplateModalOpen = true"
							>Use A Template</button>
						</div>
					</div>
					<!-- End -->

					<ModalBasic
						id="use-template-modal"
						v-if="useTemplateModalOpen"
						:modalOpen="useTemplateModalOpen"
						@close-modal="useTemplateModalOpen = false"
						title="Use an Audience Template">
						<!-- Modal content -->
						<div class="px-5 py-4">
						<div class="text-sm mb-5">
								<div class="space-y-4">
								<div class="mb-5">
									<label class="block text-sm font-medium mb-1" for="role">
										Which project would you like to use?
									</label>
									<DropdownClassic
										:options="projects"
										:select="projects[0]"
										:width="'w-96'"
                  				@projectSelected="(projectId) => selectedProject = {id: projectId}" />
								</div>
								<div class="mb-5">
									<label class="block text-sm font-medium mb-1" for="role">
										What template would you like to use?
									</label>
									<DropdownClassic
										:options="templates"
										:select="templates[0]"
										:width="'w-96'"
                  				@projectSelected="(template) => selectedTemplate = {id: template}" />
								</div>
									<p class="block text-sm mt-1 ml-2" v-if="selectedTemplate != null">
										The default template will install the recommended audiences. You can always add or remove an audience later.
									</p>
								</div>
						</div>
						</div>
						<!-- Modal footer -->
						<div class="px-5 py-4 border-t border-slate-200">
							<div class="flex flex-wrap justify-end space-x-2">
								<button
									class="btn-sm border-slate-200 hover:border-slate-300 text-slate-600 disabled:border-slate-300 disabled:bg-slate-100 disabled:text-slate-400 disabled:cursor-not-allowed"
									:disabled="isCreatingDefault"
									@click.stop="useTemplateModalOpen = false">
									Cancel
								</button>
								<button
									class="btn-sm bg-indigo-500 hover:bg-indigo-600 text-white disabled:border-slate-300 disabled:bg-slate-100 disabled:text-slate-400 disabled:cursor-not-allowed"
									:disabled="isCreatingDefault"
									@click="createDefaultSegments()">
									<svg v-if="isCreatingDefault" class="animate-spin w-4 h-4 fill-current shrink-0 mr-2"
										viewBox="0 0 16 16">
										<path d="M8 16a7.928 7.928 0 01-3.428-.77l.857-1.807A6.006 6.006 0 0014 8c0-3.309-2.691-6-6-6a6.006 6.006 0 00-5.422 8.572l-1.806.859A7.929 7.929 0 010 8c0-4.411 3.589-8 8-8s8 3.589 8 8-3.589 8-8 8z" />
									</svg>
									Add Audience from Template
								</button>
							</div>
						</div>
					</ModalBasic>
					<!-- End Use Template -->

				</div>
			</main>

		</div>

	</div>
</template>

<script>
import { ref } from 'vue'
import ModalBlank from '../../components/ModalBlank.vue'
import ModalBasic from '../../components/ModalBasic.vue'
import DropdownFull from '../../components/DropdownFull.vue'
import DropdownClassic from '../../components/DropdownClassic.vue'
import Notification from '../../components/Notification.vue'
import PaginationNumeric from '../../components/PaginationNumeric.vue'
import SearchForm from '../../components/SearchForm.vue'
import Header from '../../partials/Header.vue'
import Sidebar from "../../partials/Sidebar.vue"
import { deleteSegment, getSegments, saveSegmentToDB, publishSegment, SEGMENT_STATUS } from '../../services/segmentbuilder'
import SegmentTable from './SegmentTable.vue'
import { loadProjects } from '../../utils/Utils.js'
import { DefaultSegmentBuilder } from '../../services/default-segment-builder'

import * as Utils from '../../utils/Utils';
const URL_DOMAIN = Utils.URL_DOMAIN;

let orgId;

export default {
	name: 'SegmentOverview',
	components: {
		Sidebar,
		Header,
		SearchForm,
		PaginationNumeric,
		SegmentTable,
		Notification,
		ModalBlank,
		ModalBasic,
		DropdownFull,
		DropdownClassic,
	},
	data() {
		return {
			segments: [],
			deleteSegmentModelOpen: false,
			segmentToDelete: null,
			isDeleting: false,
			isCreatingDefault: false,
			initialLoad: false,
			useTemplateModalOpen: false,
			projects: [],
			selectedProjectIndex: -1,
			selectedProject: null,
			templates: [{id: 'default', name: 'Default'}],
			selectedTemplate: null,
			areDependentWidgets: false,
		}
	},
	setup() {
		const sidebarOpen = ref(false)
		const notificationInfoOpen = ref(true)

		return {
			sidebarOpen,
			notificationInfoOpen
		}
	},
	methods: {
		addSegment() {
			amplitude.getInstance().logEvent('SEGMENT_ADD');
			this.$router.push('/build-segment');
		},
		loadSegment(segmentId) {
			let segmentData;
			for (let i = 0; i < this.segments.length; i++) {
				if (this.segments[i].id === segmentId) {
					segmentData = this.segments[i];
				}
			}
			this.$router.push({ name: 'SegmentBuilder', query: { id: segmentId } });
		},

		async requestUpgrade() {
			const response = await fetch(`${URL_DOMAIN}/onboard/self-service/upgrade`, {
				method: 'POST',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				}
			});

			if (response.ok && response.status >= 200 && response.status < 300) {
				localStorage.setItem('selfServiceUpgradeRequested', true);
			}
		},
		async confirmDeleteSegment(segment) {
			this.segmentToDelete = segment;

			try {
				this.areDependentWidgets = await this.checkDependentWidgets(segment.viewname);
			} catch (e) {
				console.error(e);
			}

			this.deleteSegmentModelOpen = true;
		},
		async checkDependentWidgets(segmentViewName) {
			const response = await fetch(`${Utils.URL_DOMAIN}/widgets?filter[where][projectId]=${segmentViewName}`, {
			method: 'GET',
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
			}
			});
			const jsonresponse = await response.json();

			if (Array.isArray(jsonresponse) && jsonresponse.length) {
				return true;
			}

			return false;
		},
		async deleteSegment() {
			amplitude.getInstance().logEvent('SEGMENT_DELETE');
			this.isDeleting = true;
			await deleteSegment(this.segmentToDelete);
			this.segments.splice(this.segments.findIndex(segment => segment.id === this.segmentToDelete.id), 1);
			this.deleteSegmentModelOpen = false;
			this.isDeleting = false;
		},
		async createDefaultSegments() {
			this.isCreatingDefault = true;
			const defaultSegments = new DefaultSegmentBuilder(
				this.selectedProject.id
			).buildDefaultSegments();

			for (let i = 0; i < defaultSegments.length; i++) {
				let currSegment = defaultSegments[i];
				const result = await publishSegment(
					currSegment.name,
					'ETH',
					orgId,
					currSegment.instructions
				);
				const dbResult = await saveSegmentToDB(
					currSegment.name,
					'ETH',
					orgId,
					currSegment.instructions,
					undefined,
					SEGMENT_STATUS.PUBLISHED,
					currSegment.description,
					result.body.view_name
				);
			}
			this.segments = await getSegments();
			localStorage.setItem('selfServiceAudienceCount', this.selfServiceTierUsage);
			this.isCreatingDefault = false;
			this.useTemplateModalOpen = false;
		},
	},
	computed: {
		segmentCount() {
			return this.segments.length;
		},
		segmentTableLabel() {
			if (this.segments.length == 1)
				return "Audience";
			else
				return "Audiences";
		},
		selfServiceTierUsage() {
			return this.segments.filter(x => !x.viewname.endsWith('_5') && x.status == 'PUBLISHED').length;
		},
		selfServiceTierUsagePercent() {
			return `${this.selfServiceTierUsage / 2.0 * 100}%`;
		},
		isSelfService() {
			return localStorage.getItem('selfService') == 'true';
		}
	},
	async mounted() {
		orgId = Number(localStorage.getItem('userOrgId')) || 1;

		loadProjects().then((projects) => {
			this.projects = projects;
			if (this.projects.length) {
				this.selectedProjectIndex = 0;
				this.selectedProject = this.projects[0].id;
			}
		});
		this.segments = await getSegments();
		localStorage.setItem('selfServiceAudienceCount', this.selfServiceTierUsage);
		this.initialLoad = true;
	}
}
</script>
