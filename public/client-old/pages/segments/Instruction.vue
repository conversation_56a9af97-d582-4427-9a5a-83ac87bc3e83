<template>
	<div class="flex mt-4">
		<div v-if="!isLoaded" class="h-48 w-full ml-4 rounded-lg mb-2 p-6 bg-gray-200 animate-pulse">
		</div>
		<div v-if="isLoaded" class="border-t border-l-2 border-b border-r border-slate-300 ml-4 rounded-lg mb-2 p-6"
			style="min-width: 38rem; max-width: 44rem;">
			<div>
				<span class="text-base mr-2">whose</span>

				<div class="relative inline-block text-left">

				 <!-- Event Type -->
				 <DropdownSegmentMenu
					data-cy="segment-menu-event-type"
				 	class="relative inline-flex"
					:title="selectedEventType.name || 'Select Event Type'"
					:disabled="isDisabled || isNowPublished"
				>
					<li
						v-for="eventType in eventTypes"
						v-if="isLoaded"
						:data-cy="`segment-menu-event-type-${eventType.value}`"
						class="flex items-center w-full hover:bg-slate-50 py-1 px-3 cursor-pointer"
						:key="eventType.value"
						:class="eventType.value === selectedEventType.value && 'text-indigo-500'"
						:disabled="isNowPublished"
						@click="setSelectedEventType(eventType)">
						<a class="font-medium text-sm text-slate-600 hover:text-slate-800 flex py-1 px-3">{{eventType.name}}</a>
					</li>
				 </DropdownSegmentMenu>

				 <DropdownSegmentMenu
				 	v-if="hasEventValue"
					id="segment-menu-event-value"
				 	class="relative inline-flex ml-2" :title="selectedCustomEvent.name || 'Select an Event'">
					<li
						v-for="customEvent in customEvents"
						class="flex items-center w-full hover:bg-slate-50 py-1 px-3 cursor-pointer"
						:key="customEvent.value"
						:class="customEvent.value === selectedCustomEvent.value && 'text-indigo-500'"
						@click="setSelectedCustomEvent(customEvent)">
						<a class="font-medium text-sm text-slate-600 hover:text-slate-800 flex py-1 px-3">{{customEvent.name}}</a>
					</li>
				</DropdownSegmentMenu>

				<DropdownSegmentMenu
				 	v-if="hasUTMCampaignValue"
					id="segment-menu-utm-campaign"
				 	class="relative inline-flex ml-2" :title="selectedUTMCampaign.name || 'Select a Campaign'">
					<li
						v-for="utmCampaign in utmCampaigns"
						class="flex items-center w-full hover:bg-slate-50 py-1 px-3 cursor-pointer"
						:key="utmCampaign.value"
						:class="utmCampaign.value === selectedUTMCampaign.value && 'text-indigo-500'"
						@click="setSelectedUTMCampaign(utmCampaign)">
						<a class="font-medium text-sm text-slate-600 hover:text-slate-800 flex py-1 px-3">{{utmCampaign.name}}</a>
					</li>
				</DropdownSegmentMenu>

				<DropdownSegmentMenu
				 	v-if="hasWalletTagValue"
					 id="segment-menu-wallet-tag"
				 	class="relative inline-flex ml-2" :title="selectedWalletTag.name || 'Select a Trait'">
					<li
						v-for="walletTag in walletTags"
						class="flex items-center w-full hover:bg-slate-50 py-1 px-3 cursor-pointer"
						:key="walletTag.value"
						:class="walletTag.value === walletTag.value && 'text-indigo-500'"
						@click="setSelectedWalletTag(walletTag)">
						<a class="font-medium text-sm text-slate-600 hover:text-slate-800 flex py-1 px-3">{{walletTag.name}}</a>
					</li>
				</DropdownSegmentMenu>

				<span
					v-if="!requiresTime && supportsTime && !showSubjectTypeInput && !this.time"
					class="text-xs font-semibold text-ralpurple-500 ml-4 cursor-pointer"
					@click.stop="timeSelection(true)">
						+ TIME
				</span>

				 <!-- Required Params - Operator & Amount -->
				 <span v-if="requiresParameters">
					<span class="text-base ml-3">is</span>
					<DropdownSegmentMenu
						data-cy="segment-menu-operator"
						class="relative inline-flex ml-2"
						:title="selectedOperator.name || 'Select an Operator'"
						:disabled="isNowPublished">
						<li
							v-for="operator in operators"
							class="flex items-center w-full hover:bg-slate-50 py-1 px-3 cursor-pointer"
							:key="operator.value"
							:class="operator.value === selectedOperator.value && 'text-indigo-500'"
							@click="setSelectedOperator(operator)">
						 	<a
							 	:data-cy="`segment-menu-operator-${operator.value}`"
								class="font-medium text-sm text-slate-600 hover:text-slate-800 flex py-1 px-3">
								{{operator.name}}
							</a>
						</li>
					</DropdownSegmentMenu>
					<input
						data-cy="segment-menu-amount"
						class="form-input w-24 ml-3 disabled:bg-gray-200"
						type="number"
						placeholder="amount"
						:disabled="isNowPublished"
						:value="amount"
						@input="updateAmount" />
				 </span>
				</div>

				<div class="mt-5">
					<span v-if="showSubjectTypeInput" class="text-base">with</span>

					<!-- Subject Type -->
					<DropdownSegmentMenu
						v-if="showSubjectTypeInput"
						data-cy="segment-menu-subject-type"
						class="relative inline-flex ml-2"
						:title="selectedSubjectType.name || 'Target'"
						:disabled="JSON.stringify(selectedEventType) == '{}' || isNowPublished">
						<li v-for="subjectType in subjectTypes" :key="subjectType.value"
							class="flex items-center w-full hover:bg-slate-50 py-1 px-3 cursor-pointer"
							:class="subjectType === selectedSubjectType && 'text-indigo-500'"
							@click="setSelectedSubjectType(subjectType)">
							<a
								:data-cy="`segment-menu-subject-type-${subjectType.value}`"
								class="font-medium text-sm text-slate-600 hover:text-slate-800 flex py-1 px-3">
								{{subjectType.name}}
							</a>
						</li>
					</DropdownSegmentMenu>

					<!-- Address -->
					<input
						v-if="showAddressInput"
						id="segment-menu-address"
						class="form-input ml-3 w-52 disabled:bg-gray-200"
						:class="{'bg-gray-200': JSON.stringify(selectedSubjectType) == '{}'}" type="text"
						placeholder="0x00000000000000000000"
						:disabled="JSON.stringify(selectedSubjectType) == '{}' || isNowPublished" v-model="address"
						v-debounce:200="(val) => setSelectedSubjectValue({value: val})" />

					<!-- Subject Value -->
					<DropdownSegmentMenu
						v-if="!showAddressInput && showSubjectTypeInput"
						data-cy="segment-menu-subject-value"
						class="relative inline-flex ml-2"
						:title="selectedSubjectValue.name || 'Value'"
						:disabled="JSON.stringify(selectedSubjectType) == '{}' || isNowPublished">
						<li v-for="subjectValue in subjectValues"
							class="flex items-center w-full hover:bg-slate-50 py-1 px-3 cursor-pointer"
							:class="subjectValue === selectedSubjectValue && 'text-indigo-500'"
							@click="setSelectedSubjectValue(subjectValue)">
							<a
								data-cy="segment-menu-subject-value-item"
								class="font-medium text-sm text-slate-600 hover:text-slate-800 flex py-1 px-3">
								{{subjectValue.name}}
							</a>
						</li>
					</DropdownSegmentMenu>
					<span
						v-if="!requiresTime && supportsTime && showSubjectTypeInput"
						class="text-xs font-semibold text-ralpurple-500 ml-4 cursor-pointer"
						@click.stop="timeSelection(true)">
							+ TIME
					</span>
				</div>
			</div>

			<!-- Time -->
			<div class="mt-5" v-if="time || requiresTime">
				<DropdownSegmentMenu
					id="segment-menu-time"
					class="relative inline-flex"
					:title="selectedTimeOption || 'Timeframe'"
					:disabled="(JSON.stringify(selectedSubjectValue) == '{}' && subjectTypes > 0) || isNowPublished">
					<li v-for="option in timeOptions" class="flex items-center w-full hover:bg-slate-50 py-1 px-3 cursor-pointer"
						:class="option === selectedTimeOption && 'text-indigo-500'" @click="setSelectedTimeOption(option)">
						<a class="font-medium text-sm text-slate-600 hover:text-slate-800 flex py-1 px-3">{{option}}</a>
					</li>
				</DropdownSegmentMenu>

				<span
					v-if="selectedTimeOption === timeOptions.WITHIN"
					class="text-base mt-1 ml-2">
					the last
				</span>
				<input
					v-if="selectedTimeOption"
					id="days"
					class="form-input w-20 ml-2 disabled:bg-gray-200"
					:class="{ 'bg-gray-200': (JSON.stringify(selectedSubjectValue) == '{}' && subjectTypes.length > 0)}" type="number" placeholder="days"
					:value="daysInput" :disabled="(JSON.stringify(selectedSubjectValue) == '{}' && subjectTypes.length > 0) || isNowPublished"
					@input="updateSelectedTimeValue" />
				<span class="text-base mt-1 ml-2">
					{{timeOptionDisplay}}
				</span>
				<input
					v-if="selectedTimeOption === timeOptions.BETWEEN"
					id="daysBetween"
					class="form-input w-20 ml-2 disabled:bg-gray-200"
					:class="{ 'bg-gray-200': (JSON.stringify(selectedSubjectValue) == '{}' && subjectTypes.length > 0)}" type="number" placeholder="days"
					:value="daysBetweenInput" :disabled="(JSON.stringify(selectedSubjectValue) == '{}' && subjectTypes.length > 0) || isNowPublished"
					@input="updateSelectedTimeValue" />

				<svg v-if="time || !requiresTime" class="relative inline-flex w-4 h-4 ml-2 fill-slate-300 hover:fill-slate-400 cursor-pointer" @click.stop="removeTimeSelection()">
					<path d="M7.95 6.536l4.242-4.243a1 1 0 111.415 1.414L9.364 7.95l4.243 4.242a1 1 0 11-1.415 1.415L7.95 9.364l-4.243 4.243a1 1 0 01-1.414-1.415L6.536 7.95 2.293 3.707a1 1 0 011.414-1.414L7.95 6.536z" />
				</svg>
			</div>
		</div>
		<button v-if="!isNowPublished"
			class="btn my-auto ml-4 w-8 h-8 border-slate-200 hover:border-slate-300" @click.stop="localRemoveInstructions()">
			<svg class="w-4 h-4 fill-slate-300 hover:fill-rose-500 shrink-0" viewBox="0 0 16 16">
				<path
					d="M5 7h2v6H5V7zm4 0h2v6H9V7zm3-6v2h4v2h-1v10c0 .6-.4 1-1 1H2c-.6 0-1-.4-1-1V5H0V3h4V1c0-.6.4-1 1-1h6c.6 0 1 .4 1 1zM6 2v1h4V2H6zm7 3H3v9h10V5z" />
			</svg>
		</button>
	</div>
	<span class="text-xs font-semibold ml-5" v-if="position != 'last'">AND</span>
</template>

<script>
import { vue3Debounce } from 'vue-debounce';
import { SegmentFactory } from '../../../../src/segment-builder/segment-factory';
import { SubjectType } from '../../../../src/segment-builder/segment.abstract';
import Datepicker from '../../components/Datepicker.vue';
import DropdownSegmentMenu from '../../components/DropdownSegmentMenu.vue';
import { SegmentBuilderDataLoader } from '../../services/segment-builder-data-loader';
import { DEFAULT_SEGMENT_END_DAYS, isEmpty } from '../../utils/Utils';

const TIME_OPTIONS = {
	BEFORE: 'Before',
	WITHIN: 'Within',
	BETWEEN: 'Between',
}

let orgId;

export default {
	name: 'InstructionItem',
	components: {
		DropdownSegmentMenu,
		Datepicker,
	},
	directives: {
		debounce: vue3Debounce()
	},
	props: ['id', 'position', 'projects', 'isDisabled', 'isNowPublished', 'instructionData'],
	data() {
		return {
			isLoaded: false,
			appInfo: [],
			eventTypes: [],
			customEvents: [],
			utmCampaigns: [],
			walletTags: [],
			hasEventValue: false,
			hasUTMCampaignValue: false,
			hasWalletTagValue: false,
			selectedCustomEvent: {},
			selectedUTMCampaign: {},
			selectedWalletTag: {},
			instruction: {},
			selectedEventType: {},
			requiresParameters: false,
			time: false,
			requiresTime: false,
			supportsTime: true,
			selectedOperator: {},
			amount: null,
			subjectTypes: [],
			selectedSubjectType: {},
			address: '',
			subjectValues: {},
			selectedSubjectValue: {},
			selectedTimeOption: null,
			selectedTimeValue: null,
			daysInput: null,
			daysBetweenInput: null,
			timeOptions: TIME_OPTIONS,
			operators: [
				{
					name: 'greater than',
					value: '>'
				},
				{
					name: 'less than',
					value: '<'
				},
				{
					name: 'equal to',
					value: '='
				},
				{
					name: 'contains',
					value: 'contains'
				},
				{
					name: 'does not contain',
					value: 'does not contain'
				},
			],
		}
	},
	computed: {
		timeOptionDisplay() {
			switch (this.selectedTimeOption) {
				case TIME_OPTIONS.BEFORE:
					return 'days ago';
				case TIME_OPTIONS.WITHIN:
					return 'days';
				case TIME_OPTIONS.BETWEEN:
					return 'and';
				default:
					return '';
			}
		},
		showAddressInput() {
			const supportedAddressTypes = [SubjectType.ADDRESS.value, SubjectType.TOKEN.value, SubjectType.NFT.value];
			return supportedAddressTypes.includes(this.selectedSubjectType.value);
		},
		showSubjectTypeInput() {
			return isEmpty(this.selectedEventType) || this.subjectTypes.length > 0
		}
	},
	methods: {
		localRemoveInstructions() {
			this.$emit('removeInstruction', this.id);
		},
		timeSelection(toggle) {
			toggle == null ? this.time = false : this.time = toggle;

			if (toggle) {
				this.defaultTime();
			}
		},
		removeTimeSelection() {
			this.timeSelection(false);
			this.selectedTimeOption = null;
			this.selectedTimeValue = null;
			this.daysInput = null;
			this.daysBetweenInput = null;
			this.instruction = this.instruction.setTime({});
		},
		setSelectedEventType(eventType) {
			this.resetInstruction();
			this.selectedEventType = eventType;
			let segment = SegmentFactory.createSegment(eventType.value, this.id, orgId);
			this.instruction = segment;
			this.subjectTypes = segment.getPossibleSubjects();
			this.requiresParameters = segment.requiresParameters();
			this.requiresTime = segment.requiresTime();
			this.supportsTime = segment.supportsTime();
			this.hasEventValue = segment.hasEventValue();
			this.hasUTMCampaignValue = segment.hasUTMCampaignValue();
			this.hasWalletTagValue = segment.hasCustomTraitValue();
			if (this.requiresTime) {
				this.defaultTime();
			}
		},
		setSelectedSubjectType(subjectType) {
			this.selectedSubjectType = subjectType;
			if (subjectType.value == SubjectType.PROJECT.value) {
				this.subjectValues = this.projects.map(project => ({
					name: project.name,
					value: project.id
				}));
			} else if (subjectType.value == SubjectType.CATEGORY.value) {
				this.subjectValues = SegmentBuilderDataLoader.getCategories(this.appInfo).sort((a, b) => a.name.localeCompare(b.name));
			} else if (subjectType.value == SubjectType.DAPP.value) {
				this.subjectValues = SegmentBuilderDataLoader.getDapps(this.appInfo);
			} else {
				this.subjectValues = [];
			}
		},
		setSelectedSubjectValue(subjectValue) {
			if (!isEmpty(subjectValue)) {
				this.selectedSubjectValue = subjectValue;
				this.instruction = this.instruction.setSubject({
					type: this.selectedSubjectType.value,
					value: subjectValue.value
				});
			}
		},
		setSelectedOperator(operator) {
			this.selectedOperator = operator;
			if (this.amount != null || this.amount !== undefined) {
				this.updateAmount({ target: { value: this.amount } });
			}
		},
		setSelectedCustomEvent(customEvent) {
			this.selectedCustomEvent = customEvent;
			this.selectedSubjectType = { name: 'Custom Event', value: SubjectType.EVENT.value };
			this.setSelectedSubjectValue({
				value: customEvent.value,
			});
		},
		setSelectedUTMCampaign(utmCampaign) {
			this.selectedUTMCampaign = utmCampaign;
			this.selectedSubjectType = { name: 'Attribution Campaign', value: SubjectType.UTM_CAMPAIGN.value };
			this.setSelectedSubjectValue({
				value: utmCampaign.value,
			});
		},
		setSelectedWalletTag(walletTag) {
			this.selectedWalletTag = walletTag;
			this.selectedSubjectType = { name: 'Wallet Tag', value: SubjectType.WALLET_TAG.value };
			this.setSelectedSubjectValue({
				value: walletTag.value,
			});
		},
		defaultTime() {
			this.daysInput = 30;
			this.selectedTimeOption = TIME_OPTIONS.WITHIN;
			this.selectedTimeValue = {
				start: 0,
				end: Number(this.daysInput)
			};
			this.updateSelectedTimeValue({target: {id: 'days', value: this.daysInput}});
		},
		setSelectedTimeOption(timeOption) {
			this.selectedTimeOption = timeOption;
			this.updateSelectedTimeValue({ target: { id: 'days', value: this.daysInput } });
		},
		updateSelectedTimeValue(event) {
			if (event.target.id === 'days') {
				this.daysInput = event.target.value;
			} else if (event.target.id === 'daysBetween') {
				this.daysBetweenInput = event.target.value;
			}

			if (!this.selectedTimeOption) {
				return;
			}

			if (this.selectedTimeOption === TIME_OPTIONS.BEFORE && this.daysInput) {
				this.selectedTimeValue = {
					start: Number(this.daysInput),
					end: DEFAULT_SEGMENT_END_DAYS
				};
			} else if (this.selectedTimeOption === TIME_OPTIONS.WITHIN && this.daysInput) {
				if (!this.daysInput) return;
				this.selectedTimeValue = {
					start: 0,
					end: Number(this.daysInput)
				};
			} else if (this.selectedTimeOption === TIME_OPTIONS.BETWEEN && this.daysInput && this.daysBetweenInput) {
				this.selectedTimeValue = {
					start: Number(this.daysInput),
					end: Number(this.daysBetweenInput)
				};
			} else {
				return;
			}
			this.instruction = this.instruction.setTime(this.selectedTimeValue);
		},
		updateAmount(event) {
			if (this.selectedOperator.value && event.target.value) {
				this.amount = event.target.value;
				//This clears out an already selected subjecttype if changing the value --ADAM (hope I don't break stuff)
				/*if (this.selectedSubjectType.value) {
					this.instruction.setSubject({
						type: this.selectedSubjectType.value,
						value: ''
					});
				}*/
				this.instruction = this.instruction.addRequiredParam({
					operator: this.selectedOperator.value,
					value: this.amount
				});
			}
		},
		resetInstruction() {
			this.selectedOperator = this.selectedSubjectType = this.selectedSubjectValue = {};
			this.amount = this.selectedTimeValue = this.daysInput = this.daysBetweenInput = null;
			this.selectedTimeOption = this.address = '';
		},
		hydrateInstruction() {
			if (this.instructionData.instruction) {
				this.setSelectedEventType(SegmentFactory.getSegmentTypeFromInstruction(this.instructionData.instruction, this.id, orgId));
			}
			if (!isEmpty(this.instructionData.required)) {
				this.setSelectedOperator(this.operators.find(operator => operator.value === this.instructionData.required.operator));
				this.updateAmount({ target: { value: this.instructionData.required.value } });
				this.amount = this.instructionData.required.value;
			}
			if (!isEmpty(this.instructionData.subject)) {
				const subjectType = this.subjectTypes.find(subjectType => subjectType.value === this.instructionData.subject.type);
				if (subjectType) {
					console.log("Set Subject Type: ", subjectType)
					this.setSelectedSubjectType(subjectType);
				}
				if (this.instructionData.subject.type === SubjectType.TOKEN.value
				|| this.instructionData.subject.type === SubjectType.NFT.value
				|| this.instructionData.subject.type === SubjectType.ADDRESS.value) {
					this.address = this.instructionData.subject.value;
					this.setSelectedSubjectValue({ value: this.instructionData.subject.value });
				} else if (this.instructionData.subject.type === SubjectType.EVENT.value) {
					if (this.customEvents.length) {
						this.setSelectedCustomEvent({
							name: this.customEvents.find(customEvent => customEvent.value === this.instructionData.subject.value).name,
							value: this.instructionData.subject.value
						});
					} else {
						this.setSelectedCustomEvent({
							name: this.instructionData.subject.value,
							value: this.instructionData.subject.value
						});
					}

				} else if(this.instructionData.subject.type === SubjectType.WALLET_TAG.value) {
					this.setSelectedWalletTag({
						name: this.instructionData.subject.value,
						value: this.instructionData.subject.value
					})
				}
				else {
					this.setSelectedSubjectValue(this.subjectValues.find(subjectValue => subjectValue.value === this.instructionData.subject.value));
				}
			}
			if (!isEmpty(this.instructionData.time)) {
				this.time = true;
				if (this.instructionData.time.start === 0) {
					this.selectedTimeOption = TIME_OPTIONS.WITHIN;
					this.daysInput = this.instructionData.time.end;
				} else if (this.instructionData.time.end === DEFAULT_SEGMENT_END_DAYS) {
					this.selectedTimeOption = TIME_OPTIONS.BEFORE;
					this.daysInput = this.instructionData.time.start;
				} else {
					this.selectedTimeOption = TIME_OPTIONS.BETWEEN;
					this.daysInput = this.instructionData.time.start;
					this.daysBetweenInput = this.instructionData.time.end;
				}
				this.updateSelectedTimeValue({ target: { id: 'days', value: this.daysInput } });
				if (this.daysBetweenInput) {
					this.updateSelectedTimeValue({ target: { id: 'daysBetween', value: this.daysBetweenInput } });
				}
			}
		},
	},
	watch: {
		subjectTypes(subjectTypes) {
			if (subjectTypes.length === 1) {
				this.setSelectedSubjectType(subjectTypes[0]);
			}
		},
		instruction: {
			handler() {
				const forceUpdate = !isEmpty(this.selectedSubjectValue) || this.address || isEmpty(this.instruction.getPossibleSubjects());
				if (this.selectedSubjectType.value || forceUpdate) {
					this.$emit('update:instruction', this.instruction.buildSegment());
				}
			},
			deep: true
		},
	},
	async mounted() {
		orgId = Number(localStorage.getItem('userOrgId')) || 1;

		const startTime = Date.now();
		this.eventTypes = SegmentBuilderDataLoader.getEventTypes().sort((a, b) => a.name.localeCompare(b.name));
		console.log(`Event Types load time: ${Date.now() - startTime}ms`);
		this.appInfo = await SegmentBuilderDataLoader.loadAppInfo();
		//I wonder if we can mvoe these to the instruction themselves for example could getCustomEvents be a static method on the event instruction?
		this.customEvents = await SegmentBuilderDataLoader.getCustomEvents();
		this.utmCampaigns = await SegmentBuilderDataLoader.getUTMCampaigns();
		this.walletTags = await SegmentBuilderDataLoader.getWalletTags();
		if (!isEmpty(this.instructionData)) {
			this.hydrateInstruction();
		}
		this.isLoaded = true;


	},
}
</script>
