<template>

	<div class="bg-white shadow-lg rounded-sm border border-slate-200 relative">

		<header class="grid grid-flow-col grid-cols-2 sm:auto-cols-max px-5 py-4">
			<h2 class="col-span-1 justify-start text-md font-semibold text-slate-800">
				{{walletCountDisplay}}
			</h2>

			<button
				class="place-self-end bg-gradient-button-primary hover:bg-ralprimary-dark text-white font-bold text-xs py-2 px-4 mr-4 rounded-button transition duration-500 ease-in-out"
				style="width: 12rem;"
				data-cy="create-new-campaign"
				@click.prevent="this.$router.push('/campaign-builder')">
				+ Create New Campaign
			</button>

			<button @click="showWalletOptionsModal = true"
				class="btn-xs border-slate-200 hover:border-slate-300 place-self-end py-2 px-4 mr-4">
				<img src="../../images/edit-pencil.svg" width="16" height="16" class="download-link-color" />
			</button>
			<button @click="downloadWalletList()"
				class="btn-xs border-slate-200 hover:border-slate-300 place-self-end py-2 px-4">
				<img v-if="!downloadPreparing" src="../../images/download-icon.svg" width="16" height="16" class="download-link-color" />
				<div v-if="downloadPreparing" role="status" style="display: flex; align-items: center;">
					<svg style="height:16px; width: 16px;" aria-hidden="true" class="mr-2 w-8 h-8 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/>
						<path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/>
					</svg>
					<span v-text="downloadStatus"></span>
				</div>
			</button>
		</header>

		<div class="rounded-sm border border-slate-200">
			<div class="overflow-x-auto">
				<div class="bg-white p-2 sm:p-4 sm:h-64 rounded-2xl shadow-lg flex flex-col sm:flex-row gap-5 select-none"
					v-if="!isLoaded">
					<div class="w-full justify-center content-center mt-12">
						<div class="text-xs text-slate-400 flex justify-center content-center">Loading wallets...</div>
						<div class="area">
							<div class="loading">
								<span></span>
							</div>
						</div>
					</div>
				</div>
				<div class="py-5 text-center text-sm text-slate-800" v-if="isLoaded && !dataAvailable">
					No Data in Segment
				</div>

				<table class="table-auto w-full divide-y divide-slate-200" v-if="isLoaded && dataAvailable">
					<thead class="
							text-xs
							font-semibold
							uppercase
							text-slate-500
							bg-slate-50
							border-t border-b border-slate-200
						">
						<tr>
							<th class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
								<div class="font-semibold text-left">Wallet</div>
							</th>
							<th class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap" v-if="walletDataOptions.includeNetWorth">
								<div class="font-semibold text-left">Net Worth</div>
							</th>
							<th class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap" v-if="walletDataOptions.includeNftCount">
								<div class="font-semibold text-left">Number of NFTs</div>
							</th>
							<th class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap" v-if="walletDataOptions.includeEthCount">
								<div class="font-semibold text-left">ETH Balance</div>
							</th>
							<th class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap" v-if="walletDataOptions.includeDataFromEventType && showEventDropdown">
								<div class="font-semibold text-left">Event Data</div>
							</th>
						</tr>
					</thead>
					<tbody class="text-sm">
						<tr v-for="wallet in fullWalletData" :key="wallet">
							<td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
								<div class="font-medium text-slate-800">
									<a class="text-ralpurple-500 cursor-pointer" @click.stop="navigateToWallet(wallet)">
										{{ wallet.address }}
										<img src="../../images/external-link-out.svg" width="16" height="16" class="inline-block ml-1" style="margin-bottom: 2px;" />
									</a>
								</div>
							</td>
							<td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap" v-if="walletDataOptions.includeNetWorth">
								<div class="font-medium text-slate-800">
									{{wallet.net_worth !== '?' ? '$' : ''}}{{ formatWalletData(wallet.net_worth) }}
								</div>
							</td>
							<td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap" v-if="walletDataOptions.includeNftCount">
								<div class="font-medium text-slate-800">
									{{ formatWalletData(wallet.total_nfts) }}
								</div>
							</td>
							<td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap" v-if="walletDataOptions.includeEthCount">
								<div class="font-medium text-slate-800">
									{{ formatWalletData(wallet.eth_balance) }}
								</div>
							</td>
							<td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap" v-if="walletDataOptions.includeDataFromEventType && showEventDropdown">
								<div class="font-medium text-slate-800">
									{{ wallet?.data_val }}
								</div>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
		<!-- End -->
	</div>

	<div class="px-6 py-4" v-if="dataAvailable">
		<PaginationNumeric2 :pageCount="Math.ceil(totalWalletCount / 10)" @page="fetchPage" />
	</div>

	<ModalBlank
		id="wallet-options-modal"
		:modal-open="showWalletOptionsModal"
	>
		<div class="p-5 space-x-4">
			<div>
				<div class="mb-2 text-lg font-semibold text-slate-800">
					{{ 'Wallet Information Options' }}
				</div>
				<!-- Modal content -->
				<div class="mt-5 mb-10 space-y-2">
					<ul class="mb-4">
						<li class="py-1 px-3">
							<label class="flex items-center">
								<input type="checkbox" class="form-checkbox" v-model="walletDataOptions.includeNetWorth" />
								<span class="text-sm font-medium ml-2">Include Net Worth</span>
							</label>
						</li>
						<li class="py-1 px-3">
							<label class="flex items-center">
								<input type="checkbox" class="form-checkbox" v-model="walletDataOptions.includeNftCount" />
								<span class="text-sm font-medium ml-2">Include NFT Count</span>
							</label>
						</li>
						<li class="py-1 px-3">
							<label class="flex items-center">
								<input type="checkbox" class="form-checkbox" v-model="walletDataOptions.includeEthCount" />
								<span class="text-sm font-medium ml-2">Include ETH Count</span>
							</label>
						</li>
						<li class="py-1 px-3">
							<label class="flex items-center">
								<input type="checkbox" class="form-checkbox" v-model="showEventDropdown" />
								<span class="text-sm font-medium ml-2">Include Data From Custom Event:</span>
							</label>
							<div class="w-full px-5" v-if="showEventDropdown">
								<DropdownFull
								:clearOnClick="true"
								:options="eventOptions"
								:selectedId="eventId"
								v-on:change="eventChanged"
								/>
							</div>
						</li>
					</ul>
				</div>

				<!-- Modal footer -->
				<div class="flex flex-wrap justify-end space-x-2">
					<button
						class="btn-sm border-slate-200 hover:border-slate-300 disabled:border-slate-300 disabled:bg-slate-100 disabled:text-slate-400 disabled:cursor-not-allowed"
						@click.stop="updateWalletOptions">
						Close
					</button>
				</div>
			</div>
		</div>
	</ModalBlank>
</template>

<script>
import PaginationNumeric2 from '../../components/PaginationNumeric2.vue';
import SearchForm from '../../components/SearchForm.vue';
import DropdownFull from '../../components/DropdownFull.vue';
import '../../css/long-loader-style.css';
import { executeSegment, getWalletSegmentInfo, updateSegment, getWalletSegmentDownload } from '../../services/segmentbuilder';
import { getCustomEventTypes } from '../../services/custom-events';

import ModalBlank from '../../components/ModalBlank.vue';
import { csv } from 'd3-fetch';

export default {
	name: 'SegmentWalletList',
	components: {
		SearchForm,
		PaginationNumeric2,
		ModalBlank,
		DropdownFull
	},
	props: ['segmentName', 'segmentId', 'segmentViewName'],
	data() {
		return {
			walletList: [],
			fullWalletData: [],
			totalWalletCount: 0,
			isLoaded: false,
			dataAvailable: false,
			downloadPreparing: false,
			downloadStatus: '',
			showWalletOptionsModal: false,
			eventTypes: [],
			eventId: '',
			showEventDropdown: false,
			walletDataOptions: {
				includeNetWorth: true,
				includeEthCount: true,
				includeNftCount: true,
				includeDataFromEventType: '',
			}
		}
	},
	computed: {
		walletCountDisplay() {
			return `Wallets ${this.totalWalletCount ? `(${this.totalWalletCount})` : ''}`
		},
	},
	setup() { },
	methods: {
		async fetchPage(page) {
			this.isLoaded = false;
			this.downloadStatus = "Processing Wallets... (0%)";

			console.log("FETCHING PAGE", this.segmentViewName, 'ETH', page);
			const result = await executeSegment(
				this.segmentViewName,
				'ETH',
				page - 1, //zero-based
			);
			this.initialSegmentResult = result;
			this.walletList = result.body.data;
			this.totalWalletCount = Number(result.body.totalcount) || 0;
			if (this.totalWalletCount > 0) {
				this.fullWalletData = await this.getAdditionalWalletData(this.walletList, { ...this.walletDataOptions, includeDataFromEventType: this.showEventDropdown && this.walletDataOptions.includeDataFromEventType });
				this.dataAvailable = true;
			}
			this.isLoaded = true;

			this.continueDownloadingWallets().catch(err => {
			});

		},
		async continueDownloadingWallets() {
			const responseLimit = 400000;
			const sizePerAddress = 100;
			const addressesPerResponse = responseLimit / sizePerAddress;
			const pageCount = Math.ceil(this.totalWalletCount / addressesPerResponse);

			const allAddressesResponse = await this.splitRequest(
				[
					this.segmentViewName,
					'ETH',
					undefined, //zero-based
					undefined,
					{ countonly: false, pagesize: addressesPerResponse }
				],
				executeSegment,
				parameters => new Array(pageCount).fill(undefined).map((x, i) => {
					const singleParams = [...parameters];

					singleParams[2] = i;

					return singleParams;
				}),
				responses => responses.map(x => x.body.data).flat()
			);

			this.allWalletList = allAddressesResponse;

			return this.continueDownloadingWalletData();
		},
		async splitRequest(parameters, requestor, splitter, merger) {
			const requests = splitter(parameters).map(x => requestor(...x));
			const responses = await Promise.all(requests);
			const results = merger(responses);

			return results;
		},
		async getAdditionalWalletData(walletList, options) {
			let fullWalletData;
			const walletOverview = await getWalletSegmentInfo(walletList, 'ETH', options);
			fullWalletData = walletOverview.body;

			let missingWallets;

			if (fullWalletData.length < walletList.length) {
				missingWallets = walletList.filter(wallet => !fullWalletData.find(w => w.address === wallet));

			} else if(walletList.length < fullWalletData.length) {
				missingWallets = fullWalletData.filter(wallet => !walletList.find(w => w === wallet.address));
			}

			if (missingWallets && missingWallets.length > 0) {
				fullWalletData = [
					...fullWalletData,
					...missingWallets.map(wallet => ({
						address: wallet,
						net_worth: '?',
						total_nfts: '?',
						eth_balance: '?',
					}))
				]
			}

			return fullWalletData;
		},
		async continueDownloadingWalletData() {
			const responseLimit = 400000;
			const sizePerAddress = 500;
			const addressesPerResponse = responseLimit / sizePerAddress;
			const pageCount = Math.ceil(this.totalWalletCount / addressesPerResponse);

			const allWalletDataResponse = await this.splitRequest(
				this.allWalletList,
				this.getAdditionalWalletData,
				parameters => new Array(pageCount).fill(undefined).map((x, i) =>
					[
						parameters.slice(
							i * addressesPerResponse,
							i * addressesPerResponse + addressesPerResponse
						),
						{ ...this.walletDataOptions, includeDataFromEventType: this.showEventDropdown && this.walletDataOptions.includeDataFromEventType }
					]
				),
				responses => responses.flat()
			);

			this.allFullWalletData = allWalletDataResponse;
		},
		formatWalletData(walletData) {
			if (walletData !== undefined) {
				return walletData.toLocaleString();
			}
		},
		navigateToWallet(wallet) {
			let routeData = this.$router.resolve({
				name: 'WalletProfile',
				params: { address: wallet.address }
			});
			window.open(routeData.href, '_blank');
		},
		async downloadWalletList() {
			if (this.downloadPreparing) {
				return;
			}

			this.downloadPreparing = true;
			this.downloadStatus = "Preparing Download... (0%)";
			let percentage = 5;
			let result = await getWalletSegmentDownload(this.segmentViewName, '', this.walletDataOptions);
			let csvURL = '';
			console.log(result);
			while(csvURL === '' && !result.error) {
				await new Promise(r => setTimeout(r, 2000));
				this.downloadStatus = `Generating Download... (${percentage}%)`;
				percentage += 5;
				if(percentage > 75) {
					percentage = 75;
				}
				result = await getWalletSegmentDownload(this.segmentViewName, result.body.queryExecutionId, this.walletDataOptions);
				csvURL = result.body.url || '';
				console.log(result);
			}
			this.downloadStatus = `Generating File Download... (85%)`;
			console.log("DONE", result.url);

			const nowString = new Date().toISOString().replace(/:[0-9]+\.[0-9]+/, '');
			const fileName = `${this.segmentName || `audience`}-${nowString}.csv`;
			console.log("DOWNLOADING", csvURL, fileName);
			await this.downloadFileFromURL(csvURL, 10, fileName);
			this.downloadPreparing = false;
		},
		async downloadFileFromURL(url, retries, fileName) {
			for (let i = 0; i < retries; i++) {
				try {
				const response = await fetch(url);
				if (response.ok) {
					const blob = await response.blob();
					const downloadUrl = URL.createObjectURL(blob);

					const link = document.createElement('a');
					link.href = downloadUrl;
					link.download = fileName;
					document.body.appendChild(link);
					link.click();
					document.body.removeChild(link);

					break; // Download successful, exit the loop
				} else {
					console.warn(`Download failed. Retry ${i + 1}/${retries}`);
				}
				} catch (error) {
				console.warn(`Download failed. Retry ${i + 1}/${retries}`, error);
				}

				await new Promise(resolve => setTimeout(resolve, 5000)); // Wait for 5 seconds before retrying
			}
		},
		downloadFile(fileName, type, fileBody) {
			const file = new File(
				[fileBody],
				fileName,
				{ type }
			)

			const link = document.createElement('a');
			const url = URL.createObjectURL(file);

			link.href = url;
			link.download = file.name;
			document.body.appendChild(link);
			link.click();

			document.body.removeChild(link);
			URL.revokeObjectURL(url);
		},
		updateWalletOptions() {
			this.showWalletOptionsModal = false;

			this.fetchPage(1);
		},

		eventChanged(eventId) {
			const evt = this.eventOptions.find(x => x.id === eventId);
			if (!evt) {
				this.walletDataOptions.includeDataFromEventType = '';
				this.eventId = '';

				return;
			}

			this.eventId = eventId;

			this.walletDataOptions.includeDataFromEventType = evt.name;
		},
	},
	async mounted() {
		const [, eventTypes] = await Promise.all([
			this.fetchPage(1),
			getCustomEventTypes()
		]);

		this.eventOptions = eventTypes.body.map((x, i) => ({ id: i, name: x }));

		if (this.segmentId) {
			await updateSegment(this.segmentId, { addressCount: this.totalWalletCount });
		}
		this.isLoaded = true;
	}
}
</script>

<style>
.download-link-color {
	filter: invert(30%) sepia(73%) saturate(6620%) hue-rotate(252deg) brightness(91%) contrast(98%);
}
</style>
