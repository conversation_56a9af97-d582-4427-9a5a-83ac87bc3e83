<template>
	<div class="grid grid-flow-col border-b border-slate-200 justify-center items-center gap-2">
		<div class="mt-4 mb-4">
			<button
				data-cy="segment-preview-save-and-preview"
				class="btn border-slate-200 hover:border-ralgranite-300 text-ralgranite-300 hover:text-ralgranite-400 bg-white disabled:border-slate-300 disabled:bg-slate-100 disabled:text-slate-400 disabled:cursor-not-allowed mr-4"
				@click.stop="runPreview()" :disabled="isCreateSegmentReady || isPreviewLoading || isPublishLoading"
				v-if="!isNowPublished">
				<svg v-if="isPreviewLoading" class="animate-spin w-4 h-4 fill-current shrink-0 mr-2" viewBox="0 0 16 16">
					<path
						d="M8 16a7.928 7.928 0 01-3.428-.77l.857-1.807A6.006 6.006 0 0014 8c0-3.309-2.691-6-6-6a6.006 6.006 0 00-5.422 8.572l-1.806.859A7.929 7.929 0 010 8c0-4.411 3.589-8 8-8s8 3.589 8 8-3.589 8-8 8z" />
				</svg>
				<span class="hidden xs:block">Save & Preview</span>
			</button>

			<button
				class="btn border-slate-200 hover:border-ralgranite-300 text-ralgranite-300 hover:text-ralgranite-400 bg-white disabled:border-slate-300 disabled:bg-slate-100 disabled:text-slate-400 disabled:cursor-not-allowed mr-4"
				@click.stop="runDuplicate()" v-if="isNowPublished">
				<span class="hidden xs:block">Duplicate Segment</span>
			</button>

			<button
				v-if="!isSelfService || !isAtSelfServiceLimit || isNowPublished || isCreateSegmentReady || isPreviewLoading || isPublishLoading || isNowPublished"
				class="btn bg-indigo-500 hover:bg-indigo-600 text-white disabled:bg-indigo-600 disabled:text-slate-400 disabled:cursor-not-allowed disabled"
				:disabled="isCreateSegmentReady || isPreviewLoading || isPublishLoading || isNowPublished"
				@click.stop="runSegment()">
				<svg v-if="isPublishLoading" class="animate-spin w-4 h-4 fill-current shrink-0 mr-2" viewBox="0 0 16 16">
					<path
						d="M8 16a7.928 7.928 0 01-3.428-.77l.857-1.807A6.006 6.006 0 0014 8c0-3.309-2.691-6-6-6a6.006 6.006 0 00-5.422 8.572l-1.806.859A7.929 7.929 0 010 8c0-4.411 3.589-8 8-8s8 3.589 8 8-3.589 8-8 8z" />
				</svg>
				<span class="hidden xs:block" v-if="!isNowPublished">Publish</span>
				<span class="hidden xs:block" v-if="isNowPublished">Published</span>
			</button>
			<button
				v-if="isSelfService && isAtSelfServiceLimit && !isNowPublished && !isCreateSegmentReady && !isPreviewLoading && !isPublishLoading && !isNowPublished"
				class="btn bg-indigo-500 hover:bg-indigo-600 text-white disabled:bg-indigo-600 disabled:text-slate-400 disabled:cursor-not-allowed disabled"
				id="upgradae-button">
				<span class="hidden xs:block">Upgrade</span>
			</button>
		</div>
	</div>


	<div class="overflow-x-auto p-b-4">
		<div class="py-5 text-center text-sm text-slate-800"
			v-if="(!wallets || wallets.length == 0) && !isPreviewLoading && !isPublishLoading && !isNowPublished">
			No Preview Data Available
		</div>

		<div class="py-5 text-center text-sm text-slate-800"
			v-if="(!wallets || wallets.length == 0) && !isPreviewLoading && !isPublishLoading && isNowPublished">
			Segment has been Published. <br />
			You can view the full list of wallets in Wallet Results.
		</div>

		<div class="p-2 sm:p-4 sm:h-64 flex flex-col sm:flex-row gap-5 select-none" v-if="isPreviewLoading">
			<div class="flex flex-col flex-1 gap-5 sm:p-2">
				<div class="flex flex-1 flex-col gap-3">
					<div class="bg-gray-200 w-full animate-pulse h-5 rounded-2xl"></div>
					<div class="bg-gray-200 w-full animate-pulse h-5 rounded-2xl"></div>
					<div class="bg-gray-200 w-full animate-pulse h-5 rounded-2xl"></div>
					<div class="bg-gray-200 w-full animate-pulse h-5 rounded-2xl"></div>
					<div class="bg-gray-200 w-full animate-pulse h-5 rounded-2xl"></div>
				</div>
			</div>
		</div>

		<table v-if="wallets && wallets.length > 0 && !isPreviewLoading && !isPublishLoading"
			class="table-auto w-full divide-y divide-slate-200">
			<tr>
				<td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
					<div class="text-xs font-medium text-slate-800">Total Wallets in Segment ({{ count }})</div>
				</td>
			</tr>
			<tbody class="text-sm">
				<tr v-for="wallet in wallets" :key="wallet">
					<td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
						<div class="text-xs font-medium text-slate-800">
							<router-link class="text-ralpurple-500" :to="{name: 'WalletProfile', params: {address: wallet}}">
								{{ wallet }}
							</router-link>
						</div>
					</td>
				</tr>
			</tbody>
		</table>
	</div>
</template>

<script>
import DropdownClassicDark from '../../components/DropdownClassicDark.vue';
import ModalBasic from '../../components/ModalBasic.vue';
import { getSegments } from '../../services/segmentbuilder'

export default {
	name: 'SegmentPreview',
	props: ['wallets', 'count', 'hasMore', 'isCreateSegmentReady', 'isPreviewLoading', 'isPublishLoading', 'isNowPublished'],
	emits: ['runPreview', 'runSegment'],
	components: {
    DropdownClassicDark,
    ModalBasic,
},
	data() {
		return {
			statusOptions: [{ "name": "Draft", "id": 0 }, { "name": "Published", "id": 1 }],
			selected: { "name": "Draft", "id": 0 },
		}
	},
	computed: {
		isSelfService() {
			return localStorage.getItem('selfService') == 'true';
		},
		isAtSelfServiceLimit() {
			return localStorage.getItem('selfServiceAudienceCount') >= 2;
		}
	},
	methods: {
		runSegment() {
			this.$emit('runSegment');
		},
		runPreview() {
			this.$emit('runPreview');
		},
		runDuplicate() {
			this.$emit('runDuplicate');
		},
	},
	async mounted() {

			// this.segments = await getSegments();
			// localStorage.setItem('selfServiceAudienceCount', this.segments.filter(x => !x.viewname.endsWith('_5')).length);
	}
}
</script>
