<template>
  <div class="flex h-screen overflow-hidden">

    <!-- Sidebar -->
    <Sidebar :sidebarOpen="sidebarOpen" @close-sidebar="sidebarOpen = false" />

    <!-- Content area -->
    <div class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">

      <!-- Site header -->


      <main>
        <!-- Avatar -->
        <div class="relative h-32 bg-slate-200">
          <img class="object-cover h-full w-full" src="../images/profile_banner.png" width="979" height="230"
            alt="Profile background" />
          <!-- adjust to height of 64 down the line ^^ -->

          <div class="absolute top-0 left-0 px-6 py-4 mt-3">
            <!--<img class="rounded-lg drop-shadow border border-ralpurple-700" src="../images/web3-wallet-profile.png"
              width="128" height="128" />
            -->
            <h1 class="mb-2 text-2xl font-semibold text-slate-200">Wallet Address</h1>
            <h4 class="mb-1 mt-3 text-normal font-semibold tracking-tight text-ralcloud-500">{{ $route.params.address }}</h4>
            <a v-if="walletEmail" :href="`mailto:${walletEmail}`" class="mb-3 mt-1 text-normal font-semibold tracking-tight text-ralcloud-500">{{ walletEmail }}</a>
          </div>
        </div>

        <!-- Empty State -->
        <!--
            <div
              class="max-w-2xl m-auto mt-16"

            >
              <div class="text-center px-4">
                <div
                  class="
                    inline-flex
                    items-center
                    justify-center
                    w-16
                    h-16
                    rounded-full
                    bg-gradient-to-t
                    from-ralcloud-300
                    to-ralcloud-700
                    mb-4
                  "
                >
                <svg xmlns="http://www.w3.org/2000/svg" height="42" width="42" viewBox="0 0 48 48"><title>zoom</title><g fill="#FFFFFF" class="nc-icon-wrapper"><path d="M45.268,41.732,32.639,29.1A17.066,17.066,0,1,0,29.1,32.639L41.732,45.268a2.5,2.5,0,0,0,3.536-3.536ZM4,19A15,15,0,1,1,19,34,15.017,15.017,0,0,1,4,19Z" fill="#FFFFFF"></path></g></svg>
                </div>
                <h2 class="text-2xl text-slate-800 font-bold mb-2">
                  Woops, we don't have data on that wallet yet!
                </h2>
                <div class="mb-6">
                  Need some help? <a href="https://msng.link/o/?RaleonHQ=tg" class="text-ralpurple-700 font-bold">Drop us a message.</a>
                </div>
                <router-link to="/" class="btn bg-indigo-500 hover:bg-indigo-600 text-white">Back to Dashboard</router-link>
              </div>
            </div>
            -->
        <!-- End -->

        <div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">
          <!-- Page header -->
          <div class="sm:flex sm:justify-between sm:items-center mb-8">

            <!-- Left: Title -->
            <div class="mb-4 sm:mb-0">
              <div class="flex flex-wrap justify-center sm:justify-start space-x-4">
                <div class="flex items-center">
                  <div v-if="!isLoaded" class="bg-gray-200 h-6 rounded-xl text-center animate-pulse"
                    style="width: 15rem"></div>
                  <template v-if="isLoaded">
                    <span class="text-sm font-medium whitespace-nowrap text-slate-500 ml-2 mr-2">
                      {{ walletPersona}}
                    </span>
                    <Tooltip size="lg" bg="dark" position="right">
                      <div class="text-sm font-medium text-slate-200">Generated by Raleon Insights.<br /><a
                          href="/support/insights" class="text-white mt-1" target="_blank">Learn more -></a></div>
                    </Tooltip>
                    <div v-for="tag of walletTags"
                      class="text-xs inline-flex font-medium bg-slate-300 text-granite-600 rounded-full text-center px-2.5 py-1 ml-3">
                      {{ tag}}
                    </div>
                  </template>
                </div>
              </div>
            </div>

            <!-- Right: Actions -->
            <!--
            <div class="grid grid-flow-col sm:auto-cols-max justify-start sm:justify-end gap-2">
              <button class="btn bg-indigo-500 hover:bg-indigo-600 text-white">Add to Campaign</button>
            </div>
            -->

          </div>

          <div class="relative mb-8">
            <div class="absolute bottom-0 w-full h-px bg-slate-200" aria-hidden="true"></div>
            <ul class="
                relative
                text-sm
                font-medium
                flex flex-nowrap
                -mx-4
                sm:-mx-6
                lg:-mx-8
                overflow-x-scroll
                no-scrollbar
              ">
              <li class="
                  mr-6
                  last:mr-0
                  first:pl-4
                  sm:first:pl-6
                  lg:first:pl-8
                  last:pr-4
                  sm:last:pr-6
                  lg:last:pr-8
                " v-on:click="toggleTabs(0)">
                <span v-bind:class="{
                  'block pb-3 text-indigo-500 whitespace-nowrap border-b-2 border-indigo-500':
                    openTab === 0,
                }" class="
                    flex
                    block
                    pb-3
                    text-slate-500
                    hover:text-slate-600
                    whitespace-nowrap
                    hover:border-b-2 hover:border-indigo-500
                    cursor-pointer
                  ">Overview</span>
              </li>
              <li class="
                  mr-6
                  last:mr-0
                  first:pl-4
                  sm:first:pl-6
                  lg:first:pl-8
                  last:pr-4
                  sm:last:pr-6
                  lg:last:pr-8
                " v-on:click="toggleTabs(1)" v-if="devEnabled == 'true'">
                <span v-bind:class="{
                  'block pb-3 text-indigo-500 whitespace-nowrap border-b-2 border-indigo-500':
                    openTab === 1,
                }" class="
                    flex
                    block
                    pb-3
                    text-slate-500
                    hover:text-slate-600
                    whitespace-nowrap
                    hover:border-b-2 hover:border-indigo-500
                    cursor-pointer
                  ">Event History
                </span>
              </li>
              <li class="mr-6 last:mr-0 first:pl-4 sm:first:pl-6 lg:first:pl-8 last:pr-4 sm:last:pr-6" v-on:click="toggleTabs(2)">
                <span
                  v-bind:class="{'block pb-3 text-indigo-500 whitespace-nowrap border-b-2 border-indigo-500': openTab === 2 }"
                  class="flex block pb-3 text-slate-500 hover:text-slate-600 whitespace-nowrap hover:border-b-2 hover:border-indigo-500">
                  Audiences
                </span>
              </li>
            </ul>
          </div>

          <!-- Tab 1-->
          <div v-bind:class="{
            'hidden': openTab !== 0,
            'block animate-fadein-content ': openTab === 0,
          }">
            <!-- project header -->
            <div class="sm:flex sm:justify-between sm:items-center mb-4">
              <!-- Left: Title -->
              <div class="mb-4 sm:mb-0">
                <h2 class="text-xl md:text-xl text-slate-400 uppercase font-semibold">
                  Wallet Details for Project
                </h2>
              </div>

              <!-- Right: Actions  -->
              <div class="
                grid grid-flow-col
                sm:auto-cols-max
                justify-start
                sm:justify-end
                gap-2
              ">
                <DropdownClassic :options="projectList" :select="projectList[0]" v-if="isLoaded"
                  @projectSelected="(projectId) => selectedProject = projectId" />
              </div>
            </div>

            <!-- project section content -->
            <ProfileProjectSummary :walletAddress="$route.params.address" :attributionStatus="attributionEnabled"
              :projectId="selectedProject" />

            <!-- overview section content -->
            <div class="relative mb-4 mt-8">
              <h2 class="text-xl md:text-xl text-slate-400 uppercase font-semibold">
                Wallet Overview
              </h2>
            </div>

            <div class="grid grid-cols-12 gap-6" id="walletTabContent">

              <ProfileCurrentBalanceByType :walletAddress="$route.params.address.toLowerCase()" />

              <ProfileActivityByHour :walletAddress="$route.params.address.toLowerCase()" />

              <UniversalLister :widget="dappWalletShareData" />

              <UniversalLister :widget="walletFlow" />

              <UniversalLister :widget="walletNFTs" />

            </div>
          </div>

          <!-- Tab 2-->
          <div v-bind:class="{
            'hidden': openTab !== 1,
            'block animate-fadein-content ': openTab === 1,
          }">

            <ProfileEventHistory :walletAddress="$route.params.address" />

          </div>

          <!-- Tab 3 -->
          <div
            v-bind:class="{
              'hidden': openTab !== 2,
              'block animate-fadein-content ': openTab === 2,
            }">
            <h2 class="mb-4 text-xl md:text-xl text-slate-400 uppercase font-semibold">
              Current Audiences
            </h2>

            <div class="bg-white shadow-lg rounded-sm border border-slate-200 min-w-10 w-1/3 px-5 py-5">
              <ul class="list-none">
                <li v-for="segment of segments">
                  <a
                    class="font-medium text-ralpurple-500 hover:text-ralpurple-700 cursor-pointer"
                    @click.stop="navigateToSegment(segment.id)">
                    {{ segment.name }}
                  </a>
                </li>

                <li v-if="segments.length == 0">
                  No Audiences
                </li>
              </ul>
            </div>
          </div>



        </div>
      </main>

    </div>

  </div>
</template>

<script>
import { ref } from 'vue'
import DropdownClassic from '../components/DropdownClassic.vue'
import FilterButton from '../components/DropdownFilter.vue'
import QuickMetric from '../components/QuickMetric.vue'
import Tooltip from '../components/Tooltip.vue'
import Header from '../partials/Header.vue'
import Sidebar from '../partials/Sidebar.vue'
import { getOrgById } from '../services/organization'
import { getProjectsByOrgId } from '../services/project'

import { getClusterPersona, getClusterPersonaEmoji } from '../../../src/utils/utils'
import ProfileActivityByHour from '../partials/analytics/Wallet/ProfileActivityByHour.vue'
import ProfileAllTimeActivityByDapp from '../partials/analytics/Wallet/ProfileAllTimeActivityByDapp.vue'
import ProfileAllTimeActivityByNetwork from '../partials/analytics/Wallet/ProfileAllTimeActivityByNetwork.vue'
import ProfileAssetsHeld from '../partials/analytics/Wallet/ProfileAssetsHeld.vue'
import ProfileCurrentBalance from '../partials/analytics/Wallet/ProfileCurrentBalance.vue'
import ProfileCurrentBalanceByType from '../partials/analytics/Wallet/ProfileCurrentBalanceByType.vue'
import ProfileEventHistory from '../partials/analytics/Wallet/ProfileEventHistory.vue'
import ProfilePopularDAO from '../partials/analytics/Wallet/ProfilePopularDAO.vue'
import ProfilePopularDapps from '../partials/analytics/Wallet/ProfilePopularDapps.vue'
import ProfileProjectSummary from '../partials/analytics/Wallet/ProfileProjectSummary.vue'
import ProfileRelatedWallets from '../partials/analytics/Wallet/ProfileRelatedWallets.vue'
import ProfileSummary from '../partials/analytics/Wallet/ProfileSummary.vue'
import ProfileTokenHoldDuration from '../partials/analytics/Wallet/ProfileTokenHoldDuration.vue'
import ProfileTokenInflow from '../partials/analytics/Wallet/ProfileTokenInflow.vue'
import ProfileTokenOutflow from '../partials/analytics/Wallet/ProfileTokenOutflow.vue'
import UniversalLister from '../partials/UniversalLister.vue'

import { getMetric, getWalletTags, getWalletEmail } from '../services/metrics'
import { getWalletSegments } from '../services/segmentbuilder'

export default {
  name: 'WalletProfile',
  components: {
    Sidebar,
    Header,
    FilterButton,
    Tooltip,
    QuickMetric,
    ProfileAllTimeActivityByNetwork,
    ProfileCurrentBalanceByType,
    ProfileCurrentBalance,
    ProfileActivityByHour,
    ProfileSummary,
    ProfileRelatedWallets,
    ProfilePopularDAO,
    ProfilePopularDapps,
    ProfileAssetsHeld,
    ProfileAllTimeActivityByDapp,
    ProfileTokenHoldDuration,
    ProfileTokenOutflow,
    ProfileTokenInflow,
    ProfileEventHistory,
    DropdownClassic,
    ProfileProjectSummary,
    UniversalLister
  },
  props: ['address'],
  setup() {

    amplitude.getInstance().logEvent('WALLET_PROFILE_VIEWED');
    const sidebarOpen = ref(true)
    let projectList = [];

    const devEnabled = localStorage.getItem('devEnabled');
    const betaEnabled = localStorage.getItem('betaEnabled');

    return {
      sidebarOpen,
      projectList,
      devEnabled,
      betaEnabled
    }
  },
  data() {
    return {
      openTab: 0,
      attributionEnabled: false,
      walletPersona: '',
      walletTags: [],
      isLoaded: false,
      selectedProject: '',
      walletEmail: '',
      dappWalletShareData: {
        title: 'dApp Wallet Share',
        id: `dapp-wallet-share-${Math.floor(Math.random() * 90000) + 100000}`,
        XAxisInputLabel: 'dapp',
        inputs: ['count'],
        apiName: 'wallet-share',
        projectId: this.address.toLowerCase(),
        type: 'PIE',
        size: 9
      },
      walletNFTs: {
        title: 'NFTs',
        id: `nft-${Math.floor(Math.random() * 90000) + 100000}`,
        XAxisInputLabel: 'contract_name',
        inputs: ['contract_name', 'contract_address', 'count', 'chain_name'],
        apiName: 'wallet-nfts',
        projectId: this.address.toLowerCase(),
        type: 'TABLE',
        size: 9
      },
      walletFlow: {
        title: 'USD Flow',
        id: `flow-${Math.floor(Math.random() * 90000) + 100000}`,
        XAxisInputLabel: 'type',
        inputs: ['usd'],
        apiName: 'wallet-flow',
        projectId: this.address.toLowerCase(),
        type: 'BAR HORIZONTAL',
        size: 3
      },
      segments: [],
    }
  },
  methods: {
    toggleTabs(tabNumber) {
      console.log("tab pass" + tabNumber + "and " + this.openTab);
      this.openTab = tabNumber;
    },
    navigateToSegment(id) {
      this.$router.push({ name: 'SegmentBuilder', query: { id } });
    },
    async getWalletInfo() {
      let walletInfo = await getMetric(
        'ETH',
        this.address.toLowerCase(),
        'ADDRESS_MODEL_PERSONA'
      );

      if (walletInfo.body && walletInfo.body.Items.length) {
        const data = walletInfo.body.Items[0].data.cluster;
        this.walletPersona = getClusterPersonaEmoji(data) + getClusterPersona(data);
      } else {
        this.walletPersona = getClusterPersonaEmoji() + getClusterPersona();
      }
    },
    async getWalletTags() {
      let tags = await getWalletTags(
        'ETH',
        this.address.toLowerCase(),
        null
      );

      if (tags.body && tags.body.length) {
        this.walletTags = tags.body.filter(tag => tag.active).map(tag => tag.tag);
      }
    },
    async loadProjects() {
      const projects = await getProjectsByOrgId(
        localStorage.getItem('userOrgId'),
      );

      if (projects && projects.length) {
        this.projectList = projects.map(project => {
          return {
            id: project.uuid,
            name: project.name
          }
        });
      }

      this.selectedProject = this.projectList[0].id;
    },
    async getWalletEmail() {
      const results = await getWalletEmail(
        localStorage.getItem('userOrgId'),
        this.address.toLowerCase(),
      );

      if (results && results.body && results.body.length) {
        this.walletEmail = results.body[0].email;
      }
    },
    async getWalletSegments() {
      const results = await getWalletSegments(this.address.toLowerCase());

      if (results && results.body && results.body.length) {
        this.segments = results.body;
      }
    },
  },
  async mounted() {
    this.loadProjects().then(() => {});
    this.getWalletSegments().then(() => {});
    this.getWalletInfo().then(() => {});
    this.getWalletTags().then(() => {});
    this.getWalletEmail().then(() => {});

    const orgResult = await getOrgById(
      `${localStorage.getItem('userOrgId')}`,
    );

    this.attributionEnabled = orgResult.attribution;
    this.isLoaded = true;
  }
}
</script>
