<template>
  <main class="bg-white">
    <div class="relative flex">

      <!-- Content -->
      <div class="w-full md:w-1/2">
        <div class="min-h-screen h-full flex flex-col after:flex-1">
          <div class="flex-1">
            <div class="flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8">
              <!-- Logo -->
              <router-link class="block" to="/">
                <img src="../images/raleon-logo-whitebg.jpg" width="64" height="64" />
              </router-link>
            </div>
          </div>

          <div v-if="!resettingPassword" class="max-w-sm mx-auto px-4 py-8">
            <h1 class="text-3xl text-slate-800 font-bold mb-6">Reset your Password ✨</h1>
            <div class="space-y-4">
              <div :class="{ error: v$.email.$errors.length }">
                <label class="block text-sm font-medium mb-1" for="email">Email Address <span class="text-rose-500">*</span></label>
                <input v-model="v$.email.$model" id="email" class="form-input w-full" type="email" />
                <div class="input-errors" v-for="error of v$.email.$errors" :key="error.$uid">
                  <div class="error-msg text-center text-sm font-small text-rose-400 pt-2">{{ error.$message }}</div>
                </div>
                <div v-if="emailSent" class="success-msg text-center text-sm font-small text-ralapple-400 pt-2">Reset password email sent.</div>
              </div>
            </div>
            <div class="flex justify-center mt-3">
              <button
                class="btn bg-indigo-500 hover:bg-indigo-600 text-white whitespace-nowrap disabled:border-slate-300 disabled:bg-slate-100 disabled:text-slate-400 disabled:cursor-not-allowed"
                :disabled="sendResetLinkDisabled"
                @click.stop="sendResetLink"
              >
                Send Reset Link
              </button>
            </div>
          </div>

          <div v-if="resettingPassword" class="max-w-sm mx-auto px-4 py-8">
            <h1 class="text-3xl text-slate-800 font-bold mb-6">Reset your Password ✨</h1>
              <label class="block text-sm font-medium" for="email-resetting-pw">Email Address</label>
              <input :placeholder="email" id="email-resetting-pw" class="mb-3 form-input w-full hover:cursor-not-allowed" disabled type="email" />
              <label class="block text-sm font-medium mb-1" for="pw1">New Password
                <span class="text-rose-500">*</span>
              </label>
              <div class="relative">
                <input v-model="v$.password.$model" id="pw1" class="form-input w-full" type="password" />
                  <img
                    v-if="!showPassword"
                    id="pwEye"
                    class="ml-1 absolute mt-1 hover:cursor-pointer top-0 right-0"
                    src="../images/password-show.svg"
                    width="40"
                    height="40"
                    alt="Password hidden"
                    @click.stop="showHidePw" />

                  <img
                    v-if="showPassword"
                    id="pwEye"
                    class="ml-1 absolute mt-1 hover:cursor-pointer top-0 right-0"
                    src="../images/password-hide.svg"
                    width="40"
                    height="40"
                    alt="Password hidden"
                    @click.stop="showHidePw" />

                <div class="input-errors" v-for="error of v$.password.$errors" :key="error.$uid">
                  <div class="error-msg text-center text-sm font-small text-rose-400 pt-2">{{ error.$message }}</div>
                </div>
              </div>
              <label class="block text-sm font-medium mt-3 mb-1" for="pw2">Confirm Password <span class="text-rose-500">*</span></label>
              <div class="relative">
                <input v-model="v$.confirmPassword.$model" id="pw2" class="mb-1 form-input w-full" type="password" />
                <img
                  v-if="!showConfirmPassword"
                  id="confirmPwEye"
                  class="ml-1 absolute mt-1 hover:cursor-pointer top-0 right-0"
                  src="../images/password-show.svg"
                  width="40"
                  height="40"
                  alt="Password hidden"
                  @click.stop="showHidePw" />

                <img
                  v-if="showConfirmPassword"
                  id="confirmPwEye"
                  class="ml-1 absolute mt-1 hover:cursor-pointer top-0 right-0"
                  src="../images/password-hide.svg"
                  width="40"
                  height="40"
                  alt="Password hidden"
                  @click.stop="showHidePw" />
              </div>
              <div class="input-errors" v-for="error of v$.confirmPassword.$errors" :key="error.$uid">
                <div class="error-msg text-center text-sm font-small text-rose-400 pt-2">{{ error.$message }}</div>
              </div>
              <div v-if="passwordChanged" class="success-msg text-center text-sm font-small text-ralapple-400 pt-2">Password changed. Logging you in.</div>
            <div class="flex justify-center mt-3">
              <button
                class="btn bg-indigo-500 hover:bg-indigo-600 text-white whitespace-nowrap disabled:border-slate-300 disabled:bg-slate-100 disabled:text-slate-400 disabled:cursor-not-allowed"
                :disabled="changePasswordDisabled"
                @click.stop="updatePassword"
              >
                <svg v-if="changingPassword" class="animate-spin w-4 h-4 fill-current shrink-0 mr-2"
                  viewBox="0 0 16 16">
                  <path
                    d="M8 16a7.928 7.928 0 01-3.428-.77l.857-1.807A6.006 6.006 0 0014 8c0-3.309-2.691-6-6-6a6.006 6.006 0 00-5.422 8.572l-1.806.859A7.929 7.929 0 010 8c0-4.411 3.589-8 8-8s8 3.589 8 8-3.589 8-8 8z" />
                </svg>
                Change Password
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Image -->
      <div class="hidden md:block absolute top-0 bottom-0 right-0 md:w-1/2" aria-hidden="true">
        <img class="object-cover object-center w-full h-full" src="../images/r-login-bg.png" width="760" height="1024"
          alt="Authentication" />
      </div>

    </div>

  </main>
</template>

<script>

import { initResetPassword, finishResetPassword, userLogin, getUserInfo, setUserInfo } from '../services/user.js'
import { useVuelidate } from '@vuelidate/core'
import { required, email, minLength, sameAs, helpers } from '@vuelidate/validators'
import { ref } from 'vue'

export default {
  name: 'ResetPassword',
  data() {
    return {
      email: '',
      emailSent: false,
      resettingPassword: false,
      password: '',
      confirmPassword: '',
      showPassword: false,
      showConfirmPassword: false,
      resetKey: '',
      passwordChanged: false,
      changingPassword: false,
    }
  },
  setup() {
    const externalResults = ref({})
    return {
      externalResults,
      v$: useVuelidate({ $externalResults: externalResults })
    }
  },
  computed: {
    sendResetLinkDisabled() {
      if (!this.email || this.emailSent) return true;

      if (this.v$.email.$errors.length) {
        const validator = this.v$.email.$errors[0].$validator;
        return validator == 'email' || validator == 'required';
      }
    },
    changePasswordDisabled() {
      return this.changingPassword ||
        !this.password ||
        !this.confirmPassword ||
        this.v$.password.$errors.length ||
        this.v$.confirmPassword.$errors.length;
    },
    resetPasswordDisabled() {
      if (!this.password || !this.confirmPassword) return true;

      const errors = this.v$.password.$errors.concat(this.v$.confirmPassword.$errors);
      if (errors.length) return true;

      return false;
    },
  },
  methods: {
    async sendResetLink() {
      this.emailSent = true;
      const result = await initResetPassword(this.email);
      if (result.error) {
        Object.assign(this.externalResults, { email: ['Email address does not exist.'] })
        this.emailSent = false;
      }
    },
    async userLogin() {
      const loginResult = await userLogin(this.email, this.password);

      if (loginResult.token) {
        localStorage.setItem('token', loginResult.token);
        let userInfo = await getUserInfo();
        await setUserInfo(userInfo);
        this.$router.push('/chat');
      } else {
        console.error('Error logging in');
      }
    },
    async updatePassword() {
      this.changingPassword = true;
      let response = await finishResetPassword(this.resetKey, this.password, this.confirmPassword);
      if (response.error && response.error.statusCode != 200) {
        Object.assign(this.externalResults, { confirmPassword: ['Error resetting password'] })
      } else {
        this.passwordChanged = true;
        setTimeout(async () => {
          await this.userLogin();
        }, 1500);
      }
    },
    showHidePw(event) {
      if (event.target.id == 'pwEye') {
        this.showPassword = !this.showPassword;
        document.getElementById('pw1').setAttribute('type', `${this.showPassword ? 'text' : 'password'}`);
      } else {
        this.showConfirmPassword = !this.showConfirmPassword;
        document.getElementById('pw2').setAttribute('type', `${this.showConfirmPassword ? 'text' : 'password'}`);
      }
    }
  },
  async mounted() {
    if (this.$route.query.email) {
      this.email = this.$route.query.email;
      this.resettingPassword = true;

      if (!this.$route.query.resetKey) throw new Error('Invalid token');

      this.resetKey = this.$route.query.resetKey;
    }
  },
  validations() {
    return {
      email: { email },
      password: {
        required: helpers.withMessage('Password is required.', required),
        minLength: helpers.withMessage('Passwords must be at least 8 characters.', minLength(8)),
      },
      confirmPassword: {
        required: helpers.withMessage('Password is required.', required),
        minLength: helpers.withMessage('Passwords must be at least 8 characters.', minLength(8)),
        sameAsPassword: helpers.withMessage('Passwords do not match.', sameAs(this.password))
      }
    }
  }
}
</script>
