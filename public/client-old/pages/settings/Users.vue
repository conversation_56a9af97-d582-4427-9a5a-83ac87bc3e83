<template>
	<div class="flex h-screen overflow-hidden bg-gray-50">
		<!-- Toast Status -->
		<ToastStatus :status="status" :text="statusText" @clear-status="clearStatus()" />

		<!-- Content area -->
		<div class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
			<main>
				<div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">
					<!-- Page header -->
					<div class="mb-8 flex items-center justify-between">
						<h1 class="text-3xl md:text-4xl text-gray-900 font-bold bg-gradient-to-r from-purple-600 to-purple-700 bg-clip-text text-transparent">Settings</h1>
						<button class="px-4 py-2 border border-gray-200 text-gray-600 font-medium rounded-xl hover:bg-gray-50 hover:border-gray-300 transition-all duration-200">
							<router-link to="/signin" @click="logout()" class="flex items-center gap-2">
								<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
								</svg>
								Sign Out
							</router-link>
						</button>
					</div>

					<!-- Content -->
					<div class="bg-white shadow-xl rounded-2xl mb-8 border border-gray-100">
						<div class="flex flex-col md:flex-row md:-mr-px">
							<SettingsSidebar />

							<div class="grow">
								<div class="p-8 space-y-8">
									<!-- Header with Invite Button -->
									<div class="flex items-center justify-between">
										<div>
											<h2 class="text-2xl font-bold text-gray-900 mb-2">Team Members</h2>
											<p class="text-gray-600">Manage user access and permissions for your organization.</p>
										</div>
										<button
											@click.stop="showInviteUsersModal = true"
											class="px-6 py-3 bg-gradient-to-r from-purple-600 to-purple-700 text-white font-medium rounded-xl hover:from-purple-700 hover:to-purple-800 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-all duration-200 flex items-center gap-2"
										>
											<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
											</svg>
											Invite Users
										</button>
									</div>

									<!-- Users Table Section -->
									<section>
										<div class="bg-white border border-gray-100 rounded-xl overflow-hidden">
											<div class="overflow-x-auto">
												<RaleonTable
													:row-data="users"
													:collapse-on-mobile="true"
													:show-custom-buttons="true"
													:auto-wrap="true"
													:forced-equal-width="true"
													@custom-button-row-clicked="handleCustomButtonClick($event)"
													:column-headers="[
													{
														name: 'Email',
														value: 'email',
														tooltip: null
													}, {
														name: 'Name',
														value: 'name',
														tooltip: null
													}, {
														name: 'Status',
														value: 'status',
														tooltip: null
													}
												]" />
											</div>
										</div>
									</section>

									<!-- Empty State (if no users) -->
									<div v-if="users.length === 0" class="text-center py-12">
										<svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
										</svg>
										<h3 class="text-lg font-medium text-gray-900 mb-2">No team members yet</h3>
										<p class="text-gray-500 mb-6">Get started by inviting your first team member to collaborate.</p>
										<button
											@click.stop="showInviteUsersModal = true"
											class="px-6 py-3 bg-gradient-to-r from-purple-600 to-purple-700 text-white font-medium rounded-xl hover:from-purple-700 hover:to-purple-800 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-all duration-200"
										>
											Invite Your First User
										</button>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</main>

			<!-- Invite Users Modal -->
			<InviteUsersModal
				:show="showInviteUsersModal"
				@close="showInviteUsersModal = false"
				@invites-sent="async (event) => await setStatus(event, true)"
				@invites-failed="async (event) => await setStatus(event, false)" />
		</div>
	</div>
</template>

<script>
import { ref } from 'vue'
import Sidebar from '../../partials/Sidebar.vue'
import Header from '../../partials/Header.vue'
import SettingsSidebar from '../../partials/settings/SettingsSidebar.vue'
import UsersTable from './UsersTable.vue'
import InviteUsersModal from './InviteUsersModal.vue'
import ToastStatus from '../component/ToastStatus.vue'
import { getUsers, resendInvite, removeUser } from '../../services/invite'
import RaleonTable from '../../../client/components/RaleonTable.ts.vue';
import { customerIOTrackEvent } from '../../../client/services/customerio.js';


let orgId;

export default {
	name: 'Account',
	components: {
	  Sidebar,
	  Header,
	  SettingsSidebar,
	  UsersTable,
	  InviteUsersModal,
	  ToastStatus,
	  RaleonTable,
	},
	setup() {
	  const sidebarOpen = ref(false);

	  return {
		 sidebarOpen,
	  }
	},
	computed: {
		userEmail() {
			return localStorage.getItem("email");
		}
	},
	data() {
		return {
			users: [],
			showInviteUsersModal: false,
			status: '',
			statusText: '',
		}
	},
	methods: {
		clearStatus() {
			this.status = '';
		},
		logout() {
			localStorage.removeItem('token');
		},
		async setStatus(e, success) {
			this.status = success ? 'success' : 'fail';
			this.statusText = e;
			await this.getUsersAndInvites();
		},
		inviteUsers() {
			customerIOTrackEvent('Viewed Teammates');
			this.showInviteUsersModal = true;
		},
		async getUsersAndInvites() {
			const {users, pendingInvites, expiredInvites} = await getUsers();
			const mappedUsers = [];
			const currentUserEmail = this.userEmail;

			users.forEach(user => {
				const isCurrentUser = user.email === currentUserEmail;
				mappedUsers.push({
					id: user.id,
					email: user.email,
					name: user.firstName + ' ' + user.lastName,
					status: { value: 'Active', color: this.getStatusTextColor('Active') },
					showCustomButton: isCurrentUser ? null : 'Remove User',
					userType: 'active'
				})
			});
			pendingInvites.forEach(invite => {
				mappedUsers.push({
					id: invite.id,
					email: invite.email,
					name: '',
					status: { value: 'Pending', color: this.getStatusTextColor('Pending')},
					showCustomButton: 'Resend Invite',
					userType: 'pending'
				})
			});

			expiredInvites.forEach(expiredInvite => {
				mappedUsers.push({
					id: expiredInvite.id,
					email: expiredInvite.email,
					name: '',
					status: { value: 'Expired', color: this.getStatusTextColor('Expired')},
					showCustomButton: 'Resend Invite',
					userType: 'expired'
				})
			});

			this.users = mappedUsers.sort((a, b) => a.email.localeCompare(b.email));
		},
		getStatusTextColor(status) {
				const additionalStyles = 'rounded-2xl px-2.5 py-1.5 min-w-full text-center'
				switch (status) {
					case 'Pending':
						return `text-ralblack-primary bg-ralwarning-light ${additionalStyles}`
					case 'Expired':
						return `text-red-main bg-red-300 ${additionalStyles}`
					case 'Active':
						return `text-ralsuccess-dark bg-ralsuccess-light ${additionalStyles}`
					default:
						return `text-ralinfo-dark bg-ralinfo-light ${additionalStyles}`
				}
			},
			async handleCustomButtonClick(row) {
				if (row.userType === 'pending' || row.userType === 'expired') {
					await this.resendInvite(row);
				} else if (row.userType === 'active') {
					await this.confirmRemoveUser(row);
				}
			},
			async resendInvite(row) {
				try {
						await resendInvite(row.email);
						this.setStatus('Invites Sent', true);
				} catch (e) {
					this.setStatus(e, false);
				}
			},
			async confirmRemoveUser(row) {
				const confirmed = confirm(`Are you sure you want to remove ${row.name || row.email} from the organization? This action cannot be undone.`);
				if (confirmed) {
					try {
						await removeUser(row.id);
						this.setStatus('User removed successfully', true);
					} catch (e) {
						this.setStatus(e.message || 'Failed to remove user', false);
					}
				}
			}
	},
	async mounted() {
		customerIOTrackEvent('Viewed Users');
		await this.getUsersAndInvites();
	}
 }
 </script>
