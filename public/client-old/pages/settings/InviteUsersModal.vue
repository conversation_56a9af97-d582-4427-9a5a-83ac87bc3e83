<template>
	<ModalBlank
		id="inviteUsersModal"
		:modal-open="show"
		@close-modal="$emit('close')">
		<div class="p-5 space-x-4">
			<div>
				<div class="mb-2 text-lg font-semibold text-slate-800">
					Invite people to Raleon
				</div>
				<!-- Modal content -->
				<div class="mt-5 mb-10 space-y-2">
					<label for="toInput" class="text-sm font-bold">To: </label>
					<div
						id="toAddresses"
						class="form-input w-full mb-10 resize-y overflow-auto h-24"
						data-text="Enter email addresses"
						@keydown.space="formatEmail"
						@keydown.enter="formatEmail"
						@focusout="formatEmail"
						@click="focusAddressEdit">
						<span v-for="email in validatedEmails" class="bg-ralgranite-300 box-border rounded text-white inline-flex mr-2 mb-2 pl-3 pr-3 pt-2 pb-2 opacity-50">
							{{email}}
							<button class="ml-3" @click.stop="removeSelectedEmail(email)">
								<svg class="w-4 h-4 fill-current">
									<path d="M7.95 6.536l4.242-4.243a1 1 0 111.415 1.414L9.364 7.95l4.243 4.242a1 1 0 11-1.415 1.415L7.95 9.364l-4.243 4.243a1 1 0 01-1.414-1.415L6.536 7.95 2.293 3.707a1 1 0 011.414-1.414L7.95 6.536z" />
								</svg>
							</button>
						</span>
						<span v-for="invalidEmail in invalidEmails" class="bg-rose-300 box-border rounded text-white inline-flex mr-2 mb-2 pl-3 pr-3 pt-2 pb-2">
							{{invalidEmail}}
							<button class="ml-3" @click.stop="removeSelectedEmail(invalidEmail)">
								<svg class="w-4 h-4 fill-current">
									<path d="M7.95 6.536l4.242-4.243a1 1 0 111.415 1.414L9.364 7.95l4.243 4.242a1 1 0 11-1.415 1.415L7.95 9.364l-4.243 4.243a1 1 0 01-1.414-1.415L6.536 7.95 2.293 3.707a1 1 0 011.414-1.414L7.95 6.536z" />
								</svg>
							</button>
						</span>
						<span
							id="addressEdit"
							contentEditable="true"
							data-text="Enter email addresses"
							class="h-23 border-none focus-visible:outline-none min-h-full"
							@click="focusAddressEdit"
							@keydown.backspace="removeLastEmail">&nbsp;
						</span>
					</div>
					<div class="input-errors" v-for="error of v$.invalidEmails.$silentErrors" :key="error.$uid">
						<div class="error-msg text-center text-sm font-small text-rose-400">{{ error.$message }}</div>
					</div>
				</div>

				<div class="mt-5 mb-10 space-y-2">
					<label for="roleInput" class="text-sm font-bold">Invite As: </label>
					<select id="roleInput" class="form-input w-full">
						<option value="customer" selected="true">General User</option>
						<option value="customer-admin">Admin User</option>
						<option value="support">Support User</option>
					</select>
				</div>

				<!-- Modal footer -->
				<div class="flex flex-wrap justify-end space-x-2">
					<button
						class="btn-sm border-slate-200 hover:border-slate-300 disabled:border-slate-300 disabled:bg-slate-100 disabled:text-slate-400 disabled:cursor-not-allowed"
						@click.stop="cancel">
						Cancel
					</button>
					<button
						class="btn bg-ralpurple-500 hover:bg-ralpurple-600 text-white disabled:border-slate-300 disabled:bg-slate-100 disabled:text-slate-400 disabled:cursor-not-allowed"
						:disabled="inviteUsersDisabled"
						@click.stop="inviteUsers">
						<svg v-if="invitingUsers" class="animate-spin w-4 h-4 fill-current shrink-0 mr-2"
							viewBox="0 0 16 16">
							<path
								d="M8 16a7.928 7.928 0 01-3.428-.77l.857-1.807A6.006 6.006 0 0014 8c0-3.309-2.691-6-6-6a6.006 6.006 0 00-5.422 8.572l-1.806.859A7.929 7.929 0 010 8c0-4.411 3.589-8 8-8s8 3.589 8 8-3.589 8-8 8z" />
						</svg>
						Invite Users
					</button>
				</div>
			</div>
		</div>
	</ModalBlank>
</template>

<script>
import ModalBlank from '../../components/ModalBlank.vue'
import { useVuelidate } from '@vuelidate/core'
import { maxLength, helpers } from '@vuelidate/validators'
import { inviteUsers } from '../../services/invite'
import { customerIOTrackEvent } from '../../../client/services/customerio.js';

export default {
	name: 'Account',
	components: {
	  ModalBlank,
	},
	props: ['show'],
	emits: ['close', 'invites-sent', 'invites-failed'],
	setup() {
		return {
			v$: useVuelidate()
		}
	},
	data() {
		return {
			emails: '',
			validatedEmails: [],
			invalidEmails: [],
			invitingUsers: false,
		}
	},
	computed: {
		inviteUsersDisabled() {
			const hasError = this.v$.invalidEmails.$silentErrors.length > 0;
			const noEmails = this.validatedEmails.length === 0;
			return hasError || noEmails || this.invitingUsers;
		}
	},
	methods: {
		focusAddressEdit(e) {
			const element = document.getElementById('addressEdit');
			element.classList.add('no-before');
			element.focus();
		},
		formatEmail(e) {
			const element = document.getElementById('addressEdit');
			if (element.innerText.trim()) {
				let emails = element.innerText.trim().split(' ').filter(x => x);
				let currentEmail = emails[emails.length - 1];
				if (this.validateEmail(currentEmail)) {
					this.validatedEmails.push(currentEmail);
				} else {
					this.invalidEmails.push(currentEmail);
				}
				element.innerHTML = '&nbsp;';
			}
		},
		removeLastEmail() {
			const element = document.getElementById('addressEdit')
			if (!element.innerText.trim()) {
				if (this.invalidEmails.length) {
					this.invalidEmails.pop();
				} else {
					this.validatedEmails.pop();
				}
				element.innerHTML = '&nbsp;';
			}
		},
		removeSelectedEmail(email) {
			let count = this.validatedEmails.length;
			this.validatedEmails = this.validatedEmails.filter(e => e !== email);
			if (count === this.validatedEmails.length) {
				this.invalidEmails = this.invalidEmails.filter(e => e !== email);
			}
		},
		validateEmail(email) {
			const pattern = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
			return pattern.test(email.toLowerCase());
		},
		cancel() {
			this.validatedEmails = [];
			this.invalidEmails = [];
			this.$emit('close');
		},
		async inviteUsers() {
			this.invitingUsers = true;
			const role = document.getElementById('roleInput').value;
			const emails = this.validatedEmails;
			try {
				await inviteUsers(emails, role);
				this.$emit('invites-sent', 'Invitations sent.');
				this.invalidEmails = [];
				this.validatedEmails = [];
			} catch(e) {
				this.$emit('invites-failed', e);
			}
			this.$emit('close');
			this.invitingUsers = false;
			customerIOTrackEvent('Teammate Invited');
		}
	},
	validations() {
		return {
			invalidEmails: {
				maxLength: helpers.withMessage('Please correct the invalid email address.', maxLength(0)),
				$autoDirty: true
			}
		}
	},
	watch: {
		show() {
			this.$nextTick(() => this.focusAddressEdit());
		}
	}
}
</script>
<style>
	#addressEdit:empty:before {
		content: attr(data-text);
		color: #a9a9a9;
	}
	#addressEdit.no-before::before {
		display: none;
	}
</style>
