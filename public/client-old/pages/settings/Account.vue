<template>
  <div class="flex h-screen overflow-hidden">

    <!-- Sidebar -->
    <Sidebar :sidebarOpen="sidebarOpen" @close-sidebar="sidebarOpen = false" />
    <ToastStatus :status="status" :text="statusText" @clear-status="clearStatus()" />

    <!-- Content area -->
    <div class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">

      <!-- Site header -->


      <main>
        <div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">

          <!-- Page header -->
          <div class="mb-8 flex">
            <!-- Title -->
            <h1 class="text-2xl md:text-3xl text-slate-800 font-bold">Settings</h1>
			<button class="ml-auto btn border-slate-200 hover:border-slate-300 text-slate-600">
				<router-link to="/signin" @click="logout()">
				Sign Out
				</router-link>
			</button>
          </div>

          <!-- Content -->
          <div class="bg-white shadow-lg rounded-sm mb-8">
            <div class="flex flex-col md:flex-row md:-mr-px">
              <SettingsSidebar />

              <div class="grow">
                <!-- Panel body -->
                <div class="p-6 space-y-6">
                  <h2 class="text-2xl text-slate-800 font-bold mb-5">Account Details</h2>
                  <!--
                  <section>
                    <h3 class="text-xl leading-snug text-slate-800 font-bold mb-1">Organization Image</h3>
                    <div class="flex items-center">
                      <div class="mr-4">
                        <img class="w-20 h-20 rounded-full" src="../../images/user-avatar-80.png" width="80" height="80" alt="User upload" />
                      </div>
                      <button class="btn-sm bg-indigo-500 hover:bg-indigo-600 text-white">Change</button>
                    </div>
                  </section>
                  -->
                  <section>
                    <div class="sm:flex sm:items-center space-y-4 sm:space-y-0 sm:space-x-4 mt-5">
                      <div class="sm:w-1/3">
                        <label class="block text-sm font-medium mb-1" for="name" >Account Name</label>
                        <input id="name" class="form-input w-full" type="text" v-model="orgName" />
                      </div>
                    </div>
                  </section>

				  <section>
					<div class="sm:flex sm:items-center space-y-4 sm:space-y-0 sm:space-x-4 mt-5">
					<div class="sm:w-1/2">
						<label class="block text-sm font-medium mb-1" for="name" >Account ID</label>
						<div class="text-sm"> AccountID is used in the Raleon Snippet and Raleon APIs.</div>
						<div class="text-xs font-bold p-4 text-gray-600 bg-gray-200 inline-block rounded-lg mt-2">ID: {{orgId}}</div>
					</div>
					</div>
				</section>

				<section>
					<div class="sm:flex sm:items-center space-y-4 sm:space-y-0 sm:space-x-4 mt-5">
                      <div class="sm:w-1/3">
                        <label class="block text-sm font-medium mb-1" for="name" >Org Profile Pic</label>
						<div class="text-sm"> Profile picture is used in quest chats.</div>
                        <img v-if="profilePicUrl" :src="profilePicUrl" style="max-width: 50px; max-height: 50px; border-radius: 100% ;"/>
						<a v-if="profilePicUrl" @click="profilePicUrl = ''" class="ml-4" style="text-decoration: underline; cursor: pointer;">
							Replace
						</a>
						<form v-if="!profilePicUrl">
							<input
								type="file"
								class="form-input w-full"
								:placeholder="`Enter URL or upload image`"
								@change="uploadFile($event, '/chat-graph/image').then(url => this.profilePicUrl = url);"
							/>
							<i>For best results, upload a 256x256 image</i>
						</form>
                      </div>
                    </div>
				</section>
				<section>
                    <div class="sm:flex sm:items-center space-y-4 sm:space-y-0 sm:space-x-4 mt-5">
                      <div class="sm:w-1/3">
                        <label class="block text-sm font-medium mb-1" for="name" >Default Quest Album Art</label>
                        <div v-if="questAlbumArtUrl" style="height: 256px; width: 180px; overflow: hidden; background-size: cover; background-position: center; border-radius: 25px;" :style='{ "background-image": `url(${this.questAlbumArtUrl})`}'>
							<!-- <img :src="questAlbumArtUrl" /> -->
						</div>
						<a v-if="questAlbumArtUrl" @click="questAlbumArtUrl = ''" class="ml-4" style="text-decoration: underline; cursor: pointer;">
							Replace
						</a>
						<form v-if="!questAlbumArtUrl">
							<input
								type="file"
								class="form-input w-full"
								:placeholder="`Enter URL or upload image`"
								@change="uploadFile($event, `/images/campaign/0/upload`).then(url => this.questAlbumArtUrl = url);"
							/>
						</form>
                      </div>
                    </div>
				</section>
                <section>
                    <div class="sm:flex sm:items-center space-y-4 sm:space-y-0 sm:space-x-4 mt-5">
                      <div class="sm:w-1/3">
                        <label class="block text-sm font-medium mb-1" for="name" >Quest Bot Name</label>
						<div class="text-sm"> Name displayed as the bot in the quest chats.</div>
                        <input id="botName" class="form-input w-full" type="text" v-model="questBotName" :placeholder="orgName"/>
                      </div>
                    </div>
                </section>
			</div>
                <!-- Panel footer -->
                <footer>
                  <div class="flex flex-col px-6 py-5 border-t border-slate-200">
                    <div class="flex self-end">
                      <button class="btn border-slate-200 hover:border-slate-300 text-slate-600">Cancel</button>
                      <button class="btn bg-indigo-500 hover:bg-indigo-600 text-white ml-3" @click="updateOrgName()">Save Changes</button>
                    </div>
                  </div>
                </footer>
              </div>
            </div>
          </div>

        </div>
      </main>
    </div>

  </div>
</template>

<script>
import { ref } from 'vue'
import Sidebar from '../../partials/Sidebar.vue'
import Header from '../../partials/Header.vue'
import SettingsSidebar from '../../partials/settings/SettingsSidebar.vue'
import AccountPanel from '../../partials/settings/AccountPanel.vue'
import ToastStatus from '../component/ToastStatus.vue'
import { updateOrgById, getOrgById } from '../../services/organization';
import * as Utils from '../../utils/Utils';
const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
  name: 'Account',
  components: {
    Sidebar,
    Header,
    SettingsSidebar,
    AccountPanel,
    ToastStatus
  },
  setup() {

    const sidebarOpen = ref(false)
	const orgId = 0;

    return {
      sidebarOpen,
	  orgId
    }
  },
  data() {
    return {
      orgName: '',
      statusText: '',
      status: 'off',
	  profilePicUrl: '',
	  questAlbumArtUrl: '',
	  questBotName: '',
    }
  },
  methods: {
    clearStatus() {
      this.status = '';
    },
	logout() {
      localStorage.removeItem('token');
    },
    async updateOrgName() {
      let result = await updateOrgById(localStorage.getItem("userOrgId"), this.orgName, this.profilePicUrl, this.questAlbumArtUrl, this.questBotName);

      if(result != null) {
        this.status = 'success';
        this.statusText = 'Organization name updated!';
      }
      else {
        this.status = 'fail';
        this.statusText = 'We could not update your organization name.';
      }
    },
	async uploadFile(event, endpointPart) {
		const file = event.target.files[0];
		if (!endpointPart) {
			throw new Error('No endpoint to upload file to');
		}

		const endpoint = endpointPart.startsWith('/')
			? `${URL_DOMAIN}${endpointPart}`
			: endpointPart;

		const formData = new FormData();
		formData.append('file', file);

		const response = await fetch(endpoint, {
			method: 'POST',
			credentials: 'include',
			headers: {
				Authorization: endpoint.startsWith(URL_DOMAIN) ? `Bearer ${localStorage.token}` : undefined
			},
			body: formData
		});
		const result = await response.text();

		return result;
	},
  },
  async mounted() {
    let result = await getOrgById(localStorage.getItem("userOrgId"));
    this.orgName = result.name;
	this.profilePicUrl = result.profilePicUrl;
	this.questAlbumArtUrl = result.questAlbumArtUrl;
	this.questBotName = result.questBotName;
	this.orgId = localStorage.getItem("userOrgId");
  }
}
</script>
