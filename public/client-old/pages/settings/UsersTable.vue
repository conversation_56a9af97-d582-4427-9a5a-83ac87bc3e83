<template>
	<tr>
		<td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
			<div class="flex">
				{{user.email}}
		 	</div>
		</td>
		<td class="px-2 py-3 whitespace-nowrap">
			<div class="text-left">{{user.name}}</div>
		</td>
		<td class="px-2 py-3 whitespace-nowrap">
			<div class="inline-flex font-medium bg-emerald-500 text-white rounded-full text-center px-2.5 py-0.5" v-if="user.status == 'Active'">Active</div>
			<div class="inline-flex font-medium bg-amber-100 text-amber-600 rounded-full text-center px-2.5 py-0.5" v-if="user.status == 'Pending'">Pending</div>
			<div class="inline-flex font-medium bg-rose-600 text-white rounded-full text-center px-2.5 py-0.5" v-if="user.status == 'Expired'">Expired</div>
			<DropdownEditMenu v-if="user.status != 'Active'" :align="'right'" class="float-right inline-flex relative mr-3">
				<li @click="resendInvite">
					<a
						class="font-medium text-sm text-slate-600 hover:text-slate-800 hover:cursor-pointer flex py-1 px-3">
						Resend Invite
					</a>
				</li>
			</DropdownEditMenu>
		</td>
	</tr>
</template>

<script>
import DropdownEditMenu from '../../components/DropdownEditMenu.vue'
import { resendInvite } from '../../services/invite';
export default {
	name: 'UsersTable',
	props: ['user'],
	emits: ['invites-sent', 'invites-failed'],
	components: {
		DropdownEditMenu
	},
	computed: {},
	methods: {
		async resendInvite() {
			try {
				await resendInvite(this.user.email);
				this.$emit('invites-sent', 'Invitation resent');
			} catch(e) {
				this.$emit('invites-failed', e);
			}

		}
	},
}
</script>
