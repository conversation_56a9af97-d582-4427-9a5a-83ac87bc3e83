<template>
  <div class="flex h-screen overflow-hidden bg-gray-50">
    <!-- Content area -->
    <div class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
      <ToastStatus :status="status" :text="statusText" @clear-status="clearStatus()" />

      <main>
        <div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">

          <!-- Page header -->
          <div class="mb-8 flex items-center justify-between">
            <h1 class="text-3xl md:text-4xl text-gray-900 font-bold bg-gradient-to-r from-purple-600 to-purple-700 bg-clip-text text-transparent">Settings</h1>
            <button class="px-4 py-2 border border-gray-200 text-gray-600 font-medium rounded-xl hover:bg-gray-50 hover:border-gray-300 transition-all duration-200">
              <router-link to="/signin" @click="logout()" class="flex items-center gap-2">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
                Sign Out
              </router-link>
            </button>
          </div>

          <!-- Content -->
          <div class="bg-white shadow-xl rounded-2xl mb-8 border border-gray-100">
            <div class="flex flex-col md:flex-row md:-mr-px">
              <SettingsSidebar />

              <div class="grow">
                <!-- Panel body -->
                <div class="p-8 space-y-8">
                  <div class="mb-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">My Profile</h2>
                    <p class="text-gray-600">Manage your personal account information and preferences.</p>
                  </div>

                  <!-- Name Section -->
                  <section>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2" for="firstName">First Name</label>
                        <input 
                          id="firstName" 
                          class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors duration-200" 
                          type="text" 
                          v-model="firstName" 
                          placeholder="Enter your first name"
                        />
                      </div>
                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2" for="lastName">Last Name</label>
                        <input 
                          id="lastName" 
                          class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors duration-200" 
                          type="text" 
                          v-model="lastName" 
                          placeholder="Enter your last name"
                        />
                      </div>
                    </div>
                  </section>

                  <!-- Email Section -->
                  <section>
                    <div class="border border-gray-200 rounded-xl p-6 bg-gray-50">
                      <div class="flex items-center gap-3 mb-2">
                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                        <h3 class="text-lg font-semibold text-gray-900">Email Address</h3>
                      </div>
                      <p class="text-gray-600 mb-3">Your email address is used for account access and notifications.</p>
                      <div class="bg-white px-4 py-3 rounded-lg border border-gray-200">
                        <span class="text-gray-900 font-medium">{{ userEmail }}</span>
                      </div>
                    </div>
                  </section>

                  <!-- Future Password Section (commented out but styled for consistency) -->
                  <!--
                  <section>
                    <div class="border border-gray-200 rounded-xl p-6">
                      <div class="flex items-center gap-3 mb-2">
                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                        </svg>
                        <h3 class="text-lg font-semibold text-gray-900">Password</h3>
                      </div>
                      <p class="text-gray-600 mb-4">You can set a permanent password if you don't want to use temporary login codes.</p>
                      <button class="px-4 py-2 border border-purple-200 text-purple-600 rounded-lg hover:bg-purple-50 transition-colors duration-200">
                        Set New Password
                      </button>
                    </div>
                  </section>
                  -->

                </div>
                <footer class="border-t border-gray-100 bg-gray-50/50 rounded-b-2xl">
                  <div class="flex flex-col px-8 py-6">
                    <div class="flex justify-end gap-4">
                      <button class="px-6 py-3 border border-gray-200 text-gray-600 font-medium rounded-xl hover:bg-gray-50 hover:border-gray-300 transition-all duration-200">
                        Cancel
                      </button>
                      <button 
                        @click="updateName()"
                        class="px-6 py-3 bg-gradient-to-r from-purple-600 to-purple-700 text-white font-medium rounded-xl hover:from-purple-700 hover:to-purple-800 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-all duration-200"
                      >
                        Save Changes
                      </button>
                    </div>
                  </div>
                </footer>
              </div>
            </div>
          </div>

        </div>
      </main>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import Sidebar from '../../partials/Sidebar.vue'
import Header from '../../partials/Header.vue'
import SettingsSidebar from '../../partials/settings/SettingsSidebar.vue'
import AccountPanel from '../../partials/settings/AccountPanel.vue'
import { updateUserFLName } from '../../services/user';
import ToastStatus from '../component/ToastStatus.vue'
import { useSessionStore } from '../../../client/pages/useSessionStore.ts';

export default {
  name: 'Account',
  components: {
    Sidebar,
    Header,
    SettingsSidebar,
    AccountPanel,
    ToastStatus
  },
  setup() {

    const sidebarOpen = ref(false)

    return {
      sidebarOpen,
    }
  },
  computed: {
    userEmail() {
        return localStorage.getItem("email");
    }
  },
  data() {
	const sessionStore = useSessionStore();
    return {
      firstName: localStorage.getItem("firstName"),
      lastName: localStorage.getItem("lastName"),
      status: 'off',
      statusText: '',
	    sessionStore,
    }
  },
  methods: {
    clearStatus() {
      this.status = '';
    },
	logout() {
      this.sessionStore.logOut();
      localStorage.removeItem('token');
    },
    async updateName() {
      var user = JSON.parse(localStorage.getItem("userInfo"));
      console.log("We have user information" + user)
      console.log("user id is " + user.id)
      let r = await updateUserFLName(
        user.id,
        this.firstName,
        this.lastName
      );
      localStorage.setItem(
          'firstName',
          this.firstName != null ? this.firstName : 'Profile',
      );
      localStorage.setItem(
          'lastName',
          this.lastName != null ? this.lastName : 'Profile',
      );

      if(r != null) {
        this.status = 'success';
        this.statusText = 'Your name was updated!';
      }
      else {
        this.status = 'fail';
        this.statusText = 'We were unable to update your name.'
      }
    }
  }
}
</script>
