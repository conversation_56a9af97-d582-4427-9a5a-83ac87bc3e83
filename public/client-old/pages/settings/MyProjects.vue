<template>
	<div class="flex h-screen overflow-hidden">

	  <!-- Sidebar -->
	  <Sidebar :sidebarOpen="sidebarOpen" @close-sidebar="sidebarOpen = false" />

	  <!-- Content area -->
	  <div class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
	  <ToastStatus :status="status" :text="statusText" @clear-status="clearStatus()" />
		<!-- Site header -->


		<main>
		  <div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">

			<!-- Page header -->
			<div class="mb-8 flex">
			  <!-- Title -->
			  <h1 class="text-2xl md:text-3xl text-slate-800 font-bold">Settings</h1>
			  <button class="ml-auto btn border-slate-200 hover:border-slate-300 text-slate-600">
				  <router-link to="/signin" @click="logout()">
				  Sign Out
				  </router-link>
			  </button>
			</div>

			<!-- Content -->
			<div class="bg-white shadow-lg rounded-sm mb-8">
			  <div class="flex flex-col md:flex-row md:-mr-px">
				<SettingsSidebar />

				<div class="grow">
				  <!-- Panel body -->
				  <div class="p-6 space-y-6">
					<h2 class="relative">
						<div class="text-2xl text-slate-800 font-bold mb-5">My Projects</div>
						<button
							class="btn bg-indigo-500 hover:bg-indigo-600 text-white absolute float-right top-0 right-0"
							@click.stop="showInviteUsersModal = true">
							<span @click="newProjectName = ''; isNameModalOpen = true;" class="hidden xs:block">New Project</span>
						</button>
					</h2>
					 <section>
						<div class="bg-white shadow-lg rounded-sm border border-slate-200 relative">
							<div class="rounded-sm border border-slate-200">
								<div class="overflow-x-auto">
									<table class="table-fixed w-full divide-y divide-slate-200">
										<thead class="text-xs font-semibold uppercase text-slate-500 bg-slate-50">
											<tr>
												<th class="px-2 last:pr-5 py-3"><div class="text-left">Name</div></th>
												<th class="px-2 py-3"><div class="text-left">Login</div></th>
												<th class="px-2 py-3"><div class="text-left">Leave</div></th>
											</tr>
										</thead>
										<tbody class="text-sm">
											<tr v-for="org in userOrgs">
												<td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
													<div class="flex">
														{{org.name}}
													</div>
												</td>
												<td class="px-2 py-3 whitespace-nowrap">
													<i v-if="org.id == userOrgId">This Project</i>
													<button
														v-if="org.id != userOrgId"
														class="btn bg-indigo-500 hover:bg-indigo-600 text-white"
														@click.stop="loginToOrg(org)">
														<span class="hidden xs:block">Login</span>
													</button>
												</td>
												<td class="px-2 py-3 whitespace-nowrap">
													<i v-if="org.id != userOrgId">Login first to leave</i>
													<button
														v-if="org.id == userOrgId && userOrgs.length > 1"
														class="btn my-auto ml-4 border-slate-200 hover:border-slate-300"
														@click.stop="leaveOrg(org)">
														<span class="hidden xs:block">Leave Project</span>
													</button>
													<i v-if="userOrgs.length == 1">Unable to leave only project</i>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					  </section>
					  <!-- Password -->
					  <!--
					  <section>
						<h3 class="text-xl leading-snug text-slate-800 font-bold mb-1">Password</h3>
						<div class="text-sm">You can set a permanent password if you don't want to use temporary login codes.</div>
						<div class="mt-5">
						  <button class="btn border-slate-200 shadow-sm text-indigo-500">Set New Password</button>
						</div>
					  </section>
					-->

				  </div>
				</div>
			  </div>
			</div>

		  </div>
		</main>
	  </div>
	</div>

	<ModalBasic id="name-modal" :fitContent="false" :modalOpen="isNameModalOpen" @close-modal="isNameModalOpen = false" title="New Project">
		<div class="flex flex-col">
			<!-- Modal content -->
			<div class="px-5 py-4 flex-grow">
				<p style="font-size: 1.25em; ">Enter a name for your new project</p>


				<div class="p-4 flex-grow">
					<span class="mr-2">Name</span>
					<!-- <div class="flex items-center"> -->
						<input type="text"
						class="text-lg bg-white outline-0 outline-none italic focus:outline-0 focus:text-slate-600 focus:font-normal p-2 rounded-md hover:bg-slate-200 transition-colors duration-500"
						v-model="newProjectName" />
					<!-- </div> -->
				</div>


			</div>
			<!-- Modal footer -->
			<div class="px-5 py-4 border-t border-slate-200">
			<div class="flex flex-wrap justify-end space-x-2">
				<button class="btn-sm border-slate-200 hover:border-slate-300 text-slate-600" @click.stop="isNameModalOpen = false">Cancel</button>
                <button class="btn-sm bg-indigo-500 hover:bg-indigo-600 text-white" @click.stop="createNewProject()">Create</button>
			</div>
			</div>
		</div>
	</ModalBasic>
  </template>

  <script>
  import { ref } from 'vue'
  import Sidebar from '../../partials/Sidebar.vue'
  import Header from '../../partials/Header.vue'
  import SettingsSidebar from '../../partials/settings/SettingsSidebar.vue'
  import ModalBasic from '../../components/ModalBasic.vue'
  import AccountPanel from '../../partials/settings/AccountPanel.vue'
  import { updateUserFLName } from '../../services/user';
  import ToastStatus from '../component/ToastStatus.vue'
import { URL_DOMAIN } from '../../utils/Utils'
import * as userService from '../../services/user.js'

  export default {
	name: 'Account',
	components: {
	  Sidebar,
	  Header,
	  SettingsSidebar,
	  AccountPanel,
	  ToastStatus,
	  ModalBasic
	},
	setup() {

	  const sidebarOpen = ref(false)

	  return {
		sidebarOpen,
	  }
	},
	computed: {
	  userEmail() {
		  return localStorage.getItem("email");
	  },
	  userOrgId() {
		return localStorage.getItem("userOrgId");
	  }
	},
	data() {
	  return {
		userOrgs: [],
		isNameModalOpen: false,
		newProjectName: '',
	  }
	},
	methods: {
		async loginToOrg(org) {
			const loginRequest = await fetch(`${URL_DOMAIN}/users/login/${org.id}`, {
				method: 'POST',
				credentials: 'omit', // include, *same-origin, omit
				headers: {
					Authorization: `Bearer ${localStorage.getItem('token')}`,
					'Content-Type': 'application/json',
				}
			});
			const loginResult = await loginRequest.json();
			console.log(loginResult);
			if (loginResult.token) {
				localStorage.setItem('token', loginResult.token);
				let userInfo = await userService.getUserInfo();
				await userService.setUserInfoSignin(userInfo);
			} else {
				this.loginError = true;
			}
			location.reload();
		},
		async leaveOrg(org) {
			const leaveRequest = await fetch(`${URL_DOMAIN}/user`, {
				method: 'DELETE',
				credentials: 'omit', // include, *same-origin, omit
				headers: {
					Authorization: `Bearer ${localStorage.getItem('token')}`,
					'Content-Type': 'application/json',
				}
			});
			const leaveResult = await leaveRequest.json();
			console.log(leaveResult);
			if (leaveResult.token) {
				localStorage.setItem('token', leaveResult.token);
				let userInfo = await userService.getUserInfo();
				await userService.setUserInfoSignin(userInfo);
			} else {
				this.leaveError = true;
			}
			location.reload();
		},
		async createNewProject() {
			const newProjectRequest = await fetch(`${URL_DOMAIN}/onboard/self-service-additional`, {
				method: 'POST',
				credentials: 'omit', // include, *same-origin, omit
				headers: {
					Authorization: `Bearer ${localStorage.getItem('token')}`,
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					"organizationName": this.newProjectName
				})
			});

			location.reload();

		}
	},
	async mounted() {

		const userOrgsRequest = await fetch(`${URL_DOMAIN}/user/orgs`, {
			method: 'GET',
			credentials: 'omit', // include, *same-origin, omit
			headers: {
				Authorization: `Bearer ${localStorage.getItem('token')}`,
				'Content-Type': 'application/json',
			}
		});
		this.userOrgs = await userOrgsRequest.json();
	}
  }
  </script>
