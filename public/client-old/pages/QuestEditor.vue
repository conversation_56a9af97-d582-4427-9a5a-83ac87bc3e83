<template>
	<div class="flex h-screen overflow-hidden bg-white">
		<Sidebar :sidebarOpen="sidebarOpen" @close-sidebar="sidebarOpen = false" />

		<!-- Empty State -->
		<div class="m-6" v-if="templateSelection == '[]' && !isLoading && templateMode">
			<div class="text-left">
				<h1 class="text-xl font-bold mb-4">Create quest from a template</h1>
				<p class="text-md text-gray-600">Using Raleon team's expertise and common trends, we've pre-created quests ready for your use. Looking for a more personalized quest? We're just a message away and ready to assist.</p>
			</div>

			<div class="mt-8" v-for="template in templates">
				<div
					:class="{ 'border-3': template.emphasized, 'border-ralprimary-main': template.emphasized, 'border-ralprimary-ultralight': !template.emphasized }"
					class="relative overflow-hidden rounded-lg border shadow-md hover:cursor-pointer hover:shadow-2xl transition duration-500 flex items-center group mt-4"
					@click.stop="useTemplate(template)"
				>
					<div class="flex-none p-10 pr-4">
						<svg xmlns="http://www.w3.org/2000/svg" width="87" height="75" viewBox="0 0 37 32" fill="none">
<rect width="37" height="32" rx="8" :fill="template.svgFill" />
<path :d="template.svgPath" fill="white"/>
</svg>
					</div>
					<div class="p-6 flex-grow">
						<h2 class="mb-2 font-semibold">{{ template.name }}</h2>
						<p class="text-sm text-gray-600 mb-4">{{ template.description }}</p>
						<p class="text-xs text-yellow-700 italic mb-2">{{ template.info }}</p>
						<div class="text-xs bg-ralinfo-light text-ralinfo-dark font-semibold py-1 px-4 rounded-full inline-block lowercase mr-2" v-for="tag in template.tags">
							{{ tag }}
						</div>
					</div>
				</div>

			</div>

			<!-- <div class="text-center px-4 mt-8">
				<button class="
			btn-xs
			border-slate-200
			hover:border-ralgranite-300
			text-ralgranite-300
			hover:text-ralgranite-400
			bg-white
			ml-4
			" @click.stop="useTemplateModelOpen = true">
					Import a Dashboard
				</button>
			</div> -->
		</div>
		<!-- End -->

		<div class="scroll-container relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden" v-if="templateSelection !== '[]' || !templateMode">
			<main>

				<div class="flex flex-row snippet-banner bg-ralprimary-main justify-center items-center" v-if="isSnippetWarningVisible">
					<h1 class="mr-4">Snippet May Not Be Installed</h1>
					<p class="mr-4">Snippet must be installed for this Quest to be visible on your site</p>
					<a class="text-white" href="/snippet">Verify Now</a>

				</div>

				<div class="px-6 sm:px-6 lg:px-12 py-8 w-full max-w-9xl mx-auto">

					<!-- Page header -->
					<div class="flex sm:justify-between sm:items-center mb-4">
						<h1
							class="text-2xl md:text-3xl text-ralblack-primary font-bold"
							data-cy="campaign-name"
						>
							<input class="text-2xl md:text-3xl font-bold bg-white border-0 outline-0 outline-none p-2 rounded-md hover:bg-slate-200 transition-colors duration-500" v-model="questName"
								:class="{ 'text-slate-800 border-none outline-0 bg-slate-200': !questName }"
								@focus="isEditingTitle = true"
								placeholder="Quest Name"
								@blur="isEditingTitle = false"
								@change="dirty = true"
								/>
						</h1>
						<button v-if="campaignStatus" class="cursor-text mt-1 ml-12 rounded-md py-1 px-4 text-sm uppercase"
							:class="{
									'bg-ralgray-light': campaignStatus === 'Draft' || campaignStatus === 'Quest Not Saved',
									'text-ralblack-primary': campaignStatus === 'Draft' || campaignStatus === 'Quest Not Saved',
									'bg-raltable-ring-secondary': campaignStatus === 'Running',
									'text-ralprimary-dark': campaignStatus === 'Running',
									'bg-ralsuccess-light': campaignStatus === 'Completed',
									'text-ralsuccess-dark': campaignStatus === 'Completed',
								}">
							{{ campaignStatus }}
						</button>
						<div class="flex-grow"></div>
						<a class="mr-2" target="_blank" href="https://docs.raleon.io/docs/quest-editor" style="text-decoration: underline; cursor: pointer;">
							Help
						</a>
						<div class="flex">
							<button
								class="bg-gradient-button-primary text-white font-bold text-xs py-2 px-4 ml-2 rounded-button"
								:style="{ 'background': saving || !dirty ? 'lightgrey' : '' }"
								@click="saveQuest(true)"
								data-cy="edit-campaign"
								:disabled="saving || !dirty">
								Save and Preview
							</button>

							<button
								class="bg-gradient-button-primary text-white font-bold text-xs py-2 px-4 ml-2 rounded-button"
								:style="{ 'background': saving || (!dirty && questStartDate && questEndDate) ? 'lightgrey' : '' }"
								@click="publishQuest()"
								data-cy="edit-campaign"
								:disabled="saving || (!dirty && questStartDate && questEndDate)">
								Publish
							</button>
						</div>
					</div>
					<div class="p-5" v-if="saveFailure">
						<div class="p-5 flex flex-row" style="background-color: rgb(243, 76, 76); color: white; justify-content: space-between; align-items: center;">
							<span style="font-weight: bold; font-size: 1.5em; ">Quest Save Failed</span>
							<a href="#" @click="isRescueModalOpen = true">View/Copy Quest Script</a>
						</div>
					</div>
					<div class="border-b border-ralwhite-line"></div>

					<div class="flex rounded-md bg-white border border-ralprimary-ultralight mt-8 hover:shadow-xl transition duration-500">
						<div class="cursor-move w-4"></div>

						<div class="flex flex-col w-full">
							<h3 class="p-4 text-xl font-bold">Quest Settings</h3>
							<transition name="expand">
								<div class="expandable"  :class="{ 'expand-enter-active': isSettingsExpanded }">
									<div class="p-4 flex-grow" v-if="id">
										<span class="mr-2">Campaign Id</span>
										<!-- <div class="flex items-center"> -->
											<input type="text"
											placeholder="Enter Quest Narrator Name..."
											class="text-lg bg-white border-0 outline-0 outline-none italic focus:outline-0 focus:text-slate-600 focus:font-normal p-2 rounded-md hover:bg-slate-200 transition-colors duration-500"
											v-model="id"
											disabled
											/>
										<!-- </div> -->
									</div>
									<div class="p-4 flex-grow" v-if="questId">
										<span class="mr-2">Quest Id</span>
										<!-- <div class="flex items-center"> -->
											<input type="text"
											placeholder="Enter Quest Narrator Name..."
											class="text-lg bg-white border-0 outline-0 outline-none italic focus:outline-0 focus:text-slate-600 focus:font-normal p-2 rounded-md hover:bg-slate-200 transition-colors duration-500"
											v-model="questId"
											disabled
											/>
										<!-- </div> -->
									</div>
									<div class="p-4 flex-grow">
										<span class="mr-2">Quest Category</span>
										<!-- <div class="flex items-center"> -->
											<input type="text"
											@change="dirty = true"
											placeholder="Enter Category... (optional)"
											class="text-lg bg-white border-0 outline-0 outline-none italic focus:outline-0 focus:text-slate-600 focus:font-normal p-2 rounded-md hover:bg-slate-200 transition-colors duration-500"
											v-model="questCategory" />
										<!-- </div> -->
									</div>
									<div class="p-4 flex-grow" >
										<span class="mr-2">Quest Description</span>
										<!-- <div class="flex items-center"> -->
											<input type="text"
											@change="dirty = true"
											placeholder="Enter Description... (optional)"
											class="text-lg bg-white border-0 outline-0 outline-none italic focus:outline-0 focus:text-slate-600 focus:font-normal p-2 rounded-md hover:bg-slate-200 transition-colors duration-500"
											v-model="questDescription" />
										<!-- </div> -->
									</div>
								</div>
							</transition>
							<div class="p-4 flex-grow">
								<span class="mr-2">Audience</span>
								<select class="rounded-md mr-2" v-model="selectedSegment" :disabled="isSelfService" @change="dirty = true">
									<option v-for="segment in segments" :key="segment.id" :value="segment.id">
										{{ segment.name }}
									</option>
								</select>

								<button
									v-if="isSelfService"
									id="upgrade-button-1"
									class="btn bg-indigo-500 hover:bg-indigo-600 text-white disabled:bg-indigo-600 disabled:text-slate-400 disabled:cursor-not-allowed disabled"
									@click="requestUpgrade()"
								>
									<span class="hidden xs:block">Upgrade</span>
								</button>
							</div>

							<transition name="expand">
								<div class="expandable"  :class="{ 'expand-enter-active': isSettingsExpanded }">
									<div class="p-4 flex-grow" >
										<span class="mr-2">Quest Priority</span>
										<!-- <div class="flex items-center"> -->
											<input type="text"
											@change="dirty = true"
											placeholder="Enter Priority..."
											class="text-lg bg-white border-0 outline-0 outline-none italic focus:outline-0 focus:text-slate-600 focus:font-normal p-2 rounded-md hover:bg-slate-200 transition-colors duration-500"
											v-model="questPriority" />
										<!-- </div> -->
									</div>
									<div class="p-4 flex-grow flex flex-row items-center" >
										<span class="mr-2">Quest Profile Pic</span>
										<img v-if="questProfilePicUrl" :src="questProfilePicUrl" style="max-width: 50px; max-height: 50px; border-radius: 100% ;"/>
										<a v-if="questProfilePicUrl" @click="questProfilePicUrl = ''" class="ml-4" style="text-decoration: underline; cursor: pointer;">
											Replace
										</a>
										<form v-if="!questProfilePicUrl">
											<input
												type="file"
												class="form-input w-full"
												:placeholder="`Enter URL or upload image`"
												@change="uploadFile($event, '/chat-graph/image').then(url => this.questProfilePicUrl = url); dirty=true"
											/>
											<i>For best results, upload a 256x256 image</i>
										</form>
									</div>
									<div class="p-4 flex-grow flex flex-row items-center" >
										<span class="mr-2">Quest Album Art</span>
										<div v-if="questAlbumArtUrl" style="height: 256px; width: 180px; overflow: hidden; background-size: cover; background-position: center; border-radius: 25px;" :style='{ "background-image": `url(${this.questAlbumArtUrl})`}'>
											<!-- <img :src="questAlbumArtUrl" /> -->
										</div>
										<a v-if="questAlbumArtUrl" @click="questAlbumArtUrl = ''" class="ml-4" style="text-decoration: underline; cursor: pointer;">
											Replace
										</a>
										<form v-if="!questAlbumArtUrl">
											<input
												type="file"
												class="form-input w-full"
												:placeholder="`Enter URL or upload image`"
												:disabled="!this.id"
												@change="uploadFile($event, `/images/campaign/${this.id}/upload`).then(url => this.questAlbumArtUrl = url); dirty=true"
											/>
										</form>
									</div>
								</div>
							</transition>

							<div class="p-4 flex-grow">
								<span class="mr-2">Start Date</span>
								<!-- <div class="flex items-center"> -->
									<input type="date"
									@change="dirty = true"
									class="text-lg bg-white border-0 outline-0 outline-none italic focus:outline-0 focus:text-slate-600 focus:font-normal p-2 rounded-md hover:bg-slate-200 transition-colors duration-500"
									v-model="questStartDate" />
								<!-- </div> -->
							</div>
							<div class="p-4 flex-grow">
								<span class="mr-2">End Date</span>
								<!-- <div class="flex items-center"> -->
									<input type="date"
									@change="dirty = true"
									class="text-lg bg-white border-0 outline-0 outline-none italic focus:outline-0 focus:text-slate-600 focus:font-normal p-2 rounded-md hover:bg-slate-200 transition-colors duration-500"
									v-model="questEndDate" />
								<!-- </div> -->
							</div>

							<transition name="expand">
								<div class="expandable"  :class="{ 'expand-enter-active': isSettingsExpanded }">
									<div class="p-4 flex-grow" >
										<span class="mr-2">Visibility</span>
										<select class="rounded-md" v-model="questVisibility" @change="dirty = true">
											<option :key="'always'" :value="'always'">Always Visible In Quests List</option>
											<option :key="'complete'" :value="'complete'">Visible In Quests List Upon Completion</option>
										</select>
									</div>
									<div class="p-4 flex-grow">
										<span class="mr-2">Bot Name</span>
										<!-- <div class="flex items-center"> -->
											<input type="text"
											placeholder="Enter Quest Bot Name..."
											class="text-lg bg-white border-0 outline-0 outline-none italic focus:outline-0 focus:text-slate-600 focus:font-normal p-2 rounded-md hover:bg-slate-200 transition-colors duration-500"
											v-model="questSender"
											@blur="jimmy(); dirty = true" />
										<!-- </div> -->
									</div>
								</div>
							</transition>

							<button @click="isSettingsExpanded = !isSettingsExpanded" class="py-2 m-4 mr-6 px-4 border border-transparent text-sm font-medium rounded-md text-indigo-600 bg-white border-indigo-600 hover:bg-indigo-700 hover:text-white flex flex-row justify-center">
								<svg class="expand-icon" :class="{ rotate: isSettingsExpanded }"  xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
									<polyline points="6 9 12 15 18 9"></polyline>
								</svg>

								{{ isSettingsExpanded ? 'Collapse' : 'Advanced Settings' }}
							</button>
						</div>


					</div>

					<span v-for="(item, index) in compList">
						<div
							v-if="index && isSequential(index)"
							style="cursor: row-resize;"
							class="flex items-center bg-white border-2 border-ralprimary-ultralight hover:border-ralprimary-main justify-center mt-8 border-dashed rounded-md py-4 px-8 m-4 cursor-pointer text-sm text-center text-ralgray-light hover:text-ralgray-main transition-colors duration-500"
							@click.stop="addCommandBetween(null, index - 1, index)"
						>
								Insert Component Here
						</div>

						<component
						:is="item.component"
						:key="item.props.compId"
						v-bind="item.props"
						@updateDetails="updateCommandInfo"
						@updateType="updateComponentType"
						@deleteRequested="deleteComponent(index)"
						ref="components"
						></component>

					</span>

					<div
						class="flex items-center bg-white border-2 border-ralprimary-ultralight hover:border-ralprimary-main justify-center mt-8 border-dashed rounded-md py-4 px-8 m-4 cursor-pointer text-sm text-center text-ralgray-light hover:text-ralgray-main transition-colors duration-500"
						@click.stop="addCommand(null)"
					>
							Add Component
					</div>
				</div>
			</main>

		</div>
	</div>

	<iframe class="playground" style="position: absolute; left: 20px; bottom: 0; width: 397px; height: 500px;"></iframe>


	<ModalBasic id="rescue-modal" :fitContent="true" :modalOpen="isRescueModalOpen" @close-modal="isRescueModalOpen = false" title="Quest Recovery">
		<div class="flex flex-col">
            <!-- Modal content -->
            <div class="px-5 py-4 flex-grow" style="min-width: 80vw; min-height: 80vh">

			<p style="font-size: 1.5em; ">Quest save has not completed, possibly due to temporary failure or because of invalid configuration.</p>
			<p style="font-size: 1.5em; font-weight: bold;">Copy the quest script below to keep it for re-import later:</p>
			<div id="quest-rescue-container">
				  <pre id="quest-rescue-script">{{ rescueScript }}</pre>
				  <button class="copy-btn" @click="copyToClipboard()">Copy to Clipboard</button>
			</div>
            </div>
            <!-- Modal footer -->
            <div class="px-5 py-4 border-t border-slate-200">
              <div class="flex flex-wrap justify-end space-x-2">
                <button class="btn-sm border-slate-200 hover:border-slate-300 text-slate-600" @click.stop="isRescueModalOpen = false">Close</button>
              </div>
            </div>
		</div>
	</ModalBasic>

	<ModalBasic id="publish-modal" :fitContent="true" :modalOpen="isPublishModalOpen" @close-modal="isPublishModalOpen = false" title="Publish Quest">
		<div class="flex flex-col">
			<!-- Modal content -->
			<div class="px-5 py-4 flex-grow">
				<p style="font-size: 1.5em; ">Select the start and end dates for the Quest to publish it.</p>


				<div class="p-4 flex-grow">
					<span class="mr-2">Start Date</span>
					<!-- <div class="flex items-center"> -->
						<input type="date"
						@change="dirty = true"
						class="text-lg bg-white border-0 outline-0 outline-none italic focus:outline-0 focus:text-slate-600 focus:font-normal p-2 rounded-md hover:bg-slate-200 transition-colors duration-500"
						v-model="questStartDate" />
					<!-- </div> -->
				</div>
				<div class="p-4 flex-grow">
					<span class="mr-2">End Date</span>
					<!-- <div class="flex items-center"> -->
						<input type="date"
						@change="dirty = true"
						class="text-lg bg-white border-0 outline-0 outline-none italic focus:outline-0 focus:text-slate-600 focus:font-normal p-2 rounded-md hover:bg-slate-200 transition-colors duration-500"
						v-model="questEndDate" />
					<!-- </div> -->
				</div>

			</div>
			<!-- Modal footer -->
			<div class="px-5 py-4 border-t border-slate-200">
			<div class="flex flex-wrap justify-end space-x-2">
				<button class="btn-sm border-slate-200 hover:border-slate-300 text-slate-600" @click.stop="isPublishModalOpen = false">Close</button>
                <button class="btn-sm bg-indigo-500 hover:bg-indigo-600 text-white" @click.stop="saveQuest()">Publish</button>
			</div>
			</div>
		</div>
	</ModalBasic>

	<ModalBasic id="welcome-modal" :fitContent="false" :modalOpen="isWelcomehModalOpen" @close-modal="isWelcomehModalOpen = false" title="Let’s get your first 🏆 Quest started!">
		<div class="flex flex-col">
			<!-- Modal content -->
			<div class="px-5 py-4 flex-grow">
				<p style="font-size: 1.25em; ">We’ve loaded the sections you need for your Quest. All you have to do is edit the sections, test to make sure it looks how you want, and publish!</p>


			</div>
			<!-- Modal footer -->
			<div class="px-5 py-4 border-t border-slate-200">
			<div class="flex flex-wrap justify-end space-x-2">
				<button class="btn-sm border-slate-200 hover:border-slate-300 text-slate-600" @click.stop="isWelcomehModalOpen = false">Close</button>
			</div>
			</div>
		</div>
	</ModalBasic>
</template>

<script>
	import { ref } from 'vue'
	import Sidebar from "../partials/Sidebar.vue"
	import * as Utils from '../utils/Utils';
	import AppMessageCommand from "./component/AppMessageCommand.vue"
	import UserMessageCommand from "./component/UserMessageCommand.vue"
	import MultipleChoiceCommand from "./component/ButtonCommand.vue"
	import ShowImageCommand from "./component/ShowImageCommand.vue"
	import EndQuestCommand from './component/EndQuestCommand.vue'
	import SendCustomEventCommand from './component/SendCustomEventCommand.vue'
	import AwardXPCommand from './component/AwardXPCommand.vue'
	import ShowQuestCommand from './component/ShowQuestCommand.vue'
	import HasEventCommand from './component/HasEventCommand.vue'
	import FetchQuestCommand from './component/FetchQuestCommand.vue'
	import TypingIndicatorCommand from './component/TypingIndicatorCommand.vue';
	import { useCampaignStore } from './campaign-builder/stores/campaign-store';
	import { getPublishedSegments } from '../services/segmentbuilder';
	import ModalBasic from '../components/ModalBasic.vue';
	import { getOrgById } from '../services/organization';

	const URL_DOMAIN = Utils.URL_DOMAIN;

	export default {
		name: 'QuestEditorPage',
		props: [],
		emits: [],
		components: {
			Sidebar,
			AppMessageCommand,
			MultipleChoiceCommand,
			UserMessageCommand,
			EndQuestCommand,
			SendCustomEventCommand,
			AwardXPCommand,
			ShowQuestCommand,
			HasEventCommand,
			FetchQuestCommand,
			TypingIndicatorCommand,
			ModalBasic
		},
		setup() {
			const sidebarOpen = ref(false);
			return {
				sidebarOpen,
			}
		},
		beforeRouteLeave(to, from, next) {
			if (this.dirty) {
				const go = confirm('Changes you made may not be saved.');
				if (go) {
					return next();
				}

				return next(false);
			}

			next();
		},
		data() {
				const endDate = new Date();
				endDate.setDate(endDate.getDate() + 30);

			return {
				isSettingsExpanded: false,
				isLoading: false,
				isSnippetWarningVisible: false,
				isWelcomehModalOpen: false,
				importScript: '',
				id: '',
				questId: '',
				isRescueModalOpen: '',
				isPublishModalOpen: false,
				saveFailure: false,
				rescueScript: '',
				dirty: false,
				questName: '',
				questCategory: '',
				questDescription: '',
				questVisibility: 'always',
				questPriority: 1,
				questStartDate: null, // new Date().toISOString().split('T')[0],
				questEndDate: null, // endDate.toISOString().split('T')[0],

				questActionsBlock: null,

				questSender: localStorage.getItem('orgName') || '',
				templates,

				questProfilePicUrl: '',
				selectedSegment: -1,
				segments: [],
				questAlbumArtUrl: '',
				templateSelection: '',
				comList: [],
				saving: false,
				compList: [],
				templateMode: false,
				campaignStore: null,
				commands: [
					{
						name: "App Message",
						value: "app-message",
					},
					{
						name: "Typing Indicator",
						value: "typing-indicator",
					},
					{
						name: "User Message",
						value: "user-message",
					},
					{
						name: "Multiple Choice",
						value: "simple-button"
					},
					{
						name: "Show Image",
						value: "show-image"
					},
					{
						name: "Save Event",
						value: "send-custom-event"
					},
					{
						name: "Check for Event",
						value: "has-custom-event"
					},
					{
						name: "Give Points",
						value: "award-xp"
					},
					{
						name: "Quest Actions",
						value: "show-quest-info"
					},
					{
						name: "End Quest",
						value: "end-quest"
					},
					{
						name: "Get Next Quest",
						value: "fetch-quest"
					}
				]
			}
		},
		async mounted() {
			const that = this;
			self.importQuestScript = function (...args) { that.importQuestScript(...args); };
			self.generateOutput = function (...args) { that.generateOutput(...args); };

			const _that = this;
			self.generateOutput = function () { that.generateOutput(); };

			const org = await getOrgById(localStorage.getItem("userOrgId"));

			this.questProfilePicUrl = this.questProfilePicUrl || org.profilePicUrl;
			this.questSender = this.questSender || org.questBotName;

			window.addEventListener('beforeunload', (event) => {
				if (this.dirty) {
					if (this.saving && !this.saveFailure) {
						this.rescueScript = this.generateOutput();
						this.isRescueModalOpen = true;
					}
					event.preventDefault();
					return (event.returnValue = "");
				}
			});

			this.isLoading = true;
			//amplitude.getInstance().logEvent('CAMPAIGN_ANALYTICS');

			this.campaignStore = useCampaignStore();
			const [segments] = await Promise.all([
				getPublishedSegments(),
				this.campaignStore.loadRewardTypes(),
				this.campaignStore.loadGoalTypes()
			]);

			this.segments = segments;
			const emptyAudience = {
				id: -1,
				name: 'Everyone',
			};
			this.segments.splice(0, 0, emptyAudience);

			if (location.search?.includes('template=true')) {
				this.templateMode = true;
				this.questName = 'Template Quest'
				this.questCategory = 'onboarding';
			}

			if (location.search?.includes('quest_id=')) {
				this.questId = location.search.match(/(?<=quest_id=)[0-9]+/)?.[0];
			}

			if (location.search?.includes('id=')) {
				this.id = location.search.match(/(?<=\bid=)[0-9]+/)?.[0];

				const response = await fetch(`${URL_DOMAIN}/chat-graph/campaign/${this.id}`, {
					method: 'GET',
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					}
				});

				const result = await response.json();
				const chatGraph = result.chatGraph;

				this.templateSelection = chatGraph;
				this.importScript = this.templateSelection;
				this.questName = result.name;
				this.questPriority = result.priority;
				this.questCategory = result.category;
				this.questDescription = result.description;
				this.questAlbumArtUrl = result?.image?.url;
				this.questSender = result.sender || localStorage.getItem('orgName') || '';
				this.questVisibility = result.hiddenUntilComplete ? 'complete' : 'always';
				try {
					this.questStartDate = new Date(result.startDate).toISOString().split('T')[0];
					if (new Date(this.questStartDate).getTime() > new Date('12/31/4010').getTime()) {
						this.questStartDate = null;
					}

					this.questEndDate = new Date(result.endDate).toISOString().split('T')[0];
					if (new Date(this.questEndDate).getTime() > new Date('12/31/4010').getTime()) {
						this.questEndDate = null;
					}

				} catch (e) {
					console.error('Date parse error', e);
				}
				this.selectedSegment = result.segmentId;

				this.importQuestScript();
			} else {
				this.templateSelection = '[]';
				document.querySelector('iframe.playground').style.display = 'none';
			}

			this.isLoading = false;
			this.dirty = false;
			setTimeout(() => this.dirty = false);

			if (this.questEndDate && this.questStartDate && localStorage.getItem('snippetVerified') != 'true') {
				this.isSnippetWarningVisible = true;
			}



			if (this.id) {
				this.intializePlayground();

				if (localStorage.getItem('questEditorWelcomeViewed') != 'true' && location.search.includes('wasTemplate=true')) {
					this.isWelcomehModalOpen = true;

					localStorage.setItem('questEditorWelcomeViewed', 'true');
				}
			}


		},
		computed: {
			getCompList() {

			},
			isSelfService() {
				return localStorage.getItem('selfService') == 'true';
			},
			campaignStatus() {
				return !this.id
					? 'Quest Not Saved'
					: new Date(this.questStartDate).getTime() > Date.now() || !this.questStartDate
						? 'Draft'
						: new Date(this.questEndDate).getTime() < Date.now()
							? 'Completed'
							: 'Running'
			}
		},
		methods: {
			copyToClipboard() {
				var codeText = this.generateOutput();

				/* Create a temporary textarea element */
				var textarea = document.createElement("textarea");
				textarea.value = codeText;
				document.body.appendChild(textarea);

				/* Copy the textarea content to clipboard */
				textarea.select();
				document.execCommand("copy");

				/* Remove the temporary textarea */
				document.body.removeChild(textarea);
			},

			async requestUpgrade() {
				const response = await fetch(`${URL_DOMAIN}/onboard/self-service/upgrade`, {
					method: 'POST',
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					}
				});

				if (response.ok && response.status >= 200 && response.status < 300) {
					localStorage.setItem('selfServiceUpgradeRequested', true);
				}
			},
			async useTemplate(template) {
				this.templateSelection = template.syntax;
				this.importScript = this.templateSelection;
				this.importQuestScript();
				this.questName = template.name;

				this.$forceUpdate();

				await new Promise(r => setTimeout(r));


				this.saveQuest(true, true);
			},
			generateOutput() {
				console.log("Generating Quest Script");

				let jsonOutput = '[';
				this.$refs.components?.forEach( (component,index) => {

					if (index === this.$refs.components.length - 1) {
						jsonOutput += component.generateSyntax();
					} else {
						jsonOutput += component.generateSyntax() + ',';
					}
				});

				if (this.questProfilePicUrl) {
					jsonOutput += `${this.compList.length ? ',' : ''}
					{
						"type": "config-styles",
						"data": {
							"app-profile-url": "${this.questProfilePicUrl}"
						}
					}`;
				}

				console.log(jsonOutput);

				return jsonOutput + ']';
			},
			async jimmy() {
				this.$forceUpdate();

				await new Promise(r => setTimeout(r));

				this.importScript = this.generateOutput();
				this.compList = [];
				this.$forceUpdate();

				await new Promise(r => setTimeout(r));

				this.importQuestScript()

				await new Promise(r => setTimeout(r));

				this.$forceUpdate();
			},
			async publishQuest() {
				if (!this.questStartDate || !this.questEndDate) {
					this.questStartDate = new Date().toISOString().split('T')[0];
					this.questEndDate = new Date();
					this.questEndDate.setDate(this.questEndDate.getDate() + 30);
					this.questEndDate = this.questEndDate.toISOString().split('T')[0];
					this.isPublishModalOpen = true;
					return;
				}

				this.saveQuest();
			},
			async saveQuest(reloadOnly, wasTemplate) {
				this.saving = true;
				this.saveFailure = false;

				localStorage.setItem('onboardingComplete', 'true');

				try {
					const output = this.generateOutput();

					let response;
					if (!this.id) {

						response = await fetch(`${URL_DOMAIN}/chat-graph/`, {
							method: 'POST',
							credentials: 'omit',
							mode: 'cors',
							headers: {
								'Content-Type': 'application/json',
								Authorization: `Bearer ${localStorage.getItem('token')}`,
							},
							body: JSON.stringify({
								id: Utils.uuidv4(),
								chatGraph: output,
								name: this.questName,
								orgId: localStorage.getItem('userOrgId'),
								priority: this.questPriority,
								category: this.questCategory,
								description: this.questDescription || ' ',
								segmentId: this.selectedSegment,
								startDate: this.questStartDate || new Date('1/1/4011').toISOString().split('T')[0],
								endDate: this.questEndDate || new Date('1/2/4011').toISOString().split('T')[0],
								sender: this.questSender || ' ',
								hiddenUntilComplete: this.questVisibility == 'complete'
							}),
						});

						const result = await response.json();

						if (response.ok && response.status >= 200 && response.status < 300) {
							this.saving = false;
							this.dirty = false;
							this.saveFailure = false;
							// if (reloadOnly && ) {

							// 	const iframe = document.querySelector('iframe.playground');
							// 	iframe.src = 'about:blank';
							// 	iframe.src = '';
							// 	this.importScript = this.generateOutput();
							// 	iframe.onload = () => this.intializePlayground();
							// 	iframe.style.height = '500px';

							// 	// location.reload();
							// 	return;
							// }
							location.search = `?id=${result.campaignId}&quest_id=${result.id}${wasTemplate ? '&wasTemplate=true' : ''}`;
							return;
						} else {
							this.saveFailure = true;
							this.saving = false;
							this.rescueScript = this.generateOutput();
						}

					} else {

						response = await fetch(`${URL_DOMAIN}/chat-graph/campaign/${this.id}`, {
							method: 'PATCH',
							credentials: 'omit',
							mode: 'cors',
							headers: {
								'Content-Type': 'application/json',
								Authorization: `Bearer ${localStorage.getItem('token')}`,
							},
							body: JSON.stringify({
								id: Utils.uuidv4(),
								chatGraph: output,
								name: this.questName,
								orgId: localStorage.getItem('userOrgId'),
								priority: this.questPriority,
								category: this.questCategory,
								description: this.questDescription || ' ',
								segmentId: this.selectedSegment,
								startDate: this.questStartDate || new Date('1/1/4011').toISOString().split('T')[0],
								endDate: this.questEndDate || new Date('1/2/4011').toISOString().split('T')[0],
								sender: this.questSender || ' ',
								hiddenUntilComplete: this.questVisibility == 'complete'
							}),
						});

						const result = await response.json();
					}

					console.log(response);

					if (response.ok  && response.status >= 200 && response.status < 300) {
						this.saving = false;
						this.dirty = false;
						this.saveFailure = false;
						if (reloadOnly) {
								const iframe = document.querySelector('iframe.playground');
								iframe.src = 'about:blank';
								iframe.src = '';
								this.importScript = this.generateOutput();
								iframe.onload = () => this.intializePlayground();
								iframe.style.height = '500px';

							// location.reload();
							return;
						}
						this.$router.push(`/campaign/${this.id}`);
					} else {
						this.saveFailure = true;
						this.saving = false;
						this.rescueScript = this.generateOutput();
					}
				} catch (e) {
					this.saveFailure = true;
					this.saving = false;
					throw e;
				}

			},
			deleteComponent(index) {
				this.safeSplice(index, 1);
				this.dirty = true;
			},
			//optional json
			buildCommand(type, json, useIdOne, nextId) {
				//nId is either going to be generated or come from an import
				let nId = null;
				if(json == null)
					nId = useIdOne ? 1 : this.generateId();
				else
					nId = json.id;



				console.log("Adding new command type " + type);
				let newComp = {};
				switch (type) {
					case 'app-message':
						newComp = {
							component: AppMessageCommand,
							props: {
									commandList: this.compList,
									compId: nId,
									commandsAvailable: this.commands,
									hydrate: json,
									sender: this.questSender || ' '
								},
							id: nId
						};
						break;
					case 'user-message':
						newComp = {
							component: UserMessageCommand,
							props: {
									commandList: this.compList,
									compId: nId,
									commandsAvailable: this.commands,
									hydrate: json,
									sender: this.questSender || ' '
								},
							id: nId
						};
						break;
					case 'show-image':
						newComp = {
							component: ShowImageCommand,
							props: {
									commandList: this.compList,
									compId: nId,
									commandsAvailable: this.commands,
									hydrate: json
								},
							id: nId
						};
						break;
					case 'simple-button':
						newComp = {
							component: MultipleChoiceCommand,
							props: {
									commandList: this.compList,
									compId: nId,
									commandsAvailable: this.commands,
									hydrate: json,
									sender: this.questSender || ' '
								},
							id: nId
						};
						break;
					case 'end-quest':
						newComp = {
							component: EndQuestCommand,
							props: {
									commandList: this.compList,
									compId: nId,
									commandsAvailable: this.commands,
									hydrate: json
								},
							id: nId
						};
						break;
					case 'send-custom-event':
						newComp = {
							component: SendCustomEventCommand,
							props: {
									commandList: this.compList,
									compId: nId,
									commandsAvailable: this.commands,
									hydrate: json
								},
							id: nId
						};
						break;
					case 'has-custom-event':
						newComp = {
							component: HasEventCommand,
							props: {
									commandList: this.compList,
									compId: nId,
									commandsAvailable: this.commands,
									hydrate: json
								},
							id: nId
						};
						break;
					case 'award-xp':
						newComp = {
							component: AwardXPCommand,
							props: {
									commandList: this.compList,
									compId: nId,
									commandsAvailable: this.commands,
									hydrate: json
								},
							id: nId
						};
						break;
					case 'show-quest-info':
						newComp = {
							component: ShowQuestCommand,
							props: {
									commandList: this.compList,
									compId: nId,
									commandsAvailable: this.commands,
									hydrate: json,
									rewardTypes: this.campaignStore.rewardTypes,
									generateNewIds: !this.id
								},
							id: nId
						};
						this.questActionsBlock = newComp;
						break;
					case 'fetch-quest':
						newComp = {
							component: FetchQuestCommand,
							props: {
									commandList: this.compList,
									compId: nId,
									commandsAvailable: this.commands,
									hydrate: json
								},
							id: nId
						};
						break;
					case 'typing-indicator':
						newComp = {
							component: TypingIndicatorCommand,
							props: {
									commandList: this.compList,
									compId: nId,
									commandsAvailable: this.commands,
									hydrate: json
								},
							id: nId
						};
						break;
					default:
						newComp = {
							component: AppMessageCommand,
							props: {
									commandList: this.compList,
									compId: nId,
									commandsAvailable: this.commands,
									hydrate: json,
									sender: this.questSender || ' '
								},
							id: nId
						};
				}
				return newComp;
			},
			isSequential(index) {
				const prevId = this.compList[index - 1]?.props?.compId;
				const prev = this.$refs.components?.find(x => x.compId == prevId);
				const next = this.compList[index];

				return (prev?.selectedNextComponent === next?.props?.compId);
			},
			addCommand(command, index = this.compList.length, nextId = null) {
				this.dirty = true;
				let newCom = command;
				const last = this.$refs?.components?.[index - 1];
				if(command == null) {

					// newCom = this.buildCommand('app-message', null, !this.compList.length);
					// this.compList.splice(index, 0, newCom);

					newCom = {
						props: {
							compId: !this.compList.length ? 1 : this.generateId()
						}
					};

					this.safeSplice(index, 0, {
						type: 'app-message',
						id: newCom.props.compId,
						data: {
							message: '',
							sender: this.questSender,
							type: 'received'
						},
						next: {
							id: nextId || null,
							condition: "immediate"
						}
					});
				} else {
					// this.compList.splice(index, 0, command);
					// this.safeSplice(index, 0, command);
					this.compList.push(command);
				}

				if (last && !last.selectedNextComponent) {
					last.selectedNextComponent = newCom.props.compId;
				}

				return newCom;
			},
			addCommandBetween(command, prevIndex, nextIndex) {

				const lastInfo = this.compList[prevIndex];
				const lastId = lastInfo?.props?.compId;
				const nextInfo = this.compList[nextIndex];
				const nextId = nextInfo?.props?.compId;

				const resultInfo = this.addCommand(null, nextIndex, nextId);

				const last = this.$refs.components?.find(x => x.compId === lastId);
				last.selectedNextComponent = resultInfo.props.compId;

				// this.$forceUpdate();
				// setTimeout(() => {
				// 	const result = this.$refs.components?.find(x => x.compId === resultInfo.props.compId);
				// 	result.selectedNextComponent = nextId;

				// 	this.$forceUpdate();
				// });

				return result;
			},
			async uploadFile(event, endpointPart) {
				const file = event.target.files[0];
				if (!endpointPart) {
					throw new Error('No endpoint to upload file to');
				}

				const endpoint = endpointPart.startsWith('/')
					? `${URL_DOMAIN}${endpointPart}`
					: endpointPart;

				const formData = new FormData();
				formData.append('file', file);

				const response = await fetch(endpoint, {
					method: 'POST',
					credentials: 'include',
					headers: {
						Authorization: endpoint.startsWith(URL_DOMAIN) ? `Bearer ${localStorage.token}` : undefined
					},
					body: formData
				});
				const result = await response.text();

				return result;
			},
			updateCommandInfo(payload) {
				if (!payload.isNonChange) {
					this.dirty = true;
				}

				let index = this.compList.findIndex(obj => obj.id === payload.id)

				if (index !== -1) {
					this.compList[index].name = payload.title;
				} else {
					let newComp = { name: payload.title, id: payload.id };
					this.compList.push(newComp);
				}
			},
			async updateComponentType(data) {
				console.log("Changing type");
				let index = this.compList.findIndex(component => component.id === data.id);
				const oldNextId = this.$refs.components?.find(x => x.compId == data.id)?.selectedNextComponent;

				if (index !== -1) {
					this.dirty = true;
					console.log("Removing component " + data.id);
					// const newComInfo =  this.buildCommand(data.newType, null, data.id === 1, data.next);
					const newId = data.id === 1 ? 1 : this.generateId(); // newComInfo.props?.compId;

					const nodeType = data.newType === 'app-message';


					await this.safeSplice(index, 1, {
						type: ['app-message', 'user-message'].includes(data.newType) ? 'chat-bubble' : data.newType,
						id: newId,
						data: {
							type: ['app-message', 'user-message'].includes(data.newType)
								? data.newType === 'app-message'
									? 'received'
									: 'sent'
								: undefined
						},
						next: {
							id: oldNextId || null,
							condition: "immediate"
						}
					});


					this.$refs.components?.forEach(x => {
						if (x.selectedNextComponent && x.selectedNextComponent === data.id) {
							x.selectedNextComponent = newId;
						}
					});

					// const newCom = this.$refs.components?.find(x => x.compId === newId);
					// newCom.selectedNextComponent = oldNextId;

					this.$forceUpdate();
				}
			},
			importQuestScript() {
				JSON.parse(this.importScript || '[]').forEach((item, index) => {
					console.log("Each item type is: " + item.type);

					let newComponent;

					switch(item.type) {
						case 'chat-bubble':
							if(item.data.type == 'received') {
								newComponent = this.buildCommand('app-message', item);
							}
							else if(item.data.type == 'sent') {
								newComponent = this.buildCommand('user-message', item);
							}
						break;
						case 'simple-button':
							newComponent = this.buildCommand('simple-button', item);
						break;
						case 'send-custom-event':
							newComponent = this.buildCommand('send-custom-event', item);
						break;
						case 'show-image':
							newComponent = this.buildCommand('show-image', item);
						break;
						case 'award-xp':
							newComponent = this.buildCommand('award-xp', item);
						break;
						case 'end-quest':
							newComponent = this.buildCommand('end-quest', item);
						break;
						case 'show-quest-info':
							newComponent = this.buildCommand('show-quest-info', item);
						break;
						case 'has-custom-event':
							newComponent = this.buildCommand('has-custom-event', item);
						break;
						case 'fetch-quest':
							newComponent = this.buildCommand('fetch-quest', item);
						break;
						case 'typing-indicator':
							newComponent = this.buildCommand('typing-indicator', item);
						break;
						case 'config-styles':
							this.questProfilePicUrl = item?.data?.['app-profile-url'];
							break;
						default:
							newComponent = this.buildCommand('app-message', item);
						break;
					}

					if (newComponent) {
						this.compList.push(newComponent);
					}
				});

				this.$forceUpdate();
				setTimeout(() => this.$forceUpdate());
			},
			generateId() {
				return Math.floor(Math.random() * (1000000 - 1 + 1)) + 1;
			},
			async safeSplice(index, del, ...newItems) {
				const container = document.querySelector('.scroll-container');
				const oldScroll = container.scrollTop;

				this.$forceUpdate();
				await new Promise(r => setTimeout(r));

				const json = JSON.parse(this.generateOutput());
				json.splice(index, del, ...newItems);

				this.compList = [];
				this.$forceUpdate()
				await new Promise(r => setTimeout(r));;

				this.importScript = JSON.stringify(json);
				this.importQuestScript();

				this.$forceUpdate();
				await new Promise(r => setTimeout(r));

				container.scrollTo({
					top: del === 0 && newItems.length ? oldScroll + 300 : oldScroll
				});
			},
			intializePlayground() {

				const iframe = document.querySelector('iframe.playground');

				const actualWebSocket = iframe.contentWindow.WebSocket;

				const id = Number(this.id);
				const userId = 'playground'; // -' + Utils.uuidv4();
				const questId = Number(this.questId);
				const syntax = this.importScript;
				let socket;
				iframe.contentWindow.localStorage.setItem(
					'raleonf-local-server-url',
					location.hostname === 'localhost'
						? 'http://localhost:3000/api/v1'
						: `https://${location.hostname}/api/v1`
				);

				const tweakInterval = setInterval(() => {
					const closeButton = iframe.contentDocument?.body?.querySelector('message-container')?.shadowRoot?.querySelector('.above-header')?.querySelector('.close-button');

					if (closeButton) {
						closeButton.addEventListener('click', () => iframe.style.height = '100px');
						// clearInterval(tweakInterval);
					}

					const chatHeader = iframe.contentDocument?.body?.querySelector('message-container')?.shadowRoot?.querySelector('.chat-header');
					if (chatHeader) {
						chatHeader.addEventListener('click', () => iframe.style.height = '500px');
					}

					const installHint = iframe.contentDocument?.body?.querySelector('message-container')?.shadowRoot?.querySelector('.powered-by');
					if (installHint && localStorage.getItem('snippetVerified') != 'true') {
						installHint.outerHTML = `
							<div class="install-hint" style="
								color: rgb(83, 27, 193);
								opacity: 1;
								background: white;

								padding: 2px 22px;
								backdrop-filter: blur(32px);
								border-radius: 6px;
								font-family: Verdana;
								font-style: normal;
								font-weight: 500;
								font-size: 10px;
								line-height: 18px;
								text-transform: uppercase;
								border: 2px solid rgb(83, 27, 193);

							">
								<a
									href="/snippet"
									target="_blank"
									style="text-decoration: none; font-size: 1.3em;font-weight: 900;font-family: sans-serif;"
								>Install Raleon Snippet</a>
							</div>`
					}
				}, 100)

				iframe.contentWindow.WebSocket = function (...args) {
					socket = new actualWebSocket(...args);

					let disableRegister = true;
					const actualSend = socket.send;
					socket.send = function (...sendArgs) {
						if (disableRegister && sendArgs[0].includes('command":"register')) {
							return;
						}

						actualSend.apply(socket, sendArgs);
					};

					socket.addEventListener('open', () => {
						setTimeout(() => {
							disableRegister = false;


							socket.send(JSON.stringify({
								command: 'register',
								data: {
									raleonId: userId,
									orgId: localStorage.getItem('userOrgId') || 1,
									walletAddress: userId,
									questCategory: '',
									sessionId: '',
									questId: questId,
									campaignId: id,
									skipHistory: true,
									playground_quest: syntax,
									useActualQuestId: true
								}
							}));

							// disableRegister = true;
						}, 1000);
					})

					return socket;
				}

				const snippet = iframe.contentDocument.createElement('script');
				snippet.src = 'https://dqpqjbq51w8fz.cloudfront.net/raleon_snippet.min.js';
				iframe.contentDocument.body.appendChild( snippet);

				snippet.onload = function() {
					iframe.contentWindow.setupRaleonParametersV2({
						orgId: localStorage.getItem('userOrgId'),
						enable_action_prompts: false,
						enable_quests: true,
						enableActionPrompts: false,
						enableQuests: true,
						defaultOpenChat: true,
						position: {
							left: '0px',
							bottom: '20px'
						}
					});
					iframe.contentWindow.raleon.walletConnected(userId);
					iframe.contentWindow.raleon.enableQuests();

					// const event = new CustomEvent('show-chat', { bubbles: true, composed: true, detail: { clearChat: true } });
					// this.dispatchEvent(event);
				};
			}
		}
	}

	const templates = [
		{
			name: 'New User Education and Onboarding',
			syntax: `[
					{
						"type": "chat-bubble",
						"id": 1,
						"data": {
							"message": "Hey there! 🚀 Welcome to Raleon! We designed this Quest Template to be your starting point to create an amazing experience for your users!",
							"sender": " ",
							"type": "received"
						},
						"next": {
							"id": 787096,
							"condition": "immediate",
							"delay": 0
						}
					}
					,
					{
						"type": "simple-button",
						"id": 787096,
						"data": {
							"message": "The format is designed to guide your user thru a few key learnings and conclude with a call to action. <b>All you need to do is update the text to make it fit your project! </b>",
							"sender": " ",
							"buttons": [
						{
							"text": "Sounds easy!",
							"onClick": 317813
						}]
						},
						"next": {
							"condition": "event"
						}
					}
					,
				{
					"type": "award-xp",
					"id": 317813,
					"data": {
						"xp": "50",
						"season": "default"
					},
					"next": {
						"id": 391196,
						"condition": "immediate"
					}
				}
					,
					{
						"type": "chat-bubble",
						"id": 391196,
						"data": {
							"message": "Great! Let's dive in to some best practices for using this dialogue format!",
							"sender": " ",
							"type": "received"
						},
						"next": {
							"id": 263813,
							"condition": "delay",
							"delay": 2
						}
					}
					,
					{
						"type": "simple-button",
						"id": 263813,
						"data": {
							"message": "Imagine a billboard. It's short, punchy, and grabs attention. Similarly, when you're engaging with customers, short and impactful messages matter. ",
							"sender": " ",
							"buttons": [
						{
							"text": "Like a text message?",
							"onClick": 568816
						}]
						},
						"next": {
							"condition": "event"
						}
					}
					,
					{
						"type": "simple-button",
						"id": 568816,
						"data": {
							"message": "Yes! Exactly. When messages are clear and concise, they're more likely to be read and understood.",
							"sender": " ",
							"buttons": [
						{
							"text": "Got it!!",
							"onClick": 731924
						}]
						},
						"next": {
							"condition": "event"
						}
					}
					,
				{
					"type": "award-xp",
					"id": 731924,
					"data": {
						"xp": "75",
						"season": "default"
					},
					"next": {
						"id": 465308,
						"condition": "immediate"
					}
				}
					,
					{
						"type": "simple-button",
						"id": 465308,
						"data": {
							"message": "Now, think about the last time you achieved something 🌟. How did you feel?",
							"sender": " ",
							"buttons": [
						{
							"text": "I felt great!",
							"onClick": 976800
						}]
						},
						"next": {
							"condition": "event"
						}
					}
					,
					{
						"type": "simple-button",
						"id": 976800,
						"data": {
							"message": "That's the feeling! 🥳 In dialogue, frequent positive reinforcements can create that. By validating and praising your user, they'll be more engaged. ",
							"sender": " ",
							"buttons": [
						{
							"text": "Like...confetti and points?",
							"onClick": 13722
						}]
						},
						"next": {
							"condition": "event"
						}
					}
					,
				{
					"type": "award-xp",
					"id": 13722,
					"data": {
						"xp": "75",
						"season": "default"
					},
					"next": {
						"id": 980839,
						"condition": "immediate"
					}
				}
					,
					{
						"type": "simple-button",
						"id": 980839,
						"data": {
							"message": "Yes! Exactly! :) Celebrating small wins can make a HUGE difference in user experience. 🎉 ",
							"sender": " ",
							"buttons": [
						{
							"text": "Makes sense! Everyone loves a celebration!",
							"onClick": 841116
						}]
						},
						"next": {
							"condition": "event"
						}
					}
					,
					{
						"type": "simple-button",
						"id": 841116,
						"data": {
							"message": "Now, to seal the deal, every interaction should drive towards a...?",
							"sender": " ",
							"buttons": [
						{
							"text": "Call to action?",
							"onClick": 987115
						}]
						},
						"next": {
							"condition": "event"
						}
					}
					,
					{
						"type": "simple-button",
						"id": 987115,
						"data": {
							"message": "You're on fire! 🔥 A clear CTA is like a destination. It guides users on where to go next. And you want it to be compelling!",
							"sender": " ",
							"buttons": [
						{
							"text": "Keep them moving forward!",
							"onClick": 658043
						}]
						},
						"next": {
							"condition": "event"
						}
					}
					,
					{
						"type": "simple-button",
						"id": 658043,
						"data": {
							"message": "Precisely! 💡And lastly, remember, time is gold. Aim for a short dialogue. Capture attention, deliver value, and move on. You don't want to lose them in a maze of messages.",
							"sender": " ",
							"buttons": [
						{
							"text": "Keep it simple....and convert!",
							"onClick": 339358
						}]
						},
						"next": {
							"condition": "event"
						}
					}
					,
					{
						"type": "simple-button",
						"id": 339358,
						"data": {
							"message": "That's it! Practice makes perfect. Ready to give it a shot?",
							"sender": " ",
							"buttons": [
						{
							"text": "Let's go!",
							"onClick": 707515
						}]
						},
						"next": {
							"condition": "event"
						}
					}
					,
				{
					"type": "award-xp",
					"id": 707515,
					"data": {
						"xp": "100",
						"season": "default"
					},
					"next": {
						"id": 231957,
						"condition": "immediate"
					}
				}
					,
					{
						"type": "chat-bubble",
						"id": 231957,
						"data": {
							"message": "Great! Now it's time for you to turn this example into your own! ",
							"sender": " ",
							"type": "received"
						},
						"next": {
							"id": 848204,
							"condition": "immediate",
							"delay": 0
						}
					}
					,
					{
						"type": "simple-button",
						"id": 848204,
						"data": {
							"message": "Use this script as a template. Simply adjust it to match the particular learning point you want to convey to your users, but maintain the structure and best practices outlined above. This is your call to action! Make it your own! ",
							"sender": " ",
							"buttons": [
						{
							"text": "Any final pointers?",
							"onClick": 67506
						}]
						},
						"next": {
							"condition": "event"
						}
					}
					,
					{
						"type": "chat-bubble",
						"id": 67506,
						"data": {
							"message": "I have a pro tip for you! You can use HTML within your messages to <a href=https://raleon.io>create links</a>, add <b>formatting</b>, and make the experience as <i>personalized</i> as you want!",
							"sender": " ",
							"type": "received"
						},
						"next": {
							"id": 680919,
							"condition": "delay",
							"delay": 3
						}
					}
					,
					{
						"type": "chat-bubble",
						"id": 680919,
						"data": {
							"message": "That's all! Have fun. You can access <a href=https://docs.raleon.io/docs/quest-editor>support documentation here</a>. And if you need any help, the Raleon team is here to help!",
							"sender": " ",
							"type": "received"
						},
						"next": {
							"id": 968456,
							"condition": "immediate",
							"delay": 0
						}
					}
					,
				{
					"type": "end-quest",
					"id": 968456,
					"data": {},
					"next": {
						"condition": "immediate",
						"id": null
					}
				}
				]`,
			description: 'Guide users to learn about your project or brand in an engaging way, and take the right steps toward user activation.',
			info: '',
			tags: ['Most Common', 'User Acquisition'],
			svgFill: "#22C55E",
			emphasized: true,
			svgPath: "M18.3687 15.4547C18.7479 14.988 19.0286 14.5067 19.2109 14.0109C19.3932 13.5151 19.4844 12.939 19.4844 12.2828C19.4844 11.6265 19.3932 11.0505 19.2109 10.5547C19.0286 10.0588 18.7479 9.57758 18.3687 9.11091C19.4771 8.86299 20.4505 9.0307 21.2891 9.61404C22.1276 10.1974 22.5469 11.087 22.5469 12.2828C22.5469 13.4786 22.1276 14.3682 21.2891 14.9515C20.4505 15.5349 19.4771 15.7026 18.3687 15.4547ZM23.0938 22.5859V20.5297C23.0938 19.7859 22.9042 19.0932 22.525 18.4515C22.1458 17.8099 21.4896 17.2703 20.5562 16.8328C23.0792 17.1536 24.8036 17.6203 25.7297 18.2328C26.6557 18.8453 27.1187 19.6109 27.1187 20.5297V22.5859H23.0938ZM25.5 16.264V14.0765H23.3125V12.764H25.5V10.5765H26.8125V12.764H29V14.0765H26.8125V16.264H25.5ZM14.8906 15.564C13.9281 15.564 13.1406 15.2578 12.5281 14.6453C11.9156 14.0328 11.6094 13.2453 11.6094 12.2828C11.6094 11.3203 11.9156 10.5328 12.5281 9.92029C13.1406 9.30779 13.9281 9.00154 14.8906 9.00154C15.8531 9.00154 16.6406 9.30779 17.2531 9.92029C17.8656 10.5328 18.1719 11.3203 18.1719 12.2828C18.1719 13.2453 17.8656 14.0328 17.2531 14.6453C16.6406 15.2578 15.8531 15.564 14.8906 15.564ZM8 22.5859V20.5297C8 20.0192 8.1349 19.5562 8.40469 19.1406C8.67448 18.725 9.03542 18.4151 9.4875 18.2109C10.5375 17.7442 11.4745 17.4088 12.2984 17.2047C13.1224 17.0005 13.9865 16.8984 14.8906 16.8984C15.7948 16.8984 16.6552 17.0005 17.4719 17.2047C18.2885 17.4088 19.2219 17.7442 20.2719 18.2109C20.724 18.4151 21.0885 18.725 21.3656 19.1406C21.6427 19.5562 21.7812 20.0192 21.7812 20.5297V22.5859H8ZM14.8906 14.2515C15.4594 14.2515 15.9297 14.0656 16.3016 13.6937C16.6734 13.3218 16.8594 12.8515 16.8594 12.2828C16.8594 11.714 16.6734 11.2437 16.3016 10.8718C15.9297 10.5 15.4594 10.314 14.8906 10.314C14.3219 10.314 13.8516 10.5 13.4797 10.8718C13.1078 11.2437 12.9219 11.714 12.9219 12.2828C12.9219 12.8515 13.1078 13.3218 13.4797 13.6937C13.8516 14.0656 14.3219 14.2515 14.8906 14.2515ZM9.3125 21.2734H20.4688V20.5297C20.4688 20.2963 20.4104 20.0776 20.2937 19.8734C20.1771 19.6692 19.9948 19.5088 19.7469 19.3922C18.7406 18.9255 17.8875 18.612 17.1875 18.4515C16.4875 18.2911 15.7219 18.2109 14.8906 18.2109C14.0594 18.2109 13.2974 18.2911 12.6047 18.4515C11.912 18.612 11.0479 18.9255 10.0125 19.3922C9.79375 19.4942 9.6224 19.651 9.49844 19.8625C9.37448 20.0739 9.3125 20.2963 9.3125 20.5297V21.2734Z"
		}, {
			name: 'Grow Your Community - Discord',
			syntax: `[
					{
						"type": "chat-bubble",
						"id": 1,
						"data": {
							"message": "Welcome back! 🚀 If you're interested in growing your community, then you've come to the right place.",
							"sender": " ",
							"type": "received"
						},
						"next": {
							"id": 842577,
							"condition": "delay",
							"delay": 3
						}
					}
					,
					{
						"type": "chat-bubble",
						"id": 842577,
						"data": {
							"message": "In this template, we've set up a direct path to joining your Discord community, as well as a short educational path to explain why joining your community is worth it! ",
							"sender": " ",
							"type": "received"
						},
						"next": {
							"id": 625075,
							"condition": "delay",
							"delay": 3
						}
					}
					,
					{
						"type": "chat-bubble",
						"id": 625075,
						"data": {
							"message": "To make this Quest your own:<br><br><b>1.</b> Update the Quest Actions component below to point users to your Discord and give a reward for joining! Then,<br><b>2.</b> Update the dialogue text throughout this Quest to fit your project and your community!</b>",
							"sender": " ",
							"type": "received"
						},
						"next": {
							"id": 309193,
							"condition": "delay",
							"delay": 6
						}
					}
					,
					{
						"type": "simple-button",
						"id": 309193,
						"data": {
							"message": "For this community, Discord is where all the magic happens. Real-time conversations, insights, and some exclusive access to the team, power users, special announcements and more!",
							"sender": " ",
							"buttons": [
						{
							"text": "I'm in! Let's go to the Discord!",
							"onClick": 993810
						},
						{
							"text": "Tell me more before I join",
							"onClick": 428070
						}]
						},
						"next": {
							"condition": "event"
						}
					}
					,
				{
					"type": "show-quest-info",
					"id": 993810,
					"data": {
						"onComplete": 660659,
						"onRewardReady": 0,
						"onRewardClaimed": 0
						,"reward": {"configData":{"amount":"250"},"type":"award-xp"},
								"actions": {"goals":[{"type":"JoinDiscordServer","requiredData":{"discordServerName":{"type":"string","friendlyName":"Discord Server Name","value":"Your Discord Server Name"},"discordServerInviteLink":{"type":"string","friendlyName":"Discord Server Invite Link","value":"ENTER YOUR SERVER LINK HERE"},"discordServerId":{"type":"string","friendlyName":"Discord Server Id","actionDisplayName":"Join","value":"ENTER YOUR SERVER ID HERE"}},"externalId":"2272c93e-51f5-4400-aa30-7d9532b9395c","name":"Join Discord Server","content":{"message":"Join our discord server.","buttonText":"Join Discord Server","buttonUrl":"https://discord.com/api/oauth2/authorize?client_id=1096494427364663458&redirect_uri=https%3A%2F%2Fapp.raleon.io%2Fapi%2Fv1%2Fgoals%2Foauth-redirect%2Fdiscord&response_type=code&scope=identify%20guilds%20guilds.members.read","headerImageUrl":"","closeMessage":"","css":{}}}]}
					},
					"next": {
						"id": 660659,
						"condition": "event"
					}
				}
					,
				{
					"type": "end-quest",
					"id": 660659,
					"data": {},
					"next": {
						"condition": "immediate",
						"id": null
					}
				}
				,
					{
						"type": "simple-button",
						"id": 428070,
						"data": {
							"message": "Joining our Discord is like walking into a room full of passionate individuals united by our common values. Everyone is sharing tips, tricks, and even granting early access to new opportunities and <i>alpha</i>! 🔥 Plus, you get direct answers to any of your questions!",
							"sender": " ",
							"buttons": [
						{
							"text": "Sounds appealing!",
							"onClick": 965133
						}]
						},
						"next": {
							"condition": "event"
						}
					}
					,
					{
						"type": "simple-button",
						"id": 965133,
						"data": {
							"message": "It's a great group of people. We also use our Discord to communicate special announcements, host virtual events, and listen to feedback to make our project even better! ",
							"sender": " ",
							"buttons": [
						{
							"text": "How do I join?",
							"onClick": 385572
						}]
						},
						"next": {
							"condition": "event"
						}
					}
					,
					{
						"type": "simple-button",
						"id": 385572,
						"data": {
							"message": "It's easy! Just follow the links below to join. Once you're in, you'll receive a reward too :) ",
							"sender": " ",
							"buttons": [
						{
							"text": "Let's do it!",
							"onClick": 993810
						}]
						},
						"next": {
							"condition": "event"
						}
					}
					]`,
			description: 'Make sure users know about and join your community so they can easily get updates and provide feedback.',
			info: '',
			tags: ['Community Growth'],
			svgFill: "#9254F7",
			svgPath: "M19.5375 20.9344L21.375 19.8406L23.2125 20.9344L22.7312 18.8562L24.3281 17.4563L22.2063 17.2812L21.375 15.3125L20.5437 17.2812L18.4219 17.4563L20.0188 18.8562L19.5375 20.9344ZM13.9375 17.9375V19.25H11.3125C10.9516 19.25 10.6426 19.1215 10.3855 18.8645C10.1285 18.6074 10 18.2984 10 17.9375V8.3125C10 7.95156 10.1285 7.64258 10.3855 7.38555C10.6426 7.12852 10.9516 7 11.3125 7H20.9375C21.2984 7 21.6074 7.12852 21.8645 7.38555C22.1215 7.64258 22.25 7.95156 22.25 8.3125V10.9375H20.9375V8.3125H11.3125V17.9375H13.9375ZM16.5625 24.5C16.2016 24.5 15.8926 24.3715 15.6355 24.1145C15.3785 23.8574 15.25 23.5484 15.25 23.1875V13.5625C15.25 13.2016 15.3785 12.8926 15.6355 12.6355C15.8926 12.3785 16.2016 12.25 16.5625 12.25H26.1875C26.5484 12.25 26.8574 12.3785 27.1145 12.6355C27.3715 12.8926 27.5 13.2016 27.5 13.5625V23.1875C27.5 23.5484 27.3715 23.8574 27.1145 24.1145C26.8574 24.3715 26.5484 24.5 26.1875 24.5H16.5625ZM16.5625 23.1875H26.1875V13.5625H16.5625V23.1875Z"
		}, {
			name: 'Cross Sell to Returning User',
			syntax: `[
					{
						"type": "chat-bubble",
						"id": 1,
						"data": {
							"message": "You're back! We are so excited to see you trying out this Embedded Quest Template for cross selling to returning users! ",
							"sender": " ",
							"type": "received"
						},
						"next": {
							"id": 150567,
							"condition": "delay",
							"delay": 4
						}
					}
					,
					{
						"type": "chat-bubble",
						"id": 150567,
						"data": {
							"message": "As always, all you have to do is update the Quest text in this Editor to make this Quest your own!<br><br>Be sure to check out Raleon's Audiences feature to set up targeting to match this cross sell. A personalized message will convert more every time!! ",
							"sender": " ",
							"type": "received"
						},
						"next": {
							"id": 428982,
							"condition": "delay",
							"delay": 5
						}
					}
					,
					{
						"type": "simple-button",
						"id": 428982,
						"data": {
							"message": "Did you know that 90% of people who use PRODUCT 1 also benefit from PRODUCT 2?",
							"sender": " ",
							"buttons": [
						{
							"text": "90%! What is PRODUCT 2 all about?",
							"onClick": 65774
						}]
						},
						"next": {
							"condition": "event"
						}
					}
					,
					{
						"type": "simple-button",
						"id": 65774,
						"data": {
							"message": "Great question! 🚀 Imagine you had a pen. What if you could also have a notebook that perfectly complements it? Together, they’d be an unbeatable combo for your note taking needs.",
							"sender": " ",
							"buttons": [
						{
							"text": "You do need both to write!",
							"onClick": 919969
						}]
						},
						"next": {
							"condition": "event"
						}
					}
					,
					{
						"type": "chat-bubble",
						"id": 919969,
						"data": {
							"message": "PRODUCT 2 is just like the notebook. It makes life better for anyone using PRODUCT 1 because it is able to solve [insert user challenges here!].",
							"sender": " ",
							"type": "received"
						},
						"next": {
							"id": 950858,
							"condition": "delay",
							"delay": 5
						}
					}
					,
					{
						"type": "simple-button",
						"id": 950858,
						"data": {
							"message": "Now, here's the magic: 🎩✨<br><br>1 - PRODUCT 2 lasts twice as long [update with your product value here].<br>2 - It’s compact and pairs well with PRODUCT 1. [update here too]<br>3 - It provides the ideal surface every time.[and here :) ] <br><br>Sounds like a perfect fit, right?",
							"sender": " ",
							"buttons": [
						{
							"text": "Yes! How can I try it?",
							"onClick": 652656
						}]
						},
						"next": {
							"condition": "event"
						}
					}
					,
					{
						"type": "chat-bubble",
						"id": 652656,
						"data": {
							"message": "I thought you might ask! 😃  Just [give instructions to access your product here]. ",
							"sender": " ",
							"type": "received"
						},
						"next": {
							"id": 497474,
							"condition": "delay",
							"delay": 2
						}
					}
					,
					{
						"type": "chat-bubble",
						"id": 497474,
						"data": {
							"message": "Don't forget, you can use HTML to link directly to the product. Here's the formatting <a href=https://raleon.io>to make a link</a>. ",
							"sender": " ",
							"type": "received"
						},
						"next": {
							"id": 382547,
							"condition": "delay",
							"delay": 3
						}
					}
					,
					{
						"type": "chat-bubble",
						"id": 382547,
						"data": {
							"message": "And that's it for this template! Always remember to end your Quests gracefully so that your users know exactly what they should do next.<br><br>Now it's your turn! <b>Update this Quest now to start selling more!</b>",
							"sender": " ",
							"type": "received"
						},
						"next": {
							"id": 43991,
							"condition": "immediate",
							"delay": 0
						}
					}
					,
				{
					"type": "end-quest",
					"id": 43991,
					"data": {},
					"next": {
						"condition": "immediate",
						"id": null
					}
				}
				]`,
			description: 'Improve your user retention by having Quests that get your existing users to take additional actions.',
			info: '',
			tags: ['User Retention'],
			svgFill: "#60A5FA",
			svgPath: "M21.9437 14.5906C22.2719 14.5906 22.5508 14.4758 22.7805 14.2461C23.0102 14.0164 23.125 13.7375 23.125 13.4094C23.125 13.0812 23.0102 12.8023 22.7805 12.5727C22.5508 12.343 22.2719 12.2281 21.9437 12.2281C21.6156 12.2281 21.3367 12.343 21.107 12.5727C20.8773 12.8023 20.7625 13.0812 20.7625 13.4094C20.7625 13.7375 20.8773 14.0164 21.107 14.2461C21.3367 14.4758 21.6156 14.5906 21.9437 14.5906ZM15.5563 14.5906C15.8844 14.5906 16.1633 14.4758 16.393 14.2461C16.6227 14.0164 16.7375 13.7375 16.7375 13.4094C16.7375 13.0812 16.6227 12.8023 16.393 12.5727C16.1633 12.343 15.8844 12.2281 15.5563 12.2281C15.2281 12.2281 14.9492 12.343 14.7195 12.5727C14.4898 12.8023 14.375 13.0812 14.375 13.4094C14.375 13.7375 14.4898 14.0164 14.7195 14.2461C14.9492 14.4758 15.2281 14.5906 15.5563 14.5906ZM18.75 20.5406C19.7125 20.5406 20.5984 20.2818 21.4078 19.7641C22.2172 19.2464 22.8042 18.5427 23.1687 17.6531H14.3312C14.7104 18.5427 15.301 19.2464 16.1031 19.7641C16.9052 20.2818 17.7875 20.5406 18.75 20.5406ZM18.75 24.5C17.5396 24.5 16.4021 24.2703 15.3375 23.8109C14.2729 23.3516 13.3469 22.7281 12.5594 21.9406C11.7719 21.1531 11.1484 20.2271 10.6891 19.1625C10.2297 18.0979 10 16.9604 10 15.75C10 14.5396 10.2297 13.4021 10.6891 12.3375C11.1484 11.2729 11.7719 10.3469 12.5594 9.55938C13.3469 8.77188 14.2729 8.14844 15.3375 7.68906C16.4021 7.22969 17.5396 7 18.75 7C19.9604 7 21.0979 7.22969 22.1625 7.68906C23.2271 8.14844 24.1531 8.77188 24.9406 9.55938C25.7281 10.3469 26.3516 11.2729 26.8109 12.3375C27.2703 13.4021 27.5 14.5396 27.5 15.75C27.5 16.9604 27.2703 18.0979 26.8109 19.1625C26.3516 20.2271 25.7281 21.1531 24.9406 21.9406C24.1531 22.7281 23.2271 23.3516 22.1625 23.8109C21.0979 24.2703 19.9604 24.5 18.75 24.5ZM18.75 23.1875C20.8263 23.1875 22.585 22.467 24.026 21.026C25.467 19.585 26.1875 17.8263 26.1875 15.75C26.1875 13.6737 25.467 11.915 24.026 10.474C22.585 9.033 20.8263 8.3125 18.75 8.3125C16.6737 8.3125 14.915 9.033 13.474 10.474C12.033 11.915 11.3125 13.6737 11.3125 15.75C11.3125 17.8263 12.033 19.585 13.474 21.026C14.915 22.467 16.6737 23.1875 18.75 23.1875Z"
		}
	];

</script>

<style scoped>
*[disabled] {
	cursor: not-allowed !important;
}

.copy-btn {
	background-color: #4caf50;
	color: white;
	border: none;
	padding: 10px 20px;
	text-align: center;
	text-decoration: none;
	display: inline-block;
	font-size: 16px;
	margin: 4px 2px;
	cursor: pointer;
	position: absolute;
	top: 10px;
	right: 10px;
	margin-top:40px;
	}

	#quest-rescue-container {
	position: relative;
	padding-top: 40px;
	}

	#quest-rescue-script {
	background-color: #f1f1f1;
	padding: 10px;
	font-family: "Courier New", Courier, monospace;
	}

	.expand-icon {
		transition: transform 0.3s ease-in-out;
	}

	.expand-icon.rotate {
		transform: rotate(180deg);
	}

	.expandable {
  max-height: 0;
  overflow: hidden;
  transition: max-height .5s ease-in-out;
}

.expand-enter-active.expandable {
  max-height: 2000px;
}

.expand-leave-active.expandable {
  max-height: 0;
}


.snippet-banner {
  padding: 20px;
  text-align: center;
}

.snippet-banner h1 {
  color: #ffffff;
  font-size: 1em;
  font-weight: bold;
}

.snippet-banner p {
  color: #ffffff;
  font-size: 1em;
}

.snippet-banner a {
  background-color: #60A5FA;
  padding-left: 1em;
  padding-right: 1em;
  line-height: 2em;
  height: 2em;
  border: none;
  border-radius: 1em;
  cursor: pointer;
}

</style>
