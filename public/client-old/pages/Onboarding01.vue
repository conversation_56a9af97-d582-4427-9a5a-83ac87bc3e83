	<template>
	<main class="bg-white">
		<div class="relative flex">
			<!-- Content -->
			<div class="w-full md:w-1/2">
				<div class="min-h-screen h-full flex flex-col after:flex-1 z-50">
					<div class="flex-1">
						<div class="
	                flex
	                items-center
	                justify-between
	                h-16
	                px-4
	                sm:px-6
	                lg:px-8
	              ">
							<!-- Logo -->
							<!-- <router-link class="block" to="/"> -->
								<img src="../images/raleon-logo-whitebg.jpg" width="64" height="64" />
							<!-- </router-link> -->
						</div>
					</div>
					<div class="max-w-md mx-auto px-4 py-8">
						<h1 class="text-3xl text-slate-800 font-bold mb-6">
							Welcome to Raleon! ✨
						</h1>
						<div class="text-sm">What is your primary goal signing up today?</div>
              				<!-- Form -->
							<form style="min-width: 400px" >

								<div class="space-y-3 mb-8">
									<label class="block text-sm font-medium mb-1" for="organizationName"></label>
									<input v-model="chosenAdventure" id="targetUsers" class="form-input w-full" type="text" />
								</div>
								<div class="flex justify-center">
									<div>
										<div class="btn bg-indigo-500 hover:bg-indigo-600 text-white ml-auto" to="/signup?self-service=true" @click="chooseYourAdventure()"><span class="mr-2">Let's Go!</span>
											<svg v-if="!clicked" xmlns="http://www.w3.org/2000/svg" height="18" viewBox="0 96 960 960" width="18" fill="#FFFFFF"><path d="m480 896-42-43 247-247H160v-60h525L438 299l42-43 320 320-320 320Z"/></svg>
											<div v-if="clicked" class="spinner"></div>
										</div>
									</div>
								</div>
							</form>
					</div>
				</div>
			</div>
			<div class="hidden md:block absolute top-0 bottom-0 right-0 md:w-1/2" aria-hidden="true">
				<img class="object-cover object-center w-full h-full" src="../images/r-login-bg.png" width="760"
					height="1024" alt="Authentication" />
			</div>
		</div>
	</main>
</template>

<script>

import * as Utils from '../utils/Utils';
const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	props: { redirect: String },
	name: 'Onboarding',
	data() {
		return {
			email: '',
			password: '',
			loginError: false,
			chosenAdventure: '',
			targetUsers: '',
			clicked: false
		};
	},
	async mounted() {

	},
	methods: {
		async chooseYourAdventure() {
			if (this.clicked) {
				return;
			}

			this.clicked = true;
			fetch(`${Utils.URL_DOMAIN}/onboard/adventure/update`, {
				method: "post",
				withCreditentials: true,
				credentials: 'omit',
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					"accept": "application/json",
					"Content-Type": "application/json"
				},
				body: JSON.stringify({
					chosenAdventure: this.chosenAdventure
				}),
			}).catch();


			this.$router.push('/QuestEditor?template=true');
			// 		break;
			// switch (this.chosenAdventure) {
			// 	case 'analytics':
			// 		const response = await fetch(`${Utils.URL_DOMAIN}/onboard/analytics`, {
			// 			method: "post",
			// 			withCreditentials: true,
			// 			credentials: 'omit',
			// 			headers: {
			// 				'Authorization': `Bearer ${localStorage.getItem('token')}`,
			// 				"accept": "application/json",
			// 				"Content-Type": "application/json"
			// 			},
			// 			body: JSON.stringify({
			// 				targetUsers: this.targetUsers,
			// 				chosenAdventure: this.chosenAdventure
			// 			}),
			// 		});
			// 		const path = await response.text();

			// 		this.$router.push(path);
			// 		break;
			// 	case 'campaigns':
			// 		this.$router.push('/QuestEditor?template=true');
			// 		break;
			// 	case 'attribution':
			// 		this.$router.push('/campaigns/Overview');
			// 		break;

			// }
		}
	}
};
</script>
<style>
.spinner {
  top: 50%;
  left: 50%;
  width: 1.5em;
  height: 1.5em;
  border-radius: 50%;
  border: 0.15em solid transparent;
  border-top-color: #fff;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
