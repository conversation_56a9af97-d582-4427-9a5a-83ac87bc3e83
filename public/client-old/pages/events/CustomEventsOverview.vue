<style scoped>
input[type="search"].show-clear {
	-webkit-appearance: searchfield !important;
}

input[type="search"].show-clear::-webkit-search-cancel-button {
	-webkit-appearance: searchfield-cancel-button !important;
}

.simple-filter,
.sankey-container {
	margin-bottom: 2.5em;
}

.swappy {
	visibility: visible;
	opacity: 0;
	max-height: 0;

	transition-property: opacity, max-height;
	transition-duration: 0.25s;
	transition-timing-function: ease-in-out;
}

.swappy-visible {
	opacity: 100%;
	max-height: 5em;
}

.swappy-link {
	text-decoration: underline;
	cursor: pointer;
}

.swappy-flex {
	position: absolute;
	width: 100%;
	top: 5em;

	transition-property: top;
	transition-duration: 0.25s;
	transition-timing-function: ease-in-out;
}

.swap-up {
	top: 1em;
}
</style>
<template>
	<div class="flex h-screen overflow-hidden">
		<!-- Sidebar -->
		<Sidebar :sidebarOpen="sidebarOpen" @close-sidebar="sidebarOpen = false" />

		<!-- Content area -->
		<div
			class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden"
		>
			<main>
				<div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">
					<!-- Page header -->
					<div class="sm:flex sm:justify-between sm:items-center mb-8">
						<!-- Left: Title -->
						<div class="mb-4 sm:mb-0">
							<h1 class="text-2xl md:text-3xl text-slate-800 font-bold">
								Custom Events
							</h1>
							<p class="text-sm mt-1 text-slate-400">
								These are the latest custom events that have occurred on your
								dApp.
							</p>

							<span class="text-sm mt-1 text-slate-400">
								<a
									class="text-ralpurple-500 hover:text-ralpurple-700 hover:cursor-pointer"
									@click.stop="navigateToSupport"
								>
									<svg
										xmlns="http://www.w3.org/2000/svg"
										height="16"
										width="16"
										viewBox="0 0 48 48"
										class="inline-flex mr-1"
									>
										<g fill="#212121">
											<path
												d="M45,2H11a1,1,0,0,0-1,1V41a3,3,0,0,1-6,0V26H7a1,1,0,0,0,0-2H3a1,1,0,0,0-1,1V41a5.006,5.006,0,0,0,5,5H41a5.006,5.006,0,0,0,5-5V3A1,1,0,0,0,45,2ZM38,36H18a1,1,0,0,1,0-2H38a1,1,0,0,1,0,2Zm0-8H18a1,1,0,0,1,0-2H38a1,1,0,0,1,0,2Zm1-9a1,1,0,0,1-1,1H18a1,1,0,0,1-1-1V11a1,1,0,0,1,1-1H38a1,1,0,0,1,1,1Z"
												fill="#6635E6"
											></path>
										</g>
									</svg>
									How to get started
								</a>
							</span>
						</div>
					</div>

					<div class="inline-flex rounded-md shadow-sm mb-5" role="group">
						<button
							type="button"
							:class="{ 'text-blue-700': isSankeyChartVisible, 'text-gray-900': !isSankeyChartVisible }"
							@click="isSankeyChartVisible = !isSankeyChartVisible"
							class="py-2 px-4 text-sm font-medium bg-white rounded-l-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-2 focus:ring-blue-700 focus:text-blue-700 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-blue-500 dark:focus:text-white"
						>
							User Flow
						</button>
						<button
							type="button"
							:class="{ 'text-blue-700': !isSankeyChartVisible, 'text-gray-900': isSankeyChartVisible }"
							@click="isSankeyChartVisible = !isSankeyChartVisible"
							class="py-2 px-4 text-sm font-medium bg-white rounded-r-lg border-t border-b border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-2 focus:ring-blue-700 focus:text-blue-700 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-blue-500 dark:focus:text-white"
						>
							Event Frequency
						</button>
					</div>

					<div class = "simple-filter" v-if="!isSankeyChartVisible">
						<CustomEventLineChart
							:widgetData="customEventByDay"
							editMenu="false"
							isLoaded="false"
						/>
					</div>

					<div class = "sankey-container" v-if="isSankeyChartVisible">
						<div
							v-if="!isLoaded"
							class="bg-white p-2 sm:p-4 sm:h-64 rounded-2xl shadow-lg flex flex-col sm:flex-row gap-5 select-none"
						>
							<div class="w-full justify-center content-center mt-12">
								<div
									class="text-xs text-slate-400 flex justify-center content-center"
								>
									Loading custom event types...
								</div>
								<div class="area">
									<div class="loading">
										<span></span>
									</div>
								</div>
							</div>
						</div>
						<CustomEventSankeyChart
							v-if="isLoaded && allEventTypes"
							:eventTypes="allEventTypes"
							:filteredEventTypes="applicationFilteredEventTypes"
							:applications="applications"
							title="User Flow"
							:height="800"
							:width="1200"
							fontSize="12"
						/>
					</div>


					<!-- Segment Table -->
					<div
						class="bg-white shadow-lg rounded-sm border border-slate-200 relative"
					>
						<header
							:class="{ 'swappy-visible': !advancedFilters }"
							class="swappy simple-filter grid grid-flow-col grid-cols-2 sm:auto-cols-max px-5 py-4"
						>
							<form class="col-span-2" @submit.prevent="filterCustomEvents">
								<label
									for="filter"
									class="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white"
									>Search</label
								>
								<div class="relative">
									<div
										class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none"
									>
										<svg
											aria-hidden="true"
											class="w-5 h-5 text-gray-500 dark:text-gray-400"
											fill="none"
											stroke="currentColor"
											viewBox="0 0 24 24"
											xmlns="http://www.w3.org/2000/svg"
										>
											<path
												stroke-linecap="round"
												stroke-linejoin="round"
												stroke-width="2"
												d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
											></path>
										</svg>
									</div>
									<input
										v-model="filterText"
										type="search"
										name="filter"
										id="filter"
										class="block w-full p-4 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
										placeholder="Search custom events..."
										autofocus
									/>
								</div>
							</form>
						</header>
						<div
							class="flex justify-between swappy-flex"
							:class="{ 'swap-up': advancedFilters }"
						>
							<h2
								class="col-span-1 justify-start text-sm font-semibold text-slate-800 px-5 py-2"
								v-text="countText"
							></h2>
							<a
								@click="toggleAdvancedFilters()"
								class="swappy-link px-5 py-2"
								v-text="
									advancedFilters ? 'Back to Simple Filter' : 'Advanced Filters'
								"
							></a>
						</div>
						<div class="rounded-sm border border-slate-200">
							<div>
								<div class="overflow-x-auto">
									<table class="table-auto w-full divide-y divide-slate-200">
										<thead
											class="text-xs font-semibold uppercase text-slate-500 bg-slate-50"
										>
											<tr>
												<th class="px-2 first:pl-5 last:pr-5 py-3">
													<div
														:class="{ 'swappy-visible': !advancedFilters }"
														class="swappy flex items-center"
													>
														<div class="text-center">Event Type</div>
													</div>

													<form
														:class="{ 'swappy-visible': advancedFilters }"
														class="swappy"
														@submit.prevent
													>
														<label
															for="event-type"
															class="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white"
															>Event Type</label
														>
														<div class="relative">
															<div
																class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none"
															>
																<svg
																	aria-hidden="true"
																	class="w-5 h-5 text-gray-500 dark:text-gray-400"
																	fill="none"
																	stroke="currentColor"
																	viewBox="0 0 24 24"
																	xmlns="http://www.w3.org/2000/svg"
																>
																	<path
																		stroke-linecap="round"
																		stroke-linejoin="round"
																		stroke-width="2"
																		d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
																	></path>
																</svg>
															</div>
															<input
																v-model="selectedEventType"
																list="event-type-list"
																type="search"
																id="event-type"
																class="show-clear block w-full p-4 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
																placeholder="Event Type"
															/>
															<datalist id="event-type-list">
																<option
																	v-for="eventType in eventTypes"
																	v-text="eventType"
																></option>
															</datalist>
														</div>
													</form>
												</th>
												<th class="px-2 first:pl-5 last:pr-5 py-3">
													<div
														:class="{ 'swappy-visible': !advancedFilters }"
														class="swappy text-left"
													>
														Address
													</div>

													<form
														:class="{ 'swappy-visible': advancedFilters }"
														class="swappy"
														@submit.prevent
													>
														<label
															for="address"
															class="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white"
															>Address</label
														>
														<div class="relative">
															<div
																class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none"
															>
																<svg
																	aria-hidden="true"
																	class="w-5 h-5 text-gray-500 dark:text-gray-400"
																	fill="none"
																	stroke="currentColor"
																	viewBox="0 0 24 24"
																	xmlns="http://www.w3.org/2000/svg"
																>
																	<path
																		stroke-linecap="round"
																		stroke-linejoin="round"
																		stroke-width="2"
																		d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
																	></path>
																</svg>
															</div>
															<input
																v-model="selectedAddress"
																list="address-list"
																type="search"
																id="address"
																class="show-clear block w-full p-4 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
																placeholder="Address"
															/>
															<datalist id="address-list">
																<option
																	v-for="address in addresses"
																	v-text="address"
																></option>
															</datalist>
														</div>
													</form>
												</th>
												<th class="px-2 first:pl-5 last:pr-5 py-3">
													<div
														:class="{ 'swappy-visible': !advancedFilters }"
														class="swappy text-left"
													>
														Date
													</div>

													<form
														:class="{ 'swappy-visible': advancedFilters }"
														class="swappy"
														@submit.prevent
													>
														<label
															for="date"
															class="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white"
															>Date</label
														>
														<div class="relative">
															<div
																class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none"
															>
																<svg
																	aria-hidden="true"
																	class="w-5 h-5 text-gray-500 dark:text-gray-400"
																	fill="none"
																	stroke="currentColor"
																	viewBox="0 0 24 24"
																	xmlns="http://www.w3.org/2000/svg"
																>
																	<path
																		stroke-linecap="round"
																		stroke-linejoin="round"
																		stroke-width="2"
																		d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
																	></path>
																</svg>
															</div>
															<input
																v-model="selectedDate"
																list="date-list"
																type="search"
																id="date"
																class="show-clear block w-full p-4 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
																placeholder="Date"
															/>
															<datalist id="date-list">
																<option
																	v-for="date in dates"
																	v-text="date"
																></option>
															</datalist>
														</div>
													</form>
												</th>
												<th class="px-2 first:pl-5 last:pr-5 py-3">
													<div
														:class="{ 'swappy-visible': !advancedFilters }"
														class="swappy text-left"
													>
														Application
													</div>
													<form
														:class="{ 'swappy-visible': advancedFilters }"
														class="swappy"
														@submit.prevent
													>
														<label
															for="application"
															class="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white"
														>Application</label>
														<div class="relative">
															<div
																class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none"
															>
																<svg
																	aria-hidden="true"
																	class="w-5 h-5 text-gray-500 dark:text-gray-400"
																	fill="none"
																	stroke="currentColor"
																	viewBox="0 0 24 24"
																	xmlns="http://www.w3.org/2000/svg"
																>
																	<path
																		stroke-linecap="round"
																		stroke-linejoin="round"
																		stroke-width="2"
																		d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
																	></path>
																</svg>
															</div>
															<input
																v-model="selectedApplication"
																list="app-list"
																type="search"
																id="application"
																class="show-clear block w-full p-4 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
																placeholder="Application"
															/>
															<datalist id="app-list">
																<option
																	v-for="application in applications"
																	v-text="application"
																></option>
															</datalist>
														</div>
													</form>
												</th>
												<th class="px-2 first:pl-5 last:pr-5 py-3">
													<div
														:class="{ 'swappy-visible': !advancedFilters }"
														class="swappy text-left"
													>
														Event Data
													</div>
													<form
														:class="{ 'swappy-visible': advancedFilters }"
														class="swappy"
														@submit.prevent
													>
														<label
															for="eventData"
															class="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white"
														>Event Data</label>
														<div class="relative">
															<div
																class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none"
															>
																<svg
																	aria-hidden="true"
																	class="w-5 h-5 text-gray-500 dark:text-gray-400"
																	fill="none"
																	stroke="currentColor"
																	viewBox="0 0 24 24"
																	xmlns="http://www.w3.org/2000/svg"
																>
																	<path
																		stroke-linecap="round"
																		stroke-linejoin="round"
																		stroke-width="2"
																		d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
																	></path>
																</svg>
															</div>
															<input
																v-model="selectedEventData"
																list="app-list"
																type="search"
																id="eventData"
																class="show-clear block w-full p-4 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
																placeholder="Event Data"
															/>
															<datalist id="app-list">
																<option
																	v-for="evtData in eventData"
																	v-text="evtData"
																></option>
															</datalist>
														</div>
													</form>
												</th>
											</tr>
										</thead>
										<tbody class="text-sm" v-if="customEvents.length > 0">
											<tr v-for="event in customEvents">
												<td
													class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap"
												>
													<div class="flex items-center text-slate-800">
														<div class="font-medium text-slate-800">
															<a @click.stop="">{{ event.eventType }}</a>
														</div>
													</div>
												</td>
												<td
													class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap"
												>
													<div class="font-medium text-slate-800">
														<a
															@click.stop="navigateToWallet(event.address)"
															class="text-ralpurple-500 hover:cursor-pointer"
														>
															{{ event.address }}
														</a>
													</div>
												</td>
												<td
													class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap"
												>
													<div class="text-left">{{ event.timestamp }}</div>
												</td>
												<td
													class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap"
												>
													<div class="text-left">{{ event.application }}</div>
												</td>
												<td
													class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap"
												>
													<div v-if="event.dataType == 'json'" class="text-left">
														{{ prettyJSON(event.data) }}
													</div>
													<div v-else class="text-left">{{ event.data }}</div>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
						<div
							v-if="!isLoaded"
							class="bg-white p-2 sm:p-4 sm:h-64 rounded-2xl shadow-lg flex flex-col sm:flex-row gap-5 select-none"
						>
							<div class="w-full justify-center content-center mt-12">
								<div
									class="text-xs text-slate-400 flex justify-center content-center"
								>
									Loading custom events...
								</div>
								<div class="area">
									<div class="loading">
										<span></span>
									</div>
								</div>
							</div>
						</div>
						<div
							class="py-5 text-center text-sm text-slate-800"
							v-if="isLoaded && !dataAvailable"
						>
							No Custom Events Found
						</div>
					</div>
					<!-- End -->
				</div>
			</main>
		</div>
	</div>
</template>

<script>
import { ref } from "vue";
import Notification from "../../components/Notification.vue";
import PaginationNumeric from "../../components/PaginationNumeric.vue";
import SearchForm from "../../components/SearchForm.vue";
import moment from 'moment';
import _ from 'underscore';
import Header from "../../partials/Header.vue";
import Sidebar from "../../partials/Sidebar.vue";
import CustomEventLineChart from "../../partials/analytics/CustomEvents/CustomEventLineChart.vue";
import CustomEventSankeyChart from "../../partials/analytics/CustomEvents/CustomEventSankeyChart.vue";
import { getCustomEventByDay, getCustomEvents, getCustomEventTypes } from "../../services/custom-events";
import { tailwindConfig } from '../../utils/Utils';

let orgId;

export default {
	name: "CustomEventsOverview",
	components: {
		Sidebar,
		Header,
		SearchForm,
		PaginationNumeric,
		Notification,
		CustomEventLineChart,
		CustomEventSankeyChart
	},
	data() {
		return {
			allCustomEvents: [],
			totalCount: 0,
			advancedFilters: false,
			hasFiltered: false,
			hasAdvancedFiltered: false,
			customEvents: [],
			selectedEventType: null,
			selectedAddress: null,
			selectedDate: null,
			selectedEventType: null,
			selectedEventData: null,
			selectedApplication: null,
			isDropdownOpen: false,
			isAdvancedOpen: false,
			customEventByDay: {
				title: 'Events Per Day',
				type: 'LINE',
				id: 99,
				apiName: 'REVENUE',
				infoLabel: 'nothing',
				projectId: 99,
				size: '3',
				inputs: 'widgetData.inputs',
				XAxisInputLabel: 'widgetData.XAxisInputLabel',
				inputSummary: 'widgetData.inputSummary',
				time: 'widgetData.time',
				isLoaded: false
			},
			eventTypes: [],
			allEventTypes: [],
			applicationFilteredEventTypes: [],
			addresses: [],
			dates: [],
			applications: [],
			eventData: [],
			filterText: "",
			isLoaded: false,
			dataAvailable: false,
			countText: "",
			isSankeyChartVisible: true,
		};
	},
	setup() {
		const sidebarOpen = ref(false);
		const notificationInfoOpen = ref(true);

		return {
			sidebarOpen,
			notificationInfoOpen,
		};
	},
	watch: {
		filterText: function (filterText, oldFilterText) {
			if (!this.hasFiltered) {
				this.hasFiltered = true;
			}

			this.customEvents = this.allCustomEvents.filter(
				(x) =>
					x.eventType.includes(filterText) ||
					x.address.includes(filterText) ||
					x.timestamp.includes(filterText) ||
					x.application?.includes(filterText) ||
					x.data?.includes(filterText)
			);
			this.updateCountText();
		},
		selectedEventType: function (filter, oldFilter) {
			this.customEvents = this.allCustomEvents.filter(
				(x) =>
					x.eventType.includes(this.selectedEventType || "") &&
					x.address.includes(this.selectedAddress || "") &&
					x.timestamp.includes(this.selectedDate || "") &&
					x.application?.includes(this.selectedApplication || "") &&
					x.data?.includes(this.selectedEventData || "")
			);
			this.updateCountText();
		},
		selectedAddress: function (filter, oldFilter) {
			this.customEvents = this.allCustomEvents.filter(
				(x) =>
					x.eventType.includes(this.selectedEventType || "") &&
					x.address.includes(this.selectedAddress || "") &&
					x.timestamp.includes(this.selectedDate || "") &&
					x.application?.includes(this.selectedApplication || "") &&
					x.data?.includes(this.selectedEventData || "")
			);
			this.updateCountText();
		},
		selectedDate: function (filter, oldFilter) {
			this.customEvents = this.allCustomEvents.filter(
				(x) =>
					x.eventType.includes(this.selectedEventType || "") &&
					x.address.includes(this.selectedAddress || "") &&
					x.timestamp.includes(this.selectedDate || "") &&
					x.application?.includes(this.selectedApplication || "") &&
					x.data?.includes(this.selectedEventData || "")
			);
			this.updateCountText();
		},
		selectedApplication: function (filter, oldFilter) {
			this.customEvents = this.allCustomEvents.filter(
				(x) =>
					x.eventType.includes(this.selectedEventType || "") &&
					x.address.includes(this.selectedAddress || "") &&
					x.timestamp.includes(this.selectedDate || "") &&
					x.application?.includes(this.selectedApplication || "") &&
					x.data?.includes(this.selectedEventData || "")
			);
			this.updateCountText();
		},
		selectedEventData: function (filter, oldFilter) {
			this.customEvents = this.allCustomEvents.filter(
				(x) =>
					x.eventType.includes(this.selectedEventType || "") &&
					x.address.includes(this.selectedAddress || "") &&
					x.timestamp.includes(this.selectedDate || "") &&
					x.application?.includes(this.selectedApplication || "") &&
					x.data?.includes(this.selectedEventData || "")
			);
			this.updateCountText();
		},
	},
	methods: {
		prettyJSON(dataString) {
			try {
				const jsonData = JSON.parse(JSON.parse(dataString));
				return JSON.stringify(jsonData, null, 2);
			} catch(e) {
				return dataString;
			}
		},
		navigateToWallet(address) {
			let routeData = this.$router.resolve({
				name: "WalletProfile",
				params: { address },
			});
			window.open(routeData.href, "_blank");
		},
		navigateToSupport() {
			this.$router.push({
				path: "/support/marketing-attribution",
			});
		},
		toggleAdvancedFilters() {
			if (!this.hasAdvancedFiltered) {
				this.hasAdvancedFiltered = true;
			}

			this.advancedFilters = !this.advancedFilters;

			if (this.advancedFilters) {
				this.customEvents = this.allCustomEvents.filter(
					(x) =>
						x.eventType.includes(this.selectedEventType || "") &&
						x.address.includes(this.selectedAddress || "") &&
						x.timestamp.includes(this.selectedDate || "") &&
						x.application.includes(this.selectedApplication || "")
				);
			} else {
				this.customEvents = this.allCustomEvents.filter(
					(x) =>
						x.eventType.includes(this.filterText) ||
						x.address.includes(this.filterText) ||
						x.timestamp.includes(this.filterText) ||
						x.application.includes(this.filterText)

				);
			}

			this.updateCountText();
		},
		setupLineChart(data) {
			let colors = [
				tailwindConfig().theme.colors.indigo[500],
				tailwindConfig().theme.colors.sky[400],
				tailwindConfig().theme.colors.indigo[800],
				tailwindConfig().theme.colors.emerald[800],
				tailwindConfig().theme.colors.blue[800],
				tailwindConfig().theme.colors.orange[500],
				tailwindConfig().theme.colors.lime[500],
				tailwindConfig().theme.colors.pink[700],
				tailwindConfig().theme.colors.slate[500],
				tailwindConfig().theme.colors.gray[400],
				tailwindConfig().theme.colors.zinc[800],
				tailwindConfig().theme.colors.red[800],
				tailwindConfig().theme.colors.amber[800],
				tailwindConfig().theme.colors.teal[500],
				tailwindConfig().theme.colors.cyan[500],
				tailwindConfig().theme.colors.violet[700],
				tailwindConfig().theme.colors.indigo[200],
				tailwindConfig().theme.colors.sky[200],
				tailwindConfig().theme.colors.indigo[400],
				tailwindConfig().theme.colors.emerald[400],
				tailwindConfig().theme.colors.blue[400],
				tailwindConfig().theme.colors.orange[200],
				tailwindConfig().theme.colors.lime[200],
				tailwindConfig().theme.colors.pink[300],
				tailwindConfig().theme.colors.indigo[900],
				tailwindConfig().theme.colors.sky[900],
				tailwindConfig().theme.colors.indigo[900],
				tailwindConfig().theme.colors.emerald[900],
				tailwindConfig().theme.colors.blue[900],
				tailwindConfig().theme.colors.orange[900],
				tailwindConfig().theme.colors.lime[900],
				tailwindConfig().theme.colors.pink[900],
				tailwindConfig().theme.colors.slate[200],
				tailwindConfig().theme.colors.gray[200],
				tailwindConfig().theme.colors.zinc[200],
				tailwindConfig().theme.colors.red[200],
				tailwindConfig().theme.colors.amber[200],
				tailwindConfig().theme.colors.teal[200],
				tailwindConfig().theme.colors.cyan[200],
				tailwindConfig().theme.colors.violet[200],
				tailwindConfig().theme.colors.slate[400],
				tailwindConfig().theme.colors.gray[400],
				tailwindConfig().theme.colors.zinc[400],
				tailwindConfig().theme.colors.red[400],
				tailwindConfig().theme.colors.amber[400],
				tailwindConfig().theme.colors.teal[400],
				tailwindConfig().theme.colors.cyan[400],
				tailwindConfig().theme.colors.violet[400],
				tailwindConfig().theme.colors.slate[600],
				tailwindConfig().theme.colors.gray[600],
				tailwindConfig().theme.colors.zinc[600],
				tailwindConfig().theme.colors.red[600],
				tailwindConfig().theme.colors.amber[600],
				tailwindConfig().theme.colors.teal[600],
				tailwindConfig().theme.colors.cyan[600],
				tailwindConfig().theme.colors.violet[600],
			];
			console.log('SETUP LINE CHART ' + JSON.stringify(data));
			let items = data.body.Items;
			if(items == undefined) return undefined;
			let items_data = items[0].data;
			if(items_data == undefined) return undefined;
			let labels = [];
			let event_types = [];
			for (let index = 0; index < items_data.length; index++) {
				labels.push(items_data[index].day);
			}

			labels = _.uniq(labels);

			console.log("Final labels: " + labels);

			for (let index = 0; index < items_data.length; index++) {
				event_types.push(items_data[index].event_type);
			}

			event_types = _.uniq(event_types);

			//We want a dataset for each event_type, sorted by day
			let datasets = [];
			for (let i = 0; i < event_types.length; i++) {
				let event_type = event_types[i];
				let data = [];
				for (let index = 0; index < labels.length; index++) {
					let day = labels[index];
					let count = 0;
					for (let index = 0; index < items_data.length; index++) {
						if (items_data[index].day == day && items_data[index].event_type == event_type) {
							count = items_data[index].count;
						}
					}
					data.push(count);
				}
				datasets.push({
					label: event_type,
					data: data,
					borderColor: colors[i],
					backgroundColor: colors[i],
					hoverBackgroundColor: colors[i],
					hoverBorderColor: tailwindConfig().theme.colors.white,
					fill: false,
					borderWidth: 2,
					tension: 0,
					pointRadius: 0,
					pointHoverRadius: 3,
					clip: 20,
				});
			}

			let finalLabels = [];
			for(let i = 0; i < labels.length; i++) {
				finalLabels.push(moment(labels[i]).format('MM-DD-YY'));
			}

			let chartData = {
				labels: finalLabels,
				datasets: datasets,
			};

			console.log("Final chart data: " + JSON.stringify(chartData));
			return chartData;
		},
		updateCountText() {
			this.countText =
				this.customEvents.length === this.allCustomEvents.length
					? this.allCustomEvents.length == this.totalCount
						? `Showing all ${
								this.customEvents.length > 1 ? this.customEvents.length : ""
						} ${this.customEvents.length === 1 ? "event" : "events"}`
						: `Showing the ${
								this.customEvents.length > 1 ? this.customEvents.length : ""
						} most recently streamed ${this.customEvents.length === 1 ? "event" : "events"} (out of ${this.totalCount} total ${this.totalCount == 1 ? "event" : "events"})`
					: this.allCustomEvents.length == this.totalCount
						? `Showing ${this.customEvents.length}  ${
								this.customEvents.length === 1 ? "event" : "events"
						} out of all ${
								this.allCustomEvents.length > 1 ? this.allCustomEvents.length : ""
						} ${
								this.allCustomEvents.length === 1 ? "event" : "events"
						} `
						: `Showing ${this.customEvents.length}  ${
								this.customEvents.length === 1 ? "event" : "events"
						} out of the ${
								this.allCustomEvents.length > 1 ? this.allCustomEvents.length : ""
						} most recent ${
								this.allCustomEvents.length === 1 ? "event" : "events"
						} (out of ${this.totalCount} total ${this.totalCount == 1 ? "event" : "events"})`;
		}
	},
	computed: {},
	async mounted() {
		orgId = Number(localStorage.getItem("userOrgId")) || 1;

		const [data, totalCountData, allEventTypes, eventsByDay] = await Promise.all([
			getCustomEvents(false),
			getCustomEvents(true),
			getCustomEventTypes(),
			getCustomEventByDay(),
		]);

		this.customEventByDay.chartData = this.setupLineChart(eventsByDay);
		this.allEventTypes = allEventTypes.body;

		this.totalCount = totalCountData.body[0];
		console.log(`events: ${JSON.stringify(data)}`);
		console.log(`eventsByDay: ${JSON.stringify(eventsByDay)}`);
		if (data.body && data.body.length) {
			this.allCustomEvents = data.body.map((evt) => {
				return {
					eventType: evt.eventType,
					address: evt.address,
					timestamp: new Date(evt.timestamp).toLocaleDateString(),
					application: evt.applicationId,
					data: evt.data && evt.data !== 'null' ? evt.data : "",
					dataType: evt.dataType,
				};
			});
			this.eventTypes = Array.from(
				new Set(this.allCustomEvents.map((x) => x.eventType))
			);
			this.addresses = Array.from(
				new Set(this.allCustomEvents.map((x) => x.address))
			);
			this.dates = Array.from(
				new Set(this.allCustomEvents.map((x) => x.timestamp))
			);
			this.applications = Array.from(
				new Set(this.allCustomEvents.filter((x) => x.application).map((x) => x.application))
			);
			this.applicationFilteredEventTypes = Array.from(
				new Set(this.allCustomEvents.filter((x) => x.application).map((x) => x.eventType))
			);
			this.eventData = Array.from(
				new Set(this.allCustomEvents.filter((x) => x.data).map((x) => x.data))
			);
			this.selectedEventType = null;
			this.customEvents = [...this.allCustomEvents];
			this.updateCountText();
			this.dataAvailable = true;
			if (localStorage.getItem("custom_events") == "false") {
				const orgService = await import("../../services/organization.js");
				await orgService.updateOrgInteraction(
					orgId,
					"custom_events",
					Date.now(),
					JSON.parse(localStorage.getItem("interactionData"))
				);
				localStorage.setItem("custom_events", true);
			}
		} else {
			this.dataAvailable = false;
		}
		this.isLoaded = true;
	},
};
</script>
