<template>
    <tr class="hover:bg-slate-100">
      <td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
        <div class="flex items-center text-slate-800">
          <div class="text-slate-800"><a :href="campaignLink" class="text-ralpurple-500 font-semibold">{{attribution.name}}</a></div>
        </div>
      </td>
      <td class="px-2 py-3 whitespace-nowrap">
        <div class="grid justify-items-center">
          <div class="inline-flex font-medium bg-emerald-100 text-emerald-600 rounded-full text-center px-2.5 py-0.5" v-if="attribution.status == 'VERIFIED'">Verified</div>
          <div class="inline-flex font-medium bg-amber-100 text-amber-600 rounded-full text-center px-2.5 py-0.5" v-else-if="attribution.status == 'UNVERIFIED'">Unverified</div>
        </div>
      </td>
      <td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
        <div
          v-if="!isLoadingAdditionalData"
          class="text-center"
        >{{attribution.totalViews}}</div>

        <div
          v-if="isLoadingAdditionalData"
          class="bg-gray-200 w-full animate-pulse h-4 rounded-2xl"
        ></div>
      </td>
      <td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
        <div
          v-if="!isLoadingAdditionalData"
          class="text-center"
        >{{attribution.connections}}</div>

        <div
          v-if="isLoadingAdditionalData"
          class="bg-gray-200 w-full animate-pulse h-4 rounded-2xl"
        ></div>
      </td>
      <td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
        <div
          v-if="!isLoadingAdditionalData"
          class="text-center"
        >{{attribution.conversion}}</div>
        <div
          v-if="!isLoadingAdditionalData"
          class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-200"
        >
          <div class="bg-ralapple-500 h-2.5 rounded-full" :style="conversionProgress"></div>
        </div>

        <div
          v-if="isLoadingAdditionalData"
          class="bg-gray-200 w-full animate-pulse h-4 rounded-2xl"
        ></div>
      </td>
      <!-- <td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
        <div class="text-center text-ralapple-500 font-semibold">${{attribution.revenue}}</div>
      </td> -->
    </tr>
</template>

<script>
export default {
  name: 'ExperienceTable',
  props: ['attribution', 'isLoadingAdditionalData'],
  components: {

  },
  computed: {
    campaignLink() {
      return "/campaigns/" + this.attribution.id
    },
    conversionProgress() {
      var convPercent = this.attribution.conversion.replace("%", "");
      convPercent = parseInt(convPercent);
      console.log("Converted percent is " + convPercent);
      if(convPercent > 0 && convPercent <= 5)
        return "width: 5%";
      else
        return "width: " + convPercent + "%";
    }
  },
  setup(props, { emit }) {

    return {

    }
  }
}
</script>
