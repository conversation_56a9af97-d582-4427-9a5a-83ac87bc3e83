<template>
  <tr class="hover:bg-slate-100">
    <td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
      <div class="flex items-center text-slate-800">
        <div class="text-slate-800"><a :href="walletLink"
            class="text-ralpurple-500 font-semibold">{{walletAddressLabel}}</a></div>
      </div>
    </td>
    <td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
      <div class="text-left">{{wallet.type}}</div>
    </td>
    <!--
      <td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
        <div class="text-center">{{wallet.visits}}</div>
      </td>
      -->
    <td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
      <div class="text-center">{{formattedConnections}}</div>
    </td>
    <td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
      <div class="text-center">{{wallet.conversions}}</div>
    </td>
    <td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
      <div class="text-left text-emerald-500">${{wallet.revenue}}</div>
    </td>
    <td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
      <div class="justify-center">
        <div v-if="wallet.channels.includes('twitter')" class="flex inline">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="18px" height="18px">
            <linearGradient id="_osn9zIN2f6RhTsY8WhY4a" x1="10.341" x2="40.798" y1="8.312" y2="38.769"
              gradientUnits="userSpaceOnUse">
              <stop offset="0" stop-color="#2aa4f4" />
              <stop offset="1" stop-color="#007ad9" />
            </linearGradient>
            <path fill="url(#_osn9zIN2f6RhTsY8WhY4a)"
              d="M46.105,11.02c-1.551,0.687-3.219,1.145-4.979,1.362c1.789-1.062,3.166-2.756,3.812-4.758  c-1.674,0.981-3.529,1.702-5.502,2.082C37.86,8.036,35.612,7,33.122,7c-4.783,0-8.661,3.843-8.661,8.582  c0,0.671,0.079,1.324,0.226,1.958c-7.196-0.361-13.579-3.782-17.849-8.974c-0.75,1.269-1.172,2.754-1.172,4.322 c0,2.979,1.525,5.602,3.851,7.147c-1.42-0.043-2.756-0.438-3.926-1.072c0,0.026,0,0.064,0,0.101c0,4.163,2.986,7.63,6.944,8.419 c-0.723,0.198-1.488,0.308-2.276,0.308c-0.559,0-1.104-0.063-1.632-0.158c1.102,3.402,4.299,5.889,8.087,5.963  c-2.964,2.298-6.697,3.674-10.756,3.674c-0.701,0-1.387-0.04-2.065-0.122C7.73,39.577,12.283,41,17.171,41  c15.927,0,24.641-13.079,24.641-24.426c0-0.372-0.012-0.742-0.029-1.108C43.483,14.265,44.948,12.751,46.105,11.02" />
          </svg>
        </div>
        <div v-if="wallet.channels.includes('discord')" class="flex inline">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="18px" height="18px">
            <radialGradient id="La9SoowKGoEUHOnYdJMSEa" cx="24" cy="10.009" r="32.252" gradientUnits="userSpaceOnUse">
              <stop offset="0" stop-color="#8c9eff" />
              <stop offset=".368" stop-color="#889af8" />
              <stop offset=".889" stop-color="#7e8fe6" />
              <stop offset="1" stop-color="#7b8ce1" />
            </radialGradient>
            <path fill="url(#La9SoowKGoEUHOnYdJMSEa)"
              d="M40.107,12.15c-0.065-0.102-0.139-0.182-0.236-0.255c-0.769-0.578-4.671-3.339-9.665-3.875 c-0.01-0.001-0.048-0.003-0.057-0.003c-0.098,0-0.183,0.057-0.224,0.14l-0.269,0.538c0,0-0.001,0-0.001,0 c-0.017,0.033-0.026,0.071-0.026,0.111c0,0.109,0.07,0.202,0.168,0.236c0.006,0.002,0.048,0.011,0.063,0.014  c4.267,1.028,6.863,2.89,9.149,4.945c-4.047-2.066-8.044-4.001-15.009-4.001s-10.961,1.936-15.009,4.001  c2.286-2.055,4.882-3.917,9.149-4.945c0.015-0.004,0.057-0.012,0.063-0.014c0.098-0.034,0.168-0.127,0.168-0.236  c0-0.04-0.009-0.078-0.026-0.111c0,0-0.001,0-0.001,0l-0.269-0.538c-0.041-0.083-0.125-0.14-0.224-0.14 c-0.009,0-0.048,0.002-0.057,0.003c-4.994,0.536-8.896,3.297-9.665,3.875c-0.097,0.073-0.17,0.153-0.236,0.255  c-0.708,1.107-5.049,8.388-5.892,21.632c-0.009,0.142,0.04,0.289,0.135,0.395c4.592,5.144,11.182,5.752,12.588,5.823  c0.167,0.008,0.327-0.065,0.427-0.199l1.282-1.709c0.1-0.134,0.046-0.322-0.111-0.379c-2.705-0.986-5.717-2.7-8.332-5.706 C11.231,34.457,16.12,37,24,37s12.769-2.543,16.009-4.993c-2.616,3.006-5.627,4.719-8.332,5.706  c-0.157,0.057-0.211,0.245-0.111,0.379l1.282,1.709c0.101,0.134,0.26,0.208,0.427,0.199c1.407-0.072,7.996-0.679,12.588-5.823 c0.095-0.106,0.144-0.253,0.135-0.395C45.156,20.538,40.815,13.257,40.107,12.15z" />
            <ellipse cx="30.5" cy="26" opacity=".05" rx="4.5" ry="5" />
            <ellipse cx="30.5" cy="26" opacity=".05" rx="4" ry="4.5" />
            <ellipse cx="30.5" cy="26" fill="#fff" rx="3.5" ry="4" />
            <ellipse cx="17.5" cy="26" opacity=".05" rx="4.5" ry="5" />
            <ellipse cx="17.5" cy="26" opacity=".05" rx="4" ry="4.5" />
            <ellipse cx="17.5" cy="26" fill="#fff" rx="3.5" ry="4" />
          </svg>
        </div>
        <div v-if="wallet.channels.includes('facebook')" class="flex inline">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="18px" height="18px">
            <linearGradient id="Ld6sqrtcxMyckEl6xeDdMa" x1="9.993" x2="40.615" y1="9.993" y2="40.615"
              gradientUnits="userSpaceOnUse">
              <stop offset="0" stop-color="#2aa4f4" />
              <stop offset="1" stop-color="#007ad9" />
            </linearGradient>
            <path fill="url(#Ld6sqrtcxMyckEl6xeDdMa)"
              d="M24,4C12.954,4,4,12.954,4,24s8.954,20,20,20s20-8.954,20-20S35.046,4,24,4z" />
            <path fill="#fff"
              d="M26.707,29.301h5.176l0.813-5.258h-5.989v-2.874c0-2.184,0.714-4.121,2.757-4.121h3.283V12.46 c-0.577-0.078-1.797-0.248-4.102-0.248c-4.814,0-7.636,2.542-7.636,8.334v3.498H16.06v5.258h4.948v14.452 C21.988,43.9,22.981,44,24,44c0.921,0,1.82-0.084,2.707-0.204V29.301z" />
          </svg>
        </div>

        <div v-if="wallet.channels.includes('web')" class="flex inline">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="18px" height="18px">
            <linearGradient id="Ld6sqrtcxMyckEl6xeDdMa" x1="9.993" x2="40.615" y1="9.993" y2="40.615"
              gradientUnits="userSpaceOnUse">
              <stop offset="0" stop-color="#2aa4f4" />
              <stop offset="1" stop-color="#007ad9" />
            </linearGradient>
            <path fill="url(#Ld6sqrtcxMyckEl6xeDdMa)"
              d="M24,4C12.954,4,4,12.954,4,24s8.954,20,20,20s20-8.954,20-20S35.046,4,24,4z" />
            <path fill="#fff"
              d="M26.707,29.301h5.176l0.813-5.258h-5.989v-2.874c0-2.184,0.714-4.121,2.757-4.121h3.283V12.46 c-0.577-0.078-1.797-0.248-4.102-0.248c-4.814,0-7.636,2.542-7.636,8.334v3.498H16.06v5.258h4.948v14.452 C21.988,43.9,22.981,44,24,44c0.921,0,1.82-0.084,2.707-0.204V29.301z" />
          </svg>
        </div>
      </div>
    </td>
    <td class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
      <div class="text-left" v-if="wallet.persona != 'LOADING'">
        <span>{{wallet.persona}}</span>
      </div>
      <div class="text-center" v-else-if="wallet.persona == 'LOADING'">
        <svg class="animate-spin w-4 h-4 fill-current shrink-0" viewBox="0 0 16 16">
          <path
            d="M8 16a7.928 7.928 0 01-3.428-.77l.857-1.807A6.006 6.006 0 0014 8c0-3.309-2.691-6-6-6a6.006 6.006 0 00-5.422 8.572l-1.806.859A7.929 7.929 0 010 8c0-4.411 3.589-8 8-8s8 3.589 8 8-3.589 8-8 8z" />
        </svg>
      </div>
    </td>
  </tr>
</template>

<script>

import { formatThousands } from '../../utils/Utils'

export default {
  name: 'ExperienceTable',
  props: ['wallet'],
  components: {

  },
  computed: {
    walletLink() {
      return '/wallet/' + this.wallet.address
    },
    walletAddressLabel() {
      return this.wallet.address.substring(0, 12) + '...'
    },
    formattedConnections() {
      return formatThousands(this.wallet.connections)
    }
  },
  setup(props, { emit }) {

    return {

    }
  }
}
</script>
