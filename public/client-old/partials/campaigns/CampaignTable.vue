<template>
  <div class="bg-white shadow-lg rounded-sm border border-slate-200 relative">
    <header class="px-5 py-4">
      <h2 class="font-semibold text-slate-800">Campaign Events <span class="text-slate-400 font-medium">2</span></h2>
    </header>
    <div>
        <div class="rounded-sm border border-slate-200">
          <div class="overflow-x-auto">
            <table class="table-auto w-full divide-y divide-slate-200">
              <CampaignItems
                v-for="item in items"
                :key="item.id"
                :item="item"
              />                        
            </table>
          </div>
        </div>
    </div>
  </div>
</template>

<script>
import { ref, watch } from 'vue'
import CampaignItems from './CampaignTableItem.vue'

export default {
  name: 'CampaignTable',
  components: {
    CampaignItems
  },  
  props: ['selectedItems'],
  setup(props, { emit }) { 

    const items = ref([
      {
        id: '0',
        name: 'AMA Campaign #5',
        description: 'Did an AMA with Project Galaxy.',
        type: 'AMA',
        date: '05/28/2022',
        impact_amount: '+249',
        descriptionTitle: 'Excepteur sint occaecat cupidatat.',
        descriptionBody: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam quis. Ut enim ad minim veniam quis. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
      },

      {
        id: '1',
        name: 'Office Hours Discord',
        description: 'Did our 5th office hours discord, focusing on our token specifically.',
        type: 'Webinar',
        date: '05/22/2022',
        impact_amount: '+5',
        descriptionTitle: 'Excepteur sint occaecat cupidatat.',
        descriptionBody: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam quis. Ut enim ad minim veniam quis. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
      },
    ])  

    return {
      items
    }
  }
}
</script>