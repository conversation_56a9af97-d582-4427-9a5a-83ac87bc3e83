<style scoped>
  .list-item {
    list-style: none;
  }

  .list-item.hoverable >>> svg.dropdown-menu-icon,
  .list-item.hoverable >>> h4 {
    opacity: 0%;
    transition-property: opacity, max-height;
    transition-delay: 0.25s;
    transition-duration: 0.25s;
    transition-timing-function: ease-in-out;
  }

  .list-item.hoverable >>> h4 {
    max-height: 0;
  }

  .list-item.hoverable:hover >>> svg.dropdown-menu-icon,
  .list-item.hoverable:hover >>> h4 {
    opacity: 100%;
    max-height: 100px;
  }

  .list-item.hoverable {
    position: relative;
    transition-property: padding-top;
    transition-duration: 0.5s;
    transition-delay: 0.25s;
    transition-timing-function: ease-in-out;
  }

  .hoverable .ribbon-badge {
    position: absolute;
    top: 0em;
    font-weight: bold;
    left: 0;
    width: 100%;
    text-align: center;
    color: white;
    height: 2em;
    line-height: 2em;

    opacity: 0%;
    max-height: 0;
    transition-property: opacity, max-height;
    transition-duration: 0.5s;
    transition-delay: 0.25s;
    transition-timing-function: ease-in-out;
  }

  .list-item.hoverable:hover .ribbon-badge {
    max-height: 100px;
    opacity: 100%;
  }

  .list-item.hoverable:hover{
    padding-top: 2em;
  }
</style>
<template>
  <div
    class="
      list-item
      flex flex-col
      col-span-full
      bg-white
      shadow-md
      rounded-lg
      border border-ralprimary-ultralight
    "
    v-bind:class="{
      hoverable,
      'sm:col-span-3 xl-col-span-3': widget.size == 3,
      'sm:col-span-6 xl-col-span-6': widget.size == 6,
      'sm:col-span-9 xl-col-span-9': widget.size == 9,
      'sm:col-span-12 xl-col-span-12': widget.size == 12,
      'max-h-48': widget.type == 'NUMBER'
    }"
  >
    <UniversalNumberChart
      :projectId="widget.projectId"
      :widgetData="widget"
	  :isMissingDataSource="isMissingDataSource"
      v-if="widget.type == 'NUMBER'"
      @edit-Widget="editWidget"
      @remove-Widget="removeWidget"
	  @refresh-Widget="refreshWidget"
	  @view-Api="viewMetricAPI"
      smallHeader="true"
    />

    <UniversalDoughnutChart
      :projectId="widget.projectId"
      :widgetData="widget"
	  :isMissingDataSource="isMissingDataSource"
      v-if="widget.type == 'DONUT'"
      @edit-Widget="editWidget"
      @remove-Widget="removeWidget"
	  @refresh-Widget="refreshWidget"
	  @view-Api="viewMetricAPI"
    />

    <UniversalPieChart
      :projectId="widget.projectId"
      :widgetData="widget"
	  :isMissingDataSource="isMissingDataSource"
      v-if="widget.type == 'PIE'"
      @edit-Widget="editWidget"
      @remove-Widget="removeWidget"
	  @refresh-Widget="refreshWidget"
	  @view-Api="viewMetricAPI"
    />

    <UniversalTableChart
      :projectId="widget.projectId"
      :widgetData="widget"
	  :isMissingDataSource="isMissingDataSource"
      v-if="widget.type == 'TABLE'"
      @edit-Widget="editWidget"
      @remove-Widget="removeWidget"
	  @refresh-Widget="refreshWidget"
	  @view-Api="viewMetricAPI"
    />

    <UniversalBarChart
      :projectId="widget.projectId"
      :widgetData="widget"
	  :isMissingDataSource="isMissingDataSource"
      v-if="widget.type == 'BAR'"
      @edit-Widget="editWidget"
      @remove-Widget="removeWidget"
	  @refresh-Widget="refreshWidget"
	  @view-Api="viewMetricAPI"
    />

    <UniversalBarChartHorizontal
      :projectId="widget.projectId"
      :widgetData="widget"
	  :isMissingDataSource="isMissingDataSource"
      v-if="widget.type == 'BAR HORIZONTAL'"
      @edit-Widget="editWidget"
      @remove-Widget="removeWidget"
	  @refresh-Widget="refreshWidget"
	  @view-Api="viewMetricAPI"
    />

    <UniversalLineChart
      :projectId="widget.projectId"
      :widgetData="widget"
	  :isMissingDataSource="isMissingDataSource"
      v-if="widget.type == 'LINE'"
      @edit-Widget="editWidget"
      @remove-Widget="removeWidget"
	  @refresh-Widget="refreshWidget"
	  @view-Api="viewMetricAPI"
    />

    <a v-if="ribbonBadge" class="ribbon-badge to-ralocean-500 from-ralpurple-500 bg-gradient-to-r" :href="ribbonBadgeHref">

		<span v-if="isMissingDataSource"><img class="" src="../images/warning-icon.svg" style="display: inline; transform: scale(0.7); filter: invert(98%) sepia(0%) saturate(1000%) hue-rotate(318deg) brightness(1002%) contrast(1002%);" /></span>
      <span v-text="ribbonBadge"></span>
    </a>
  </div>

</template>

<script>
import { defineAsyncComponent } from 'vue';

export default {
  name: 'WidgetLister',
  props: ['widget', 'ribbonBadge', 'ribbonBadgeHref', 'hoverable', 'isMissingDataSource'],
  emits: ['topEditWidget', 'topRemoveWidget', 'topRefreshWidget', 'topViewApi'],
  methods: {
    editWidget(val) {
      this.$emit('topEditWidget', val);
    },
    removeWidget(val) {
      this.$emit('topRemoveWidget', val);
    },
	refreshWidget(val) {
		this.$emit('topRefreshWidget', val);
	},
	viewMetricAPI(val) {
		console.log("Emmiting topViewAPI", val)
		this.$emit('topViewApi', val);
	}
  },
  components: {
    UniversalNumberChart: defineAsyncComponent(() =>
      import('../partials/analytics/Project/UniversalNumberChart.vue'),
    ),
    UniversalDoughnutChart: defineAsyncComponent(() =>
      import('../partials/analytics/Project/UniversalDoughnutChart.vue'),
    ),
    UniversalPieChart: defineAsyncComponent(() =>
      import('../partials/analytics/Project/UniversalPieChart.vue'),
    ),
    UniversalTableChart: defineAsyncComponent(() =>
      import('../partials/analytics/Project/UniversalTableChart.vue'),
    ),
    UniversalBarChart: defineAsyncComponent(() =>
      import('../partials/analytics/Project/UniversalBarChart.vue'),
    ),
    UniversalBarChartHorizontal: defineAsyncComponent(() =>
      import('../partials/analytics/Project/UniversalBarChartHorizontal.vue'),
    ),
    UniversalLineChart: defineAsyncComponent(() =>
      import('../partials/analytics/Project/UniversalLineChart.vue'),
    ),
  },
};
</script>
