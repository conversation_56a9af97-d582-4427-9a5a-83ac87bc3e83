<template>
<div class="flex flex-col col-span-full sm:col-span-6 xl:col-span-4 bg-white shadow-lg rounded-sm border border-slate-200">
  <ReportCardHeader title="Most Popular dApps With Your Users" />

  <RankedChart :data="items" primaryColumnTitle="dApp" secondaryColumnTitle="Activity" categoryIndicator="1" color="orange" />

  <ReportCardFooter reportLinkLabel="View Popular dApps Report" reportLink="#00"/>
</div>
</template>

<script>
import { ref } from 'vue'
import RankedChart from '../../charts/RankedChart.vue'
import ReportCardHeader from '../../components/ReportCardHeader.vue'
import ReportCardFooter from '../../components/ReportCardFooter.vue'
  
export default {
  name: 'UserPopularDapps',
  components: {
    RankedChart,
    ReportCardHeader,
    ReportCardFooter
  },
  
  data() {
    return {
      items: [
      {
        name: 'OpenSea',
        amount: 300000,
        category: 'Marketplace',
      },
      {
        name: 'Uniswap',
        amount: 236000,
        category: 'Exchange',
      },
      {
        name: 'MetaMask Swap',
        amount: 200000,
        category: 'DeFi',
      },
      {
        name: 'Polygon Bridge',
        amount: 103000,
        category: 'DeFi',
      },
      {
        name: 'Cool Pets',
        amount: 71000,
        category: 'Collectibles',
      },
      {
        name: 'MyProtocol',
        amount: 30000,
        category: 'DeFi',
      },
      ]
    }
  }
}
</script>