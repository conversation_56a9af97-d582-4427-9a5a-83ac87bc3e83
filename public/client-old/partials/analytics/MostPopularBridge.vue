<template>
<div class="flex flex-col col-span-full sm:col-span-6 xl:col-span-4 bg-white shadow-lg rounded-sm border border-slate-200">
  <ReportCardHeader title="Most Used Bridge By Your Users" />

  <RankedChart :data="items" primaryColumnTitle="Brdige" secondaryColumnTitle="Volume" categoryIndicator="0" color="purple" />

  <ReportCardFooter reportLinkLabel="Bridge Activity Report" reportLink="#00"/>
</div>
</template>

<script>
import { ref } from 'vue'
import RankedChart from '../../charts/RankedChart.vue'
import ReportCardHeader from '../../components/ReportCardHeader.vue'
import ReportCardFooter from '../../components/ReportCardFooter.vue'
  
export default {
  name: 'MostPopularBridge',
  components: {
    RankedChart,
    ReportCardHeader,
    ReportCardFooter
  },
  
  data() {
    return {
      items: [
      {
        name: 'Wormhole',
        amount: 8200000,
        category: '',
      },
      {
        name: 'Polygon Bridge',
        amount: 4300000,
        category: '',
      },
      {
        name: 'Binance Bridge',
        amount: 1100000,
        category: '',
      },
      {
        name: 'Avalanche Bridge',
        amount: 800000,
        category: '',
      },
      {
        name: 'Allbridge',
        amount: 520000,
        category: '',
      },
      {
        name: 'Other',
        amount: 302000,
        category: '',
      },
      ]
    }
  }
}
</script>