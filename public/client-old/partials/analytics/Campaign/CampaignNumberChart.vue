<template>
  <div
    class="
      flex flex-col
      col-span-full
      bg-white
      shadow-lg
      rounded-sm
      sm:col-span-3
      xl-col-span-3
      border border-slate-200
    "
  >
    <div class="pl-8 pr-8 pt-4 h-48 overflow-hidden" v-if="!isLoaded">
      <div class="bg-gray-200 w-full animate-pulse h-4 rounded-2xl mb-6"></div>
      <div class="bg-gray-200 w-full h-28 rounded-xl animate-pulse">
        <div class="flex justify-center align-middle animate-pulse pt-6">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="h-16 w-16" stroke="#FFFFFF" stroke-width="1">
            <path fill="#FFFFFF" />
            <path fill="#FFFFFF"
              d="M16 7.5a4 4 0 1 0-8 0H6a6 6 0 1 1 10.663 3.776l-7.32 8.723L18 20v2H6v-1.127l9.064-10.802A3.982 3.982 0 0 0 16 7.5z" />
          </svg>
        </div>
      </div>
    </div>
      <ReportCardHeader
        v-if="isLoaded"
        :title="widgetData.title"
        @edit-widget="editWidget"
        @remove-widget="removeWidget"
        :editMenu="editMenu"
      />

<!--
      <div v-if="!isLoaded">
        <div class="bg-gray-200 w-8 h-8 rounded-xl text-center ml-4 mt-4 mb-4 animate-pulse">
          <span class="pt-2 text-2xl text-gray-100 animate-pulse">#</span>
        </div>
      </div>
-->

      <div v-if="isLoaded">
        <NumberChart
          :title="widgetData.title"
          :amount="widgetData.display"
          :infoLabel="widgetData.infoLabel"
          :isLoaded="loader"
          :label="labelSymbol"
        />
      </div>

  </div>
</template>

<script>
import NumberChart from '../../../charts/NumberChart.vue';
import ReportCardHeader from '../../../components/ReportCardHeader.vue';

export default {
  name: 'Persona',
  props: ['campaignId', 'widgetData', 'editMenu', 'isLoaded'],
  emits: ['editWidget', 'removeWidget'],
  components: {
    NumberChart,
    ReportCardHeader,
  },
  methods: {
    editWidget() {
      this.$emit('editWidget', this.widgetData.id);
    },
    removeWidget() {
      this.$emit('removeWidget', this.widgetData.id);
    },
  },
  setup() {
    const val = 0;
    return {
      val,
    };
  },
  data() {
    return {
      labelSymbol: '',
    };
  },
  async mounted() {
    const metrics = await import('../../../services/metrics.js');

    console.log('Widget data came through ' + JSON.stringify(this.widgetData));

    if (this.widgetData.apiName == 'CONNECTIONS') {
      this.val = 83;
    } else if (this.widgetData.apiName == 'CONVERSIONS') {
      this.val = '43';
      this.labelSymbol = '%';
    } else if (this.widgetData.apiName == 'VISITS') {
      this.val = 200;
    } else if (this.widgetData.apiName == 'REVENUE') {
      this.val = 2000;
      this.labelSymbol = '$';
    }

    /*
    const inputs = this.widgetData.inputs;
    console.log(
      'Data retrieved from project at ' +
        this.widgetData.title +
        ' data is ' +
        JSON.stringify(result),
    );

    this.val = result.body;
    if (this.val.length > 0) {
      if (typeof this.val[0].data === 'number') {
        this.val = this.val[0].data;
      } else {
        this.val = this.val[0].data[inputs[0]];
      }
    }
    */

    this.$forceUpdate();
  },
};
</script>
