<template>
  <div
    class="
      flex flex-col
      col-span-full
      bg-white
      shadow-lg
      rounded-sm
      sm:col-span-6
      xl-col-span-6
      border border-slate-200
    "
  >

    <div class="relative pl-8 pr-8 pt-4 h-72 overflow-hidden" v-if="!isLoaded">
      <div class="bg-gray-200 w-full animate-pulse h-4 rounded-2xl mb-6"></div>
      <div class="bg-gray-200 w-full animate-pulse h-full rounded-xl">
        <div class="flex justify-center align-middle animate-pulse pt-8">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="h-36 w-36" stroke="#FFFFFF "
            stroke-width="1">
            <path fill="#FFFFFF" />
            <path fill="#FFFFFF" d="M3 12h2v9H3v-9zm16-4h2v13h-2V8zm-8-6h2v19h-2V2z" />
          </svg>
        </div>
      </div>
    </div>

    <ReportCardHeader
      v-if="isLoaded"
      :title="widgetData.title"
      @edit-widget="editWidget"
      @remove-widget="removeWidget"
      :editMenu="editMenu"
    />

    <BarChart
      v-if="isLoaded"
      :data="widgetData.chartData"
      :totalVal="totalVal"
      totalValLabel="widgetData.infoLabel"
      totalIndicator="0"
      width="389"
      height="260"
    />
  </div>
</template>

<script>
import {ref} from 'vue';
import BarChart from '../../../charts/BarChart.vue';
import ReportCardHeader from '../../../components/ReportCardHeader.vue';

export default {
  name: 'CampaignBarChart',
  props: ['campaignId', 'widgetData','editMenu', 'isLoaded'],
  emits: ['editWidget', 'removeWidget'],
  methods: {
    editWidget() {
      this.$emit('editWidget', this.widgetData.id);
    },
    removeWidget() {
      this.$emit('removeWidget', this.widgetData.id);
    },
  },
  components: {
    BarChart,
    ReportCardHeader,
  },
  setup() {
    const totalVal = ref(0);

    return {
      totalVal,
    };
  },
  watch: {},
  async mounted() {
    const metrics = await import('../../../services/metrics.js');
    this.$forceUpdate();
  },
};
</script>
