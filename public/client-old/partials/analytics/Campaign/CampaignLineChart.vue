<template>
  <div
    class="
      flex flex-col
      col-span-full
      bg-white
      shadow-lg
      rounded-sm
      sm:col-span-6
      xl-col-span-6
      border border-slate-200
    "
  >

    <div class="relative pl-8 pr-8 pt-4 h-72 overflow-hidden" v-if="!isLoaded">
      <div class="bg-gray-200 w-full animate-pulse h-4 rounded-2xl mb-6"></div>
      <div class="bg-gray-200 w-full h-full rounded-xl animate-pulse">
        <div class="flex justify-center align-middle animate-pulse pt-8">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="h-36 w-36" stroke="#FFFFFF "
            stroke-width="1">
            <path fill="#FFFFFF" />
            <path fill="#FFFFFF"
              d="M5 3v16h16v2H3V3h2zm15.293 3.293l1.414 1.414L16 13.414l-3-2.999-4.293 4.292-1.414-1.414L13 7.586l3 2.999 4.293-4.292z" />
          </svg>
        </div>
      </div>
    </div>
    <ReportCardHeader
      v-if="isLoaded"
      :title="widgetData.title"
      @edit-widget="editWidget"
      @remove-widget="removeWidget"
      :editMenu="editMenu"
    />

    <!-- adjustment = inc or dec; summaryBool 1 or 0 -->
    <LineChart
      v-if="isLoaded"
      :data="widgetData.chartData"
      width="595"
      height="248" />
  </div>
</template>

<script>
import {ref} from 'vue';
import _ from 'underscore';
import ReportCardHeader from '../../../components/ReportCardHeader.vue';
import LineChart from '../../../charts/LineChartOverTime.vue';

// Import utilities
import {tailwindConfig} from '../../../utils/Utils';

export default {
  name: 'CampaignLineChart',
  props: ['campaignId', 'widgetData','editMenu', 'isLoaded'],
  emits: ['editWidget', 'removeWidget'],
  components: {
    LineChart,
    ReportCardHeader,
  },
  methods: {
    editWidget() {
      this.$emit('editWidget', this.widgetData.id);
    },
    removeWidget() {
      this.$emit('removeWidget', this.widgetData.id);
    },
  },
  setup() {
    const chartData = ref(null);

    return {
      chartData,
    };
  },
  async mounted() {
    this.chartData = {
      labels: [
        '12-01-2020',
        '01-01-2021',
        '02-01-2021',
        '03-01-2021',
        '04-01-2021',
        '05-01-2021',
        '06-01-2021',
        '07-01-2021',
        '08-01-2021',
        '09-01-2021',
        '10-01-2021',
      ],
      datasets: [
        // Indigo line
        {
          label: 'Visits',
          data: [2200, 1900, 1850, 1700, 1200, 1300, 800, 802, 815, 700, 850],
          borderColor: tailwindConfig().theme.colors.blue[500],
          fill: false,
          borderWidth: 2,
          tension: 0,
          pointRadius: 0,
          pointHoverRadius: 3,
          pointBackgroundColor: tailwindConfig().theme.colors.blue[500],
          clip: 20,
        },
        // Blue line
        {
          label: 'Conversions',
          data: [1200, 1000, 950, 700, 620, 580, 550, 502, 415, 300, 250],
          borderColor: tailwindConfig().theme.colors.indigo[400],
          fill: false,
          borderWidth: 2,
          tension: 0,
          pointRadius: 0,
          pointHoverRadius: 3,
          pointBackgroundColor: tailwindConfig().theme.colors.indigo[400],
          clip: 20,
        },
        // emerald line
        {
          label: 'Connections',
          data: [1500, 1200, 1150, 900, 820, 780, 650, 602, 515, 500, 450],
          borderColor: tailwindConfig().theme.colors.emerald[500],
          fill: false,
          borderWidth: 2,
          tension: 0,
          pointRadius: 0,
          pointHoverRadius: 3,
          pointBackgroundColor: tailwindConfig().theme.colors.emerald[500],
          clip: 20,
        },
      ],
    };

    this.$forceUpdate();
  },
};
</script>
