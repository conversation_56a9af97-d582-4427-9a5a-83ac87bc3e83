<template>
  <div class="col-span-full xl:col-span-4 bg-white shadow-lg rounded-sm border border-slate-200">

    <ReportCardHeader title="Title of Report" />

    <div class="border-t border-slate-200">
      <div class="max-w-2xl m-auto mt-16 mb-16">

        <div class="text-center px-4">
          <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-t from-slate-200 to-slate-100 mb-4">
            <svg class="shrink-0 h-6 w-6" viewBox="0 0 24 24">
              <path class="fill-current text-slate-600" :class="isExactActive && 'text-indigo-500'" d="M20 7a.75.75 0 01-.75-.75 1.5 1.5 0 00-1.5-1.5.75.75 0 110-1.5 1.5 1.5 0 001.5-1.5.75.75 0 111.5 0 1.5 1.5 0 001.5 1.5.75.75 0 110 1.5 1.5 1.5 0 00-1.5 1.5A.75.75 0 0120 7zM4 23a.75.75 0 01-.75-.75 1.5 1.5 0 00-1.5-1.5.75.75 0 110-1.5 1.5 1.5 0 001.5-1.5.75.75 0 111.5 0 1.5 1.5 0 001.5 1.5.75.75 0 110 1.5 1.5 1.5 0 00-1.5 1.5A.75.75 0 014 23z" />
              <path class="fill-current text-slate-400" :class="isExactActive && 'text-indigo-300'" d="M17 23a1 1 0 01-1-1 4 4 0 00-4-4 1 1 0 010-2 4 4 0 004-4 1 1 0 012 0 4 4 0 004 4 1 1 0 010 2 4 4 0 00-4 4 1 1 0 01-1 1zM7 13a1 1 0 01-1-1 4 4 0 00-4-4 1 1 0 110-2 4 4 0 004-4 1 1 0 112 0 4 4 0 004 4 1 1 0 010 2 4 4 0 00-4 4 1 1 0 01-1 1z" />
            </svg>
          </div>
          <div class="mb-6">To begin seeing results, please choose a data source for this report.</div>
          <button class="btn border-slate-200 hover:border-slate-300 text-indigo-500" @click.stop="$emit('openChooseData')">Choose Data Source</button>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import { ref } from 'vue'
import ReportCardHeader from '../../components/ReportCardHeader.vue'
  
export default {
  name: 'SetupStateReport',
  components: {
    ReportCardHeader,
  },
  emits: ['openChooseData']
}
</script>