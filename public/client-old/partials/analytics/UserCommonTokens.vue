<template>
<div class="flex flex-col col-span-full sm:col-span-6 xl:col-span-4 bg-white shadow-lg rounded-sm border border-slate-200">
    <ReportCardHeader title="Common Tokens Across Your Users" />

    <RankedChart :data="items" primaryColumnTitle="Token" secondaryColumnTitle="Value" categoryIndicator="0" color="green" />
    
    <ReportCardFooter reportLinkLabel="Common Tokens Report" reportLink="#00"/>
</div>
</template>

<script>
import { ref } from 'vue'
import RankedChart from '../../charts/RankedChart.vue'
import ReportCardHeader from '../../components/ReportCardHeader.vue'
import ReportCardFooter from '../../components/ReportCardFooter.vue'
  
export default {
  name: 'UserCommonTokens',
  components: {
    RankedChart,
    ReportCardHeader,
    ReportCardFooter
  },
  
  data() {
    return {
      items: [
      {
        name: 'ETH',
        amount: 1200000,
        category: '',
      },
      {
        name: 'USDT',
        amount: 800000,
        category: '',
      },
      {
        name: 'USDC',
        amount: 524000,
        category: '',
      },
      {
        name: 'LUNA',
        amount: 103000,
        category: 'DeFi',
      },
      {
        name: 'MATIC',
        amount: 75000,
        category: '',
      },
      {
        name: 'Other',
        amount: 35000,
        category: '',
      },
      ]
    }
  }
}
</script>