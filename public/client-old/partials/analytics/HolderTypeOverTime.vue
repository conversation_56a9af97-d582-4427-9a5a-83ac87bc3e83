<template>
  <div
    class="
      flex flex-col
      col-span-full
      sm:col-span-8
      bg-white
      shadow-lg
      rounded-sm
      border border-slate-200
    "
  >
    <ReportCardHeader title="Holder Type Over Time" />

    <!-- adjustment = inc or dec; summaryBool 1 or 0 -->
    <LineChart
      :data="chartData"
      totalVal="1200"
      percentVal="15"
      indicator="inc"
      summary="0"
      width="595"
      height="248"
    />

    <ReportCardFooter
      reportLinkLabel="View Holders Over Time Report"
      reportLink="#00"
    />
  </div>
</template>

<script>
import {ref} from 'vue';
import LineChart from '../../charts/LineChartOverTime.vue';
import ReportCardHeader from '../../components/ReportCardHeader.vue';
import ReportCardFooter from '../../components/ReportCardFooter.vue';

// Import utilities
import {tailwindConfig} from '../../utils/Utils';

export default {
  name: 'HolderTypeOverTime',
  components: {
    <PERSON><PERSON><PERSON>,
    Report<PERSON><PERSON><PERSON>eader,
    ReportCardFooter,
  },
  setup() {
    const chartData = ref({
      labels: [
        '12-01-2020',
        '01-01-2021',
        '02-01-2021',
        '03-01-2021',
        '04-01-2021',
        '05-01-2021',
        '06-01-2021',
        '07-01-2021',
        '08-01-2021',
        '09-01-2021',
        '10-01-2021',
        '11-01-2021',
        '12-01-2021',
        '01-01-2022',
        '02-01-2022',
        '03-01-2022',
        '04-01-2022',
        '05-01-2022',
        '06-01-2022',
        '07-01-2022',
        '08-01-2022',
        '09-01-2022',
        '10-01-2022',
        '11-01-2022',
        '12-01-2022',
        '01-01-2023',
      ],
      datasets: [
        // Indigo line
        {
          label: 'Middleweight',
          data: [
            500, 900, 875, 850, 842, 900, 800, 802, 815, 700, 850, 865, 900,
            850, 900, 925, 935, 950, 980, 950, 980, 1000, 1050, 1025, 1100,
            1200,
          ],
          borderColor: tailwindConfig().theme.colors.blue[500],
          fill: false,
          borderWidth: 2,
          tension: 0,
          pointRadius: 0,
          pointHoverRadius: 3,
          pointBackgroundColor: tailwindConfig().theme.colors.blue[500],
          clip: 20,
        },
        // Blue line
        {
          label: 'Whale',
          data: [
            1200, 1000, 800, 550, 542, 600, 500, 402, 415, 400, 550, 565, 700,
            580, 600, 625, 605, 650, 610, 650, 680, 650, 750, 725, 650, 600,
          ],
          borderColor: tailwindConfig().theme.colors.indigo[400],
          fill: false,
          borderWidth: 2,
          tension: 0,
          pointRadius: 0,
          pointHoverRadius: 3,
          pointBackgroundColor: tailwindConfig().theme.colors.indigo[400],
          clip: 20,
        },
        // emerald line
        {
          label: 'Welterweight',
          data: [
            122, 170, 192, 86, 102, 124, 115, 115, 56, 104, 0, 72, 208, 186,
            223, 188, 114, 162, 200, 150, 118, 118, 76, 122, 230, 268,
          ],
          borderColor: tailwindConfig().theme.colors.emerald[500],
          fill: false,
          borderWidth: 2,
          tension: 0,
          pointRadius: 0,
          pointHoverRadius: 3,
          pointBackgroundColor: tailwindConfig().theme.colors.emerald[500],
          clip: 20,
        },
        //Slate line
        {
          label: 'Featherweight',
          data: [
            133, 171, 182, 200, 112, 104, 166, 145, 43, 82, 10, 44, 308, 286,
            123, 288, 214, 262, 100, 250, 218, 218, 56, 112, 130, 168,
          ],
          borderColor: tailwindConfig().theme.colors.slate[500],
          fill: false,
          borderWidth: 2,
          tension: 0,
          pointRadius: 0,
          pointHoverRadius: 3,
          pointBackgroundColor: tailwindConfig().theme.colors.slate[500],
          clip: 20,
        },
      ],
    });

    return {
      chartData,
    };
  },
};
</script>
