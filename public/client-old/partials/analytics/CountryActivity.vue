<template>
  <div class="flex flex-col col-span-full sm:col-span-6 xl:col-span-4 bg-white shadow-lg rounded-sm border border-slate-200">
    <ReportCardHeader title="Activity by Country" />

    <PieChart :data="chartData" totalVal="" totalValLabel="" totalIndicator="0" width="389" height="260" />

    <ReportCardFooter reportLinkLabel="Activity by Country Report" reportLink="#00"/>
  </div>
</template>

<script>
import { ref } from 'vue'
import Pie<PERSON>hart from '../../charts/PieChart.vue'
import ReportCardHeader from '../../components/ReportCardHeader.vue'
import ReportCardFooter from '../../components/ReportCardFooter.vue'

// Import utilities
import { tailwindConfig } from '../../utils/Utils'

export default {
  name: 'UserLostChart',
  components: {
    <PERSON><PERSON><PERSON>,
    ReportCardHeader,
    ReportCardFooter
  },
  setup() {
    const chartData = ref({
      labels: ['USA', 'Canada', 'UK', 'Japan', 'Other'],
      datasets: [
        {
          label: 'Activity by Country',
          data: [
            30000, 55000, 15000, 24000, 44000
          ],
          backgroundColor: [
            tailwindConfig().theme.colors.indigo[500],
            tailwindConfig().theme.colors.sky[400],
            tailwindConfig().theme.colors.rose[500],
            tailwindConfig().theme.colors.emerald[500],
          ],
          hoverBackgroundColor: [
            tailwindConfig().theme.colors.indigo[600],
            tailwindConfig().theme.colors.sky[500],
            tailwindConfig().theme.colors.rose[600],
            tailwindConfig().theme.colors.emerald[600],
          ],
          hoverBorderColor: tailwindConfig().theme.colors.white,
        },
      ],
    })

    return {
      chartData,
    } 
  }
}
</script>