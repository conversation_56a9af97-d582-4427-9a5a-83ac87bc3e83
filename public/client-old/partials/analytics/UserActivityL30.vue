<template>
  <QuickMetric title="User Activity L30" :amount="count" />
</template>

<script>
import QuickMetric from '../../components/QuickMetric.vue'
import { getLifetimeTransactions } from "../../services/metrics"


export default {
  name: 'UserActivityL30',
  components: {
    QuickMetric
  },
  setup() {
    const count = 0
    return {
      count
    }
  },
  async mounted() {
    const lifetimeTransactions = await getLifetimeTransactions('ETH', '******************************************')
    console.log("Data retrieved from metric: " + JSON.stringify(lifetimeTransactions))
    console.log("Count is " + this.count)
    this.count = lifetimeTransactions.body.transaction_count
    console.log("Count is now " + this.count)

    //Test token balances
    const result = await getMetric('ETH', '******************************************', 'ADDRESS_TOKEN_VALUES');
    console.log("Data retrieved from metric token balances: " + JSON.stringify(tokenValues));

    this.$forceUpdate()
  }
}
</script>
