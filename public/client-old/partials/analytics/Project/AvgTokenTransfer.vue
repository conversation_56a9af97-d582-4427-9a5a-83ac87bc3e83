<template>
  <QuickMetric
    title="Avg. Token Transfer Size"
    :amount="val"
    infoLabel="last 30 days"
    :isLoaded="loader"
  />
</template>

<script>
import QuickMetric from '../../../components/QuickMetric.vue';
import {getProjectAvgTokenTransfer} from '../../../services/metrics';

export default {
  name: 'AvgTokenTransfer',
  props: ['projectId'],
  components: {
    QuickMetric,
  },
  setup() {
    const val = 0;
    return {
      val,
    };
  },
  data() {
    return {
      loader: false,
    };
  },
  watch: {
    projectId: async function (newVal, oldVal) {
      const result = await getProjectAvgTokenTransfer('NONE', newVal);
      console.log(
        'Data retrieved from project avg token transfer metric: ' +
          JSON.stringify(result),
      );

      this.val = result.body;
      if (this.val.length > 0) {
        this.val = parseInt(this.val[0].data);
      }
      this.loader = true;

      this.$forceUpdate();
    },
  },
  async mounted() {},
};
</script>
