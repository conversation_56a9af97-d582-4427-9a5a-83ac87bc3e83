<template>
  <QuickMetric
    title="Churn Prediction"
    :amount="val"
    infoLabel="Predicted # of At Risk Users You'll Lose"
    :isLoaded="loader"
  />
</template>

<script>
import QuickMetric from '../../../components/QuickMetric.vue';
import {
  getProjectAtRiskUsers,
  getProjectDormantUsers,
  getProjectUniqueWallets,
} from '../../../services/metrics';

export default {
  name: 'Persona',
  props: ['projectId'],
  components: {
    QuickMetric,
  },
  setup() {
    const val = 0;
    return {
      val,
    };
  },
  data() {
    return {
      loader: false,
    };
  },
  watch: {
    projectId: async function (newVal, oldVal) {
      const resultTotal = await getProjectUniqueWallets('NONE', newVal);
      const resultDormant = await getProjectDormantUsers(
        'NONE',
        newVal,
        undefined,
        'latest',
      );
      const resultAtRisk = await getProjectAtRiskUsers(
        'NONE',
        newVal,
        undefined,
        'latest',
      );

      let resultTotalMetric = resultTotal.body;
      if (resultTotalMetric.length > 0) {
        resultTotalMetric = parseInt(resultTotalMetric[0].data);
      }

      let resultDormantMetric = resultDormant.body;
      if (resultDormantMetric.length > 0) {
        resultDormantMetric = parseInt(resultDormantMetric[0].data);
      }

      let resultAtRiskMetric = resultAtRisk.body;
      if (resultAtRiskMetric.length > 0) {
        resultAtRiskMetric = parseInt(resultAtRiskMetric[0].data);
      }

      var churnRate = resultDormantMetric / resultTotalMetric;

      this.val = resultAtRiskMetric * churnRate;
      this.loader = true;

      this.$forceUpdate();
    },
  },
  async mounted() {},
};
</script>
