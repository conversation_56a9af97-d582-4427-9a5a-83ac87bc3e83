﻿<template>
  <div class="flex flex-col col-span-full sm:col-span-8 bg-white shadow-lg rounded-sm border border-slate-200">
    <ReportCardHeader title="New Users vs. Lost Users" />

    <!-- adjustment = inc or dec; summaryBool 1 or 0 -->
    <LineChart :data="chartData" width="595" height="248" />

  </div>
</template>

<script>
import { ref } from 'vue'
import LineChart from '../../../charts/LineChartOverTimeDay.vue'
import ReportCardFooter from '../../../components/ReportCardFooter.vue'
import ReportCardHeader from '../../../components/ReportCardHeader.vue'
import { getProjectDormantUsers, getProjectNewUsers } from "../../../services/metrics"
// Import utilities
import { buildDate, tailwindConfig } from '../../../utils/Utils'


export default {
  name: 'ProjectNewCustvsLostCust',
  props: ['projectId'],
  components: {
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>eader,
    ReportCardFooter,
  },
  setup() {
    const chartData = ref(null)

    return {
      chartData
    }
  },
  async mounted() {
    const resultNewUsers = await getProjectNewUsers('NONE', this.projectId, '2022-06-08T06:36:37.765Z', new Date())
    console.log("Data retrieved from new users over time: " + JSON.stringify(resultNewUsers))

    const resultDormantUsers = await getProjectDormantUsers('NONE', this.projectId, '2022-06-08T06:36:37.765Z', new Date())
    console.log("Data retrieved from dormant users over time: " + JSON.stringify(resultDormantUsers))

    var colorList = [tailwindConfig().theme.colors.indigo[500],tailwindConfig().theme.colors.blue[400], tailwindConfig().theme.colors.emerald[500],tailwindConfig().theme.colors.slate[500]]

    this.chartData = ref({})
    this.chartData.labels = []
    this.chartData.datasets = []

    var chartLabels = []
    var newUserDataSet = []

    const newUserSortedDataset = resultNewUsers.body.data.sort((a, b) => b.date_processed > a.date_processed ? 1: -1); //Puts our most recent processed date as first in the list
    for(var i = 0; i < newUserSortedDataset.length; i++) {
      var d = new Date(newUserSortedDataset[i].date_processed)

      var dString = buildDate(d)

      //Dupe check
      var dupe = false
      if(i > 0) {
        newUserDataSet.forEach(function(ds) {
          if(ds.date == dString) {
            dupe = true
          }
        })
        if(!dupe) {
          newUserDataSet.push({
            date: dString,
            value: newUserSortedDataset[i].data
          })
        }
      } else {
          newUserDataSet.push({
            date: dString,
            value: newUserSortedDataset[i].data
          })
      }
    }

    var chartNewUserData = []
    for(var i = 0; i < newUserDataSet.length; i++) {
      chartLabels.push(newUserDataSet[i].date)
      chartNewUserData.push(newUserDataSet[i].value)
    }

    this.chartData.labels = chartLabels
    var newDataset1 = {
      label: "New Users",
      data: chartNewUserData,
      borderColor: colorList[0],
      fill: false,
      borderWidth: 2,
      tension: 0,
      pointRadius: 0,
      pointHoverRadius: 3,
      pointBackgroundColor: colorList[0],
      clip: 20
    }
    this.chartData.datasets.push(newDataset1)

    //Dormant Users
    var dormantNewUserDataSet = []
    const dormantSortedDataset = resultDormantUsers.body.data.sort((a, b) => b.date_processed > a.date_processed ? 1: -1); //Puts our most recent processed date as first in the list

    for(var i = 0; i < dormantSortedDataset.length; i++) {
      var d = new Date(dormantSortedDataset[i].date_processed)

     var dString = buildDate(d)

      //Dupe check
      var dupe = false
      if(i > 0) {
        dormantNewUserDataSet.forEach(function(ds) {
          if(ds.date == dString) {
            dupe = true
          }
        })

        if(!dupe) {
          dormantNewUserDataSet.push({
            date: dString,
            value: dormantSortedDataset[i].data
          })
        }
      } else {
          dormantNewUserDataSet.push({
            date: dString,
            value: dormantSortedDataset[i].data
          })
      }
    }

    var dormantChartNewUserData = []
    for(var i = 0; i < dormantNewUserDataSet.length; i++) {
      dormantChartNewUserData.push(dormantNewUserDataSet[i].value)
    }

    var newDataset2 = {
      label: "Lost Users",
      data: dormantChartNewUserData,
      borderColor: colorList[2],
      fill: false,
      borderWidth: 2,
      tension: 0,
      pointRadius: 0,
      pointHoverRadius: 3,
      pointBackgroundColor: colorList[2],
      clip: 20
    }
    this.chartData.datasets.push(newDataset2)

    this.$forceUpdate()
  }
}
</script>
