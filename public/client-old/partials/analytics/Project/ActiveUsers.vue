<template>
  <QuickMetric
    title="Active Users"
    :amount="val"
    infoLabel="<30 days since last interaction"
    :isLoaded="loader"
  />
</template>

<script>
import QuickMetric from '../../../components/QuickMetric.vue';
import { genericMetricCall } from '../../../services/metrics';

export default {
  name: 'Persona',
  props: ['projectId'],
  components: {
    QuickMetric,
  },
  setup() {
    const val = 0;
    return {
      val,
    };
  },
  data() {
    return {
      loader: false,
    };
  },
  watch: {
    projectId: async function (newVal, oldVal) {
      const result = await genericMetricCall(
        'active-users',
        'NONE',
        newVal,
        undefined,
        'latest'
      );
      console.log(
        'Data retrieved from project active users metric: ' +
          JSON.stringify(result),
      );

      this.val = result.body;
      if (this.val.length > 0) {
        this.val = this.val[0].data;
      }
      this.loader = true;

      this.$forceUpdate();
    },
  },
  async mounted() {},
};
</script>
