<template>
  <QuickMetric
    title="New Users"
    :amount="val"
    infoLabel="first interaction last 7"
    :isLoaded="loader"
  />
</template>

<script>
import QuickMetric from '../../../components/QuickMetric.vue';
import {getProjectNewUsers} from '../../../services/metrics';

export default {
  name: 'Persona',
  props: ['projectId'],
  components: {
    QuickMetric,
  },
  setup() {
    const val = 0;
    return {
      val,
    };
  },
  data() {
    return {
      loader: false,
    };
  },
  watch: {
    projectId: async function (newVal, oldVal) {
      const result = await getProjectNewUsers(
        'NONE',
        newVal,
        undefined,
        'latest',
      );
      console.log(
        'Data retrieved from project new users metric: ' +
          JSON.stringify(result),
      );

      this.val = result.body;
      if (this.val.length > 0) {
        this.val = this.val[0].data;
      }
      this.loader = true;

      this.$forceUpdate();
    },
  },
  async mounted() {},
};
</script>
