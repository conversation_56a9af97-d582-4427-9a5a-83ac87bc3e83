<template>
  <div
    class="
      flex flex-col
      col-span-full
      sm:col-span-6
      xl:col-span-4
      bg-white
      shadow-lg
      rounded-sm
      border border-slate-200
    "
  >
    <ReportCardHeader title="Common Tokens Held Across Your Users" />

    <RankedChart
      :data="items"
      primaryColumnTitle="Tokens"
      secondaryColumnTitle="# Of Wallets"
      categoryIndicator="0"
      color="green"
    />
  </div>
</template>

<script>
import {ref} from 'vue';
import RankedChart from '../../../charts/RankedChart.vue';
import ReportCardHeader from '../../../components/ReportCardHeader.vue';
import ReportCardFooter from '../../../components/ReportCardFooter.vue';
import {getProjectCommonTokensHeld} from '../../../services/metrics';

// Import utilities
import {tailwindConfig, arrayBuilder} from '../../../utils/Utils';

export default {
  name: 'CommonTokensHeld',
  props: ['projectId'],
  components: {
    Ranked<PERSON>hart,
    ReportCardHeader,
    ReportCardFooter,
  },
  setup() {
    const chartData = ref(null);

    return {
      chartData,
    };
  },
  async mounted() {
    const result = await getProjectCommonTokensHeld('NONE', this.projectId);
    console.log(
      'Data retrieved from project common tokens ' + JSON.stringify(result),
    );

    var newData = [];
    for (var i = 0; i < result.body[0].data.length; i++) {
      var dataItem = {
        name: result.body[0].data[i].contract_ticker,
        amount: result.body[0].data[i].wallet_count,
      };
      newData.push(dataItem);
    }

    newData.sort((a, b) => parseFloat(b.amount) - parseFloat(a.amount));

    this.items = newData;
    this.$forceUpdate();
  },
};
</script>
