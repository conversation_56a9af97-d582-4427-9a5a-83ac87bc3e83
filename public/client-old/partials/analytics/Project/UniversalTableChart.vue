<template>
	<div class="relative pl-8 pr-8 pt-4 h-72 overflow-hidden" v-if="!isLoaded">
		<div class="bg-gray-200 w-full animate-pulse h-4 rounded-2xl mb-6"></div>
		<div class="bg-gray-200 w-full h-full rounded-xl animate-pulse">
			<div class="flex justify-center align-middle animate-pulse pt-8">
				<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="h-36 w-36" stroke="#FFFFFF "
					stroke-width="1">
					<path fill="#FFFFFF" />
					<path fill="#FFFFFF"
						d="M4 8h16V5H4v3zm10 11v-9h-4v9h4zm2 0h4v-9h-4v9zm-8 0v-9H4v9h4zM3 3h18a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1z" />
				</svg>
			</div>
		</div>
	</div>

	<div class="m-0 p-0" v-if="isLoaded">
		<ReportCardHeader :title="widgetData.title" :widgetId="widgetData.id" :infoLabel="friendlyUpdatedDate"
			:isMissingDataSource="isMissingDataSource" :infoLabel2="friendlyDate" @edit-widget="editWidget"
			@remove-widget="removeWidget" @refresh-widget="refreshWidget" @view-api="viewAPI"/>

		<TableChart :data="chartData" primaryColumnTitle="Top 10 dApps" secondaryColumnTitle="Activity"
			categoryIndicator="1" color="orange" />
	</div>
</template>

<script>
import moment from 'moment';
import { ref } from 'vue';
import TableChart from '../../../charts/TableChart.vue';
import ReportCardHeader from '../../../components/ReportCardHeader.vue';

export default {
	name: 'UniversalDoughtnut',
	props: ['projectId', 'widgetData', 'isMissingDataSource'],
	emits: ['editWidget', 'removeWidget', 'refreshWidget', 'viewApi'],
	components: {
		TableChart,
		ReportCardHeader,
	},
	methods: {
		editWidget() {
			this.$emit('editWidget', this.widgetData.id);
		},
		removeWidget() {
			this.$emit('removeWidget', this.widgetData.id);
		},
		refreshWidget() {
			this.$emit('refreshWidget', this.widgetData.id);
		},
		viewAPI() {
			this.$emit('viewApi', this.apiCall);
		}
	},
	setup() {
		const chartData = ref(null);
		const totalVal = ref(0);
		const apiCall = ref(null);

		return {
			chartData,
			totalVal,
			apiCall,
		};
	},
	data() {
		return {
			isLoaded: false,
			friendlyDate: '',
			friendlyUpdatedDate: ''
		}
	},
	async mounted() {
		const metrics = await import('../../../services/metrics.js');
		let genericMetricAddress = this.widgetData.datasource != 'custom-metrics' ? this.projectId : this.widgetData.customMetricId;
		const result = await metrics.genericMetricCall(
			this.widgetData.apiName,
			'NONE',
			genericMetricAddress,
			undefined,
			'latest',
		);

		this.apiCall = metrics.getMetricAPI(
			this.widgetData.apiName,
			'NONE',
			genericMetricAddress,
			undefined,
			'latest',
		);
		console.log(
			'Data retrieved from universal table chart' + JSON.stringify(result),
		);

		const reportbuilder = await import('../../../services/reportbuilder.js');
		this.friendlyDate = reportbuilder.getFriendlyDateName(
			this.widgetData.time,
		);
		this.friendlyUpdatedDate = result && result.body && result.body[0]
			? `Last Updated: ${moment(result.body[0].date_processed).fromNow()}`
			: '';

		console.log('Widget data ' + JSON.stringify(this.widgetData.inputs));

		if (this.widgetData.inputs.length > 0 && result.body[0] && result.body[0].data) {
			var tableHeaders = [];
			var tableRows = [];
			for (var i = 0; i < result.body[0].data.length; i++) {
				var newRow = [];
				this.widgetData.inputs.forEach(item => {
					newRow.push(result.body[0].data[i][item] === "null" ? "Unknown" : result.body[0].data[i][item]);
				});
				//console.log('New row ' + JSON.stringify(newRow));
				tableRows.push(newRow);
			}

			this.widgetData.inputs.forEach(item => {
				tableHeaders.push(item);
			});

			this.chartData = { headers: tableHeaders, rows: tableRows };
		}

		//console.log('Table rows are ' + JSON.stringify(tableRows));
		//Need to get the friendly named table headers as well

		this.isLoaded = true;
		this.$forceUpdate();
	},
};
</script>
