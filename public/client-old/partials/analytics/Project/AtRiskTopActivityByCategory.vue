<template>
  <div
    class="
      flex flex-col
      col-span-full
      sm:col-span-6
      xl:col-span-4
      bg-white
      shadow-lg
      rounded-sm
      border border-slate-200
    "
  >
    <ReportCardHeader
      title="What Categories Your At Risk Users Are Spending Their Time In"
    />

    <PieChart
      :data="chartData"
      totalVal=""
      totalValLabel=""
      totalIndicator="0"
      width="389"
      height="260"
    />
  </div>
</template>

<script>
import {ref} from 'vue';
import Pie<PERSON><PERSON> from '../../../charts/PieChart.vue';
import ReportCardFooter from '../../../components/ReportCardFooter.vue';
import ReportCardHeader from '../../../components/ReportCardHeader.vue';
import {getProjectAtRiskActivities} from '../../../services/metrics';

// Import utilities
import {tailwindConfig} from '../../../utils/Utils';

export default {
  name: 'AtRiskByCategory',
  props: ['projectId'],
  components: {
    <PERSON><PERSON><PERSON>,
    ReportCardHeader,
    ReportCardFooter,
  },
  setup() {
    const chartData = ref({});

    return {
      chartData,
    };
  },
  async mounted() {
    const result = await getProjectAtRiskActivities('NONE', this.projectId);
    console.log(
      'Data retrieved from project at risk activities by cat ' +
        JSON.stringify(result),
    );

    var categoryCount = [];
    for (var i = 0; i < result.body[0].data.length; i++) {
      if (result.body[0].data[i].category != null) {
        if (result.body[0].data[i].category == '')
          result.body[0].data[i].category = 'Unclassified';

        if (categoryCount > 0) {
          var found = false;
          for (cat in categoryCount) {
            if (cat.key == result.body[0].data[i].category) {
              cat.value += result.body[0].data[i].count;
              found = true;
            }
          }

          if (!found) {
            categoryCount.push({
              key: result.body[0].data[i].category,
              value: result.body[0].data[i].count,
            });
          }
        } else {
          categoryCount.push({
            key: result.body[0].data[i].category,
            value: result.body[0].data[i].count,
          });
        }
      }
    }

    this.chartData = ref({});
    this.chartData.datasets = [];

    var labelList = [];
    var dataList = [];
    let dataListTest = new Map([]);
    for (var i = 0; i < categoryCount.length; i++) {
      if (dataListTest.has(categoryCount[i].key)) {
        dataListTest.set(
          categoryCount[i].key,
          dataListTest.get(categoryCount[i].key) + categoryCount[i].value,
        );
      } else if (!dataListTest.has(categoryCount[i].key)) {
        dataListTest.set(categoryCount[i].key, categoryCount[i].value);
      }
    }

    dataListTest.forEach((value, key) => {
      labelList.push(key);
      dataList.push(value);
    });

    this.chartData.labels = labelList;
    var newDataset = {
      label: 'Categories',
      data: dataList,
      backgroundColor: [
        tailwindConfig().theme.colors.indigo[500],
        tailwindConfig().theme.colors.sky[400],
        tailwindConfig().theme.colors.indigo[800],
        tailwindConfig().theme.colors.emerald[800],
        tailwindConfig().theme.colors.blue[800],
        tailwindConfig().theme.colors.orange[500],
        tailwindConfig().theme.colors.lime[500],
        tailwindConfig().theme.colors.pink[700],
      ],
      hoverBackgroundColor: [
        tailwindConfig().theme.colors.indigo[600],
        tailwindConfig().theme.colors.sky[500],
        tailwindConfig().theme.colors.indigo[900],
        tailwindConfig().theme.colors.emerald[800],
        tailwindConfig().theme.colors.blue[800],
        tailwindConfig().theme.colors.orange[500],
        tailwindConfig().theme.colors.lime[500],
        tailwindConfig().theme.colors.pink[700],
      ],
      hoverBorderColor: tailwindConfig().theme.colors.white,
    };
    this.chartData.datasets.push(newDataset);

    this.$forceUpdate();
  },
};
</script>
