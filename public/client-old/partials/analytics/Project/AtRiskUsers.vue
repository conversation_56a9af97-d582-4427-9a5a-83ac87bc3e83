<template>
  <QuickMetric
    title="At Risk Users"
    :amount="val"
    infoLabel="30 - 60 days since last interaction"
    :isLoaded="loader"
  />
</template>

<script>
import QuickMetric from '../../../components/QuickMetric.vue';
import {getProjectAtRiskUsers} from '../../../services/metrics';

export default {
  name: 'Persona',
  props: ['projectId'],
  components: {
    QuickMetric,
  },
  setup() {
    const val = 0;
    return {
      val,
    };
  },
  data() {
    return {
      loader: false,
    };
  },
  watch: {
    projectId: async function (newVal, oldVal) {
      const result = await getProjectAtRiskUsers('NONE', newVal);
      console.log(
        'Data retrieved from project at risk users metric: ' +
          JSON.stringify(result),
      );

      this.val = result.body;
      if (this.val.length > 0) {
        this.val = this.val[0].data;
      }

      if(this.val == '')
        this.val = '...';

      this.loader = true;

      this.$forceUpdate();
    },
  },
  async mounted() {},
};
</script>
