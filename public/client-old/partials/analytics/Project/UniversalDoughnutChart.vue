<template>
	<div class="relative pl-8 pr-8 pt-4 h-72 overflow-hidden" v-if="!isLoaded">
		<div class="bg-gray-200 w-full animate-pulse h-4 rounded-2xl mb-6"></div>
		<div class="bg-gray-200 w-full h-full rounded-xl animate-pulse">
			<div class="flex justify-center align-middle animate-pulse pt-8">
				<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="h-36 w-36" stroke="#FFFFFF "
					stroke-width="1">
					<path fill="#FFFFFF" />
					<path fill="#FFFFFF"
						d="M11 2.05v2.012C7.054 4.554 4 7.92 4 12c0 4.418 3.582 8 8 8 1.849 0 3.55-.627 4.906-1.68l1.423 1.423C16.605 21.153 14.4 22 12 22 6.477 22 2 17.523 2 12c0-5.185 3.947-9.449 9-9.95zM21.95 13c-.2 2.011-.994 3.847-2.207 5.328l-1.423-1.422c.86-1.107 1.436-2.445 1.618-3.906h2.013zM13.002 2.05c4.724.469 8.48 4.226 8.95 8.95h-2.013c-.451-3.618-3.319-6.486-6.937-6.938V2.049z" />
				</svg>
			</div>
		</div>
	</div>

	<div class="m-0 p-0" v-if="isLoaded">
		<ReportCardHeader :title="widgetData.title" :widgetId="widgetData.id" @edit-widget="editWidget"
			:infoLabel="friendlyUpdatedDate" :isMissingDataSource="isMissingDataSource" :infoLabel2="friendlyDate"
			@remove-widget="removeWidget" @refresh-widget="refreshWidget" @view-api="viewAPI"/>

		<DoughnutChart :data="chartData" :totalVal="totalVal" totalValLabel="widgetData.infoLabel" totalIndicator="0"
			width="389" height="260" />
	</div>
</template>

<script>
import moment from 'moment';
import { ref } from 'vue';
import DoughnutChart from '../../../charts/DoughnutChart.vue';
import ReportCardHeader from '../../../components/ReportCardHeader.vue';
import { genericMetricCall, getMetricAPI } from '../../../services/metrics.js';

// Import utilities
import { tailwindConfig } from '../../../utils/Utils';

export default {
	name: 'UniversalDoughtnut',
	props: ['projectId', 'widgetData', 'isMissingDataSource'],
	emits: ['editWidget', 'removeWidget', 'refreshWidget', 'viewApi'],
	methods: {
		editWidget() {
			this.$emit('editWidget', this.widgetData.id);
		},
		removeWidget() {
			this.$emit('removeWidget', this.widgetData.id);
		},
		refreshWidget() {
			this.$emit('refreshWidget', this.widgetData.id);
		},
		viewAPI() {
			this.$emit('viewApi', this.apiCall);
		}
	},
	components: {
		DoughnutChart,
		ReportCardHeader,
	},
	setup() {
		const chartData = ref(null);
		const totalVal = ref(0);
		const apiCall = ref(null);

		return {
			chartData,
			totalVal,
			apiCall,
		};
	},
	data() {
		return {
			isLoaded: false,
			friendlyDate: '',
		}
	},
	async mounted() {
		let genericMetricAddress = this.widgetData.datasource != 'custom-metrics' ? this.projectId : this.widgetData.customMetricId;
		const result = await genericMetricCall(
			this.widgetData.apiName,
			'NONE',
			genericMetricAddress,
			undefined,
			'latest',
		);

		this.apiCall = getMetricAPI(
			this.widgetData.apiName,
			'NONE',
			genericMetricAddress,
			undefined,
			'latest',
		);
		console.log(
			'Data retrieved from universal doughnut chart' + JSON.stringify(result),
		);

		const reportbuilder = await import('../../../services/reportbuilder.js');
		this.friendlyDate = reportbuilder.getFriendlyDateName(
			this.widgetData.time,
		);
		this.friendlyUpdatedDate = result && result.body && result.body[0]
			? `Last Updated: ${moment(result.body[0].date_processed).fromNow()}`
			: '';

		var labelListPrep = [];
		var dataListPrep = [];

		if (!result.body || !result.body.length || !result.body[0].data) {
			this.isLoaded = true;
			return;
		}

		let withDayIncludedDataset = [];
		let dateIncluded = false;
		let latestDay = null;
		for (var i = 0; i < result.body.length; i++) {
			for (var j = 0; j < result.body[i].data.length; j++) {
				let data = result.body[i].data[j];
				let dataset = {};
				dataset.date_processed = result.body[i].data[j].day;
				dataset.data = data;
				if (!moment(dataset.date_processed).isValid()) {
					console.log("invalid day range skipping")
				}
				//console.log('In loop', dataset);
				//If date is after startdate push to end of array
				dateIncluded = true;
				withDayIncludedDataset.push(dataset);

				if (!latestDay || moment(dataset.date_processed).isAfter(latestDay)) {
					latestDay = moment(dataset.date_processed);
				}
			}
		}
		if (dateIncluded) {
			let latestDayValue = withDayIncludedDataset.filter((item) => {
				return moment(item.date_processed).isSame(latestDay);
			});

			result.body[0].data = [];
			for (let i = 0; i < latestDayValue.length; i++) {
				result.body[0].data.push(latestDayValue[i].data);
			}
		}

		for (var i = 0; i < result.body[0].data.length; i++) {
			const clusterInfo = result.body[0].data[i];
			for (let item in clusterInfo) {
				if (clusterInfo[item] == '') {
					clusterInfo[item] = 'Unclassified';
				}

				if (item == this.widgetData.XAxisInputLabel) {
					labelListPrep.push(clusterInfo[item] === "null" ? "Unknown" : clusterInfo[item]);
				}

				//console.log('comparing: ' + item + ' to ' + this.widgetData.inputs[0]);
				if (item == this.widgetData.inputs[0]) {
					dataListPrep.push(clusterInfo[item]);
				}

				if (this.widgetData.inputSummary != null) {
					if (item == this.widgetData.inputSummary) {
						this.totalVal += parseInt(clusterInfo[item]);
					}
				}
			}
		}

		console.log('dataListPrep: ' + dataListPrep);
		console.log('labelListPrep: ' + labelListPrep);
		//Makes sure we have unique items
		let dataListMap = new Map([]);
		for (var i = 0; i < labelListPrep.length; i++) {
			if (dataListMap.has(labelListPrep[i])) {
				dataListMap.set(
					labelListPrep[i],
					dataListMap.get(labelListPrep[i]) + dataListPrep[i],
				);
			} else if (!dataListMap.has(labelListPrep[i])) {
				dataListMap.set(labelListPrep[i], dataListPrep[i]);
			}
		}

		dataListMap = new Map([...dataListMap.entries()].sort((a, b) => b[1] - a[1]));

		var labelList = [];
		var dataList = [];
		let idx = 0;
		let extraLabel = 'others';
		let extraCount = 0;
		dataListMap.forEach((value, key) => {
			if (idx++ < 20) {
				labelList.push(key);
				dataList.push(value);
			}
			else {
				extraCount += value;
			}
		});
		if (extraCount > 0) {
			this.friendlyUpdatedDate = result && result.body && result.body[0]
			? `Last Updated: ${moment(result.body[0].date_processed).fromNow()} - Constrained to Top 20`
			: '';
		}

		this.chartData = ref({});
		this.chartData.datasets = [];

		this.chartData.labels = labelList;
		//TODO can we move these colors out and import them?
		let colors = [
			tailwindConfig().theme.colors.indigo[500],
			tailwindConfig().theme.colors.sky[400],
			tailwindConfig().theme.colors.indigo[800],
			tailwindConfig().theme.colors.emerald[800],
			tailwindConfig().theme.colors.blue[800],
			tailwindConfig().theme.colors.orange[500],
			tailwindConfig().theme.colors.lime[500],
			tailwindConfig().theme.colors.pink[700],
			tailwindConfig().theme.colors.slate[500],
			tailwindConfig().theme.colors.gray[400],
			tailwindConfig().theme.colors.zinc[800],
			tailwindConfig().theme.colors.red[800],
			tailwindConfig().theme.colors.amber[800],
			tailwindConfig().theme.colors.teal[500],
			tailwindConfig().theme.colors.cyan[500],
			tailwindConfig().theme.colors.violet[700],
			tailwindConfig().theme.colors.indigo[200],
			tailwindConfig().theme.colors.sky[200],
			tailwindConfig().theme.colors.indigo[400],
			tailwindConfig().theme.colors.emerald[400],
			tailwindConfig().theme.colors.blue[400],
			tailwindConfig().theme.colors.orange[200],
			tailwindConfig().theme.colors.lime[200],
			tailwindConfig().theme.colors.pink[300],
			tailwindConfig().theme.colors.indigo[900],
			tailwindConfig().theme.colors.sky[900],
			tailwindConfig().theme.colors.indigo[900],
			tailwindConfig().theme.colors.emerald[900],
			tailwindConfig().theme.colors.blue[900],
			tailwindConfig().theme.colors.orange[900],
			tailwindConfig().theme.colors.lime[900],
			tailwindConfig().theme.colors.pink[900],
			tailwindConfig().theme.colors.slate[200],
			tailwindConfig().theme.colors.gray[200],
			tailwindConfig().theme.colors.zinc[200],
			tailwindConfig().theme.colors.red[200],
			tailwindConfig().theme.colors.amber[200],
			tailwindConfig().theme.colors.teal[200],
			tailwindConfig().theme.colors.cyan[200],
			tailwindConfig().theme.colors.violet[200],
			tailwindConfig().theme.colors.slate[400],
			tailwindConfig().theme.colors.gray[400],
			tailwindConfig().theme.colors.zinc[400],
			tailwindConfig().theme.colors.red[400],
			tailwindConfig().theme.colors.amber[400],
			tailwindConfig().theme.colors.teal[400],
			tailwindConfig().theme.colors.cyan[400],
			tailwindConfig().theme.colors.violet[400],
			tailwindConfig().theme.colors.slate[600],
			tailwindConfig().theme.colors.gray[600],
			tailwindConfig().theme.colors.zinc[600],
			tailwindConfig().theme.colors.red[600],
			tailwindConfig().theme.colors.amber[600],
			tailwindConfig().theme.colors.teal[600],
			tailwindConfig().theme.colors.cyan[600],
			tailwindConfig().theme.colors.violet[600],
		];

		let backgroundColor = [];
		let hoverBackgroundColor = [];
		for(var i = 0; i < dataList.length; i++) {
			backgroundColor.push(colors[i]);
			hoverBackgroundColor.push(colors[i]);
		}

		var newDataset = {
			label: 'Categories',
			data: dataList,
			backgroundColor: backgroundColor,
			hoverBackgroundColor: hoverBackgroundColor,
			hoverBorderColor: tailwindConfig().theme.colors.white,
		};
		console.log(JSON.stringify(newDataset));
		console.log(JSON.stringify(this.chartData));
		this.chartData.datasets.push(newDataset);

		this.isLoaded = true;
		this.$forceUpdate();
	},
};
</script>
