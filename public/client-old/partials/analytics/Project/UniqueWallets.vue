<template>
  <QuickMetric
    title="Total Unique Wallets"
    :amount="val"
    infoLabel="all time"
    :isLoaded="loader"
  />
</template>

<script>
import QuickMetric from '../../../components/QuickMetric.vue';
import {getProjectUniqueWallets} from '../../../services/metrics';

export default {
  name: 'UniqueWallet',
  props: ['projectId'],
  components: {
    QuickMetric,
  },
  setup() {
    const val = 0;
    return {
      val,
    };
  },
  watch: {
    projectId: async function (newVal, oldVal) {
      const result = await getProjectUniqueWallets('NONE', newVal);
      console.log(
        'Data retrieved from projectunique wallet metric: ' +
          JSON.stringify(result),
      );

      this.val = result.body;
      if (this.val.length > 0) {
        this.val = this.val[0].data;
      }
      this.loader = true;

      this.$forceUpdate();
    },
  },
  data() {
    return {
      loader: false,
    };
  },
  async mounted() {},
};
</script>
