<template>

	<div class="relative pl-8 pr-8 pt-4 h-72 overflow-hidden" v-if="!isLoaded">
		<div class="bg-gray-200 w-full animate-pulse h-4 rounded-2xl mb-6"></div>
		<div class="bg-gray-200 w-full h-full rounded-xl animate-pulse">
			<div class="flex justify-center align-middle animate-pulse pt-8">
				<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="h-36 w-36" stroke="#FFFFFF "
					stroke-width="1">
					<path fill="#FFFFFF" />
					<path fill="#FFFFFF"
						d="M5 3v16h16v2H3V3h2zm15.293 3.293l1.414 1.414L16 13.414l-3-2.999-4.293 4.292-1.414-1.414L13 7.586l3 2.999 4.293-4.292z" />
				</svg>
			</div>
		</div>
	</div>

	<div class="m-0 p-0" v-if="isLoaded">
		<ReportCardHeader :title="widgetData.title" :widgetId="widgetData.id" @edit-widget="editWidget"
			:infoLabel2="friendlyDate" :isMissingDataSource="isMissingDataSource" :infoLabel="friendlyUpdatedDate"
			@remove-widget="removeWidget" @refresh-widget="refreshWidget" @view-api="viewAPI" />

		<!-- adjustment = inc or dec; summaryBool 1 or 0 -->
		<LineChart :data="chartData" width="595" height="248" />
	</div>

</template>

<script>
import { isValidES3Identifier } from '@babel/types';
import moment from 'moment';
import _ from 'underscore';
import { ref } from 'vue';
import LineChart from '../../../charts/LineChartOverTime.vue';
import ReportCardHeader from '../../../components/ReportCardHeader.vue';

// Import utilities
import { tailwindConfig } from '../../../utils/Utils';

export default {
	name: 'UniversalLineChart',
	props: ['projectId', 'widgetData', 'isMissingDataSource'],
	emits: ['editWidget', 'removeWidget', 'refreshWidget', 'viewApi'],
	components: {
		LineChart,
		ReportCardHeader,
	},
	methods: {
		editWidget() {
			this.$emit('editWidget', this.widgetData.id);
		},
		removeWidget() {
			this.$emit('removeWidget', this.widgetData.id);
		},
		refreshWidget() {
			this.$emit('refreshWidget', this.widgetData.id);
		},
		viewAPI() {
			console.log("Emitting viewAPI")
			this.$emit('viewApi', this.apiCall);
		}
	},
	setup() {
		const chartData = ref(null);
		const apiCall = ref(null);

		return {
			chartData,
			apiCall
		};
	},
	data() {
		return {
			isLoaded: false,
			friendlyDate: '',
			friendlyUpdatedDate: '',
		}
	},
	async mounted() {
		//Global colors
		let colors = [
			tailwindConfig().theme.colors.indigo[500],
			tailwindConfig().theme.colors.sky[400],
			tailwindConfig().theme.colors.indigo[800],
			tailwindConfig().theme.colors.emerald[800],
			tailwindConfig().theme.colors.blue[800],
			tailwindConfig().theme.colors.orange[500],
			tailwindConfig().theme.colors.lime[500],
			tailwindConfig().theme.colors.pink[700],
			tailwindConfig().theme.colors.slate[500],
			tailwindConfig().theme.colors.gray[400],
			tailwindConfig().theme.colors.zinc[800],
			tailwindConfig().theme.colors.red[800],
			tailwindConfig().theme.colors.amber[800],
			tailwindConfig().theme.colors.teal[500],
			tailwindConfig().theme.colors.cyan[500],
			tailwindConfig().theme.colors.violet[700],
			tailwindConfig().theme.colors.indigo[200],
			tailwindConfig().theme.colors.sky[200],
			tailwindConfig().theme.colors.indigo[400],
			tailwindConfig().theme.colors.emerald[400],
			tailwindConfig().theme.colors.blue[400],
			tailwindConfig().theme.colors.orange[200],
			tailwindConfig().theme.colors.lime[200],
			tailwindConfig().theme.colors.pink[300],
			tailwindConfig().theme.colors.indigo[900],
			tailwindConfig().theme.colors.sky[900],
			tailwindConfig().theme.colors.indigo[900],
			tailwindConfig().theme.colors.emerald[900],
			tailwindConfig().theme.colors.blue[900],
			tailwindConfig().theme.colors.orange[900],
			tailwindConfig().theme.colors.lime[900],
			tailwindConfig().theme.colors.pink[900],
			tailwindConfig().theme.colors.slate[200],
			tailwindConfig().theme.colors.gray[200],
			tailwindConfig().theme.colors.zinc[200],
			tailwindConfig().theme.colors.red[200],
			tailwindConfig().theme.colors.amber[200],
			tailwindConfig().theme.colors.teal[200],
			tailwindConfig().theme.colors.cyan[200],
			tailwindConfig().theme.colors.violet[200],
			tailwindConfig().theme.colors.slate[400],
			tailwindConfig().theme.colors.gray[400],
			tailwindConfig().theme.colors.zinc[400],
			tailwindConfig().theme.colors.red[400],
			tailwindConfig().theme.colors.amber[400],
			tailwindConfig().theme.colors.teal[400],
			tailwindConfig().theme.colors.cyan[400],
			tailwindConfig().theme.colors.violet[400],
			tailwindConfig().theme.colors.slate[600],
			tailwindConfig().theme.colors.gray[600],
			tailwindConfig().theme.colors.zinc[600],
			tailwindConfig().theme.colors.red[600],
			tailwindConfig().theme.colors.amber[600],
			tailwindConfig().theme.colors.teal[600],
			tailwindConfig().theme.colors.cyan[600],
			tailwindConfig().theme.colors.violet[600],
		];
		const metrics = await import('../../../services/metrics.js');
		const reportbuilder = await import('../../../services/reportbuilder.js');

		let startdate, enddate;

		if(this.widgetData.time) {
				let numberOfDays = reportbuilder.getNumberOfDaysFromTimeRange(
				this.widgetData.time,
			);
			this.friendlyDate = reportbuilder.getFriendlyDateName(
				this.widgetData.time,
			);
			enddate = new Date();
			startdate = new Date(
				enddate.getTime() - numberOfDays * 24 * 60 * 60 * 1000,
			);
		}
		else {
			console.log("Start Time: " + this.widgetData.starttime + " End Time: " + this.widgetData.endtime + "")
			startdate = new Date(this.widgetData.starttime || Date.now() - 86400000);
			enddate = new Date(this.widgetData.endtime || Date.now());
			this.friendlyDate = `${startdate.toLocaleDateString()} - ${enddate.toLocaleDateString()}`
		}

		const inputs = this.widgetData.inputs;
		const XAxisInputLabel = this.widgetData.XAxisInputLabel;

		let genericMetricAddress = this.widgetData.datasource != 'custom-metrics' ? this.projectId : this.widgetData.customMetricId;

		this.apiCall = metrics.getMetricAPI(
			this.widgetData.apiName,
			'NONE',
			genericMetricAddress,
			startdate.toISOString(),
			enddate.toISOString());
		const result = await metrics.genericMetricCall(
			this.widgetData.apiName,
			'NONE',
			genericMetricAddress,
			startdate.toISOString(),
			enddate.toISOString(),
		);

		console.log('result.body.length', result.body.length)
		if (result.body.length === 0 || result.body.length === undefined) {
			this.isLoaded = true;
			this.chartData = ref({
				labels: [],
				datasets: [],
			});
			this.$forceUpdate();
			return;
		}

		const latestDate = new Date(result.body.map(x => x.date_processed).sort().reverse()[0]);
		this.friendlyUpdatedDate = `Last Updated: ${moment(latestDate).fromNow()}`;
		console.log(
		  'Data retrieved from universal Line chart' + JSON.stringify(result),
		);

		//This is for the case where we have historically calculated data as part of a single metric, vs using snapshots of each metric run
		let withDayIncludedDataset = [];
		let dateIncluded = false;
		for (var i = 0; i < result.body.length; i++) {
			for (var j = 0; j < result.body[i].data.length; j++) {
				let data = result.body[i].data[j];
				let dataset = {};
				dataset.date_processed = result.body[i].data[j].day;
				dataset.data = data;
				if (!moment(dataset.date_processed).isValid()) {
					//console.log("invalid day range skipping")
				}
				//console.log('In loop', dataset);
				//If date is after startdate push to end of array
				if (new Date(dataset.date_processed) > startdate) {
					//console.log("Pushing", dataset);
					dateIncluded = true;
					withDayIncludedDataset.push(dataset);
				}
				else {
					//console.log("Date is before startdate");
				}
			}
		}

		//console.log('withDayIncludedDataset', JSON.stringify(withDayIncludedDataset));
		//console.log("XAxisInputLabel", XAxisInputLabel)
		let startingData = XAxisInputLabel === 'day' || dateIncluded ? withDayIncludedDataset : result.body;

		//For this chart time is always the Label
		var groups = _.groupBy(startingData, function (item) {
			return moment(item.date_processed).startOf('day').format();
		});

		//console.log('groups: ' + JSON.stringify(groups));
		let finalDataObjects = [];
		//This cleans up the data so there is only one element per day
		Object.keys(groups).forEach(function (key) {
			//console.log('key: ' + key);
			for(var i = 0; i < groups[key].length; i++) {
				let obj = groups[key][i];
				obj.date_processed = moment(obj.date_processed).startOf('day').format();
				//console.log("obj", obj)
				finalDataObjects.push(obj);
			}
			//groups[key] = _.uniq(groups[key], 'key');
			//let obj = groups[key][0]; //Grab the first element in the array
			//obj.date_processed = moment(obj.date_processed).startOf('day').format();
			//finalDataObjects.push(obj);
		});

		//console.log('finalDataObjects: ' + JSON.stringify(finalDataObjects));
		finalDataObjects = finalDataObjects.reverse();
		var labelSet = new Set();
		finalDataObjects.forEach(function (item) {
			labelSet.add(moment(item.date_processed).format('MM-DD-YY'));
		});

		var labelList = Array.from(labelSet);


		//console.log('labelList: ' + JSON.stringify(labelList));
		//console.log('finalDataObjects: ' + JSON.stringify(finalDataObjects));

		//Gather the dataset keys
		let dataSetKeys = [];
		let labelIsTime = false;
		if (XAxisInputLabel == 'time' || XAxisInputLabel === 'day') {
			//console.log("Using Time");
			dataSetKeys.push(XAxisInputLabel);
			labelIsTime = true;
		} else {
			for (var i = 0; i < finalDataObjects.length; i++) {
				if (Array.isArray(finalDataObjects[i].data)) {
					for (var j = 0; j < finalDataObjects[i].data.length; j++) {
						if (finalDataObjects[i].data[j][XAxisInputLabel] == '')
							dataSetKeys.push('Unclassified');
						else
							dataSetKeys.push(finalDataObjects[i].data[j][XAxisInputLabel]);
					}
				} else {
					//console.log(finalDataObjects[i])
					dataSetKeys.push(finalDataObjects[i].data[XAxisInputLabel]);
				}
			}
		}

		dataSetKeys = _.uniq(dataSetKeys);
		let datasets = [];
		if (labelIsTime) {
			//console.log("Label is time, line 262")
			for (var i = 0; i < inputs.length; i++) {
				//console.log(dataSetKeys[0])
				datasets.push({
					data: [],
					label: inputs[i], //dataSetKeys[0] == undefined ? 'Count' : dataSetKeys[i],
					borderColor: colors[i],
					backgroundColor: colors[i],
					hoverBackgroundColor: colors[i],
					hoverBorderColor: tailwindConfig().theme.colors.white,
					borderWidth: 2,
					tension: 0.5,
					lineTension: 0.2,
					borderWidth: 3,
				});
			}

			//If label is time, we need to combine our finalDataObjects into one object per day
			let combinedData = [];
			let combinedDataKeys = [];

			for (let i = 0; i < finalDataObjects.length; i++) {
				let finalObj = finalDataObjects[i];
				let data = finalObj.data;
				let key = finalObj.date_processed;

				let existingIndex = combinedDataKeys.indexOf(key);
				if (existingIndex == -1) {
					combinedDataKeys.push(key);
					combinedData.push(finalObj);
				}
				else {
					let existingData = combinedData[existingIndex];
					for (let j = 0; j < Object.keys(data).length; j++) {
						let currKey = Object.keys(data)[j];
						let currVal = data[currKey];

						// If the value is a number, add it to the existing value with the same key
						if (typeof currVal === "number") {
							existingData.data[currKey] += currVal;
						}
					}
				}
			}

			finalDataObjects = combinedData;
			finalDataObjects.sort((a, b) => new Date(a.date_processed) - new Date(b.date_processed));
		} else {
			//console.log("Label is not time, line 272")
			for (var i = 0; i < dataSetKeys.length; i++) {
				datasets.push({
					data: [],
					label: dataSetKeys[i],
					borderColor: colors[i],
					backgroundColor: colors[i],
					hoverBackgroundColor: colors[i],
					hoverBorderColor: tailwindConfig().theme.colors.white,
					borderWidth: 2,
					lineTension: 0.2,
					tension: 0.5,
					borderWidth: 3,
				});
			}
		}

		finalDataObjects.forEach(function (item) {
			let data = item.data;
			let indexesAdded = [];
			if (!Array.isArray(data)) {
				//console.log("NOT AN ARRAY LINE 296", data, data[inputs[k]], XAxisInputLabel);
				for (var k = 0; k < inputs.length; k++) {
					let datasetIdx = k;
					//find datasetIdx where we look for dataset.label
					for (var j = 0; j < datasets.length; j++) {
						//console.log("comparing", datasets[j].label, data[XAxisInputLabel], "")
						if (datasets[j].label == data[XAxisInputLabel]) {
							datasetIdx = j;
							break;
						}
					}
					if (typeof data === 'object') {
						//console.log("OBJECT", data[inputs[k]], datasetIdx)
						datasets[datasetIdx].data.push(parseInt(data[inputs[k]]));
					} else if (typeof data === 'number') {
						datasets[datasetIdx].data.push(parseInt(data));
					}
				}

			} else {
				for (var i = 0; i < data.length; i++) {
					//path 1: we only have 1 line guaranteed
					if (labelIsTime) {
						//console.log("SINGLE LINE");
						for (var j = 0; j < inputs.length; j++) {
							if (!indexesAdded.includes(j)) {
								datasets[j].data.push(parseInt(data[i][inputs[j]]));
								indexesAdded.push(j);
							} else {
								datasets[j].data[datasets[j].data.length - 1] += parseInt(data[i][inputs[j]]);
							}
						}
					}
					else {
						//Multiple lines supported, because the label is not time based
						//console.log("MULTIPLE LINES");
						for (var j = 0; j < dataSetKeys.length; j++) {
							if (data[i][XAxisInputLabel] == dataSetKeys[j]) {
								if (!indexesAdded.includes(j)) {
									datasets[j].data.push(parseInt(data[i][inputs[0]]));
									indexesAdded.push(j);
								} else {
									datasets[j].data[datasets[j].data.length - 1] += parseInt(
										data[i][inputs[0]],
									);
								}
							}
						}
					}
					//Some datasets may not have the key we are looking for, so we need to push 0
					for (var k = 0; k < datasets.length; k++) {
						if (!indexesAdded.includes(k)) {
							datasets[k].data.push(0);
						}
					}
				}
			}
		});

		console.log(labelList)
		labelList.sort((a, b) => new Date(a) - new Date(b));

		this.chartData = ref({
			labels: labelList,
			datasets: datasets,
		});

		this.isLoaded = true;
		this.$forceUpdate();
	},
};
</script>
