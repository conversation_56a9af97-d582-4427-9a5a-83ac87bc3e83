<template>
  <div
    class="
      col-span-4
      sm:col-span-4
      md:col-span-4
      lg:col-span-4
      xl:col-span-4
      bg-white
      shadow-lg
      rounded-sm
      border border-slate-200
    "
  >
    <ReportCardHeader title="Interactions By Type for Project" />

    <RankedChart
      :data="items"
      primaryColumnTitle="Interaction Type"
      secondaryColumnTitle="Activity"
      categoryIndicator="1"
      color="orange"
    />
  </div>
</template>

<script>
import { ref } from 'vue';
import RankedChart from '../../../charts/RankedChart.vue';
import ReportCardFooter from '../../../components/ReportCardFooter.vue';
import ReportCardHeader from '../../../components/ReportCardHeader.vue';
import { getProjectInteractionsByType } from '../../../services/metrics';

// Import utilities

export default {
  name: 'ProjectInteractionsByType',
  props: ['projectId'],
  components: {
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>eader,
    ReportCardFooter,
  },
  setup() {
    const totalTokenVal = ref(null);
    const chartData = ref(null);

    return {
      chartData,
      totalTokenVal,
    };
  },
  watch: {
    projectId: async function (newVal, oldVal) {
      const result = await getProjectInteractionsByType('NONE', newVal);
      console.log(
        'Data retrieved from project interactions by type ' +
          JSON.stringify(result),
      );

      var newData = [];
      if (result.body && result.body.length && result.body[0].data) {
        for (var i = 0; i < result.body[0].data.length; i++) {
          if (result.body[0].data[i].key != null) {
            var dataItem = {
              name: result.body[0].data[i].key,
              amount: result.body[0].data[i].count,
            };
            newData.push(dataItem);
          }
        }
      }

      newData.sort((a, b) => parseFloat(b.amount) - parseFloat(a.amount));

      this.items = newData;
      this.$forceUpdate();
    },
  },
  async mounted() {},
};
</script>
