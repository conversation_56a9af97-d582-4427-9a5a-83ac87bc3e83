<template>
  <QuickMetric
    title="Total USD Value of Wallets"
    :amount="val"
    label="$"
    infoLabel="for all holders"
    :isLoaded="loader"
  />
</template>

<script>
import QuickMetric from '../../../components/QuickMetric.vue';
import {getProjectTotalTokenHolderUSD} from '../../../services/metrics';

export default {
  name: 'TotalTokenHolder',
  props: ['projectId'],
  components: {
    QuickMetric,
  },
  setup() {
    const val = 0;
    return {
      val,
    };
  },
  data() {
    return {
      loader: false,
    };
  },
  async mounted() {
    const result = await getProjectTotalTokenHolderUSD('NONE', this.projectId);
    console.log(
      'Data retrieved from project total usd holder wallet metric: ' +
        JSON.stringify(result),
    );

    this.val = result.body;
    if (this.val.length > 0) {
      this.val = this.val[0].data;
    }
    this.loader = true;

    this.$forceUpdate();
  },
};
</script>
