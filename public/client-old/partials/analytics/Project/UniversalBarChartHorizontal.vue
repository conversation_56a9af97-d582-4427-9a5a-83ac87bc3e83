<template>
	<div class="relative pl-8 pr-8 pt-4 h-72 overflow-hidden" v-if="!isLoaded">
		<div class="bg-gray-200 w-full animate-pulse h-4 rounded-2xl mb-6"></div>
		<div class="bg-gray-200 w-full h-full rounded-xl animate-pulse">
			<div class="flex justify-center align-middle animate-pulse pt-8">
				<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="h-36 w-36" stroke="#FFFFFF "
					stroke-width="1">
					<path fill="#FFFFFF" />
					<path fill="#FFFFFF" d="M12 3v2H3V3h9zm4 16v2H3v-2h13zm6-8v2H3v-2h19z" />
				</svg>
			</div>
		</div>
	</div>

	<div class="m-0 p-0" v-if="isLoaded">
		<ReportCardHeader :title="widgetData.title" :widgetId="widgetData.id" :isMissingDataSource="isMissingDataSource"
			:infoLabel="friendlyUpdatedDate" :infoLabel2="friendlyDate" @edit-widget="editWidget"
			@remove-widget="removeWidget" @refresh-widget="refreshWidget" @view-api="viewAPI"/>

		<BarChart :data="chartData" :totalVal="totalVal" totalValLabel="widgetData.infoLabel" totalIndicator="0" width="389"
			height="260" />
	</div>
</template>

<script>
import moment from 'moment';
import { ref } from 'vue';
import BarChart from '../../../charts/BarChartHorizontal.vue';
import ReportCardHeader from '../../../components/ReportCardHeader.vue';

// Import utilities
import { tailwindConfig } from '../../../utils/Utils';

export default {
	name: 'UniversalBarChart',
	props: ['projectId', 'widgetData', 'isMissingDataSource'],
	emits: ['editWidget', 'removeWidget', 'refreshWidget', 'viewApi'],
	components: {
		BarChart,
		ReportCardHeader,
	},
	methods: {
		editWidget() {
			this.$emit('editWidget', this.widgetData.id);
		},
		removeWidget() {
			this.$emit('removeWidget', this.widgetData.id);
		},
		refreshWidget() {
			this.$emit('refreshWidget', this.widgetData.id);
		},
		viewAPI() {
			this.$emit('viewApi', this.apiCall);
		}
	},
	setup() {
		const chartData = ref(null);
		const totalVal = ref(0);
		const apiCall = ref(null);

		return {
			chartData,
			totalVal,
			apiCall
		};
	},
	data() {
		return {
			isLoaded: false,
			friendlyDate: '',
			friendlyUpdatedDate: '',
		}
	},
	async mounted() {
		const metrics = await import('../../../services/metrics.js');
		let genericMetricAddress = this.widgetData.datasource != 'custom-metrics' ? this.projectId : this.widgetData.customMetricId;
		this.apiCall = metrics.getMetricAPI(
			this.widgetData.apiName,
			'NONE',
			genericMetricAddress,
			undefined,
			'latest',
		);
		const result = await metrics.genericMetricCall(
			this.widgetData.apiName,
			'NONE',
			genericMetricAddress,
			undefined,
			'latest',
		);
		console.log(
			'Data retrieved from universal barchart chart' + JSON.stringify(result),
		);

		const reportbuilder = await import('../../../services/reportbuilder.js');
		this.friendlyDate = reportbuilder.getFriendlyDateName(
			this.widgetData.time,
		);
		this.friendlyUpdatedDate = result && result.body && result.body[0]
			? `Last Updated: ${moment(result.body[0].date_processed).fromNow()}`
			: '';

		var labelListPrep = [];
		var dataListPrep = [];
		if (!result.body || !result.body.length || !result.body[0].data) {
			this.isLoaded = true;
			return;
		}


		let withDayIncludedDataset = [];
		let dateIncluded = false;
		let latestDay = null;
		for (var i = 0; i < result.body.length; i++) {
			for (var j = 0; j < result.body[i].data.length; j++) {
				let data = result.body[i].data[j];
				let dataset = {};
				dataset.date_processed = result.body[i].data[j].day;
				dataset.data = data;
				if (!moment(dataset.date_processed).isValid()) {
					console.log("invalid day range skipping")
				}
				//console.log('In loop', dataset);
				//If date is after startdate push to end of array
				dateIncluded = true;
				withDayIncludedDataset.push(dataset);

				if (!latestDay || moment(dataset.date_processed).isAfter(latestDay)) {
					latestDay = moment(dataset.date_processed);
        		}
			}
		}
		if(dateIncluded) {
			let latestDayValue = withDayIncludedDataset.filter((item) => {
				return moment(item.date_processed).isSame(latestDay);
			});

			result.body[0].data = [];
			for(let i = 0; i < latestDayValue.length; i++) {
				result.body[0].data.push(latestDayValue[i].data);
			}
		}




		for (var i = 0; i < result.body[0].data.length; i++) {
			for (let item in result.body[0].data[i]) {
				if (result.body[0].data[i][item] == '')
					result.body[0].data[i][item] = 0;

				if (item == this.widgetData.XAxisInputLabel) {
					labelListPrep.push(result.body[0].data[i][item] === "null" ? "Unknown" : result.body[0].data[i][item]);
				}

				if (item == this.widgetData.inputs[0]) {
					dataListPrep.push(result.body[0].data[i][item]);
				}

				if (this.widgetData.inputSummary != null) {
					if (item == this.widgetData.inputSummary) {
						this.totalVal += parseInt(result.body.data[i][item]);
					}
				}
			}
		}
		//Makes sure we have unique items
		let dataListMap = new Map([]);
		for (var i = 0; i < labelListPrep.length; i++) {
			if (dataListMap.has(labelListPrep[i])) {
				dataListMap.set(
					labelListPrep[i],
					dataListMap.get(labelListPrep[i]) + dataListPrep[i],
				);
			} else if (!dataListMap.has(labelListPrep[i])) {
				dataListMap.set(labelListPrep[i], dataListPrep[i]);
			}
		}

		dataListMap = new Map([...dataListMap.entries()].sort((a, b) => b[1] - a[1]));

		var labelList = [];
		var dataList = [];
		let idx = 0;
		let extraLabel = 'others';
		let extraCount = 0;
		dataListMap.forEach((value, key) => {
			if (idx++ < 10) {
				labelList.push(key);
				dataList.push(value);
			}
			else {
				extraCount += value;
			}
		});
		if (extraCount > 0) {
			//labelList.push(extraLabel);
			//dataList.push(extraCount);
			this.friendlyUpdatedDate = result && result.body && result.body[0]
			? `Last Updated: ${moment(result.body[0].date_processed).fromNow()} - Constrained to Top 10`
			: '';
		}

		this.chartData = ref({});
		this.chartData.datasets = [];

		this.chartData.labels = labelList;
		var newDataset = {
			label: this.widgetData.inputs[0],
			data: dataList,
			backgroundColor: [
				tailwindConfig().theme.colors.indigo[500],
				tailwindConfig().theme.colors.sky[400],
				tailwindConfig().theme.colors.indigo[800],
				tailwindConfig().theme.colors.emerald[800],
				tailwindConfig().theme.colors.blue[800],
				tailwindConfig().theme.colors.orange[500],
				tailwindConfig().theme.colors.lime[500],
				tailwindConfig().theme.colors.pink[700],
			],
			hoverBackgroundColor: [
				tailwindConfig().theme.colors.indigo[600],
				tailwindConfig().theme.colors.sky[500],
				tailwindConfig().theme.colors.indigo[900],
				tailwindConfig().theme.colors.emerald[800],
				tailwindConfig().theme.colors.blue[800],
				tailwindConfig().theme.colors.orange[500],
				tailwindConfig().theme.colors.lime[500],
				tailwindConfig().theme.colors.pink[700],
			],
			hoverBorderColor: tailwindConfig().theme.colors.white,
		};
		this.chartData.datasets.push(newDataset);

		console.log('Charts are ' + JSON.stringify(this.chartData));

		this.isLoaded = true;
		this.$forceUpdate();
	},
};
</script>
