<template>
  <QuickMetric
    title="Highest Value Wallet"
    :amount="val"
    label="$"
    infoLabel="by ETH across all your users"
    :isLoaded="loader"
  />
</template>

<script>
import QuickMetric from '../../../components/QuickMetric.vue';
import {getProjectHighestValueWalletETH} from '../../../services/metrics';

export default {
  name: 'HighestValueWallet',
  props: ['projectId'],
  components: {
    QuickMetric,
  },
  setup() {
    const val = 0;
    return {
      val,
    };
  },
  data() {
    return {
      loader: false,
    };
  },
  async mounted() {
    const result = await getProjectHighestValueWalletETH(
      'NONE',
      this.projectId,
    );
    console.log(
      'Data retrieved from project highest value wallet metric: ' +
        JSON.stringify(result),
    );

    this.val = result.body;
    if (this.val.length > 0) {
      this.val = this.val[0].data.quote;
    }
    this.loader = true;

    this.$forceUpdate();
  },
};
</script>
