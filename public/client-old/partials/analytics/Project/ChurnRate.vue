<template>
  <QuickMetric
    title="Churn Rate"
    :amount="val"
    infoLabel="All time"
    label="%"
    :isLoaded="loader"
  />
</template>

<script>
import QuickMetric from '../../../components/QuickMetric.vue';
import {
  getProjectDormantUsers,
  getProjectUniqueWallets,
} from '../../../services/metrics';

export default {
  name: 'Persona',
  props: ['projectId'],
  components: {
    QuickMetric,
  },
  setup() {
    const val = 0;
    return {
      val,
    };
  },
  data() {
    return {
      loader: false,
    };
  },
  watch: {
    projectId: async function (newVal, oldVal) {
      const resultTotal = await getProjectUniqueWallets('NONE', newVal);
      const resultDormant = await getProjectDormantUsers(
        'NONE',
        this.projectId,
        undefined,
        'latest',
      );

      let resultTotalMetric = resultTotal.body;
      if (resultTotalMetric.length > 0) {
        resultTotalMetric = parseInt(resultTotalMetric[0].data);
      }

      let resultDormantMetric = resultDormant.body;
      if (resultDormantMetric.length > 0) {
        resultDormantMetric = parseInt(resultDormantMetric[0].data);
      }

      var churnRate = (resultDormantMetric / resultTotalMetric) * 100;

      this.val = churnRate;
      this.loader = true;

      this.$forceUpdate();
    },
  },
  async mounted() {},
};
</script>
