<template>
  <QuickMetric
    title="How Much You Are Of Your User's Activity"
    :amount="val"
    label="%"
    infoLabel="% of user wallet activity last 30"
    :isLoaded="loader"
  />
</template>

<script>
import QuickMetric from '../../../components/QuickMetric.vue';
import {getProjectActiveUserPercentActivity} from '../../../services/metrics';

export default {
  name: 'PercentOfWallet',
  props: ['projectId'],
  components: {
    QuickMetric,
  },
  setup(props) {
    const val = 0;
    return {
      val,
    };
  },
  data() {
    return {
      loader: false,
    };
  },
  async mounted() {
    const result = await getProjectActiveUserPercentActivity(
      'NONE',
      this.projectId,
    );
    console.log(
      'Data retrieved from project % of wallet metric: ' +
        JSON.stringify(result),
    );

    this.val = result.body;
    if (this.val.length > 0) {
      this.val = this.val[0].data;
    }
    this.loader = true;

    this.$forceUpdate();
  },
};
</script>
