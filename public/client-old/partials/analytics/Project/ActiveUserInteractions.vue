<template>
  <QuickMetric
    title="Interactions from Active Users"
    :amount="val"
    infoLabel="# of active interactions last 30 days"
    :isLoaded="loader"
  />
</template>

<script>
import QuickMetric from '../../../components/QuickMetric.vue';
import {getProjectActiveUserInteractionCount} from '../../../services/metrics';

export default {
  name: 'ActiveUserInteraction',
  props: ['projectId'],
  components: {
    QuickMetric,
  },
  setup() {
    const val = 0;
    return {
      val,
    };
  },
  data() {
    return {
      loader: false,
    };
  },
  watch: {
    projectId: async function (newVal, oldVal) {
      const result = await getProjectActiveUserInteractionCount('NONE', newVal);
      console.log(
        'Data retrieved from project active user interaction metric: ' +
          JSON.stringify(result),
      );

      this.val = result.body;
      if (this.val.length > 0) {
        this.val = this.val[0].data;
      }
      this.loader = true;

      this.$forceUpdate();
    },
  },
  async mounted() {},
};
</script>
