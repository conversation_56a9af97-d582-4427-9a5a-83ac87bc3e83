<template>
  <div
    class="
      flex flex-col
      col-span-full
      sm:col-span-6
      xl:col-span-4
      bg-white
      shadow-lg
      rounded-sm
      border border-slate-200
    "
  >
    <ReportCardHeader title="What dApps Your New Users Come From" />

    <RankedChart
      :data="items"
      primaryColumnTitle="Top 10 dApps"
      secondaryColumnTitle="Activity"
      categoryIndicator="1"
      color="purple"
    />
  </div>
</template>

<script>
import {ref} from 'vue';
import RankedChart from '../../../charts/RankedChart.vue';
import ReportCardFooter from '../../../components/ReportCardFooter.vue';
import ReportCardHeader from '../../../components/ReportCardHeader.vue';
import {getProjectNewUserActivities} from '../../../services/metrics';

// Import utilities

export default {
  name: 'ProjectNewUserActivities',
  props: ['projectId'],
  components: {
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>eader,
    ReportCardFooter,
  },
  setup() {
    const totalTokenVal = ref(null);
    const chartData = ref(null);

    return {
      chartData,
      totalTokenVal,
    };
  },
  async mounted() {
    const result = await getProjectNewUserActivities('NONE', this.projectId);
    console.log(
      'Data retrieved from project new user activities ' +
        JSON.stringify(result),
    );

    var newData = [];
    var length = 0;
    if (result.body[0].data.length > 10) length = 10;
    else length = result.body[0].data.length;

    for (var i = 0; i < length; i++) {
      if (result.body[0].data[i].key != null) {
        var dataItem = {
          name: result.body[0].data[i].key,
          amount: result.body[0].data[i].count,
          category: result.body[0].data[i].category,
        };
        newData.push(dataItem);
      }
    }

    newData.sort((a, b) => parseFloat(b.amount) - parseFloat(a.amount));

    this.items = newData;
    this.$forceUpdate();
  },
};
</script>
