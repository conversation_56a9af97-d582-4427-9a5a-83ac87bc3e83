<template>
	<div class="pl-8 pr-8 pt-4 h-48 overflow-hidden" v-if="!isLoaded">
		<div class="bg-gray-200 w-full animate-pulse h-4 rounded-2xl mb-6"></div>
		<div class="bg-gray-200 w-full h-28 rounded-xl animate-pulse">
			<div class="flex justify-center align-middle animate-pulse pt-6">
				<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="h-16 w-16" stroke="#FFFFFF"
					stroke-width="1">
					<path fill="#FFFFFF" />
					<path fill="#FFFFFF"
						d="M16 7.5a4 4 0 1 0-8 0H6a6 6 0 1 1 10.663 3.776l-7.32 8.723L18 20v2H6v-1.127l9.064-10.802A3.982 3.982 0 0 0 16 7.5z" />
				</svg>
			</div>
		</div>
	</div>

	<div v-else-if="isLoaded" class="m-0 p-0">
		<ReportCardHeader smallHeader="true" :widgetId="widgetData.id" :metricData="metricData" :title="widgetData.title"
			:tooltipInfo="widgetData.apiName" hasWalletList="true" @edit-widget="editWidget" @remove-widget="removeWidget"
			@refresh-widget="refreshWidget" @view-api="viewAPI" />
		<NumberChart :title="widgetData.title" :amount="val" />
	</div>
</template>

<script>
import NumberChart from '../../../charts/NumberChart.vue';
import { ref } from 'vue';
import ReportCardHeader from '../../../components/ReportCardHeader.vue';

export default {
	name: 'Persona',
	props: ['projectId', 'widgetData'],
	emits: ['editWidget', 'removeWidget', 'refreshWidget', 'viewApi'],
	components: {
		NumberChart,
		ReportCardHeader,
	},
	methods: {
		editWidget() {
			this.$emit('editWidget', this.widgetData.id);
		},
		removeWidget() {
			this.$emit('removeWidget', this.widgetData.id);
		},
		refreshWidget() {
			this.$emit('refreshWidget', this.widgetData.id);
		},
		viewAPI() {
			this.$emit('viewApi', this.apiCall);
		}
	},
	setup() {
		const val = 0;
		let metricData = {};
		const apiCall = ref(null);
		return {
			val,
			metricData,
			apiCall
		};
	},
	data() {
		return {
			isLoaded: false,
		};
	},
	async mounted() {
		const metrics = await import('../../../services/metrics.js');
		let genericMetricAddress = this.widgetData.datasource != 'custom-metrics' ? this.projectId : this.widgetData.customMetricId;
		const result = await metrics.genericMetricCall(
			this.widgetData.apiName,
			'NONE',
			genericMetricAddress,
			undefined,
			'latest',
		);

		const inputs = this.widgetData.inputs;
		console.log(
			'Data retrieved from project at ' +
			this.widgetData.title +
			' data is ' +
			JSON.stringify(result),
		);

		this.apiCall = metrics.getMetricAPI(
			this.widgetData.apiName,
			'NONE',
			genericMetricAddress,
			undefined,
			'latest',
		);

		console.log("Inputs are " + JSON.stringify(inputs));

		this.val = result.body;
		if (this.val.length > 0) {
			if (typeof this.val[0].data === 'number') {
				this.val = this.val[0].data;
			}
			else {
				let data = this.val[0].data[0];
				try {
					let val = parseFloat(data[inputs[0]]);
					this.val = val;
				} catch (error) {
					this.val = this.val[0].data[inputs[0]];
				}
			}
			this.metricData = result.body[0];
		}
		else {
			if (this.val.Items != undefined && this.val.Items.length > 0) {
				this.val = this.val.Items[0].data;
				this.metricData = result.body.Items[0];
			}
			else {
				this.val = 0;
			}
		}


		this.isLoaded = true;
		this.$forceUpdate();
	},
};
</script>
