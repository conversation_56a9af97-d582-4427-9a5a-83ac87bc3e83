<template>
  <QuickMetric
    title="Total # Of Token HODLERS"
    :amount="val"
    infoLabel="for all wallets"
    :isLoaded="loader"
  />
</template>

<script>
import QuickMetric from '../../../components/QuickMetric.vue';
import {getProjectTotalTokenholders} from '../../../services/metrics';

export default {
  name: 'TotalTokenHolders',
  props: ['projectId'],
  components: {
    QuickMetric,
  },
  setup() {
    const val = 0;
    return {
      val,
    };
  },
  data() {
    return {
      loader: false,
    };
  },
  watch: {
    projectId: async function (newVal, oldVal) {
      const result = await getProjectTotalTokenholders('NONE', newVal);
      console.log(
        'Data retrieved from project total # holder wallet metric: ' +
          JSON.stringify(result),
      );

      this.val = result.body;
      if (this.val.length > 0) {
        this.val = this.val[0].data;
      }
      this.loader = true;

      this.$forceUpdate();
    },
  },
  async mounted() {},
};
</script>
