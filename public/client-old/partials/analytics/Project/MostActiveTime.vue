<template>
  <QuickMetric
    title="Most Active Time"
    :amount="val"
    label=""
    infoLabel="as discovered in the last 30 days"
    :isLoaded="loader"
  />
</template>

<script>
import QuickMetric from '../../../components/QuickMetric.vue';
import {getProjectMostActiveTime} from '../../../services/metrics';

export default {
  name: 'MostActiveTime',
  props: ['projectId'],
  components: {
    QuickMetric,
  },
  setup(props) {
    const val = 0;
    return {
      val,
    };
  },
  data() {
    return {
      loader: false,
    };
  },
  async mounted() {
    const result = await getProjectMostActiveTime('NONE', this.projectId);
    console.log(
      'Data retrieved from project active time metric: ' +
        JSON.stringify(result),
    );

    this.val = result.body;
    if (this.val.length > 0) {
      this.val = this.val[0].data + ' UTC';
    }
    this.loader = true;

    this.$forceUpdate();
  },
};
</script>
