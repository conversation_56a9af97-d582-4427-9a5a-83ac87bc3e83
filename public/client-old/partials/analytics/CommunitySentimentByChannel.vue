<template>
  <div class="flex flex-col col-span-full sm:col-span-6 xl:col-span-4 bg-white shadow-lg rounded-sm border border-slate-200">
    <ReportCardHeader title="Sentiment by Channel" />

    <RankedChart :data="items" primaryColumnTitle="Channel" secondaryColumnTitle="Sentiment %" categoryIndicator="0" color="green" />
  </div>
</template>

<script>
import { ref } from 'vue'
import RankedChart from '../../charts/RankedChart.vue'
import ReportCardHeader from '../../components/ReportCardHeader.vue'
import ReportCardFooter from '../../components/ReportCardFooter.vue'
  
export default {
  name: 'SentimentByChannel',
  components: {
    RankedChart,
    ReportCardHeader,
    ReportCardFooter
  },
  
  data() {
    return {
      items: [
      {
        name: 'General',
        amount: 92,
        category: '',
      },
      {
        name: 'Me<PERSON>',
        amount: 82,
        category: '',
      },
      {
        name: 'dev-chat',
        amount: 64,
        category: '',
      },
      {
        name: 'General',
        amount: 63,
        category: '',
      },
      {
        name: 'ideas-suggestions',
        amount: 58,
        category: '',
      },
      {
        name: 'Support',
        amount: 36,
        category: '',
      }
      ]
    }
  }
}
</script>