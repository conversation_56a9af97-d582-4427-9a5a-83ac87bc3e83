<template>

	<transition name="slide" appear>
		<div
			v-if="isOpen"
			id="campaign-edit-pane"
			class="drawer flex flex-col z-[100] bg-white right-0 absolute shadow-md shadow-slate-300 inset-y-0 min-w-[410px] w-1/2 md:w-1/2 lg:w-[38%] xl:w-1/3 overflow-scroll"
			:class="{ open: isOpen }"
			@click.stop>

			<div class="px-6 sm:px-6 lg:px-12 py-8 w-full max-w-9xl mx-auto">

				<div class="flex sm:justify-between sm:items-center mb-2">
					<h1 class="text-xl md:text-xl text-slate-800 font-bold">{{ title }}</h1>
					<span class="cursor-pointer" @click="$emit('closeEditPane')">CLOSE</span>
				</div>

			</div>

			<div class="border-b border-ralwhite-line"></div>

			<div class="flex justify-between text-center mt-4">
				<a href="#" class="text-xs font-medium text-ralpurple-500 w-1/3 px-4 py-2 bg-white border-b-2 border-ralpurple-500">Insights</a>
				<a href="#" class="text-xs w-1/3 px-4 py-2 bg-white hover:border-b-2 hover:border-ralpurple-300">Data Source</a>
				<a href="#" class="text-xs w-1/3 px-4 py-2 bg-white hover:border-b-2 hover:border-ralpurple-300">Layout</a>
			</div>

			<div class="flex items-center border-b-2 border-x-ralgranite-300 mt-4">
				<span class="text-gray-500">
					<svg xmlns="http://www.w3.org/2000/svg" height="48" viewBox="0 96 960 960" width="48" class="w-6 h-6 fill-current ml-2" color="#4F3EAC"><path d="M796 935 533 672q-30 26-69.959 40.5T378 727q-108.162 0-183.081-75Q120 577 120 471t75-181q75-75 181.5-75t181 75Q632 365 632 471.15 632 514 618 554q-14 40-42 75l264 262-44 44ZM377 667q81.25 0 138.125-57.5T572 471q0-81-56.875-138.5T377 275q-82.083 0-139.542 57.5Q180 390 180 471t57.458 138.5Q294.917 667 377 667Z"/></svg>
				</span>
				<input type="text" placeholder="Search Insights" class="text-sm w-full px-3 py-2 border-none focus:border-none">
			</div>


			<div class="px-6 sm:px-6 w-full mx-auto">
				<div class="mt-4">

					<span class="inline-block border border-ralprimary-light text-ralprimary-light py-1 px-2 text-center text-xs rounded-full uppercase">General</span>

					<div class="grid grid-cols-2 gap-4 mt-4">

						<!-- Card 1 -->
						<div class="bg-gradient-to-br from-ralblack-primary to-ralblack-secondary hover:border-ralprimary-light border-2 border-white transition rounded-md p-4 cursor-pointer duration-300 ease-in-out">
							<div class="flex items-center mb-2">
							<span class="text-gray-500">

							</span>
							<h2 class="text-white text-md">Card Title</h2>
							<span class="ml-auto">
								<!-- <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 96 960 960" width="24" class="w-6 h-6 fill-current" color="FFFFFF"><path d="M220 976q-24.75 0-42.375-17.625T160 916V482q0-24.75 17.625-42.375T220 422h70v-96q0-78.85 55.606-134.425Q401.212 136 480.106 136T614.5 191.575Q670 247.15 670 326v96h70q24.75 0 42.375 17.625T800 482v434q0 24.75-17.625 42.375T740 976H220Zm0-60h520V482H220v434Zm260.168-140Q512 776 534.5 753.969T557 701q0-30-22.668-54.5t-54.5-24.5Q448 622 425.5 646.5t-22.5 55q0 30.5 22.668 52.5t54.5 22ZM350 422h260v-96q0-54.167-37.882-92.083-37.883-37.917-92-37.917Q426 196 388 233.917 350 271.833 350 326v96ZM220 916V482v434Z"/></svg> -->
							</span>
							</div>
							<p class="text-gray-300 text-sm">Brief descriptive text goes here.</p>
						</div>

						<!-- Card 2 -->
						<div class="bg-gradient-to-br from-ralblack-primary to-ralblack-secondary hover:border-ralprimary-light border-2 border-white transition rounded-md p-4 cursor-pointer duration-300 ease-in-out">
							<div class="flex items-center mb-2">
							<span class="text-gray-500">

							</span>
							<h2 class="text-white text-md">Card Title</h2>
							<span class="ml-auto">
								<!-- <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 96 960 960" width="24" class="w-6 h-6 fill-current" color="FFFFFF"><path d="M220 976q-24.75 0-42.375-17.625T160 916V482q0-24.75 17.625-42.375T220 422h70v-96q0-78.85 55.606-134.425Q401.212 136 480.106 136T614.5 191.575Q670 247.15 670 326v96h70q24.75 0 42.375 17.625T800 482v434q0 24.75-17.625 42.375T740 976H220Zm0-60h520V482H220v434Zm260.168-140Q512 776 534.5 753.969T557 701q0-30-22.668-54.5t-54.5-24.5Q448 622 425.5 646.5t-22.5 55q0 30.5 22.668 52.5t54.5 22ZM350 422h260v-96q0-54.167-37.882-92.083-37.883-37.917-92-37.917Q426 196 388 233.917 350 271.833 350 326v96ZM220 916V482v434Z"/></svg> -->
							</span>
							</div>
							<p class="text-gray-300 text-sm">Brief descriptive text goes here.</p>
						</div>

						<!-- Card 3 -->
						<div class="bg-gradient-to-br from-ralblack-primary to-ralblack-secondary hover:border-ralprimary-light border-2 border-white transition rounded-md p-4 cursor-pointer duration-300 ease-in-out">
							<div class="flex items-center mb-2">
							<span class="text-gray-500">

							</span>
							<h2 class="text-white text-md">Card Title</h2>
							<span class="ml-auto">
								<!-- <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 96 960 960" width="24" class="w-6 h-6 fill-current" color="FFFFFF"><path d="M220 976q-24.75 0-42.375-17.625T160 916V482q0-24.75 17.625-42.375T220 422h70v-96q0-78.85 55.606-134.425Q401.212 136 480.106 136T614.5 191.575Q670 247.15 670 326v96h70q24.75 0 42.375 17.625T800 482v434q0 24.75-17.625 42.375T740 976H220Zm0-60h520V482H220v434Zm260.168-140Q512 776 534.5 753.969T557 701q0-30-22.668-54.5t-54.5-24.5Q448 622 425.5 646.5t-22.5 55q0 30.5 22.668 52.5t54.5 22ZM350 422h260v-96q0-54.167-37.882-92.083-37.883-37.917-92-37.917Q426 196 388 233.917 350 271.833 350 326v96ZM220 916V482v434Z"/></svg> -->
							</span>
							</div>
							<p class="text-gray-300 text-sm">Brief descriptive text goes here.</p>
						</div>

						<!-- Card 4 -->
						<div class="bg-gradient-to-br from-ralblack-primary to-ralblack-secondary hover:border-ralprimary-light border-2 border-white transition rounded-md p-4 cursor-pointer duration-300 ease-in-out">
							<div class="flex items-center mb-2">
							<span class="text-gray-500">

							</span>
							<h2 class="text-white text-md">Card Title</h2>
							<span class="ml-auto">
								<!-- <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 96 960 960" width="24" class="w-6 h-6 fill-current" color="FFFFFF"><path d="M220 976q-24.75 0-42.375-17.625T160 916V482q0-24.75 17.625-42.375T220 422h70v-96q0-78.85 55.606-134.425Q401.212 136 480.106 136T614.5 191.575Q670 247.15 670 326v96h70q24.75 0 42.375 17.625T800 482v434q0 24.75-17.625 42.375T740 976H220Zm0-60h520V482H220v434Zm260.168-140Q512 776 534.5 753.969T557 701q0-30-22.668-54.5t-54.5-24.5Q448 622 425.5 646.5t-22.5 55q0 30.5 22.668 52.5t54.5 22ZM350 422h260v-96q0-54.167-37.882-92.083-37.883-37.917-92-37.917Q426 196 388 233.917 350 271.833 350 326v96ZM220 916V482v434Z"/></svg> -->
							</span>
							</div>
							<p class="text-gray-300 text-sm">Brief descriptive text goes here.</p>
						</div>

  					</div>

					<span class="inline-block border border-ralprimary-light text-ralprimary-light py-1 px-2 text-center text-xs rounded-full uppercase mt-4">NFT</span>

					<div class="grid grid-cols-2 gap-4 mt-4">

						<!-- Card 1 -->
						<div class="bg-gradient-to-br from-ralblack-primary to-ralblack-secondary hover:border-ralprimary-light border-2 border-white transition rounded-md p-4 cursor-pointer duration-300 ease-in-out">
							<div class="flex items-center mb-2">
							<span class="text-gray-500">

							</span>
							<h2 class="text-white text-md">Card Title</h2>
							<span class="ml-auto">
								<!-- <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 96 960 960" width="24" class="w-6 h-6 fill-current" color="FFFFFF"><path d="M220 976q-24.75 0-42.375-17.625T160 916V482q0-24.75 17.625-42.375T220 422h70v-96q0-78.85 55.606-134.425Q401.212 136 480.106 136T614.5 191.575Q670 247.15 670 326v96h70q24.75 0 42.375 17.625T800 482v434q0 24.75-17.625 42.375T740 976H220Zm0-60h520V482H220v434Zm260.168-140Q512 776 534.5 753.969T557 701q0-30-22.668-54.5t-54.5-24.5Q448 622 425.5 646.5t-22.5 55q0 30.5 22.668 52.5t54.5 22ZM350 422h260v-96q0-54.167-37.882-92.083-37.883-37.917-92-37.917Q426 196 388 233.917 350 271.833 350 326v96ZM220 916V482v434Z"/></svg> -->
							</span>
							</div>
							<p class="text-gray-300 text-sm">Brief descriptive text goes here.</p>
						</div>

						<!-- Card 2 -->
						<div class="bg-gradient-to-br from-ralblack-primary to-ralblack-secondary hover:border-ralprimary-light border-2 border-white transition rounded-md p-4 cursor-pointer duration-300 ease-in-out">
							<div class="flex items-center mb-2">
							<span class="text-gray-500">

							</span>
							<h2 class="text-white text-md">Card Title</h2>
							<span class="ml-auto">
								<!-- <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 96 960 960" width="24" class="w-6 h-6 fill-current" color="FFFFFF"><path d="M220 976q-24.75 0-42.375-17.625T160 916V482q0-24.75 17.625-42.375T220 422h70v-96q0-78.85 55.606-134.425Q401.212 136 480.106 136T614.5 191.575Q670 247.15 670 326v96h70q24.75 0 42.375 17.625T800 482v434q0 24.75-17.625 42.375T740 976H220Zm0-60h520V482H220v434Zm260.168-140Q512 776 534.5 753.969T557 701q0-30-22.668-54.5t-54.5-24.5Q448 622 425.5 646.5t-22.5 55q0 30.5 22.668 52.5t54.5 22ZM350 422h260v-96q0-54.167-37.882-92.083-37.883-37.917-92-37.917Q426 196 388 233.917 350 271.833 350 326v96ZM220 916V482v434Z"/></svg> -->
							</span>
							</div>
							<p class="text-gray-300 text-sm">Brief descriptive text goes here.</p>
						</div>

						<!-- Card 3 -->
						<div class="bg-gradient-to-br from-ralblack-primary to-ralblack-secondary hover:border-ralprimary-light border-2 border-white transition rounded-md p-4 cursor-pointer duration-300 ease-in-out">
							<div class="flex items-center mb-2">
							<span class="text-gray-500">

							</span>
							<h2 class="text-white text-md">Card Title</h2>
							<span class="ml-auto">
								<!-- <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 96 960 960" width="24" class="w-6 h-6 fill-current" color="FFFFFF"><path d="M220 976q-24.75 0-42.375-17.625T160 916V482q0-24.75 17.625-42.375T220 422h70v-96q0-78.85 55.606-134.425Q401.212 136 480.106 136T614.5 191.575Q670 247.15 670 326v96h70q24.75 0 42.375 17.625T800 482v434q0 24.75-17.625 42.375T740 976H220Zm0-60h520V482H220v434Zm260.168-140Q512 776 534.5 753.969T557 701q0-30-22.668-54.5t-54.5-24.5Q448 622 425.5 646.5t-22.5 55q0 30.5 22.668 52.5t54.5 22ZM350 422h260v-96q0-54.167-37.882-92.083-37.883-37.917-92-37.917Q426 196 388 233.917 350 271.833 350 326v96ZM220 916V482v434Z"/></svg> -->
							</span>
							</div>
							<p class="text-gray-300 text-sm">Brief descriptive text goes here.</p>
						</div>

						<!-- Card 4 -->
						<div class="bg-gradient-to-br from-ralblack-primary to-ralblack-secondary hover:border-ralprimary-light border-2 border-white transition rounded-md p-4 cursor-pointer duration-300 ease-in-out">
							<div class="flex items-center mb-2">
							<span class="text-gray-500">

							</span>
							<h2 class="text-white text-md">Card Title</h2>
							<span class="ml-auto">
								<!-- <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 96 960 960" width="24" class="w-6 h-6 fill-current" color="FFFFFF"><path d="M220 976q-24.75 0-42.375-17.625T160 916V482q0-24.75 17.625-42.375T220 422h70v-96q0-78.85 55.606-134.425Q401.212 136 480.106 136T614.5 191.575Q670 247.15 670 326v96h70q24.75 0 42.375 17.625T800 482v434q0 24.75-17.625 42.375T740 976H220Zm0-60h520V482H220v434Zm260.168-140Q512 776 534.5 753.969T557 701q0-30-22.668-54.5t-54.5-24.5Q448 622 425.5 646.5t-22.5 55q0 30.5 22.668 52.5t54.5 22ZM350 422h260v-96q0-54.167-37.882-92.083-37.883-37.917-92-37.917Q426 196 388 233.917 350 271.833 350 326v96ZM220 916V482v434Z"/></svg> -->
							</span>
							</div>
							<p class="text-gray-300 text-sm">Brief descriptive text goes here.</p>
						</div>

					</div>

					<span class="inline-block border border-ralprimary-light text-ralprimary-light py-1 px-2 text-center text-xs rounded-full uppercase mt-4">AVAILABLE</span>

					<div class="grid grid-cols-2 gap-4 mt-4">

						<!-- Card 1 -->
						<div class="bg-gradient-to-br from-ralblack-primary to-ralblack-secondary hover:border-ralprimary-light border-2 border-white transition rounded-md p-4 cursor-pointer duration-300 ease-in-out">
							<div class="flex items-center mb-2">
							<span class="text-gray-500">

							</span>
							<h2 class="text-white text-base">Active Users Last 30</h2>
							<span class="ml-auto">
								<!-- <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 96 960 960" width="24" class="w-6 h-6 fill-current" color="FFFFFF"><path d="M220 976q-24.75 0-42.375-17.625T160 916V482q0-24.75 17.625-42.375T220 422h70v-96q0-78.85 55.606-134.425Q401.212 136 480.106 136T614.5 191.575Q670 247.15 670 326v96h70q24.75 0 42.375 17.625T800 482v434q0 24.75-17.625 42.375T740 976H220Zm0-60h520V482H220v434Zm260.168-140Q512 776 534.5 753.969T557 701q0-30-22.668-54.5t-54.5-24.5Q448 622 425.5 646.5t-22.5 55q0 30.5 22.668 52.5t54.5 22ZM350 422h260v-96q0-54.167-37.882-92.083-37.883-37.917-92-37.917Q426 196 388 233.917 350 271.833 350 326v96ZM220 916V482v434Z"/></svg> -->
							</span>
							</div>
							<p class="text-gray-300 text-sm">Brief descriptive text goes here.</p>
							<span class="inline-block border border-white text-white py-1 px-2 text-center text-xs rounded-full uppercase mt-4">general</span>
						</div>

						<!-- Card 2 -->
						<div class="bg-gradient-to-br from-ralblack-primary to-ralblack-secondary hover:border-ralprimary-light border-2 border-white transition rounded-md p-4 cursor-pointer duration-300 ease-in-out">
							<div class="flex items-center mb-2">
							<span class="text-gray-500">

							</span>
							<h2 class="text-white text-md">SushiSwap Risks</h2>
							<span class="ml-auto">
								<!-- <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 96 960 960" width="24" class="w-6 h-6 fill-current" color="FFFFFF"><path d="M220 976q-24.75 0-42.375-17.625T160 916V482q0-24.75 17.625-42.375T220 422h70v-96q0-78.85 55.606-134.425Q401.212 136 480.106 136T614.5 191.575Q670 247.15 670 326v96h70q24.75 0 42.375 17.625T800 482v434q0 24.75-17.625 42.375T740 976H220Zm0-60h520V482H220v434Zm260.168-140Q512 776 534.5 753.969T557 701q0-30-22.668-54.5t-54.5-24.5Q448 622 425.5 646.5t-22.5 55q0 30.5 22.668 52.5t54.5 22ZM350 422h260v-96q0-54.167-37.882-92.083-37.883-37.917-92-37.917Q426 196 388 233.917 350 271.833 350 326v96ZM220 916V482v434Z"/></svg> -->
							</span>
							</div>
							<p class="text-gray-300 text-sm">Brief descriptive text goes here.</p>
							<span class="inline-block border border-white text-white py-1 px-2 text-center text-xs rounded-full uppercase mt-4">general</span>
						</div>

					</div>

					<span class="inline-block border border-ralprimary-light text-ralprimary-light py-1 px-2 text-center text-xs rounded-full uppercase mt-4">UNAVAILABLE</span>

					<span class="block text-ralgray-main text-sm mt-2">These Data Sources do not support the insight you selected.</span>

					<div class="grid grid-cols-2 gap-4 mt-4">

						<!-- Card 1 -->
						<div class="bg-ralgray-main hover:border-ralblack-secondary border-2 border-white transition rounded-md p-4 cursor-pointer duration-300 ease-in-out">
							<div class="flex items-center mb-2">
							<span class="text-gray-500">

							</span>
							<h2 class="text-ralblack-primary text-md">DAO Token Holders</h2>
							<span class="ml-auto">
								<!-- <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 96 960 960" width="24" class="w-6 h-6 fill-current" color="FFFFFF"><path d="M220 976q-24.75 0-42.375-17.625T160 916V482q0-24.75 17.625-42.375T220 422h70v-96q0-78.85 55.606-134.425Q401.212 136 480.106 136T614.5 191.575Q670 247.15 670 326v96h70q24.75 0 42.375 17.625T800 482v434q0 24.75-17.625 42.375T740 976H220Zm0-60h520V482H220v434Zm260.168-140Q512 776 534.5 753.969T557 701q0-30-22.668-54.5t-54.5-24.5Q448 622 425.5 646.5t-22.5 55q0 30.5 22.668 52.5t54.5 22ZM350 422h260v-96q0-54.167-37.882-92.083-37.883-37.917-92-37.917Q426 196 388 233.917 350 271.833 350 326v96ZM220 916V482v434Z"/></svg> -->
							</span>
							</div>
							<p class="text-gray-200 text-sm">Brief descriptive text goes here.</p>
							<span class="inline-block border border-ralblack-primary text-ralblack-primary py-1 px-2 text-center text-xs rounded-full uppercase mt-4">Token</span>
						</div>

						<!-- Card 2 -->
						<div class="bg-ralgray-main hover:border-ralblack-secondary border-2 border-white transition rounded-md p-4 cursor-pointer duration-300 ease-in-out">
							<div class="flex items-center mb-2">
							<span class="text-gray-500">

							</span>
							<h2 class="text-ralblack-primary text-md">Event Stream</h2>
							<span class="ml-auto">
								<!-- <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 96 960 960" width="24" class="w-6 h-6 fill-current" color="FFFFFF"><path d="M220 976q-24.75 0-42.375-17.625T160 916V482q0-24.75 17.625-42.375T220 422h70v-96q0-78.85 55.606-134.425Q401.212 136 480.106 136T614.5 191.575Q670 247.15 670 326v96h70q24.75 0 42.375 17.625T800 482v434q0 24.75-17.625 42.375T740 976H220Zm0-60h520V482H220v434Zm260.168-140Q512 776 534.5 753.969T557 701q0-30-22.668-54.5t-54.5-24.5Q448 622 425.5 646.5t-22.5 55q0 30.5 22.668 52.5t54.5 22ZM350 422h260v-96q0-54.167-37.882-92.083-37.883-37.917-92-37.917Q426 196 388 233.917 350 271.833 350 326v96ZM220 916V482v434Z"/></svg> -->
							</span>
							</div>
							<p class="text-gray-200 text-sm">Brief descriptive text goes here.</p>
							<span class="inline-block border border-ralblack-primary text-ralblack-primary py-1 px-2 text-center text-xs rounded-full uppercase mt-4">Token</span>
						</div>

					</div>
				</div>
			</div>
		</div>
	</transition>
</template>

<style>
	.slide-enter {
		opacity: 0;
	}

	.slide-enter-active {
		animation: slide-in .75s ease-out forwards;
	}

	.slide-leave-active {
		animation: slide-out .75s ease-in forwards;
	}

	@keyframes slide-in {
		from {
			transform: translateX(110%);
		}
		to {
			transform: translateX(0);
		}
	}

	@keyframes slide-out {
		from {
			transform: translateX(0);
		}
		to {
			transform: translateX(110%);
		}
	}
</style>

<script>
	export default {
		name: 'ChartEditPane',
		props: ['title', 'isOpen'],
		emits: ['chartUpdated', 'closeEditPane'],
	}
</script>
