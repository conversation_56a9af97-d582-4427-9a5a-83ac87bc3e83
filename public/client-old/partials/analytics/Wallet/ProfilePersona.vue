<template>
  <QuickMetric title="Persona" :amount="val" :isLoaded="loader"/>
</template>

<script>
import QuickMetric from '../../../components/QuickMetric.vue'
import { getAddressPersona } from "../../../services/metrics"


export default {
  name: 'Persona',
  props: ['walletAddress'],
  components: {
    QuickMetric
  },
  setup() {
    const val = 0
    return {
      val
    }
  },
  data() {
    return {
      loader: false
    }
  },
  async mounted() {
    const result = await getAddressPersona('ETH', this.walletAddress)
    console.log("Data retrieved from persona metric: " + JSON.stringify(result))

    var personaList = new Map()
    for(var i = 0; i < result.body.personas.length; i++)
    {
      personaList.set(result.body.personas[i], result.body.persona_scores[i])
    }
    
    var sortedPersona = new Map([...personaList.entries()].sort((a, b) => b[1] - a[1]));
    this.val = sortedPersona.keys().next().value

    this.loader = true;

    this.$forceUpdate()
  }
}
</script>
