<template>
<div class="flex flex-col col-span-full sm:col-span-6 xl:col-span-4 bg-white shadow-lg rounded-sm border border-slate-200">
  <ReportCardHeader title="Favorite Asset by Category" />

  <RankedChart :data="items" primaryColumnTitle="Category" secondaryColumnTitle="Score" categoryIndicator="0" color="green" />
</div>
</template>

<script>
import { ref } from 'vue'
import RankedChart from '../../../charts/RankedChart.vue'
import ReportCardHeader from '../../../components/ReportCardHeader.vue'
import ReportCardFooter from '../../../components/ReportCardFooter.vue'
  
export default {
  name: 'ProfileFavoriteAssets',
  components: {
    Ranked<PERSON>hart,
    ReportCardHeader,
    ReportCardFooter
  },
  
  data() {
    return {
      items: [
      {
        name: 'DAO Governance',
        amount: 80,
        category: 'Marketplace',
      },
      {
        name: 'Tokens',
        amount: 65,
        category: 'Exchange',
      },
      {
        name: 'Collectible NFT',
        amount: 42,
        category: 'Social',
      },
      {
        name: 'Gaming NFT',
        amount: 30,
        category: 'DeFi',
      },
      {
        name: 'POAP',
        amount: 22,
        category: 'DeFi',
      },
      {
        name: 'Other',
        amount: 10,
        category: 'Games',
      },
      ]
    }
  }
}
</script>