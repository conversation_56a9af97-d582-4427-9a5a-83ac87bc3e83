<template>
  <div class="flex flex-col col-span-full sm:col-span-6 xl:col-span-4 bg-white shadow-lg rounded-sm border border-slate-200">
    <ReportCardHeader title="Overview" />
    <div class="px-5 py-4 items-left">
      <div class="text-sm mb-4">
        <h3 class="font-medium text-slate-800">User Since</h3>
        <div>{{customer_since}}</div>
      </div>
      <div class="text-sm mb-4">
        <h3 class="flex font-medium text-slate-800">
          <div class="mr-2">Wallet Age</div>
        </h3>
        <div>{{wallet_age}}</div>
      </div>
      <div class="text-sm mb-4">
        <h3 class="font-medium text-slate-800">Last Activity With You</h3>
        <div>{{last_activity_with_you}}</div>
      </div>
      <div class="text-sm mb-4">
        <h3 class="font-medium text-slate-800">Last Activity</h3>
        <div>{{last_activity}}</div>
      </div>
      <div class="text-sm mb-4">
        <h3 class="font-medium text-slate-800">Average Transaction Size</h3>
        <div>$1,823</div>
      </div>
      <div class="text-sm mb-4">
        <h3 class="flex font-medium text-slate-800">
          <div class="mr-2">Best Marketing Channel</div>
          <Tooltip size="lg" bg="dark" position="bottom">
            <div class="text-sm font-medium text-slate-200">This is estimated based on what Raleon has tracked to date.</div>
          </Tooltip></h3>
        <div>Twitter</div>
      </div>
      <!--
      <div class="text-sm mb-4">
        <h3 class="flex font-medium text-slate-800">
          <div class="mr-2">Sentiment Toward You</div>
          <Tooltip size="lg" bg="dark" position="bottom">
            <div class="text-sm font-medium text-slate-200">This is an estimate based on Raleon's insights engine as a result of activity on social channels.</div>
          </Tooltip>
        </h3>
        <div>
          <div class="text-xs mr-1.5 font-medium bg-emerald-100 text-emerald-600 rounded-full text-center px-2.5 py-1" style="width: 100px;">
            Good
          </div>
        </div>
      </div>
    -->
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import DoughnutChart from '../../../charts/DoughnutChart.vue'
import ReportCardFooter from '../../../components/ReportCardFooter.vue'
import ReportCardHeader from '../../../components/ReportCardHeader.vue'
import Tooltip from '../../../components/Tooltip.vue'

// Import utilities
import { tailwindConfig } from '../../../utils/Utils'

export default {
  name: 'ProfileSummary',
  props: ['walletAddress'],
  components: {
    DoughnutChart,
    ReportCardHeader,
    ReportCardFooter,
    Tooltip,
  },
  setup() {
    const chartData = ref({
      labels: ["Transfer", "Stake", "Reward", "Mint", "Sign" ],
      datasets: [
        {
          label: 'Sessions By Device',
          data: [
            500, 400, 800, 7000, 12000,
          ],
          backgroundColor: [
            tailwindConfig().theme.colors.indigo[500],
            tailwindConfig().theme.colors.sky[400],
            tailwindConfig().theme.colors.indigo[800],
            tailwindConfig().theme.colors.emerald[800],
            tailwindConfig().theme.colors.blue[800],
          ],
          hoverBackgroundColor: [
            tailwindConfig().theme.colors.indigo[600],
            tailwindConfig().theme.colors.sky[500],
            tailwindConfig().theme.colors.indigo[900],
            tailwindConfig().theme.colors.emerald[800],
            tailwindConfig().theme.colors.blue[800],
          ],
          hoverBorderColor: tailwindConfig().theme.colors.white,
        },
      ],
    })

    return {
      chartData,
    }
  },
  data() {
    return {
    last_activity: "TBD",
    last_activity_with_you: "TBD",
    ltv: "$350",
    customer_since: "TBD",
    wallet_age: 0,
    }
  },
  async mounted() {
    // const last_activity_result = await getAddressLastTransaction('ETH', this.walletAddress)
    // console.log("Last activity result metric " + JSON.stringify(last_activity_result))

    // //this.last_activity = timeDifference(new Date(), new Date(last_activity_result.body.date))
    // this.last_activity = "7/18/22"

    // this.wallet_age = "92 days"


    // const last_activity_with_you_result = await getAddressLastTransaction('ETH', this.walletAddress)

    // //this.last_activity_with_you_result = timeDifference(new Date(), new Date(last_activity_with_you_result.body.date))
    // this.last_activity_with_you = "7/17/22"

    // const customer_since_result = await getAddressCustomerSince('ETH', this.walletAddress)
    // //this.customer_since = customer_since_result.Items.data.customer_since_date
    // this.customer_since = "6/25/22"

    // const ltv_result = await getAddressLTV('ETH', this.walletAddress)
    // this.ltv = ltv_result.Items.data.ltv

    this.$forceUpdate()
  }
}
</script>
