<template>
	<div
		class="flex flex-col col-span-full sm:col-span-6 xl:col-span-6 bg-white shadow-lg rounded-sm border border-slate-200">
		<div class="relative pl-8 pr-8 pt-4 h-72 overflow-hidden" v-if="!isLoaded">
			<div class="bg-gray-200 w-full animate-pulse h-4 rounded-2xl mb-6"></div>
			<div class="bg-gray-200 w-full h-full rounded-xl animate-pulse">
				<div class="flex justify-center align-middle animate-pulse pt-8">
					<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="h-36 w-36" stroke="#FFFFFF "
						stroke-width="1">
						<path fill="#FFFFFF" />
						<path fill="#FFFFFF"
							d="M11 2.05v2.012C7.054 4.554 4 7.92 4 12c0 4.418 3.582 8 8 8 1.849 0 3.55-.627 4.906-1.68l1.423 1.423C16.605 21.153 14.4 22 12 22 6.477 22 2 17.523 2 12c0-5.185 3.947-9.449 9-9.95zM21.95 13c-.2 2.011-.994 3.847-2.207 5.328l-1.423-1.422c.86-1.107 1.436-2.445 1.618-3.906h2.013zM13.002 2.05c4.724.469 8.48 4.226 8.95 8.95h-2.013c-.451-3.618-3.319-6.486-6.937-6.938V2.049z" />
					</svg>
				</div>
			</div>
		</div>

		<div v-if="isLoaded">
			<ReportCardHeader title="Current Balance" />

			<DoughnutChart :data="chartData" :totalVal="totalTokenVal" totalValSymbol="$" totalValLabel=""
				totalIndicator="1" width="389" height="260" />
		</div>
	</div>
</template>

<script>
import { ref } from 'vue'
import DoughnutChart from '../../../charts/DoughnutChart.vue'
import ReportCardFooter from '../../../components/ReportCardFooter.vue'
import ReportCardHeader from '../../../components/ReportCardHeader.vue'
import { getMetric, getWalletTokens } from "../../../services/metrics"

// Import utilities
import { arrayBuilder, tailwindConfig } from '../../../utils/Utils'

export default {
	name: 'ProfileCurrentBalanceByType',
	props: ['walletAddress'],
	components: {
		DoughnutChart,
		ReportCardHeader,
		ReportCardFooter
	},
	setup() {
		const chartData = ref(null)

		return {
			chartData,
		}
	},
	data() {
		return {
			totalTokenVal: 0,
			isLoaded: false
		}
	},
	async mounted() {
		const result = await getWalletTokens(this.walletAddress);
		console.log("Data retrieved from token balance metric: " + JSON.stringify(result))

		this.chartData = ref({})
		this.chartData.datasets = []
		let tokenList = [];
		let dataList = [];
		if (result.body[0]?.data?.length > 0) {
			this.totalTokenVal = 0;
			for (var i = 0; i < result.body[0].data.length; i++) {
				tokenList.push(result.body[0].data[i].token_name)
				dataList.push(result.body[0].data[i].usd_value)
				this.totalTokenVal += result.body[0].data[i].usd_value != 'null' ? parseInt(result.body[0].data[i].usd_value) : 0
			}
		}

		console.log("Total token value is " + this.totalTokenVal)

		this.chartData.labels = tokenList
		var newDataset = {
			label: 'Tokens',
			data: dataList,
			backgroundColor: [
				tailwindConfig().theme.colors.indigo[500],
				tailwindConfig().theme.colors.sky[400],
				tailwindConfig().theme.colors.indigo[800],
				tailwindConfig().theme.colors.emerald[800],
				tailwindConfig().theme.colors.blue[800],
				tailwindConfig().theme.colors.orange[500],
				tailwindConfig().theme.colors.lime[500],
				tailwindConfig().theme.colors.pink[700],
			],
			hoverBackgroundColor: [
				tailwindConfig().theme.colors.indigo[600],
				tailwindConfig().theme.colors.sky[500],
				tailwindConfig().theme.colors.indigo[900],
				tailwindConfig().theme.colors.emerald[800],
				tailwindConfig().theme.colors.blue[800],
				tailwindConfig().theme.colors.orange[500],
				tailwindConfig().theme.colors.lime[500],
				tailwindConfig().theme.colors.pink[700],
			],
			hoverBorderColor: tailwindConfig().theme.colors.white,
		}
		this.chartData.datasets.push(newDataset)

		this.isLoaded = true;
		this.$forceUpdate()
	}
}
</script>
