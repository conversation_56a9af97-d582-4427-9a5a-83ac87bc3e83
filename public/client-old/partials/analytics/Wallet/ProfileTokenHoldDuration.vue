<template>
<div class="flex flex-col col-span-full sm:col-span-6 xl:col-span-4 bg-white shadow-lg rounded-sm border border-slate-200">
  <ReportCardHeader title="Token Hold Duration" />

  <RankedChart :data="items" primaryColumnTitle="Tokens" secondaryColumnTitle="Days" categoryIndicator="1" color="green" />
</div>
</template>

<script>
import { ref } from 'vue'
import RankedChart from '../../../charts/RankedChart.vue'
import ReportCardHeader from '../../../components/ReportCardHeader.vue'
import ReportCardFooter from '../../../components/ReportCardFooter.vue'
import { getAddressHoldDuration } from "../../../services/metrics"

export default {
  name: 'ProfileTokenHoldDuration',
  props: ['walletAddress'],
  components: {
    Ranked<PERSON>hart,
    ReportCardHeader,
    ReportCardFooter
  },
  
  setup() {
    const items = ref(null)
    return {
      items
    }
  },
  async mounted() {
    const result = await getAddressHoldDuration('ETH', this.walletAddress)
    console.log("Data retrieved from hold duration metric: " + JSON.stringify(result))

    var newData = []
    for(var i = 0; i < result.Items.data.tokens.length; i++)
    {
      var dataItem = {
        name: result.Items.data.tokens[i],
        amount: result.Items.data.hold_duration_days[i],
      }
      newData.push(dataItem)
    }

    this.items = newData

    this.$forceUpdate()
  }
}
</script>