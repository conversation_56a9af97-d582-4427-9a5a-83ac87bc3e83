<template>
  <div class="flex flex-col col-span-full sm:col-span-6 xl:col-span-6 bg-white shadow-lg rounded-sm border border-slate-200">
    <ReportCardHeader title="Token Outflow" />

    <DoughnutChart :data="chartData" :totalVal="totalTokenVal" totalValLabel="" totalValSymbol="$" totalIndicator="1" width="389" height="260" />
  </div>
</template>

<script>
import { ref } from 'vue'
import Doughnut<PERSON>hart from '../../../charts/DoughnutChartRight.vue'
import ReportCardHeader from '../../../components/ReportCardHeader.vue'
import ReportCardFooter from '../../../components/ReportCardFooter.vue'
import { getAddressTokenOutflow } from "../../../services/metrics"

// Import utilities
import { tailwindConfig } from '../../../utils/Utils'

export default {
  name: 'ProfileTokenOutflow',
  props: ['walletAddress'],
  components: {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    ReportCardHeader,
    ReportCardFooter
  },
  setup() {
    const totalTokenVal = ref(null)
    const chartData = ref(null)

    return {
      chartData,
      totalTokenVal
    } 
  },
  async mounted() {
    const result = await getAddressTokenOutflow('ETH', this.walletAddress)
    console.log("Data retrieved from token outflow metric: " + JSON.stringify(result))

    this.chartData = ref({})
    this.chartData.datasets = []

    var tokenList = []
    var dataList = []
    for(var i = 0; i < result.Items.data.tokens.length; i++)
    {
      tokenList.push(result.Items.data.tokens[i])
      dataList.push(result.Items.data.usd_values[i])

      this.totalTokenVal += result.Items.data.usd_values[i]
    }
    this.chartData.labels = tokenList
    var newDataset = {
      label: 'Tokens',
      data: dataList,
      backgroundColor: [
        tailwindConfig().theme.colors.indigo[500],
        tailwindConfig().theme.colors.sky[400],
        tailwindConfig().theme.colors.indigo[800],
        tailwindConfig().theme.colors.emerald[800],
        tailwindConfig().theme.colors.blue[800],
        tailwindConfig().theme.colors.orange[500],
        tailwindConfig().theme.colors.lime[500],
        tailwindConfig().theme.colors.pink[700],
      ],
      hoverBackgroundColor: [
        tailwindConfig().theme.colors.indigo[600],
        tailwindConfig().theme.colors.sky[500],
        tailwindConfig().theme.colors.indigo[900],
        tailwindConfig().theme.colors.emerald[800],
        tailwindConfig().theme.colors.blue[800],
        tailwindConfig().theme.colors.orange[500],
        tailwindConfig().theme.colors.lime[500],
        tailwindConfig().theme.colors.pink[700],
      ],
      hoverBorderColor: tailwindConfig().theme.colors.white,
    }
    this.chartData.datasets.push(newDataset)

    this.$forceUpdate()
  }
}
</script>