<template>
  <QuickMetric title="Current Balance" :amount="count" label="$" :isLoaded="loader" />
</template>

<script>
import QuickMetric from '../../../components/QuickMetric.vue'
import { getMetric } from "../../../services/metrics"


export default {
  name: 'CurrentBalance',
  props: ['walletAddress'],
  components: {
    QuickMetric
  },
  setup() {
    const count = 0
    return {
      count
    }
  },
  data() {
    return {
      loader: false,
    }
  },
  async mounted() {
    //network address metricName

    const result = await getMetric('ETH', this.walletAddress, 'ADDRESS_TOTAL_VALUE');

    console.log("Data retrieved from core balance metric: " + JSON.stringify(result))
    this.count = parseInt(result.body[0].data)

    this.loader = true;
    this.$forceUpdate()
  }
}
</script>
