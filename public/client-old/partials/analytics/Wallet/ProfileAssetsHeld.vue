<template>
  <div class="flex flex-col col-span-full sm:col-span-6 xl:col-span-4 bg-white shadow-lg rounded-sm border border-slate-200">
    <ReportCardHeader title="Current Assets Held" />
    
    <PieChart :data="chartData" totalVal="" totalValLabel="" totalIndicator="0" width="389" height="260" />

    <ReportCardFooter reportLinkLabel="Viwe Asset List" reportLink="#00"/>
  </div>
</template>

<script>
import { ref } from 'vue'
import PieChart from '../../../charts/PieChart.vue'
import ReportCardHeader from '../../../components/ReportCardHeader.vue'
import ReportCardFooter from '../../../components/ReportCardFooter.vue'

// Import utilities
import { tailwindConfig } from '../../../utils/Utils'

export default {
  name: 'ProfileAssetsHeld',
  props: ['walletAddress'],
  components: {
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>ardHeader,
    ReportCardFooter
  },
  setup() {
    const chartData = ref({
      labels: ['Tokens', 'DAO Governance', 'POAP', 'Collectible NFT', 'Gaming NFT', 'Spam/Scam', 'Other'],
      datasets: [
        {
          label: 'Held by Category',
          data: [
            122300, 53000, 5000, 24000, 12000, 1000, 1500
          ],
          backgroundColor: [
            tailwindConfig().theme.colors.indigo[500],
            tailwindConfig().theme.colors.sky[400],
            tailwindConfig().theme.colors.rose[500],
            tailwindConfig().theme.colors.emerald[500],
            tailwindConfig().theme.colors.stone[500],
            tailwindConfig().theme.colors.orange[500],
            tailwindConfig().theme.colors.teal[500],
          ],
          hoverBackgroundColor: [
            tailwindConfig().theme.colors.indigo[600],
            tailwindConfig().theme.colors.sky[500],
            tailwindConfig().theme.colors.rose[600],
            tailwindConfig().theme.colors.emerald[600],
            tailwindConfig().theme.colors.stone[500],
            tailwindConfig().theme.colors.orange[500],
            tailwindConfig().theme.colors.teal[500],
          ],
          hoverBorderColor: tailwindConfig().theme.colors.white,
        },
      ],
    })

    return {
      chartData,
    } 
  }
}
</script>