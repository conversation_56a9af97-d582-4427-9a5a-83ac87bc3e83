<template>
<div class="flex flex-col col-span-full sm:col-span-6 xl:col-span-4 bg-white shadow-lg rounded-sm border border-slate-200">
  <ReportCardHeader title="DAO Holdings" />

  <RankedChart :data="items" primaryColumnTitle="DAO" secondaryColumnTitle="Holding" categoryIndicator="0" color="green" />
</div>
</template>

<script>
import { ref } from 'vue'
import RankedChart from '../../../charts/RankedChart.vue'
import ReportCardHeader from '../../../components/ReportCardHeader.vue'
import ReportCardFooter from '../../../components/ReportCardFooter.vue'
  
export default {
  name: 'ProfilePopularDAO',
  components: {
    Ranked<PERSON>hart,
    ReportCardHeader,
    ReportCardFooter
  },
  
  data() {
    return {
      items: [
      {
        name: 'Aave',
        amount: 31000,
        category: 'Marketplace',
      },
      {
        name: 'Synthetix',
        amount: 22000,
        category: 'Exchange',
      },
      {
        name: 'MeebitsDAO',
        amount: 20000,
        category: 'DeFi',
      },
      {
        name: 'NFTX',
        amount: 11200,
        category: 'DeFi',
      },
      {
        name: 'FWB',
        amount: 8000,
        category: 'Collectibles',
      },
      {
        name: 'ENS',
        amount: 3000,
        category: 'Games',
      },
      ]
    }
  }
}
</script>