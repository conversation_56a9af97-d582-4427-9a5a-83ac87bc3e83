<template>
  <QuickMetric title="Engagement Score" :amount="count" indicator="inc" percentVal="5" />
</template>

<script>
import QuickMetric from '../../../components/QuickMetric.vue'
import { getAddressEngagementScore } from "../../../services/metrics"


export default {
  name: 'EngagementScore',
  components: {
    QuickMetric
  },
  setup() {
    const count = 0
    return {
      count
    }
  },
  async mounted() {
    const result = await getAddressEngagementScore('ETH', '******************************************')
    console.log("Data retrieved from metric: " + JSON.stringify(result))
    console.log("Engagement Scorei s now" + result.body.engagement_score)
    this.count = parseInt(result.body.engagement_score)

    this.$forceUpdate()
  }
}
</script>
