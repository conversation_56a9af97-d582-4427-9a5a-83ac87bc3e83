<template>
<div class="flex flex-col col-span-full sm:col-span-6 xl:col-span-6 bg-white shadow-lg rounded-sm border border-slate-200">
  <ReportCardHeader title="dApp Wallet Share" />

  <RankedChart :data="items" primaryColumnTitle="dApp" secondaryColumnTitle="Activity" categoryIndicator="1" color="orange" />
</div>
</template>

<script>
import { ref } from 'vue'
import RankedChart from '../../../charts/RankedChart.vue'
import ReportCardFooter from '../../../components/ReportCardFooter.vue'
import ReportCardHeader from '../../../components/ReportCardHeader.vue'
import { getMetric } from "../../../services/metrics"

export default {
  name: 'ProfilePopularDapps',
  props: ['walletAddress'],
  components: {
    Ranked<PERSON>hart,
    ReportCardHeader,
    ReportCardFooter
  },

  setup() {
    const items = ref(null)
    return {
      items
    }
  },
  methods: {
    async getAddressDappActivity() {
      const result = await getMetric('NONE', this.walletAddress, 'PROJECT_ACTIVITIES_BY_WALLET');

      if (result.body && result.body.Items && result.body.Items.length) {
        this.items = result.body.Items[0].data.map((item) => {
          return {
            name: item.key,
            amount: item.count,
            category: item.category
          }
        });
      }
    },
  },
  async mounted() {
    await this.getAddressDappActivity();
    this.$forceUpdate()
  }
}
</script>
