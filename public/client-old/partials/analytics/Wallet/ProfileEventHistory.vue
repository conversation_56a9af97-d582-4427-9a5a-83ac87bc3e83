<template>
    <div
      class="
        flex flex-col
        col-span-full
        bg-white
        shadow-lg
        rounded-sm
        border border-slate-200
        sm:col-span-8 xl-col-span-8"
    >

      <header
        class="
          px-5
          py-4
          border-b border-slate-100
          flex
          items-center
          justify-between
        "
      >
    <h2 class="font-semibold text-slate-800">Event History</h2>
    </header>
    <!-- Posts -->
    <div class="xl:-translate-x-16">
      <!-- Post -->
      <article class="pt-6">
        <div class="xl:flex">
          <div class="w-32 shrink-0">

          </div>
          <div class="grow pb-6 border-b border-slate-200">
            <!-- List -->
            <ul class="-my-2">
              <!-- List item -->
              <li class="relative py-2">
                <div class="flex items-center mb-1">
                  <div class="absolute left-0 h-full w-0.5 bg-slate-200 self-start ml-2.5 -translate-x-1/2 translate-y-3" aria-hidden="true"></div>
                  <div class="absolute left-0 rounded-full bg-indigo-500" aria-hidden="true">
                    <svg class="w-5 h-5 fill-current text-white" viewBox="0 0 20 20">
                      <path d="M14.4 8.4L13 7l-4 4-2-2-1.4 1.4L9 13.8z" />
                    </svg>
                  </div>
                  <h4 class="text-lg font-semibold text-slate-800 pl-9">Converted on <a href="#" class="text-ralpurple-500">Special Campaign</a> with ABC Project</h4>
                </div>
                <div class="pl-9 text-xs">7/04/2022</div>
              </li>
              <!-- List item -->
              <li class="relative py-2">
                <div class="flex items-center mb-1">
                  <div class="absolute left-0 h-full w-0.5 bg-slate-200 self-start ml-2.5 -translate-x-1/2 translate-y-3" aria-hidden="true"></div>
                  <div class="absolute left-0 rounded-full bg-indigo-500" aria-hidden="true">
                    <svg class="w-5 h-5 fill-current text-white" viewBox="0 0 20 20">
                      <path d="M14.4 8.4L13 7l-4 4-2-2-1.4 1.4L9 13.8z" />
                    </svg>
                  </div>
                  <h4 class="text-lg font-semibold text-slate-800 pl-9">Connected Wallet because of <a href="#" class="text-ralpurple-500">Special Campaign</a></h4>
                </div>
                <div class="pl-9 text-xs">7/04/2022</div>
              </li>
              <!-- List item -->
              <li class="relative py-2">
                <div class="flex items-center mb-1">
                  <div class="absolute left-0 h-full w-0.5 bg-slate-200 self-start ml-2.5 -translate-x-1/2 translate-y-3" aria-hidden="true"></div>
                  <div class="absolute left-0 rounded-full bg-indigo-500" aria-hidden="true">
                    <svg class="w-5 h-5 fill-current text-white" viewBox="0 0 20 20">
                      <path d="M14.4 8.4L13 7l-4 4-2-2-1.4 1.4L9 13.8z" />
                    </svg>
                  </div>
                  <h4 class="text-lg font-semibold text-slate-800 pl-9">Connected Wallet</h4>
                </div>
                <div class="pl-9 text-xs">7/01/2022</div>
              </li>
              <!-- List item -->
              <li class="relative py-2">
                <div class="flex items-center mb-1">
                  <div class="absolute left-0 rounded-full bg-indigo-500" aria-hidden="true">
                    <svg class="w-5 h-5 fill-current text-white" viewBox="0 0 20 20">
                      <path d="M14.4 8.4L13 7l-4 4-2-2-1.4 1.4L9 13.8z" />
                    </svg>
                  </div>
                  <h4 class="text-lg font-semibold text-slate-800 pl-9">Wallet Inflow of $3,250</h4>
                </div>
                <div class="pl-9 text-xs">6/27/2022</div>
              </li>
              <li class="relative py-2">
                <div class="flex items-center mb-1">
                  <div class="absolute left-0 rounded-full bg-indigo-500" aria-hidden="true">
                    <svg class="w-5 h-5 fill-current text-white" viewBox="0 0 20 20">
                      <path d="M14.4 8.4L13 7l-4 4-2-2-1.4 1.4L9 13.8z" />
                    </svg>
                  </div>
                  <h4 class="text-lg font-semibold text-slate-800 pl-9">First Time Wallet Connection With ABC Project</h4>
                </div>
                <div class="pl-9 text-xs">6/25/2022</div>
              </li>
            </ul>
          </div>
        </div>
      </article>
    </div>

  </div>
</template>

<script>
import { ref } from 'vue'
import DoughnutChart from '../../../charts/DoughnutChart.vue'
import ReportCardFooter from '../../../components/ReportCardFooter.vue'
import ReportCardHeader from '../../../components/ReportCardHeader.vue'
import Tooltip from '../../../components/Tooltip.vue'
import { getMetric } from '../../../services/metrics'
import { formatDate } from '../../../utils/Utils'

// Import utilities
import { tailwindConfig } from '../../../utils/Utils'

export default {
  name: 'ProfileEventHistory',
  props: ['walletAddress'],
  components: {
    DoughnutChart,
    ReportCardHeader,
    ReportCardFooter,
    Tooltip,
  },
  setup() {
    const lastActivity = ref(0)
    const lastActivityWithYou = ref(0)
    const ltv = ref(0)
    const customerSince = ref(0)
    const hold_duration = ref(0)
    const hold_duration_tooltip = ref(null)

    const chartData = ref({
      labels: ["Transfer", "Stake", "Reward", "Mint", "Sign" ],
      datasets: [
        {
          label: 'Sessions By Device',
          data: [
            500, 400, 800, 7000, 12000,
          ],
          backgroundColor: [
            tailwindConfig().theme.colors.indigo[500],
            tailwindConfig().theme.colors.sky[400],
            tailwindConfig().theme.colors.indigo[800],
            tailwindConfig().theme.colors.emerald[800],
            tailwindConfig().theme.colors.blue[800],
          ],
          hoverBackgroundColor: [
            tailwindConfig().theme.colors.indigo[600],
            tailwindConfig().theme.colors.sky[500],
            tailwindConfig().theme.colors.indigo[900],
            tailwindConfig().theme.colors.emerald[800],
            tailwindConfig().theme.colors.blue[800],
          ],
          hoverBorderColor: tailwindConfig().theme.colors.white,
        },
      ],
    })

    return {
      chartData,
      lastActivity,
    }
  },
  methods: {
    async getLastActivityDate() {
      const lastActivity = await getMetric(
        'ETH',
        this.walletAddress,
        'ADDRESS_LAST_TRANSACTION'
      );
      if (lastActivity.body && lastActivity.body.Items.length && lastActivity.body.Items[0].data) {
        this.lastActivity = formatDate(lastActivity.body.Items[0].data);
      }
    },
    async getAddressLastTransaction() {
		this.lastTransaction = 'Processing...';
      /*const lastTransaction = await getMetric(
        'ANY',
        `${this.walletAddress}_${this.projectId}`,
        'PROJECT_ADDRESS_LAST_TRANSACTION'
      );

      if (lastTransaction.body && lastTransaction.body.Items.length && lastTransaction.body.Items[0].data) {
        this.lastTransaction = formatDate(lastTransaction.body.Items[0].data);
      } else {
        this.lastTransaction = 'No Interactions';
      }*/
    },
    async getAddressFirstTransaction() {
		this.customerSince = 'Processing...';
      /*const firstTransaction = await getMetric(
        'ANY',
        `${this.walletAddress}_${this.projectId}`,
        'PROJECT_ADDRESS_FIRST_TRANSACTION'
      );

      if (firstTransaction.body && firstTransaction.body.Items.length && firstTransaction.body.Items[0].data) {
        this.customerSince = formatDate(firstTransaction.body.Items[0].data);
      } else {
        this.customerSince = 'No Interactions';
      }*/
    },
  },
  async mounted() {
    await this.getLastActivityDate();
    await this.getAddressLastTransaction();
    await this.getAddressFirstTransaction();

    // const ltv_result = await getAddressLTV('ETH', this.walletAddress)
    // this.ltv = ltv_result.Items.data.ltv

    // const hold_duration_result = await getAddressHoldDuration('ETH', this.walletAddress)
    // this.hold_duration = hold_duration_result.Items.data.avg_hold
    // this.hold_duration_tooltip = hold_duration_result.Items.data.info

    this.$forceUpdate()
  }
}
</script>
