<template>
  <div class="flex flex-col col-span-full sm:col-span-6 bg-white shadow-lg rounded-sm border border-slate-200">
    <ReportCardHeader title="Hourly Activity" />
    <!-- Chart built with Chart.js 3 -->
    <!-- Change the height attribute to adjust the chart height -->
    <BarChart :data="chartData" width="595" height="248" totalVal="0" totalIndicator="0" totalValLabel=""/>
  </div>
</template>

<script>
import { ref } from 'vue'
import BarChart from '../../../charts/BarChartLinear.vue'
import ReportCardFooter from '../../../components/ReportCardFooter.vue'
import ReportCardHeader from '../../../components/ReportCardHeader.vue'
import { getMetric } from "../../../services/metrics"

// Import utilities
import { tailwindConfig } from '../../../utils/Utils'

export default {
  name: 'ProfileActivityByHour',
  props: ['walletAddress'],
  components: {
    <PERSON><PERSON><PERSON>,
    ReportCardHeader,
    ReportCardFooter
  },
  setup() {
    const chartData = ref(null)

    return {
      chartData,
    }
  },
  async mounted() {
    const result = await getMetric('ETH', this.walletAddress, 'ADDRESS_ACTIVE_TIMES')
    console.log("Data retrieved from active time metric: " + JSON.stringify(result))

    this.chartData = ref({})
    this.chartData.datasets = []

    var labelList = []
    var dataList = []
    for(var i = 0; i < result.body.Items[0].data.hours.length; i++)
    {
      labelList.push(result.body.Items[0].data.hours[i])
      dataList.push(result.body.Items[0].data.transaction_counts[i])
    }
    this.chartData.labels = labelList
    var newDataset = {
      label: 'Hours',
      data: dataList,
      backgroundColor: [
        tailwindConfig().theme.colors.indigo[500],
        tailwindConfig().theme.colors.sky[400],
        tailwindConfig().theme.colors.indigo[800],
        tailwindConfig().theme.colors.emerald[800],
        tailwindConfig().theme.colors.blue[800],
        tailwindConfig().theme.colors.orange[500],
        tailwindConfig().theme.colors.lime[500],
        tailwindConfig().theme.colors.pink[700],
      ],
      hoverBackgroundColor: [
        tailwindConfig().theme.colors.indigo[600],
        tailwindConfig().theme.colors.sky[500],
        tailwindConfig().theme.colors.indigo[900],
        tailwindConfig().theme.colors.emerald[800],
        tailwindConfig().theme.colors.blue[800],
        tailwindConfig().theme.colors.orange[500],
        tailwindConfig().theme.colors.lime[500],
        tailwindConfig().theme.colors.pink[700],
      ],
      hoverBorderColor: tailwindConfig().theme.colors.white,
    }
    this.chartData.datasets.push(newDataset)

    this.$forceUpdate()
  }
}
</script>
