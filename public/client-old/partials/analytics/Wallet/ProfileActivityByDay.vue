<template>
  <div class="flex flex-col col-span-full sm:col-span-6 bg-white shadow-lg rounded-sm border border-slate-200">
    <ReportCardHeader title="Daily Activity" />
    <!-- Chart built with Chart.js 3 -->
    <!-- Change the height attribute to adjust the chart height -->
    <BarChart :data="chartData" width="595" height="248" totalVal="0" totalIndicator="0" totalValLabel=""/>
  </div>
</template>

<script>
import { ref } from 'vue'
import BarChart from '../../../charts/BarChartLinear.vue'
import ReportCardFooter from '../../../components/ReportCardFooter.vue'
import ReportCardHeader from '../../../components/ReportCardHeader.vue'
import { getMetric } from "../../../services/metrics"

// Import utilities
import { tailwindConfig } from '../../../utils/Utils'

export default {
  name: 'ProfileActivityByDay',
  components: {
    <PERSON><PERSON><PERSON>,
    Report<PERSON>ardHeader,
    ReportCardFooter
  },
  setup() {
    const chartData = ref(null)

    const chartData2 = ref({
      labels: [
        '12-01-2020', '12-02-2020', '12-03-2020',
        '12-04-2020', '12-05-2020', '12-06-2020', '12-07-2020'
      ],
      datasets: [
        // Indigo bars
        {
          label: 'Activity',
          data: [800, 2600, 4000, 1200, 3200, 1700, 700],
          backgroundColor: tailwindConfig().theme.colors.indigo[500],
          hoverBackgroundColor: tailwindConfig().theme.colors.indigo[600],
          barPercentage: 0.66,
          categoryPercentage: 0.66,
        },
      ],
    })

    return {
      chartData,
    }
  },
  async mounted() {
    const result = await getMetric('ETH', '******************************************', 'ADDRESS_ACTIVE_TIMES');
    console.log("Data retrieved from active time metric: " + JSON.stringify(result))

    this.chartData = ref({})
    this.chartData.datasets = []

    var labelList = []
    var dataList = []

    if (result.body.Items.length) {
      for (var i = 0; i < result.body.Items[0].data.hours.length; i++) {
        labelList.push(result.body.Items[0].data.hours[i])
        dataList.push(result.body.Items[0].data.transaction_counts[i])
      }
    }

    this.chartData.labels = labelList
    var newDataset = {
      label: 'Hours',
      data: dataList,
      backgroundColor: [
        tailwindConfig().theme.colors.indigo[500],
        tailwindConfig().theme.colors.sky[400],
        tailwindConfig().theme.colors.indigo[800],
        tailwindConfig().theme.colors.emerald[800],
        tailwindConfig().theme.colors.blue[800],
        tailwindConfig().theme.colors.orange[500],
        tailwindConfig().theme.colors.lime[500],
        tailwindConfig().theme.colors.pink[700],
      ],
      hoverBackgroundColor: [
        tailwindConfig().theme.colors.indigo[600],
        tailwindConfig().theme.colors.sky[500],
        tailwindConfig().theme.colors.indigo[900],
        tailwindConfig().theme.colors.emerald[800],
        tailwindConfig().theme.colors.blue[800],
        tailwindConfig().theme.colors.orange[500],
        tailwindConfig().theme.colors.lime[500],
        tailwindConfig().theme.colors.pink[700],
      ],
      hoverBorderColor: tailwindConfig().theme.colors.white,
    }
    this.chartData.datasets.push(newDataset)

    this.$forceUpdate()
  }
}
</script>
