<template>
  <div class="flex flex-col col-span-full sm:col-span-8 bg-white shadow-lg rounded-sm border border-slate-200">
    <ReportCardHeader title="All Time Activity by Network" />

    <!-- adjustment = inc or dec; summaryBool 1 or 0 -->
    <LineChart :data="chartData" width="595" height="248" />

    <ReportCardFooter reportLinkLabel="View Activity Report" reportLink="#00" />
  </div>
</template>

<script>
import { ref } from 'vue'
import LineChart from '../../../charts/LineChartOverTime.vue'
import ReportCardFooter from '../../../components/ReportCardFooter.vue'
import ReportCardHeader from '../../../components/ReportCardHeader.vue'
import { getAddressAllTimeActivityByNetwork } from "../../../services/metrics"
// Import utilities
import { tailwindConfig } from '../../../utils/Utils'


export default {
  name: 'ProfileAllTimeActivity',
  props: ['walletAddress'],
  components: {
    Line<PERSON>hart,
    ReportCardHeader,
    ReportCardFooter,
  },
  setup() {

    const chartData = ref(null)

    return {
      chartData,
    }
  },
  async mounted() {
    const result = await getAddressAllTimeActivityByNetwork('ETH', this.walletAddress)
    console.log("Data retrieved from AllTimeActivityBynetwork metric: " + JSON.stringify(result))

    var colorList = [tailwindConfig().theme.colors.indigo[500],tailwindConfig().theme.colors.blue[400], tailwindConfig().theme.colors.emerald[500],tailwindConfig().theme.colors.slate[500]]

    this.chartData = ref({})
    this.chartData.datasets = []
    for (const [key, value] of Object.entries(result.body)) {
      if(key == "date_label")
        this.chartData.labels = value

      if(key == "datasets") {
        var i = 0
        for (const [dataset, datav] of Object.entries(value)) {
          var newDataset = {
            label: dataset,
            data: datav,
            borderColor: colorList[i],
            fill: false,
            borderWidth: 2,
            tension: 0,
            pointRadius: 0,
            pointHoverRadius: 3,
            pointBackgroundColor: colorList[i],
            clip: 20
          }
          this.chartData.datasets.push(newDataset)
          i++
        }
      }
    }
    this.$forceUpdate()
  }
}
</script>
