<template>
	<div class="flex flex-col col-span-full bg-white shadow-lg rounded-sm border border-slate-200">

		<div v-if="!isLoaded">
			<div class="flex gap-32 mt-4 ml-4">
				<div class="bg-gray-200 w-32 animate-pulse h-5 rounded-2xl"></div>
				<div class="bg-gray-200 w-32 animate-pulse h-5 rounded-2xl"></div>
				<div class="bg-gray-200 w-32 animate-pulse h-5 rounded-2xl"></div>
				<div class="bg-gray-200 w-32 animate-pulse h-5 rounded-2xl"></div>
			</div>

			<div class="flex gap-32 mt-4 pb-4 ml-4">
				<div class="bg-gray-200 w-32 animate-pulse h-5 rounded-2xl"></div>
				<div class="bg-gray-200 w-32 animate-pulse h-5 rounded-2xl"></div>
				<div class="bg-gray-200 w-32 animate-pulse h-5 rounded-2xl"></div>
				<div class="bg-gray-200 w-32 animate-pulse h-5 rounded-2xl"></div>
			</div>
		</div>

		<div v-if="isLoaded">
			<div class="grid grid-cols-12 px-5 py-4 items-left gap-3">
				<div class="text-sm mb-4 col-span-3">
					<h3 class="font-medium text-slate-800">Audience</h3>
					<div>{{ audience }}</div>
				</div>
				<div class="text-sm mb-4 col-span-3">
					<h3 class="flex font-medium text-slate-800">
						<div class="mr-2">Last Project Interaction</div>
					</h3>
					<div>{{ lastTransaction }}</div>
				</div>
				<div class="text-sm mb-4 col-span-3">
					<h3 class="font-medium text-slate-800">Last Activity On-Chain</h3>
					<div>{{ lastActivity }}</div>
				</div>
				<div class="text-sm mb-4 col-span-3">
					<h3 class="flex font-medium text-slate-800">
						<div class="mr-2">Best Marketing Channel</div>
						<Tooltip size="lg" bg="dark" position="bottom">
							<div class="text-sm font-medium text-slate-200">Generated by Raleon Insights. The number in
								parenthesis
								represents how many connections have been made.<br /><a href="/support/insights"
									class="text-white mt-1" target="_blank">Learn more -></a></div>
						</Tooltip>
					</h3>
					<div class="flex" v-if="!attributionStatus">
						<svg class="shrink-0 h-6 w-6" viewBox="0 0 24 24">
							<path class="fill-current text-indigo-500"
								d="M19.714 14.7l-7.007 7.007-1.414-1.414 7.007-7.007c-.195-.4-.298-.84-.3-1.286a3 3 0 113 3 2.969 2.969 0 01-1.286-.3z" />
							<path class="fill-current text-indigo-300"
								d="M10.714 18.3c.4-.195.84-.298 1.286-.3a3 3 0 11-3 3c.002-.446.105-.885.3-1.286l-6.007-6.007 1.414-1.414 6.007 6.007z" />
							<path class="fill-current text-indigo-500"
								d="M5.7 10.714c.195.4.298.84.3 1.286a3 3 0 11-3-3c.446.002.885.105 1.286.3l7.007-7.007 1.414 1.414L5.7 10.714z" />
							<path class="fill-current text-indigo-300"
								d="M19.707 9.292a3.012 3.012 0 00-1.415 1.415L13.286 5.7c-.4.195-.84.298-1.286.3a3 3 0 113-3 2.969 2.969 0 01-.3 1.286l5.007 5.006z" />
						</svg>
						<span class="text-xs ml-2 pt-0.5"><a href="/campaigns/Overview" class="text-ralpurple-500">Setup
								Campaigns</a></span>
					</div>
					<div v-else-if="attributionStatus">
						{{ bestMarketingChannel }}
					</div>
				</div>
				<div class="text-sm mb-4 col-span-3">
					<h3 class="font-medium text-slate-800">Customer Since</h3>
					<div>{{ firstTransaction }}</div>
				</div>
				<div class="text-sm mb-4 col-span-3">
					<h3 class="font-medium text-slate-800">Average Transaction Size</h3>
					<div>{{ avgTokenTxnAmt }}</div>
				</div>
				<div class="text-sm mb-4 col-span-3">
					<h3 class="font-medium text-slate-800"># Of Connections</h3>
					<div>
						<div class="flex" v-if="!attributionStatus">
							<svg class="shrink-0 h-6 w-6" viewBox="0 0 24 24">
								<path class="fill-current text-indigo-500"
									d="M19.714 14.7l-7.007 7.007-1.414-1.414 7.007-7.007c-.195-.4-.298-.84-.3-1.286a3 3 0 113 3 2.969 2.969 0 01-1.286-.3z" />
								<path class="fill-current text-indigo-300"
									d="M10.714 18.3c.4-.195.84-.298 1.286-.3a3 3 0 11-3 3c.002-.446.105-.885.3-1.286l-6.007-6.007 1.414-1.414 6.007 6.007z" />
								<path class="fill-current text-indigo-500"
									d="M5.7 10.714c.195.4.298.84.3 1.286a3 3 0 11-3-3c.446.002.885.105 1.286.3l7.007-7.007 1.414 1.414L5.7 10.714z" />
								<path class="fill-current text-indigo-300"
									d="M19.707 9.292a3.012 3.012 0 00-1.415 1.415L13.286 5.7c-.4.195-.84.298-1.286.3a3 3 0 113-3 2.969 2.969 0 01-.3 1.286l5.007 5.006z" />
							</svg>
							<span class="text-xs ml-2 pt-0.5"><a href="/campaigns/Overview" class="text-ralpurple-500">Setup
									Campaigns</a></span>
						</div>
						<div v-else-if="attributionStatus">
							{{ connectionCount }}
						</div>
					</div>
				</div>
				<div class="text-sm mb-4 col-span-3">
					<h3 class="font-medium text-slate-800">Conversion %</h3>
					<div class="flex" v-if="!attributionStatus">
						<svg class="shrink-0 h-6 w-6" viewBox="0 0 24 24">
							<path class="fill-current text-indigo-500"
								d="M19.714 14.7l-7.007 7.007-1.414-1.414 7.007-7.007c-.195-.4-.298-.84-.3-1.286a3 3 0 113 3 2.969 2.969 0 01-1.286-.3z" />
							<path class="fill-current text-indigo-300"
								d="M10.714 18.3c.4-.195.84-.298 1.286-.3a3 3 0 11-3 3c.002-.446.105-.885.3-1.286l-6.007-6.007 1.414-1.414 6.007 6.007z" />
							<path class="fill-current text-indigo-500"
								d="M5.7 10.714c.195.4.298.84.3 1.286a3 3 0 11-3-3c.446.002.885.105 1.286.3l7.007-7.007 1.414 1.414L5.7 10.714z" />
							<path class="fill-current text-indigo-300"
								d="M19.707 9.292a3.012 3.012 0 00-1.415 1.415L13.286 5.7c-.4.195-.84.298-1.286.3a3 3 0 113-3 2.969 2.969 0 01-.3 1.286l5.007 5.006z" />
						</svg>
						<span class="text-xs ml-2 pt-0.5"><a href="/campaigns/Overview" class="text-ralpurple-500">Setup
								Campaigns</a></span>
					</div>
					<div v-else-if="attributionStatus">
						{{ conversionPercent }}
					</div>
				</div>
				<!--
      <div class="text-sm mb-4">
        <h3 class="flex font-medium text-slate-800">
          <div class="mr-2">Sentiment Toward You</div>
          <Tooltip size="lg" bg="dark" position="bottom">
            <div class="text-sm font-medium text-slate-200">This is an estimate based on Raleon's insights engine as a result of activity on social channels.</div>
          </Tooltip>
        </h3>
        <div>
          <div class="text-xs mr-1.5 font-medium bg-emerald-100 text-emerald-600 rounded-full text-center px-2.5 py-1" style="width: 100px;">
            Good
          </div>
        </div>
      </div>
    -->
			</div>
		</div>
	</div>
</template>

<script>
import { ref } from 'vue'
import DoughnutChart from '../../../charts/DoughnutChart.vue'
import ReportCardFooter from '../../../components/ReportCardFooter.vue'
import ReportCardHeader from '../../../components/ReportCardHeader.vue'
import Tooltip from '../../../components/Tooltip.vue'
import { getAddressLTV, getMetric, getUTMWalletConnectionCount, getUtmWalletSources } from "../../../services/metrics"
import { formatDate, tailwindConfig } from '../../../utils/Utils'

export default {
	name: 'ProfileSummary',
	props: ['walletAddress', 'attributionStatus', 'projectId'],
	components: {
		DoughnutChart,
		ReportCardHeader,
		ReportCardFooter,
		Tooltip,
	},
	setup() {
		const chartData = ref({
			labels: ["Transfer", "Stake", "Reward", "Mint", "Sign"],
			datasets: [
				{
					label: 'Sessions By Device',
					data: [
						500, 400, 800, 7000, 12000,
					],
					backgroundColor: [
						tailwindConfig().theme.colors.indigo[500],
						tailwindConfig().theme.colors.sky[400],
						tailwindConfig().theme.colors.indigo[800],
						tailwindConfig().theme.colors.emerald[800],
						tailwindConfig().theme.colors.blue[800],
					],
					hoverBackgroundColor: [
						tailwindConfig().theme.colors.indigo[600],
						tailwindConfig().theme.colors.sky[500],
						tailwindConfig().theme.colors.indigo[900],
						tailwindConfig().theme.colors.emerald[800],
						tailwindConfig().theme.colors.blue[800],
					],
					hoverBorderColor: tailwindConfig().theme.colors.white,
				},
			],
		})

		return {
			chartData,
		}
	},
	data() {
		return {
			ltv: '$350',
			walletAge: 0,
			audience: 'TBD',
			lastActivity: 'TBD',
			firstTransaction: 'TBD',
			lastTransaction: 'TBD',
			avgTokenTxnAmt: 'TBD',
			connectionCount: 'TBD',
			bestMarketingChannel: 'TBD',
			conversionPercent: 'TBD',
			isLoaded: false
		}
	},
	methods: {
		async getLastActivityDate() {
			const lastActivity = await getMetric(
				'ETH',
				this.walletAddress,
				'ADDRESS_LAST_TRANSACTION'
			);
			if (lastActivity.body && lastActivity.body.Items.length && lastActivity.body.Items[0].data) {
				this.lastActivity = formatDate(lastActivity.body.Items[0].data);
			}
		},
		async getAddressLastTransaction() {
			const lastTransaction = await getMetric(
				'ANY',
				`${this.walletAddress}_${this.projectId}`,
				'PROJECT_ADDRESS_LAST_TRANSACTION'
			);

			if (lastTransaction.body && lastTransaction.body.Items.length && lastTransaction.body.Items[0].data) {
				this.lastTransaction = formatDate(lastTransaction.body.Items[0].data);
			} else {
				this.lastTransaction = 'No Interactions';
			}
		},
		async getAddressFirstTransaction() {
			const firstTransaction = await getMetric(
				'ANY',
				`${this.walletAddress}_${this.projectId}`,
				'PROJECT_ADDRESS_FIRST_TRANSACTION'
			);

			if (firstTransaction.body && firstTransaction.body.Items.length && firstTransaction.body.Items[0].data) {
				this.firstTransaction = formatDate(firstTransaction.body.Items[0].data);
			} else {
				this.firstTransaction = 'No Interactions';
			}
		},
		async getAddressAverageTokenTransferAmt() {
			const avgTokenTxnAmt = await getMetric(
				'ANY',
				`${this.walletAddress}_${this.projectId}`,
				'PROJECT_ADDRESS_AVG_TOKEN_TRANSFER_AMOUNT'
			);


			if (avgTokenTxnAmt.body && avgTokenTxnAmt.body.Items.length && avgTokenTxnAmt.body.Items[0].data) {
				this.avgTokenTxnAmt = `${avgTokenTxnAmt.body.Items[0].data.toFixed(2)} USD`;
			} else {
				this.avgTokenTxnAmt = 'No Transactions';
			}
		},
		async getUtmWalletConnections() {
			const walletConnectionCount = await getUTMWalletConnectionCount(
				localStorage.getItem('userOrgId') || '1',
				this.walletAddress
			);

			if (walletConnectionCount.body && walletConnectionCount.body.length) {
				this.isLoaded = true;
				return walletConnectionCount.body[0].data;
			} else {
				this.isLoaded = true;
				return 0;
			}
		},
		async getUtmWalletSources() {
			const sources = await getUtmWalletSources(
				localStorage.getItem('userOrgId') || '1',
				this.walletAddress
			);

			if (sources.body.length && sources.body[0].data.length) {
				const data = sources.body[0].data;
				const highestCount = data.reduce((prev, curr) => prev.count > curr.count ? prev : curr);
				if (highestCount && highestCount.utmSource) {
					const utmSourceName = highestCount.utmSource.charAt(0).toUpperCase() + highestCount.utmSource.slice(1);
					this.bestMarketingChannel = (`${utmSourceName} (${highestCount.count})`);
					this.conversionPercent = '100%'
				}
			} else {
				this.bestMarketingChannel = 'No Conversions Yet';
				this.conversionPercent = 'No Conversions Yet';
			}
		},
		getAudience() {
			if (this.lastTransaction !== 'TBD' && this.lastTransaction !== 'No Interactions') {
				const lastTxnDate = new Date(this.lastTransaction);
				const todaysDate = new Date();
				const oneDay = 24 * 60 * 60 * 1000;
				const diffDays = Math.round(Math.abs((lastTxnDate - todaysDate) / oneDay));
				console.log(diffDays);
				switch (true) {
					case (diffDays <= 7):
						this.audience = 'New Users';
						break;
					case (diffDays <= 30):
						this.audience = 'Active Users';
						break;
					case (diffDays <= 90 && diffDays >= 30):
						this.audience = 'At-Risk Users';
						break;
					case (diffDays > 90):
						this.audience = 'Dormant Users';
						break;
					default:
						this.audience = 'TBD';
				}
			} else {
				this.audience = 'No Audience';
			}
		},
		async fetchProjectBasedMetrics() {
			//Adam removing due to costs
			//await this.getAddressLastTransaction();
			//await this.getAddressFirstTransaction();
			//await this.getAddressAverageTokenTransferAmt();
			this.lastTransaction = 'Processing...';
			this.firstTransaction = 'Processing...';
			this.avgTokenTxnAmt = 'Processing...';

		},
	},
	async mounted() {
		await this.getLastActivityDate();
		await this.fetchProjectBasedMetrics();
		await this.getUtmWalletSources();
		this.connectionCount = await this.getUtmWalletConnections();
		this.getAudience();

		this.walletAge = "92 days";

		const ltv_result = await getAddressLTV('ETH', this.walletAddress)
		this.ltv = ltv_result.Items.data.ltv

		this.$forceUpdate()
	},
	watch: {
		projectId: async function (newVal, oldVal) {
			await this.fetchProjectBasedMetrics();
			this.getAudience();

			// this.$forceUpdate()
		}
	},
}
</script>
