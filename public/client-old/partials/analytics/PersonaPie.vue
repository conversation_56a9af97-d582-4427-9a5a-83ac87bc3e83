<template>
  <div class="flex flex-col col-span-full sm:col-span-6 xl:col-span-4 bg-white shadow-lg rounded-sm border border-slate-200">
    <ReportCardHeader title="What Persona's Make Up Your Community" />

    <DoughnutChart :data="chartData" totalVal="20723" totalValLabel="" totalIndicator="0" width="389" height="260" />

    <ReportCardFooter reportLinkLabel="View Interactions Report" reportLink="#00"/>
  </div>
</template>

<script>
import { ref } from 'vue'
import DoughnutChart from '../../charts/DoughnutChart.vue'
import ReportCardHeader from '../../components/ReportCardHeader.vue'
import ReportCardFooter from '../../components/ReportCardFooter.vue'

// Import utilities
import { tailwindConfig } from '../../utils/Utils'

export default {
  name: 'Wallet Usage',
  components: {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>eader,
    ReportCardFooter
  },
  setup() {
    const chartData = ref({
      labels: ["New Wallet", "DeFi", "Collector", "Trader", "Holder" ],
      datasets: [
        {
          label: 'Sessions By Device',
          data: [
            800, 200, 500, 900, 1000,
          ],
          backgroundColor: [
            tailwindConfig().theme.colors.indigo[500],
            tailwindConfig().theme.colors.sky[400],
            tailwindConfig().theme.colors.indigo[800],
            tailwindConfig().theme.colors.emerald[800],
            tailwindConfig().theme.colors.blue[800],
          ],
          hoverBackgroundColor: [
            tailwindConfig().theme.colors.indigo[600],
            tailwindConfig().theme.colors.sky[500],
            tailwindConfig().theme.colors.indigo[900],
            tailwindConfig().theme.colors.emerald[800],
            tailwindConfig().theme.colors.blue[800],
          ],
          hoverBorderColor: tailwindConfig().theme.colors.white,
        },
      ],
    })

    return {
      chartData,
    } 
  }
}
</script>