<template>
  <div class="flex flex-col col-span-full sm:col-span-8 bg-white shadow-lg rounded-sm border border-slate-200">
    <ReportCardHeader title="New vs. Returning User Activity" />

    <!-- adjustment = inc or dec; summaryBool 1 or 0 -->
    <LineChart :data="chartData" totalVal="20.2K" percentVal="-22" indicator="dec" summary="0" width="595" height="248" />

    <ReportCardFooter reportLinkLabel="User Activity Report" reportLink="#00"/>
  </div>
</template>

<script>
import { ref } from 'vue'
import LineChart from '../../charts/LineChartOverTime.vue'
import ReportCardFooter from '../../components/ReportCardFooter.vue'
import ReportCardHeader from '../../components/ReportCardHeader.vue'

// Import utilities
import { tailwindConfig } from '../../utils/Utils'

export default {
  name: 'NewVsReturningUser',
  components: {
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>eader,
    ReportCardFooter
  },
  setup() {
    const chartData = ref({
      labels: [
        '12-01-2020', '01-01-2021', '02-01-2021',
        '03-01-2021', '04-01-2021', '05-01-2021',
        '06-01-2021', '07-01-2021', '08-01-2021',
        '09-01-2021', '10-01-2021', '11-01-2021',
        '12-01-2021', '01-01-2022', '02-01-2022',
        '03-01-2022', '04-01-2022', '05-01-2022',
        '06-01-2022', '07-01-2022', '08-01-2022',
        '09-01-2022', '10-01-2022', '11-01-2022',
        '12-01-2022', '01-01-2023',
      ],
      datasets: [
        // Indigo line
        {
          label: 'New User',
          data: [
            73, 64, 73, 69, 104, 104, 164,
            164, 120, 120, 120, 148, 142, 104,
            122, 110, 104, 152, 166, 233, 268,
            252, 284, 284, 333, 323,
          ],
          borderColor: tailwindConfig().theme.colors.indigo[500],
          fill: false,
          borderWidth: 2,
          tension: 0,
          pointRadius: 0,
          pointHoverRadius: 3,
          pointBackgroundColor: tailwindConfig().theme.colors.indigo[500],
          clip: 20,
        },
        // Blue line
        {
          label: 'Returning',
          data: [
            184, 86, 42, 378, 42, 243, 38,
            120, 0, 0, 42, 0, 84, 0,
            276, 0, 124, 42, 124, 88, 88,
            215, 156, 88, 124, 64,
          ],
          borderColor: tailwindConfig().theme.colors.slate[500],
          fill: false,
          borderWidth: 2,
          tension: 0,
          pointRadius: 0,
          pointHoverRadius: 3,
          pointBackgroundColor: tailwindConfig().theme.colors.slate[500],
          clip: 20,
        },
      ],
    })

    return {
      chartData,
    }
  }
}
</script>
