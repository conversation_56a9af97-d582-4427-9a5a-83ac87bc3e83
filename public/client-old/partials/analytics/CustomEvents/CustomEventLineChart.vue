<template>
   <div
     class="
       flex flex-col
       col-span-full
       bg-white
       shadow-lg
       rounded-sm
       sm:col-span-6
       xl-col-span-6
       border border-slate-200
     "
   >
     <ReportCardHeader
       :title="widgetData.title"
       @edit-widget="editWidget"
       @remove-widget="removeWidget"
       :editMenu="editMenu"
     />

     <!-- adjustment = inc or dec; summaryBool 1 or 0 -->
     <LineChart :data="widgetData.chartData" width="595" height="150" />
   </div>
 </template>

 <script>
 import {ref} from 'vue';
 import _ from 'underscore';
 import ReportCardHeader from '../../../components/ReportCardHeader.vue';
 import LineChart from '../../../charts/LineChartOverTime.vue';

 // Import utilities
 import {tailwindConfig} from '../../../utils/Utils';

 export default {
   name: 'CustomEventLineChart',
   props: ['campaignId', 'widgetData','editMenu'],
   emits: ['editWidget', 'removeWidget'],
   components: {
     LineChart,
     ReportCardHeader,
   },
   methods: {
     editWidget() {
       this.$emit('editWidget', this.widgetData.id);
     },
     removeWidget() {
       this.$emit('removeWidget', this.widgetData.id);
     },
   },
   setup() {
     const chartData = ref(null);

     return {
       chartData,
     };
   },
   async mounted() {
      let colors = [
         tailwindConfig().theme.colors.indigo[500],
         tailwindConfig().theme.colors.sky[400],
         tailwindConfig().theme.colors.indigo[800],
         tailwindConfig().theme.colors.emerald[800],
         tailwindConfig().theme.colors.blue[800],
         tailwindConfig().theme.colors.orange[500],
         tailwindConfig().theme.colors.lime[500],
         tailwindConfig().theme.colors.pink[700],
         tailwindConfig().theme.colors.slate[500],
         tailwindConfig().theme.colors.gray[400],
         tailwindConfig().theme.colors.zinc[800],
         tailwindConfig().theme.colors.red[800],
         tailwindConfig().theme.colors.amber[800],
         tailwindConfig().theme.colors.teal[500],
         tailwindConfig().theme.colors.cyan[500],
         tailwindConfig().theme.colors.violet[700],
         tailwindConfig().theme.colors.indigo[200],
         tailwindConfig().theme.colors.sky[200],
         tailwindConfig().theme.colors.indigo[400],
         tailwindConfig().theme.colors.emerald[400],
         tailwindConfig().theme.colors.blue[400],
         tailwindConfig().theme.colors.orange[200],
         tailwindConfig().theme.colors.lime[200],
         tailwindConfig().theme.colors.pink[300],
         tailwindConfig().theme.colors.indigo[900],
         tailwindConfig().theme.colors.sky[900],
         tailwindConfig().theme.colors.indigo[900],
         tailwindConfig().theme.colors.emerald[900],
         tailwindConfig().theme.colors.blue[900],
         tailwindConfig().theme.colors.orange[900],
         tailwindConfig().theme.colors.lime[900],
         tailwindConfig().theme.colors.pink[900],
         tailwindConfig().theme.colors.slate[200],
         tailwindConfig().theme.colors.gray[200],
         tailwindConfig().theme.colors.zinc[200],
         tailwindConfig().theme.colors.red[200],
         tailwindConfig().theme.colors.amber[200],
         tailwindConfig().theme.colors.teal[200],
         tailwindConfig().theme.colors.cyan[200],
         tailwindConfig().theme.colors.violet[200],
         tailwindConfig().theme.colors.slate[400],
         tailwindConfig().theme.colors.gray[400],
         tailwindConfig().theme.colors.zinc[400],
         tailwindConfig().theme.colors.red[400],
         tailwindConfig().theme.colors.amber[400],
         tailwindConfig().theme.colors.teal[400],
         tailwindConfig().theme.colors.cyan[400],
         tailwindConfig().theme.colors.violet[400],
         tailwindConfig().theme.colors.slate[600],
         tailwindConfig().theme.colors.gray[600],
         tailwindConfig().theme.colors.zinc[600],
         tailwindConfig().theme.colors.red[600],
         tailwindConfig().theme.colors.amber[600],
         tailwindConfig().theme.colors.teal[600],
         tailwindConfig().theme.colors.cyan[600],
         tailwindConfig().theme.colors.violet[600],
      ];

     this.chartData = {
       labels: [
       ],
       datasets: [
       ],
     };

     this.$forceUpdate();
   },
 };
 </script>
