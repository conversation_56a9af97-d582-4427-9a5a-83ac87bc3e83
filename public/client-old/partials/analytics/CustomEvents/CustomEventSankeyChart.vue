<style scoped>
input[type="search"].show-clear {
	-webkit-appearance: searchfield !important;
}

input[type="search"].show-clear::-webkit-search-cancel-button {
	-webkit-appearance: searchfield-cancel-button !important;
}
</style>
<template>
  <div
    class="
      flex flex-col
      col-span-full
      bg-white
      shadow-lg
      rounded-sm
      sm:col-span-6
      xl-col-span-6
      border border-slate-200
    "
  >
    <ReportCardHeader
      :title="title"
      editMenu="false"
    />
    <div class="relative pl-8 pr-8 py-4 mb-4 h-72 overflow-hidden" v-if="!isLoaded && isLoading">
      <div class="bg-gray-200 w-full animate-pulse h-4 rounded-2xl mb-6"></div>
      <div class="bg-gray-200 w-full h-full rounded-xl animate-pulse">
        <div class="flex justify-center align-middle animate-pulse pt-8">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            class="h-36 w-36"
            stroke="#FFFFFF "
            stroke-width="1"
          >
            <path fill="#FFFFFF" />
            <path
              fill="#FFFFFF"
              d="M5 3v16h16v2H3V3h2zm15.293 3.293l1.414 1.414L16 13.414l-3-2.999-4.293 4.292-1.414-1.414L13 7.586l3 2.999 4.293-4.292z"
            />
          </svg>
        </div>
      </div>
    </div>

    <div class="flex flex-row flex-grow px-5 py-5" v-if="!isLoading && eventOptions">
      <div class="w-full px-5">
        <label class="block text-sm font-medium mb-1" for="role"
          >Start Event</label
        >
        <DropdownFull
          :clearOnClick="true"
          :options="applicationId !== undefined ? applicationFilteredEventTypes : eventOptions"
          :selectedId="startEventId"
          v-on:change="startEventChanged"
        />
      </div>

      <div class="w-full pr-5">
        <label class="block text-sm font-medium mb-1" for="role"
          >End Event</label
        >
        <DropdownFull
          :clearOnClick="true"
          :options="applicationId !== undefined ? applicationFilteredEventTypes : eventOptions"
          :selectedId="endEventId"
          v-on:change="endEventChanged"
        />
      </div>
	  <div class="w-full pr-5">
        <label class="block text-sm font-medium mb-1" for="role"
          >Application Filter</label
        >
        <DropdownFull
          :clearOnClick="true"
          :options="applicationOptions"
          :selectedId="applicationId"
          @change="applicationFilterChanged"
        />
      </div>
    </div>

    <div class="px-5 pb-5" v-if="!isLoading && !isLoaded && eventOptions">
      <b class="text-sm mt-1">
        Select an end event to see a User Flow chart depicting the journeys your users take through your dApp.
      </b>
      <svg class="px-5 pt-5" width="1200" viewBox="0,0,1200,400" style="max-width: 100%;height: auto;opacity: 0.25;height: intrinsic;"><g stroke="currentColor"><rect x="1" y="5" height="379.003717472119" width="15" fill="grey"><title>landingpage
        5,664</title></rect><rect x="296.75" y="5" height="140.2527881040892" width="15" fill="grey"><title>receivedquote-1
        2,096</title></rect><rect x="592.5" y="5" height="99.97026022304831" width="15" fill="grey"><title>swap-2
        1,494</title></rect><rect x="888.25" y="10.155892472369544" height="78.22304832713769" width="15" fill="grey"><title>receivedquote-3
        1,169</title></rect><rect x="1184" y="248.12964254023677" height="49.449814126394074" width="15" fill="grey"><title>deposited
        739</title></rect><rect x="296.75" y="155.2527881040892" height="115.42750929368023" width="15" fill="grey"><title>OTHER-1
        1,725</title></rect><rect x="296.75" y="280.68029739776955" height="37.33828996282523" width="15" fill="grey"><title>swap-1
        558</title></rect><rect x="592.5" y="271.8497321940973" height="45.10037174721185" width="15" fill="grey"><title>receivedquote-2
        674</title></rect><rect x="888.25" y="278.9679457078393" height="28.639405204460957" width="15" fill="grey"><title>swap-3
        428</title></rect><rect x="888.25" y="170.34354230962188" height="6.089219330855144" width="15" fill="grey"><title>OTHER-3
        91</title></rect><rect x="296.75" y="328.0185873605948" height="66.98141263940522" width="15" fill="grey"><title>usertrywhitelist-1
        1,001</title></rect><rect x="592.5" y="336.87524037212575" height="36.26765799256498" width="15" fill="grey"><title>OTHER-2
        542</title></rect><rect x="592.5" y="147.7777814161754" height="6.4907063197026105" width="15" fill="grey"><title>usertrywhitelist-2
        97</title></rect><rect x="888.25" y="338.8429022033577" height="15.189591078066883" width="15" fill="grey"><title>usertrywhitelist-3
        227</title></rect></g><g fill="none" stroke-opacity="0.5"><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-0" gradientUnits="userSpaceOnUse" x1="16" x2="296.75"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M16,75.1263940520446C156.375,75.1263940520446,156.375,75.1263940520446,296.75,75.1263940520446" stroke="url(#O-e983290e92922-link-0)" stroke-width="140.2527881040892"><title>landingpage → receivedquote-1
        2,096</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-1" gradientUnits="userSpaceOnUse" x1="311.75" x2="592.5"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M311.75,53.07806691449814C452.125,53.07806691449814,452.125,53.07806691449814,592.5,53.07806691449814" stroke="url(#O-e983290e92922-link-1)" stroke-width="96.15613382899627"><title>receivedquote-1 → swap-2
        1,437</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-2" gradientUnits="userSpaceOnUse" x1="607.5" x2="888.25"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M607.5,41.76951672862453C747.875,41.76951672862453,747.875,46.925409200994075,888.25,46.925409200994075" stroke="url(#O-e983290e92922-link-2)" stroke-width="73.53903345724906"><title>swap-2 → receivedquote-3
        1,099</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-3" gradientUnits="userSpaceOnUse" x1="16" x2="1184"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M16,270.182156133829C600,270.182156133829,600,257.63150127629626,1184,257.63150127629626" stroke="url(#O-e983290e92922-link-3)" stroke-width="19.003717472118957"><title>landingpage → deposited
        284</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-4" gradientUnits="userSpaceOnUse" x1="16" x2="296.75"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M16,202.96654275092936C156.375,202.96654275092936,156.375,212.96654275092936,296.75,212.96654275092936" stroke="url(#O-e983290e92922-link-4)" stroke-width="115.42750929368029"><title>landingpage → OTHER-1
        1,725</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-5" gradientUnits="userSpaceOnUse" x1="16" x2="296.75"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M16,298.3531598513011C156.375,298.3531598513011,156.375,299.34944237918216,296.75,299.34944237918216" stroke="url(#O-e983290e92922-link-5)" stroke-width="37.33828996282527"><title>landingpage → swap-1
        558</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-6" gradientUnits="userSpaceOnUse" x1="311.75" x2="592.5"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M311.75,295.9368029739777C452.125,295.9368029739777,452.125,293.2623715993018,592.5,293.2623715993018" stroke="url(#O-e983290e92922-link-6)" stroke-width="28.90706319702602"><title>swap-1 → receivedquote-2
        432</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-7" gradientUnits="userSpaceOnUse" x1="607.5" x2="888.25"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M607.5,287.1062377703055C747.875,287.1062377703055,747.875,292.7523323249397,888.25,292.7523323249397" stroke="url(#O-e983290e92922-link-7)" stroke-width="25.82899628252788"><title>receivedquote-2 → swap-3
        386</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-8" gradientUnits="userSpaceOnUse" x1="607.5" x2="888.25"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M607.5,79.64312267657992C747.875,79.64312267657992,747.875,171.44763152895274,888.25,171.44763152895274" stroke="url(#O-e983290e92922-link-8)" stroke-width="2.20817843866171"><title>swap-2 → OTHER-3
        33</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-9" gradientUnits="userSpaceOnUse" x1="903.25" x2="1184"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M903.25,170.54428580404567C1043.625,170.54428580404567,1043.625,276.96979123912155,1184,276.96979123912155" stroke="url(#O-e983290e92922-link-9)" stroke-width="1"><title>OTHER-3 → deposited
        6</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-10" gradientUnits="userSpaceOnUse" x1="16" x2="296.75"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M16,350.51301115241637C156.375,350.51301115241637,156.375,361.5092936802974,296.75,361.5092936802974" stroke="url(#O-e983290e92922-link-10)" stroke-width="66.98141263940519"><title>landingpage → usertrywhitelist-1
        1,001</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-11" gradientUnits="userSpaceOnUse" x1="311.75" x2="592.5"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M311.75,366.5613382899628C452.125,366.5613382899628,452.125,357.15033330892874,592.5,357.15033330892874" stroke="url(#O-e983290e92922-link-11)" stroke-width="31.98513011152416"><title>usertrywhitelist-1 → OTHER-2
        478</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-12" gradientUnits="userSpaceOnUse" x1="607.5" x2="1184"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M607.5,341.492340743873C895.75,341.492340743873,895.75,292.0924678190472,1184,292.0924678190472" stroke="url(#O-e983290e92922-link-12)" stroke-width="2.944237918215613"><title>OTHER-2 → deposited
        44</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-13" gradientUnits="userSpaceOnUse" x1="311.75" x2="592.5"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M311.75,158.53159851301112C452.125,158.53159851301112,452.125,151.72573680650996,592.5,151.72573680650996" stroke="url(#O-e983290e92922-link-13)" stroke-width="3.7472118959107803"><title>OTHER-1 → usertrywhitelist-2
        56</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-14" gradientUnits="userSpaceOnUse" x1="311.75" x2="592.5"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M311.75,105.33828996282527C452.125,105.33828996282527,452.125,338.7153890710105,592.5,338.7153890710105" stroke="url(#O-e983290e92922-link-14)" stroke-width="3.6802973977695164"><title>receivedquote-1 → OTHER-2
        55</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-15" gradientUnits="userSpaceOnUse" x1="607.5" x2="888.25"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M607.5,343.9347199260291C747.875,343.9347199260291,747.875,306.637090689252,888.25,306.637090689252" stroke="url(#O-e983290e92922-link-15)" stroke-width="1.940520446096654"><title>OTHER-2 → swap-3
        29</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-16" gradientUnits="userSpaceOnUse" x1="607.5" x2="1184"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M607.5,81.14869888475835C895.75,81.14869888475835,895.75,267.53484700120333,1184,267.53484700120333" stroke="url(#O-e983290e92922-link-16)" stroke-width="1"><title>swap-2 → deposited
        12</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-17" gradientUnits="userSpaceOnUse" x1="311.75" x2="592.5"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M311.75,102.19330855018586C452.125,102.19330855018586,452.125,148.814956137365,592.5,148.814956137365" stroke="url(#O-e983290e92922-link-17)" stroke-width="2.074349442379182"><title>receivedquote-1 → usertrywhitelist-2
        31</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-18" gradientUnits="userSpaceOnUse" x1="607.5" x2="888.25"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M607.5,338.44773107844543C747.875,338.44773107844543,747.875,86.80645009318738,888.25,86.80645009318738" stroke="url(#O-e983290e92922-link-18)" stroke-width="3.144981412639405"><title>OTHER-2 → receivedquote-3
        47</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-19" gradientUnits="userSpaceOnUse" x1="311.75" x2="1184"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M311.75,335.88104089219325C747.875,335.88104089219325,747.875,285.16681726142633,1184,285.16681726142633" stroke="url(#O-e983290e92922-link-19)" stroke-width="10.907063197026021"><title>usertrywhitelist-1 → deposited
        163</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-20" gradientUnits="userSpaceOnUse" x1="311.75" x2="1184"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M311.75,162.54646840148695C747.875,162.54646840148695,747.875,274.6277838041773,1184,274.6277838041773" stroke="url(#O-e983290e92922-link-20)" stroke-width="4.282527881040892"><title>OTHER-1 → deposited
        64</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-21" gradientUnits="userSpaceOnUse" x1="607.5" x2="888.25"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M607.5,350.8603704836499C747.875,350.8603704836499,747.875,348.0771029468521,888.25,348.0771029468521" stroke="url(#O-e983290e92922-link-21)" stroke-width="11.91078066914498"><title>OTHER-2 → usertrywhitelist-3
        178</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-22" gradientUnits="userSpaceOnUse" x1="903.25" x2="1184"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M903.25,340.8503371475956C1043.625,340.8503371475956,1043.625,295.5720217223929,1184,295.5720217223929" stroke="url(#O-e983290e92922-link-22)" stroke-width="4.014869888475836"><title>usertrywhitelist-3 → deposited
        60</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-23" gradientUnits="userSpaceOnUse" x1="311.75" x2="592.5"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M311.75,168.16728624535313C452.125,168.16728624535313,452.125,275.32928609744306,592.5,275.32928609744306" stroke="url(#O-e983290e92922-link-23)" stroke-width="6.959107806691449"><title>OTHER-1 → receivedquote-2
        104</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-24" gradientUnits="userSpaceOnUse" x1="311.75" x2="592.5"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M311.75,310.6914498141264C452.125,310.6914498141264,452.125,340.8566530115309,592.5,340.8566530115309" stroke="url(#O-e983290e92922-link-24)" stroke-width="1"><title>swap-1 → OTHER-2
        9</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-25" gradientUnits="userSpaceOnUse" x1="903.25" x2="1184"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M903.25,11.895669424042406C1043.625,11.895669424042406,1043.625,269.9437689342888,1184,269.9437689342888" stroke="url(#O-e983290e92922-link-25)" stroke-width="3.4795539033457246"><title>receivedquote-3 → deposited
        52</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-26" gradientUnits="userSpaceOnUse" x1="903.25" x2="1184"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M903.25,280.0385776780995C1043.625,280.0385776780995,1043.625,278.50882469637054,1184,278.50882469637054" stroke="url(#O-e983290e92922-link-26)" stroke-width="2.141263940520446"><title>swap-3 → deposited
        32</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-27" gradientUnits="userSpaceOnUse" x1="607.5" x2="1184"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M607.5,274.05791063275905C895.75,274.05791063275905,895.75,277.30436372982786,1184,277.30436372982786" stroke="url(#O-e983290e92922-link-27)" stroke-width="1"><title>receivedquote-2 → deposited
        4</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-28" gradientUnits="userSpaceOnUse" x1="311.75" x2="592.5"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M311.75,345.9516728624535C452.125,345.9516728624535,452.125,312.333003569562,592.5,312.333003569562" stroke="url(#O-e983290e92922-link-28)" stroke-width="9.234200743494423"><title>usertrywhitelist-1 → receivedquote-2
        138</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-29" gradientUnits="userSpaceOnUse" x1="311.75" x2="592.5"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M311.75,329.2230483271375C452.125,329.2230483271375,452.125,103.76579925650557,592.5,103.76579925650557" stroke="url(#O-e983290e92922-link-29)" stroke-width="2.4089219330855016"><title>usertrywhitelist-1 → swap-2
        36</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-30" gradientUnits="userSpaceOnUse" x1="311.75" x2="592.5"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M311.75,155.95539033457248C452.125,155.95539033457248,452.125,101.85873605947954,592.5,101.85873605947954" stroke="url(#O-e983290e92922-link-30)" stroke-width="1.4052044609665426"><title>OTHER-1 → swap-2
        21</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-31" gradientUnits="userSpaceOnUse" x1="607.5" x2="888.25"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M607.5,148.54729814479992C747.875,148.54729814479992,747.875,84.46444265824314,888.25,84.46444265824314" stroke="url(#O-e983290e92922-link-31)" stroke-width="1.5390334572490705"><title>usertrywhitelist-2 → receivedquote-3
        23</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-32" gradientUnits="userSpaceOnUse" x1="607.5" x2="1184"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M607.5,151.5249933120862C895.75,151.5249933120862,895.75,272.08503287480926,1184,272.08503287480926" stroke="url(#O-e983290e92922-link-32)" stroke-width="1"><title>usertrywhitelist-2 → deposited
        12</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-33" gradientUnits="userSpaceOnUse" x1="607.5" x2="888.25"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M607.5,150.22016059833155C747.875,150.22016059833155,747.875,173.45506647319067,888.25,173.45506647319067" stroke="url(#O-e983290e92922-link-33)" stroke-width="1.8066914498141262"><title>usertrywhitelist-2 → OTHER-3
        27</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-34" gradientUnits="userSpaceOnUse" x1="607.5" x2="888.25"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M607.5,272.88690691528694C747.875,272.88690691528694,747.875,175.39558691928733,888.25,175.39558691928733" stroke="url(#O-e983290e92922-link-34)" stroke-width="2.074349442379182"><title>receivedquote-2 → OTHER-3
        31</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-35" gradientUnits="userSpaceOnUse" x1="607.5" x2="888.25"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M607.5,81.88475836431225C747.875,81.88475836431225,747.875,339.177474694064,888.25,339.177474694064" stroke="url(#O-e983290e92922-link-35)" stroke-width="1"><title>swap-2 → usertrywhitelist-3
        10</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-36" gradientUnits="userSpaceOnUse" x1="311.75" x2="1184"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M311.75,103.364312267658C747.875,103.364312267658,747.875,268.07016298633346,1184,268.07016298633346" stroke="url(#O-e983290e92922-link-36)" stroke-width="1"><title>receivedquote-1 → deposited
        4</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-37" gradientUnits="userSpaceOnUse" x1="607.5" x2="888.25"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M607.5,301.3255686253241C747.875,301.3255686253241,747.875,340.816879898525,888.25,340.816879898525" stroke="url(#O-e983290e92922-link-37)" stroke-width="2.6096654275092934"><title>receivedquote-2 → usertrywhitelist-3
        39</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-38" gradientUnits="userSpaceOnUse" x1="607.5" x2="888.25"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M607.5,152.361424538852C747.875,152.361424538852,747.875,279.4028899457575,888.25,279.4028899457575" stroke="url(#O-e983290e92922-link-38)" stroke-width="1"><title>usertrywhitelist-2 → swap-3
        13</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-39" gradientUnits="userSpaceOnUse" x1="311.75" x2="1184"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M311.75,281.41635687732344C747.875,281.41635687732344,747.875,279.64637116477206,1184,279.64637116477206" stroke="url(#O-e983290e92922-link-39)" stroke-width="1"><title>swap-1 → deposited
        2</title></path></g><g style="mix-blend-mode: multiply;"><linearGradient id="O-e983290e92922-link-40" gradientUnits="userSpaceOnUse" x1="311.75" x2="592.5"><stop offset="0%" stop-color="grey"></stop><stop offset="100%" stop-color="grey"></stop></linearGradient><path d="M311.75,281.01486988847586C452.125,281.01486988847586,452.125,153.93391524517165,592.5,153.93391524517165" stroke="url(#O-e983290e92922-link-40)" stroke-width="1"><title>swap-1 → usertrywhitelist-2
        10</title></path></g></g><g font-family="sans-serif" font-size="12"><text x="22" y="194.5018587360595" dy="0.35em" text-anchor="start"></text><text x="317.75" y="75.1263940520446" dy="0.35em" text-anchor="start"></text><text x="613.5" y="54.98513011152416" dy="0.35em" text-anchor="start"></text><text x="882.25" y="49.26741663593839" dy="0.35em" text-anchor="end"></text><text x="1178" y="272.85454960343384" dy="0.35em" text-anchor="end"></text><text x="317.75" y="212.9665427509293" dy="0.35em" text-anchor="start"></text><text x="317.75" y="299.34944237918216" dy="0.35em" text-anchor="start"></text><text x="613.5" y="294.39991806770325" dy="0.35em" text-anchor="start"></text><text x="882.25" y="293.2876483100698" dy="0.35em" text-anchor="end"></text><text x="882.25" y="173.38815197504945" dy="0.35em" text-anchor="end"></text><text x="317.75" y="361.5092936802974" dy="0.35em" text-anchor="start"></text><text x="613.5" y="355.00906936840823" dy="0.35em" text-anchor="start"></text><text x="613.5" y="151.0231345760267" dy="0.35em" text-anchor="start"></text><text x="882.25" y="346.**************" dy="0.35em" text-anchor="end"></text></g>
      </svg>
    </div>

    <SankeyChart
      v-if="isLoaded"
      :sankeyData="sankeyData"
      :divisor="divisor"
    />
    <div v-if="isLoaded" class="sankey-footer px-5 pb-5 flex flex-auto flex-row justify-between">
      <span class="sankey-start-label" v-text="startFooter"></span>
      <span class="sankey-end-label" v-text="endFooter"></span>
    </div>
  </div>
</template>

<script>
// Import utilities
import ReportCardHeader from '../../../components/ReportCardHeader.vue';
import SankeyChart from '../../../charts/SankeyChart.vue';
import { getCustomEventsSankey } from '../../../services/custom-events';
import { formatNumber } from '../../../utils/Utils';
import DropdownFull from '../../../components/DropdownFull.vue';

export default {
  name: 'CustomEventSankeyChart',
  props: [
    'eventTypes',
	'filteredEventTypes',
	'applications',
    'title',
    'width',
    'height',
    'fontSize'
  ],
  emits: [],
  components: {
    ReportCardHeader,
    SankeyChart,
    DropdownFull
  },
  methods: {
    startEventChanged(startEventId) {
      const startEvent = this.eventOptions.find(x => x.id === startEventId);
      this.startEvent = startEvent.name;
      this.startEventId = startEventId;
      if (this.startEvent && this.endEvent) {
        this.load();
      }
    },
    endEventChanged(endEventId) {
      const endEvent = this.eventOptions.find(x => x.id === endEventId);
      this.endEvent = endEvent.name;
      this.endEventId = endEventId;
      if (this.startEvent && this.endEvent) {
        this.load();
      }
    },
	applicationFilterChanged(applicationId) {
	  this.applicationId = applicationId;
	  this.startEvent = 'landing-page';
	  this.startEventId = this.applicationFilteredEventTypes?.find(x => x.name === this.startEvent)?.id;
	},
    async load() {
      this.isLoading = true;
      this.isLoaded = false;
      const startEvents = [this.startEvent.toLowerCase().replace(/[^a-z0-9]/gi, '')];
      const endEvents = !this.endEvent ? undefined : [this.endEvent.toLowerCase().replace(/[^a-z0-9]/gi, '')];
	  const applicationFilter = this.applicationOptions.find(x => x.id === this.applicationId);
	  const options = {
        startEvents,
        endEvents,
        events: {
          count: 100000,
          filterNonAlphanumericCharacters: true,
          normalizeEventNameCapitalization: true
        },
      };
	  if (applicationFilter) {
		options.events.applicationIdFilter = applicationFilter.id;
	  }
      const data = await getCustomEventsSankey(options);

      const journeyCount = data.vertices.filter(x => startEvents.includes(x.eventName)).reduce((a,x) => a + x.eventCount, 0);
      const completionCount = data.vertices.filter(x => endEvents.includes(x.eventName)).reduce((a,x) => a + x.eventCount, 0);
      const completionPercentage = completionCount / journeyCount * 100;

      this.divisor = journeyCount;

      this.sankeyData = data;
      this.isLoaded = true;
      this.isLoading = false;

      this.startFooter = `${formatNumber(journeyCount)} journeys initiated`;
      this.endFooter = `${formatNumber(completionCount)} journeys (${completionPercentage.toFixed(1)}%) reached target event`;
    }
  },
  data() {
    return {
      startEvent: 'landing-page',
      endEvent: '',
      isLoaded: false,
      isLoading: false,
      startFooter: '',
      endFooter: '',
      divisor: 0,
      eventOptions: [],
	  applicationOptions: [],
	  applicationFilteredEventTypes: [],
      startEventId: undefined,
      endEventId: undefined,
	  applicationId: undefined,
    };
  },
  setup() {},
  watch: {},
  async mounted() {
	  const eventTypes = this.eventTypes.length ? this.eventTypes : [];
      const eventOptions = eventTypes.map((x, i) => ({ id: i, name: x }));
      this.startEventId = eventOptions?.find(x => x.name === this.startEvent)?.id;
      this.eventOptions = eventOptions;
	  this.applicationOptions = this.applications.map((x, i) => ({ id: i, name: x }));
	  this.applicationFilteredEventTypes = this.filteredEventTypes.map((x, i) => ({ id: i, name: x }));
  },
};
</script>
