﻿<template>
  <div class="flex flex-col col-span-full sm:col-span-8 bg-white shadow-lg rounded-sm border border-slate-200">
    <ReportCardHeader title="New Users vs. Lost Users" />

    <!-- adjustment = inc or dec; summaryBool 1 or 0 -->
    <LineChart :data="chartData" totalVal="1.2K" percentVal="15" indicator="inc" summary="0" width="595" height="248" />

    <ReportCardFooter reportLinkLabel="New User Report" reportLink="#00"/>
  </div>  
</template>

<script>
  import { ref } from 'vue'
  import LineChart from '../../charts/LineChartOverTime.vue'
  import ReportCardHeader from '../../components/ReportCardHeader.vue'
  import ReportCardFooter from '../../components/ReportCardFooter.vue'

  // Import utilities
  import { tailwindConfig } from '../../utils/Utils'

  export default {
  name: 'NewCustomersOverTime',
  components: {
  <PERSON><PERSON><PERSON>,
  Report<PERSON><PERSON>Header,
  ReportCardFooter,
  },
  setup() {

  const chartData = ref({
  labels: [
  '12-01-2020', '01-01-2021', '02-01-2021',
  '03-01-2021', '04-01-2021', '05-01-2021',
  '06-01-2021', '07-01-2021', '08-01-2021',
  '09-01-2021', '10-01-2021', '11-01-2021',
  '12-01-2021', '01-01-2022', '02-01-2022',
  '03-01-2022', '04-01-2022', '05-01-2022',
  '06-01-2022', '07-01-2022', '08-01-2022',
  '09-01-2022', '10-01-2022', '11-01-2022',
  '12-01-2022', '01-01-2023',
  ],
  datasets: [
  // Indigo line
  {
  label: 'New Users',
  data: [
  1000, 1200, 900, 850, 842, 900, 800,
  802, 815, 700, 850, 865, 900, 850,
  900, 925, 935, 950, 910, 950, 980,
  1000, 1050, 1025, 1100, 1200,
  ],
  borderColor: tailwindConfig().theme.colors.emerald[500],
  fill: false,
  borderWidth: 2,
  tension: 0,
  pointRadius: 0,
  pointHoverRadius: 3,
  pointBackgroundColor: tailwindConfig().theme.colors.emerald[500],
  clip: 20,
  },
  //
  {
  label: 'Lost Users',
  data: [
  500, 800, 900, 915, 800, 750, 700,
  705, 685, 700, 675, 650, 675, 660,
  700, 685, 715, 720, 685, 700, 725,
  750, 725, 700, 715, 725,
  ],
  borderColor: tailwindConfig().theme.colors.rose[500],
  fill: false,
  borderWidth: 2,
  tension: 0,
  pointRadius: 0,
  pointHoverRadius: 3,
  pointBackgroundColor: tailwindConfig().theme.colors.rose[500],
  clip: 20,
  }
  ],
  })

  return {
  chartData,
  }
  }
  }
</script>