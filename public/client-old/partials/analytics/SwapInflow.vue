<template>
  <div class="flex flex-col col-span-full sm:col-span-6 xl:col-span-4 bg-white shadow-lg rounded-sm border border-slate-200">
    <ReportCardHeader title="Swap Inflow" />

    <RankedChart :data="items" primaryColumnTitle="Token" secondaryColumnTitle="Volume" categoryIndicator="0" color="green" />

    <ReportCardFooter reportLinkLabel="Swap Inflow Report" reportLink="#00"/>
  </div>
</template>

<script>
import { ref } from 'vue'
import RankedChart from '../../charts/RankedChart.vue'
import ReportCardHeader from '../../components/ReportCardHeader.vue'
import ReportCardFooter from '../../components/ReportCardFooter.vue'
  
export default {
  name: 'SwapInflow',
  components: {
    RankedChart,
    ReportCardHeader,
    ReportCardFooter
  },
  
  data() {
    return {
      items: [
      {
        name: 'ETH',
        amount: 4700,
        category: '',
      },
      {
        name: 'USDT',
        amount: 4200,
        category: '',
      },
      {
        name: 'LUNA',
        amount: 3400,
        category: '',
      },
      {
        name: 'USDC',
        amount: 3100,
        category: '',
      },
      {
        name: 'DAI',
        amount: 2200,
        category: '',
      },
      {
        name: 'MATIC',
        amount: 1700,
        category: '',
      },
      {
        name: 'BTC',
        amount: 924,
        category: '',
      },
      {
        name: 'SOL',
        amount: 696,
        category: '',
      },
      ]
    }
  }
}
</script>