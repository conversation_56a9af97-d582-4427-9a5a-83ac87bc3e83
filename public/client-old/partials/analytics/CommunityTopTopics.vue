<template>
  <div class="flex flex-col col-span-full sm:col-span-6 xl:col-span-4 bg-white shadow-lg rounded-sm border border-slate-200">
    <ReportCardHeader title="Most Popular Topics" />

    <RankedChart :data="items" primaryColumnTitle="Topic" secondaryColumnTitle="Conversation" categoryIndicator="0" color="green" />
  </div>
</template>

<script>
import { ref } from 'vue'
import RankedChart from '../../charts/RankedChart.vue'
import ReportCardHeader from '../../components/ReportCardHeader.vue'
import ReportCardFooter from '../../components/ReportCardFooter.vue'
  
export default {
  name: 'PopularTopics',
  components: {
    RankedChart,
    ReportCardHeader,
    ReportCardFooter
  },
  
  data() {
    return {
      items: [
      {
        name: 'Airdrop',
        amount: 4700,
        category: '',
      },
      {
        name: 'gm',
        amount: 4200,
        category: '',
      },
      {
        name: 'Lunch',
        amount: 3400,
        category: '',
      },
      {
        name: 'Support',
        amount: 3100,
        category: '',
      },
      {
        name: 'Scam',
        amount: 2200,
        category: '',
      },
      {
        name: 'Proposal',
        amount: 1700,
        category: '',
      },
      {
        name: 'New NFT',
        amount: 924,
        category: '',
      },
      {
        name: 'Tokens',
        amount: 696,
        category: '',
      },
      ]
    }
  }
}
</script>