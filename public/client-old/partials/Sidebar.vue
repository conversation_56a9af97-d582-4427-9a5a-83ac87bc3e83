<template>
	<div>
		<!-- Sidebar backdrop (mobile only) -->
		<!-- <div class="
			fixed
			inset-0
			bg-ralgranite-500 bg-opacity-30
			z-40
			lg:hidden lg:z-auto
			transition-opacity
			duration-200
		 " :class="sidebarOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'" aria-hidden="true"></div> -->

		<!-- Sidebar -->
		<!-- <div id="sidebar" ref="sidebar" class="
			flex flex-col
			absolute
			z-40
			left-0
			top-0
			lg:static lg:left-auto lg:top-auto lg:translate-x-0 md:static md:left-auto md:top-auto md:translate-x-0
			transform
			h-screen
			overflow-y-scroll
			lg:overflow-y-auto
			no-scrollbar
			w-80
			lg:w-24 lg:sidebar-expanded:!w-80 md:sidebar-expanded:!w-80
			2xl:!w-80
			shrink-0
			bg-gradient-to-t from-ralsecondary-start to-ralsecondary-end
			px-6 py-8
			transition-all
			duration-300
			ease-in-out
			xs:-translate-x-80 sm:-translate-x-80 md:translate-x-0 lg:translate-x-0 xl:translate-x-0 2xl:translate-x-0"


		@mouseover="sidebarHover()"
		@mouseleave="sidebarHoverLeave()"> -->

		<div id="sidebar" ref="sidebar" class="
			flex flex-col
			absolute
			z-40
			left-0
			top-0
			transform
			h-screen
			overflow-y-scroll
			xs:static xs:left-auto xs:top-auto
			no-scrollbar
			w-24
			hover:md:sidebar-expanded:w-80
			shrink-0
			bg-gradient-to-t from-ralsecondary-start to-ralsecondary-end
			px-6 py-8
			transition-all
			duration-300
			ease-in-out" :class="sidebarExpanded ? 'xl:w-80' : ''" @mouseover="sidebarHover()"
			@mouseleave="sidebarHoverLeave()">

			<!-- :class="sidebarOpen ? 'translate-x-0' : '-translate-x-80'" -->
			<!-- Sidebar header -->
			<div class="flex justify-center sm:px-2 mb-auto">
				<!-- Close button -->
				<!-- <button ref="trigger" class="lg:hidden text-slate-500 hover:text-slate-400" @click.stop="$emit('close-sidebar')"
			  aria-controls="sidebar" :aria-expanded="sidebarOpen">
			  <span class="sr-only">Close sidebar</span>
			  <svg class="w-6 h-6 fill-current" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
				 <path d="M10.7 18.7l1.4-1.4L7.8 13H20v-2H7.8l4.3-4.3-1.4-1.4L4 12z" />
			  </svg>
			</button> -->
				<!-- Logo -->
				<div class="flex flex-col justify-center">
					<router-link class="block" to="/home">
						<span v-if="sidebarExpanded" class="lg:hidden lg:sidebar-expanded:block 2xl:block">
							<a href="index.html" class="flex justify-center">
								<img src="../images/logo-horizontal.svg" />
							</a>
						</span>
						<!-- mobile adjustment-->
						<span v-if="!sidebarExpanded" class="
						text-center
						w-6
						" aria-hidden="true">
							<a class="block" href="index.html">
								<!-- need to fix this for center alignment -->
								<img src="../images/raleon_icon.png" style="width: 27px; height: 22px" />
							</a>
						</span>
					</router-link>


					<div
						v-if="userOrgs.length > 1 && sidebarExpanded"
						class="flex flex-row mt-4"
						style="
							cursor: pointer;
							justify-content: space-between;
							background: rgba(255,255,255,0.2);
							color: lightgrey;
							align-items: center;
							padding: 0.75em 1em;
							max-width: 18em;
						"
						:style="{ 'border-radius': orgDropdownOpen ? '0.5em 0.5em 0 0' : '0.5em'}"
						@click="orgDropdownOpen = !orgDropdownOpen"
					>
						<span
							style="
								overflow: hidden;
								text-overflow: ellipsis;
								white-space: nowrap;
							"
						>{{ userOrg.name }}</span>
							<svg
								width="25"
								height="25"
								viewBox="0 0 24 24"
								fill="none"
								xmlns="http://www.w3.org/2000/svg"
								:style="{ 'transform': orgDropdownOpen ? 'rotate(180deg)' : ''}"
							>
								<path d="M7 10L12 15L17 10H7Z" fill="white"/>
							</svg>
					</div>
					<ul
						v-if="orgDropdownOpen && sidebarExpanded"
						style="
							background-color: rgba(255,255,255,0.2);
							border-radius: 0 0 0.5em 0.5em;
							color: lightgrey;
							padding: 1em;
							padding-bottom: 0.5em;
							max-width: 18em;
						"
					>
						<li
							v-for="org in userOtherOrgs"
							@click="switchOrgs(org)"
							style="
								cursor: pointer;
								margin-bottom: 1em;
								overflow:hidden;
								text-overflow: ellipsis;
								white-space: nowrap;
							"
						>{{ org.name  }}</li>
					</ul>
				</div>
			</div>

			<!-- Links -->

			<div class="mt-auto mb-auto">
				<ul>
					<!-- Dashboard -->
					<SidebarLinkGroup v-slot="parentLink" :activeCondition="currentRoute.fullPath === '/' ||
						currentRoute.fullPath.includes('dashboard')
						">
						<a class="
						 block
						 text-slate-200
						 hover:text-white
						 truncate
						 transition
						 duration-150
						 opacity-70 hover:opacity-100
					  " :class="(currentRoute.fullPath === '/' ||
					  		currentRoute.fullPath.includes('dashboard')) &&
					  	'hover:text-slate-200'
					  	" href="#0" @click.prevent="
		sidebarExpanded
			? parentLink.handleClick()
			: (sidebarExpanded = true)
		">
							<div class="flex items-center justify-between">
								<div class="flex items-center">
									<img src="../images/sidebar-icons/home.svg" />
									<span v-if="sidebarExpanded" class="
								 text-sm
								 font-medium
								 ml-3
								 lg:opacity-0 lg:sidebar-expanded:opacity-100
								 2xl:opacity-100
								 duration-200
							  ">
										Dashboards
									</span>
								</div>
								<!-- Icon -->
								<div v-if="sidebarExpanded" class="flex shrink-0 ml-2">
									<svg class="w-3 h-3 shrink-0 ml-1 fill-current text-slate-400"
										:class="parentLink.expanded && 'transform rotate-180'" viewBox="0 0 12 12">
										<path d="M5.9 11.4L.5 6l1.4-1.4 4 4 4-4L11.3 6z" />
									</svg>
								</div>
							</div>
						</a>
						<div class="lg:hidden lg:sidebar-expanded:block 2xl:block">
							<ul class="pl-9 mt-1" :class="!parentLink.expanded && 'hidden'">
								<router-link to="/dashboard/" custom>
									<li class="mb-4 mt-1 last:mb-0">
										<button class="
									btn-xs
									text-ralcloud-500
									hover:bg-ralpurple-700 hover:text-white
								 " @click.stop="addDashboard()">
											<svg class="
									  w-4
									  h-4
									  fill-current
									  opacity-50
									  shrink-0
									  hover:text-ralcloud-500
									" viewBox="0 0 16 16">
												<path
													d="M15 7H9V1c0-.6-.4-1-1-1S7 .4 7 1v6H1c-.6 0-1 .4-1 1s.4 1 1 1h6v6c0 .6.4 1 1 1s1-.4 1-1V9h6c.6 0 1-.4 1-1s-.4-1-1-1z" />
											</svg>
											<span class="ml-2">New Dashboard</span>
										</button>
									</li>
								</router-link>
								<li class="text-ralcloud-500 text-sm">Shared</li>
								<router-link v-for="dash in sharedDashboardList" :key="dash.id" :to="{
									name: 'UniversalDashboard',
									params: {dashboardId: dash.id},
								}" custom v-slot="{href, navigate, isExactActive}">
									<li class="mb-1 last:mb-0" :class="isExactActive && 'border-r-3 border-indigo-500'">
										<a class="
									block
									text-slate-400
									hover:text-slate-200
									transition
									duration-150
									truncate
								 " :class="isExactActive && '!text-indigo-500'" :href="href" @click="navigate">
											<span class="
									  text-sm
									  font-medium
									  lg:opacity-0 lg:sidebar-expanded:opacity-100
									  2xl:opacity-100
									  duration-200
									">{{ dash.name }}</span>
										</a>
									</li>
								</router-link>
								<li class="text-ralcloud-500 mt-4 text-sm" v-if="personalDashCount > 0">Private</li>
								<router-link v-for="dash in personalDashboardList" :key="dash.id" :to="{
									name: 'UniversalDashboard',
									params: {dashboardId: dash.id},
								}" custom v-slot="{href, navigate, isExactActive}">
									<li class="mb-1 last:mb-0" :class="isExactActive && 'border-r-3 border-indigo-500'">
										<a class="
									block
									text-slate-400
									hover:text-slate-200
									transition
									duration-150
									truncate
								 " :class="isExactActive && '!text-indigo-500'" :href="href" @click="navigate">
											<span class="
									  text-sm
									  font-medium
									  lg:opacity-0 lg:sidebar-expanded:opacity-100
									  2xl:opacity-100
									  duration-200
									">{{ dash.name }}</span>
										</a>
									</li>
								</router-link>
							</ul>
						</div>
					</SidebarLinkGroup>
					<!-- Personalization Analytics -->

					<router-link to="/overview-segment" custom="" v-slot="{href, navigate, isExactActive}">
						<li class="px-3 py-2 rounded-sm mt-4 mb-0.5 last:mb-0"
							:class="isExactActive && 'bg-ralbutton-primary-dark-selected rounded-md'">
							<a class="
							block
							text-slate-200
							hover:text-white
							truncate
							transition
							duration-150
					 opacity-70 hover:opacity-100
						 " :class="isExactActive && 'hover:text-slate-200 opacity-100'" :href="href" @click="navigate">
								<div class="flex items-center">
									<img src="../images/sidebar-icons/users.svg">

									<span v-if="sidebarExpanded" class="
								 text-sm
								 font-medium
								 ml-3
								 lg:opacity-0 lg:sidebar-expanded:opacity-100
								 2xl:opacity-100
								 duration-200
							  ">Audiences</span>
								</div>
							</a>
						</li>
					</router-link>
					<SidebarLinkGroup v-slot="parentLink" :activeCondition="currentRoute.fullPath === '/' ||
						currentRoute.fullPath.includes('campaign') ||
						currentRoute.fullPath.includes('custom-events')
						">
						<a class="
						 block
						 text-slate-200
						 hover:text-white
						 truncate
						 transition
						 duration-150
					opacity-70 hover:opacity-100
					  " :class="(currentRoute.fullPath === '/' ||
					  		currentRoute.fullPath.includes('campaigns')) ||
					  	currentRoute.fullPath.includes('custom-events') &&
					  	'hover:text-slate-200'
					  	" href="#0" @click.prevent="
		sidebarExpanded
			? parentLink.handleClick()
			: (sidebarExpanded = true)
		">
							<div class="flex items-center justify-between mt-4">
								<div class="flex items-center">
									<img src="../images/sidebar-icons/web.svg">
									<span v-if="sidebarExpanded" class="
								 text-sm
								 font-medium
								 ml-3
								 lg:opacity-0 lg:sidebar-expanded:opacity-100
								 2xl:opacity-100
								 duration-200
							  ">
										Web2 Activity
									</span>
								</div>
								<!-- Icon -->
								<div v-if="sidebarExpanded" class="flex shrink-0 ml-2">
									<svg class="w-3 h-3 shrink-0 ml-1 fill-current text-slate-400"
										:class="parentLink.expanded && 'transform rotate-180'" viewBox="0 0 12 12">
										<path d="M5.9 11.4L.5 6l1.4-1.4 4 4 4-4L11.3 6z" />
									</svg>
								</div>
							</div>
						</a>
						<div class="lg:hidden lg:sidebar-expanded:block 2xl:block">
							<ul class="pl-9 mt-1" :class="!parentLink.expanded && 'hidden'">
								<router-link to="/campaigns/Overview" custom v-slot="{href, navigate, isExactActive}">
									<li class="mb-1 last:mb-0" :class="isExactActive && 'border-r-3 border-indigo-500'">
										<a class="block text-slate-400 hover:text-slate-200 transition duration-150 truncate"
											:class="isExactActive && '!text-indigo-500'" :href="href" @click="navigate">
											<span
												class="text-sm font-medium lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200">Attribution</span>
										</a>
									</li>
								</router-link>
								<router-link to="/custom-events" custom v-slot="{href, navigate, isExactActive}">
									<li class="mb-1 last:mb-0" :class="isExactActive && 'border-r-3 border-indigo-500'">
										<a class="block text-slate-400 hover:text-slate-200 transition duration-150 truncate"
											:class="isExactActive && '!text-indigo-500'" :href="href" @click="navigate">
											<span
												class="text-sm font-medium lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200">Custom
												Events</span>
										</a>
									</li>
								</router-link>
							</ul>
						</div>
					</SidebarLinkGroup>

					<!-- Campaigns -->
					<router-link to="/campaign-overview" custom="" v-slot="{href, navigate, isExactActive}">
						<li class="px-3 py-2 rounded-sm mt-4 mb-0.5 last:mb-0"
							:class="isExactActive && 'bg-ralbutton-primary-dark-selected rounded-md'">
							<a class="
							block
							text-slate-200
							hover:text-white
							truncate
							transition
							duration-150
					 opacity-70 hover:opacity-100
						 " :class="isExactActive && 'hover:text-slate-200 opacity-100'" :href="href" @click="navigate">
								<div class="flex items-center">
									<img src="../images/sidebar-icons/zap.svg">

									<span v-if="sidebarExpanded" class="
								 text-sm
								 font-medium
								 ml-3
								 lg:opacity-0 lg:sidebar-expanded:opacity-100
								 2xl:opacity-100
								 duration-200
							  ">Action Prompts</span>
								</div>
							</a>
						</li>
					</router-link>

					<!-- Campaigns -->
					<router-link to="/Quests" custom="" v-slot="{href, navigate, isExactActive}">
						<li class="px-3 py-2 rounded-sm mt-4 mb-0.5 last:mb-0"
							:class="isExactActive && 'bg-ralbutton-primary-dark-selected rounded-md'">
							<a class="
							block
							text-slate-200
							hover:text-white
							truncate
							transition
							duration-150
					 opacity-70 hover:opacity-100
						 " :class="isExactActive && 'hover:text-slate-200 opacity-100'" :href="href" @click="navigate">
								<div class="flex items-center">
									<img src="../images/sidebar-icons/trophy.svg">

									<span v-if="sidebarExpanded" class="
								 text-sm
								 font-medium
								 ml-3
								 lg:opacity-0 lg:sidebar-expanded:opacity-100
								 2xl:opacity-100
								 duration-200
							  ">Quests</span>
								</div>
							</a>
						</li>
					</router-link>

					<!-- Projects -->
					<router-link v-if="shouldShowProjects()" to="/connections/" custom
						v-slot="{href, navigate, isExactActive}">
						<li class="px-3 py-2 mt-4 rounded-sm mb-0.5 last:mb-0"
							:class="isExactActive && 'bg-ralbutton-primary-dark-selected rounded-md'">
							<a class="
							block
							text-slate-200
							hover:text-white
							truncate
							transition
							duration-150
					 opacity-70 hover:opacity-100
						 " :class="isExactActive && 'opacity-100'" :href="href" @click="navigate">
								<div class="flex items-center">
									<img src="../images/sidebar-icons/layers.svg" />
									<span v-if="sidebarExpanded" class="
								 text-sm
								 font-medium
								 ml-3
								 lg:opacity-0 lg:sidebar-expanded:opacity-100
								 2xl:opacity-100
								 duration-200
							  ">Projects</span>
								</div>
							</a>
						</li>
					</router-link>

					<!-- Loyalty Programs -->
					<router-link v-if="isLoyaltyProgramsEnabled" to="/loyalty/programs" custom
						v-slot="{href, navigate, isExactActive}">
						<li class="px-3 py-2 mt-4 rounded-sm mb-0.5 last:mb-0"
							:class="isExactActive && 'bg-ralbutton-primary-dark-selected rounded-md'">
							<a class="
							block
							text-slate-200
							hover:text-white
							truncate
							transition
							duration-150
					 opacity-70 hover:opacity-100
						 " :class="isExactActive && 'opacity-100'" :href="href" @click="navigate">
								<div class="flex items-center">
									<img src="../images/sidebar-icons/layers.svg" />
									<span v-if="sidebarExpanded" class="
								 text-sm
								 font-medium
								 ml-3
								 lg:opacity-0 lg:sidebar-expanded:opacity-100
								 2xl:opacity-100
								 duration-200
							  ">Loyalty Programs</span>
								</div>
							</a>
						</li>
					</router-link>
				</ul>
			</div>

			<!-- Support, Settings, and User -->
			<div class="mt-auto">
				<ul>
					<li class="px-3 py-2 rounded-sm mt-4 mb-0.5 last:mb-0"
						:class="isExactActive && 'bg-ralbutton-primary-dark-selected rounded-md'">
						<a class="
					block
					text-slate-200
					hover:text-white
					truncate
					transition
					duration-150
				opacity-70 hover:opacity-100
					" :class="isExactActive && 'hover:text-slate-200 opacity-100'" :href="dev_doc_url" @click="navigate">
							<div class="flex items-center">
								<img src="../images/sidebar-icons/support.svg">

								<span v-if="sidebarExpanded" class="
							text-sm
							font-medium
							ml-3
							lg:opacity-0 lg:sidebar-expanded:opacity-100
							2xl:opacity-100
							duration-200
						">Support</span>
							</div>
						</a>
					</li>
				</ul>
				<ul v-if="isAdmin">
					<router-link to="/settings/manage-users" custom="" v-slot="{href, navigate, isExactActive}">
						<li class="px-3 py-2 rounded-sm mt-4 mb-0.5 last:mb-0"
							:class="isExactActive && 'bg-ralbutton-primary-dark-selected rounded-md'">
							<a class="
							block
							text-slate-200
							hover:text-white
							truncate
							transition
							duration-150
					 opacity-70 hover:opacity-100
						 " :class="isExactActive && 'hover:text-slate-200 opacity-100'" :href="href" @click="navigate">
								<div class="flex items-center">
									<img src="../images/sidebar-icons/settings.svg">

									<span v-if="sidebarExpanded" class="
								 text-sm
								 font-medium
								 ml-3
								 lg:opacity-0 lg:sidebar-expanded:opacity-100
								 2xl:opacity-100
								 duration-200
							  ">Settings</span>
								</div>
							</a>
						</li>
					</router-link>
				</ul>

				<ul>
					<router-link to="/settings/myprofile" custom="" v-slot="{href, navigate, isExactActive}">
						<li class="px-1.5 py-2 rounded-sm mt-4 mb-0.5 last:mb-0"
							:class="isExactActive && 'bg-ralbutton-primary-dark-selected rounded-md'">
							<a class="
							block
							text-slate-200
							hover:text-white
							truncate
							transition
							duration-150
						 " :class="isExactActive && 'hover:text-slate-200 opacity-100'" :href="href" @click="navigate">
								<div class="flex items-center">
									<div class="text-xs font-semibold text-white p-2 rounded-full bg-ralprimary-main"
										v-bind:class="avatarColors" alt="User">
										{{ avatarName }}
									</div>

									<span v-if="sidebarExpanded" class="
								 text-sm
								 font-medium
								 ml-3
								 lg:opacity-0 lg:sidebar-expanded:opacity-100
								 2xl:opacity-100
								 duration-200
							  ">{{ userName }}</span>
								</div>
							</a>
						</li>
					</router-link>
				</ul>

			</div>

			<!-- Expand / collapse button -->
			<!-- <div class="pt-3 hidden lg:inline-flex 2xl:hidden justify-end mt-auto">
			<div class="px-3 py-2">
			  <button @click.prevent="sidebarExpanded = !sidebarExpanded">
				 <span class="sr-only">Expand / collapse sidebar</span>
				 <svg class="w-6 h-6 fill-current sidebar-expanded:rotate-180" viewBox="0 0 24 24">
					<path class="text-slate-400" d="M19.586 11l-5-5L16 4.586 23.414 12 16 19.414 14.586 18l5-5H7v-2z" />
					<path class="text-slate-600" d="M3 23H1V1h2z" />
				 </svg>
			  </button>
			</div>
		 </div> -->
		</div>
	</div>
</template>

<script>
import { ref } from 'vue';
import { useRouter } from 'vue-router';

import DropdownClassicDark from '../components/DropdownClassicDark.vue';
import { addDashboardForOrg, getDashboardsByOrgId, getPersonalDashboardsByOrgId } from '../services/dashboard';
import SidebarLinkGroup from './SidebarLinkGroup.vue';
import * as Utils from '../utils/Utils';
import * as userService from '../services/user.js'
import { stackOffsetWiggle } from 'd3';
const URL_DOMAIN = Utils.URL_DOMAIN;
export default {
	name: 'Sidebar',
	props: ['sidebarOpen'],
	components: {
		SidebarLinkGroup,
		DropdownClassicDark,
	},
	setup(props, { emit }) {
		const trigger = ref(null);
		const sidebar = ref(null);

		const userInfo = JSON.parse(localStorage.getItem('userInfo'));

		// const storedSidebarExpanded = localStorage.getItem('sidebar-expanded');
		// const sidebarExpanded = ref(
		//   storedSidebarExpanded === null ? false : storedSidebarExpanded === 'true',
		// );

		const currentRoute = useRouter().currentRoute.value;



		//TODO remove intercom instrumentation

		return {
			trigger,
			sidebar,
			currentRoute,
			userInfo,
		};
	},
	data() {
		const devEnabled = localStorage.getItem('devEnabled') === 'true';
		const betaEnabled = localStorage.getItem('betaEnabled') === 'true';

		return {
			sharedDashboardList: [],
			personalDashboardList: [],
			sidebarExpanded: false,
			orgDropdownOpen: false,
			devEnabled,
			betaEnabled,
			userOrgs: [],
			dev_doc_url: 'https://docs.raleon.io',
			isLoyaltyProgramsEnabled: window.statsig?.checkGate('loyalty_program') || false
		};
	},
	watch: {
		sidebarExpanded: function (newVal, oldVal) {
			localStorage.setItem('sidebar-expanded', newVal);
			if (newVal) {
				document.querySelector('body').classList.add('sidebar-expanded');
			} else {
				document.querySelector('body').classList.remove('sidebar-expanded');
			}
		}
	},
	computed: {

		userOrgId() {
			return localStorage.getItem("userOrgId");
		},
		userOrg() {
			return this.userOrgs?.find(x => x.id == this.userOrgId);
		},
		userOtherOrgs() {
			return this.userOrgs?.filter(x => x.id != this.userOrgId);
		},
		personalDashCount() {
			return this.personalDashboardList.length;
		},
		isAdmin() {
			//TODO: update this to check on the server side
			let userInfo = JSON.parse(localStorage.getItem('userInfo'));
			return userInfo && userInfo.roles.includes('admin') || userInfo.roles.includes('customer-admin');
		},
		avatarName() {
			var fn = localStorage.getItem('firstName');
			var ln = localStorage.getItem('lastName');
			if (ln == '') return fn[0]?.toUpperCase();
			else return fn[0]?.toUpperCase() + ln[0]?.toUpperCase();
		},
		userName() {
			console.log('Name: ' + localStorage.getItem('firstName'));
			return (
				localStorage.getItem('firstName') +
				' ' +
				localStorage.getItem('lastName')
			);
		},
		avatarColors() {
			if (localStorage.getItem('avatarColors') != null) {
				var colors = JSON.parse(localStorage.getItem('avatarColors'));
				return colors.from + ' ' + colors.to;
			} else return '';
		},
	},
	methods: {
		async switchOrgs(org) {
			const loginRequest = await fetch(`${URL_DOMAIN}/users/login/${org.id}`, {
				method: 'POST',
				credentials: 'omit', // include, *same-origin, omit
				headers: {
					Authorization: `Bearer ${localStorage.getItem('token')}`,
					'Content-Type': 'application/json',
				}
			});
			const loginResult = await loginRequest.json();
			console.log(loginResult);
			if (loginResult.token) {
				localStorage.setItem('token', loginResult.token);
				let userInfo = await userService.getUserInfo();
				await userService.setUserInfoSignin(userInfo);
				if(this.redirect == 'docs') {
					this.docLogin();
				}
				else {
					this.$router.push('/chats');
				}
			} else {
				this.loginError = true;
			}
			location.reload();
		},
		async addDashboard() {
			var orgId = parseInt(localStorage.getItem('userOrgId'));

			const result = await addDashboardForOrg(orgId, 'Untitled', '', this.userInfo.id, 0);
			console.log('New dashboard result ' + JSON.stringify(result));
			if (result != null) {
				this.getDashboardList();
				this.$router.push({
					name: 'UniversalDashboard',
					params: { dashboardId: result.id },
				});
			}
		},
		shouldShowProjects() {
			const userInfo = JSON.parse(localStorage.getItem('userInfo'));
			if (userInfo?.roles?.includes('raleon-support')) {
				return true;
			}

			return false;
		},
		clickHandler({ target }) {
			// if (!sidebar.value || !trigger.value) return;
			// if (
			//   (this.props && !this.props.sidebarOpen) ||
			//   sidebar.value.contains(target) ||
			//   trigger.value.contains(target)
			// )
			//   return;
			// emit('close-sidebar');
		},
		keyHandler({ keyCode }) {
			if ((this.props && !this.props.sidebarOpen) || keyCode !== 27) return;
			emit('close-sidebar');
		},
		resizeHandler() {
			this.sidebarExpanded = window.innerWidth >= 1280;
		},
		sidebarHover() {
			//dont show sidebar details if breakpoint is small
			if (!this.sidebarExpanded && window.innerWidth >= 768) {
				this.sidebarExpanded = true;
			}
		},
		sidebarHoverLeave() {
			if (window.innerWidth < 1280) {
				this.sidebarExpanded = false;
			}
		},
		async getDashboardList() {
			//Shared
			this.sharedDashboardList = [];
			const dashboardResult = await getDashboardsByOrgId(
				`${localStorage.getItem('userOrgId')}`,
			);
			/*console.log(
				'Data retrieved from shared dashboards: ' + JSON.stringify(dashboardResult),
			);*/

			if (dashboardResult.length > 0) {
				for (var i = 0; i < dashboardResult.length; i++) {
					this.sharedDashboardList.push({
						name: dashboardResult[i].name,
						id: dashboardResult[i].id,
					});
				}
			}

			//Personal
			this.personalDashboardList = [];
			const personalDashboardResult = await getPersonalDashboardsByOrgId(
				`${localStorage.getItem('userOrgId')}`, this.userInfo.id
			);
			/*console.log(
				'Data retrieved from personal dashboards: ' + JSON.stringify(personalDashboardResult),
			);*/

			if (personalDashboardResult.length > 0) {
				for (var i = 0; i < personalDashboardResult.length; i++) {
					this.personalDashboardList.push({
						name: personalDashboardResult[i].name,
						id: personalDashboardResult[i].id,
					});
				}
			}
		},
		updateStatsigUsage() {
			this.isLoyaltyProgramsEnabled = window.statsig?.checkGate('loyalty_program') || false;
		}
	},
	async mounted() {
		this.getDashboardList();
		document.addEventListener('statsigInitialized', this.updateStatsigUsage);

		let response = {};
		let jsonresponse = {};
		try {
			console.log("Trying request to /users/doc-login", `${URL_DOMAIN}/users/doc-login`)
			response = await fetch(`${URL_DOMAIN}/users/doc-login`, {
				method: 'GET',
				withCreditentials: true,
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Access-Control-Allow-Origin': '*',
					'Content-Type': 'application/json'
				}
			});
			console.log("Response from /users/doc-login", response)
			jsonresponse = await response.json();
			console.log("DOCS DOCS DOCS", jsonresponse)
			if (jsonresponse.docs_url)
				this.dev_doc_url = `${jsonresponse.docs_url}`;
			console.log("DOCS DOCS DOCS", this.dev_doc_url);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}

		const userOrgsRequest = await fetch(`${URL_DOMAIN}/user/orgs`, {
			method: 'GET',
			credentials: 'omit', // include, *same-origin, omit
			headers: {
				Authorization: `Bearer ${localStorage.getItem('token')}`,
				'Content-Type': 'application/json',
			}
		});
		this.userOrgs = await userOrgsRequest.json();

		document.addEventListener('click', this.clickHandler);
		document.addEventListener('keydown', this.keyHandler);
		this.resizeHandler();

		window.addEventListener('resize', this.resizeHandler);
	},
	beforeUnmount() {
		document.removeEventListener('statsigInitialized', this.updateStatsigUsage);
	},
	unmounted() {
		document.removeEventListener('click', this.clickHandler);
		document.removeEventListener('keydown', this.keyHandler);
		document.removeEventListener('resize', this.resizeHandler);
	},
};
</script>
