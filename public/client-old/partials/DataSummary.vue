<template>
    <div class="flex items-center">

        <div class="text-xs mr-1.5 font-medium bg-emerald-100 text-emerald-600 rounded-full text-center px-2.5 py-1">
        {{wallets}} Addresses
        </div>

        <div class="text-xs mr-1.5 font-medium bg-sky-100 text-sky-500 rounded-full text-center px-2.5 py-1">
          <span v-if="smartContracts > 1">{{smartContracts}} Smart Contracts</span>
          <span v-else-if="smartContracts == 1">{{smartContracts}} Smart Contract</span>
        </div>

        <div class="text-xs mr-1.5 font-medium bg-indigo-100 text-indigo-600 rounded-full text-center px-2.5 py-1">
        {{networks}} Network
        </div>

        <div class="text-xs mr-1.5 font-medium bg-blue-100 text-blue-600 rounded-full text-center px-2.5 py-1">
        {{tokens}} Token
        </div>
    </div>
</template>

<script>
export default {
  name: 'DataSummary',
  props: ['wallets', 'smartContracts', 'tokens', 'networks']
}
</script>
