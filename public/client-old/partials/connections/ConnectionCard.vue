<template>
  <div class="col-span-full sm:col-span-6 xl:col-span-4 bg-white shadow-lg rounded-sm border border-slate-200">
    <div class="flex flex-col h-full p-5">
      <header>
        <div class="flex items-center justify-between">
          <div class="flex shrink-0 -space-x-3 -ml-px">
                <router-link class="inline-flex text-slate-800 hover:text-slate-900 mb-1" to="#">
                  <h2 class="text-xl leading-snug font-semibold">{{item.name}}</h2>
                </router-link>
          </div>
          <div>
              <button class="btn border-slate-200 hover:border-slate-300 text-slate-600">
                  <svg class="w-4 h-4 fill-current text-slate-500 shrink-0" viewBox="0 0 16 16">
                      <path d="M11.7.3c-.4-.4-1-.4-1.4 0l-10 10c-.2.2-.3.4-.3.7v4c0 .6.4 1 1 1h4c.3 0 .5-.1.7-.3l10-10c.4-.4.4-1 0-1.4l-4-4zM4.6 14H2v-2.6l6-6L10.6 8l-6 6zM12 6.6L9.4 4 11 2.4 13.6 5 12 6.6z" />
                  </svg>
              </button>
          </div>
        </div>
      </header>
      <div class="grow mt-2">
            <div class="text-sm">{{item.description}}</div>
      </div>
      <footer class="mt-5">
        <div class="flex justify-between items-center">
          <div>
            <!-- <div class="text-xs inline-flex font-medium rounded-full text-center px-2.5 py-1" :class="typeColor(item.type)">{{item.type}}</div> -->
            <div class="text-xs inline-flex font-medium text-slate-500 mb-2">Activated on {{item.start_date}}</div>
          </div>
                <div>
                    <button class="btn-sm border-slate-200 hover:border-slate-300 shadow-sm flex items-center">
                      <div class="flex jutify-between items-center" v-html="typeContent(item.type)" />
                      X
                    </button>
                </div>
        </div>
      </footer>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ConnectionCard',
  props: ['item'],
  setup() {

    const typeContent = (type) => {
      switch (type) {
        case 'Connected':
          return `<svg class="w-3 h-3 shrink-0 fill-current text-emerald-500 mr-2" viewBox="0 0 12 12">
                    <path d="M10.28 1.28L3.989 7.575 1.695 5.28A1 1 0 00.28 6.695l3 3a1 1 0 001.414 0l7-7A1 1 0 0010.28 1.28z" />
                  </svg>
                  <span>Connected</span>`;
        case 'Syncing':
          return `<svg class="w-3 h-3 shrink-0 fill-current text-amber-500 mr-1" viewBox="0 0 12 12">
                    <path d="M11.953 4.29a.5.5 0 00-.454-.292H6.14L6.984.62A.5.5 0 006.12.173l-6 7a.5.5 0 00.379.825h5.359l-.844 3.38a.5.5 0 00.864.445l6-7a.5.5 0 00.075-.534z" />
                  </svg>
                  <span>Syncing</span>`;
        case 'Error':
          return `<svg class="w-3 h-3 shrink-0 fill-current text-red-500 mr-1" viewBox="0 0 12 12">
                      <path d="M8 0C3.6 0 0 3.6 0 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8zm3.5 10.1l-1.4 1.4L8 9.4l-2.1 2.1-1.4-1.4L6.6 8 4.5 5.9l1.4-1.4L8 6.6l2.1-2.1 1.4 1.4L9.4 8l2.1 2.1z" />
                  </svg>
                  <span>Error</span>`;
        default:
          return 'What?';
      }
    }

    const smartContractContent = (connectionType) => {
      switch (connectionType) {
        case 'smart_contract':
        return (
          `<div class="text-sm">Tracking 
          <div class="text-xs inline-flex font-medium bg-emerald-100 text-emerald-600 rounded-full text-center px-2.5 py-1">ENS Registry</div> 
          on <div class="text-xs inline-flex font-medium bg-indigo-100 text-indigo-600 rounded-full text-center px-2.5 py-1">Ethereum</div> 
          at address of <div class="text-xs inline-flex font-medium bg-amber-100 text-amber-600 rounded-full text-center px-2.5 py-1">0x7Be...D12b</div>.</div>`
          );

        case 'token':
        return (
          `<div class="text-sm">Tracking <div class="text-xs inline-flex font-medium bg-blue-100 text-blue-600 rounded-full text-center px-2.5 py-1">$ACME</div> on <div class="text-xs inline-flex font-medium bg-indigo-100 text-indigo-600 rounded-full text-center px-2.5 py-1">Ethereum</div>.
            </div>`
          );
        default:
          return (`<div></div>`);
      }
    }

    const categoryIcon = (category) => {
      switch (category) {
        case '1':
          return (
            `<div class="w-10 h-10 rounded-full flex items-center justify-center shrink-0 bg-rose-500">
              <svg class="w-9 h-9 fill-current text-rose-50" viewBox="0 0 36 36">
                <path d="M25 24H11a1 1 0 01-1-1v-5h2v4h12v-4h2v5a1 1 0 01-1 1zM14 13h8v2h-8z" />
              </svg>
            </div>`
          );
        case '2':
          return (
            `<div class="w-10 h-10 rounded-full flex items-center justify-center shrink-0 bg-emerald-500">
              <svg class="w-9 h-9 fill-current text-emerald-50" viewBox="0 0 36 36">
                <path d="M15 13v-3l-5 4 5 4v-3h8a1 1 0 000-2h-8zM21 21h-8a1 1 0 000 2h8v3l5-4-5-4v3z" />
              </svg>
            </div>`
          );
        case '3':
          return (
            `<div class="w-10 h-10 rounded-full flex items-center justify-center shrink-0 bg-sky-500">
              <svg class="w-9 h-9 fill-current text-sky-50" viewBox="0 0 36 36">
                <path d="M23 11v2.085c-2.841.401-4.41 2.462-5.8 4.315-1.449 1.932-2.7 3.6-5.2 3.6h-1v2h1c3.5 0 5.253-2.338 6.8-4.4 1.449-1.932 2.7-3.6 5.2-3.6h3l-4-4zM15.406 16.455c.066-.087.125-.162.194-.254.314-.419.656-.872 1.033-1.33C15.475 13.802 14.038 13 12 13h-1v2h1c1.471 0 2.505.586 3.406 1.455zM24 21c-1.471 0-2.505-.586-3.406-1.455-.066.087-.125.162-.194.254-.316.422-.656.873-1.028 1.328.959.878 2.108 1.573 3.628 1.788V25l4-4h-3z" />
              </svg>
            </div>`
          );
        case '4':
          return (
            `<div class="w-10 h-10 rounded-full flex items-center justify-center shrink-0 bg-indigo-500">
              <svg class="w-9 h-9 fill-current text-indigo-50" viewBox="0 0 36 36">
                <path d="M18 10c-4.4 0-8 3.1-8 7s3.6 7 8 7h.6l5.4 2v-4.4c1.2-1.2 2-2.8 2-4.6 0-3.9-3.6-7-8-7zm4 10.8v2.3L18.9 22H18c-3.3 0-6-2.2-6-5s2.7-5 6-5 6 2.2 6 5c0 2.2-2 3.8-2 3.8z" />
              </svg>
            </div>`
          );
        default:
          return (`<div></div>`);
      }
    }    
    
    return {
      typeContent,
      categoryIcon,
      smartContractContent,
    }
  }
}
</script>