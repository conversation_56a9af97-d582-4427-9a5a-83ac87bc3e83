<template>

<div class="relative mb-8">
  <div class="absolute bottom-0 w-full h-px bg-slate-200" aria-hidden="true"></div>
  <ul class="relative text-sm font-medium flex flex-nowrap -mx-4 sm:-mx-6 lg:-mx-8 overflow-x-scroll no-scrollbar">

  <li
    class="mr-6 last:mr-0 first:pl-4 sm:first:pl-6 lg:first:pl-8 last:pr-4 sm:last:pr-6 lg:last:pr-8"
    v-for="tab in tabs"
    :key="tab"
    v-on:click="switchTab(tab);"
  >
  <span    
    v-bind:class="{
      'block pb-3 text-indigo-500 whitespace-nowrap border-b-2 border-indigo-500': activeTab === tab
    }"
    class="flex block pb-3 text-slate-500 hover:text-slate-600 whitespace-nowrap hover:border-b-2 hover:border-indigo-500 cursor-pointer"><slot :name="header">{{ tab }} </slot></span>
  </li>
  
  </ul>
</div>

    <div class="card-body">
      <div class="tab-panel"><slot :name="tabPanelSlotName"> </slot></div>
    </div>
</template>


 

<script>

export default {
  props: {
    initialTab: String,
    tabs: Array,
    orgId: Number
  },
  emits: ['openAddProjectModal'],
  data() {
    return {
      activeTab: this.initialTab
    };
  },
  computed: {
    tabPanelSlotName() {
      return `tab-panel-${this.activeTab}`;
    }
  },
  methods: {
    tabHeadSlotName(tabName) {
      return `tab-head-${tabName}`;
    },

    switchTab(tabName) {
      this.activeTab = tabName;
    }
  }
};
</script>