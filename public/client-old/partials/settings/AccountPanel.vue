<template>
  <div class="grow">
    <!-- Panel body -->
    <div class="p-6 space-y-6">
      <h2 class="text-2xl text-slate-800 font-bold mb-5">Account Details</h2>
      <!--
      <section>
        <h3 class="text-xl leading-snug text-slate-800 font-bold mb-1">Organization Image</h3>
        <div class="flex items-center">
          <div class="mr-4">
            <img class="w-20 h-20 rounded-full" src="../../images/user-avatar-80.png" width="80" height="80" alt="User upload" />
          </div>
          <button class="btn-sm bg-indigo-500 hover:bg-indigo-600 text-white">Change</button>
        </div>
      </section>
      -->
      <section>
        <div class="sm:flex sm:items-center space-y-4 sm:space-y-0 sm:space-x-4 mt-5">
          <div class="sm:w-1/3">
            <label class="block text-sm font-medium mb-1" for="name" >Account Name</label>
            <input id="name" class="form-input w-full" type="text" v-model="orgName" />
          </div>
        </div>
      </section>

      <section>
        <div class="sm:flex sm:items-center space-y-4 sm:space-y-0 sm:space-x-4 mt-5">
          <div class="sm:w-1/3">
            <label class="block text-sm font-medium mb-1" for="name" >Account ID</label>
			<div class="text-sm">Your AccountID is used in the Raleon Snippet and Raleon APIs.</div>
            <div class="text-sm font-heavy">ID: {orgId}</div>
          </div>
        </div>
      </section>
    </div>
    <!-- Panel footer -->
    <footer>
      <div class="flex flex-col px-6 py-5 border-t border-slate-200">
        <div class="flex self-end">
          <button class="btn border-slate-200 hover:border-slate-300 text-slate-600">Cancel</button>
          <button class="btn bg-indigo-500 hover:bg-indigo-600 text-white ml-3" @click="updateOrg()">Save Changes</button>
        </div>
      </div>
    </footer>
  </div>
</template>

<script>
import { ref } from 'vue'
import { getOrgById, updateOrgById } from "../../services/organization"

export default {
  name: 'AccountPanel',
  setup() {

  const orgId = 0
  const sync = ref('Off')
  const orgName = ''

    return {
      sync,
      orgName,
	  orgId
    }
  },
  methods: {
    async updateOrg() {
      console.log("ORGIDU: " + this.orgId)
      console.log("ORG NAME U " + this.orgName)
      const r = await updateOrgById(1, this.orgName)
      console.log("Update from org API " + JSON.stringify(r))
    }
  },
  async mounted() {
    this.orgId = localStorage.getItem('userOrgId')
    const result = await getOrgById(this.orgId)
    console.log("Data retrieved from organization API " + JSON.stringify(result))
    this.orgName = result.name
    this.$forceUpdate()
  }
}
</script>
