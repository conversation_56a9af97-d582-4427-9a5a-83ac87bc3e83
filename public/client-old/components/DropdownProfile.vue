<template>
  <div class="relative inline-flex">
    <button
      ref="trigger"
      class="inline-flex justify-center items-center group"
      aria-haspopup="true"
      @click.prevent="dropdownOpen = !dropdownOpen"
      :aria-expanded="dropdownOpen">
      <div
        class="
          p-3
          text-xs
          font-semibold
          text-white
          p-2
          rounded-full
          bg-gradient-to-br
        "
        v-bind:class="avatarColors"
        alt="User">
        {{ avatarName }}
      </div>
      <div class="flex items-center truncate">
        <span
          class="truncate ml-2 text-sm font-medium group-hover:text-slate-800"
          >{{ userName }}</span>
        <svg
          class="w-3 h-3 shrink-0 ml-1 fill-current text-slate-400"
          viewBox="0 0 12 12">
          <path d="M5.9 11.4L.5 6l1.4-1.4 4 4 4-4L11.3 6z" />
        </svg>
      </div>
    </button>
    <transition
      enter-active-class="transition ease-out duration-200 transform"
      enter-from-class="opacity-0 -translate-y-2"
      enter-to-class="opacity-100 translate-y-0"
      leave-active-class="transition ease-out duration-200"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0">
      <div
        v-show="dropdownOpen"
        class="origin-top-right z-10 absolute top-full min-w-44 bg-white border border-slate-200 py-1.5 rounded shadow-lg overflow-hidden mt-1"
        :class="align === 'right' ? 'right-0' : 'left-0'">
        <ul
          ref="dropdown"
          @focusin="dropdownOpen = true"
          @focusout="dropdownOpen = false">
          <li>
            <router-link
              class="font-medium text-sm text-indigo-500 hover:text-indigo-600 flex items-center py-1 px-3"
              to="/support"
              @click="dropdownOpen = false">
              Support
            </router-link>
          </li>
          <li v-if="isAdmin">
            <router-link
              class="font-medium text-sm text-indigo-500 hover:text-indigo-600 flex items-center py-1 px-3"
              to="/settings/manage-users"
              @click="dropdownOpen = false">
              Settings
            </router-link>
          </li>
          <li v-if="!isAdmin">
            <router-link
              class="font-medium text-sm text-indigo-500 hover:text-indigo-600 flex items-center py-1 px-3"
              to="/settings/myprofile"
              @click="dropdownOpen = false">
              Settings
            </router-link>
          </li>
          <li>
            <router-link
              class="font-medium text-sm text-indigo-500 hover:text-indigo-600 flex items-center py-1 px-3"
              to="/signin"
              @click="dropdownOpen = false; logout();">
              Sign Out
            </router-link>
          </li>
        </ul>
      </div>
    </transition>
  </div>
</template>

<script>
import {onMounted, onUnmounted, ref} from 'vue';

export default {
  name: 'DropdownProfile',
  props: ['align'],
  computed: {
    avatarName() {
      var fn = localStorage.getItem('firstName');
      var ln = localStorage.getItem('lastName');
      if (ln == '') return fn[0].toUpperCase();
      else return fn[0].toUpperCase() + ln[0].toUpperCase();
    },
    userName() {
      console.log('Name: ' + localStorage.getItem('firstName'));
      return (
        localStorage.getItem('firstName') +
        ' ' +
        localStorage.getItem('lastName')
      );
    },
    avatarColors() {
      if (localStorage.getItem('avatarColors') != null) {
        var colors = JSON.parse(localStorage.getItem('avatarColors'));
        return colors.from + ' ' + colors.to;
      } else return '';
    },
    isAdmin() {
      let userInfo = JSON.parse(localStorage.getItem('userInfo'));
      return userInfo && userInfo.roles.includes('admin') || userInfo.roles.includes('customer-admin');
    },
  },
  data() {
    return {
      profileName: 'Raleon, Inc.',
    };
  },
  methods: {
    logout() {
      localStorage.removeItem('token');
    },
  },
  setup() {
    const dropdownOpen = ref(false);
    const trigger = ref(null);
    const dropdown = ref(null);
    // close on click outside
    const clickHandler = ({target}) => {
      if (
        !dropdownOpen.value ||
        dropdown.value.contains(target) ||
        trigger.value.contains(target)
      )
        return;
      dropdownOpen.value = false;
    };

    // close if the esc key is pressed
    const keyHandler = ({keyCode}) => {
      if (!dropdownOpen.value || keyCode !== 27) return;
      dropdownOpen.value = false;
    };

    onMounted(() => {
      document.addEventListener('click', clickHandler);
      document.addEventListener('keydown', keyHandler);

      //let possibleProfileName = localStorage.getItem('firstName') || 'Profile';
      //console.log('PROFIEL NAME: ' + possibleProfileName);
      //this.profileName = possibleProfileName;
    });

    onUnmounted(() => {
      document.removeEventListener('click', clickHandler);
      document.removeEventListener('keydown', keyHandler);
    });

    return {
      dropdownOpen,
      trigger,
      dropdown,
    };
  },
};
</script>
