<style scoped>
 input[type=search].manual-clear::-webkit-search-cancel-button {
    appearance: searchfield-cancel-button;
}
</style>
<template>
  <div class="relative inline-flex w-full">
    <input
      class="
        btn w-full
        justify-between
        min-w-44
        bg-white
        border-slate-200
        hover:border-slate-300
        text-slate-500
        hover:text-slate-600
      "
      :class="{ 'manual-clear': !clearOnClick }"
      ref="input"
      aria-label="Select date range"
      type="search"
      :list="dataListId"
      :value="getSelectedName()"
      @input="selectedNameChange"
      @mousedown="clicked"
      @touchstart="clicked"
      @blur="restore"
      @change.stop
    />

    <datalist :id="dataListId">
      <option v-for="option in options" :value="option.name" />
    </datalist>
  </div>
</template>

<script>
import { uuidv4 } from '../utils/Utils';

export default {
  name: 'DropdownFull',
  props: ['options', 'reset', 'selectedId', 'clearOnClick', 'overrideName'],
  data() {
    return {
      selectedName: '',
      dataListId: `options-list-${uuidv4()}`,
    };
  },
  methods: {
    getSelectedName: function () {
	  if (this.overrideName) {
		return this.overrideName;
	  }

      if (this.selectedId === -1 || (this.selectedId !== 0 && !this.selectedId)) {
        return '';
      }

	  //console.log("HELLO HELLO", JSON.stringify(this.options));

      const match = this.options.find(x => x.id == this.selectedId);
      if (match) {
        return match.name;
      }
    },
    selectedNameChange: function (event) {
      const val = event.target.value;
      if (val === '' && this.$refs.input) {
        this.$refs.input.blur();
      }

      const match = this.options.find(x => x.name.trim() === val.trim());
      if (match) {
        this.selectedName = match.name;
        this.openData
        this.$emit('change', match.id);

        if (this.$refs.input) {
          this.$refs.input.blur();
        }
      }
    },
    clicked: function () {
      if (this.clearOnClick && this.$refs.input) {
        this.$refs.input.value = '';
      }
    },
    restore: function () {
      if (this.clearOnClick && this.$refs.input) {
        this.$refs.input.value = this.getSelectedName();
      }
    }
  },
};
</script>
