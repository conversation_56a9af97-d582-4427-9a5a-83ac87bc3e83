<template>
  <header class="
      px-5
      pt-4
      flex
      items-center
      justify-between
    ">
    <h2 class="font-semibold text-slate-800">{{ title }}</h2>

    <div align="right" class="relative inline-flex">

      <EditMenu align="right" class="relative inline-flex text-left" v-if="isEditable">
        <!-- <li v-if="isWalletListVisible">
          <a class="
              font-medium
              text-sm text-slate-600
              hover:text-slate-900
              flex
              py-1
              px-3
              cursor-pointer
            " @click.stop="viewWallets">View Wallets</a>
        </li> -->
        <li>
          <a class="
              font-medium
              text-sm text-slate-600
              hover:text-slate-900
              flex
              py-1
              px-3
            " href="#" @click.stop="$emit('editWidget')">Edit</a>
        </li>
		<li>
          <a class="
              font-medium
              text-sm text-slate-600
              hover:text-slate-900
              flex
              py-1
              px-3
            " href="#" @click.stop="$emit('refreshWidget')">Refresh</a>
        </li>
		<li>
          <a class="
              font-medium
              text-sm text-slate-600
              hover:text-slate-900
              flex
              py-1
              px-3
            " href="#" @click.stop="$emit('viewApi')">View API</a>
        </li>
        <li>
          <a class="
              font-medium
              text-sm text-rose-500
              hover:text-rose-600
              flex
              py-1
              px-3
            " href="#" @click.stop="$emit('removeWidget')">Remove</a>
        </li>
      </EditMenu>
    </div>

  </header>
  <div v-bind:class="{ 'border-b border-slate-100 pb-2': smallHeader != 'true' }">
    <h4 class="text-xs font-semibold text-slate-400 lowercase ml-5" v-if="infoLabel != null">
		<span v-if="isMissingDataSource" style="text-transform: initial"><img class="" src="../images/warning-icon.svg" style="display: inline; transform: scale(0.7); filter: invert(75%) sepia(3%) saturate(2403%) hue-rotate(175deg) brightness(88%) contrast(83%);" />This chart's data source has been removed and the chart will no longer update.<br/>
		<span v-if="infoLabel">(</span></span>{{ infoLabel }}<span v-if="isMissingDataSource && infoLabel">)</span>
	</h4>
    <h4 class="text-xs font-semibold text-slate-400 lowercase ml-5" v-if="infoLabel2 != null">{{ infoLabel2 }}</h4>
  </div>
</template>

<script>
import FilterButton from '../components/DropdownFilter.vue';
import EditMenu from './DropdownEditMenu.vue';
import Tooltip from './Tooltip.vue';

export default {
  name: 'ReportCardHeader',
  components: {
    EditMenu,
    FilterButton,
    Tooltip
  },
  props: ['title', 'editMenu', 'infoLabel', 'infoLabel2', 'smallHeader', 'tooltipInfo', 'widgetId', 'metricData', 'hasWalletList', 'isMissingDataSource'],
  computed: {
    isEditable() {
      if (this.editMenu == "false")
        return false;
      else if (this.editMenu == null || this.editMenu == true)
        return true;
    },
    isWalletListVisible() {
      console.log("Do we have the wallet list? " + this.hasWalletList);
      if (this.hasWalletList == "false" || this.hasWalletList == null)
        return false;
      else
        return true;
    }
  },
  setup() {
    const devEnabled = localStorage.getItem('devEnabled');
    const betaEnabled = localStorage.getItem('betaEnabled');

    return {
      devEnabled,
      betaEnabled
    }
  },
  methods: {
    viewWallets() {
      this.$router.push({
        name: 'Reports',
        params: {
          widgetId: this.widgetId,
          metricData: JSON.stringify(this.metricData),
        }
      });
    }
  }


};
</script>
