<template>
  <!-- Modal backdrop -->
  <transition
    enter-active-class="transition ease-out duration-200"
    enter-from-class="opacity-0"
    enter-to-class="opacity-100"
    leave-active-class="transition ease-out duration-100"
    leave-from-class="opacity-100"
    leave-to-class="opacity-0"
  >
    <div v-show="modalOpen" class="fixed inset-0 bg-slate-900 bg-opacity-30 z-50 transition-opacity" aria-hidden="true"></div>
  </transition>
  <!-- Modal dialog -->
  <transition
    enter-active-class="transition ease-in-out duration-200"
    enter-from-class="opacity-0 translate-y-4"
    enter-to-class="opacity-100 translate-y-0"
    leave-active-class="transition ease-in-out duration-200"
    leave-from-class="opacity-100 translate-y-0"
    leave-to-class="opacity-0 translate-y-4"
  >
    <!-- Start -->
    <ModalBasic id="choose-data-modal" :modalOpen="chooseDataModelOpen" @close-modal="chooseDataModelOpen = false" title="Choose Data Source">
      <!-- Modal content -->
      <div class="px-5 py-4">
        <div class="mb-5">
          <label class="block text-sm font-medium mb-1" for="role">Start from Template</label>
          <select id="role" class="form-select w-full">
            <option value="token">Custom</option>
            <option value="smart_contract">DeFI</option>
            <option value="token">Game</option>
            <option value="token">Exchange</option>
            <option value="token">NFT</option>
          </select>
        </div>

        <div class="text-sm mb-5">
          <form>
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium mb-1" for="name">Name</label>
                <input id="wallet-address" class="form-input w-full" type="text" placeholder="Name of your dashboard" />
              </div>
            </div>
          </form>
        </div>
      </div>
      <!-- Modal footer -->
      <div class="px-5 py-4 border-t border-slate-200">
        <div class="flex flex-wrap justify-end space-x-2">
          <button class="btn-sm border-slate-200 hover:border-slate-300 text-slate-600" @click.stop="chooseDataModelOpen = false">Cancel</button>
          <button class="btn-sm bg-indigo-500 hover:bg-indigo-600 text-white">Add</button>
        </div>
      </div>
    </ModalBasic>
    <!-- End -->
  </transition>
</template>

<script>
import { ref, nextTick, onMounted, onUnmounted, watch } from 'vue'

export default {
  name: 'ModalChooseDataSource',
  emits: ['open-modal', 'close-modal'],
  setup(props, { emit }) {

    const modalContent = ref(null)
    const searchInput = ref(null)
    
    // close on click outside
    const clickHandler = ({ target }) => {
      if (!props.modalOpen || modalContent.value.contains(target)) return
      emit('close-modal')
    }

    // close if the esc key is pressed
    const keyHandler = ({ keyCode }) => {
      if (!props.modalOpen || keyCode !== 27) return
      emit('close-modal')
    }

    onMounted(() => {
      document.addEventListener('click', clickHandler)
      document.addEventListener('keydown', keyHandler)
    })

    onUnmounted(() => {
      document.removeEventListener('click', clickHandler)
      document.removeEventListener('keydown', keyHandler)
    })

    watch(() => props.modalOpen, (open) => {
      open && nextTick(() => searchInput.value.focus())
    })    
  }
}
</script>