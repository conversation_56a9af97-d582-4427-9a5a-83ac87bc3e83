<template>
  <div>
    <nav class="flex justify-between mt-6" role="navigation" aria-label="Navigation">
      <div class="flex-1 mr-2">
        <a class="btn bg-white border-slate-200" href="#" :disabled="this.currentPage == 1"
          @click.prevent="getPage(this.currentPage - 1)"
          :class="[this.currentPage == 1 ? 'text-slate-300 cursor-not-allowed' : 'text-indigo-500']">&lt;-
          <span class="hidden sm:inline">&nbsp;Previous</span></a>
      </div>
      <div class="grow text-center">
        <ul class="inline-flex text-sm font-medium -space-x-px" v-for="page in getDisplayedPages()">
          <li v-if="page !== '...'">
            <a href="#" @click.prevent="getPage(page)">
              <span
                class="inline-flex items-center justify-center rounded-full leading-5 px-4 py-4 border border-slate-200 shadow-sm"
                :class="[
                  page == this.currentPage ? 'bg-ralpurple-500 text-white' : 'bg-white text-indigo-500'
                ]">
                <span class="w-5 w-auto" style="min-width: 1.25rem">{{ page }}</span>
              </span>
            </a>
          </li>
          <li v-else>
            <span
              class="inline-flex items-center justify-center rounded-full leading-5 px-4 py-4 bg-white border border-slate-200 text-indigo-500 shadow-sm">
              <span class="w-5">...</span>
            </span>
          </li>
        </ul>
      </div>
      <div class="flex-1 text-right ml-2">
        <a class="btn bg-white border-slate-200 hover:border-slate-300 text-indigo-500" href="#"
          :disabled="this.currentPage == pageCount" @click.prevent="getPage(this.currentPage + 1)"
          :class="[this.currentPage == pageCount ? 'text-slate-300 cursor-not-allowed' : 'text-indigo-500']">
          <span class="hidden sm:inline">Next&nbsp;</span>-&gt;</a>
      </div>
    </nav>
  </div>
</template>

<script>
export default {
  name: 'PaginationNumeric2',
  props: ['pageCount'],
  data() {
    return {
      currentPage: 1
    }
  },
  methods: {
    getPage(page) {
      if (page > 0 && page <= this.pageCount) {
        this.currentPage = page;
        this.$emit('page', page);
      }
    },
    getDisplayedPages() {
      if (this.pageCount < 6) {
        let pages = [];
        for (let i = 1; i <= this.pageCount; i++) {
          pages.push(i);
        }
        return pages;
      }
      if (this.currentPage < 4) {
        return [1, 2, 3, 4, '...', this.pageCount];
      }
      if (this.currentPage >= 4 && this.currentPage < this.pageCount - 3) {
        return [1, '...', this.currentPage - 1, this.currentPage, this.currentPage + 1, '...', this.pageCount];
      }
      if (this.currentPage >= this.pageCount - 3) {
        return [1, '...', this.pageCount - 3, this.pageCount - 2, this.pageCount - 1, this.pageCount];
      }
    }
  },
  emits: ['page']
}
</script>
