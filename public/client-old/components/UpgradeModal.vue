<template>
	<ModalBasic id="upgrade-modal" :fitContent="true" :modalOpen="modalOpen && !isSelfServiceUpgradeRequested" @close-modal="$emit('close-modal')" title="Upgrade">
		<div class="flex flex-col">
            <!-- Modal content -->
            <div class="px-5 py-4 flex-grow" style="min-width: 80vw; min-height: 80vh">
              <h1 style="font-size: 5em; font-weight: bold; text-align: center">Upgrade Now</h1>
			  <h4 style="font-size: 3em;">Benefits go brrr</h4>
			  <p>Only goes up 📈📈📈</p>
			  <img src="https://i.giphy.com/media/np0ar0Kk1nOBor3kku/giphy.webp"/>
            </div>
            <!-- Modal footer -->
            <div class="px-5 py-4 border-t border-slate-200">
              <div class="flex flex-wrap justify-end space-x-2">
                <button class="btn-sm border-slate-200 hover:border-slate-300 text-slate-600" @click.stop="$emit('close-modal')">Cancel</button>
                <button class="btn-sm bg-indigo-500 hover:bg-indigo-600 text-white" @click.stop="requestUpgrade()">Upgrade Account</button>
              </div>
            </div>
		</div>
	</ModalBasic>
	<ModalBasic id="upgrade-confirmation-modal" :fitContent="false" :modalOpen="isConfirmationModalOpen || (modalOpen && isSelfServiceUpgradeRequested)" @close-modal="isConfirmationModalOpen = false; $emit('close-modal')" title="Upgrade Requested">
		<div class="flex flex-col">
            <!-- Modal content -->
            <div class="px-5 py-4 flex-grow">
			  <p>A request to upgrade your account has been submitted successfully. Someone from Raleon will reach out shortly.</p>
            </div>
            <!-- Modal footer -->
            <div class="px-5 py-4 border-t border-slate-200">
              <div class="flex flex-wrap justify-end space-x-2">
                <button class="btn-sm border-slate-200 hover:border-slate-300 text-slate-600" @click.stop="isConfirmationModalOpen = false; $emit('close-modal')">Close</button>
              </div>
            </div>
		</div>
	</ModalBasic>

  </template>

  <script>
	import ModalBasic from './ModalBasic.vue';
	import * as Utils from '../utils/Utils';


	const URL_DOMAIN = Utils.URL_DOMAIN;

	export default {
		name: 'UpgradeModal',
		components: {
			ModalBasic
		},
		props: ['modalOpen'],
		emits: ['close-modal'],
		data() {
			return {
				isConfirmationModalOpen: false
			}
		},
		methods: {
			async requestUpgrade() {
				const response = await fetch(`${URL_DOMAIN}/onboard/self-service/upgrade`, {
					method: 'POST',
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					}
				});

				if (response.ok && response.status >= 200 && response.status < 300) {
					this.isConfirmationModalOpen = true;
					localStorage.setItem('selfServiceUpgradeRequested', true);
					this.$emit('close-modal');
				}
			}
		},
		computed: {
			isSelfServiceUpgradeRequested() {
				return localStorage.getItem('selfServiceUpgradeRequested') == 'true';
			}
		}
	}
  </script>
