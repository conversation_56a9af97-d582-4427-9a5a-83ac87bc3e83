<template>
	<div class="flex justify-center items-center relative">
		<div
			id="editableHeader"
			contenteditable="true"
			placeholder="Untitled"
			spellcheck="false"
			class="mb-4 sm:mb-0 min-w-24
			text-2xl md:text-3xl font-bold cursor-text
			bg-clip-text bg-gradient-to-r from-ralpurple-500 to-ralocean-500
			focus:opacity-50
			focus:underline focus:underline-offset-4
			hover:underline hover:underline-offset-4 decoration-2"
			@mouseover="editing = true"
			@mouseout="editing = false"
			@focusin="editing = true"
			@focusout="updateHeader"
			@keydown.enter="handleEnterKeyPress"
			ref="editableHeader"
		>
			{{headerText}}
		</div>
		<img
			v-if="editing"
			class="ml-2 mt-2 h-4 w-4 items-center"
			src="../images/edit-pencil.svg"
		/>
	</div>
</template>

<script>
	import Sidebar from '../partials/Sidebar.vue'
	export default {
		name: 'EditableHeader',
		components: {
    		Sidebar,
		},
		emits: ['updatedHeader'],
		props: ['headerText'],
		data() {
			return {
				editing: false
			}
		},
		methods: {
			updateHeader() {
				this.editing = false;
				const newHeader = this.$refs.editableHeader.innerText;
				if (!(newHeader.trim())) {
					this.$emit('updatedHeader', 'Untitled');
				} else {
					this.$emit('updatedHeader', newHeader);
				}
			},
			handleEnterKeyPress() {
				this.$refs.editableHeader.blur();
			}
		},
		mounted() {
			if (this.headerText === 'Untitled' || this.headerText === '') {
				this.$refs.editableHeader.focus();
			}
		}
	}

</script>

<style>
	[contenteditable]:empty::after {
		content: attr(placeholder);
		opacity: .5;
	}

	#editableHeader:not(:focus) {
		color: transparent
	}
</style>
