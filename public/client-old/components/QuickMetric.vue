﻿<template>
  <div
    class="
      col-span-3
      sm:col-span-2
      md:col-span-3
      lg:col-span-3
      xl:col-span-3
      max-w-sm
      bg-white
      shadow-lg
      rounded-sm
      border border-slate-200
    "
  >
    <div class="flex flex-col flex-1 gap-5 sm:p-2" v-if="!isLoaded">
      <div class="bg-gray-200 w-full animate-pulse h-3 rounded-2xl"></div>
      <div class="h-24 w-full rounded-xl bg-gray-200 animate-pulse"></div>
    </div>

    <div class="px-5 py-5 pt-5" v-if="isLoaded">
      <header class="flex items-center justify-between">
        <h3 class="text-sm font-semibold text-slate-500 uppercase mb-1">
          {{ title }}
        </h3>
        <div align="right" class="relative inline-flex">
          <!-- <EditMenu align="right" class="relative inline-flex text-left">
            <li class="text-center py-1 border-b border-slate-200">
              <a href="#">
                <span class="text-center text-sm px-2.5 py-1 mb-1">MyProtocol</span>
              </a>
            </li>
            <li>
              <a class="font-medium text-sm text-slate-600 hover:text-slate-900 flex py-1 px-3" href="#0">Change Project</a>
            </li>
            <li>
              <a class="font-medium text-sm text-rose-500 hover:text-rose-600 flex py-1 px-3" href="#0">Remove</a>
            </li>
          </EditMenu> -->
        </div>
      </header>

      <h4
        class="text-xs font-semibold text-slate-400 lowercase mb-1"
        v-if="showLabel === true"
      >
        {{ infoLabel }}
      </h4>

      <div class="flex items-start">
        <div
          class="text-2xl font-bold text-slate-800 mb-1"
          v-if="labelType === 'currency'"
        >
          <span v-if="!showNoDataTooltip">{{ label }}</span>{{ shorthandAmount }}
          <Tooltip size="lg" bg="dark" position="right" class="mt-3 ml-2" v-if="showNoDataTooltip">
            <div class="text-sm font-medium text-slate-200" >
              We do not currently have data for this chart, or the data has not yet been fully processed.
            </div>
          </Tooltip>
        </div>
        <div
          class="flex text-2xl font-bold text-slate-800 mb-1 mr-2"
          v-else-if="labelType === 'percent'"
        >
          {{ shorthandAmount }}<span v-if="!showNoDataTooltip">{{ label }}</span>
          <Tooltip size="lg" bg="dark" position="right" class="mt-3 ml-2" v-if="showNoDataTooltip">
            <div class="text-sm font-medium text-slate-200" >
              We do not currently have data for this chart, or the data has not yet been fully processed.
            </div>
          </Tooltip>
        </div>
        <div
          class="flex items-start text-2xl font-bold text-slate-800 mb-1 mr-2"
          v-else
        >
          {{ shorthandAmount }}
          <Tooltip size="lg" bg="dark" position="right" class="mt-3 ml-2" v-if="showNoDataTooltip">
            <div class="text-sm font-medium text-slate-200" >
              We do not currently have data for this chart, or the data has not yet been fully processed.
            </div>
          </Tooltip>
        </div>

        <div class="text-sm" v-if="indicator === 'inc'">
          <div
            class="
              text-sm
              font-semibold
              text-white
              px-1.5
              bg-green-600
              rounded-full
            "
          >
            {{ percentVal }}%
          </div>
        </div>

        <div class="text-sm" v-else-if="indicator === 'dec'">
          <div
            class="
              text-sm
              font-semibold
              text-white
              px-1.5
              bg-rose-500
              rounded-full
            "
          >
            {{ percentVal }}%
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import EditMenu from '../components/DropdownEditMenu.vue';
import FilterButton from '../components/DropdownFilter.vue';
import Tooltip from '../components/Tooltip.vue';
import { abbrevAmount } from '../utils/Utils';

export default {
  name: 'QuickMetric',
  components: {
    EditMenu,
    FilterButton,
    Tooltip
  },
  props: [
    'title',
    'amount',
    'label',
    'indicator',
    'percentVal',
    'infoLabel',
    'isLoaded',
  ], //'type'
  setup(props, context) {
    const typeColor = type => {
      switch (type) {
        case 'warning':
          return 'bg-amber-500';
        case 'error':
          return 'bg-rose-500';
        case 'success':
          return 'bg-emerald-500';
        default:
          return 'bg-indigo-500';
      }
    };

    var shorthandAmount = 0;
    if(props.amount == '')
      shorthandAmount = '...';
    else
      shorthandAmount = props.amount;

    //Label checker
    var labelType = null;

    if (props.label === '$') {
      labelType = 'currency';
    } else if (props.label === '%') {
      labelType = 'percent';
    }

    function updateValue(newVal) {
      if (typeof newVal === 'string') this.shorthandAmount = newVal;
      else this.shorthandAmount = abbrevAmount(newVal);
    }

    return {
      labelType,
      shorthandAmount,
      typeColor,
      updateValue,
    };
  },
  computed: {
    showLabel() {
      if (this.infoLabel != 'null' && this.infoLabel != undefined) return true;
    },
    showNoDataTooltip() {
      if(this.shorthandAmount == '...') {
        return true;
      }
      else {
        return false;
      }
    }
  },
  watch: {
    amount: function (newVal, oldVal) {
      console.log('Value updated from: ' + oldVal + ' to ' + newVal);
      this.updateValue(newVal);
    },
  },
  async mounted() {
    this.updateValue(this.shorthandAmount);
  },
};
</script>
