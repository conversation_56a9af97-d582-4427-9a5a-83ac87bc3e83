<template>
	<div v-if="isRaleonSupportUser" class=" mb-8 bg-white rounded-xl bg-ralinfo-light bg-opacity-50 border border-slate-200 relative px-5 py-4">
		<div class="flex flex-col justify-start">
			<img src="../../images/logo-horizontal-purple.svg" class="w-60 h-20" />
			<div class="-mt-6 ml-14 text-[#4F3EAC] uppercase tracking-widest">Support</div>

			<div class="bg-white rounded-md border border-slate-200 relative px-5 py-4 mt-6 w-fit max-w-sm max-h-xs shadow-lg shadow-slate-400">
				<div class="text-md text-ralblack-primary uppercase" v-html="cardText"></div>
				<div class="flex justify-start mt-4">
					<button
						class="flex items-center rounded-button border border-md border-slate-500  text-ralblack-primary bg-white hover:bg-slate-100 disabled:text-white disabled:bg-ralbutton-primary-light-deactivated disabled:cursor-not-allowed font-bold text-xs py-2 px-4"
						@click="refreshClicked"
						:disabled="isRefreshing || doneRefreshing">
						<svg v-if="isRefreshing" class="animate-spin w-4 h-4 fill-current shrink-0 mr-2" viewBox="0 0 16 16">
							<path d="M8 16a7.928 7.928 0 01-3.428-.77l.857-1.807A6.006 6.006 0 0014 8c0-3.309-2.691-6-6-6a6.006 6.006 0 00-5.422 8.572l-1.806.859A7.929 7.929 0 010 8c0-4.411 3.589-8 8-8s8 3.589 8 8-3.589 8-8 8z" />
						</svg>
						<img v-if="doneRefreshing" src="../../images/check.svg" class="mr-2" />
						<img v-if="errorRefreshing" src="../../images/error.svg" class="mr-2" />
						{{buttonText}}
					</button>
					<button
						v-if="showSmartContractButton"
						class="flex ml-2 items-center rounded-button border border-md border-slate-500  text-ralblack-primary bg-white hover:bg-slate-100 disabled:text-white disabled:bg-ralbutton-primary-light-deactivated disabled:cursor-not-allowed font-bold text-xs py-2 px-4"
						@click="refreshSmartContractClicked"
						:disabled="isSmartContractRefreshing || doneSmartContractRefreshing">
						<svg v-if="isSmartContractRefreshing" class="animate-spin w-4 h-4 fill-current shrink-0 mr-2" viewBox="0 0 16 16">
							<path d="M8 16a7.928 7.928 0 01-3.428-.77l.857-1.807A6.006 6.006 0 0014 8c0-3.309-2.691-6-6-6a6.006 6.006 0 00-5.422 8.572l-1.806.859A7.929 7.929 0 010 8c0-4.411 3.589-8 8-8s8 3.589 8 8-3.589 8-8 8z" />
						</svg>
						<img v-if="doneSmartContractRefreshing" src="../../images/check.svg" class="mr-2" />
						<img v-if="errorSmartContractRefreshing" src="../../images/error.svg" class="mr-2" />
						Pull Latest Smart Contracts/NFTs/Tokens
					</button>
				</div>
			</div>

			<div v-if="(orgId == '1' || orgId == '5')" class="bg-white rounded-md border border-slate-200 relative px-5 py-4 mt-6 w-fit max-w-sm max-h-xs shadow-lg shadow-slate-400">
				<div class="text-md text-ralblack-primary uppercase">Spin up a new Envrionment and User. Default password is "raleonBillions!""</div>
				<div class="flex justify-start mt-4">
					<button
						class="flex items-center rounded-button border border-md border-slate-500  text-ralblack-primary bg-white hover:bg-slate-100 disabled:text-white disabled:bg-ralbutton-primary-light-deactivated disabled:cursor-not-allowed font-bold text-xs py-2 px-4"
						@click="launchCreateEnv">
						Create Environment
					</button>
				</div>
			</div>
		</div>
	</div>
</template>

<script>

	import { ref, onMounted } from 'vue';
	import * as Utils from '../../utils/Utils';
	const URL_DOMAIN = Utils.URL_DOMAIN;

	export default {
		name: 'RefreshDataCard',
		props: ['cardText', 'buttonText', 'doneRefreshing', 'errorRefreshing', 'doneSmartContractRefreshing', 'errorSmartContractRefreshing', 'showSmartContractButton'],
		emits: ['refreshDataClicked','refreshSmartContractClicked', 'createEnv'],
		setup(props, context) {
			let isRaleonSupportUser = ref(false)
			const isRefreshing = ref(false);
			const isSmartContractRefreshing = ref(false);
			let orgId = ref(0);
			onMounted(async () => {
				const response = await fetch(`${URL_DOMAIN}/users/is-raleon-support`, {
					method: 'GET',
					withCreditentials: true,
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Access-Control-Allow-Origin': '*',
						'Content-Type': 'application/json'
					},
				});
				const data = await response.json();
				isRaleonSupportUser.value = data;
				orgId.value = localStorage.getItem('userOrgId')
				console.log("ORG ID: ", orgId)
			});

			const refreshClicked = () => {
				isRefreshing.value = true;
				context.emit('refreshDataClicked');
			}

			const refreshSmartContractClicked = () => {
				isSmartContractRefreshing.value = true;
				context.emit('refreshSmartContractClicked');
			}

			const launchCreateEnv = () => {
				context.emit('createEnv');
			}

			return {
				isRaleonSupportUser,
				isRefreshing,
				isSmartContractRefreshing,
				refreshClicked,
				refreshSmartContractClicked,
				launchCreateEnv,
				orgId
			}
		},
		watch: {
			doneRefreshing: function (newVal) {
				if (newVal) {
					this.isRefreshing = false;
				}
			},
			errorRefreshing: function (newVal) {
				if (newVal) {
					this.isRefreshing = false;
				}
			},
			doneSmartContractRefreshing: function (newVal) {
				if (newVal) {
					this.isSmartContractRefreshing = false;
				}
			},
			errorSmartContractRefreshing: function (newVal) {
				if (newVal) {
					this.isSmartContractRefreshing = false;
				}
			}
		}
	}

</script>>
