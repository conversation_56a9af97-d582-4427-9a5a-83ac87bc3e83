﻿<template>
  <div class="px-5 py-5 pt-5">
    <div class="flex items-start">
      <div
        class="text-2xl font-bold text-slate-800 mb-1"
        v-if="labelType === 'currency'"
      >
        {{ label }}{{ shorthandAmount }}
        <Tooltip size="lg" bg="dark" position="right" class="mt-3 ml-2" v-if="showNoDataTooltip">
          <div class="text-sm font-medium text-slate-200" >
            We do not currently have data for this chart, or the data has not yet been fully processed.
          </div>
        </Tooltip>
      </div>
      <div
        class="text-2xl font-bold text-slate-800 mb-1 mr-2"
        v-else-if="labelType === 'percent'"
      >
        {{ shorthandAmount }}{{ label }}
          <Tooltip size="lg" bg="dark" position="right" class="mt-3 ml-2" v-if="showNoDataTooltip">
            <div class="text-sm font-medium text-slate-200" >
              We do not currently have data for this chart, or the data has not yet been fully processed.
            </div>
          </Tooltip>
      </div>
      <div
        class="flex items-start text-2xl font-bold text-slate-800 mb-1 mr-2"
        v-else
      >
        {{ shorthandAmount }}
          <Tooltip size="lg" bg="dark" position="right" class="mt-3 ml-2" v-if="showNoDataTooltip">
            <div class="text-sm font-medium text-slate-200" >
              We do not currently have data for this chart, or the data has not yet been fully processed.
            </div>
          </Tooltip>
      </div>

      <div class="text-sm" v-if="indicator === 'inc'">
        <div
          class="
            text-sm
            font-semibold
            text-white
            px-1.5
            bg-green-600
            rounded-full
          "
        >
          {{ percentVal }}
        </div>
      </div>

      <div class="text-sm" v-else-if="indicator === 'dec'">
        <div
          class="
            text-sm
            font-semibold
            text-white
            px-1.5
            bg-rose-500
            rounded-full
          "
        >
          {{ percentVal }}%
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import EditMenu from '../components/DropdownEditMenu.vue';
import FilterButton from '../components/DropdownFilter.vue';
import Tooltip from '../components/Tooltip.vue';
import { abbrevAmount } from '../utils/Utils';

export default {
  name: 'NumberChart',
  components: {
    EditMenu,
    FilterButton,
    Tooltip
  },
  props: [
    'title',
    'amount',
    'label',
    'indicator',
    'percentVal',
    'infoLabel',
  ], //'type'
  setup(props, context) {
    const typeColor = type => {
      switch (type) {
        case 'warning':
          return 'bg-amber-500';
        case 'error':
          return 'bg-rose-500';
        case 'success':
          return 'bg-emerald-500';
        default:
          return 'bg-indigo-500';
      }
    };

    var shorthandAmount = 0;

    //Shorthand run - should turn into a function as we use this in RankedChart too
    if(props.amount == '')
      updateValue('...');
    else
      updateValue(props.amount);

    //Label checker
    var labelType = null;

    if (props.label === '$') {
      labelType = 'currency';
    } else if (props.label === '%') {
      labelType = 'percent';
    }

    function updateValue(newVal) {
      if (typeof newVal === 'string') {
        shorthandAmount = newVal;
      }
      else
        shorthandAmount = abbrevAmount(newVal);
    }

    return {
      labelType,
      shorthandAmount,
      typeColor,
      updateValue
    };
  },
  computed: {
    showLabel() {
      if (this.infoLabel != 'null' && this.infoLabel != undefined) return true;
    },
    showNoDataTooltip() {
      if(this.shorthandAmount == '...') {
        return true;
      }
      else {
        return false;
      }
    }
  },
  watch: {
    amount: function (newVal, oldVal) {
      console.log('Value updated from: ' + oldVal + ' to ' + newVal);
      this.shorthandAmount = newVal;
      //this.updateValue(newVal);
    },
  },
  async mounted() {
    this.updateValue(this.shorthandAmount);
  },
};
</script>
