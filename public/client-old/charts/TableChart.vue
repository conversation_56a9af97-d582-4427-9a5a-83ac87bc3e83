﻿<template>
  <div class="grow p-3">
    <div class="flex flex-col h-full">
      <!-- Card content -->
      <div class="overflow-x-auto">
        <table class="table-auto w-full">
          <!-- Table header -->
          <thead
            class="
              text-xs
              font-semibold
              uppercase
              text-slate-500
              bg-slate-50
              border-t border-b border-slate-200
            "
          >
            <tr v-if="newList != undefined">
              <th
                v-for="item in newList.headers"
                class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap"
              >
                <div class="font-semibold text-left">{{ item }}</div>
              </th>
            </tr>
          </thead>
          <!-- Table body -->
          <tbody
            v-if="newList != undefined"
            class="text-sm divide-y divide-slate-200"
          >
            <tr v-for="item in newList.rows" class="hover:bg-slate-50">
              <td
                v-for="r in item"
                class="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap w-px"
              >
                {{ r }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TableChart',
  props: ['data'],
  setup(props, context) {
    const newList = props.data;

    function updateTable(data) {
      var color = 'orange';
      var listUpdate = data;

      if (listUpdate == undefined || listUpdate == null) return null;

      this.newList = this.data;
    }

    return {
      newList,
      updateTable,
    };
  },
  watch: {
    data: function (newVal, oldVal) {
      this.updateTable(newVal);
    },
  },
  async mounted() {
    this.updateTable(this.data);
  },
};
</script>
