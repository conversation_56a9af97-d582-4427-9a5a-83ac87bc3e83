﻿<template>
  <div class="grow p-3">
    <div class="flex flex-col h-full">
      <!-- Card content -->
      <div class="grow">
        <ul class="flex justify-between text-xs uppercase text-slate-400 font-semibold px-2 space-x-2">
          <li>{{ primaryColumnTitle }}</li>
          <li>{{ secondaryColumnTitle }}</li>
        </ul>

        <ul class="space-y-1 text-sm text-slate-800 mt-3 mb-4">
          <li class="relative px-2 py-1" v-for="item in newList">
            <div v-html="item.html"></div>
            <div class="relative flex justify-between space-x-2">
              <div>
                <span class="text-sm font-semibold" v-if="item.bold === 1">{{ item.name }} </span>
                <span class="text-sm font-normal" v-else-if="item.bold != 1">{{ item.name }} </span>
                <span class="text-xs text-slate-500 ml-1" v-if="categoryIndicator === '1'">{{ item.category }}</span>
              </div>
              <div class="font-medium">{{ item.shorthandAmount }}</div>
            </div>
          </li>
        </ul>

      </div>
    </div>
  </div>
</template>

<script>
import { abbrevAmount } from '../utils/Utils'

export default {
  name: 'RankedChart',
  props: ['data', 'primaryColumnTitle', 'secondaryColumnTitle', 'categoryIndicator', 'color'],
  setup(props, context) {
    const newList = props.data

    function runRankedList(data) {
      var color = 'orange'
      var listUpdate = data
      
      if(listUpdate == undefined || listUpdate == null)
        return null

      //Compute our total so we can get percentages set
      var totalAmount = 0
      listUpdate.forEach(e => {
        totalAmount += e.amount
      })

      var i = listUpdate.length
      var baselineStart = 82
      listUpdate.forEach(e => {
          var calculatedPercent = (e.amount / totalAmount) * 82 //82 is the maximum length, so baselining to it

          if (i == listUpdate.length) {
            e.percent = baselineStart
          } else {
            baselineStart = baselineStart - calculatedPercent
            e.percent = baselineStart
          }
          i--

          if (props.color == 'orange')
            e.html = '<div class="absolute inset-0 bg-orange-100" aria-hidden="true" style="width: ' + e.percent + '%;">'
          else if (props.color == 'green')
            e.html = '<div class="absolute inset-0 bg-emerald-100" aria-hidden="true" style="width: ' + e.percent + '%;">'
          else if (props.color == 'purple')
            e.html = '<div class="absolute inset-0 bg-violet-100" aria-hidden="true" style="width: ' + e.percent + '%;">'

          e.shorthandAmount = abbrevAmount(e.amount)
      })
      this.newList = listUpdate
    }

    return {
      newList,
      runRankedList
    }
  },
  watch: {
    data: function (newVal, oldVal) {
      this.runRankedList(newVal);
    }
  },
  async mounted() {
    this.runRankedList(this.data);
  }
}
</script>
