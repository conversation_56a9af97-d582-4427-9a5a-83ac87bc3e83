<template>
	<div class="grow flex flex-col justify-center">
		<div className="px-5 py-3" v-if="totalIndicator === '1'">
			<div className="text-3xl font-bold text-slate-800">{{ totalValSymbol }}{{ formattedTotalVal }} <span
					class="text-xs">{{ totalValLabel }}</span></div>
		</div>

		<div>
			<canvas ref="canvas" :data="data" :width="width" :height="height"></canvas>
		</div>
		<div class="px-5 pt-2 pb-6">
			<ul ref="legend" class="flex flex-wrap justify-center -m-1"></ul>
		</div>
	</div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import {
	Chart, DoughnutController, RadialLinearScale, ArcElement, TimeScale, Tooltip, Legend
} from 'chart.js'
import 'chartjs-adapter-moment'

// Import utilities
import { tailwindConfig, abbrevAmount } from '../utils/Utils'

Chart.register(DoughnutController, ArcElement, TimeScale, Tooltip, Legend)

export default {
	name: '<PERSON>hnutChart',
	props: ['data', 'width', 'height', 'totalVal', 'totalIndicator', 'totalValLabel', 'totalValSymbol'],
	computed: {
		formattedTotalVal() {
			return abbrevAmount(this.totalVal)
		}
	},
	setup(props) {

		const canvas = ref(null)
		const legend = ref(null)
		let chart = null

		function createChart(data) {
			const ctx = canvas.value
			if (chart != undefined) chart.destroy();
			chart = new Chart(ctx, {
				type: 'doughnut',
				data: props.data,
				options: {
					cutout: '80%',
					layout: {
						padding: {
							top: 4,
							bottom: 4,
							left: 24,
							right: 24,
						},
					},
					plugins: {
						legend: {
							display: false,
						},
					},
					interaction: {
						intersect: false,
						mode: 'point',
					},
					tooltips: {
						callbacks: {
							label: function (tooltipItem, data) {
								let label = data.labels[tooltipItem.index];
								let value = data.datasets[tooltipItem.datasetIndex].data[tooltipItem.index];
								return `${label}: ${value}`;
							},
						},
					},
					animation: {
						duration: 200,
					},
					maintainAspectRatio: false,
				},
				plugins: [{
					id: 'htmlLegend',
					afterUpdate(c, args, options) {
						const ul = legend.value
						if (!ul) return
						// Remove old legend items
						while (ul.firstChild) {
							ul.firstChild.remove()
						}
						// Reuse the built-in legendItems generator
						const items = c.options.plugins.legend.labels.generateLabels(c)
						items.forEach((item) => {
							const li = document.createElement('li')
							li.style.margin = tailwindConfig().theme.margin[1]
							// Button element
							const button = document.createElement('button')
							button.classList.add('btn-xs')
							button.style.backgroundColor = tailwindConfig().theme.colors.white
							button.style.borderWidth = tailwindConfig().theme.borderWidth[1]
							button.style.borderColor = tailwindConfig().theme.colors.slate[200]
							button.style.color = tailwindConfig().theme.colors.slate[500]
							button.style.boxShadow = tailwindConfig().theme.boxShadow.md
							button.style.opacity = item.hidden ? '.3' : ''
							button.onclick = () => {
								c.toggleDataVisibility(item.index, !item.index)
								c.update()
							}
							// Color box
							const box = document.createElement('span')
							box.style.display = 'block'
							box.style.width = tailwindConfig().theme.width[2]
							box.style.height = tailwindConfig().theme.height[2]
							box.style.backgroundColor = item.fillStyle
							box.style.borderRadius = tailwindConfig().theme.borderRadius.sm
							box.style.marginRight = tailwindConfig().theme.margin[1]
							box.style.pointerEvents = 'none'
							// Label
							const label = document.createElement('span')
							label.style.display = 'flex'
							label.style.alignItems = 'center'
							const labelText = document.createTextNode(item.text)
							label.appendChild(labelText)
							li.appendChild(button)
							button.appendChild(box)
							button.appendChild(label)
							ul.appendChild(li)
						})
					},
				}],
			})
		}

		function refreshChart(data) {
			createChart(data);
		}

		return {
			canvas,
			legend,
			refreshChart,
			createChart
		}
	},
	watch: {
		data: function (newVal, oldVal) {
			this.refreshChart(newVal)
		}
	},
	async mounted() {
		this.refreshChart(this.data)
	}
}
</script>
