<template>
  <div class="px-5 py-3">
    <div class="flex flex-wrap justify-between items-end">
      <div class="flex items-start" v-if="summary === '1'">
        <div class="text-3xl font-bold text-slate-800 mr-2">{{ totalVal }}</div>

        <div class="text-sm" v-if="indicator === 'inc'">
          <div
            class="
              text-sm
              font-semibold
              text-white
              px-1.5
              bg-green-600
              rounded-full
            "
          >
            {{ percentVal }}%
          </div>
        </div>

        <div class="text-sm" v-else-if="indicator === 'dec'">
          <div
            class="
              text-sm
              font-semibold
              text-white
              px-1.5
              bg-amber-500
              rounded-full
            "
          >
            {{ percentVal }}%
          </div>
        </div>
      </div>
      <div class="grow ml-2 mb-1">
        <ul ref="legend" class="flex flex-wrap justify-end"></ul>
      </div>
    </div>
  </div>
  <!-- Chart built with Chart.js 3 -->
  <div class="grow">
    <canvas ref="canvas" :data="data" :width="width" :height="height"></canvas>
  </div>
</template>

<script>
import {
  Chart,
  Filler,
  LinearScale,
  LineController,
  LineElement,
  PointElement,
  TimeScale,
  Tooltip,
} from 'chart.js';
import 'chartjs-adapter-moment';
import {ref} from 'vue';
// Import utilities
import {formatNumber, tailwindConfig} from '../utils/Utils';

Chart.register(
  LineController,
  LineElement,
  Filler,
  PointElement,
  LinearScale,
  TimeScale,
  Tooltip,
);

export default {
  name: 'LineChartOverTimeDay',
  props: [
    'data',
    'width',
    'height',
    'totalVal',
    'percentVal',
    'indicator',
    'summary',
  ],
  setup(props) {
    const canvas = ref(null);
    const legend = ref(null);
    let chart = null;

    /*onMounted(() => {
      createChart(props.data);
    })*/

    //onUnmounted(() => chart.destroy())

    function createChart(data) {
      const ctx = canvas.value;
      if (chart != undefined) chart.destroy();
      chart = new Chart(ctx, {
        type: 'line',
        data: data,
        options: {
          layout: {
            padding: 20,
          },
          scales: {
            y: {
              grid: {
                drawBorder: false,
                beginAtZero: true,
              },
              ticks: {
                maxTicksLimit: 5,
                callback: value => formatNumber(value),
              },
            },
            x: {
              type: 'time',
              time: {
                parser: 'MM-DD-YYYY',
                unit: 'day',
              },
              grid: {
                display: false,
                drawBorder: false,
              },
              ticks: {
                autoSkipPadding: 48,
                maxRotation: 0,
              },
            },
          },
          plugins: {
            legend: {
              display: false,
            },
            tooltip: {
              callbacks: {
                title: context => false, // Disable tooltip title
                label: context => formatNumber(context.parsed.y),
              },
            },
          },
          interaction: {
            intersect: false,
            mode: 'nearest',
          },
          maintainAspectRatio: false,
          resizeDelay: 200,
        },
        plugins: [
          {
            id: 'htmlLegend',
            afterUpdate(c, args, options) {
              const ul = legend.value;
              if (!ul) return;
              // Remove old legend items
              while (ul.firstChild) {
                ul.firstChild.remove();
              }
              // Reuse the built-in legendItems generator
              const items = c.options.plugins.legend.labels.generateLabels(c);
              items.forEach(item => {
                const li = document.createElement('li');
                li.style.marginLeft = tailwindConfig().theme.margin[3];
                // Button element
                const button = document.createElement('button');
                button.style.display = 'inline-flex';
                button.style.alignItems = 'center';
                button.style.opacity = item.hidden ? '.3' : '';
                button.onclick = () => {
                  c.setDatasetVisibility(
                    item.datasetIndex,
                    !c.isDatasetVisible(item.datasetIndex),
                  );
                  c.update();
                };
                // Color box
                const box = document.createElement('span');
                box.style.display = 'block';
                box.style.width = tailwindConfig().theme.width[3];
                box.style.height = tailwindConfig().theme.height[3];
                box.style.borderRadius =
                  tailwindConfig().theme.borderRadius.full;
                box.style.marginRight = tailwindConfig().theme.margin[2];
                box.style.borderWidth = '3px';
                box.style.borderColor =
                  c.data.datasets[item.datasetIndex].borderColor;
                box.style.pointerEvents = 'none';
                // Label
                const label = document.createElement('span');
                label.style.color = tailwindConfig().theme.colors.slate[500];
                label.style.fontSize = tailwindConfig().theme.fontSize.sm[0];
                label.style.lineHeight =
                  tailwindConfig().theme.fontSize.sm[1].lineHeight;
                const labelText = document.createTextNode(item.text);
                label.appendChild(labelText);
                li.appendChild(button);
                button.appendChild(box);
                button.appendChild(label);
                ul.appendChild(li);
              });
            },
          },
        ],
      });
    }

    function refreshChart(data) {
      createChart(data);
    }

    return {
      canvas,
      legend,
      refreshChart,
      createChart,
    };
  },
  watch: {
    data: function (newVal, oldVal) {
      this.refreshChart(newVal);
    },
  },
  async mounted() {
    this.refreshChart(this.data);
  },
};
</script>
