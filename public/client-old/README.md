## Universal Dashboard & Reports

The dashboard /dashboard/universal/{dashboardId} will load the respective widgets from the DB for that dashboard.

## Universal Reports

When a widget is loaded, it loads in a universal report. Current report types supported:
- Number
- Pie
- Doughnut

The "data" field on the widget table is where an object gets passed that provides more instruction to the universal widget on how to handle the data. The object attributes that are being used currently are:
- Title is the name of the widget
- infoLabel is a brief description that's used to label widget
- apiMethod is the name of the API method that you want to be called to get the data.
- Inputs: array of what attributes to use as which column. Order matters
- XAxisInputLabel is what attribute to use as the labels for the x-axis

Running a new build
