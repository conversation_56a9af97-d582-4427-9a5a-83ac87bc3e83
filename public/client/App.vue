<template>
  <div>
    <!-- Store the global instance of BrandImageManager that can be accessed by other components -->
    <BrandImageManager ref="brandImageManager" style="display: none;" />

    <!-- Main App Content -->
    <RouterView />
  </div>
</template>

<script>
import { RouterView } from 'vue-router';
import BrandImageManager from './components/BrandImageManager.vue';

export default {
  name: 'App',
  components: {
    RouterView,
    BrandImageManager
  }
}
</script>
