/**
 * Utility functions for handling JSON parsing with special cases
 */

import { jsonrepair } from 'jsonrepair';

/**
 * Safely parses JSON that may contain newlines or other special characters
 * that could cause standard JSON.parse to fail
 *
 * @param jsonString The JSON string to parse
 * @returns The parsed JSON object
 */
export function safeParseJson<T>(jsonString: string): T {
  try {
    // First attempt: Try direct parsing
    return JSON.parse(jsonString) as T;
  } catch (error) {
    console.log("Direct JSON parsing failed, attempting repair:", error);

    try {
      // Second attempt: Try with jsonrepair
      const repairedJson = jsonrepair(jsonString);
      return JSON.parse(repairedJson) as T;
    } catch (repairError) {
      console.log("JSON repair failed:", repairError);

      try {
        // Third attempt: Handle newlines in string values
        // This specifically targets the issue with newlines in briefText
        const preprocessedJson = preprocessJsonWithNewlines(jsonString);
        return JSON.parse(preprocessedJson) as T;
      } catch (preprocessError) {
        console.log("Preprocessing <PERSON>SO<PERSON> failed:", preprocessError);

        try {
          // Fourth attempt: Fix unescaped newlines in JSON strings
          console.log("Attempting to fix unescaped newlines...");
          const fixedJson = fixUnescapedNewlines(jsonString);
          return JSON.parse(fixedJson) as T;
        } catch (fixError) {
          console.log("Fixing unescaped newlines failed:", fixError);

          try {
            // Fifth attempt: Combine approaches
            console.log("Attempting combined approach...");
            // Try fixing unescaped newlines first
            const fixedJson = fixUnescapedNewlines(jsonString);
            // Then try jsonrepair
            const repairedJson = jsonrepair(fixedJson);
            return JSON.parse(repairedJson) as T;
          } catch (combinedError) {
            console.log("Combined approach failed:", combinedError);

            try {
              // Sixth attempt: Another combination
              const fixedJson = fixUnescapedNewlines(jsonString);
              const preprocessedJson = preprocessJsonWithNewlines(fixedJson);
              return JSON.parse(preprocessedJson) as T;
            } catch (finalError) {
              console.log("Sixth attempt failed:", finalError);

              try {
                // Seventh attempt: Handle raw JSON with unescaped newlines
                console.log("Attempting to handle raw JSON with newlines...");
                const handledJson = handleRawJsonWithNewlines(jsonString);
                return JSON.parse(handledJson) as T;
              } catch (rawError) {
                console.error("All JSON parsing attempts failed:", rawError);
                throw new Error("Failed to parse JSON after multiple attempts");
              }
            }
          }
        }
      }
    }
  }
}

/**
 * Preprocesses JSON string to handle newlines in string values
 * This is particularly useful for fields like briefText that may contain
 * multiple paragraphs with newlines
 *
 * @param jsonString The JSON string to preprocess
 * @returns A preprocessed JSON string with escaped newlines
 */
function preprocessJsonWithNewlines(jsonString: string): string {
  // First, check if we're dealing with a valid JSON structure
  if (!jsonString.trim().startsWith('{') || !jsonString.trim().endsWith('}')) {
    throw new Error("Input is not a valid JSON object");
  }

  // Use a regex-based approach to handle newlines in string values
  // This regex matches JSON string values and preserves newlines within them
  return jsonString.replace(/("(?:[^"\\]|\\.)*")|[\n\r]+/g, (match, stringLiteral) => {
    // If this is a string literal, preserve it exactly (including any newlines)
    if (stringLiteral) {
      return stringLiteral;
    }
    // Otherwise, it's a newline outside a string literal, replace with a space
    return ' ';
  });
}

/**
 * Fixes unescaped newlines in JSON strings
 * This function handles the case where the JSON string contains actual newlines
 * inside string values that aren't properly escaped
 *
 * @param jsonString The JSON string to fix
 * @returns A JSON string with properly escaped newlines
 */
function fixUnescapedNewlines(jsonString: string): string {
  // First, we'll try to identify string values and escape any newlines within them
  let inString = false;
  let result = '';
  let escaped = false;

  for (let i = 0; i < jsonString.length; i++) {
    const char = jsonString[i];

    if (escaped) {
      // This character is escaped, add it as-is
      result += char;
      escaped = false;
      continue;
    }

    if (char === '\\') {
      // This is an escape character
      result += char;
      escaped = true;
      continue;
    }

    if (char === '"' && !escaped) {
      // This is an unescaped quote, toggle string mode
      inString = !inString;
      result += char;
      continue;
    }

    if (inString && (char === '\n' || char === '\r')) {
      // This is an unescaped newline inside a string, escape it
      result += '\\n';
      continue;
    }

    // For all other characters, add them as-is
    result += char;
  }

  return result;
}

/**
 * Handles raw JSON with unescaped newlines by extracting and properly formatting
 * the JSON object properties
 *
 * This is a more aggressive approach for handling malformed JSON with unescaped newlines
 * It attempts to extract the key-value pairs directly and rebuild a valid JSON object
 *
 * @param jsonString The raw JSON string with unescaped newlines
 * @returns A properly formatted JSON string
 */
function handleRawJsonWithNewlines(jsonString: string): string {
  // First, check if this looks like a JSON object
  if (!jsonString.trim().startsWith('{') || !jsonString.trim().endsWith('}')) {
    throw new Error("Input is not a valid JSON object");
  }

  try {
    // For the specific case of a Brief object, we can try a more direct approach
    // Extract the three key fields we expect: subjectLine, previewText, and briefText
    const subjectLineMatch = jsonString.match(/"subjectLine"\s*:\s*"([^"]*)"/i);
    const previewTextMatch = jsonString.match(/"previewText"\s*:\s*"([^"]*)"/i);

    // For briefText, we need to be more careful since it can contain newlines
    // Find the start of the briefText field
    const briefTextStart = jsonString.indexOf('"briefText"');
    if (briefTextStart === -1) {
      throw new Error("Could not find briefText field");
    }

    // Find the start of the briefText value (after the colon and opening quote)
    const valueStart = jsonString.indexOf('"', jsonString.indexOf(':', briefTextStart) + 1);
    if (valueStart === -1) {
      throw new Error("Could not find start of briefText value");
    }

    // Find the end of the briefText value (the closing quote before the end of the object or next field)
    let valueEnd = -1;
    let inString = true;
    let escaped = false;

    for (let i = valueStart + 1; i < jsonString.length; i++) {
      const char = jsonString[i];

      if (escaped) {
        escaped = false;
        continue;
      }

      if (char === '\\') {
        escaped = true;
        continue;
      }

      if (char === '"' && !escaped) {
        valueEnd = i;
        break;
      }
    }

    if (valueEnd === -1) {
      // If we couldn't find the end quote, try a different approach
      // Look for the next field or the end of the object
      const nextField = jsonString.indexOf('",', valueStart);
      const endObject = jsonString.indexOf('"}', valueStart);

      if (nextField !== -1) {
        valueEnd = nextField;
      } else if (endObject !== -1) {
        valueEnd = endObject;
      } else {
        throw new Error("Could not find end of briefText value");
      }
    }

    // Extract the briefText value (without the quotes)
    const briefText = jsonString.substring(valueStart + 1, valueEnd);

    // Clean up the briefText by replacing literal newlines with \n
    const cleanedBriefText = briefText.replace(/[\n\r]+/g, '\n');

    // Create a new object with the extracted values
    const extractedObject = {
      subjectLine: subjectLineMatch ? subjectLineMatch[1] : "Default Subject",
      previewText: previewTextMatch ? previewTextMatch[1] : "Default Preview",
      briefText: cleanedBriefText
    };

    // Convert the object back to a JSON string
    return JSON.stringify(extractedObject);
  } catch (specificError) {
    console.log("Specific Brief extraction failed:", specificError);

    try {
      // Fall back to a more general approach
      // Try to extract the key-value pairs using regex
      // This regex looks for "key": and then captures everything until the next key or end of object
      const keyValueRegex = /"([^"]+)"\s*:\s*([^,}]+)(?=[,}])/g;
      const matches = Array.from(jsonString.matchAll(keyValueRegex));

      if (matches.length === 0) {
        throw new Error("Could not extract key-value pairs from JSON");
      }

      // Build a new object with the extracted key-value pairs
      const extractedObject: Record<string, any> = {};

      for (const match of matches) {
        const key = match[1];
        let value = match[2].trim();

        // Handle string values (they should start and end with quotes)
        if (value.startsWith('"')) {
          // Find the matching end quote for this string value
          // This is tricky because the string might contain escaped quotes
          const valueStart = jsonString.indexOf(value);
          let valueEnd = valueStart;
          let inString = true;
          let escaped = false;

          for (let i = valueStart + 1; i < jsonString.length; i++) {
            const char = jsonString[i];

            if (escaped) {
              escaped = false;
              valueEnd = i;
              continue;
            }

            if (char === '\\') {
              escaped = true;
              valueEnd = i;
              continue;
            }

            if (char === '"' && !escaped) {
              inString = false;
              valueEnd = i;
              break;
            }

            valueEnd = i;
          }

          // Extract the full string value including quotes
          value = jsonString.substring(valueStart, valueEnd + 1);

          // Replace newlines with \n in the string value
          value = value.replace(/[\n\r]+/g, '\\n');
        }

        // Add the key-value pair to our object
        try {
          // Try to parse the value (it might be a number, boolean, etc.)
          extractedObject[key] = JSON.parse(value);
        } catch (e) {
          // If parsing fails, use the value as is
          extractedObject[key] = value;
        }
      }

      // Convert the object back to a JSON string
      return JSON.stringify(extractedObject);
    } catch (error) {
      console.error("Failed to extract JSON properties:", error);
      throw error;
    }
  }
}

/**
 * Validates that a parsed brief object has the expected structure
 *
 * @param brief The brief object to validate
 * @returns A valid brief object, with default values for missing fields
 */
export function validateBrief(brief: any): { subjectLine: string; previewText: string; briefText: string } {
  if (!brief) {
    return {
      subjectLine: 'Default Subject',
      previewText: 'Default Preview',
      briefText: 'Default Brief Content'
    };
  }

  return {
    subjectLine: typeof brief.subjectLine === 'string' ? brief.subjectLine : 'Default Subject',
    previewText: typeof brief.previewText === 'string' ? brief.previewText : 'Default Preview',
    briefText: typeof brief.briefText === 'string' ? brief.briefText : 'Default Brief Content'
  };
}
