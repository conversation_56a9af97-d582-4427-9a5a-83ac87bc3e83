export const URL_DOMAIN = `${window.location.origin.includes('3030') ? 'http://localhost:3000' : window.location.origin}/api/v1`;

export const GOOGLE_CLIENT_ID = window.location.origin.includes('localhost') || window.location.origin.includes('3030') || window.location.origin.includes('dev.raleon.io')
  ? '606701202896-gu4rr25m8907orue6kiuls1qp2lqah2b.apps.googleusercontent.com' // Add your dev Google Client ID here
  : '606701202896-votutn1d07dt697u0ndf2ln3bdraed9e.apps.googleusercontent.com'; // Add your production Google Client ID here

export const REVENUE_RANGES = [
  { value: 0, revenueUsdMin: 0, revenueUsdMax: 250000, label: '$0 - $250K' },
  { value: 250000, revenueUsdMin: 250000, revenueUsdMax: 500000, label: '$250K - $500K' },
  { value: 500000, revenueUsdMin: 500000, revenueUsdMax: 1000000, label: '$500K - $1M' },
  { value: 1000000, revenueUsdMin: 1000000, revenueUsdMax: 2500000, label: '$1M - $2.5M' },
  { value: 2500000, revenueUsdMin: 2500000, revenueUsdMax: 5000000, label: '$2.5M - $5M' },
  { value: 5000000, revenueUsdMin: 5000000, revenueUsdMax: 7500000, label: '$5M - $7.5M' },
  { value: 7500000, revenueUsdMin: 7500000, revenueUsdMax: 10000000, label: '$7.5M - $10M' },
  { value: 10000000, revenueUsdMin: 10000000, revenueUsdMax: 15000000, label: '$10M - $15M' },
  { value: 15000000, revenueUsdMin: 15000000, revenueUsdMax: 100000000, label: '$15M+' },
];

export const EARN_CONDITION_TYPES = {
	DOLLAR_SPENT: 'dollar-spent',
	SHOPIFY_SEGMENT: 'shopify-segment',
	NTH_PURCHASE: 'nth-purchase',
	FIRST_LOYALTY_PURCHASE: 'first-loyalty-purchase',
	BUY_PRODUCT: 'buy-product'
}

export const EARN_EFFECT_TYPES = {
	POINTS_PER_DOLLAR: 'points', //May want this to be different in the future
	POINTS: 'points',
	DOLLAR_OFF_COUPON: 'dollars-off-coupon',
	PERCENT_OFF_COUPON: 'percent-discount',
}

export const EARN_CONDITION_VARIABLES = {
	TOTAL_PRICE: 'total-price',
}

export const EARN_CONDITION_SCHEMA = {
	DOLLAR_SPENT: {
		type: 'dollar-spent',
		label: 'Each $ Spent',
		subtitle: 'Converts all purchases to points.',
		chainable: false,
		conditionTypes: [{
			fake: true,
			variables: [{
				label: null,
				value: 'purchaseTotalIncrease',
				noInput: true,
				amount: 1,

				operators: [{
					label: 'No conditions, as this way to earn applies to all purchases.',
					value: '='
				}]
			}]
		}, {
			fake: true,
			variables: [{
				label: null,
				value: null,
				noInput: true,
				amount: null,

				operators: [{
					label: 'Click SETUP REWARDS To Continue',
					value: null
				}]
			}]
		}],
		imageSlotKey: 'test-image'
	},
	NTH_PURCHASE: {
		type: 'nth-purchase',
		label: 'NTH Purchase',
		chainable: true,
		subtitle: 'Customer Makes their [X] Purchase Within the last Y days/months',
		conditionTypes: [{
			variables: [{
				label: 'Purchases',
				value: 'purchaseCount',

				operators: [{
					label: 'Custormer Makes At Least',
					value: '>='
				}],
			}],

		},
		 {
			optional: true,
			variables: [{
				label: 'Days',
				value: 'purchaseDateAgo',
				operators: [{
					label: 'Within The Last',
					value: '<='
				}]
			}],

		}],
		imageSlotKey: 'test-image'
	}
}

export const REWARD_SCHEMAS = {
	POINTS_PER_DOLLAR: {
		label: 'Points Per <currencyPrefix>1<currencyPostfix> Spent',
		value: 'points',
		collapsedTitle: 'Points',
		subtitle: '# of Points',
		imageSlotKey: 'test-image',
		showPointsEquivalency: true,
		restrictionTypes: []
	},
	POINTS: {
		label: 'Points',
		value: 'points',
		collapsedTitle: 'Points',
		subtitle: '# of Points',
		imageSlotKey: 'test-image',
		showPointsEquivalency: true,
		restrictionTypes: [],
		limitTypes: [{
			value: 'maxUserRedemptions',
			label: 'Redeems per User'
		}, {
			value: 'maxUserGrants',
			label: 'Max Amount That Can Be Earned',
		}]
	},
	DOLLAR_OFF_COUPON: {
		label: 'Dollars Off Coupon',
		value: 'dollars-off-coupon',
		collapsedTitle: 'Dollars Off',
		subtitle: 'Dollars off',
		imageSlotKey: 'test-image',
		restrictionTypes: [{
			value: 'minimumOrderAmount',
			label: 'Minimum Order Amount'
		}, {
			value: 'expiresInDays',
			label: 'Expires In (Days)'
		}],
		limitTypes: [{
			value: 'maxUserRedemptions',
			label: 'Redeems per User'
		}, {
			value: 'maxUserGrants',
			label: 'Max Amount That Can Be Earned',
		}]
	},
	PERCENT_OFF_COUPON: {
		label: '% Discount',
		value: 'percent-discount',
		collapsedTitle: '% Discount',
		subtitle: 'Percent off',
		imageSlotKey: 'test-image',
		restrictionTypes: [{
			value: 'minimumOrderAmount',
			label: 'Minimum Order Amount'
		}, {
			value: 'expiresInDays',
			label: 'Expires In (Days)'
		}, {
			value: 'maximumDiscount',
			label: 'Maximum Discount'
		}],
		limitTypes: [{
			value: 'maxUserRedemptions',
			label: 'Redeems per User'
		}, {
			value: 'maxUserGrants',
			label: 'Max Amount That Can Be Earned',
		}]
	},
	FREE_SHIPPING_COUPON: {
		label: 'Free Shipping',
		value: 'free-shipping',
		collapsedTitle: 'Free Shipping',
		subtitle: 'Free Shipping',
		imageSlotKey: 'free-shipping-reward',
		restrictionTypes: [{
			value: 'minimumOrderAmount',
			label: 'Minimum Order Amount'
		}],
		limitTypes: [{
			value: 'maxUserRedemptions',
			label: 'Redeems per User'
		}, {
			value: 'maxUserGrants',
			label: 'Max Amount That Can Be Earned',
		}]
	}

}
