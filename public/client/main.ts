import { createApp } from 'vue';
import App from './App.ts.vue';
import router from './router';
import { createPinia } from 'pinia';
import HighchartsVue from "highcharts-vue";

import './css/style.scss';
import {useSessionStore} from './pages/useSessionStore.ts';


const app = createApp(App)
	.use(createPinia())
	.use(router)
	.use(HighchartsVue)
	.mount('#app');

const sessionStore = useSessionStore();
sessionStore.checkSession();


