<template>
	<StatusMessage :message=status.message :status=status.type @resetStatus="status.type = 'nope'"></StatusMessage>

	<div class="mt-3 ml-3 mr-3 sm:mt-7 sm:ml-7 sm:mr-7 mb-2">
		<div class="text-neutral-800 text-opacity-70 sm:text-4xl md:text-6xl lg:text-7xl font-normal font-['Open Sans'] ">Notifications Branding
		</div>
	</div>
	<div class="inline-flex items-center cursor-pointer ml-3 sm:ml-7" @click="this.$router.push('/loyalty/onsite');">
		<svg width="24" height="24" viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg"
			class="hover:text-ralprimary-dark transition-color duration-300">
			<path
				d="M15.3333 12L10.3333 17M10.3333 17L15.3333 22M10.3333 17H23.6667M32 17C32 8.71573 25.2843 2 17 2C8.71573 2 2 8.71573 2 17C2 25.2843 8.71573 32 17 32C25.2843 32 32 25.2843 32 17Z"
				stroke="#202020" stroke-opacity="0.8" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" />
		</svg>
		<span class="ml-3 text-lg transition-color duration-300">Loyalty Branding</span>
	</div>
	<div class="ml-3 sm:ml-7 flex mb-2 mt-2">
		<LearnMoreText text="Learn more" url="https://docs.raleon.io/docs/the-loyalty-panel"></LearnMoreText>
		<span class="text-neutral-500 text-sm font-medium font-['Inter']">&nbsp;</span>
		<span class="text-neutral-800 text-opacity-80 text-sm font-medium font-['Inter']">about branding your Loyalty
			Panel.</span>
	</div>


	<Tabswitcher
		v-if="!isLargeScreen"
		:tabs="getTabs"
		class="lg:hidden"
		@fileUploaded="handleFileUploaded"
		@fileUploading="handleFileUploading" />


	<div class="hidden lg:flex">
		<div class="pl-7 w-[calc(100%-32em)]">

			<LoyaltyBrandingNotificationSetup
				:branding="branding"
				:is-logo-uploading="isLogoUploading"
				:preview-type="previewType"
				:launcherActive="launcherActive"
				@fileUploading="handleFileUploading"
				@fileUploaded="handleFileUploaded"
				@switchPreview="switchPreview"
				@brandingChanged="handleBrandingChanged"
				:is-hero-image-uploading="isHeroImageUploading" />
		</div>
		<div v-if="isLargeScreen" class="px-7 right-1">
			<LoyaltyBrandingLivePreview
				:branding="branding"
				preview-type="launcher"
				:set-is-logged-in="true"
				:launcherActive="true"
				:relative-notifications="true"
				@saveStatus="setStatus"
				@previewTypeChanged="switchPreview" />
		</div>
	</div>

	<ModalBlank id="info-modal" :modalOpen="aiModelOpen" @close-modal="aiModelOpen = false">
		<div class="p-5 flex flex-col space-x-4 justify-center">
			<div class="flex justify-center">
				<RaleonLoader />
			</div>
			<div>

			<div class="flex justify-center mb-2 mt-10">
				<div class="text-lg font-semibold text-ralblack-primary flex items-center">
					<svg width="24" height="26" viewBox="0 0 24 26" fill="none" xmlns="http://www.w3.org/2000/svg" class="mr-2" v-if="aiTextIndex == 0">
						<path
							d="M8.14838 4.24398L8.31532 4.32745C8.99 4.67522 9.53949 5.22471 9.88726 5.89939L9.97073 6.06632C10.3046 6.7271 11.2505 6.7271 11.5914 6.06632L11.6748 5.89939C12.0226 5.22471 12.5721 4.67522 13.2468 4.32745L13.4137 4.24398C14.0745 3.91011 14.0745 2.96416 13.4137 2.62334L13.2468 2.53988C12.5721 2.1921 12.0226 1.64262 11.6748 0.967932L11.5914 0.801C11.2575 0.140226 10.3115 0.140226 9.97073 0.801L9.88726 0.967932C9.53949 1.64262 8.99 2.1921 8.31532 2.53988L8.14838 2.62334C7.48761 2.95721 7.48761 3.90316 8.14838 4.24398Z"
							fill="#E86AD6" />
						<path
							d="M9.46298 10.7613C10.1446 10.4135 10.1446 9.4467 9.46298 9.09893L8.44747 8.57726C7.54325 8.11124 6.80597 7.37396 6.33995 6.46974L5.81829 5.45424C5.47051 4.7726 4.5037 4.7726 4.15592 5.45424L3.63426 6.46974C3.16824 7.37396 2.43095 8.11124 1.52673 8.57726L0.51123 9.09893C-0.17041 9.4467 -0.17041 10.4135 0.51123 10.7613L1.52673 11.283C2.43095 11.749 3.16824 12.4863 3.63426 13.3905L4.15592 14.406C4.5037 15.0876 5.47051 15.0876 5.81829 14.406L6.33995 13.3905C6.80597 12.4863 7.54325 11.749 8.44747 11.283L9.46298 10.7613Z"
							fill="#E86AD6" />
						<path
							d="M22.5115 14.9207L21.4821 14.3921C20.2371 13.7591 19.2424 12.7645 18.6095 11.5194L18.0809 10.49C17.6148 9.5719 16.6828 9.00155 15.6534 9.00155C14.624 9.00155 13.6919 9.5719 13.2259 10.49L12.6973 11.5194C12.0643 12.7645 11.0697 13.7591 9.82466 14.3921L8.79525 14.9207C7.87712 15.3867 7.30677 16.3188 7.30677 17.3482C7.30677 18.3776 7.87712 19.3096 8.79525 19.7756L9.82466 20.3043C11.0697 20.9372 12.0643 21.9319 12.6973 23.1769L13.2259 24.2063C13.6919 25.1244 14.624 25.6948 15.6534 25.6948C16.6828 25.6948 17.6148 25.1244 18.0809 24.2063L18.6095 23.1769C19.2424 21.9319 20.2371 20.9372 21.4821 20.3043L22.5115 19.7756C23.4296 19.3096 24 18.3776 24 17.3482C24 16.3188 23.4296 15.3867 22.5115 14.9207ZM21.5656 17.9185L20.5362 18.4471C18.8947 19.2818 17.587 20.5894 16.7524 22.2309L16.2237 23.2604C16.0638 23.5734 15.7716 23.6081 15.6534 23.6081C15.5351 23.6081 15.243 23.5734 15.083 23.2604L14.5544 22.2309C13.7197 20.5894 12.4121 19.2818 10.7706 18.4471L9.7412 17.9185C9.4282 17.7585 9.39342 17.4664 9.39342 17.3482C9.39342 17.2299 9.4282 16.9378 9.7412 16.7778L10.7706 16.2492C12.4121 15.4145 13.7197 14.1069 14.5544 12.4654L15.083 11.436C15.243 11.123 15.5351 11.0882 15.6534 11.0882C15.7716 11.0882 16.0638 11.123 16.2237 11.436L16.7524 12.4654C17.587 14.1069 18.8947 15.4145 20.5362 16.2492L21.5656 16.7778C21.8786 16.9378 21.9133 17.2299 21.9133 17.3482C21.9133 17.4664 21.8786 17.7585 21.5656 17.9185Z"
							fill="#E86AD6" />
					</svg>
					<TextFadeEffect :textList="aiLoadText" @text-complete="aiModelOpen = false" @text-index="aiTextIndex = $event" speed="1000"></TextFadeEffect>
				</div>
			</div>
			</div>
		</div>
	</ModalBlank>
</template>

<script>
import * as Utils from '../../client-old/utils/Utils';
import Tabswitcher from '../components/TabSwitcher.ts.vue';
import StatusMessage from '../components/StatusMessage.ts.vue';
import LearnMoreText from '../components/LearnMoreText.ts.vue';
// import LoyaltyBrandingReferralProgramSetup from './subpages/LoyaltyBrandingReferralProgramSetup.ts.vue';
import LoyaltyBrandingNotificationSetup from './subpages/LoyaltyBrandingNotificationSetup.ts.vue';
import LoyaltyBrandingLivePreview from './subpages/LoyaltyBrandingLivePreview.ts.vue';
import ModalBlank from '../components/ModalBlank.vue';
import TextFadeEffect from '../components/TextFadeEffect.ts.vue';
import RaleonLoader from '../components/RaleonLoader.ts.vue';
import * as OrganizationSettings from '../services/organization-settings.js'
import { customerIOTrackEvent } from '../services/customerio.js';

import { DEFAULT_BRANDING } from './LoyaltyBranding.ts.vue';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	props: [],
	data() {
		return {
			status: {},
			brandingDefault: { ...DEFAULT_BRANDING },
			branding: { ...DEFAULT_BRANDING },
			brandingChanged: false,
			previewType: 'landing-page-member',
			isLogoUploading: false,
			isHeroImageUploading: false,
			isLargeScreen: false,
			tabs: [{
				name: 'SETUP',
				component: LoyaltyBrandingNotificationSetup,
				props: this.tabSetupProps
			}, {
				name: 'LIVE PREVIEW',
				component: LoyaltyBrandingLivePreview,
				props: this.tabLivePreviewProps,
			},],
			aiLoadText: ['Copilot is learning your brand...', 'Applying your colors...', 'Finished your first draft!'],
			aiModelOpen: false,
			aiTextIndex: 0,
			launcherActive: true,
		}
	},
	watch: {
		branding: {
			handler: function (val, oldVal) {
				this.brandingChanged = true;
			},
			deep: true
		},
		status: {
			handler: function (val, oldVal) {
				if (val.type === 'success' || val.type === 'nope') {
					this.brandingChanged = false;
				}
			},
			deep: true
		}
	},
	components: {
		Tabswitcher,
		LoyaltyBrandingLivePreview,
		LoyaltyBrandingNotificationSetup,
		StatusMessage,
		LearnMoreText,
		TextFadeEffect,
		ModalBlank,
		RaleonLoader,
		OrganizationSettings
	},
	created() {
		this.isLargeScreen = window.innerWidth >= 1024;
		window.addEventListener('resize', this.resizeHandler);
		window.addEventListener('beforeunload', this.beforeUnloadHandler);
	},
	beforeDestroy() {
		window.removeEventListener('resize', this.resizeHandler);
		window.removeEventListener('beforeunload', this.beforeUnloadHandler);
	},
	beforeRouteLeave(to, from, next) {
		if (this.brandingChanged || this.status?.type == 'fail') {
			const answer = confirm('You have unsaved changes. Are you sure you want to leave?');
			answer ? next() : next(false);
		} else {
			next();
		}
	},
	async mounted() {
		customerIOTrackEvent('Notification Branding Viewed');
		let firstBrandingView = await OrganizationSettings.getOrganizationSetting('firstBrandingView');

		if(firstBrandingView == 'false' || firstBrandingView == null) {
			let res1 = await OrganizationSettings.updateOrganizationSetting('firstBrandingView', 'true');
			this.aiModelOpen = true;
		}

		await this.isLauncherActive();

		try {
			await this.fetchBranding();
			this.$nextTick(() => {
				this.brandingChanged = false;
			});
		} catch (e) {
			console.log(e);
		}
	},
	computed: {
		tabSetupProps() {
			return {
				branding: this.branding,
				previewType: this.previewType,
				isLogoUploading: this.isLogoUploading,
				isHeroImageUploading: this.isHeroImageUploading,
				launcherActive: this.launcherActive
			};
		},
		tabLivePreviewProps() {
			return {
				branding: this.branding,
				previewType: 'launcher',
				setIsLoggedIn: true,
				relativeNotifications: true,
				launcherActive: true,
				focusReferral: true
			};
		},
		getTabs() {
			return [{
				name: 'SETUP',
				component: LoyaltyBrandingNotificationSetup,
				props: this.tabSetupProps,
				eventListeners: {
					fileUploaded: this.handleFileUploaded,
					fileUploading: this.handleFileUploading,
					switchPreview: this.handlePreviewTypeChanged,
					brandingChanged: this.handleBrandingChanged
				}
			}, {
				name: 'LIVE PREVIEW',
				component: LoyaltyBrandingLivePreview,
				props: this.tabLivePreviewProps,
				eventListeners: {
					previewTypeChanged: this.handlePreviewTypeChanged,
				}
			},]
		}
	},
	methods: {
		async isLauncherActive() {
			const response = await fetch(`${URL_DOMAIN}/loyalty-programs`, {
				method: 'GET',
				withCreditentials: true,
				credentials: 'omit',
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Access-Control-Allow-Origin': '*',
					'Content-Type': 'application/json'
				}
			});
			let jsonresponse = await response.json();
			let launcherActive = jsonresponse[0]?.launcherActive;
			if(launcherActive === null) {
				launcherActive = true;
			}
			this.launcherActive = launcherActive;
			console.log('launcherActive', this.launcherActive);
			return launcherActive;
		},
		setStatus(data) {
			this.status.type = data.type;
			this.status.message = data.message;
		},
		handlePreviewTypeChanged(e) {
			this.previewType = e;
		},
		handleBrandingChanged(e) {
			this.branding = e;
		},
		handleFileUploading(file) {
			console.log('File uploading:', file);
			switch (file.type) {
				case "hero-image":
					this.isHeroImageUploading = true;
					break;
				case "logo":
					this.isLogoUploading = true;
					break;
			}
		},
		handleFileUploaded(file) {
			console.log('File uploaded:', file);
			switch (file.type) {
				case "hero-image":
					this.branding.guest.heroImageUrl = file.url;
					this.isHeroImageUploading = false;
					break;
				case "logo":
					this.branding.logoUrl = file.url;
					this.isLogoUploading = false;
					break;
			}
		},
		switchPreview(key) {
			this.previewType = key;
		},
		async fetchBranding() {
			customerIOTrackEvent('Viewed Branding');
			const response = await fetch(`${URL_DOMAIN}/branding`, {
				method: 'GET',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
					'ngrok-skip-browser-warning': true,
				}
			});

			if (response.ok && response.status >= 200 && response.status < 300) {
				const result = await response.json();

				this.branding = result;
				console.log('BRANDING RESULT', this.branding)

				//fill out any missing data with defaults
				if(this.branding && this.branding.launcher && !this.branding.launcher.launcherPosition) {
					this.branding.launcher.launcherPosition = 'left';
				}
				if(this.branding && this.branding.launcher && !this.branding.launcher.size) {
					this.branding.launcher.size = 'small';
				}
				if(this.branding && this.branding.launcher && !this.branding.launcher.radius) {
					this.branding.launcher.radius = '10px';
				}
				if(this.branding && !this.branding.header) {
					this.branding.header = this.branding.launcher?.callToAction;
				}

				this.branding.notifications = this.branding.notifications || {
					...DEFAULT_BRANDING.notifications
				};
				this.branding.notifications.styling = this.branding.notifications.styling || {
					...DEFAULT_BRANDING.notifications.styling
				};

				this.$forceUpdate();
			}

			if (!this.branding) {
				console.log("No Branding updating to Default");
				this.branding = {...this.brandingDefault}
				this.$nextTick();
				this.brandingChanged = false;
			}
		},
		resizeHandler() {
			if (window.innerWidth < 1024) {
				this.isLargeScreen = false;
				const temp = this.previewType;
				this.previewType = null;
				this.handlePreviewTypeChanged(temp);
			} else {
				this.isLargeScreen = true;
				const temp = this.previewType;
				this.previewType = null;
				this.handlePreviewTypeChanged(temp);
			}
		},
		beforeUnloadHandler(event) {
			if (this.brandingChanged || this.status?.type == 'fail') {
				event.preventDefault();
				event.returnValue = '';
				return '';
			}
		},
	}
}
</script>

<style>

.w-full.sticky {
	position: sticky;
	top: 0;
	height: calc(100vh - 140px);
}

</style>
