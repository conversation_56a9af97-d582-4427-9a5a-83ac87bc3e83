<template>

	<ProgramActive></ProgramActive>

	<div class="m-3 sm:m-10">
		<!-- <div class="mb-10" v-if="isOnboarding"> -->
		<div class="mb-10">
			<h2 class="mx-auto text-center text-2xl sm:text-4xl mt-20 mb-4 font-sans font-medium opacity-70 text-ralblack-primary">Your Get Started List</h2>

			<div class="mx-auto sm:w-[960px] bg-white rounded-2xl border border-violet-300 bg-opacity-75 overflow-hidden shadow-md mb-2 p-4">
				<p class="text-ralblack-primary text-lg font-medium font-['Inter'] mb-4">
					Welcome, {{ userName }}!
				</p>

				<div class="card bg-white rounded-2xl overflow-hidden border border-ralgray-light mb-4" v-for="task in onboardingTasks">
					<div class="px-5 pt-5 overflow-hidden transition-all duration-300"
					:class="{ 'h-[33rem] sm:h-96': openTaskId === task.id, 'h-[6rem] sm:h-[4.5rem]': openTaskId !== task.id }">
						<div class="flex items-center justify-between cursor-pointer"  @click.stop="checklistToggle(task.id)">
							<div class="flex items-center">
								<!-- Task todo -->
								<svg width="32" height="32" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg" v-if="task.state?.state?.includes('tarted') || task.state?.state?.includes('omplete') || !task.state || !task.state.state">
								<path fill-rule="evenodd" clip-rule="evenodd" d="M29.326 5.55831C25.7805 3.97851 21.8193 3.58714 18.0331 4.44257C14.247 5.29799 10.8388 7.35438 8.31691 10.305C5.79498 13.2557 4.29442 16.9425 4.03902 20.8157C3.78363 24.6888 4.78708 28.5408 6.89973 31.797C9.01238 35.0533 12.121 37.5394 15.762 38.8846C19.4031 40.2297 23.3814 40.3619 27.1036 39.2613C30.8259 38.1608 34.0927 35.8865 36.4168 32.7776C38.7409 29.6687 39.9978 25.8919 40 22.0103V20.1714C40 19.0669 40.8954 18.1714 42 18.1714C43.1046 18.1714 44 19.0669 44 20.1714V22.0114C43.9973 26.7556 42.4611 31.3729 39.6205 35.1726C36.78 38.9723 32.7872 41.752 28.2378 43.0972C23.6883 44.4423 18.826 44.2808 14.3758 42.6367C9.9257 40.9926 6.12624 37.954 3.54411 33.9741C0.961986 29.9943 -0.264459 25.2863 0.0476889 20.5525C0.359837 15.8186 2.19386 11.3125 5.27622 7.70615C8.35858 4.0998 12.5241 1.58643 17.1516 0.540914C21.7791 -0.504606 26.6206 -0.0262665 30.954 1.90459C31.963 2.35415 32.4164 3.5365 31.9669 4.54545C31.5173 5.55439 30.335 6.00787 29.326 5.55831ZM43.4135 4.59653C44.195 5.37719 44.1956 6.64352 43.4149 7.42496L23.4149 27.445C23.0399 27.8203 22.5311 28.0313 22.0005 28.0315C21.4699 28.0316 20.961 27.8209 20.5858 27.4457L14.5858 21.4457C13.8048 20.6646 13.8048 19.3983 14.5858 18.6172C15.3668 17.8362 16.6332 17.8362 17.4142 18.6172L21.9993 23.2023L40.5851 4.59794C41.3657 3.8165 42.6321 3.81587 43.4135 4.59653Z" fill="#958F9D"/>
								</svg>

								<!-- Task complete -->
								<svg width="32" height="32" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg" v-if="task.state?.state?.includes('erified')">
									<path fill-rule="evenodd" clip-rule="evenodd" d="M31.326 7.54683C27.7805 5.96704 23.8193 5.57567 20.0331 6.43109C16.247 7.28652 12.8388 9.34291 10.3169 12.2936C7.79498 15.2442 6.29442 18.931 6.03902 22.8042C5.78363 26.6773 6.78708 30.5293 8.89973 33.7856C11.0124 37.0418 14.121 39.5279 17.762 40.8731C21.4031 42.2183 25.3814 42.3504 29.1036 41.2499C32.8259 40.1493 36.0927 37.875 38.4168 34.7661C40.7409 31.6572 41.9978 27.8804 42 23.9988V22.16C42 21.0554 42.8954 20.16 44 20.16C45.1046 20.16 46 21.0554 46 22.16V24C45.9973 28.7441 44.4611 33.3614 41.6205 37.1611C38.78 40.9609 34.7872 43.7406 30.2378 45.0857C25.6883 46.4308 20.826 46.2693 16.3758 44.6252C11.9257 42.9811 8.12624 39.9425 5.54411 35.9627C2.96199 31.9828 1.73554 27.2749 2.04769 22.541C2.35984 17.8072 4.19386 13.301 7.27622 9.69468C10.3586 6.08832 14.5241 3.57496 19.1516 2.52944C23.7791 1.48392 28.6206 1.96226 32.954 3.89312C33.963 4.34268 34.4164 5.52503 33.9669 6.53398C33.5173 7.54292 32.335 7.99639 31.326 7.54683ZM45.4135 6.58505C46.195 7.36571 46.1956 8.63204 45.4149 9.41348L25.4149 29.4335C25.0399 29.8089 24.5311 30.0198 24.0005 30.02C23.4699 30.0201 22.961 29.8094 22.5858 29.4342L16.5858 23.4342C15.8048 22.6531 15.8048 21.3868 16.5858 20.6058C17.3668 19.8247 18.6332 19.8247 19.4142 20.6058L23.9993 25.1908L42.5851 6.58647C43.3657 5.80503 44.6321 5.8044 45.4135 6.58505Z" fill="#15803D"/>
								</svg>

								<h2 v-if="!task.state?.state?.includes('tarted')" class="title ml-4 font-semibold text-xl text-black">{{ task.taskName }}</h2>
								<h2 v-if="task.state?.state?.includes('tarted')" class="title ml-4 font-semibold text-xl text-black">{{ task.taskPendingName || task.taskName }}</h2>
							</div>
							<svg class="transition-all duration-300" :class="{'rotate-90': openTaskId === task.id}" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M15.375 12L9.37498 18L7.97498 16.6L12.575 12L7.97498 7.4L9.37498 6L15.375 12Z" fill="#989898"></path></svg>
						</div>

						<div class="body p-5 flex justify-between items-center md:flex-row lg:flex-row flex-col opacity-0 transition-all duration-300"
						:class="{'opacity-0': openTaskId !== task.id, 'opacity-100': openTaskId === task.id}"
						>
							<div class="flex flex-col sm:flex-row">
								<div class="sm:w-1/2 sm:mr-4 mt-2">
									<p v-if="!task.state?.state?.includes('tarted')" class="text text-ralblack-secondary mb-4">{{ task.taskDescription }}</p>

									<div class="flex" v-if="task.state?.state?.includes('tarted')">
										<div class="inline-flex">
											<div class="w-12 h-12 min-w-12 min-h-12 border-4 border-dashed border-gray-400 rounded-full flex items-center justify-center animate-spin duration-slow" >
											</div>
										</div>
									<p class="text text-ralblack-secondary italic mb-4 ml-6">{{ task.taskPendingDescription || `We're still processing your ${task.type}` }}</p>
									</div>

									<LightSecondaryButton
										v-if="task.id != 5 && task.userAction && (task.state?.state?.includes('omplete') || !task.state || !task.state.state)"
										:cta="task.taskCta || 'Review ' + task.type"
										icon="true"
										class="cursor-pointer"
										@click="navigate(task.userAction)"
									></LightSecondaryButton>
									<PreviewLoyalty v-if="task.id == 5" previewStyle="button"/>
								</div>
								<div class="sm:w-1/2">
									<img src="../images/QuickBrandingVideo.png" v-if="task.id == 1" @click.stop="brandingModalOpen = true; videoNumber = 1" class="cursor-pointer mt-4"/>
									<img src="../images/QuickLoyaltyProgramVideo.png" v-if="task.id == 3" @click.stop="brandingModalOpen = true; videoNumber = 3" class="cursor-pointer mt-4" />
									<img src="../images/QuickLaunchReadyVideo.png" v-if="task.id == 4" @click.stop="brandingModalOpen = true; videoNumber = 4" class="cursor-pointer mt-4"/>
									<img src="../images/SitePreview.jpg" v-if="task.id == 5" class="mt-4"/>
								</div>
							</div>
						</div>
					</div>
				</div>

			</div>
		</div>

		<div class="mx-auto sm:w-[960px]">

			<div class="flex flex-col sm:flex-row">
				<div class="flex flex-col mt-4 sm:w-1/3">
					<div class="flex items-center">
						<svg xmlns="http://www.w3.org/2000/svg" class="opacity-75" height="46" viewBox="0 -960 960 960" width="46">
							<path d="M480-240q21 0 35.5-14.5T530-290q0-21-14.5-35.5T480-340q-21 0-35.5 14.5T430-290q0 21 14.5 35.5T480-240Zm-36-154h74q0-36 8-53t34-43q35-35 49.5-58.5T624-602q0-53-36-85.5T491-720q-55 0-93.5 27T344-618l66 26q7-27 28-43.5t49-16.5q27 0 45 14.5t18 38.5q0 17-11 36t-37 42q-33 29-45.5 55.5T444-394ZM200-120q-33 0-56.5-23.5T120-200v-560q0-33 23.5-56.5T200-840h560q33 0 56.5 23.5T840-760v560q0 33-23.5 56.5T760-120H200Zm0-80h560v-560H200v560Zm0-560v560-560Z"
								stroke="black"
								stroke-width="2"
								stroke-linecap="round"
								stroke-linejoin="round"
							/>
						</svg>
						<div class="ml-2 text-lg font-semibold font-[Inter]">
							Help Documentation
						</div>
					</div>
					<div class="font-[Inter] text-base mt-4">
						Explore our guides, videos, and examples.
					</div>
					<div class="flex items-center text-sm mt-2">
						<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path fill-rule="evenodd" clip-rule="evenodd"
							d="M3.75 5.25C3.55109 5.25 3.36032 5.32902 3.21967 5.46967C3.07902 5.61032 3 5.80109 3 6V14.25C3 14.4489 3.07902 14.6397 3.21967 14.7803C3.36032 14.921 3.55109 15 3.75 15H12C12.1989 15 12.3897 14.921 12.5303 14.7803C12.671 14.6397 12.75 14.4489 12.75 14.25V9.75C12.75 9.33579 13.0858 9 13.5 9C13.9142 9 14.25 9.33579 14.25 9.75V14.25C14.25 14.8467 14.0129 15.419 13.591 15.841C13.169 16.2629 12.5967 16.5 12 16.5H3.75C3.15326 16.5 2.58097 16.2629 2.15901 15.841C1.73705 15.419 1.5 14.8467 1.5 14.25V6C1.5 5.40326 1.73705 4.83097 2.15901 4.40901C2.58097 3.98705 3.15326 3.75 3.75 3.75H8.25C8.66421 3.75 9 4.08579 9 4.5C9 4.91421 8.66421 5.25 8.25 5.25H3.75Z"
							fill="#400F92" fill-opacity="0.8" />
						<path fill-rule="evenodd" clip-rule="evenodd"
							d="M10.5 2.25C10.5 1.83579 10.8358 1.5 11.25 1.5H15.75C16.1642 1.5 16.5 1.83579 16.5 2.25V6.75C16.5 7.16421 16.1642 7.5 15.75 7.5C15.3358 7.5 15 7.16421 15 6.75V3H11.25C10.8358 3 10.5 2.66421 10.5 2.25Z"
							fill="#400F92" fill-opacity="0.8" />
						<path fill-rule="evenodd" clip-rule="evenodd"
							d="M16.2803 1.71967C16.5732 2.01256 16.5732 2.48744 16.2803 2.78033L8.03033 11.0303C7.73744 11.3232 7.26256 11.3232 6.96967 11.0303C6.67678 10.7374 6.67678 10.2626 6.96967 9.96967L15.2197 1.71967C15.5126 1.42678 15.9874 1.42678 16.2803 1.71967Z"
							fill="#400F92" fill-opacity="0.8" />
						</svg>
						<a :href="dev_doc_url" class="ml-1 text-ralprimary-dark hover:underline" target="_blank">Go to help</a>
					</div>
				</div>

				<div class="flex flex-col mt-4 sm:w-1/3 sm:ml-4">
					<div class="flex items-center">
						<svg xmlns="http://www.w3.org/2000/svg" class="opacity-75" height="46" viewBox="0 -960 960 960" width="46"><path d="m240-240-92 92q-19 19-43.5 8.5T80-177v-623q0-33 23.5-56.5T160-880h640q33 0 56.5 23.5T880-800v480q0 33-23.5 56.5T800-240H240Zm-34-80h594v-480H160v525l46-45Zm-46 0v-480 480Zm120-80h240q17 0 28.5-11.5T560-440q0-17-11.5-28.5T520-480H280q-17 0-28.5 11.5T240-440q0 17 11.5 28.5T280-400Zm0-120h400q17 0 28.5-11.5T720-560q0-17-11.5-28.5T680-600H280q-17 0-28.5 11.5T240-560q0 17 11.5 28.5T280-520Zm0-120h400q17 0 28.5-11.5T720-680q0-17-11.5-28.5T680-720H280q-17 0-28.5 11.5T240-680q0 17 11.5 28.5T280-640Z"/></svg>
						<div class="ml-2 text-lg font-semibold font-[Inter]">
							Talk to an expert
						</div>
					</div>
					<div class="font-[Inter] text-base mt-4">
						Need help? Talk to us directly through chat.
					</div>
					<div class="flex items-center text-sm mt-2">
						<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24" fill="#400F92"><path d="m240-280-86 86q-10 10-22 5t-12-19v-552q0-33 23.5-56.5T200-840h480q33 0 56.5 23.5T760-760v203q-10-2-20-2.5t-20-.5q-10 0-20 .5t-20 2.5v-203H200v400h283q-2 10-2.5 20t-.5 20q0 10 .5 20t2.5 20H240Zm80-320h240q17 0 28.5-11.5T600-640q0-17-11.5-28.5T560-680H320q-17 0-28.5 11.5T280-640q0 17 11.5 28.5T320-600Zm0 160h120q17 0 28.5-11.5T480-480q0-17-11.5-28.5T440-520H320q-17 0-28.5 11.5T280-480q0 17 11.5 28.5T320-440Zm360 160h-80q-17 0-28.5-11.5T560-320q0-17 11.5-28.5T600-360h80v-80q0-17 11.5-28.5T720-480q17 0 28.5 11.5T760-440v80h80q17 0 28.5 11.5T880-320q0 17-11.5 28.5T840-280h-80v80q0 17-11.5 28.5T720-160q-17 0-28.5-11.5T680-200v-80Zm-480-80v-400 400Z"/></svg>
						<span class="ml-1 text-ralprimary-dark hover:underline cursor-pointer" @click.stop="openChat">Open chat</span>
					</div>
				</div>

				<div class="flex flex-col mt-4 sm:w-1/3 sm:ml-4">
					<div class="flex items-center">
						<svg xmlns="http://www.w3.org/2000/svg" class="opacity-75" height="46" viewBox="0 -960 960 960" width="46"><path d="M480-360q17 0 28.5-11.5T520-400q0-17-11.5-28.5T480-440q-17 0-28.5 11.5T440-400q0 17 11.5 28.5T480-360Zm0-160q17 0 28.5-11.5T520-560v-160q0-17-11.5-28.5T480-760q-17 0-28.5 11.5T440-720v160q0 17 11.5 28.5T480-520ZM240-240l-92 92q-19 19-43.5 8.5T80-177v-623q0-33 23.5-56.5T160-880h640q33 0 56.5 23.5T880-800v480q0 33-23.5 56.5T800-240H240Zm-34-80h594v-480H160v525l46-45Zm-46 0v-480 480Z"/></svg>
						<div class="ml-2 text-lg font-semibold font-[Inter]">
							Send us feedback
						</div>
					</div>
					<div class="font-[Inter] text-base mt-4">
						Missing a feature or want to suggest an improvement?
					</div>
					<div class="flex items-center text-sm mt-2">
						<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24" fill="#400F92"><path d="M480-360q17 0 28.5-11.5T520-400q0-17-11.5-28.5T480-440q-17 0-28.5 11.5T440-400q0 17 11.5 28.5T480-360Zm0-160q17 0 28.5-11.5T520-560v-160q0-17-11.5-28.5T480-760q-17 0-28.5 11.5T440-720v160q0 17 11.5 28.5T480-520ZM240-240l-92 92q-19 19-43.5 8.5T80-177v-623q0-33 23.5-56.5T160-880h640q33 0 56.5 23.5T880-800v480q0 33-23.5 56.5T800-240H240Zm-34-80h594v-480H160v525l46-45Zm-46 0v-480 480Z"/></svg>
						<span @click.stop="sendFeedback" class="ml-1 text-ralprimary-dark hover:underline cursor-pointer">Send feedback</span>
					</div>
				</div>
			</div>
		</div>
	</div>

	<div v-if="brandingModalOpen" class="fixed inset-0 bg-slate-900 bg-opacity-30 z-50 transition-opacity" aria-hidden="true"></div>
	  <div v-if="brandingModalOpen"  class="fixed inset-0 z-50 overflow-hidden flex items-center my-4 justify-center transform px-4 sm:px-6" role="dialog" aria-modal="true">
      <div ref="modalContent" class="bg-white rounded shadow-lg overflow-auto w-650 h-430 p-4">

		<iframe v-if="videoNumber == 1" width="640" height="401" src="https://www.loom.com/embed/1f1f6c4f81014eaeb1133348ab49636b?sid=5a3f234b-b05a-4796-9245-c1ec3d423179" frameborder="0" webkitallowfullscreen mozallowfullscreen allowfullscreen></iframe>

		<iframe v-if="videoNumber == 3" width="640" height="401" src="https://www.loom.com/embed/621c0352c0e549218bffe70fa35eda06?sid=604fed9b-9fac-4001-93cf-1b2dc2848bc2" frameborder="0" webkitallowfullscreen mozallowfullscreen allowfullscreen></iframe>

		<iframe v-if="videoNumber == 4" width="640" height="401" src="https://www.loom.com/embed/94c5cd42ade04485a3bd70bdc5671719?sid=b0c544e1-8f34-4f91-ab9f-a2675741601c" frameborder="0" webkitallowfullscreen mozallowfullscreen allowfullscreen></iframe>

		<div class="flex flex-wrap justify-end space-x-2 mt-4">
			<CancelButton @click="brandingModalOpen = false" cta="Done"></CancelButton>
			</div>
      </div>
    </div>

</template>

<script>
import NumberChart from '../components/NumberChart.ts.vue';
import PrimaryButton from '../components/PrimaryButton.ts.vue';
import LightSecondaryButton from '../components/LightSecondaryButton.ts.vue';
import ProgramActive from '../components/ProgramActive.ts.vue';
import ToggleItem from '../components/ToggleItem.ts.vue';
import Campaigns from './Campaigns.ts.vue';
import * as Utils from '../../client-old/utils/Utils';
import { getMetric } from '../services/metrics.js';
import ModalBlank from '../components/ModalBlank.vue';
import CancelButton from '../components/CancelButton.ts.vue';
import StatusMessage from '../components/StatusMessage.ts.vue';
import { customerIOTrackEvent } from '../services/customerio.js';
import PreviewLoyalty from '../components/PreviewLoyalty.ts.vue';
import { Crisp } from 'crisp-sdk-web';


const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	components: {
		NumberChart,
		Campaigns,
		PrimaryButton,
		LightSecondaryButton,
		ProgramActive,
		ToggleItem,
		ModalBlank,
		CancelButton,
		StatusMessage,
		PreviewLoyalty
	},
	data() {
		return {
			loyaltyPointsBalance: "--",
			revenueFromLoyalty: "--",
			loyaltyAdoptionRate: "--",
			activeMembers: "--",
			testRes: '',
			onboardingTasks: [],
			openTaskId: null,
			overrideKey: null,
			dev_doc_url: 'https://docs.raleon.io',
			brandingModalOpen: false,
			videoNumber: 0,
			onboardingTaskInterval: null,
			isFetchingOnboardingTasks: false,
			previewStyleText: 'button',
		};
	},
	created() {

	},
	async mounted() {
		customerIOTrackEvent('Viewed Quickstart');
		Crisp.session.pushEvent("quickstart-view");

		let response = {};
		let jsonresponse = {};
		try {
			console.log("Trying request to /users/doc-login", `${URL_DOMAIN}/users/doc-login`)
			response = await fetch(`${URL_DOMAIN}/users/doc-login`, {
				method: 'GET',
				withCreditentials: true,
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Access-Control-Allow-Origin': '*',
					'Content-Type': 'application/json'
				}
			});
			console.log("Response from /users/doc-login", response)
			jsonresponse = await response.json();
			console.log("DOCS DOCS DOCS", jsonresponse)
			if (jsonresponse.docs_url)
				this.dev_doc_url = `${jsonresponse.docs_url}`;
			console.log("DOCS DOCS DOCS", this.dev_doc_url);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}

		this.fetchOnboardingStepStatus().then(() => {
			this.setOpenTask();
		}).catch(error => {
			console.error("Failed to fetch onboarding tasks:", error);
		});
		this.onboardingTaskInterval = setInterval(async () => {
			if (!this.onboardingTasks.length || this.isOnboarding) {
				if (this.isFetchingOnboardingTasks) return;
				this.isFetchingOnboardingTasks = true;
				try { await this.fetchOnboardingStepStatus() } catch (e) { console.error(e) }
				this.isFetchingOnboardingTasks = false;
			} else {
				clearInterval(this.onboardingTaskInterval);
			}
		}, 3000);
	},
	computed: {
		isOnboarding() {
			return this.onboardingTasks.some(x => !x.state || !x.state?.state?.includes('erified'))
		},
		userName() {
			if(localStorage.getItem('firstName') != '' && localStorage.getItem('firstName') != null)
				return localStorage.getItem('firstName');
			else
				return "Welcome new Raleon user";
		}
	},
	methods: {
		setOpenTask() {
			if(this.onboardingTasks != null) {
				const firstNotVerified = this.onboardingTasks.find(task => !task.state || task.state.state !== 'Verified');

				if (firstNotVerified)
					this.openTaskId = firstNotVerified.id;
			}
		},
		checklistToggle(taskId) {
			if (this.openTaskId === taskId) {
				this.openTaskId = null;
			} else {
				this.openTaskId = taskId;
			}
		},
		sendFeedback() {
			Crisp.chat.open();
		},
		openChat() {
			Crisp.chat.open();
		},
		async fetchOnboardingStepStatus() {
			let url = `/onboarding-tasks/states`;
			const response = await fetch(`${URL_DOMAIN}${url}`, {
				method: 'GET',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
			});

			this.onboardingTasks = await response.json();
			this.onboardingTasks = this.onboardingTasks
				.sort((a,b) => a.priority - b.priority)
				.filter(x => !x.type.includes('nsights'));
		},
		navigate(path) {
			this.$router.push(path);
		},
		async addCampaign() {
			const programId = await this.createLoyaltyProgram();
			const campaignId = await this.createDefaultLoyaltyCampaign(programId);

			this.$router.push(`/campaign/${campaignId}`);
		},
		async createDefaultLoyaltyCampaign(programId) {
			var payload = {
				name: "Untitled Campaign",
				evergreen: true,
				active: false
			}
			let url = `/loyalty-programs/${programId}/loyalty-campaigns`;
			const response = await fetch(`${URL_DOMAIN}${url}`, {
				method: 'POST',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify(payload),
			});

			let data = await response.json();
			console.log(data);
			return data.id;
		},
		async createLoyaltyProgram() {
			let jsonresponse = {};
			try {
				const response = await fetch(`${URL_DOMAIN}/loyalty-programs`, {
					method: 'GET',
					withCreditentials: true,
					credentials: 'omit',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Access-Control-Allow-Origin': '*',
						'Content-Type': 'application/json'
					}
				});
				jsonresponse = await response.json();
				console.log("jsonresponse: " + JSON.stringify(jsonresponse));
			} catch (err) {
				console.log("Error: " + JSON.stringify(err));
			}

			if (jsonresponse.length > 0) {
				return jsonresponse[0].id;
			} else {
				var payload = {
					name: "Default Program",
					active: false
				}

				let url = `/loyalty-programs`;
				const response = await fetch(`${URL_DOMAIN}${url}`, {
					method: 'POST',
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					},
					body: JSON.stringify(payload),
				});

				let data = await response.json();


				var payloadPoints = {
					name: "Points",
					conversionToUSD: 100
				}
				let urlPoints = `/loyalty-programs/${data.id}/loyalty-currencies`;
				const responsePoints = await fetch(`${URL_DOMAIN}${urlPoints}`, {
					method: 'POST',
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					},
					body: JSON.stringify(payloadPoints),
				});

				let dataPoints = await responsePoints.json();
				console.log('Currency', dataPoints);

				console.log(data);
				return data.id;
			}
		},
	}
}
</script>
<style scoped>.test-container {
	margin: 0.5em;
}

.test {
	width: 50%;
	padding: 0em 0.4em;
}

@media all and (max-width: 768px) {
	.test {
		width: 100%;
	}
}
</style>

