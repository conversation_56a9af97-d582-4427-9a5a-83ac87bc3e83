<template>
	<div class="p-2 sm:p-7 mr-24 flex flex-col items-center justify-center" v-if="!isFeatureAvailable && !isTrialEnded">
		<img src="../images/UpgradeInsights.png" width="584">
		<a class="text-ralprimary-dark font-bold md:text-xl lg:text-2xl sm:text-normal mt-4 font-[Inter]" href="/loyalty/settings/plans">Unlock AI Powered Member Insights</a>
		<p class="text-ralblack-primary text-lg mt-4 flex items-center font-[Inter] mb-4 w-1/2 text-center">
			Understand key points your customers are dropping off, see LTV by loyalty segment, and find the right moments to increase repeat purchases.
		</p>
		<PrimaryButton
					cta="Upgrade Loyalty Plan"
					size="xs"
					@click="() => this.$router.push('/loyalty/settings/plans')"
					/>
	</div>

	<div class="bg-gradient-to-tr from-[#4F3EAC] to-[#5E48F8] p-4 w-full justify-center shadow-lg flex items-center" v-if="!isFeatureAvailable && isTrialEnded">
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 14H11V9.8L12.6 11.4L14 10L10 6L6 10L7.4 11.4L9 9.8V14ZM10 20C8.61667 20 7.31667 19.7375 6.1 19.2125C4.88333 18.6875 3.825 17.975 2.925 17.075C2.025 16.175 1.3125 15.1167 0.7875 13.9C0.2625 12.6833 0 11.3833 0 10C0 8.61667 0.2625 7.31667 0.7875 6.1C1.3125 4.88333 2.025 3.825 2.925 2.925C3.825 2.025 4.88333 1.3125 6.1 0.7875C7.31667 0.2625 8.61667 0 10 0C11.3833 0 12.6833 0.2625 13.9 0.7875C15.1167 1.3125 16.175 2.025 17.075 2.925C17.975 3.825 18.6875 4.88333 19.2125 6.1C19.7375 7.31667 20 8.61667 20 10C20 11.3833 19.7375 12.6833 19.2125 13.9C18.6875 15.1167 17.975 16.175 17.075 17.075C16.175 17.975 15.1167 18.6875 13.9 19.2125C12.6833 19.7375 11.3833 20 10 20ZM10 18C12.2333 18 14.125 17.225 15.675 15.675C17.225 14.125 18 12.2333 18 10C18 7.76667 17.225 5.875 15.675 4.325C14.125 2.775 12.2333 2 10 2C7.76667 2 5.875 2.775 4.325 4.325C2.775 5.875 2 7.76667 2 10C2 12.2333 2.775 14.125 4.325 15.675C5.875 17.225 7.76667 18 10 18Z" fill="#FFA3DF"/>
        </svg>
        <div class="text-white ml-2">Your trial has expired and your program has been deactivated.</div>
        <div class="px-2 py-2 text-white text-sm border-[#FFF] border rounded-full ml-4 hover:text-black hover:bg-[#FFF] hover:cursor-pointer transitional-bg delay-200" @click="() => this.$router.push('/loyalty/settings/plans')">
                    Choose a Plan
        </div>
    </div>

	<div class="p-2 sm:p-7 mr-24 flex flex-col items-center justify-center" v-if="isFeatureAvailable && !isShopifyConnected">
		<img src="https://cdn.shopify.com/shopifycloud/brochure/assets/brand-assets/shopify-logo-primary-logo-456baa801ee66a0a435671082365958316831c9960c480451dd0330bcdae304f.svg" width="584">
		<h2 class="text-ralprimary-dark font-bold md:text-xl lg:text-2xl sm:text-normal mt-4 font-[Inter]">Connect Shopify for Member Insights</h2>
		<p class="text-ralblack-primary text-lg mt-4 flex items-center font-[Inter] mb-4 w-1/2 text-center">
			To view customer insights and loyalty segments, connect your Shopify store to enable data synchronization.
		</p>
		<PrimaryButton
					cta="Connect Shopify"
					size="xs"
					@click="() => this.$router.push('/integrations')"
					/>
	</div>

	<div v-if="isFeatureAvailable && isShopifyConnected" class="p-2 sm:p-7 mr-24">
		<div class="text-neutral-800 text-opacity-70 sm:text-4xl md:text-6xl lg:text-7xl font-normal font-['Open Sans']">Customer Insights</div>
		<div class="inline-flex mt-4 items-center cursor-pointer" @click="handleBackClick">
			<svg width="24" height="24" viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg"
				class="hover:text-ralprimary-dark transition-color duration-300">
				<path
					d="M15.3333 12L10.3333 17M10.3333 17L15.3333 22M10.3333 17H23.6667M32 17C32 8.71573 25.2843 2 17 2C8.71573 2 2 8.71573 2 17C2 25.2843 8.71573 32 17 32C25.2843 32 32 25.2843 32 17Z"
					stroke="#202020" stroke-opacity="0.8" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" />
			</svg>
			<span class="ml-3 text-lg transition-color duration-300">Analytics</span>
		</div>
		<div class="my-3">
			<LearnMoreText text="Read about making data-driven loyalty decisions" url="https://docs.raleon.io/docs/program-return-on-investment" />
			<span class="text-slate-800 text-sm font-medium font-['Inter']"> in our Loyalty Academy.</span>
		</div>
		<div class="mt-10">
			<RevenuePotentialText />
		</div>
		<div v-if="!hasData" class="flex flex-col items-center justify-center h-22 mb-2 mt-10">
				<svg xmlns="http://www.w3.org/2000/svg" height="52" viewBox="0 -960 960 960" width="52" fill="#5A16C9"><path d="M280-280h80v-280h-80v280Zm160 0h80v-400h-80v400Zm160 0h80v-160h-80v160ZM200-120q-33 0-56.5-23.5T120-200v-560q0-33 23.5-56.5T200-840h560q33 0 56.5 23.5T840-760v560q0 33-23.5 56.5T760-120H200Zm0-80h560v-560H200v560Zm0-560v560-560Z"/></svg>
				<p class="text-xl text-ralblack-primary mt-2">Your member insights will show up within 24 hours.</p>
		</div>
		<div v-if="hasData">
			<div class="mt-6 overflow-x-hidden bg-white rounded-2xl">
				<LineChart
					@loading="handleLoading('segment_purchase_burndown', $event)"
					:metricName="'segment_purchase_burndown'"
					:keyFieldArray="['customers_very_loyal', 'customers_growth']"
					:customLabelsArray="['Loyal Customers', 'Growth Customers']"
					:type="'burndown'"
					:chartTitle="'Purchase Burn down Loyal vs Growth'"
					:keyLabel="'Customer Count'"
					:groupBy="''"
					:calculation="''"
					:xAxisLabel="'Number of Purchases'"
					class="w-full"
				/>
			</div>
			<div class="my-4 flex overflow-x-hidden">
				<div class="w-[calc(50%-0.5rem)] p-2 bg-white rounded-2xl mr-4">
					<PieChart
						@loading="handleLoading('members_segment', $event)"
						:metricName="'members_segment'"
						:keyFieldArray="['new_users', 'not_loyal', 'some_loyalty', 'very_loyal']"
						:customLabelsArray="['New Customers', 'Not Loyal', 'Growth', 'Loyal']"
						:type="'category'"
						:chartTitle="'Customers in each Segment'"
						:keyLabel="'CLTV'"
						:valueLabel="'Customer Segment'"
						:groupBy="'day'"
						:calculation="'sum'"
					/>
				</div>
				<div class="w-[calc(50%-0.5rem)] p-2 bg-white rounded-2xl">
					<PieChart
						@loading="handleLoading('segment_loyalty_revenue', $event)"
						:metricName="'segment_loyalty_revenue'"
						:keyFieldArray="['new_users_revenue', 'not_loyal_revenue', 'some_loyalty_revenue', 'very_loyal_revenue']"
						:customLabelsArray="['New Customers', 'Not Loyal', 'Growth', 'Loyal']"
						:type="'category'"
						:chartTitle="'Customer total spend in each Segment'"
						:keyLabel="'CLTV'"
						:groupBy="'day'"
						:calculation="'sum'"
						:valueLabel="'Customer Segment'"
					/>
				</div>
			</div>
			<div class="my-4 overflow-x-hidden bg-white rounded-2xl">
				<BarChart
					@loading="handleLoading('segment_aov', $event)"
					:metricName="'segment_aov'"
					:keyFieldArray="['New_Users', 'Not_Loyal', 'Growth', 'Very_Loyal']"
					:customLabelsArray="['New Customers', 'Not Loyal', 'Growth', 'Loyal']"
					:type="'category'"
					:chartTitle="'Average Order Value in each Segment'"
					:keyLabel="'AOV'"
					:groupBy="'day'"
					:calculation="'sum'"
					:valueLabel="'Customer Segment'"
				/>
			</div>
			<div class="my-4 overflow-x-hidden bg-white rounded-2xl">
				<BarChart
					@loading="handleLoading('segment_cltv', $event)"
					:metricName="'segment_cltv'"
					:keyFieldArray="['New_Users', 'Not_Loyal', 'Growth', 'Very_Loyal']"
					:customLabelsArray="['New Customers', 'Not Loyal', 'Growth', 'Loyal']"
					:type="'category'"
					:chartTitle="'Customer LTV in each Segment'"
					:keyLabel="'CLTV'"
					:groupBy="'day'"
					:calculation="'sum'"
					:valueLabel="'Customer Segment'"
				/>
			</div>
			<div v-if="tableHasData" class="mt-4 mb-6">
				Segment
				<select v-model="selectedSegmentKey" class="border px-4 py-2 rounded-lg shadow">
					<option disabled value="">Select a Segment</option>
					<option v-for="segment in segments" :key="segment.key" :value="segment.key">
					{{ segment.label }}
					</option>
				</select>
			</div>
			<div v-if="tableHasData" class="my-4 overflow-x-hidden bg-white rounded-2xl">
				<RaleonTable :column-headers="columnHeaders" :row-data="filteredRowData"
					:clickable-rows="false"
					:auto-wrap="true"
					:allow-html="true"/>
			</div>
		</div>
	</div>
</template>

<script>
import * as Utils from '../../client-old/utils/Utils';
import { dashboards } from './AnalyticsDashboard.ts.vue';
import LearnMoreText from '../components/LearnMoreText.ts.vue';
import BarChart from '../components/charts/BarChart.ts.vue';
import LineChart from '../components/charts/LineChart.ts.vue';
import PieChart from '../components/charts/PieChart.ts.vue';
import RevenuePotentialText from '../components/charts/RevenuePotentialText.ts.vue';
import { getMetric } from '../services/metrics.js';
import RaleonTable from '../../client-old/pages/component/RaleonTable.vue';
import * as CurrencyUtils from '../services/currency.js';
import { isFeatureAvailable } from '../services/features.js';
import PrimaryButton from '../components/PrimaryButton.ts.vue';

export default {
	components: {
		LearnMoreText,
		BarChart,
		LineChart,
		PieChart,
		RevenuePotentialText,
		RaleonTable,
		PrimaryButton
	},
	data() {
		return {
			dashboards,
			hasData: true,
			tableHasData: false,
			selectedSegmentKey: '',
			segments: [
				{ key: 'Very Loyal', label: 'Loyal' },
				{ key: 'Growth', label: 'Growth' },
				{ key: 'Not Loyal', label: 'Not Loyal' }
			],
			columnHeaders: [
				{ name: '1st Purchase', value: '1st Purchase' },
				{ name: '2nd Purchase', value: '2nd Purchase' },
				{ name: '3rd Purchase', value: '3rd Purchase' }
			],
			rowData: [],
			shopifyConnected: true
		}
	},
	computed: {
		isFeatureAvailable() {
			return isFeatureAvailable('member-insights-dashboards');
		},
		isTrialEnded() {
			const freeTrialData = JSON.parse(localStorage.getItem('freeTrialData'));

			return freeTrialData != {} && !freeTrialData.hasPaidPlan && freeTrialData.daysLeft < 0
		},
		isShopifyConnected() {
			return this.shopifyConnected;
		},
		filteredRowData() {
			return this.rowData.filter(row => row.segment === this.selectedSegmentKey);
		}
	},
	async mounted() {
		await this.checkShopifyConnection();
		if (this.isShopifyConnected) {
			await this.fetchData();
		}
	},
	methods: {
		async checkShopifyConnection() {
			try {
				const response = await fetch(`${Utils.URL_DOMAIN}/integration/shopify/connected`, {
					method: 'GET',
					headers: {
						'Content-Type': 'application/json',
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
					}
				});
				if (!response.ok) {
					throw new Error('Failed to check Shopify connection');
				}
				const data = await response.json();
				this.shopifyConnected = data.connected;
			} catch (error) {
				console.error('Error checking Shopify connection:', error);
				this.shopifyConnected = false;
			}
		},
		async fetchData() {
			try {
				const metricData = await getMetric('segment_top_3', 'latest', '', this.groupBy, this.calculation);
				if (metricData.statusCode !== 200 || metricData.body.data.length === 0) {
					//this.hasData = false;
					return;
				}
				this.rowData = await this.transformData(metricData.body.data);
				this.tableHasData = true;
			} catch (error) {
				console.error('Error fetching data:', error);
				this.tableHasData = false;
			}
		},
		async transformData(data) {
			const segmentData = {
				'Very Loyal': { '1st Purchase': [], '2nd Purchase': [], '3rd Purchase': [] },
				'Growth': { '1st Purchase': [], '2nd Purchase': [], '3rd Purchase': [] },
				'Not Loyal': { '1st Purchase': [], '2nd Purchase': [], '3rd Purchase': [] }
			};

			data.forEach(item => {
				const metrics = item.metrics;
				const segment = item.group_label;
				const itemName = `${metrics.item_name.value} (Qty:${metrics.total_quantity.value})`;
				const purchaseKey = `${metrics.order_rank.value}${metrics.order_rank.value === 1 ? 'st' : metrics.order_rank.value === 2 ? 'nd' : 'rd'} Purchase`;

				if (segmentData[segment]) {
					segmentData[segment][purchaseKey].push(itemName);
				}
			});

			return Object.keys(segmentData).reduce((allRows, segment) => {
				const maxLength = Math.max(segmentData[segment]['1st Purchase'].length, segmentData[segment]['2nd Purchase'].length, segmentData[segment]['3rd Purchase'].length);

				Object.keys(segmentData[segment]).forEach(key => {
					while (segmentData[segment][key].length < maxLength) {
						segmentData[segment][key].push('');
					}
				});

				const segmentRows = Array.from({ length: maxLength }).map((_, index) => ({
					segment: segment,
					'1st Purchase': segmentData[segment]['1st Purchase'][index] || '',
					'2nd Purchase': segmentData[segment]['2nd Purchase'][index] || '',
					'3rd Purchase': segmentData[segment]['3rd Purchase'][index] || ''
				}));

				return allRows.concat(segmentRows);
			}, []);
		},
		handleLoading(chart, isLoading) {
			//this.hasData = isLoading;
		},
		handleBackClick() {
			const isAiStrategist = this.$route.path.includes('ai-strategist');
			this.$router.push(isAiStrategist ? '/ai-strategist/analytics' : '/loyalty/analytics');
		}
	}
}
</script>

