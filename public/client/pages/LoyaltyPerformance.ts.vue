<template>
	<div class="p-2 sm:p-7 mr-24 flex flex-col items-center justify-center" v-if="!isFeatureAvailable && !isTrialEnded">
		<img src="../images/VIPTeirPromo.png" width="800">
		<a class="text-ralprimary-dark font-bold md:text-xl lg:text-2xl sm:text-normal mt-4 font-[Inter]" href="/loyalty/settings/plans">Upgrade to Loyalty Plan</a>
		<p class="text-ralblack-primary text-lg mt-4 flex items-center font-[Inter] mb-4 w-1/2 text-center">
			Loyalty programs powered by Raleon help brands increase repeat purchase rate and customer lifetime value.
		</p>
		<PrimaryButton
					cta="Upgrade Loyalty Plan"
					size="xs"
					@click="() => this.$router.push('/loyalty/settings/plans')"
					/>
	</div>

	<div class="bg-gradient-to-tr from-[#4F3EAC] to-[#5E48F8] p-4 w-full justify-center shadow-lg flex items-center" v-if="!isFeatureAvailable && isTrialEnded">
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 14H11V9.8L12.6 11.4L14 10L10 6L6 10L7.4 11.4L9 9.8V14ZM10 20C8.61667 20 7.31667 19.7375 6.1 19.2125C4.88333 18.6875 3.825 17.975 2.925 17.075C2.025 16.175 1.3125 15.1167 0.7875 13.9C0.2625 12.6833 0 11.3833 0 10C0 8.61667 0.2625 7.31667 0.7875 6.1C1.3125 4.88333 2.025 3.825 2.925 2.925C3.825 2.025 4.88333 1.3125 6.1 0.7875C7.31667 0.2625 8.61667 0 10 0C11.3833 0 12.6833 0.2625 13.9 0.7875C15.1167 1.3125 16.175 2.025 17.075 2.925C17.975 3.825 18.6875 4.88333 19.2125 6.1C19.7375 7.31667 20 8.61667 20 10C20 11.3833 19.7375 12.6833 19.2125 13.9C18.6875 15.1167 17.975 16.175 17.075 17.075C16.175 17.975 15.1167 18.6875 13.9 19.2125C12.6833 19.7375 11.3833 20 10 20ZM10 18C12.2333 18 14.125 17.225 15.675 15.675C17.225 14.125 18 12.2333 18 10C18 7.76667 17.225 5.875 15.675 4.325C14.125 2.775 12.2333 2 10 2C7.76667 2 5.875 2.775 4.325 4.325C2.775 5.875 2 7.76667 2 10C2 12.2333 2.775 14.125 4.325 15.675C5.875 17.225 7.76667 18 10 18Z" fill="#FFA3DF"/>
        </svg>
        <div class="text-white ml-2">Your trial has expired and your program has been deactivated.</div>
        <div class="px-2 py-2 text-white text-sm border-[#FFF] border rounded-full ml-4 hover:text-black hover:bg-[#FFF] hover:cursor-pointer transitional-bg delay-200" @click="() => this.$router.push('/loyalty/settings/plans')">
                    Choose a Plan
        </div>
    </div>

	<div class="p-2 sm:p-7 mr-24 flex flex-col items-center justify-center" v-if="isFeatureAvailable && !isShopifyConnected">
		<img src="https://cdn.shopify.com/shopifycloud/brochure/assets/brand-assets/shopify-logo-primary-logo-456baa801ee66a0a435671082365958316831c9960c480451dd0330bcdae304f.svg" width="584">
		<h2 class="text-ralprimary-dark font-bold md:text-xl lg:text-2xl sm:text-normal mt-4 font-[Inter]">Connect Shopify for Loyalty Performance</h2>
		<p class="text-ralblack-primary text-lg mt-4 flex items-center font-[Inter] mb-4 w-1/2 text-center">
			To view loyalty performance metrics and program analytics, connect your Shopify store to enable data synchronization.
		</p>
		<PrimaryButton
					cta="Connect Shopify"
					size="xs"
					@click="() => this.$router.push('/integrations')"
					/>
	</div>

	<div class="p-2 sm:p-7 mr-24" v-if="isFeatureAvailable && isShopifyConnected">
		<div class="text-neutral-800 text-opacity-70 sm:text-4xl md:text-6xl lg:text-7xl font-normal font-['Open Sans']">Loyalty Performance</div>
		<div class="inline-flex mt-4 items-center cursor-pointer" @click="this.$router.push('/loyalty/analytics');">
			<svg width="24" height="24" viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg"
				class="hover:text-ralprimary-dark transition-color duration-300">
				<path
					d="M15.3333 12L10.3333 17M10.3333 17L15.3333 22M10.3333 17H23.6667M32 17C32 8.71573 25.2843 2 17 2C8.71573 2 2 8.71573 2 17C2 25.2843 8.71573 32 17 32C25.2843 32 32 25.2843 32 17Z"
					stroke="#202020" stroke-opacity="0.8" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" />
			</svg>
			<span class="ml-3 text-lg transition-color duration-300">Analytics</span>
		</div>
		<div class="my-3">
			<LearnMoreText text="Read about making data-driven loyalty decisions" url="https://docs.raleon.io/docs/program-return-on-investment" />
			<span class="text-slate-800 text-sm font-medium font-['Inter']"> in our Loyalty Academy.</span>
		</div>
		<div class="mt-10">
			<RevenuePotentialText />
		</div>
		<div v-if="!hasData" class="flex flex-col items-center justify-center h-22 mb-2 mt-10">
				<svg xmlns="http://www.w3.org/2000/svg" height="52" viewBox="0 -960 960 960" width="52" fill="#5A16C9"><path d="M280-280h80v-280h-80v280Zm160 0h80v-400h-80v400Zm160 0h80v-160h-80v160ZM200-120q-33 0-56.5-23.5T120-200v-560q0-33 23.5-56.5T200-840h560q33 0 56.5 23.5T840-760v560q0 33-23.5 56.5T760-120H200Zm0-80h560v-560H200v560Zm0-560v560-560Z"/></svg>
				<p class="text-xl text-ralblack-primary mt-2">Your member insights will show up within 24 hours.</p>
		</div>
		<div v-if="hasData">
			<div class="my-4 flex overflow-x-hidden">
				<div class="w-[calc(50%-0.5rem)] p-2 bg-white rounded-2xl mr-4">
					<LineChart
						@loading="handleLoading('member_attributed_revenue', $event)"
						:metricName="'member_attributed_revenue'"
						:valueField="'member_attributed_revenue'"
						:keyFieldArray="['member_attributed_revenue']"
						:customLabelsArray="['Attributed Revenue']"
						:type="'time'"
						:chartTitle="'Revenue Attributed to Loyalty'"
						:keyLabel="''"
						:startDate="latest"
						:groupBy="'week'"
						:calculation="'trend'"
						:xAxisLabel="''"
						class="w-full"
					/>
				</div>
				<div class="w-[calc(50%-0.5rem)] p-2 bg-white rounded-2xl mr-4">
					<LineChart
						@loading="handleLoading('member_attributed_revenue', $event)"
						:metricName="'member_attributed_revenue'"
						:valueField="'revenue_difference'"
						:keyFieldArray="['revenue_difference']"
						:customLabelsArray="['Weekly Attributed Revenue']"
						:type="'time'"
						:chartTitle="'Weekly Attributed Revenue'"
						:startDate="latest"
						:keyLabel="''"
						:groupBy="'week'"
						:calculation="'trend'"
						:xAxisLabel="''"
						class="w-full"
					/>
				</div>

			</div>
			<div class="my-4 flex overflow-x-hidden">
								<!-- <div class="w-[calc(50%-0.5rem)] p-2 bg-white rounded-2xl mr-4">
					<LineChart
						@loading="handleLoading('roi', $event)"
						:metricName="'roi'"
						:valueField="'roi'"
						:keyFieldArray="['roi']"
						:customLabelsArray="['ROLS']"
						:type="'time'"
						:chartTitle="'Return on Loyalty Spend'"
						:keyLabel="''"
						:groupBy="'day'"
						:calculation="'trend'"
						:xAxisLabel="''"
						class="w-full"
					/>
				</div> -->
				<div class="w-[calc(50%-0.5rem)] p-2 bg-white rounded-2xl mr-4">
					<LineChart
						@loading="handleLoading('loyalty_revenue_month', $event)"
						:metricName="'loyalty_revenue_month'"
						:valueField="'member_revenue'"
						:keyFieldArray="['member_revenue']"
						:customLabelsArray="[]"
						:type="'time'"
						:chartTitle="'Revenue from Loyalty Members'"
						:keyLabel="''"
						:startDate="latest"
						:groupBy="'day'"
						:calculation="'trend'"
						:xAxisLabel="''"
						class="w-full"
					/>
				</div>
				<div class="w-[calc(50%-0.5rem)] p-2 bg-white rounded-2xl mr-4">
					<LineChart
						@loading="handleLoading('loyalty_points_balance', $event)"
						:metricName="'loyalty_points_balance'"
						:valueField="'totalpoints'"
						:keyFieldArray="['totalpoints']"
						:customLabelsArray="['Total Points']"
						:type="'time'"
						:chartTitle="'Points Outstanding'"
						:keyLabel="''"
						:groupBy="'day'"
						:calculation="'trend'"
						:xAxisLabel="''"
						class="w-full"
					/>
				</div>
			</div>
			<div class="my-4 overflow-x-hidden bg-white rounded-2xl">
				<BarChart
					:metricName="'coupons_redeemed'"
					:keyFieldArray="['used_count']"
					:customLabelsArray="['Used']"
					:type="'top10'"
					:chartTitle="'Top Coupons Used'"
					:keyLabel="'Coupons Used'"
					:startDate="latest"
					:calculation="''"
					:valueLabel="'Customer Segment'"
				/>
			</div>
			<div class="my-4 overflow-x-hidden bg-white rounded-2xl">
				<BarChart
					:metricName="'ways_to_earn'"
					:keyFieldArray="['earn_count']"
					:customLabelsArray="['Earned']"
					:type="'top10'"
					:chartTitle="'Top Completed Ways to Earn'"
					:keyLabel="'Earned'"
					:startDate="latest"
					:calculation="''"
					:valueLabel="'Customer Segment'"
				/>
			</div>
		</div>
	</div>
</template>

<script>

	import * as Utils from '../../client-old/utils/Utils';
	import { dashboards } from './AnalyticsDashboard.ts.vue';
	import LearnMoreText from '../components/LearnMoreText.ts.vue';
	import BarChart from '../components/charts/BarChart.ts.vue';
	import LineChart from '../components/charts/LineChart.ts.vue';
	import PieChart from '../components/charts/PieChart.ts.vue';
	import RevenuePotentialText from '../components/charts/RevenuePotentialText.ts.vue';
import { isFeatureAvailable } from '../services/features';
import PrimaryButton from '../components/PrimaryButton.ts.vue';


	const URL_DOMAIN = Utils.URL_DOMAIN;

	export default {
		components: {
			LearnMoreText,
			BarChart,
			LineChart,
			PieChart,
			RevenuePotentialText,
			PrimaryButton
		},
		async mounted() {
			await this.checkShopifyConnection();
		},
		data() {
			return {
				dashboards,
				hasData: true,
				shopifyConnected: true
			}
		},
		computed: {
			isFeatureAvailable() {
				return isFeatureAvailable('performance-dashboard');
			},
			isTrialEnded() {
				const freeTrialData = JSON.parse(localStorage.getItem('freeTrialData'));

				return freeTrialData != {} && !freeTrialData.hasPaidPlan && freeTrialData.daysLeft < 0
			},
			isShopifyConnected() {
				return this.shopifyConnected;
			}
		},
		methods: {
			handleLoading(chart, isLoading) {
				if (!isLoading) {
					//this.hasData = false;
				}
			},
			async checkShopifyConnection() {
				try {
					const response = await fetch(`${URL_DOMAIN}/integration/shopify/connected`, {
						method: 'GET',
						headers: {
							'Content-Type': 'application/json',
							'Authorization': `Bearer ${localStorage.getItem('token')}`,
						}
					});
					if (!response.ok) {
						throw new Error('Failed to check Shopify connection');
					}
					const data = await response.json();
					this.shopifyConnected = data.connected;
				} catch (error) {
					console.error('Error checking Shopify connection:', error);
					this.shopifyConnected = false;
				}
			}
		},
	}
</script>

