<template>
	<ToastStatus :status="status" :text="statusText" @clear-status="clearStatus()" />

	<div class="flex flex-col p-6 bg-white shadow-sm sm:flex-row items-center justify-between">
	  <div>
		<div class="text-3xl sm:text-3xl font-sans font-medium opacity-70 text-ralblack-primary">
		  Prompt Manager
		</div>
		<p class="mt-1 text-gray-500">Manage and customize AI agent prompts</p>
	  </div>

	  <button
		v-if="activeTab !== 'logs'"
		class="flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
		@click="openCreateModal"
	  >
		<span class="w-4 h-4 mr-2">➕</span>
		Add New Prompt
	  </button>
	</div>

	<!-- Tab Navigation -->
	<div class="mb-6 px-6">
		<div class="border-b border-gray-200">
			<nav class="-mb-px flex space-x-8">
				<button
					@click="activeTab = 'templates'"
					:class="[activeTab === 'templates' ? 'border-purple-500 text-purple-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300', 'whitespace-nowrap pb-4 px-1 border-b-2 font-medium']">
					Templates
				</button>
				<button
					@click="activeTab = 'components'"
					:class="[activeTab === 'components' ? 'border-purple-500 text-purple-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300', 'whitespace-nowrap pb-4 px-1 border-b-2 font-medium']">
					Components
				</button>
                                <button
                                        @click="activeTab = 'logs'; loadPromptLogs()"
                                        :class="[activeTab === 'logs' ? 'border-purple-500 text-purple-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300', 'whitespace-nowrap pb-4 px-1 border-b-2 font-medium']">
                                        Logs
                                </button>
                                <button
                                        @click="activeTab = 'conversations'; loadConversations()"
                                        :class="[activeTab === 'conversations' ? 'border-purple-500 text-purple-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300', 'whitespace-nowrap pb-4 px-1 border-b-2 font-medium']">
                                        Conversations
                                </button>
                        </nav>
                </div>
        </div>

	<div v-if="activeTab === 'templates'" class="space-y-6 p-6">
	  <div v-if="isLoading" class="flex justify-center items-center h-64">
		<div class="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600"></div>
	  </div>

	  <template v-else>
		<div v-for="(templates, category) in groupedTemplates" :key="category" class="bg-white rounded-lg border shadow-sm">
		  <div class="p-6">
			<h2 class="text-lg font-semibold">{{ category }}</h2>
		  </div>

		  <div class="p-6 pt-0 space-y-4">
			<div v-for="template in templates" :key="template.id" class="border rounded-lg p-4">
			  <div class="flex justify-between items-start mb-2">
				<h3 class="font-medium">{{ template.name }}</h3>
				            <div class="flex items-center space-x-3">
				              <!-- Active Toggle Switch -->
				              <label :for="'active-toggle-' + template.id" class="flex items-center cursor-pointer">
				                <div class="relative">
				                  <input
				                    type="checkbox"
				                    :id="'active-toggle-' + template.id"
				                    class="sr-only"
				                    :checked="template.isActive"
				                    @change="toggleTemplateActive(template)"
				                  />
				                  <div class="block bg-gray-300 w-10 h-5 rounded-full"></div>
				                  <div
				                    class="dot absolute left-1 top-0.5 bg-white w-4 h-4 rounded-full transition"
				                    :class="{ 'translate-x-full !bg-purple-600': template.isActive }"
				                  ></div>
				                </div>
				                <span class="ml-2 text-xs font-medium text-gray-700">{{ template.isActive ? 'Active' : 'Inactive' }}</span>
				              </label>

				              <button
				                @click="copyToClipboard(template.content)"
				                class="text-gray-600 hover:text-gray-800"
				                :title="copyStatus[template.id!] || 'Copy to clipboard'"
				              >
				                 📋
				              </button>
				              <button @click="editTemplate(template)" class="text-blue-600 hover:text-blue-800">
				                 ✏️
				              </button>
				              <button
				                @click="template.id !== undefined ? deleteTemplate(template.id) : null"
				                class="text-red-600 hover:text-red-800">
				                🗑️
				              </button>
				            </div>
			  </div>
			  <div class="relative">
				<pre
				  class="text-sm bg-gray-50 p-3 rounded whitespace-pre-wrap break-words"
				  :class="{ 'max-h-[400px] overflow-hidden': !expandedTemplates[template.id!] }"
				>{{ template.content }}</pre>
				<div
				  v-if="template.content.split('\n').length > 25"
				  class="absolute bottom-0 left-0 right-0 flex justify-center py-2 bg-gradient-to-t from-gray-50"
				>
				  <button
					@click="toggleTemplate(template.id!)"
					class="px-3 py-1 text-sm text-purple-600 hover:text-purple-800 bg-white rounded-full shadow-sm border"
				  >
					{{ expandedTemplates[template.id!] ? 'Show Less' : 'Show More' }}
				  </button>
				</div>
			  </div>
			</div>
		  </div>
		</div>
	  </template>
	</div>

	<!-- Unlayer Components Section -->
	<div v-if="activeTab === 'components'" class="bg-white rounded-lg border shadow-sm mt-6">
	  <div class="p-6">
		<h2 class="text-lg font-semibold">Email Components</h2>
		<p class="text-sm text-gray-500">Manage reusable email components for AI generation</p>

		<div class="flex justify-end">
		  <button
			@click="openComponentModal"
			class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
		  >
			Add Component
		  </button>
		</div>
	  </div>

	  <div class="p-6 pt-0 space-y-4">
		<div v-for="component in unlayerComponents" :key="component.id" class="border rounded-lg p-4">
		  <div class="flex justify-between items-start mb-4">
				<div class="flex-1">
					<div class="flex items-center gap-2">
						<button
							@click="toggleComponent(component.id!)"
							class="text-gray-600 hover:text-gray-800"
						>
							{{ expandedComponents[component.id!] ? '▼' : '▶' }}
						</button>
						<h3 class="font-medium">{{ component.name }}</h3>
					</div>
					<p class="text-sm text-gray-500">{{ component.description }}</p>
				</div>
				<div class="flex space-x-2">
					<button @click="editComponent(component)" class="text-blue-600 hover:text-blue-800">
						 ✏️
					</button>
					<button @click="component.id && deleteComponent(component.id)" class="text-red-600 hover:text-red-800">
						🗑️
					</button>
				</div>
			</div>
			<pre
				v-if="expandedComponents[component.id!]"
				class="text-sm bg-gray-50 p-3 rounded transition-all duration-200"
			>{{ formatJson(component.json) }}</pre>
			<div
				v-if="expandedComponents[component.id!] && component.editableFields && Object.keys(getComponentEditableFields(component)).length > 0"
				class="mt-2 p-2 bg-purple-50 rounded-lg border border-purple-100"
			>
				<h4 class="text-sm font-medium text-purple-700 mb-1">AI Editable Fields:</h4>
				<div class="flex flex-wrap gap-2">
					<div
						v-for="(value, field) in getComponentEditableFields(component)"
						:key="field"
						class="px-2 py-1 bg-purple-100 text-purple-800 rounded-md"
					>
						<div class="text-xs font-medium">{{ field }}</div>
						<div v-if="typeof value === 'string' && value" class="text-xs text-purple-600 mt-1">
							{{ value }}
						</div>
					</div>
				</div>
			</div>
		</div>
	  </div>
	</div>

	<!-- Component Modal -->
	<div v-if="showComponentModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
	  <div class="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-auto">
		<h2 class="text-xl font-semibold mb-4">
		  {{ editingComponent ? 'Edit' : 'Create' }} Email Component
		</h2>

		<div class="space-y-4">
		  <div>
			<label class="block text-sm font-medium mb-1">Name</label>
			<input
			  v-model="currentComponent.name"
			  class="w-full px-3 py-2 border rounded-lg"
			  placeholder="Component name"
			/>
		  </div>

		  <div>
			<label class="block text-sm font-medium mb-1">Description</label>
			<textarea
			  v-model="currentComponent.description"
			  class="w-full px-3 py-2 border rounded-lg h-24"
			  placeholder="Component description"
			></textarea>
		  </div>

		  <div>
			<label class="block text-sm font-medium mb-1">JSON Structure</label>
			<textarea
			  v-model="currentComponent.json"
			  class="w-full px-3 py-2 border rounded-lg h-48 font-mono"
			  placeholder="Component JSON structure"
			  @input="parseComponentJson"
			></textarea>
			<div v-if="jsonError" class="text-red-500 text-sm mt-1">
			  {{ jsonError }}
			</div>
			<div v-if="detectedComponentTypes.length > 0" class="mt-2 text-sm">
			  <span class="font-medium">Detected components:</span>
			  <div class="flex flex-wrap gap-1 mt-1">
				<span
				  v-for="(type, index) in detectedComponentTypes"
				  :key="index"
				  class="px-2 py-1 bg-purple-100 text-purple-800 rounded-md text-xs"
				>
				  {{ type }}
				</span>
			  </div>
			</div>
		  </div>

		  <!-- Add Editable AI section -->
		  <div>
			<label class="block text-sm font-medium mb-1">Editable AI Fields</label>
			<div class="bg-gray-50 p-4 rounded-lg border h-64 overflow-y-auto">
			  <div v-if="parsedComponentJson === null" class="text-gray-500 text-sm">
				Enter valid JSON above to select editable fields
			  </div>
			  <div v-else-if="Object.keys(groupedFieldPaths).length === 0" class="text-gray-500 text-sm">
				No editable fields found in this component
			  </div>
			  <div v-else class="space-y-4">
				<div class="text-sm text-gray-600 mb-2">Select fields that can be edited by AI:</div>

				<div v-for="(group, componentId) in groupedFieldPaths" :key="componentId" class="border-b pb-3 mb-3 last:border-b-0">
				  <div class="font-medium text-sm mb-2 flex items-center">
					<span class="mr-2">{{ getComponentName(componentId) }}</span>
					<span class="px-2 py-0.5 bg-purple-100 text-purple-800 rounded text-xs">{{ getComponentType(componentId) }}</span>
				  </div>

				  <div v-for="path in group" :key="path" class="flex flex-col ml-4 mb-3">
					<div class="flex items-center">
					  <input
						type="checkbox"
						:id="'field-' + path"
						v-model="editableFields[path].editable"
						class="mr-2"
					  />
					  <label :for="'field-' + path" class="text-sm font-mono">{{ getFieldLabel(path) }}</label>
					</div>
					<div v-if="editableFields[path].editable" class="mt-1 ml-5 w-full">
					  <input
						type="text"
						v-model="editableFields[path].description"
						class="w-full px-2 py-1 text-xs border border-gray-300 rounded"
						placeholder="Add description for AI (optional)"
					  />
					</div>
				  </div>
				</div>
			  </div>
			</div>
		  </div>
		</div>

		<div class="flex justify-end space-x-3 mt-6">
		  <button
			@click="closeComponentModal"
			class="px-4 py-2 border rounded-lg hover:bg-gray-50"
		  >
			Cancel
		  </button>
		  <button
			@click="saveComponent"
			class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
		  >
			Save
		  </button>
		</div>
	  </div>
	</div>

	<!-- Create/Edit Modal -->
	<div v-if="showModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
	  <div class="bg-white rounded-lg p-6 w-full max-w-5xl">
		<h2 class="text-xl font-semibold mb-4">{{ editingTemplate ? 'Edit' : 'Create' }} Prompt Template</h2>

		<div class="flex gap-6">
		  <!-- Left sidebar with tags -->
		  <div class="w-96 border-r pr-6 flex flex-col">
			<h3 class="text-sm font-medium text-gray-700 mb-2">Available Tags</h3>
			<div class="space-y-2 overflow-y-auto max-h-[500px] pr-2 tags-scroll">
			  <div
				v-for="tag in promptTags"
				:key="tag"
				@click="insertTag(tag)"
				class="inline-flex items-center px-4 py-2 rounded-md text-sm font-medium bg-purple-50 text-purple-700 cursor-pointer hover:bg-purple-100 transition-colors duration-200 whitespace-nowrap overflow-hidden text-ellipsis w-full"
			  >
				{{ tag }}
			  </div>
			</div>
			<div class="mt-4 text-xs text-gray-500">
			  Click on a tag to insert it at the cursor position
			</div>
		  </div>

		  <!-- Right side form -->
		  <div class="flex-1 space-y-4">
			<div>
			  <label class="block text-sm font-medium mb-1">Name</label>
			  <input
				v-model="currentTemplate.name"
				class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
				placeholder="Enter template name"
			  />
			</div>

			<div>
			  <label class="block text-sm font-medium mb-1">Category</label>
			  <input
				v-model="currentTemplate.category"
				class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
				placeholder="Enter category"
			  />
			</div>

			         <div>
			           <label class="block text-sm font-medium mb-1">Type</label>
			           <input
			             v-model="currentTemplate.type"
			             class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
			             placeholder="Enter template type (e.g., email_generation, sms_reply)"
			           />
			           <p v-if="editingTemplate" class="mt-1 text-xs text-red-600">
			             Warning: Changing the type might affect how this template is used. Only change if you know what you are doing.
			           </p>
			         </div>

			<div>
			  <label class="block text-sm font-medium mb-1">Content</label>
			  <textarea
			 ref="contentTextarea"
			 v-model="currentTemplate.content"
			 class="w-full h-64 px-3 py-2 border rounded-lg font-mono text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 whitespace-pre-wrap"
			 placeholder="Enter prompt template content"
			  ></textarea>
			</div>
		  </div>
		</div>

		<div class="flex justify-end space-x-3 mt-6">
		  <button
			@click="closeModal"
			class="px-4 py-2 border rounded-lg hover:bg-gray-50"
		  >
			Cancel
		  </button>
		  <button
			@click="saveTemplate"
			class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
		  >
			Save
		  </button>
		</div>
	  </div>
	</div>
  <!-- Prompt Logs Tab -->
  <div v-if="activeTab === 'logs'" class="space-y-6 p-6">
    <div class="bg-white rounded-lg border shadow-sm">
      <div class="p-6 flex justify-between items-center">
        <h2 class="text-lg font-semibold">Prompt Logs</h2>
        <div class="flex space-x-4">
          <div class="relative">
            <input
              type="text"
              v-model="orgIdFilter"
              placeholder="Filter by Organization ID"
              class="px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
              @keyup.enter="loadPromptLogs(true)"
            />
            <button
              @click="loadPromptLogs()"
              class="absolute right-2 top-2 text-purple-600 hover:text-purple-800"
            >
              🔍
            </button>
          </div>
        </div>
      </div>

      <div class="p-6 pt-0">
        <div v-if="logsLoading" class="flex justify-center items-center h-64">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600"></div>
        </div>

        <div v-else-if="promptLogs.length === 0" class="text-center py-12 text-gray-500">
          No logs found
        </div>

        <div v-else>
          <table class="min-w-full divide-y divide-gray-200">
            <thead>
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Org ID</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="log in promptLogs" :key="log.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ log.id }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ log.type }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ formatDate(log.date) }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ log.orgId }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <button
                    @click="viewLogContent(log.id!)"
                    class="text-purple-600 hover:text-purple-900"
                  >
                    View Content
                  </button>
                </td>
              </tr>
            </tbody>
          </table>

          <!-- Pagination -->
          <div class="flex justify-between items-center mt-6">
            <div class="text-sm text-gray-700">
              Showing <span class="font-medium">{{ (currentPage - 1) * pageSize + 1 }}</span> to <span class="font-medium">{{ Math.min(currentPage * pageSize, totalLogs) }}</span> of <span class="font-medium">{{ totalLogs }}</span> logs
            </div>
            <div class="flex space-x-2">
              <button
                @click="prevPage"
                :disabled="currentPage === 1"
                :class="{'opacity-50 cursor-not-allowed': currentPage === 1}"
                class="px-3 py-1 border rounded-md text-sm"
              >
                Previous
              </button>
              <button
                @click="nextPage"
                :disabled="currentPage >= totalPages"
                :class="{'opacity-50 cursor-not-allowed': currentPage >= totalPages}"
                class="px-3 py-1 border rounded-md text-sm"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Conversations Tab -->
  <div v-if="activeTab === 'conversations'" class="space-y-6 p-6">
    <div class="bg-white rounded-lg border shadow-sm">
      <div class="p-6 flex justify-between items-center">
        <h2 class="text-lg font-semibold">Conversations</h2>
        <div class="flex space-x-4">
          <div class="relative">
            <input
              type="text"
              v-model="orgIdFilter"
              placeholder="Filter by Organization ID"
              class="px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
              @keyup.enter="loadConversations()"
            />
            <button
              @click="loadConversations()"
              class="absolute right-2 top-2 text-purple-600 hover:text-purple-800"
            >
              🔍
            </button>
          </div>
        </div>
      </div>

      <div class="p-6 pt-0">
        <div v-if="conversationsLoading" class="flex justify-center items-center h-64">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600"></div>
        </div>

        <div v-else-if="conversations.length === 0" class="text-center py-12 text-gray-500">
          No conversations found
        </div>

        <div v-else class="space-y-4">
          <div v-for="conv in conversations" :key="conv.id" class="border rounded-lg">
            <div class="p-4 flex justify-between items-center cursor-pointer" @click="toggleConversation(conv)">
              <div>
                <h3 class="font-medium">{{ conv.name || `Conversation #${conv.id}` }}</h3>
                <p class="text-sm text-gray-500">Updated {{ formatDate(conv.updatedAt) }}</p>
              </div>
              <span class="text-purple-600">{{ expandedConversations[conv.id] ? '▲' : '▼' }}</span>
            </div>
            <div v-if="expandedConversations[conv.id]" class="border-t p-4 space-y-2 bg-gray-50">
              <div v-if="!messagesByConversation[conv.id]" class="flex justify-center py-4">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-600"></div>
              </div>
              <div v-else>
                <div v-for="msg in messagesByConversation[conv.id]" :key="msg.id" class="text-sm">
                  <span class="font-medium" :class="msg.role === 'user' ? 'text-gray-700' : 'text-purple-700'">{{ msg.role }}:</span>
                  <span class="whitespace-pre-wrap"> {{ msg.content }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Log Content Modal -->
  <div v-if="showLogContentModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-auto">
      <div class="flex justify-between items-start mb-4">
        <h2 class="text-xl font-semibold">Prompt Log Content</h2>
        <div>
          <button
            @click="copyLogContent"
            class="text-purple-600 hover:text-purple-800 mr-4"
            :title="logCopyStatus || 'Copy content'"
          >
            {{ logCopyStatus ? 'Copied!' : '📋 Copy' }}
          </button>
          <button @click="closeLogContentModal" class="text-gray-500 hover:text-gray-700">
            ✕
          </button>
        </div>
      </div>

      <div v-if="logContentLoading" class="flex justify-center items-center h-64">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600"></div>
      </div>

      <div v-else>
        <pre class="bg-gray-50 p-4 rounded-lg overflow-auto max-h-[60vh] text-sm font-mono">{{ formatJson(logContent) }}</pre>
      </div>
    </div>
  </div>
  </template>

<script lang="ts">
import { defineComponent, ref, reactive } from '@vue/runtime-core';
import ToastStatus from '../../client-old/pages/component/ToastStatus.vue';
import { customerIOTrackEvent } from '../services/customerio.js';
import * as Utils from '../../client-old/utils/Utils';
import { PROMPT_TAGS } from '../../../src/services/prompt/tags';
const URL_DOMAIN = Utils.URL_DOMAIN;

interface PromptTemplate {
  id?: number;
  name: string;
  content: string;
  category: string;
  type: string; // Added type field
  isActive: boolean;
}

interface UnlayerComponent {
  id?: number;
  name: string;
  description?: string;
  json: string;
  type?: string;
  editableFields?: Record<string, boolean>;
}

interface PromptLog {
  id?: number;
  prompt: string;
  orgId: number;
  type: string;
  date: string;
}

interface Conversation {
  id: number;
  name?: string;
  createdAt: string;
  updatedAt: string;
}

interface MessageItem {
  id: number;
  role: string;
  content: string;
  createdAt: string;
}

export default defineComponent({
  name: 'PromptManager',
  components: {
    ToastStatus,
  },
  setup() {
    const contentTextarea = ref<HTMLTextAreaElement | null>(null);
    return { contentTextarea };
  },
  data() {
    return {
      activeTab: 'templates',
      status: '',
      statusText: '',
      templates: [] as PromptTemplate[],
      isLoading: true,
      showModal: false,
      currentTemplate: {
        name: '',
        content: '',
        category: '',
        type: '', // Added type field
        isActive: true,
      } as PromptTemplate,
      editingTemplate: null as PromptTemplate | null,
      // Prompt Logs data
      promptLogs: [] as PromptLog[],
      logsLoading: false,
      logContentLoading: false,
      showLogContentModal: false,
      logContent: null,
      currentPage: 1,
      pageSize: 50,
      totalLogs: 0,
      totalPages: 1,
      orgIdFilter: '' as string,
      // Import prompt tags from centralized location
      promptTags: PROMPT_TAGS.map(tag => `{${tag}}`),
      expandedTemplates: reactive({}) as Record<number, boolean>,
      copyStatus: reactive({}) as Record<number, string>,
      unlayerComponents: [] as UnlayerComponent[],
      showComponentModal: false,
      editingComponent: null as UnlayerComponent | null,
      currentComponent: {
        name: '',
        description: '',
        json: '',
        type: 'unlayer'
      },
      jsonError: '',
      expandedComponents: reactive({}) as Record<number, boolean>,
      parsedComponentJson: null as any,
      editableFields: reactive({}) as Record<string, { editable: boolean; description: string }>,
      detectedComponentTypes: [] as string[],
      componentTypesMap: {} as Record<string, string>,
      componentNamesMap: {} as Record<string, string>,
      logCopyStatus: '',
      conversations: [] as Conversation[],
      conversationsLoading: false,
      expandedConversations: reactive({}) as Record<number, boolean>,
      messagesByConversation: reactive({}) as Record<number, MessageItem[]>,
    };
  },
  computed: {
    groupedTemplates(): Record<string, PromptTemplate[]> {
      return this.templates.reduce((acc, template) => {
        if (!acc[template.category]) {
          acc[template.category] = [];
        }
        acc[template.category].push(template);
        return acc;
      }, {} as Record<string, PromptTemplate[]>);
    },
	groupedFieldPaths(): Record<string, string[]> {
		if (!this.parsedComponentJson) return {};

		const result: Record<string, string[]> = {};
		const allPaths = this.extractPaths(this.parsedComponentJson);

		allPaths.forEach(path => {
			const componentId = this.getComponentIdFromPath(path);
			if (componentId) {
			if (!result[componentId]) {
				result[componentId] = [];
			}
			result[componentId].push(path);
			}
		});

		return result;
	}
  },
  methods: {
    clearStatus() {
      this.status = '';
      this.statusText = '';
    },
    async checkAdminAccess() {
      const storedUserInfo = localStorage.getItem('userInfo');
      const userInfo = storedUserInfo ? JSON.parse(storedUserInfo) : null;
      if (userInfo?.roles?.includes('raleon-admin')) {
        console.log("User is a raleon admin");
        return;
      } else {
        this.$router.push('/chat');
      }
    },
    async loadTemplates() {
      try {
        const response = await fetch(`${URL_DOMAIN}/prompt-templates`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
        });
        this.templates = await response.json();
        // Initialize expand/collapse state for each template
        this.templates.forEach(template => {
          if (template.id) {
            this.expandedTemplates[template.id] = false;
          }
        });
      } catch (error) {
        console.error('Failed to load templates:', error);
        this.status = 'error';
        this.statusText = 'Failed to load prompt templates';
      } finally {
        this.isLoading = false;
      }
    },
    openCreateModal() {
      this.editingTemplate = null;
      this.currentTemplate = {
        name: '',
        content: '',
        category: '',
        type: '', // Initialize type
        isActive: true,
      };
      this.showModal = true;
    },
    editTemplate(template: PromptTemplate) {
      this.editingTemplate = template;
      this.currentTemplate = { ...template };
      this.showModal = true;
    },
    closeModal() {
      this.showModal = false;
      this.editingTemplate = null;
      this.currentTemplate = {
        name: '',
        content: '',
        category: '',
        type: '', // Reset type
        isActive: true,
      };
    },
    async saveTemplate() {
      try {
        // Fix the URL to avoid double api/v1
        const url = this.editingTemplate
          ? `${URL_DOMAIN}/prompt-templates/${this.editingTemplate.id}`
          : `${URL_DOMAIN}/prompt-templates`;

        const method = this.editingTemplate ? 'PATCH' : 'POST';

        const response = await fetch(url, {
          method,
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: this.currentTemplate.name,
            content: this.currentTemplate.content,
            category: this.currentTemplate.category,
            type: this.currentTemplate.type, // Add type to payload
            isActive: this.currentTemplate.isActive // Send current active state
          }),
        });

        if (!response.ok) throw new Error('Failed to save template');

        this.status = 'success';
        this.statusText = `Template ${this.editingTemplate ? 'updated' : 'created'} successfully`;
        await this.loadTemplates();
        this.closeModal();

        customerIOTrackEvent(`${this.editingTemplate ? 'Updated' : 'Created'} Prompt Template`);
      } catch (error) {
        console.error('Failed to save template:', error);
        this.status = 'error';
        this.statusText = 'Failed to save template';
      }
    },
    async deleteTemplate(id: number) {
      if (!confirm('Are you sure you want to delete this template?')) return;

      try {
        const response = await fetch(`${URL_DOMAIN}/prompt-templates/${id}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
        });

        if (!response.ok) throw new Error('Failed to delete template');

        this.status = 'success';
        this.statusText = 'Template deleted successfully';
        await this.loadTemplates();

        customerIOTrackEvent('Deleted Prompt Template');
      } catch (error) {
        console.error('Failed to delete template:', error);
        this.status = 'error';
        this.statusText = 'Failed to delete template';
      }
    },
    async toggleTemplateActive(template: PromptTemplate) {
      if (!template.id) return;

      const originalStatus = template.isActive;
      // Optimistically update UI
      template.isActive = !template.isActive;

      try {
        const response = await fetch(`${URL_DOMAIN}/prompt-templates/${template.id}`, {
          method: 'PATCH',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ isActive: template.isActive }),
        });

        if (!response.ok) {
          throw new Error(`Failed to update template status: ${response.statusText}`);
        }

        this.status = 'success';
        this.statusText = `Template "${template.name}" ${template.isActive ? 'activated' : 'deactivated'}`;
        customerIOTrackEvent(`Toggled Prompt Template Active: ${template.isActive}`);

        // Optional: Reload templates to ensure consistency, or trust optimistic update
        // await this.loadTemplates();

      } catch (error) {
        console.error('Failed to toggle template active status:', error);
        // Revert optimistic update on failure
        template.isActive = originalStatus;
        this.status = 'error';
        this.statusText = `Failed to update status for template "${template.name}"`;
      }
    },
    insertTag(tag: string) {
      if (!this.showModal || !this.contentTextarea) return;

      const textarea = this.contentTextarea as unknown as HTMLTextAreaElement;
      if (!textarea) return;

      const startPos = textarea.selectionStart ?? 0;
      const endPos = textarea.selectionEnd ?? 0;
      const currentContent = this.currentTemplate.content;
      const newContent =
        currentContent.substring(0, startPos) +
        tag +
        currentContent.substring(endPos);

      // Update content without causing a scroll jump
      this.currentTemplate.content = newContent;

      // Preserve scroll position and set cursor position
      const scrollTop = textarea.scrollTop;
      this.$nextTick(() => {
        textarea.focus();
        textarea.scrollTop = scrollTop;
        textarea.setSelectionRange(startPos + tag.length, startPos + tag.length);
      });
    },
    toggleTemplate(id: number) {
      this.expandedTemplates[id] = !this.expandedTemplates[id];
    },
    async copyToClipboard(text: string) {
      try {
        await navigator.clipboard.writeText(text);
        const templateId = this.templates.find(t => t.content === text)?.id;
        if (templateId) {
          this.copyStatus[templateId] = 'Copied!';
          setTimeout(() => {
            this.copyStatus[templateId] = '';
          }, 2000);
        }
      } catch (err) {
        console.error('Failed to copy text: ', err);
      }
    },
    async loadUnlayerComponents() {
      try {
        const response = await fetch(`${URL_DOMAIN}/unlayer-components`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
        });
        const components = await response.json();

        // Filter out components with non-null orgId
        const filteredComponents = components.filter(component => !component.orgId);
		console.log('filteredComponents', filteredComponents);

        // Process components to ensure editableFields is properly parsed
        this.unlayerComponents = filteredComponents.map(component => {
          // If editableFields is a string, parse it into an object
          if (component.editableFields && typeof component.editableFields === 'string') {
            try {
              component.editableFields = JSON.parse(component.editableFields);
            } catch (e) {
              console.error('Error parsing editable fields:', e);
              component.editableFields = {};
            }
          }
          return component;
        });

        // Initialize expansion state for each component
        this.unlayerComponents.forEach(component => {
          if (component.id) {
            this.expandedComponents[component.id] = false;
          }
        });
      } catch (error) {
        console.error('Failed to load unlayer components:', error);
      }
    },
    async saveUnlayerComponent(component) {
      try {
        const response = await fetch(`${URL_DOMAIN}/unlayer-components`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(component),
        });
        if (!response.ok) throw new Error('Failed to save component');
        await this.loadUnlayerComponents();
      } catch (error) {
        console.error('Failed to save component:', error);
      }
    },
    async deleteUnlayerComponent(id) {
      if (!confirm('Are you sure you want to delete this component?')) return;
      try {
        const response = await fetch(`${URL_DOMAIN}/unlayer-components/${id}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
        });
        if (!response.ok) throw new Error('Failed to delete component');
        await this.loadUnlayerComponents();
      } catch (error) {
        console.error('Failed to delete component:', error);
      }
    },
    openComponentModal() {
      this.editingComponent = null;
      this.currentComponent = {
        name: '',
        description: '',
        json: '',
        type: 'unlayer'
      };
      this.showComponentModal = true;
      this.jsonError = '';
      this.parsedComponentJson = null;
      this.editableFields = reactive({});
      this.detectedComponentTypes = [];
    },
    editComponent(component: UnlayerComponent) {
      this.editingComponent = component;
      this.currentComponent = { ...component };
      this.showComponentModal = true;
      this.jsonError = '';
      this.parseComponentJson();

      // Initialize editable fields from the component
      if (component.editableFields) {
        // Ensure editableFields is properly processed
        const fields = typeof component.editableFields === 'string'
          ? JSON.parse(component.editableFields)
          : component.editableFields;

        Object.keys(fields).forEach(key => {
          if (!this.editableFields[key]) {
            this.editableFields[key] = { editable: true, description: '' };
          } else {
            this.editableFields[key].editable = true;
          }

          // Set description if it exists
          if (typeof fields[key] === 'string') {
            this.editableFields[key].description = fields[key];
          }
        });
      }
    },
    closeComponentModal() {
      this.showComponentModal = false;
      this.editingComponent = null;
      this.jsonError = '';
      this.parsedComponentJson = null;
      this.editableFields = reactive({});
      this.detectedComponentTypes = [];
    },
    parseComponentJson() {
      try {
        this.parsedComponentJson = JSON.parse(this.currentComponent.json);
        this.detectComponentTypes();
        // Reset editable fields whenever JSON changes
        this.editableFields = reactive({});
        // Initialize the editable fields structure
        const allPaths = this.extractPaths(this.parsedComponentJson);
        allPaths.forEach(path => {
          this.editableFields[path] = { editable: false, description: '' };
        });
      } catch (e) {
        this.parsedComponentJson = null;
        this.detectedComponentTypes = [];
      }
    },
    detectComponentTypes() {
      this.detectedComponentTypes = [];
      if (!this.parsedComponentJson || !this.parsedComponentJson.body || !this.parsedComponentJson.body.rows) {
        return;
      }

      try {
        // Extract component types
        this.parsedComponentJson.body.rows.forEach(row => {
          if (row.columns) {
            row.columns.forEach(column => {
              if (column.contents) {
                column.contents.forEach(content => {
                  if (content.type && !this.detectedComponentTypes.includes(content.type)) {
                    this.detectedComponentTypes.push(content.type);
                  }
                });
              }
            });
          }
        });
      } catch (e) {
        console.error('Error detecting component types:', e);
      }
    },
    // Extract all possible field paths from JSON
    extractPaths(obj, parentPath = '') {
      let paths: string[] = [];

      if (obj !== null && typeof obj === 'object') {
        // Check if this is a specific component type with custom field filtering
        try {
          if (parentPath === '' && obj.body && obj.body.rows) {
            // Extract component types and their content IDs for selective field filtering
            this.componentTypesMap = {};
            this.componentNamesMap = {};

            // Traverse the structure to find all components and their types
            obj.body.rows.forEach((row, rowIndex) => {
              if (row.columns) {
                row.columns.forEach((column, colIndex) => {
                  if (column.contents) {
                    column.contents.forEach((content, contentIndex) => {
                      if (content.type && content.id) {
                        // Store the component type for this ID
                        this.componentTypesMap[content.id] = content.type;

                        // Try to extract a friendly name for the component
                        if (content.values) {
                          if (content.values.text) {
                            // Extract text without HTML
                            const textWithoutHtml = content.values.text.replace(/<[^>]*>/g, '');
                            this.componentNamesMap[content.id] = textWithoutHtml.substring(0, 20) +
                              (textWithoutHtml.length > 20 ? '...' : '');
                          } else if (content.values._meta && content.values._meta.htmlID) {
                            this.componentNamesMap[content.id] = content.values._meta.htmlID;
                          }
                        }

                        if (!this.componentNamesMap[content.id]) {
                          this.componentNamesMap[content.id] = `Component ${content.id.substring(0, 8)}`;
                        }

                        // Generate paths for all relevant fields in this component
                        const componentBasePath = `body.rows[${rowIndex}].columns[${colIndex}].contents[${contentIndex}]`;
                        const componentPaths = this.getComponentPaths(content, componentBasePath, content.type);
						console.log('componentPaths', componentPaths);
                        paths = paths.concat(componentPaths);
                      }
                    });
                  }
                });
              }
            });

            return paths;
          }
        } catch (e) {
          console.error('Error while processing component structure:', e);
        }

        // Default path extraction if not a special component structure
        Object.keys(obj).forEach(key => {
          const currentPath = parentPath ? `${parentPath}.${key}` : key;

          if (obj[key] !== null && typeof obj[key] === 'object' && !Array.isArray(obj[key])) {
            // For nested objects, recursively extract paths
            paths = paths.concat(this.extractPaths(obj[key], currentPath));
          } else if (Array.isArray(obj[key])) {
            // For arrays, add the array path and then paths for each element
            paths.push(currentPath);

            // Add paths for array elements if they are objects
            obj[key].forEach((item, index) => {
              if (item !== null && typeof item === 'object') {
                paths = paths.concat(this.extractPaths(item, `${currentPath}[${index}]`));
              }
            });
          } else {
            // For primitive values
            paths.push(currentPath);
          }
        });
      }

      return paths;
    },
    // Method to get component-specific paths
    getComponentPaths(component, basePath, componentType) {
    	const paths: string[] = [];

		if (!component.values) return paths;

		switch (componentType) {
			case 'heading':
			if (component.values.text) {
				paths.push(`${basePath}.values.text`);
			}
			break;

			case 'text':
			if (component.values.text) {
				paths.push(`${basePath}.values.text`);
			}
			break;

			case 'button':
			if (component.values.text) {
				paths.push(`${basePath}.values.text`);
			}
			if (component.values.href && component.values.href.values && component.values.href.values.href !== undefined) {
				paths.push(`${basePath}.values.href.values.href`);
			}
			break;

			case 'image':
			if (component.values.src && component.values.src.url) {
				paths.push(`${basePath}.values.src.url`);
			}
			if ('altText' in component.values) { // Check existence, even if empty
				paths.push(`${basePath}.values.altText`);
			}
			if (component.values.action && component.values.action.values && component.values.action.values.href !== undefined) {
				paths.push(`${basePath}.values.action.values.href`);
			}
			break;

			default:
			// Only include text fields for unknown types
			if (component.values.text) {
				paths.push(`${basePath}.values.text`);
			}
			break;
		}

		return paths;
	},
    // Helper method to decide which paths to include based on component type
    shouldIncludePath(path: string, componentType: string): boolean {
      // Only include paths in the values object
      if (!path.includes('.values.')) {
        return false;
      }

      switch (componentType) {
        case 'heading':
          // For headings, only include the text property
          return path.endsWith('.values.text');

        case 'button':
          // For buttons, include text and URL
          return path.endsWith('.values.text') ||
                 path.includes('.values.href.values.href');

        case 'image':
          // For images, include src and alt
          return path.includes('.values.src') || path.endsWith('.values.alt');

        case 'text':
          // For text blocks, include the text content
          return path.endsWith('.values.text');

        case 'divider':
          // Dividers typically don't have editable content
          return false;

        default:
          // For unknown component types, include common text fields
          return path.endsWith('.values.text') ||
                 path.endsWith('.values.title') ||
                 path.endsWith('.values.description') ||
                 path.endsWith('.values.content');
      }
    },
    // Get a human-readable component name
    getComponentName(componentId: string): string {
      return this.componentNamesMap[componentId] || componentId;
    },
    // Get the component type
    getComponentType(componentId: string): string {
      return this.componentTypesMap[componentId] || 'unknown';
    },
	getComponentIdFromPath(path: string): string | null {
		const match = path.match(/body\.rows\[(\d+)\]\.columns\[(\d+)\]\.contents\[(\d+)\]/);
		if (match && this.parsedComponentJson) {
			const rowIndex = parseInt(match[1]);
			const colIndex = parseInt(match[2]);
			const contentIndex = parseInt(match[3]);
			try {
			return this.parsedComponentJson.body.rows[rowIndex].columns[colIndex].contents[contentIndex].id || null;
			} catch (e) {
			console.error('Error extracting component ID from path:', e);
			return null;
			}
		}
		return null;
	},
    // Get a more readable field label from the path
    getFieldLabel(path: string): string {
    let componentType = 'unknown';
	console.log('Path:', path, 'Component Type:', componentType);
    for (const [contentId, type] of Object.entries(this.componentTypesMap)) {
      	if (path.includes(contentId)) {
      			componentType = type as string;
				break;
			}
		}

		if (path.endsWith('.values.text')) {
			if (componentType === 'heading') return 'Heading Text';
			else if (componentType === 'text') return 'Text Content';
			else if (componentType === 'button') return 'Button Text';
			else return 'Text';
		} else if (path.endsWith('.values.src.url')) {
			return 'Image URL';
		} else if (path.endsWith('.values.altText')) {
			return 'Alt Text';
		} else if (path.endsWith('.values.href.values.href')) {
			return 'Button URL';
		} else {
			const lastPart = path.split('.').pop() || '';
			return lastPart.charAt(0).toUpperCase() + lastPart.slice(1);
		}
	},
    toggleComponent(id: number) {
      this.expandedComponents[id] = !this.expandedComponents[id];
    },
    async saveComponent() {
      try {
        // Validate JSON
        try {
          JSON.parse(this.currentComponent.json); // Just validate the JSON structure

          // Only include fields that are marked as editable
          const editableFieldsToSave = {};

          // Iterate through all editableFields and only keep the ones that are true
          Object.entries(this.editableFields).forEach(([key, value]) => {
            if ((value as any).editable === true && !key.match(/^\d+$/)) { // Skip numeric keys that are array indices
              editableFieldsToSave[key] = (value as any).description;
            }
          });

          const url = this.editingComponent
            ? `${URL_DOMAIN}/unlayer-components/${this.editingComponent.id}`
            : `${URL_DOMAIN}/unlayer-components`;

          const componentToSave = {
            name: this.currentComponent.name,
            description: this.currentComponent.description,
            json: this.currentComponent.json, // Keep as string
            type: this.currentComponent.type,
            editableFields: JSON.stringify(editableFieldsToSave)
          };

          const response = await fetch(url, {
            method: this.editingComponent ? 'PATCH' : 'POST',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(componentToSave),
          });

          if (!response.ok) throw new Error('Failed to save component');

          this.status = 'success';
          this.statusText = `Component ${this.editingComponent ? 'updated' : 'created'} successfully`;
          await this.loadUnlayerComponents();
          this.closeComponentModal();
        } catch (e) {
          this.jsonError = 'Invalid JSON structure';
          return;
        }
      } catch (error) {
        console.error('Failed to save component:', error);
        this.status = 'error';
        this.statusText = 'Failed to save component';
      }
    },
    async deleteComponent(id: number) {
      if (!confirm('Are you sure you want to delete this component?')) return;

      try {
        const response = await fetch(`${URL_DOMAIN}/unlayer-components/${id}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
        });

        if (!response.ok) throw new Error('Failed to delete component');

        this.status = 'success';
        this.statusText = 'Component deleted successfully';
        await this.loadUnlayerComponents();
      } catch (error) {
        console.error('Failed to delete component:', error);
        this.status = 'error';
        this.statusText = 'Failed to delete component';
      }
    },
    // Helper to get component editable fields regardless of format
    getComponentEditableFields(component: UnlayerComponent): Record<string, any> {
      if (!component.editableFields) return {};

      // If it's a string, parse it
      if (typeof component.editableFields === 'string') {
        try {
          return JSON.parse(component.editableFields);
        } catch (e) {
          console.error('Error parsing editable fields:', e);
          return {};
        }
      }

      // If it's already an object
      return component.editableFields;
    },

    // Prompt Logs methods
    async loadPromptLogs(resetPage = false) {
      try {
        this.logsLoading = true;

        // Reset to page 1 if filtering
        if (resetPage) {
          this.currentPage = 1;
        }

        let url = `${URL_DOMAIN}/prompt-logs`;
        const queryParams = new URLSearchParams();

        // Add organization filter if provided and valid
        const orgId = parseInt(this.orgIdFilter, 10);
        if (!isNaN(orgId) && this.orgIdFilter.trim() !== '') {
          url = `${URL_DOMAIN}/prompt-logs/organization/${orgId}`;
        }

        // Add pagination filter
        const filter = {
          limit: this.pageSize,
          skip: (this.currentPage - 1) * this.pageSize,
          order: ['date DESC']
        };
        queryParams.set('filter', JSON.stringify(filter));

        // Append query parameters to the URL
        url += `?${queryParams.toString()}`;

        console.log('Loading prompt logs with filter:', this.orgIdFilter);
        console.log('Parsed Org ID:', orgId);
        console.log('Request URL:', url);
        console.log('Filter object:', filter);

        const response = await fetch(url, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
        });

        if (!response.ok) throw new Error('Failed to load prompt logs');

        const data = await response.json();
        this.promptLogs = data.data;
        this.totalLogs = data.total;
        this.totalPages = Math.ceil(this.totalLogs / this.pageSize);
      } catch (error) {
        console.error('Failed to load prompt logs:', error);
        this.status = 'error';
        this.statusText = 'Failed to load prompt logs';
      } finally {
        this.logsLoading = false;
      }
    },

    async viewLogContent(id: number) {
      try {
        this.logContentLoading = true;
        this.showLogContentModal = true;

        const response = await fetch(`${URL_DOMAIN}/prompt-logs/${id}/content`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
        });

        if (!response.ok) throw new Error('Failed to load log content');

        this.logContent = await response.json();
      } catch (error) {
        console.error('Failed to load log content:', error);
        this.status = 'error';
        this.statusText = 'Failed to load log content';
      } finally {
        this.logContentLoading = false;
      }
    },

    closeLogContentModal() {
      this.showLogContentModal = false;
      this.logContent = null;
    },

    prevPage() {
      if (this.currentPage > 1) {
        this.currentPage--;
        this.loadPromptLogs();
      }
    },

    nextPage() {
      if (this.currentPage < this.totalPages) {
        this.currentPage++;
        this.loadPromptLogs();
      }
    },
    async copyLogContent() {
      if (!this.logContent) return;
      try {
        const textToCopy = this.formatJson(this.logContent);
        await navigator.clipboard.writeText(textToCopy);
        this.logCopyStatus = 'Copied!';
        setTimeout(() => {
          this.logCopyStatus = '';
        }, 2000);
      } catch (err) {
        console.error('Failed to copy log content: ', err);
        this.logCopyStatus = 'Failed to copy';
        setTimeout(() => {
          this.logCopyStatus = '';
        }, 2000);
      }
    },

    // Conversations methods
    async loadConversations() {
      try {
        this.conversationsLoading = true;
        let url = `${URL_DOMAIN}/chat/conversations`;
        const orgId = parseInt(this.orgIdFilter, 10);
        if (!isNaN(orgId) && this.orgIdFilter.trim() !== '') {
          url = `${URL_DOMAIN}/chat/conversations/organization/${orgId}`;
        }
        url += '?limit=50&offset=0&sortField=updatedAt&sortOrder=DESC';
        const response = await fetch(url, {
          headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` },
        });
        if (!response.ok) throw new Error('Failed to load conversations');
        const data = await response.json();
        this.conversations = data.data;
        this.conversations.forEach(c => { this.expandedConversations[c.id] = false; });
      } catch (error) {
        console.error('Failed to load conversations:', error);
        this.status = 'error';
        this.statusText = 'Failed to load conversations';
      } finally {
        this.conversationsLoading = false;
      }
    },

    async loadMessages(conversationId: number) {
      if (this.messagesByConversation[conversationId]) return;
      try {
        const response = await fetch(`${URL_DOMAIN}/chat/conversations/${conversationId}/messages?limit=100&sortOrder=ASC`, {
          headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` },
        });
        if (!response.ok) throw new Error('Failed to load messages');
        const data = await response.json();
        this.messagesByConversation[conversationId] = data.data;
      } catch (error) {
        console.error('Failed to load messages:', error);
      }
    },

    async toggleConversation(conv: Conversation) {
      const id = conv.id;
      this.expandedConversations[id] = !this.expandedConversations[id];
      if (this.expandedConversations[id] && !this.messagesByConversation[id]) {
        await this.loadMessages(id);
      }
    },

    formatDate(dateString: string) {
      return new Date(dateString).toLocaleString();
    },

    formatJson(json: any) {
      if (!json) return '';
      try {
        return typeof json === 'string' ? JSON.stringify(JSON.parse(json), null, 2) : JSON.stringify(json, null, 2);
      } catch (e) {
        return json;
      }
    },
  },

  async mounted() {
    await this.checkAdminAccess();
    await this.loadTemplates();
    customerIOTrackEvent('Viewed Prompt Manager');
    await this.loadUnlayerComponents();
  },
});
</script>

<style scoped>
/* Keep the textarea and tag highlighting styles */
textarea {
  line-height: 1.5;
  tab-size: 2;
}

textarea :deep(*) {
  color: #6b21a8;
  font-weight: 500;
}

textarea :deep([class*="tag"]) {
  background-color: #f3e8ff;
  padding: 0.1em 0.3em;
  border-radius: 0.25em;
}

.\[max-h-400px\] {
  max-height: 400px;
}

pre {
  max-width: 100%;
  word-break: break-word;
}

[title="Copied!"] {
  opacity: 0.7;
}

.tags-scroll {
  scrollbar-width: thin;
  scrollbar-color: #9333ea #f3e8ff;
}

.tags-scroll::-webkit-scrollbar {
  width: 6px;
}

.tags-scroll::-webkit-scrollbar-track {
  background: #f3e8ff;
  border-radius: 3px;
}

.tags-scroll::-webkit-scrollbar-thumb {
  background-color: #9333ea;
  border-radius: 3px;
}

/* Add transition styles for smooth collapse/expand */
pre {
  overflow: hidden;
}
</style>







