<template>
	<ToastStatus :status="status" :text="statusText" @clear-status="clearStatus()" />
	<div class="m-3 sm:m-10 sm:m-7">
		<div class="relative">
			<div class="flex flex-col sm:flex-row items-start justify-between mb-12">
				<div class="flex-grow">
					<h1 class="text-3xl sm:text-6xl font-sans font-medium opacity-70 text-ralblack-primary">Customers
					</h1>
					<div class="text-ralblack-primary text-sm font-[Inter] mt-2 mb-4">
						<span>
							<p>View a list of your current customers from Shopify, along with their loyalty profile.</p>
							<p>
								Need to import loyalty points from another platform?
								<LearnMoreText text="Learn more" url="https://docs.raleon.io/docs/importing-customers"
									class="ml-0 sm:ml-1"></LearnMoreText>
							</p>
						</span>
					</div>
				</div>
				<div class="absolute top-0 right-0 flex flex-row gap-2 items-center mt-2">
					<LightSecondaryButton cta="Import Points" :is-disabled="false" @click="triggerFileInput">
						<input id="file-upload" type="file" class="hidden" @change="handleFileUpload" ref="fileInput" />
					</LightSecondaryButton>
					<LightSecondaryButton :cta="exporting ? 'Exporting...' : 'Export Customers'"
						:is-disabled="exporting" @click="exportCustomers">
					</LightSecondaryButton>
				</div>
			</div>
		</div>
		<!-- <div class="pagination">
			<LightSecondaryButton cta="Previous" :is-disabled="currentPage <= 1" @click="previousPage()"></LightSecondaryButton>
			<span>Page {{ currentPage }}</span>
			<LightSecondaryButton cta="Next" :is-disabled="nextPageNum == null" @click="nextPage()"></LightSecondaryButton>
		</div> -->
		<div v-if="!isLoading && totalCustomers > 0" class="w-full">
			<form class="relative w-full" @submit.prevent="onSearch">
				<label for="action-search" class="sr-only">Search</label>
				<input id="action-search" v-model="searchTerm" class="form-input pl-4 focus:border-slate-300 w-full"
					type="search" :placeholder="placeholder" />
				<button class="absolute inset-y-0 right-0 flex items-center group" type="submit" aria-label="Search">
					<svg class="w-4 h-4 shrink-0 fill-current text-slate-400 group-hover:text-slate-500 ml-3 mr-2"
						viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
						<path
							d="M7 14c-3.86 0-7-3.14-7-7s3.14-7 7-7 7 3.14 7 7-3.14 7-7 7zM7 2C4.243 2 2 4.243 2 7s2.243 5 5 5 5-2.243 5-5-2.243-5-5-5z" />
						<path
							d="M15.707 14.293L13.314 11.9a8.019 8.019 0 01-1.414 1.414l2.393 2.393a.997.997 0 001.414 0 .999.999 0 000-1.414z" />
					</svg>
				</button>
			</form>
			<div v-if="isLoading">
				<CampaignTableSkeletonLoader />
			</div>
		</div>

		<div class="mb-4" v-if="!isLoading && totalCustomers > 0">
			<RaleonTable :column-headers="columnHeaders" :row-data="displayShoppers" :clickable-rows="true"
				@row-clicked="handleRowClick" />
		</div>

		<NoCustomersYet v-if="!isLoading && totalCustomers === 0" />

		<div v-if="!isLoading && totalCustomers > 0" class="w-full">
			<div class="flex items-center justify-center gap-8 mb-4">
				<button :disabled="currentPage <= 1" @click="previousPage"
					class="relative h-8 max-h-[32px] w-8 max-w-[32px] select-none rounded-lg border border-gray-900 text-center align-middle font-sans text-xs font-medium uppercase text-gray-900 transition-all hover:opacity-75 focus:ring focus:ring-gray-300 active:opacity-[0.85] disabled:pointer-events-none disabled:opacity-50 disabled:shadow-none"
					type="button">
					<span class="absolute transform -translate-x-1/2 -translate-y-1/2 top-1/2 left-1/2">
						<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2"
							stroke="currentColor" aria-hidden="true" class="w-4 h-4">
							<path stroke-linecap="round" stroke-linejoin="round"
								d="M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18"></path>
						</svg>
					</span>
				</button>
				<p class="block font-sans text-base antialiased font-normal leading-relaxed text-gray-700">
					Page <strong class="text-gray-900">{{ currentPage }}</strong> of
					<strong class="text-gray-900">{{ searchTerm ? (nextPageNum ? nextPageNum : currentPage) : totalPages
						}}</strong>
				</p>
				<button :disabled="searchTerm ? !nextPageNum : currentPage >= totalPages" @click="nextPage"
					class="relative h-8 max-h-[32px] w-8 max-w-[32px] select-none rounded-lg border border-gray-900 text-center align-middle font-sans text-xs font-medium uppercase text-gray-900 transition-all hover:opacity-75 focus:ring focus:ring-gray-300 active:opacity-[0.85] disabled:pointer-events-none disabled:opacity-50 disabled:shadow-none"
					type="button">
					<span class="absolute transform -translate-x-1/2 -translate-y-1/2 top-1/2 left-1/2">
						<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2"
							stroke="currentColor" aria-hidden="true" class="w-4 h-4">
							<path stroke-linecap="round" stroke-linejoin="round"
								d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3"></path>
						</svg>
					</span>
				</button>
			</div>
		</div>
	</div>
	<ModalBlank id="import-modal" :modalOpen="importInProgress" @close-modal="importInProgress = false">
		<div class="p-5 flex flex-col space-y-4">
			<div class="text-lg font-semibold text-ralblack-primary">
				Importing Loyalty Points
			</div>
			<div class="text-sm">
				<p>Please do not close your screen while importing.</p>
			</div>
			<div class="text-sm">
				<p>Progress so far: {{ Math.floor(importProgress / importTotalRows * 100) }}% processed.</p>
				<!-- Optionally, show progress as a percentage -->
				<!-- <p>Progress: {{ (importProgress / totalRows) * 100 }}%</p> -->
			</div>
			<div class="flex justify-end space-x-2">
				<CancelButton @click="importInProgress = false" cta="Cancel"></CancelButton>
			</div>
		</div>
	</ModalBlank>
</template>

<script>
import RaleonTable from '../components/RaleonTable.ts.vue';
import * as Utils from '../../client-old/utils/Utils';
import LightSecondaryButton from '../components/LightSecondaryButton.ts.vue';
import CampaignTableSkeletonLoader from '../components/CampaignTableSkeletonLoader.ts.vue';
import LearnMoreText from '../components/LearnMoreText.ts.vue';
import ToastStatus from '../../client-old/pages/component/ToastStatus.vue'
import Papa from 'papaparse'; // CSV parsing library
import ModalBlank from '../components/ModalBlank.vue';
import { customerIOTrackEvent } from '../services/customerio.js';
import NoCustomersYet from '../components/NoCustomers.ts.vue';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	components: {
		RaleonTable,
		LightSecondaryButton,
		CampaignTableSkeletonLoader,
		LearnMoreText,
		ToastStatus,
		ModalBlank,
		NoCustomersYet,
	},
	data() {
		return {
			allShoppers: [],
			displayShoppers: [],
			searchedShoppers: [],
			currentPage: 1,
			totalPages: 0,
			totalCustomers: 0,
			nextPageNum: null,
			limit: 15,
			isLoading: true,
			searchTerm: '',
			exporting: false,
			status: '',
			statusText: '',
			placeholder: 'Search for a shopper',
			importInProgress: false,
        	importProgress: 0,
			importTotalRows: 1,
			columnHeaders: [
				{
					name: 'Name',
					tooltip: 'Shopper’s full name',
					value: 'name', // This will correspond to the combined name field
				},
				{
					name: 'Email',
					tooltip: 'Shopper’s email address',
					value: 'email', // Ensure there is an email field in your data
				},
				{
					name: 'Segment',
					tooltip: 'Shopper’s segment',
					value: 'loyalty_segment',
				},
				{
					name: 'Loyalty Points',
					tooltip: 'Current loyalty points',
					value: 'loyalty_points',
				},
			],
		};
	},
	mounted() {
		this.fetchShopperCount();
		this.fetchShopperPage(this.currentPage);
	},
	methods: {
		async exportCustomers() {
			customerIOTrackEvent('Export Customers');

			this.exporting = true;
			try {
				const response = await fetch(`${URL_DOMAIN}/export-shoppers`, {
					method: 'POST',
					credentials: 'omit',
					headers: {
						Authorization: `Bearer ${localStorage.getItem('token')}`,
						'Access-Control-Allow-Origin': '*',
						'Content-Type': 'application/json',
					},
				});

				if (!response.ok) {
					this.status = 'error';
					this.statusText = 'Failed to export customers, please try again, or contact support';
					throw new Error('Network response was not ok');
				}
				else {
					this.status = 'success';
					this.statusText = 'Export Started, it wil be emailed to you shortly';
				}


			} catch (error) {
				this.exporting = false;
				console.error('Error exporting customers:', error);
			}
			this.exporting = false;
		},
		async onSearch() {
			console.log('Searching for:', this.searchTerm);
			this.searchedShoppers = [];
			this.currentPage = 1;
			if (this.searchTerm === '') this.fetchShopperPage(1);
			else this.fetchShopperPage(1, this.searchTerm);
		},
		async fetchShopperCount() {
			let path = '/organizations/shoppers/count';
			const response = await fetch(`${URL_DOMAIN}${path}`, {
				method: 'GET',
				credentials: 'omit',
				headers: {
					Authorization: `Bearer ${localStorage.getItem('token')}`,
					'Access-Control-Allow-Origin': '*',
					'Content-Type': 'application/json',
				},
			});
			const data = await response.json();
			this.totalCustomers = data?.count || 0;
			this.totalPages = Math.ceil(this.totalCustomers / this.limit);
		},
		async fetchShopperPage(page = 1, search = '') {
			//Lets see if we already have fetched this if !search
			if (!search && this.allShoppers.length > (page - 1) * this.limit) {
				console.log('No need to fetch again');
				this.nextPageNum =
					this.allShoppers.length >= page * this.limit ? page + 1 : null;
				this.updateDisplayShoppers();
				return;
			}

			let path = '/organizations/shoppers';
			path += `?page=${page}&limit=${this.limit}`;
			if (search) {
				path += `&search=${search}`;
			}

			this.isLoading = true;
			const response = await fetch(`${URL_DOMAIN}${path}`, {
				method: 'GET',
				credentials: 'omit',
				headers: {
					Authorization: `Bearer ${localStorage.getItem('token')}`,
					'Access-Control-Allow-Origin': '*',
					'Content-Type': 'application/json',
				},
			});
			const data = await response.json();

			if (data.pagination) {
				this.nextPageNum = data.pagination.nextPage;
			}

			const shoppers = data.shoppers;

			if (!shoppers || shoppers.length === 0) {
				this.isLoading = false;
				this.displayShoppers = [];
				this.nextPageNum = 1;
				this.currentPage = 1;
				return;
			}

			if (!search) {
				this.allShoppers.push(
					...shoppers.map((shopper) => ({
						id: shopper.id,
						name: `${shopper.first_name} ${shopper.last_name}`,
						email: shopper.email,
						loyalty_points: Utils.formatNumberWithCommas(shopper.loyalty_points),
						loyalty_segment: shopper.loyalty_segment || 'N/A',
						all_data: shopper,
					}))
				);
				this.updateDisplayShoppers(false);
			} else {
				this.searchedShoppers.push(
					...shoppers.map((shopper) => ({
						id: shopper.id,
						name: `${shopper.first_name} ${shopper.last_name}`,
						email: shopper.email,
						loyalty_points: Utils.formatNumberWithCommas(shopper.loyalty_points),
						loyalty_segment: shopper.loyalty_segment || 'N/A',
						all_data: shopper,
					}))
				);
				this.updateDisplayShoppers(true);
			}

			this.isLoading = false;
		},
		updateDisplayShoppers(useSearch = false) {
			if (!useSearch) {
				const startIndex = (this.currentPage - 1) * this.limit;
				this.displayShoppers = this.allShoppers.slice(
					startIndex,
					startIndex + this.limit
				);
			} else {
				const startIndex = (this.currentPage - 1) * this.limit;
				this.displayShoppers = this.searchedShoppers.slice(
					startIndex,
					startIndex + this.limit
				);
			}
		},
		handleRowClick(shopper) {
			console.log('Clicked on:', shopper);
			this.$router.push({
				name: 'shopper-details',
				params: { id: shopper.id, shopper: shopper.all_data },
			});
		},
		async nextPage() {
			if (this.currentPage < this.totalPages) {
				this.currentPage++;
				await this.fetchShopperPage(this.currentPage);
			}
		},

		async previousPage() {
			if (this.currentPage > 1) {
				this.currentPage--;
				await this.fetchShopperPage(this.currentPage);
			}
		},
		handleFileUpload(event) {
			customerIOTrackEvent('Import Points');
			const file = event.target.files[0];
			if (file) {
				this.importInProgress = true;
				this.status = 'success';
				this.statusText = 'Points Import Started Successfully';
				Papa.parse(file, {
					header: true,
					complete: this.processCSV
				});
			} else {
				this.status = 'fail';
				this.statusText = 'No file selected';
			}
		},
		triggerFileInput() {
			this.$refs.fileInput.click();
		},
		async processCSV(results) {
			try {
				// Filter out empty rows where all fields are empty or just whitespace
				const rows = results.data.filter(row =>
					Object.values(row).some(value => value && value.toString().trim() !== '')
				);

				await this.fetchAllShoppers();
				console.log('All Shoppers:', this.allShoppers.length);
				console.log('Valid Rows:', rows.length);

				this.importTotalRows = rows.length;
				const missingShoppers = [];
				const shopperUpdates = [];

		// First Pass: Identify missing shoppers and prepare updates
				for (const row of rows) {
					const { points_to_add, shopify_id, description } = row;
					const parsedShopifyId = parseInt(shopify_id);

					const shopper = this.allShoppers.find(
						(shopper) => shopper.all_data.shopify_id === parsedShopifyId
					);

					if (shopper) {
						shopperUpdates.push({
							shopperData: shopper.all_data,
							points: parseInt(points_to_add.toString().replace(/,/g, '')),
							description,
						});
					} else {
						missingShoppers.push({
							shopify_id: parsedShopifyId,
							points: parseInt(points_to_add.toString().replace(/,/g, '')),
							description,
						});
					}
				}

				// Batch Initialize Missing Shoppers
				if (missingShoppers.length > 0) {
					const chunkSize = 100; // Adjust based on API limitations
					for (let i = 0; i < missingShoppers.length; i += chunkSize) {
						const chunk = missingShoppers.slice(i, i + chunkSize);
						await this.initializeShoppers(chunk);
						this.importProgress += chunk.length;
					}
				}

				const queue = [];
				const concurrencyLimit = 50;

				for (const update of shopperUpdates) {
					const promise = this.modifyShopperPoints(
					update.shopperData,
					update.points,
					update.description
					).then(() => {
						this.importProgress++;
					});

					queue.push(promise);

					if (queue.length > concurrencyLimit) {
						// Wait for the first promise in the queue to resolve
						await Promise.race(queue);
						// Remove resolved promises from the queue
						queue.splice(0, queue.findIndex(p => p.isFulfilled) + 1);
					}
				}

				// Wait for all remaining promises to resolve
				await Promise.all(queue);

				this.status = 'success';
				this.statusText = 'Points Imported Successfully';
			} catch (e) {
				this.status = 'fail';
				this.statusText = 'Failed to import points';
				console.error('Error importing points:', e);
			} finally {
				this.importInProgress = false;
				this.importProgress = 0;
			}
		},

		async initializeShoppers(shopifyIds) {
			try {
				const payload = { shoppers: shopifyIds };
				const response = await fetch(`${URL_DOMAIN}/organizations/import-init-shopper`, {
					method: 'POST',
					headers: {
						Authorization: `Bearer ${localStorage.getItem('token')}`,
						'Access-Control-Allow-Origin': '*',
						'Content-Type': 'application/json',
					},
					body: JSON.stringify(payload),
				});
				if (!response.ok) {
					throw new Error(`Failed to initialize shoppers: ${response.statusText}`);
				}
				const data = await response.json();
				console.log(`Successfully initialized ${data.successfulCount} shoppers.`);
			} catch (error) {
				console.error('Error initializing shoppers:', error);
				throw error; // Rethrow to be caught in the main try-catch
			}
		},
		clearStatus() {
			this.status = '';
			this.statusText = '';
		},
		async fetchAllShoppers() {
			let page = 1;
			let moreShoppers = true;
			this.allShoppers = [];
			while (moreShoppers) {
				let path = '/organizations/shoppers';
				const limit = 250;
				path += `?page=${page}&limit=${limit}`;

				console.log('Fetching shoppers:', path)

				const response = await fetch(`${URL_DOMAIN}${path}`, {
					method: 'GET',
					credentials: 'omit',
					headers: {
						Authorization: `Bearer ${localStorage.getItem('token')}`,
						'Access-Control-Allow-Origin': '*',
						'Content-Type': 'application/json',
					},
				});
				const data = await response.json();
				const shoppers = data.shoppers;
				if (shoppers?.length && shoppers.length > 0) {
					this.allShoppers.push(
						...shoppers.map((shopper) => ({
							id: shopper.id,
							name: `${shopper.first_name} ${shopper.last_name}`,
							email: shopper.email,
							loyalty_points: shopper.loyalty_points,
							loyalty_segment: shopper.loyalty_segment || 'N/A',
							all_data: shopper,
						}))
					);
					page++;
					if (shoppers.length !== this.limit) {
						moreShoppers = false;
					}
				} else {
					moreShoppers = false;
				}
			}
		},
		async modifyShopperPoints(shopper, amount, info) {
			customerIOTrackEvent('Modify Points');
			const modificationReason = `${info}: Added by Admin`;
			try {
				const response = await fetch(
					`${URL_DOMAIN}/loyalty-currencies/${shopper.loyalty_currency_id}/balance-change`,
					{
						method: 'POST',
						credentials: 'omit',
						headers: {
							Authorization: `Bearer ${localStorage.getItem('token')}`,
							'Access-Control-Allow-Origin': '*',
							'Content-Type': 'application/json',
						},
						body: JSON.stringify({
							info: modificationReason,
							balanceChange: amount,
							raleonUserId: shopper.raleon_user_id,
						}),
					}
				);
				if (!response.ok) {
					this.status = 'fail';
					this.statusText = 'Failed to modify shopper points for shopper ' + shopper.shopify_id;
					console.error('Error modifying shopper points');
				}
				const data = await response.json();
				console.log('Modification successful for shopper:', shopper.shopify_id, data);
			} catch (error) {
				this.status = 'fail';
				this.statusText = 'Failed to modify shopper points for shopper ' + shopper.shopify_id;
				console.error('Error modifying shopper points:', error);
			}
		},
	},
};
</script>


<style>
.pagination {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 20px;
}

.hidden {
	display: none;
}
</style>
