<template>

<div class="p-2 sm:p-7">
		<div class="flex flex-col sm:flex-row items-center justify-between">

			<EditableHeader :header-text="campaign.name"
				@updated-header="(header) => {campaign.name = header; saveCampaignName();}" v-if="!isLoading" />

			<div v-if="isLoading" role="status"
				class="p-3 space-y-4 bg-gray-300 dark:bg-gray-600 w-56 rounded-2xl shadow animate-pulse mb-6 sm:mb-0"></div>
			<div class="flex-grow"></div>

			<div class="flex w-full sm:w-auto justify-start">
				<div v-if="isLoading" role="status"
					class="p-3 space-y-4 bg-gray-300 dark:bg-gray-600 w-56 rounded-2xl shadow animate-pulse"></div>

				<div v-if="!isLoading">
					<div class="py-2 justify-start items-center gap-2.5 inline-flex">
						<!-- SVG and Launch Campaign Text -->
						<div class="flex items-center pr-5">
							<!--
							<svg xmlns="http://www.w3.org/2000/svg" height="24" fill="#4B5563" viewBox="0 -960 960 960"
								width="24">
								<path
									d="M284-506q14-28 29-54t33-52l-56-11-84 84 78 33Zm482-275q-70 2-149.5 41T472-636q-42 42-75 90t-49 90l114 113q42-16 90-49t90-75q65-65 104-144t41-149q0-4-1.5-8t-4.5-7q-3-3-7-4.5t-8-1.5ZM546-541q-23-23-23-56.5t23-56.5q23-23 57-23t57 23q23 23 23 56.5T660-541q-23 23-57 23t-57-23Zm-34 262 33 79 84-84-11-56q-26 18-52 32.5T512-279Zm351-534q8 110-36 214.5T688-399l20 99q4 20-2 39t-20 33L560-102q-15 15-36 11.5T495-114l-61-143-171-171-143-61q-20-8-24-29t11-36l126-126q14-14 33.5-20t39.5-2l99 20q95-95 199.5-139T819-857q8 1 16 4.5t14 9.5q6 6 9.5 14t4.5 16ZM157-321q35-35 85.5-35.5T328-322q35 35 34.5 85.5T327-151q-48 48-113.5 57T82-76q9-66 18-131.5T157-321Zm57 56q-17 17-23.5 41T180-175q25-4 49-10t41-23q12-12 13-29t-11-29q-12-12-29-11.5T214-265Z" />
							</svg>
							-->

							<div class="flex ml-2">
								<PrimaryButton cta="Publish" size="xs" @click="launchModelOpen = true" />
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>


		<div class="border-b border-gray-300 mt-6 mb-6">
			<div class="flex space-x-8">

				<div class="whitespace-nowrap py-4 px-1 font-medium text-sm focus:outline-none hover:border-b-2 hover:text-ralprimary-light hover:border-ralprimary-light transition-all duration-300 cursor-pointer"
				@click.stop="this.overview = true; this.insights = false; this.history = false;"
				:class="{'text-ralprimary-light border-ralprimary-light border-b-2 ': this.overview == true}"
				>
					Overview
				</div>

				<div class="whitespace-nowrap py-4 px-1 border-b-1 font-medium text-sm focus:outline-none hover:border-b-2 hover:text-ralprimary-light hover:border-ralprimary-light transition-all duration-300 cursor-pointer"
				@click.stop="this.overview = false; this.insights = true; this.history = false;"
				:class="{'text-ralprimary-light border-ralprimary-light border-b-2 ': this.insights == true}"
				>
					Performance Analytics
				</div>
			</div>
		</div>

		<div class="" v-if="this.overview">

			<div class="mt-6">
				<div>
					<h2 class="text-3xl sm:text-3xl font-sans font-medium opacity-70 text-ralblack-primary mb-4">Promotion</h2>
					<p>Setup the conditions for the gift with purchase promotion.</p>
				</div>
				<div class="bg-white rounded-2xl p-4 border shadow border-ralprimary-light border-opacity-50 hover:border-opacity-90 mt-4 flex-grow transition-all duration-300 ease-in-out overflow-hidden">
					<div class="">
						<div class=" text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Gift to give with purchase</div>

						<div
							class="mt-2 mr-2 mb-2 w-80 h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex cursor-pointer">
							<div class="w-full self-stretch justify-start items-center gap-2 flex">
								<input type="text"
									class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none cursor-pointer"
									placeholder="Choose product"
									maxlength="40" />

									<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368"><path d="m480-453-95-95q-11-11-27.5-11T329-548q-12 12-12 28.5t12 28.5l123 123q12 12 28 12t28-12l124-124q12-12 11.5-28T631-548q-12-11-28-11.5T575-548l-95 95Zm0 373q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-80q134 0 227-93t93-227q0-134-93-227t-227-93q-134 0-227 93t-93 227q0 134 93 227t227 93Zm0-320Z"/></svg>
							</div>
						</div>

						<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Free gift condition</div>

						<div
							class="mt-2 mr-2 mb-2 w-80 h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex cursor-pointer">
							<div class="w-full self-stretch justify-start items-center gap-2 flex">
								<input type="text"
									class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none cursor-pointer"
									placeholder="Minimum Order Amount"
									maxlength="40" />

									<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368"><path d="m480-453-95-95q-11-11-27.5-11T329-548q-12 12-12 28.5t12 28.5l123 123q12 12 28 12t28-12l124-124q12-12 11.5-28T631-548q-12-11-28-11.5T575-548l-95 95Zm0 373q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-80q134 0 227-93t93-227q0-134-93-227t-227-93q-134 0-227 93t-93 227q0 134 93 227t227 93Zm0-320Z"/></svg>
							</div>
						</div>
						<div
							class="mr-2 mb-2 w-40 h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex">
							<div class="w-full self-stretch justify-start items-center gap-2 flex">
								$
								<input type="text"
									class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
									maxlength="40" />
							</div>
						</div>
					</div>

					<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Backup gift</div>
					<div class="mt-1 mb-1 text-black text-sm font-normal font-['Inter']">In the event your primary gift goes out of stock.</div>

					<div
						class="mt-2 mr-2 mb-2 w-80 h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex cursor-pointer">
						<div class="w-full self-stretch justify-start items-center gap-2 flex">
							<input type="text"
								class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none cursor-pointer"
								placeholder="Choose product"
								maxlength="40" />

								<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368"><path d="m480-453-95-95q-11-11-27.5-11T329-548q-12 12-12 28.5t12 28.5l123 123q12 12 28 12t28-12l124-124q12-12 11.5-28T631-548q-12-11-28-11.5T575-548l-95 95Zm0 373q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-80q134 0 227-93t93-227q0-134-93-227t-227-93q-134 0-227 93t-93 227q0 134 93 227t227 93Zm0-320Z"/></svg>
						</div>
					</div>

					<div class="flex justify-end mt-4">
						<LightSecondaryButton cta="Save"/>
					</div>
				</div>
			</div>

			<div class="mt-6">
				<div>
					<h2 class="text-3xl sm:text-3xl font-sans font-medium opacity-70 text-ralblack-primary mb-4">Targeting</h2>
					<p>Specify who you want to see and receive this gift with purchase campaign.</p>
				</div>
				<div class="bg-white rounded-2xl p-4 border shadow border-ralprimary-light border-opacity-50 hover:border-opacity-90 mt-4 flex-grow transition-all duration-300 ease-in-out overflow-hidden">
					<div class="">
						<div class=" text-slate-800 text-base font-semibold font-['Inter'] leading-normal">Start if in Segment:</div>

						<div
							class="mt-2 mr-2 mb-2 w-80 h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex cursor-pointer">
							<div class="w-full self-stretch justify-start items-center gap-2 flex">
								<input type="text"
									class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none cursor-pointer"
									placeholder="All shoppers"
									maxlength="40" />

									<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368"><path d="m480-453-95-95q-11-11-27.5-11T329-548q-12 12-12 28.5t12 28.5l123 123q12 12 28 12t28-12l124-124q12-12 11.5-28T631-548q-12-11-28-11.5T575-548l-95 95Zm0 373q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-80q134 0 227-93t93-227q0-134-93-227t-227-93q-134 0-227 93t-93 227q0 134 93 227t227 93Zm0-320Z"/></svg>
							</div>
						</div>

						<div>
						<LightSecondaryButton cta="Add condition"/>
						</div>
					</div>

					<hr class="mt-4">
					<div class="mt-4 text-slate-800 text-base font-semibold font-['Inter'] leading-normal">End campaign for user when:</div>

					<div
						class="mt-2 mr-2 mb-2 w-80 h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex cursor-pointer">
						<div class="w-full self-stretch justify-start items-center gap-2 flex">
							<input type="text"
								class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none cursor-pointer"
								placeholder="Session ends"
								maxlength="40" />

								<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368"><path d="m480-453-95-95q-11-11-27.5-11T329-548q-12 12-12 28.5t12 28.5l123 123q12 12 28 12t28-12l124-124q12-12 11.5-28T631-548q-12-11-28-11.5T575-548l-95 95Zm0 373q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-80q134 0 227-93t93-227q0-134-93-227t-227-93q-134 0-227 93t-93 227q0 134 93 227t227 93Zm0-320Z"/></svg>
						</div>
					</div>

					<div class="flex justify-end mt-4">
						<LightSecondaryButton cta="Save"/>
					</div>
				</div>
			</div>

		</div>

		<div class="" v-if="this.insights">
			<div class="flex space-x-8 mb-4">
				<div class="w-1/2 bg-white rounded-2xl border border-violet-300 bg-opacity-75 shadow-md mb-2">
					<div class="px-4 pt-4 flex justify-between items-center mb-4">
						<div>Total Revenue</div>
					</div>

					<div class="px-4 pb-6 flex justify-start items-left flex-col sm:items-center sm:flex-row">
						<div class="flex flex-col justify-start mb-2">
							<span class="numberChart">$245,200</span>
						</div>
					</div>
				</div>

				<div class="w-1/2 bg-white rounded-2xl border border-violet-300 bg-opacity-75 shadow-md mb-2">
					<div class="px-4 pt-4 flex justify-between items-center mb-4">
						<div>Smart Offer Revenue Uplift</div>
					</div>

					<div class="px-4 pb-6 flex justify-start items-left flex-col sm:items-center sm:flex-row">
						<div class="flex flex-col justify-start mb-2">
							<span class="numberChart">$187,382</span>
						</div>
					</div>
				</div>

				<div class="w-1/2 bg-white rounded-2xl border border-violet-300 bg-opacity-75 shadow-md mb-2">
					<div class="px-4 pt-4 flex justify-between items-center mb-4">
						<div class="">Promotional Spend</div>
					</div>

					<div class="px-4 pb-6 flex justify-start items-left flex-col sm:items-center sm:flex-row">
						<div class="flex flex-col justify-start mb-2">
							<span class="numberChart">$18,640</span>
						</div>
					</div>
				</div>

				<div class="w-1/2 bg-white rounded-2xl border border-violet-300 bg-opacity-75 shadow-md mb-2">
					<div class="px-4 pt-4 flex justify-between items-center mb-4">
						<div class="">Total Offers Sent</div>
					</div>

					<div class="px-4 pb-6 flex justify-start items-left flex-col sm:items-center sm:flex-row">
						<div class="flex flex-col justify-start">
							<span class="numberChart">15,327</span>
						</div>
					</div>
				</div>
			</div>

			<div class="flex space-x-8">
				<div class="bg-white w-1/2 rounded-2xl items-center border border-violet-300 bg-opacity-85 overflow-hidden shadow-md mb-2 p-4 hover:shadow-lg transition-all duratio-300  flex flex-col justify-between">
					<div class="text-zinc-400 text-lg font-normal font-['Inter'] mb-4">Revenue per Variant</div>
					<img src="../images/OfferRevenueEx.png" width="411" />
				</div>

				<div class="bg-white w-1/2 rounded-2xl items-center border border-violet-300 bg-opacity-85 overflow-hidden shadow-md mb-2 p-4 hover:shadow-lg transition-all duratio-300  flex flex-col justify-between">
					<div class="text-zinc-400 text-lg font-normal font-['Inter'] mb-4">AOV per Variant</div>
					<img src="../images/VariantAOV.png" width="301" class="mb-4"/>
				</div>
			</div>
		</div>
	</div>

	<ModalBlank id="info-modal" :modalOpen="launchModelOpen" @close-modal="launchModelOpen = false">
		<div class="p-5 flex space-x-4">
			<div class="w-10 h-10 rounded-full flex items-center justify-center shrink-0 bg-indigo-100">
				<svg class="w-4 h-4 shrink-0 fill-current text-indigo-500" viewBox="0 0 16 16">
					<path
						d="M8 0C3.6 0 0 3.6 0 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8zm1 12H7V7h2v5zM8 6c-.6 0-1-.4-1-1s.4-1 1-1 1 .4 1 1-.4 1-1 1z" />
				</svg>
			</div>
			<div>
				<div class="mb-2">
					<div class="text-lg font-semibold text-ralblack-primary">Launch Promotion?</div>
				</div>
				<div class="text-sm mb-10">
					<div class="space-y-2">
						<p class="text-ralblack-secondary">You have made changes to your campaign settings but have not
							saved them. <br><br>Are you sure you want to launch your campaign before saving?</p>
					</div>
				</div>
				Klaviyo event name: stuff
				<div class="flex flex-wrap justify-end space-x-2">
					<CancelButton @click="launchModelOpen = false" cta="Cancel"></CancelButton>
					<button
						class="btn-sm bg-ralwarning-dark hover:bg-opacity-75 text-black rounded-full transitiona-all duration-300"
						@click="resetDirtySettings(); launch();">Yes, launch it.</button>
				</div>
			</div>
		</div>
	</ModalBlank>

	<ModalBlank id="info-modal" :modalOpen="generateTest" @close-modal="generateTest = false">
		<div class="p-5">
			<div class="mb-2">
				<div class="text-lg font-semibold text-ralblack-primary">Smart Offer Preview</div>
			</div>
			<div class="text-sm mb-10">
				<div class="space-y-2">
					<p class="text-ralblack-secondary">Below is a sample of personalized offers generated for review before launching the promotion.
					</p>
					<div class="flex items-center">
						What is a "Quailty Grade"?
						<Tooltip bg="dark" size="md" position="bottom" class="ml-2">
							<div class="text-xs whitespace-nowrap text-white">
								Quality grade is an indication of how confident the recommendation is. <br />
								A: Many customers match similar scenarios. <br />
								B: Quite a few customers match similar scenarios. <br/>
								C: Some customers match a similar scenarios.
							</div>
						</Tooltip>
					</div>
				</div>
			</div>

			<div v-if="testLoading">
				<div class="flex justify-center">
					<RaleonLoader />
				</div>
				<div>
					<div class="flex justify-center mb-2 mt-10">
						<div class="text-lg font-semibold text-ralblack-primary flex items-center">
							<svg width="24" height="26" viewBox="0 0 24 26" fill="none" xmlns="http://www.w3.org/2000/svg"
								class="mr-2" v-if="aiTextIndex == 0">
								<path
									d="M8.14838 4.24398L8.31532 4.32745C8.99 4.67522 9.53949 5.22471 9.88726 5.89939L9.97073 6.06632C10.3046 6.7271 11.2505 6.7271 11.5914 6.06632L11.6748 5.89939C12.0226 5.22471 12.5721 4.67522 13.2468 4.32745L13.4137 4.24398C14.0745 3.91011 14.0745 2.96416 13.4137 2.62334L13.2468 2.53988C12.5721 2.1921 12.0226 1.64262 11.6748 0.967932L11.5914 0.801C11.2575 0.140226 10.3115 0.140226 9.97073 0.801L9.88726 0.967932C9.53949 1.64262 8.99 2.1921 8.31532 2.53988L8.14838 2.62334C7.48761 2.95721 7.48761 3.90316 8.14838 4.24398Z"
									fill="#E86AD6" />
								<path
									d="M9.46298 10.7613C10.1446 10.4135 10.1446 9.4467 9.46298 9.09893L8.44747 8.57726C7.54325 8.11124 6.80597 7.37396 6.33995 6.46974L5.81829 5.45424C5.47051 4.7726 4.5037 4.7726 4.15592 5.45424L3.63426 6.46974C3.16824 7.37396 2.43095 8.11124 1.52673 8.57726L0.51123 9.09893C-0.17041 9.4467 -0.17041 10.4135 0.51123 10.7613L1.52673 11.283C2.43095 11.749 3.16824 12.4863 3.63426 13.3905L4.15592 14.406C4.5037 15.0876 5.47051 15.0876 5.81829 14.406L6.33995 13.3905C6.80597 12.4863 7.54325 11.749 8.44747 11.283L9.46298 10.7613Z"
									fill="#E86AD6" />
								<path
									d="M22.5115 14.9207L21.4821 14.3921C20.2371 13.7591 19.2424 12.7645 18.6095 11.5194L18.0809 10.49C17.6148 9.5719 16.6828 9.00155 15.6534 9.00155C14.624 9.00155 13.6919 9.5719 13.2259 10.49L12.6973 11.5194C12.0643 12.7645 11.0697 13.7591 9.82466 14.3921L8.79525 14.9207C7.87712 15.3867 7.30677 16.3188 7.30677 17.3482C7.30677 18.3776 7.87712 19.3096 8.79525 19.7756L9.82466 20.3043C11.0697 20.9372 12.0643 21.9319 12.6973 23.1769L13.2259 24.2063C13.6919 25.1244 14.624 25.6948 15.6534 25.6948C16.6828 25.6948 17.6148 25.1244 18.0809 24.2063L18.6095 23.1769C19.2424 21.9319 20.2371 20.9372 21.4821 20.3043L22.5115 19.7756C23.4296 19.3096 24 18.3776 24 17.3482C24 16.3188 23.4296 15.3867 22.5115 14.9207ZM21.5656 17.9185L20.5362 18.4471C18.8947 19.2818 17.587 20.5894 16.7524 22.2309L16.2237 23.2604C16.0638 23.5734 15.7716 23.6081 15.6534 23.6081C15.5351 23.6081 15.243 23.5734 15.083 23.2604L14.5544 22.2309C13.7197 20.5894 12.4121 19.2818 10.7706 18.4471L9.7412 17.9185C9.4282 17.7585 9.39342 17.4664 9.39342 17.3482C9.39342 17.2299 9.4282 16.9378 9.7412 16.7778L10.7706 16.2492C12.4121 15.4145 13.7197 14.1069 14.5544 12.4654L15.083 11.436C15.243 11.123 15.5351 11.0882 15.6534 11.0882C15.7716 11.0882 16.0638 11.123 16.2237 11.436L16.7524 12.4654C17.587 14.1069 18.8947 15.4145 20.5362 16.2492L21.5656 16.7778C21.8786 16.9378 21.9133 17.2299 21.9133 17.3482C21.9133 17.4664 21.8786 17.7585 21.5656 17.9185Z"
									fill="#E86AD6" />
							</svg>
							<TextFadeEffect :textList="aiLoadText" @text-complete="testLoading = false"
								@text-index="aiTextIndex = $event" :speed="2000"></TextFadeEffect>
						</div>
					</div>
				</div>
			</div>

			<div v-if="testLoading == false">
				<div class="bg-white rounded-2xl border shadow border-ralprimary-light border-opacity-50 mt-4 flex-grow transition-all duration-300 ease-in-out overflow-hidden">

					<div class="container rounded-2xl">
						<table class="min-w-full bg-white border border-gray-200">
						<thead>
							<tr class="w-full bg-gray-100 border-b border-gray-200">
							<th class="px-6 py-3 text-left text-sm font-medium text-gray-700">Customer</th>
							<th class="px-6 py-3 text-left text-sm font-medium text-gray-700">Product Recommendation</th>
							<th class="px-6 py-3 text-left text-sm font-medium text-gray-700">Offer</th>
							<th class="px-6 py-3 text-left text-sm font-medium text-gray-700">Quality Grade
							</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="variant in variants" :key="variant.id" class="bg-white border-t border-b border-ralbackground-light-line hover:bg-indigo-50 h-20"
							@mouseenter="onMouseEnter(variant.id)" @mouseleave="hoveredRowIndex = null"
							>
							<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ variant.cust }}</td>
							<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
								<div class="flex items-center">

									<img src="../images/blueshirt.png" alt="Variant Image" class="h-10 w-10 object-cover mr-2 rounded-lg" v-if="variant.cust == 'John Johnny'"/>
									<img src="../images/pinkhoodie.png" alt="Variant Image" class="h-10 w-10 object-cover mr-2 rounded-lg" v-if="variant.cust == 'Jane Janey'"/>
									<img src="../images/goldshirt.png" alt="Variant Image" class="h-10 w-10 object-cover mr-2 rounded-lg" v-if="variant.cust == 'Stephen Currison'"/>
									{{ variant.rec }}
								</div>
							</td>
							<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ variant.offer }}</td>
							<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
								<div :class="getGradientClass(variant.grade)" class="flex items-center justify-center w-10 h-10 text-black font-bold rounded-full">
									{{ variant.grade }}
								</div>
							</td>
							</tr>
						</tbody>
						</table>
					</div>

				</div>

				<div class="flex flex-wrap justify-end mt-4">
					<CancelButton @click="generateTest = false" cta="Done"></CancelButton>
				</div>
			</div>
		</div>
	</ModalBlank>

</template>

<script>

import * as Utils from '../../client-old/utils/Utils';
import CardContainer from '../components/CardContainer.ts.vue';
import LightSecondaryButton from '../components/LightSecondaryButton.ts.vue'
import PrimaryButton from '../components/PrimaryButton.ts.vue';
import ToggleItem from '../components/ToggleItem.ts.vue';
import StatusMessage from '../components/StatusMessage.ts.vue';
import LearnMoreText from '../components/LearnMoreText.ts.vue';
import CancelButton from '../components/CancelButton.ts.vue';
import EditableHeader from '../components/EditableHeader.ts.vue'
import RaleonLoader from '../components/RaleonLoader.ts.vue';
import TextFadeEffect from '../components/TextFadeEffect.ts.vue';
import ModalBlank from '../components/ModalBlank.vue';
import Tooltip from '../components/Tooltip.ts.vue';
import GeneratedText from '../components/GeneratedText.ts.vue';

import { useRoute } from 'vue-router';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	components: {
		CardContainer,
		LightSecondaryButton,
		StatusMessage,
		ToggleItem,
		PrimaryButton,
		LearnMoreText,
		CancelButton,
		EditableHeader,
		RaleonLoader,
		TextFadeEffect,
		ModalBlank,
		Tooltip,
		GeneratedText
	},
	async mounted() {

	},
	data() {
		return {
			insights: false,
			history: false,
			overview: true,
			partners: false,
			isLoading: false,
			generateTest: false,
			launchModelOpen: false,
			demoClick: false,
			campaign: { name: 'Untitled GWP Promo' },
			testLoading: true,
			hoveredRowIndex: 0,
			aiLoadText: ["Analyzing your audience", "Taking a look at your product...", "Playing match-maker...", "Clipping some coupons...", "Your Smart Offer preview is ready!"],
			aiTextIndex: 0,
		}
	},
	computed: {

	},
	methods: {
		saveCampaignName() {

		},
		getGradientClass(grade) {
			switch (grade) {
				case 'A':
				return 'bg-gradient-to-r from-green-400 to-green-500';
				case 'B':
				return 'bg-gradient-to-r from-blue-400 to-blue-500';
				case 'C':
				return 'bg-gradient-to-r from-orange-400 to-orange-500';
				case 'D':
				return 'bg-gradient-to-r from-red-400 to-red-500';
				case 'F':
				return 'bg-gradient-to-r from-gray-400 to-gray-500';
				default:
				return 'bg-gradient-to-r from-gray-400 to-gray-500';
			}
    	},
	},
}
</script>

<style scoped>
.no-focus-outline {
	box-shadow: none !important;
	outline: none !important;
}

.numberChart {
	color: rgba(32, 32, 32, 0.80);
		font-family: Inter;
		font-size: 3rem;
		font-style: normal;
		font-weight: 600;
		line-height: normal;
		letter-spacing: -2.4px;
		text-transform: uppercase;
}
</style>
