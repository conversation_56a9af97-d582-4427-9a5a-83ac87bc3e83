<template>
	<div class="bg-white rounded-2xl border shadow border-ralprimary-light border-opacity-50 p-4" v-if="isLoading">
		<div class="flex flex-col items-center justify-center p-4">
			<svg width="51" height="60" viewBox="0 0 34 40" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path
					d="M12.47 15.71C11.93 15.43 11.49 15 11.22 14.46L11.15 14.32C10.88 13.79 10.13 13.79 9.86 14.32L9.79 14.46C9.51 15 9.08 15.44 8.54 15.71L8.4 15.78C7.87 16.05 7.87 16.8 8.4 17.07L8.54 17.14C9.08 17.42 9.52 17.85 9.79 18.39L9.86 18.53C10.13 19.06 10.88 19.06 11.15 18.53L11.22 18.39C11.5 17.85 11.93 17.41 12.47 17.14L12.61 17.07C13.14 16.8 13.14 16.05 12.61 15.78L12.47 15.71Z"
					fill="#E86AD6" />
				<path
					d="M15.96 19.14L15.72 19.26C14.77 19.74 14.77 21.1 15.72 21.59L15.96 21.71C16.93 22.21 17.72 23 18.22 23.97L18.34 24.21C18.82 25.16 20.18 25.16 20.67 24.21L20.79 23.97C21.29 23 22.08 22.21 23.05 21.71L23.29 21.59C24.24 21.11 24.24 19.75 23.29 19.26L23.05 19.14C22.08 18.64 21.29 17.85 20.79 16.88L20.67 16.64C20.19 15.69 18.83 15.69 18.34 16.64L18.22 16.88C17.72 17.85 16.93 18.64 15.96 19.14Z"
					fill="#E86AD6" />
				<path
					d="M31.58 5.97999L31.26 5.81999C30.26 5.30999 29.46 4.50999 28.95 3.50999L28.79 3.18999C28.24 2.10999 27.14 1.43999 25.93 1.43999C24.72 1.43999 23.62 2.10999 23.06 3.18999L22.9 3.50999C22.39 4.50999 21.59 5.30999 20.59 5.81999L20.27 5.97999C19.19 6.52999 18.52 7.62999 18.52 8.84999C18.52 10.07 19.19 11.16 20.27 11.72L20.59 11.88C21.59 12.39 22.39 13.19 22.9 14.19L23.06 14.51C23.61 15.59 24.71 16.26 25.93 16.26C27.15 16.26 28.24 15.59 28.8 14.51L28.96 14.19C29.47 13.19 30.27 12.39 31.27 11.88L31.59 11.72C32.67 11.17 33.34 10.07 33.34 8.85999C33.34 7.64999 32.67 6.54999 31.59 5.98999L31.58 5.97999ZM30.22 9.03999L29.9 9.19999C28.33 9.99999 27.08 11.25 26.28 12.82L26.12 13.14C26.12 13.14 26.06 13.26 25.93 13.26C25.8 13.26 25.75 13.17 25.74 13.14L25.58 12.82C24.78 11.25 23.53 9.99999 21.96 9.19999L21.64 9.03999C21.64 9.03999 21.52 8.97999 21.52 8.84999C21.52 8.71999 21.61 8.66999 21.64 8.65999L21.96 8.49999C23.53 7.69999 24.78 6.44999 25.58 4.87999L25.74 4.55999C25.74 4.55999 25.8 4.43999 25.93 4.43999C26.06 4.43999 26.11 4.51999 26.12 4.55999L26.28 4.87999C27.08 6.44999 28.33 7.69999 29.9 8.49999L30.22 8.65999C30.22 8.65999 30.34 8.71999 30.34 8.84999C30.34 8.97999 30.25 9.02999 30.22 9.03999Z"
					fill="#E86AD6" />
				<path
					d="M30.5 14.93C29.67 14.93 29 15.6 29 16.43C29 23.6 23.17 29.43 16 29.43C8.83 29.43 3 23.6 3 16.43C3 9.25999 8.83 3.42999 16 3.42999C17.22 3.42999 18.43 3.59999 19.59 3.92999C20.39 4.15999 21.22 3.69999 21.44 2.89999C21.67 2.09999 21.21 1.26999 20.41 1.04999C18.98 0.639993 17.5 0.429993 16 0.429993C7.18 0.429993 0 7.60999 0 16.43C0 20.63 1.64 24.44 4.3 27.3L2.58 32.45C2.53 32.6 2.5 32.76 2.5 32.92C2.5 35.58 7.31 39.42 16 39.42C24.69 39.42 29.5 35.58 29.5 32.92C29.5 32.76 29.47 32.6 29.42 32.45L27.7 27.3C30.36 24.44 32 20.63 32 16.43C32 15.6 31.33 14.93 30.5 14.93ZM5.57 32.97L6.74 29.45C7.91 30.28 9.19 30.96 10.56 31.45L9.43 35.41C7.14 34.61 5.91 33.52 5.57 32.96V32.97Z"
					fill="#E86AD6" />
			</svg>
			<p class="text-ralprimary-dark font-bold md:text-xl lg:text-2xl sm:text-normal mt-4">Raleon Copilot is working on your Member Insights</p>
				<p class="text-ralblack-primary text-lg mt-4 flex items-center">If your store does not yet have orders, your insights will show up within 24 hours of your first order. <a href="https://docs.raleon.io/docs/segmentations" class="flex items-center ml-1 text-ralprimary-main hover:text-ralprimary-highlighted" target="_blank">Learn more
					<svg width="18" height="18" viewBox="0 0 18 18" fill="none" class="ml-1" xmlns="http://www.w3.org/2000/svg">
						<path fill-rule="evenodd" clip-rule="evenodd" d="M3.75 5.25C3.55109 5.25 3.36032 5.32902 3.21967 5.46967C3.07902 5.61032 3 5.80109 3 6V14.25C3 14.4489 3.07902 14.6397 3.21967 14.7803C3.36032 14.921 3.55109 15 3.75 15H12C12.1989 15 12.3897 14.921 12.5303 14.7803C12.671 14.6397 12.75 14.4489 12.75 14.25V9.75C12.75 9.33579 13.0858 9 13.5 9C13.9142 9 14.25 9.33579 14.25 9.75V14.25C14.25 14.8467 14.0129 15.419 13.591 15.841C13.169 16.2629 12.5967 16.5 12 16.5H3.75C3.15326 16.5 2.58097 16.2629 2.15901 15.841C1.73705 15.419 1.5 14.8467 1.5 14.25V6C1.5 5.40326 1.73705 4.83097 2.15901 4.40901C2.58097 3.98705 3.15326 3.75 3.75 3.75H8.25C8.66421 3.75 9 4.08579 9 4.5C9 4.91421 8.66421 5.25 8.25 5.25H3.75Z" fill="#5A16C9" fill-opacity="0.8"/>
						<path fill-rule="evenodd" clip-rule="evenodd" d="M10.5 2.25C10.5 1.83579 10.8358 1.5 11.25 1.5H15.75C16.1642 1.5 16.5 1.83579 16.5 2.25V6.75C16.5 7.16421 16.1642 7.5 15.75 7.5C15.3358 7.5 15 7.16421 15 6.75V3H11.25C10.8358 3 10.5 2.66421 10.5 2.25Z" fill="#5A16C9" fill-opacity="0.8"/>
						<path fill-rule="evenodd" clip-rule="evenodd" d="M16.2803 1.71967C16.5732 2.01256 16.5732 2.48744 16.2803 2.78033L8.03033 11.0303C7.73744 11.3232 7.26256 11.3232 6.96967 11.0303C6.67678 10.7374 6.67678 10.2626 6.96967 9.96967L15.2197 1.71967C15.5126 1.42678 15.9874 1.42678 16.2803 1.71967Z" fill="#5A16C9" fill-opacity="0.8"/>
					</svg>
				</a>
				</p>
		</div>
	</div>
	<div class="p-2 xl:mr-24" v-if="!isLoading">
		<div class="flex flex-wrap">
			<div
				class="w-[100%] lg:pr-4 pb-4"
				v-for="chart of dashboard.charts"
				:class="{ 'lg:w-[50%]': !chart.wide }"
			>
				<div
					v-if="isLoading"
					class="w-[100%] bg-white rounded-2xl shadow border border-violet-200 p-6 pl-2 pb-2"
				>
					<div class="animate-pulse">
						<div class="flex items-center space-x-4">
							<!-- Skeleton for an avatar or image -->
							<div
								class="h-10 w-10 bg-gray-300 rounded-full"
							></div>
							<!-- Skeleton for text lines -->
							<div class="flex-1 space-y-6 py-1">
								<div class="h-2 bg-gray-300 rounded"></div>
								<div class="space-y-3">
									<div class="grid grid-cols-3 gap-4">
										<div
											class="h-2 bg-gray-300 rounded col-span-2"
										></div>
										<div
											class="h-2 bg-gray-300 rounded col-span-1"
										></div>
									</div>
									<div class="h-2 bg-gray-300 rounded"></div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div
					v-if="!isLoading"
					class="w-[100%] bg-white rounded-2xl shadow border border-violet-200 p-6 pl-2 pb-2"
				>
					<div class="ml-4 flex">
						<div class="flex-col">
							<div
								class="text-zinc-400 text-lg font-normal font-['Inter']"
							>
								{{ chart.title }}
							</div>
							<div
								class="flex items-center"
								v-if="chart.titleValue"
							>
								<div
									class="text-slate-800 text-xl font-bold font-['Inter']"
								>
									{{
										getChartData(chart)?.[
											chart.titleValue.key
										]
									}}
								</div>
								<div
									v-if="chart.titleValue.subtitle"
									class="ml-2 text-zinc-400 text-sm font-normal font-['Inter']"
								>
									{{ chart.titleValue.subtitle }}
								</div>
							</div>
						</div>
						<div class="flex-grow"></div>

						<div v-if="chart.topRight.type === 'legend'">
							<div class="flex-col">
								<div
									v-for="series of chart.series"
									class="flex items-center align-center"
								>
									<div
										class="w-2.5 h-2.5 rounded-lg mr-2"
										:style="{ background: series.color }"
									></div>
									<div
										class="text-gray-500 text-sm font-normal font-['Inter']"
									>
										{{ series.name }}
									</div>
								</div>
							</div>
						</div>
						<div v-if="chart.topRight.type === 'trend'">
							<div class="flex flex-col items-end">
								<div class="flex">
									<div
										class="text-slate-800 text-base font-bold font-['Inter'] mr-2"
									>
										{{
											(
												(getChartData(chart)?.[
													chart.topRight.valueKey
												] || 0) * 100
											).toFixed(1)
										}}
										%
									</div>
									<div class="w-6 h-5 relative">
										<div
											v-if="
												getChartData(chart)?.[
													chart.topRight.valueKey
												] > 0
											"
											class="w-6 h-5 left-0 top-0 absolute bg-green-300 rounded-full"
										>
											<svg
												width="26"
												height="22"
												viewBox="0 0 26 22"
												fill="none"
												xmlns="http://www.w3.org/2000/svg"
											>
												<ellipse
													cx="12.8253"
													cy="11.1217"
													rx="12.5844"
													ry="10.8781"
													fill="#9DD098"
												/>
												<path
													d="M15.6491 9.96284L15.3114 10.2547C13.5045 11.8167 11.6974 13.3787 9.8901 14.941C9.5245 15.257 9.09252 15.3262 8.66107 15.1536C8.24542 14.9847 8.01732 14.6755 8.0779 14.3016C8.13233 14.0315 8.27976 13.7819 8.50092 13.5853C10.2483 12.0475 12.0184 10.5293 13.7816 9.00519L14.1156 8.71648C14.0078 8.70056 13.8991 8.68961 13.79 8.6837C13.0831 8.68096 12.3756 8.68688 11.6691 8.67914C11.1097 8.67368 10.7235 8.3408 10.7161 7.87814C10.7088 7.37359 11.0591 7.03525 11.6444 7.03206C13.2704 7.02295 14.8956 7.02189 16.5199 7.02887C17.1768 7.03206 17.5467 7.33898 17.5588 7.90046C17.5886 9.2942 17.6064 10.6881 17.612 12.0821C17.6141 12.5931 17.2295 12.8936 16.6421 12.8895C16.0879 12.885 15.7234 12.5771 15.7065 12.0808C15.6886 11.4915 15.687 10.9018 15.677 10.3121C15.6749 10.2192 15.6612 10.1281 15.6491 9.96284Z"
													fill="black"
												/>
											</svg>
										</div>
										<div
											v-if="
												!(
													getChartData(chart)?.[
														chart.topRight.valueKey
													] > 0
												)
											"
											class="w-6 h-5 left-0 top-0 absolute bg-red-300 rounded-full"
										>
											<svg
												width="26"
												height="22"
												viewBox="0 0 26 22"
												fill="none"
												xmlns="http://www.w3.org/2000/svg"
												style="
													transform: rotate(180deg);
												"
											>
												<ellipse
													cx="12.8253"
													cy="11.1217"
													rx="12.5844"
													ry="10.8781"
													fill=""
												/>
												<path
													d="M15.6491 9.96284L15.3114 10.2547C13.5045 11.8167 11.6974 13.3787 9.8901 14.941C9.5245 15.257 9.09252 15.3262 8.66107 15.1536C8.24542 14.9847 8.01732 14.6755 8.0779 14.3016C8.13233 14.0315 8.27976 13.7819 8.50092 13.5853C10.2483 12.0475 12.0184 10.5293 13.7816 9.00519L14.1156 8.71648C14.0078 8.70056 13.8991 8.68961 13.79 8.6837C13.0831 8.68096 12.3756 8.68688 11.6691 8.67914C11.1097 8.67368 10.7235 8.3408 10.7161 7.87814C10.7088 7.37359 11.0591 7.03525 11.6444 7.03206C13.2704 7.02295 14.8956 7.02189 16.5199 7.02887C17.1768 7.03206 17.5467 7.33898 17.5588 7.90046C17.5886 9.2942 17.6064 10.6881 17.612 12.0821C17.6141 12.5931 17.2295 12.8936 16.6421 12.8895C16.0879 12.885 15.7234 12.5771 15.7065 12.0808C15.6886 11.4915 15.687 10.9018 15.677 10.3121C15.6749 10.2192 15.6612 10.1281 15.6491 9.96284Z"
													fill="black"
												/>
											</svg>
										</div>
									</div>
								</div>
								<div
									class="text-right text-gray-500 text-sm font-normal font-['Inter'] uppercase tracking-wide"
								>
									{{ chart.topRight.comparison }}
								</div>
							</div>
						</div>
					</div>

					<div
						class="chart-container"
						:id="`dashboard-chart-${chart.key}`"
					></div>

					<div
						style="
							position: relative;
							left: 100%;
							margin-left: -20em;
							margin-top: -1em;
							height: 1em;
							width: 20em;
							background: white;
						"
					></div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import * as Utils from '../../client-old/utils/Utils';
import Highcharts from 'highcharts';
import { getMetric } from '../services/metrics.js';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	components: {},
	async mounted() {
		await this.fetchData();
		if(!this.isLoading){
			this.dashboard.charts.forEach((chart) => {
				switch (chart.type) {
					case 'highcharts-bar':
						return this.highchartBar(chart);
					case 'highcharts-pie':
						return this.highchartPie(chart);
				}
			});
		}
	},
	data() {
		return {
			isLoading: true,
			isHovering: false,
			dashboard: dashboards.find(
				(d) => d.key === 'loyalty'
			),
			data: {
				charts: []
			}
		};
	},
	methods: {
		async fetchData() {
			try {
				const metrics = {
					member_sales: [
						'non_member_sales_percent',
						'member_sales_percent'
					],
					members_segment: [
						'not_loyal',
						'new_users',
						'very_loyal',
						'some_loyalty'
					]
				};

				const metricPromises = [];

				for (const metric in metrics) {
					const globalColors = [
						'#8349fa',
						'#a73fcb',
						'#da6fd1',
						'#ffa3df'
					];
					const promise = getMetric(
						metric,
						'latest',
						'',
						'day',
						'sum'
					).then((data) => {
						console.log('data', data);
						if(data == "Metrics still loading...") return { isLoading: true };;
						let seriesData = {};
						const items = data.body.data;
						const lastItem = items[items.length - 1];
						seriesData = [];
						let counter = 0;
						metrics[metric].forEach((key) => {
							const value = lastItem.metrics[key]?.value || 0;
							const label = lastItem.metrics[key]?.label || '';
							seriesData.push({
								name: label,
								y: Math.round(value * 100) / 100,
								color: globalColors[counter]
							});
							if (metrics[metric].length > 2) {
								counter++;
							} else {
								counter = counter + 2;
							}
						});
						return {
							key: metric,
							series: seriesData
						};
					});

					metricPromises.push(promise);
				}

				const chartsData = await Promise.all(metricPromises);
				if (chartsData.some(data => data.isLoading)) {
					console.log('Metrics are still loading...');
					return;
        		}
				this.data.charts = chartsData;
				this.isLoading = false;
			} catch (error) {
				console.error('Error fetching data:', error);
			}
		},
		getChartData(chartConfig) {
			return this.data.charts.find((x) => x.key === chartConfig.key);
		},
		highchartBar(chart) {
			const chartData = this.getChartData(chart);
			const data = chartData.series;

			Highcharts.chart(`dashboard-chart-${chart.key}`, {
				colors: ['#ff79c6', '#9b59b6', '#8e44ad', '#34495e'],
				chart: {
					type: 'bar',
					style: {
						fontFamily: 'Inter'
					}
				},
				title: {
					text: ''
				},
				xAxis: {
					type: 'category',
					title: {
						text: null
					}
				},
				yAxis: {
					min: 0,
					title: {
						text: 'Number of Members',
						align: 'high'
					},
					labels: {
						overflow: 'justify'
					}
				},
				tooltip: {
					valueSuffix: ' members'
				},
				plotOptions: {
					bar: {
						dataLabels: {
							enabled: false
						}
					},
					series: {
						borderRadius: 20
					}
				},
				legend: {
					enabled: false
				},
				credits: {
					enabled: false
				},
				series: [
					{
						name: 'Members',
						data: chartData.series
					}
				]
			});
		},
		highchartPie(chart) {
			const chartData = this.getChartData(chart);
			const memberPercent = chartData.series[1].y;
			Highcharts.chart(`dashboard-chart-${chart.key}`, {
				colors: ['#da6fd1', '#a73fcb'],
				chart: {
					type: 'pie',
					style: {
						fontFamily: 'Inter'
					}
				},
				title: {
					verticalAlign: 'middle',
					floating: true,
					text: memberPercent + '%',
					y: 20,
					style: {
						fontSize: '3em',
						color: '#000000'
					}
				},
				plotOptions: {
					pie: {
						shadow: false,
						center: ['50%', '50%'],
						innerSize: '60%',
						dataLabels: {
							enabled: false,
							distance: -50,
							style: {
								fontWeight: 'bold',
								color: 'white'
							}
						},
						showInLegend: true
					}
				},
				tooltip: {
					enabled: true
				},
				series: [
					{
						name: 'Sales',
						data: chartData.series,
						size: '100%',
						dataLabels: {
							formatter: function () {
								return this.y > 5 ? this.point.name : null;
							}
						}
					}
				]
			});
		},
		getCurrentDateFormatted() {
			const now = new Date();
			now.setDate(now.getDate() + 1);

			const year = now.getFullYear();
			const month = (now.getMonth() + 1).toString().padStart(2, '0');
			const day = now.getDate().toString().padStart(2, '0');
			return `${year}-${month}-${day}`;
		},
		getDateYesterdayFormatted() {
			const now = new Date();
			now.setDate(now.getDate() - 1);

			const year = now.getFullYear();
			const month = (now.getMonth() + 1).toString().padStart(2, '0');
			const day = now.getDate().toString().padStart(2, '0');
			return `${year}-${month}-${day}`;
		}
	}
};

export const dashboards = [
	{
		key: 'loyalty',
		title: 'Loyalty Performance',
		charts: [
			{
				key: 'member_sales',
				type: 'highcharts-pie',
				title: 'Sales Last 30 Days',
				topRight: {
					type: 'none'
				},
				series: [
					{
						key: 'member_sales_percent',
						name: 'Member Sales',
						color: '#da6fd1'
					},
					{
						key: 'non_member_sales_percent',
						name: 'Non-Member Sales',
						color: '#a73fcb'
					}
				],
				titleValue: {
					key: 'value'
				}
			},
			{
				key: 'members_segment',
				type: 'highcharts-bar',
				title: 'Members in Segment',
				topRight: {
					type: 'none'
				},
				series: [
					{ key: 'new_users', name: 'New Users', color: '#ffa3df' },
					{ key: 'not_loyal', name: 'Not Loyal', color: '#da6fd1' },
					{
						key: 'some_loyalty',
						name: 'Some Loyalty',
						color: '#a73fcb'
					},
					{ key: 'very_loyal', name: 'Very Loyal', color: '#864cf9' }
				],
				titleValue: {
					key: 'value'
				}
			}
		]
	}
];
</script>
<style>
.apexcharts-theme-light {
	min-height: 400px;
}
</style>
