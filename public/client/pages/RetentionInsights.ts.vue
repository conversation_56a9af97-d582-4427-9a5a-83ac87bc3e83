<template>
	<div class="p-2 sm:p-7 mr-24 flex flex-col items-center justify-center" v-if="!isFeatureAvailable && !isTrialEnded">
		<img src="../images/VIPTeirPromo.png" width="800">
		<a class="text-ralprimary-dark font-bold md:text-xl lg:text-2xl sm:text-normal mt-4 font-[Inter]" href="/loyalty/settings/plans">Upgrade to Loyalty Plan</a>
		<p class="text-ralblack-primary text-lg mt-4 flex items-center font-[Inter] mb-4 w-1/2 text-center">
			Loyalty programs powered by Raleon help brands increase repeat purchase rate and customer lifetime value.
		</p>
		<PrimaryButton
					cta="Upgrade Loyalty Plan"
					size="xs"
					@click="() => this.$router.push('/loyalty/settings/plans')"
					/>
	</div>

	<div class="bg-gradient-to-tr from-[#4F3EAC] to-[#5E48F8] p-4 w-full justify-center shadow-lg flex items-center" v-if="!isFeatureAvailable && isTrialEnded">
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 14H11V9.8L12.6 11.4L14 10L10 6L6 10L7.4 11.4L9 9.8V14ZM10 20C8.61667 20 7.31667 19.7375 6.1 19.2125C4.88333 18.6875 3.825 17.975 2.925 17.075C2.025 16.175 1.3125 15.1167 0.7875 13.9C0.2625 12.6833 0 11.3833 0 10C0 8.61667 0.2625 7.31667 0.7875 6.1C1.3125 4.88333 2.025 3.825 2.925 2.925C3.825 2.025 4.88333 1.3125 6.1 0.7875C7.31667 0.2625 8.61667 0 10 0C11.3833 0 12.6833 0.2625 13.9 0.7875C15.1167 1.3125 16.175 2.025 17.075 2.925C17.975 3.825 18.6875 4.88333 19.2125 6.1C19.7375 7.31667 20 8.61667 20 10C20 11.3833 19.7375 12.6833 19.2125 13.9C18.6875 15.1167 17.975 16.175 17.075 17.075C16.175 17.975 15.1167 18.6875 13.9 19.2125C12.6833 19.7375 11.3833 20 10 20ZM10 18C12.2333 18 14.125 17.225 15.675 15.675C17.225 14.125 18 12.2333 18 10C18 7.76667 17.225 5.875 15.675 4.325C14.125 2.775 12.2333 2 10 2C7.76667 2 5.875 2.775 4.325 4.325C2.775 5.875 2 7.76667 2 10C2 12.2333 2.775 14.125 4.325 15.675C5.875 17.225 7.76667 18 10 18Z" fill="#FFA3DF"/>
        </svg>
        <div class="text-white ml-2">Your trial has expired and your program has been deactivated.</div>
        <div class="px-2 py-2 text-white text-sm border-[#FFF] border rounded-full ml-4 hover:text-black hover:bg-[#FFF] hover:cursor-pointer transitional-bg delay-200" @click="() => this.$router.push('/loyalty/settings/plans')">
                    Choose a Plan
        </div>
    </div>

	<div class="p-2 sm:p-7 mr-24 flex flex-col items-center justify-center" v-if="isFeatureAvailable && !isShopifyConnected">
		<img src="https://cdn.shopify.com/shopifycloud/brochure/assets/brand-assets/shopify-logo-primary-logo-456baa801ee66a0a435671082365958316831c9960c480451dd0330bcdae304f.svg" width="584">
		<h2 class="text-ralprimary-dark font-bold md:text-xl lg:text-2xl sm:text-normal mt-4 font-[Inter]">Connect Shopify for Retention Insights</h2>
		<p class="text-ralblack-primary text-lg mt-4 flex items-center font-[Inter] mb-4 w-1/2 text-center">
			To view retention metrics and customer purchase patterns, connect your Shopify store to enable data synchronization.
		</p>
		<PrimaryButton
					cta="Connect Shopify"
					size="xs"
					@click="() => this.$router.push('/integrations')"
					/>
	</div>

	<div class="p-2 sm:p-7 mr-24" v-if="isFeatureAvailable && isShopifyConnected">
		<div class="text-neutral-800 text-opacity-70 sm:text-4xl md:text-6xl lg:text-7xl font-normal font-['Open Sans']">Retention Insights</div>
		<div class="inline-flex mt-4 items-center cursor-pointer" @click="handleBackClick">
			<svg width="24" height="24" viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg"
				class="hover:text-ralprimary-dark transition-color duration-300">
				<path
					d="M15.3333 12L10.3333 17M10.3333 17L15.3333 22M10.3333 17H23.6667M32 17C32 8.71573 25.2843 2 17 2C8.71573 2 2 8.71573 2 17C2 25.2843 8.71573 32 17 32C25.2843 32 32 25.2843 32 17Z"
					stroke="#202020" stroke-opacity="0.8" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" />
			</svg>
			<span class="ml-3 text-lg transition-color duration-300">Analytics</span>
		</div>
		<div class="my-3">
			<LearnMoreText text="Read about customer retention strategies" url="https://docs.raleon.io/docs/cumulative-repeat-purchase-rate-by-cohort-chart" />
		</div>
		<div v-if="!hasData" class="flex flex-col items-center justify-center h-22 mb-2 mt-10">
			<svg xmlns="http://www.w3.org/2000/svg" height="52" viewBox="0 -960 960 960" width="52" fill="#5A16C9"><path d="M280-280h80v-280h-80v280Zm160 0h80v-400h-80v400Zm160 0h80v-160h-80v160ZM200-120q-33 0-56.5-23.5T120-200v-560q0-33 23.5-56.5T200-840h560q33 0 56.5 23.5T840-760v560q0 33-23.5 56.5T760-120H200Zm0-80h560v-560H200v560Zm0-560v560-560Z"/></svg>
			<p class="text-xl text-ralblack-primary mt-2">Your retention insights will show up within 24 hours.</p>
		</div>
		<div v-if="hasData">
			<!-- Purchase Path Analysis -->
			<!-- <div v-if="false" class="my-4 overflow-x-hidden bg-white rounded-2xl p-6">
				<h2 class="text-xl font-semibold mb-6">Customer Purchase Paths</h2>
				<PurchasePathChart :data="purchasePathJson" />
			</div> -->
			<div class="my-4 overflow-x-hidden bg-white rounded-2xl" v-if="!isLoadingCohort">
				<HeatmapChart
					@loading="handleLoading('repeat_purchase_monthly_cohort', $event)"
					:data="cohortData"
					:isLoading="isLoadingCohort"
					:chartTitle="'Cumulative Repeat Purchase Rate by Cohort'"
				/>
			</div>
			<!-- <div class="my-4 overflow-x-hidden bg-white rounded-2xl">
				<DualAxisChart
					:data="revenueCustomerData"
					:chartTitle="'Rolling 12-Month Average: Revenue and Active Customers'"
				/>
			</div> -->
			<div class="my-4 overflow-x-hidden bg-white rounded-2xl">
				<LineChart
						@loading="handleLoading('ltv_new_customer', $event)"
						:metricName="'ltv_new_customer'"
						:valueField="'average_ltv'"
						:valueLabel="'Average LTV'"
						:keyFieldArray="['average_ltv']"
						:customLabelsArray="['Average LTV']"
						:type="'time'"
						:chartTitle="'60 day LTV for New Users'"
						:keyLabel="''"
						:startDate="latest"
						:groupBy="'month'"
						:calculation="'sum'"
						:xAxisLabel="''"
						class="w-full"
					/>
			</div>
			<div class="my-4 overflow-x-hidden bg-white rounded-2xl" v-if="!isLoadingDiscount">
				<StackedBarChart
					@loading="handleLoading('discounts_purchase_number', $event)"
					:data="discountData"
					:chartTitle="'Discount Usage by Purchase Order'"
					:isLoading="isLoadingDiscount"
				/>
			</div>
		</div>
	</div>
</template>

<script>
import LearnMoreText from '../components/LearnMoreText.ts.vue';
import BarChart from '../components/charts/BarChart.ts.vue';
import LineChart from '../components/charts/LineChart.ts.vue';
import StackedBarChart from '../components/charts/StackedBarChart.ts.vue';
import HeatmapChart from '../components/charts/HeatmapChart.ts.vue';
import DualAxisChart from '../components/charts/DualAxisChart.ts.vue';
import PurchasePathChart from '../components/PurchasePathChart.vue';
import { getMetric } from '../services/metrics.js';
import { customerIOTrackEvent } from '../services/customerio.js';
import { isFeatureAvailable } from '../services/features';
import PrimaryButton from '../components/PrimaryButton.ts.vue';
import * as Utils from '../../client-old/utils/Utils';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	components: {
		LearnMoreText,
		BarChart,
		LineChart,
		StackedBarChart,
		HeatmapChart,
		DualAxisChart,
		PurchasePathChart,
		PrimaryButton
	},
	async mounted() {
		try {
			customerIOTrackEvent('Retention Insights Viewed');

			await this.checkShopifyConnection();

			if (this.isShopifyConnected) {
				this.fetchCohortData().catch();
				this.fetchTopProductsData().catch();

				// Fetch purchase path data
				const purchasePathData = await getMetric('product_sequence', 'latest', '', 'none', 'none');
				console.log('Raw purchasePathData:', purchasePathData);

				if (purchasePathData?.statusCode === 200 && purchasePathData.body?.data?.length > 0) {
					const pathData = purchasePathData.body.data[0]?.group_label;

					let processedData = {};

					if (typeof pathData === 'string') {
						try {
							processedData = JSON.parse(pathData);
						} catch(e) {
							console.error('Error parsing JSON:', e);
							processedData = {};
						}
					} else if (typeof pathData === 'object') {
						processedData = pathData;
					}

					// Ensure we have the proper structure
					if (Array.isArray(processedData)) {
						// If we got a flat array, convert it to the expected structure
						this.purchasePathJson = {
							'ALL': {
								products: processedData
							}
						};
					} else if (typeof processedData === 'object') {
						// If we have an object but it's missing the ALL category
						if (!processedData.ALL) {
							// Create ALL category with all unique products
							const allProducts = new Set();
							Object.values(processedData).forEach(category => {
								if (category.products) {
									category.products.forEach(product => {
										allProducts.add(JSON.stringify(product));
									});
								}
							});

							processedData.ALL = {
								products: Array.from(allProducts).map(p => JSON.parse(p))
							};
						}
						this.purchasePathJson = processedData;
					}

					console.log('Processed purchasePathJson:', this.purchasePathJson);
				} else {
					console.error('No purchase path data found');
					this.purchasePathJson = {};
				}

				// Fetch discount data
				try {
					this.isLoadingDiscount = true;
					const metricData = await getMetric('discounts_purchase_number', 'latest', '', 'purchase_number', 'none');
					console.log('metricData:', metricData);
					if (metricData.statusCode !== 200 || metricData.body.data.length === 0) {
						//this.hasData = false;
						return;
					}

					const data = metricData.body.data;
					const discountGroups = [...new Set(data.map(item => item.metrics.discount_group.value))];

					this.discountData = discountGroups.reduce((acc, group) => {
						acc[group] = [0, 0, 0];
						const groupData = data.filter(item => item.metrics.discount_group.value === group);
						groupData.forEach(item => {
							const purchaseIndex = parseInt(item.group_label) - 1;
							acc[group][purchaseIndex] = item.metrics.discount_percent.value;
						});
						return acc;
					}, {});
					console.log('discountData:', this.discountData);
				} catch (error) {
					console.error('Error fetching discount data:', error);
				} finally {
					this.isLoadingDiscount = false;
				}


				const revenueCustomerMetric = await getMetric('r12_revenue', 'latest', '', 'item_name', 'none');
				if (revenueCustomerMetric.statusCode === 200 && revenueCustomerMetric.body.data.length > 0) {
					const revenueCustomerData = revenueCustomerMetric.body.data;

					this.revenueCustomerData = revenueCustomerData.map((item) => {
						return {
							date: item.group_label,
							newRevenue: item.metrics.avg_new_customer_revenue.value,
							repeatRevenue: item.metrics.avg_repeat_customer_revenue.value,
							activeCustomers: item.metrics.avg_active_customers.value
						};
					});
					console.log('Revenue Customer Data:', this.revenueCustomerData);
				}
			}
		} catch (error) {
			console.error('Error fetching data:', error);
			this.hasData = false;
		}
	},
	data() {
		return {
			hasData: true,
			shopifyConnected: true,
			purchasePathJson: {},  // Changed from array to object
			isLoadingCohort: false,
			isLoadingDiscount: true,
			isLoadingTopProducts: true,
			discountData: {
			},
			topProductsData: {},
			productNames: [],
			revenueCustomerData: [
				{ date: '2023-05', newRevenue: 100000, repeatRevenue: 200000, activeCustomers: 14000 },
				{ date: '2023-07', newRevenue: 105000, repeatRevenue: 205000, activeCustomers: 14500 },
				{ date: '2023-09', newRevenue: 110000, repeatRevenue: 210000, activeCustomers: 15000 },
				{ date: '2023-11', newRevenue: 115000, repeatRevenue: 215000, activeCustomers: 15500 },
				{ date: '2024-01', newRevenue: 120000, repeatRevenue: 220000, activeCustomers: 16000 },
				{ date: '2024-03', newRevenue: 125000, repeatRevenue: 225000, activeCustomers: 16500 }
			],
			cohortData: []
		}
	},
	computed: {
		isFeatureAvailable() {
			return isFeatureAvailable('member-insights-dashboards');
		},
		isTrialEnded() {
			const freeTrialData = JSON.parse(localStorage.getItem('freeTrialData'));

			return freeTrialData != {} && !freeTrialData.hasPaidPlan && freeTrialData.daysLeft < 0
		},
		isShopifyConnected() {
			return this.shopifyConnected;
		}
	},
	methods: {
		async checkShopifyConnection() {
			try {
				const response = await fetch(`${URL_DOMAIN}/integration/shopify/connected`, {
					method: 'GET',
					headers: {
						'Content-Type': 'application/json',
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
					}
				});
				if (!response.ok) {
					throw new Error('Failed to check Shopify connection');
				}
				const data = await response.json();
				this.shopifyConnected = data.connected;
			} catch (error) {
				console.error('Error checking Shopify connection:', error);
				this.shopifyConnected = false;
			}
		},
		async fetchTopProductsData() {
			try {
				this.isLoadingTopProducts = true;
				const topProductsMetric = await getMetric('top_products_new_customers', 'latest', '', 'item_name', 'none');
				if (topProductsMetric.statusCode === 200 && topProductsMetric.body.data.length > 0) {
					const topProductsData = topProductsMetric.body.data;

					// Get unique product names and loyalty segments
					const productNames = [...new Set(topProductsData.map(item => item.group_label))];
					const segments = [...new Set(topProductsData.map(item => item.metrics.loyaltysegment.value))];

					// Initialize the data structure
					const segmentData = {};
					segments.forEach(segment => {
						segmentData[segment] = new Array(productNames.length).fill(0);
					});

					// Fill in the data
					topProductsData.forEach(item => {
						const productIndex = productNames.indexOf(item.group_label);
						const segment = item.metrics.loyaltysegment.value;
						segmentData[segment][productIndex] = item.metrics.total_orders.value;
					});

					this.topProductsData = segmentData;
					this.productNames = productNames;

					console.log('topProductsData:', this.topProductsData);
					console.log('productNames:', this.productNames);
				}
			} catch (error) {
				console.error('Error fetching top products data:', error);
			} finally {
				this.isLoadingTopProducts = false;
			}
		},
		async fetchCohortData() {
			try {
				this.isLoadingCohort = true;
				const response = await getMetric('repeat_purchase_monthly_cohort', 'latest', '', 'month', 'none');
				if (response.statusCode === 200 && response.body.data.length > 0) {
					const transformedData = [];

					// Get current date and date from one year ago
					const currentDate = new Date();
					const oneYearAgo = new Date(currentDate);
					oneYearAgo.setFullYear(currentDate.getFullYear() - 1);

					// Generate all cohort dates from current month back to same month last year
					const allCohorts = [];
					let date = new Date(currentDate);
					while (date >= oneYearAgo) {
						allCohorts.push(date.toISOString().substring(0, 7)); // YYYY-MM
						date.setMonth(date.getMonth() - 1);
					}

					// Create a map of existing cohort data
					const cohortDataMap = new Map();
					const cohortSizeMap = new Map();
					response.body.data.forEach(item => {
						const cohortDate = item.group_label.substring(0, 7);
						const cohortSize = item.metrics.cohort_size.value;
						let lastNonZeroRate = 0;
						const monthRates = [];

						// Calculate rates for all possible months
						for (let month = 0; month <= 12; month++) {
							let rate = 0;
							if (month > 0) {
								const convKey = `conv_${month}m`;
								const conversions = item.metrics[convKey]?.value || 0;
								rate = cohortSize > 0 ? conversions : 0;
								if (rate > 0) {
									lastNonZeroRate = rate;
								} else if (lastNonZeroRate > 0) {
									rate = lastNonZeroRate;
								}
							}
							monthRates.push(Number(rate.toFixed(4)));
						}
						cohortDataMap.set(cohortDate, monthRates);
						cohortSizeMap.set(cohortDate, cohortSize);
					});

					// Generate data for each cohort
					allCohorts.slice(1).forEach((cohortDate, index) => {
						const cohortMonth = new Date(cohortDate);
						const monthsSinceStart = Math.floor((currentDate - cohortMonth) / (1000 * 60 * 60 * 24 * 30));

						// Include month 12 only if cohort is 13+ months old
						const maxMonths = Math.min(12, monthsSinceStart);


						// Get existing rates or use zeros
						const existingRates = cohortDataMap.get(cohortDate) || Array(13).fill(0);

						// Get cohort size from existing data
						const cohortData = cohortDataMap.get(cohortDate);
						const cohortSize = cohortSizeMap.get(cohortDate) || 0;

						// Add entry for cohort size (month 0)
						transformedData.push({
							cohort: cohortDate,
							month: 0,
							rate: cohortSize,
							isCustomerCount: true
						});

						// Add entries for months 1 up to maxMonths
						for (let month = 1; month <= maxMonths; month++) {
							transformedData.push({
								cohort: cohortDate,
								month: month,
								rate: existingRates[month] / 100,
								isCustomerCount: false
							});
						}
					});

					// Sort by cohort date descending, then by month ascending
					this.cohortData = transformedData.sort((a, b) => {
						const cohortCompare = a.cohort.localeCompare(b.cohort);
						if (cohortCompare !== 0) return cohortCompare;
						return a.month - b.month;
					});
					console.log('Transformed cohort data:', this.cohortData);
				}
			} catch (error) {
				console.error('Error fetching cohort data:', error);
			} finally {
				this.isLoadingCohort = false;
			}
		},
		handleLoading(chart, isLoading) {
			//this.hasData = isLoading;
		},
		handleBackClick() {
			const isAiStrategist = this.$route.path.includes('ai-strategist');
			this.$router.push(isAiStrategist ? '/ai-strategist/analytics' : '/loyalty/analytics');
		},
		shouldShowSupport() {
			const userInfo = JSON.parse(localStorage.getItem('userInfo'));
			return userInfo?.roles?.includes('raleon-admin');
		}
	}
}
</script>
