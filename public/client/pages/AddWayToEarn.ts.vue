<template>
	<SuperModal>
		<SuperModalHeader :go-home-on-logo-click="true" :collapse-logo="true" :showHeaderBackground="true"
			:show-close-button="true" :back-button-href="backButtonHref">
			<span v-if="!isReward && !isGiveaway && !isPoints" class="w-full sm:w-auto text-3xl sm:text-5xl">{{ isEditing ? 'Edit' : 'Create' }} Way to Earn</span>
			<span v-if="!isReward && isPoints" class="w-full sm:w-auto text-3xl sm:text-5xl">{{ isEditing ? 'Edit' : 'Create' }} Points Program</span>
			<span v-if="isGiveaway" class="w-full sm:w-auto text-3xl sm:text-5xl">{{ isEditing ? 'Edit' : 'Create' }} Way to Enter</span>
			<span v-if="isShopReward" class="w-full sm:w-auto text-3xl sm:text-5xl">{{ isEditing ? 'Edit' : 'Create' }} Reward</span>
			<span v-if="isPerkReward" class="w-full sm:w-auto text-3xl sm:text-5xl">{{ isEditing ? 'Edit' : 'Create' }} Perk</span>

			<div class="flex-grow"></div>

			<div class="flex flex-row lg:items-start lg:justify-end items-center justify-between w-full md:w-1/2 lg:w-1/3">
				<div class="flex flex-row items-center pr-2 sm:pr-5 justify-center flex-grow h-20">
					<PreviewLoyaltyProgram />
				</div>
				<div class="flex-shrink-0 mt-2">
					<PrimaryButton
						cta="Save"
						@click="enable"
						size="small"
						:disabled="!formValid || isSaving"
						:show-spinner="isSaving"
						class="mr-2 sm:mr-7">
					</PrimaryButton>
				</div>
			</div>
		</SuperModalHeader>

		<div
			v-if="!this.isReward"
			class="w-full flex justify-center mt-6 overflow-auto">
			<div class="flex flex-col gap-4 items-center lg:ml-44">
				<template v-if="!this.isPoints">
					<WTECustomerAction
						:earn="this.earn"
						:is-shop-reward="this.isReward"
						:is-points="this.isPoints"
						:is-giveaway="isGiveaway"
						:wte-summary-data="this.wteSummaryData"
						:wte-data="this.wteData"
						:loading="this.isLoading"
						@condition-updated="updateEarnCondition"
						@earn-image-selected="updateEarnImage"
						@is-complete="updateCompletionStatus('WTECustomerAction', $event)"
					/>

					<!-- This is to show a reward box while the page is loading -->
					<template v-if="this.isLoading">
						<WTERewards
							:earn="this.earn"
							:reward="reward"
							:is-single-reward="this.isReward"
							:is-giveaway="isGiveaway"
							:index="0"
							:loading="true"
						/>
					</template>

					<template
						v-for="(reward, index) of this.earn.rewards"
						:key="`reward-${reward?.id}-condition-${earn?.condition?.id}`">
						<WTERewards
							:earn="this.earn"
							:reward="reward"
							:is-single-reward="this.isReward"
							:is-giveaway="isGiveaway"
							:is-points="this.isPoints"
							:index="index"
							:loading="false"
							@is-complete="updateRewardCompletionStatus(index, $event)"
							@reward-edited="updateReward"
							@reward-image-selected="updateRewardImage"
							@reward-deleted="deleteReward(index)"
						/>
					</template>
				</template>

				<template v-if="this.isPoints">
					<!-- This is to show a reward box while the page is loading -->
					<template v-if="this.isLoading">
						<WTERewards
							:earn="this.earn"
							:reward="reward"
							:is-single-reward="this.isReward"
							:index="0"
							:loading="true"
						/>
					</template>

					<template
						v-for="(reward, index) of this.earn.rewards"
						:key="`reward-${reward?.id}-condition-${earn?.condition?.id}`">
						<WTERewards
							:earn="this.earn"
							:reward="reward"
							:is-single-reward="this.isReward"
							:is-points="this.isPoints"
							:index="index"
							:loading="false"
							@is-complete="updateRewardCompletionStatus(index, $event)"
							@reward-edited="updateReward"
							@reward-image-selected="updateRewardImage"
							@reward-deleted="deleteReward(index)"
						/>

						<WTECustomerAction
							:earn="this.earn"
							:is-shop-reward="this.isReward"
							:is-points="this.isPoints"
							:wte-summary-data="this.wteSummaryData"
							:wte-data="this.wteData"
							:loading="this.isLoading"
							@condition-updated="updateEarnCondition"
							@earn-image-selected="updateEarnImage"
							@is-complete="updateCompletionStatus('WTECustomerAction', $event)"
						/>
					</template>
				</template>
				<div
					v-if="earn.rewards?.length < earn.condition?.maxRewardsForThisAction">
					<LightSecondaryButton
						cta="+ Add Reward"
						@click="addReward"
						class="mr-4 sm:mr-0 mb-10"
					></LightSecondaryButton>
				</div>
			</div>
			<WTELoyaltyPreview
				:earn="this.earn"
				class="w-full sticky hidden md:block"
			/>
		</div>

		<div v-if="this.isReward"
			class="w-full flex justify-center mt-6 overflow-auto">
			<div class="flex flex-col gap-4 items-center lg:ml-44">
				<ShopItemAdd
					v-if="!isPerkReward"
					:earn="this.earn"
					:loading="this.isLoading"
					@condition-updated="updateEarnCondition"
					@updated-shop-item-config="updateShopItemConfig"
					@is-complete="updateCompletionStatus('ShopItemAdd', $event)"
				/>
				<template v-if="this.isLoading">
					<WTERewards
						:earn="this.earn"
						:reward="reward"
						:is-single-reward="this.isReward"
						:is-perk-reward="isPerkReward"
						:index="0"
						:loading="true"
					/>
				</template>

				<template v-if="!this.isLoading">
					<WTERewards
						:earn="this.earn"
						:reward="this.earn.rewards?.[0]"
						:is-single-reward="this.isReward"
						:is-perk-reward="isPerkReward"
						:index="0"
						:loading="false"
						@reward-edited="updateReward"
						@reward-deleted="deleteReward"
						@is-complete="updateCompletionStatus('WTERewards', $event)"
					/>
				</template>
			</div>
			<WTELoyaltyPreview
				:earn="this.earn"
				:is-shop-reward="this.isReward"
				class="w-full sticky hidden md:block"
			/>
		</div>

		<StatusMessage :message=status.message :status=status.type @resetStatus="status.type = 'nope'"></StatusMessage>
	</SuperModal>
</template>

<script>

import * as Utils from '../../client-old/utils/Utils';
import SuperModal from '../components/SuperModal.ts.vue';
import SuperModalHeader from '../components/SuperModalHeader.ts.vue';
import StatusMessage from '../components/StatusMessage.ts.vue'
import PrimaryButton from '../components/PrimaryButton.ts.vue';
import Tooltip from '../components/Tooltip.ts.vue';
import WTESummary from '../components/WTESummary.ts.vue';
import PreviewLoyaltyProgram from '../components/PreviewLoyalty.ts.vue';
import { RECOMMENDATION_STATES } from './LoyaltyProgram.ts.vue';
import { customerIOTrackEvent } from '../services/customerio.js';
import WTECustomerAction from './subpages/WTECustomerAction.ts.vue';
import WTERewards from './subpages/WTERewards.ts.vue';
import WTELivePreview from './subpages/WTELivePreview.ts.vue';
import WayToEarnForm from './subpages/CampaignWTEEarnView.ts.vue';
import LightSecondaryButton from '../components/LightSecondaryButton.ts.vue';
import ShopItemAdd from './subpages/ShopItemAdd.ts.vue';
import WTELoyaltyPreview from './subpages/WTELoyaltyPreview.ts.vue';
import { saveRecommendationState, enableWteOrShopItem } from '../services/ways-to-earn.js';
const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	props: ['campaignId', 'wayToEarnId', 'isFoundationalCampaign', 'giveawayId'],
	components: {
		SuperModal,
		SuperModalHeader,
		StatusMessage,
		PrimaryButton,
		Tooltip,
		PreviewLoyaltyProgram,
		ShopItemAdd,
		WTESummary,
		WTECustomerAction,
		WayToEarnForm,
		WTERewards,
		WTELivePreview,
		LightSecondaryButton,
		WTELoyaltyPreview,
	},
	data() {
		const earn = {};

		return {
			earn,
			campaign: {},
			wteData: {},
			wteComplete: false,
			// isReward: this.$route.path.includes('shop-reward'),
			status: { 'message': null, type: 'nope' },
			localWayToEarnId: this.wayToEarnId,
			predictionsRunning: [],
			shopItemPredictionRunning: false,
			predictShopItemTextTimeout: null,
			predictWTETextTimeout: null,
			wteSummaryData: {},
			isLoading: true,
			formValid: false,
			earnCompletionStatus: {
				WTECustomerAction: false,
				WTERewards: [],
			},
			shopItemCompletionStatus: {
				ShopItemAdd: false,
				WTERewards: false,
			},
			stateEffectCompletionStatus: {
				WTERewards: false,
			},
			hasUnsavedChanges: false,
			isSaving: false
		}
	},
	created() {
		window.addEventListener('beforeunload', this.beforeUnloadHandler);
	},
	beforeDestroy() {
		window.removeEventListener('beforeunload', this.beforeUnloadHandler);
	},
	beforeRouteLeave(to, from, next) {
		const showDialog = this.formValid && this.hasUnsavedChanges;
		if (showDialog && !confirm('You have unsaved changes. Are you sure you want to leave?')) {
			next(false);
		} else {
			window.removeEventListener('beforeunload', this.beforeUnloadHandler);
			this.formValid = true;
			this.hasUnsavedChanges = false;
			next();
		}
	},
	async mounted() {
		this.isLoading = true;
		const response = await fetch(`${URL_DOMAIN}/loyalty-campaigns/${this.campaignId}`, {
			method: 'GET',
			credentials: 'omit',
			mode: 'cors',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${localStorage.getItem('token')}`,
			}
		});

		if (response.ok && response.status >= 200 && response.status < 300) {
			const result = await response.json();

			this.campaign = result;
			this.earn.campaign = result;
		}

		if (!this.localWayToEarnId && !this.isReward) {
			this.addReward();
		}

		if (this.localWayToEarnId && this.localWayToEarnId > 0 && !this.isReward) {
			const wteResponse = await fetch(`${URL_DOMAIN}/wte/${this.localWayToEarnId}/load`, {
				method: 'GET',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
			});

			if (wteResponse.ok && wteResponse.status >= 200 && wteResponse.status < 300) {
				const wteData = await wteResponse.json();
				this.earn = wteData.earn;
				this.earn.predictionComplete = true;
				this.earn.condition = wteData.condition; //Need to make this work with more than 1
				this.earn.rewards = [...wteData.rewards];
				this.earn.campaign = this.campaign;
			}
		}
		else if (this.localWayToEarnId && this.localWayToEarnId > 0 && this.isPerkReward) {
			const staticEffectResponse = await fetch(`${URL_DOMAIN}/static-effect/${this.localWayToEarnId}/load`, {
				method: 'GET',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
			});

			if (staticEffectResponse.ok && staticEffectResponse.status >= 200 && staticEffectResponse.status < 300) {
				const staticEffectData = await staticEffectResponse.json();
				this.earn.staticEffect = staticEffectData.staticEffect;
				// this.earn.staticEffectConfig = staticEffectData.staticEffectConfig;
				if (staticEffectData.staticEffect.name) {
					this.earn.rewards = [{
						...staticEffectData.rewards[0],
						name: staticEffectData.staticEffect.name,
						description: staticEffectData.staticEffect.description,
						imageURL: staticEffectData.staticEffect.imageURL,
						type: staticEffectData.staticEffect.type,
						externalLink: staticEffectData.staticEffect.externalLink,
					}]
				} else {
					this.earn.rewards = [...staticEffectData.rewards];
				}
				this.earn.campaign = this.campaign;
			}
		}
		else if (this.localWayToEarnId && this.localWayToEarnId > 0 && this.isReward) {
			const shopItemResponse = await fetch(`${URL_DOMAIN}/shopitem/${this.localWayToEarnId}/load`, {
				method: 'GET',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
			});

			if (shopItemResponse.ok && shopItemResponse.status >= 200 && shopItemResponse.status < 300) {
				const shopItemData = await shopItemResponse.json();
				this.earn.shopItem = shopItemData.shopitem;
				this.earn.shopItemConfig = shopItemData.shopItemConfig;
				if (shopItemData.shopitem.name) {
					this.earn.rewards = [{
						...shopItemData.rewards[0],
						name: shopItemData.shopitem.name,
						description: shopItemData.shopitem.description,
						imageURL: shopItemData.shopitem.imageURL,
					}]
				} else {
					this.earn.rewards = [...shopItemData.rewards];
				}
				this.earn.campaign = this.campaign;
			}
		}
		else if (this.isReward && !this.localWayToEarnId) {

			if (this.isPerkReward) {
				this.earn.staticEffect = {
					name: '',
					description: '',
					imageURL: '',
					externalLink: '',
				};
			} else {
				this.earn.shopItem = {
					name: '',
					description: '',
					imageURL: '',
				};
			}
		}

		this.$nextTick(() => {
			this.isLoading = false;
		});
	},
	watch: {
		'earn.rewards': {
			handler: async function (newVal, oldVal) {
				if (!this.isLoading) {
					this.hasUnsavedChanges = true;
				}
			},
			deep: true
		},
		'earn.name': {
			handler: function (newVal, oldVal) {
				if (!this.isLoading) {
					this.hasUnsavedChanges = true;
				}
			}
		},
		'earn.condition': {
			handler: function (newVal, oldVal) {
				if (!this.isLoading) {
					this.hasUnsavedChanges = true;

					// If the condition changes, we need to reset the rewards
					if (newVal?.id != oldVal?.id) {
						this.deleteAllRewards();
						this.addReward();
					}
				}
			},
			deep: true
		},
		'$route.params.wayToEarnId'(newVal, oldVal) {
			if (newVal !== oldVal) {
				this.localWayToEarnId = newVal;
			}
		}
	},
	computed: {
		isPoints() {
			return this.$route.path.includes('points');
		},
		isEditing() {
			return this.$route.path.includes('edit');
		},
		isReward() {
			return this.isShopReward || this.isPerkReward;
		},
		isShopReward() {
			return this.$route.path.includes('shop-reward');
		},
		isPerkReward() {
			return this.$route.path.includes('perk-reward');
		},
		isVipTier() {
			return this.campaign.vipTier || location.search.includes('vip');
		},
		isGiveaway() {
			return !!this.$route.query.giveawayId || this.giveawayId;
		},
		backButtonHref() {
			let idType = this.isShopReward ? 'si' : this.isPerkReward ? 'p' : 'wte'
			const scrollTo = this.localWayToEarnId ? `sto=${idType}-${this.localWayToEarnId}` : '';
			if (this.giveawayId) {
				return `/loyalty/program/giveaway/${this.$route.query.giveawayId}`;
			}
			if(this.isVipTier) {
				return '/loyalty/program/vip';
			}
			if(!this.isFoundationalCampaign){
				return `/loyalty/campaign/${this.campaignId}`;
			}

			return `/loyalty/program${scrollTo ? `?${scrollTo}` : ''}`;
		},
	},
	methods: {
		updateEarnCondition(condition) {
			this.earn.condition = condition;
		},
		updateReward(reward, index) {
			if (!this.earn.rewards) {
				this.earn.rewards = [reward];
				return;
			}

			if (this.earn.rewards[index] != reward) {
				this.hasUnsavedChanges = true;
			}

			Object.keys(reward).forEach(key => {
				//keep the id the same so the component doesn't do a full rerender
				if (key !== 'id') {
					this.earn.rewards[index][key] = reward[key];
				}
			});
			Object.keys(this.earn.rewards[index]).forEach(key => {
				if (!(key in reward) && key !== 'id') {
					delete this.earn.rewards[index][key];
				}
			});

		},
		deleteReward(index) {
			this.earn.rewards.splice(index, 1);
			this.earn.rewards = [...this.earn.rewards];
			if (Object.keys(this.earnCompletionStatus.WTERewards)?.length == index + 1) {
				delete this.earnCompletionStatus.WTERewards[index];
			}
			this.checkFormValidity();
		},
		deleteAllRewards() {
			this.earn.rewards = [];
			this.earnCompletionStatus.WTERewards = [];
		},
		addReward() {
			if (!this.earn.rewards) this.earn.rewards = [];
			this.earn.rewards.push({
				restrictions: [],
			});
			this.earnCompletionStatus.WTERewards.push(false);
		},
		updateEarnImage(url) {
			this.earn.imageURL = url;
		},
		updateRewardImage(url, index) {
			this.earn.rewards[index].imageURL = url;
		},
		updateShopItemConfig(config) {
			this.earn.shopItemConfig = config;
		},
		earnComplete(isComplete) {
			this.wteComplete = isComplete;
			const allRewardsComplete =
				Object.values(this.rewardsComplete).every(x => x) &&
				this.earn.rewards?.length === Object.keys(this.rewardsComplete).length;

			this.allComplete = this.wteComplete && allRewardsComplete;
		},
		rewardComplete(isComplete, index) {
			if(this.isReward) {
				this.allComplete = isComplete;
				return;
			}
			this.rewardsComplete[index] = isComplete;

			const allRewardsComplete =
				Object.values(this.rewardsComplete).every(x => x) &&
				this.earn.rewards.length === Object.keys(this.rewardsComplete).length;

			this.allComplete = this.wteComplete && allRewardsComplete;
		},
		async enable(showMessage = true) {
			this.isSaving = true;
			if (this.isPerkReward) {
				let success = await this.savePerkReward();
				if (success) {
					this.hasUnsavedChanges = false;
					if (showMessage) {
						this.status.type = 'success';
						this.status.message = 'Perk saved successfully.'
					}
				} else {
					this.hasUnsavedChanges = true;
					if (showMessage) {
						this.status.type = 'fail';
						this.status.message = 'Unable to enable. Please check that all information is provided.'
					}
				}
			} else if (this.isReward) {
				let success = await this.saveShopReward();
				if (success) {
					this.hasUnsavedChanges = false;
					if (showMessage) {
						this.status.type = 'success';
						this.status.message = 'Reward saved successfully.'
					}
				} else {
					this.hasUnsavedChanges = true;
					if (showMessage) {
						this.status.type = 'fail';
						this.status.message = 'Unable to enable. Please check that all information is provided.'
					}
				}
			} else {
				let success = await this.saveWayToEarn();
				if (success) {
					this.hasUnsavedChanges = false;
					if (showMessage) {
						this.status.type = 'success';
						this.status.message = `Way to ${this.isGiveaway ? 'enter' : 'earn'} saved successfully.`
					}
				}
				else {
					this.hasUnsavedChanges = true;
					if (showMessage) {
						this.status.type = 'fail';
						this.status.message = 'Unable to enable. Please check that all information is provided.'
					}
				}
			}

			this.isSaving = false;
		},
		async saveShopReward() {
			let payload = {
				shopitem: {
					name: this.earn.rewards[0].name,
					description: this.earn.rewards[0].description,
					imageURL: this.earn.rewards[0].imageURL,
				},
				shopItemConfig: this.earn.shopItemConfig,
				rewards: this.earn.rewards,
				campaignId: this.earn.campaign.id,
				currencyId: this.earn.campaign.loyaltyProgram.loyaltyCurrencies[0].id,
				programId: this.earn.campaign.loyaltyProgramId
			}
			if (this.localWayToEarnId && this.localWayToEarnId > 0) {
				payload.shopitem.id = this.localWayToEarnId;
			}

			if (this.$route.query.recommended === 'true') {
				payload.shopitem.active = true;
			}

			const result = await enableWteOrShopItem(payload, 'shopitem');
			if (this.$route.query.recommended === 'true') {
				await saveRecommendationState(
					this.localWayToEarnId,
					`loyalty-campaigns/${this.campaignId}/loyalty-redemption-shop-items`,
					RECOMMENDATION_STATES.APPROVED_RECOMMENDATION
				);
				customerIOTrackEvent('Reward is Live');
			}

			this.localWayToEarnId = result.shopItemId;
			return (result.status == 'success');
		},

		async savePerkReward() {
			let payload = {
				staticEffect: {
					name: this.earn.rewards[0].name,
					description: this.earn.rewards[0].description,
					imageURL: this.earn.rewards[0].imageURL,
					externalLink: this.earn.rewards[0].externalLink,
					type: this.earn.rewards[0].type,
				},
				// staticEffectConfig: this.earn.shopItemConfig,
				rewards: this.earn.rewards,
				campaignId: this.earn.campaign.id,
				currencyId: this.earn.campaign.loyaltyProgram.loyaltyCurrencies[0].id,
				programId: this.earn.campaign.loyaltyProgramId
			}
			if (this.localWayToEarnId && this.localWayToEarnId > 0) {
				payload.staticEffect.id = this.localWayToEarnId;
			}

			if (this.$route.query.recommended === 'true') {
				payload.staticEffect.active = true;
			}

			const result = await enableWteOrShopItem(payload, 'perk');
			// if (this.$route.query.recommended === 'true') {
			// 	await saveRecommendationState(
			// 		this.localWayToEarnId,
			// 		`loyalty-campaigns/${this.campaignId}/loyalty-redemption-shop-items`,
			// 		RECOMMENDATION_STATES.APPROVED_RECOMMENDATION
			// 	);
			// 	customerIOTrackEvent('Reward is Live');
			// }

			this.localWayToEarnId = result.staticEffectId;
			return (result.status == 'success');
		},
		async saveWayToEarn() {
			let payload = {
				earn: {
					name: this.earn.name,
					description: this.earn.description,
					imageURL: this.earn.imageURL,
				},
				condition: [this.earn.condition],
				rewards: this.earn.rewards,
				campaignId: this.earn.campaign.id,
				currencyId: this.earn.campaign.loyaltyProgram.loyaltyCurrencies[0].id,
				programId: this.earn.campaign.loyaltyProgramId
			}
			if (this.localWayToEarnId && this.localWayToEarnId > 0) {
				payload.earn.id = this.localWayToEarnId;
			}

			if (this.$route.query.recommended === 'true') {
				payload.earn.active = true;
			}

			if (this.$route.query.giveawayId) {
				payload.earn.giveawayId = this.$route.query.giveawayId;
			}
			const result = await enableWteOrShopItem(payload, 'wte');
			this.localWayToEarnId = result.wayToEarnId;
			if (this.$route.query.recommended === 'true') {
				await saveRecommendationState(
					this.localWayToEarnId,
					`loyalty-campaigns/${this.campaignId}/loyalty-earns`,
					RECOMMENDATION_STATES.APPROVED_RECOMMENDATION
				);
				customerIOTrackEvent('WTE is Live');
			}
			return (result.status == 'success');
		},
		updateCompletionStatus(componentName, isComplete) {
			if (this.isPerkReward) {
				this.stateEffectCompletionStatus[componentName] = isComplete;
			} else if (this.isReward) {
				this.shopItemCompletionStatus[componentName] = isComplete;
			} else {
				if (componentName === 'WTERewards') return;
				this.earnCompletionStatus[componentName] = isComplete;
			}
			this.checkFormValidity();
		},
		updateRewardCompletionStatus(index, isComplete) {
			this.earnCompletionStatus.WTERewards[index] = isComplete;
			this.checkFormValidity();
		},
		checkFormValidity() {
			if(this.isPoints) {
				this.earnCompletionStatus['WTECustomerAction'] = true; //Force this to true, as its hidden
			}

			if(this.isPerkReward) {
				this.stateEffectCompletionStatus['WTERewards'] = true; //Force this to true, as its hidden
			}

			let allComplete = true;
			let completionStatus = this.isPerkReward
				? this.stateEffectCompletionStatus
				: this.isReward
					? this.shopItemCompletionStatus
					: this.earnCompletionStatus;
			for (const [key, value] of Object.entries(completionStatus)) {
				if (Array.isArray(value)) {
					if (value.some(v => !v)) {
						allComplete = false;
						break;
					}
				} else if (!value) {
					allComplete = false;
					break;
				}
			}
			this.formValid = allComplete;
		},
		beforeUnloadHandler(event) {
			if (this.formValid && this.hasUnsavedChanges) {
				event.preventDefault();
				event.returnValue = '';
				return '';
			}
		},
	}
}
</script>
<style scoped>
.super-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 10000;

	background: linear-gradient(144deg, #FAF8F5 20.89%, #E2E8F8 53.59%, #BCCAFD 88.28%);
}

h1 {
	text-transform: uppercase;
	font-size: 3em;
	font-weight: 500;
	color: grey;
}

.big-button {
	font-size: 3em;
	border-radius: 1em;
	padding: 0 0.5em;
	text-transform: uppercase;
}

.big-button>svg {
	margin-right: 0.25em;
}

.test {
	width: 50%;
	padding: 0em 0.4em;
}

@media all and (max-width: 768px) {
	.test {
		width: 100%;
	}
}

.continue-button {
	box-sizing: border-box;

	/* Auto layout */
	display: flex;
	flex-direction: row;
	justify-content: flex-end;
	align-items: center;
	padding: 2px 22px;
	gap: 10px;

	height: 69px;
	right: 16px;
	top: calc(50% - 69px/2 - 395.5px);

	border: 2px solid #FFFFFF;
	border-radius: 110px;
}

.continue-button>span {
	/* DONE & CONTINUE */

	height: 65px;

	font-family: 'Inter';
	font-style: normal;
	font-weight: 500;
	font-size: 52px;
	line-height: 65px;
	/* identical to box height, or 125% */
	text-align: right;
	letter-spacing: -0.04em;

	color: rgba(255, 255, 255, 0.75);
}

.continue-button> :before {
	/* Inside auto layout */
	flex: none;
	order: 0;
	flex-grow: 0;
}

.spinner {
  border: 6px solid #f3f3f3;
  border-top: 6px solid #15803D;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.w-full.sticky {
	position: sticky;
	top: 0;
	height: calc(100vh - 140px);
}

</style>
