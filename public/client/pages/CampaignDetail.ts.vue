<template>
	<ProgramActive></ProgramActive>
	<div class="m-3 sm:m-10 sm:m-7">
		<div class="flex flex-col sm:flex-row items-center justify-between">

			<EditableHeader :header-text="campaign.name"
				@updated-header="(header) => {campaign.name = header; saveCampaignName();}" v-if="!isLoading" />
			<!--
							<div
							v-if="!isLoading"
							class="bg-white rounded-2xl px-3 py-2 ml-4">Pending</div> -->

			<div v-if="isLoading" role="status"
				class="p-3 space-y-4 bg-gray-300 dark:bg-gray-600 w-56 rounded-2xl shadow animate-pulse mb-6 sm:mb-0"></div>
			<div class="flex-grow"></div>

			<div class="flex w-full sm:w-auto justify-start">
				<div v-if="isLoading" role="status"
					class="p-3 space-y-4 bg-gray-300 dark:bg-gray-600 w-56 rounded-2xl shadow animate-pulse"></div>

				<div v-if="!isLoading">
					<div class="py-2 justify-start items-center gap-2.5 inline-flex">
						<!-- SVG and Launch Campaign Text -->
						<div class="flex items-center pr-5">
							<svg xmlns="http://www.w3.org/2000/svg" height="24" fill="#4B5563" viewBox="0 -960 960 960"
								width="24">
								<path
									d="M284-506q14-28 29-54t33-52l-56-11-84 84 78 33Zm482-275q-70 2-149.5 41T472-636q-42 42-75 90t-49 90l114 113q42-16 90-49t90-75q65-65 104-144t41-149q0-4-1.5-8t-4.5-7q-3-3-7-4.5t-8-1.5ZM546-541q-23-23-23-56.5t23-56.5q23-23 57-23t57 23q23 23 23 56.5T660-541q-23 23-57 23t-57-23Zm-34 262 33 79 84-84-11-56q-26 18-52 32.5T512-279Zm351-534q8 110-36 214.5T688-399l20 99q4 20-2 39t-20 33L560-102q-15 15-36 11.5T495-114l-61-143-171-171-143-61q-20-8-24-29t11-36l126-126q14-14 33.5-20t39.5-2l99 20q95-95 199.5-139T819-857q8 1 16 4.5t14 9.5q6 6 9.5 14t4.5 16ZM157-321q35-35 85.5-35.5T328-322q35 35 34.5 85.5T327-151q-48 48-113.5 57T82-76q9-66 18-131.5T157-321Zm57 56q-17 17-23.5 41T180-175q25-4 49-10t41-23q12-12 13-29t-11-29q-12-12-29-11.5T214-265Z" />
							</svg>
							<span class="ml-2">Launch Campaign</span>
						</div>
						<ToggleItem @toggleChange="campaignLauncher" :state="this.campaign.active" showLabel=true
							onLabel="Active" offLabel="Inactive"></ToggleItem>
					</div>
					<div class="flex mt-1 justify-end">
						<PreviewLoyaltyProgram />
					</div>
				</div>
			</div>
		</div>

		<div class="text-sm" v-if="abtest == 'true'">
			AB Test Example / {{  campaign.name }}
		</div>

		<div v-if="showAi"
			class="mt-12 bg-white bg-opacity-75 rounded-2xl shadow border border-ralprimary-light border-opacity-50 hover:border-opacity-90 hover:shadow-lg flex-grow transition-all duration-300 ease-in-out overflow-hidden"
			:class="{'border-opacity-90': isAiOpen}" :style="{
				height: isAiOpen ?
					(aiCampaignStep === 1 ? '20em' :
						aiCampaignStep === 2 ? '10' : aiHeight)
					: '4em'
			}">
			<div class="flex items-center p-4 cursor-pointer" @click="isAiOpen = !isAiOpen">
				<div class="text-zinc-900 text-xl font-normal font-['Open Sans'] leading-loose flex-grow truncate">


					<div class="flex">
						<svg width="24" height="26" viewBox="0 0 24 26" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path
								d="M8.14838 4.24398L8.31532 4.32745C8.99 4.67522 9.53949 5.22471 9.88726 5.89939L9.97073 6.06632C10.3046 6.7271 11.2505 6.7271 11.5914 6.06632L11.6748 5.89939C12.0226 5.22471 12.5721 4.67522 13.2468 4.32745L13.4137 4.24398C14.0745 3.91011 14.0745 2.96416 13.4137 2.62334L13.2468 2.53988C12.5721 2.1921 12.0226 1.64262 11.6748 0.967932L11.5914 0.801C11.2575 0.140226 10.3115 0.140226 9.97073 0.801L9.88726 0.967932C9.53949 1.64262 8.99 2.1921 8.31532 2.53988L8.14838 2.62334C7.48761 2.95721 7.48761 3.90316 8.14838 4.24398Z"
								fill="#E86AD6" />
							<path
								d="M9.46298 10.7613C10.1446 10.4135 10.1446 9.4467 9.46298 9.09893L8.44747 8.57726C7.54325 8.11124 6.80597 7.37396 6.33995 6.46974L5.81829 5.45424C5.47051 4.7726 4.5037 4.7726 4.15592 5.45424L3.63426 6.46974C3.16824 7.37396 2.43095 8.11124 1.52673 8.57726L0.51123 9.09893C-0.17041 9.4467 -0.17041 10.4135 0.51123 10.7613L1.52673 11.283C2.43095 11.749 3.16824 12.4863 3.63426 13.3905L4.15592 14.406C4.5037 15.0876 5.47051 15.0876 5.81829 14.406L6.33995 13.3905C6.80597 12.4863 7.54325 11.749 8.44747 11.283L9.46298 10.7613Z"
								fill="#E86AD6" />
							<path
								d="M22.5115 14.9207L21.4821 14.3921C20.2371 13.7591 19.2424 12.7645 18.6095 11.5194L18.0809 10.49C17.6148 9.5719 16.6828 9.00155 15.6534 9.00155C14.624 9.00155 13.6919 9.5719 13.2259 10.49L12.6973 11.5194C12.0643 12.7645 11.0697 13.7591 9.82466 14.3921L8.79525 14.9207C7.87712 15.3867 7.30677 16.3188 7.30677 17.3482C7.30677 18.3776 7.87712 19.3096 8.79525 19.7756L9.82466 20.3043C11.0697 20.9372 12.0643 21.9319 12.6973 23.1769L13.2259 24.2063C13.6919 25.1244 14.624 25.6948 15.6534 25.6948C16.6828 25.6948 17.6148 25.1244 18.0809 24.2063L18.6095 23.1769C19.2424 21.9319 20.2371 20.9372 21.4821 20.3043L22.5115 19.7756C23.4296 19.3096 24 18.3776 24 17.3482C24 16.3188 23.4296 15.3867 22.5115 14.9207ZM21.5656 17.9185L20.5362 18.4471C18.8947 19.2818 17.587 20.5894 16.7524 22.2309L16.2237 23.2604C16.0638 23.5734 15.7716 23.6081 15.6534 23.6081C15.5351 23.6081 15.243 23.5734 15.083 23.2604L14.5544 22.2309C13.7197 20.5894 12.4121 19.2818 10.7706 18.4471L9.7412 17.9185C9.4282 17.7585 9.39342 17.4664 9.39342 17.3482C9.39342 17.2299 9.4282 16.9378 9.7412 16.7778L10.7706 16.2492C12.4121 15.4145 13.7197 14.1069 14.5544 12.4654L15.083 11.436C15.243 11.123 15.5351 11.0882 15.6534 11.0882C15.7716 11.0882 16.0638 11.123 16.2237 11.436L16.7524 12.4654C17.587 14.1069 18.8947 15.4145 20.5362 16.2492L21.5656 16.7778C21.8786 16.9378 21.9133 17.2299 21.9133 17.3482C21.9133 17.4664 21.8786 17.7585 21.5656 17.9185Z"
								fill="#E86AD6" />
						</svg>
						<div
							class="text-ralbackground-dark-secondary text-xl font-semibold font-['Inter'] leading-normal tracking-wide items-center ml-4">
							Campaign Copilot</div>
					</div>

				</div>
				<div class="flex-grow"></div>
				<div v-if="isAiOpen">
					<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path d="M12 15.3751L6 9.3751L7.4 7.9751L12 12.5751L16.6 7.9751L18 9.3751L12 15.3751Z"
							fill="#9F96E4" />
					</svg>
				</div>
				<div v-if="!isAiOpen">
					<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path d="M15.375 12L9.37498 18L7.97498 16.6L12.575 12L7.97498 7.4L9.37498 6L15.375 12Z"
							fill="#989898" />
					</svg>
				</div>
			</div>
			<div class="transition-all p-4" :class="isAiOpen ? 'opacity-100' : 'opacity-0'"
				:style="{height: isAiOpen ? (openHeight ? openHeight : '100%') : '0'}">


				<transition name="fade" mode="out-in">
					<div key="transitionWrapper">
						<div v-if="aiCampaignStep == 1" key="step1" class="flex flex-col h-[10em]">
							<div
								class="flex items-center mt-8 text-ralbackground-dark-secondary text-lg font-semibold font-['Inter'] leading-normal">
								What is the goal of this campaign?
								<Tooltip bg="dark" size="md" position="bottom" class="ml-2">
									<div class="text-xs whitespace-nowrap text-white">Copilot will use its loyalty expertise
										to give you a starting campaign based on your goal.</div>
								</Tooltip>
							</div>
							<div
								class="w-full h-28 px-4 py-3 mt-3 bg-white rounded-lg border border-gray-400 justify-start items-start gap-2 inline-flex">
								<div class="w-full h-full self-stretch justify-start items-center gap-2 flex">
									<textarea v-model="aiCampaignUserPrompt"
										class="no-focus-outline border-none w-full h-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal border-none p-0 resize-none focus:outline-none placeholder:font-semibold placeholder:italic placeholder:text-ralgray-main"
										placeholder="Write as the Chief Marketing Officer of a brand. For example: I want a valentines day campaign that brings my new years customers back"></textarea>
								</div>
							</div>

							<div class="flex-grow"></div>


							<div class="flex justify-end mt-8">
								<CancelButton cta="Cancel" @click="isAiOpen = false"></CancelButton>
								<LightSecondaryButton cta="Generate" @click="setAIStep(2)" />
							</div>
						</div>

						<div v-if="aiCampaignStep == 2" key="step2" class="flex flex-col h-[10em] justify-center">
							<div class="flex items-center justify-center flex-col mt-4">
								<RaleonLoader />
								<div
									class="flex items-center mt-8 mb-4 text-ralbackground-dark-secondary text-base font-semibold font-['Inter'] leading-normal">
									<TextFadeEffect :textList="useCopilotThinkText" /><br />
								</div>
							</div>
						</div>

						<div v-if="aiCampaignStep == 3" key="step3" class="flex flex-col h-auto" ref="aiTextContainer">
							<div
								class="flex items-center mt-8 text-ralbackground-dark-secondary text-lg font-semibold font-['Inter'] leading-normal">
								{{ userName }}, here's what Copilot came up with for you...
							</div>

							<GeneratedText class="mt-6" :htmlText="getFullSummary(aiResult)"></GeneratedText>

							<div class="flex-grow"></div>

							<div class="flex justify-end mt-8">
								<div class="flex mr-6 text-ralprimary-dark font-['Inter'] text-sm leading-normal items-center hover:underline cursor-pointer"
									@click.stop="resetAICampaign()">Edit Campaign Prompt</div>
								<CancelButton cta="Cancel" @click="isAiOpen = false"></CancelButton>
								<LightSecondaryButton cta="Add Recommendations" @click="setAIStep(4)" />
							</div>
						</div>

						<div v-if="aiCampaignStep == 4" key="step2" class="flex flex-col h-[10em] justify-center">
							<div class="flex items-center justify-center flex-col mt-4">
								<RaleonLoader />
								<div
									class="flex items-center mt-8 mb-4 text-ralbackground-dark-secondary text-base font-semibold font-['Inter'] leading-normal">
									<TextFadeEffect :textList="useCopilotDoneText" /><br />
								</div>
							</div>
						</div>

					</div>
				</transition>
			</div>
		</div>

		<div class="my-4 flex mb-10">
			<!--
			<span class="text-neutral-800 text-opacity-80 text-sm font-medium font-['Inter']">Not sure how to best use campaigns?</span>
			<span class="text-neutral-500 text-sm font-medium font-['Inter']">&nbsp;</span>
			<a class="text-violet-800 text-sm font-medium font-['Inter'] cursor-pointer hover:underline">Read about loyalty campaigns.</a>
			<div class="w-4 h-4 relative">
				<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path fill-rule="evenodd" clip-rule="evenodd" d="M3.75 5.25C3.55109 5.25 3.36032 5.32902 3.21967 5.46967C3.07902 5.61032 3 5.80109 3 6V14.25C3 14.4489 3.07902 14.6397 3.21967 14.7803C3.36032 14.921 3.55109 15 3.75 15H12C12.1989 15 12.3897 14.921 12.5303 14.7803C12.671 14.6397 12.75 14.4489 12.75 14.25V9.75C12.75 9.33579 13.0858 9 13.5 9C13.9142 9 14.25 9.33579 14.25 9.75V14.25C14.25 14.8467 14.0129 15.419 13.591 15.841C13.169 16.2629 12.5967 16.5 12 16.5H3.75C3.15326 16.5 2.58097 16.2629 2.15901 15.841C1.73705 15.419 1.5 14.8467 1.5 14.25V6C1.5 5.40326 1.73705 4.83097 2.15901 4.40901C2.58097 3.98705 3.15326 3.75 3.75 3.75H8.25C8.66421 3.75 9 4.08579 9 4.5C9 4.91421 8.66421 5.25 8.25 5.25H3.75Z" fill="#202020" fill-opacity="0.8"/>
				<path fill-rule="evenodd" clip-rule="evenodd" d="M10.5 2.25C10.5 1.83579 10.8358 1.5 11.25 1.5H15.75C16.1642 1.5 16.5 1.83579 16.5 2.25V6.75C16.5 7.16421 16.1642 7.5 15.75 7.5C15.3358 7.5 15 7.16421 15 6.75V3H11.25C10.8358 3 10.5 2.66421 10.5 2.25Z" fill="#202020" fill-opacity="0.8"/>
				<path fill-rule="evenodd" clip-rule="evenodd" d="M16.2803 1.71967C16.5732 2.01256 16.5732 2.48744 16.2803 2.78033L8.03033 11.0303C7.73744 11.3232 7.26256 11.3232 6.96967 11.0303C6.67678 10.7374 6.67678 10.2626 6.96967 9.96967L15.2197 1.71967C15.5126 1.42678 15.9874 1.42678 16.2803 1.71967Z" fill="#202020" fill-opacity="0.8"/>
				</svg>
			</div>
			-->
		</div>
		<div class="flex justify-between">
			<div class="text-zinc-500 text-2xl sm:text-5xl font-medium font-['Inter'] leading-10">Settings</div>
		</div>

		<div v-if="isLoading" role="status"
			class="w-full p-4 space-y-4 border bg-white border-ralprimary-light opacity-75 border-opacity-50 rounded-2xl shadow animate-pulse mt-2">
			<div class="flex items-center justify-between">
				<div class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24"></div>
				<div class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24"></div>
				<div class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24"></div>
				<div class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24"></div>
			</div>
		</div>

		<div v-if="!isLoading"
			class="bg-white rounded-2xl border shadow border-ralprimary-light border-opacity-50 hover:border-opacity-90 hover:shadow-lg mt-10 flex-grow transition-all duration-300 ease-in-out overflow-hidden"
			:class="{'border-opacity-75 shadow-lg': isOpen}" :style="{'height': isOpen ? scheduleType == 'Duration' ? '32em' : '28em': 'auto'}">

			<div class="accordion-header px-4 py-2 cursor-pointer" @click="toggleAccordion">
				<div class="flex justify-between items-center">
					<div class="grid grid-cols-3 gap-4 w-full">
						<div v-show="!isOpen">
							<div
								class="text-slate-800 text-base font-semibold font-['Open Sans'] leading-normal tracking-wide">
								Type</div>
							<div class="text-neutral-700 text-sm font-normal font-['Inter']">{{ getScheduleType() }}</div>
						</div>
						<div v-show="!isOpen">
							<div
								class="text-slate-800 text-base font-semibold font-['Open Sans'] leading-normal tracking-wide">
								Schedule</div>
							<div v-if="campaign.startdate || campaign.enddate"
								class="text-neutral-700 text-sm font-normal font-['Inter']" style="text-wrap: balance;">{{
									campaign.startdate || 'Now' }} - {{ campaign.enddate || 'Ongoing' }}</div>
							<div v-else-if="campaign.evergreen" class="text-neutral-700 text-sm font-normal font-['Inter']">
								Ongoing</div>
							<div v-else class="text-neutral-700 text-sm font-normal font-['Inter']">Not Specified</div>
						</div>
						<div v-show="!isOpen">
							<div
								class="text-slate-800 text-base font-semibold font-['Open Sans'] leading-normal tracking-wide">
								Target Members</div>
							<div class="text-neutral-700 text-sm font-normal font-['Inter']">{{ getSegmentLabel() }}</div>
						</div>
					</div>
					<svg class="transition-all duration-300" :class="{'rotate-90 mt-3': isOpen}" width="24" height="24"
						viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path d="M15.375 12L9.37498 18L7.97498 16.6L12.575 12L7.97498 7.4L9.37498 6L15.375 12Z"
							fill="#989898"></path>
					</svg>
				</div>
			</div>

			<!-- Accordion Body -->
			<div v-if="isOpen" class="accordion-body px-4 mb-4 transition-all duration-500"
				:style="{'max-height': isOpen ? scheduleType == 'Duration' ? '32em' : '28em': '0', opacity: isOpen ? 1 : 0}">
				<div class="text-slate-800 text-base font-semibold font-['Open Sans'] leading-normal tracking-wide">Schedule
				</div>
				<div class="mt-3 text-slate-800 text-xs font-normal font-['Open Sans'] leading-none">
					Set whether your campaign is going to run for a specific period of time (duration), or until you stop it
					(ongoing).
				</div>

				<SuperDropdown class="mt-4" :options="['Duration', 'Ongoing']" placeholder="Select a schedule"
					:model-value="scheduleType" @update:model-value="value => setScheduleType(value)" />

				<div class="mt-4 flex items-center flex-wrap justify-start" v-if="scheduleType == 'Duration'">
					<div class="w-[30%] sm:w-auto opacity-80 text-slate-800 text-sm font-medium font-['Inter'] mr-3">Start
					</div>
					<div
						class="w-[50%] sm:w-36 h-10 px-2 py-3 bg-white rounded-lg border border-neutral-700 border-opacity-20 flex-col justify-center items-start gap-2.5 inline-flex">
						<div class="justify-start items-center gap-2 inline-flex">
							<input v-model="campaign.startdate" type="date"
								class="w-36 text-neutral-700 text-base font-medium font-['Inter'] appearance-none outline-none border-none bg-transparent" />
						</div>
					</div>
					<div
						class="w-[30%] sm:w-auto opacity-80 text-slate-800 text-sm font-medium font-['Inter'] sm:ml-3 mt-4 sm:mt-0 mr-3">
						Stop</div>
					<div
						class="w-[50%] sm:w-36 h-10 px-2 py-3 bg-white rounded-lg border border-neutral-700 mt-4 sm:mt-0 border-opacity-20 flex-col justify-center items-start gap-2.5 inline-flex">
						<div class="justify-start items-center gap-2 inline-flex">
							<input v-model="campaign.enddate" type="date"
								class="w-36 text-neutral-700 text-base font-medium font-['Inter'] appearance-none outline-none border-none bg-transparent" />
						</div>
					</div>
				</div>

				<div v-if="abtest != 'true'">
					<div class="mt-6 text-slate-800 text-base font-semibold font-['Open Sans'] leading-normal tracking-wide">
						Target Member Segment for Personalization</div>
					<div class="mt-3 text-slate-800 text-xs font-normal font-['Open Sans'] leading-none">
						When you target a campaign, it makes it personalized so only a select group of your customers can see
						it.
					</div>
					<div class="flex mt-4 items-center">
						<div class="mr-4">Segment Type:</div>
						<SuperDropdown class="mr-4" :options="segmentTypeOptions"
							placeholder="Select a target type" :model-value="getSegmentTypeType()"
							@update:model-value="value => setSegmentTypeType(value)" />
					</div>
					<div class="flex mt-4 items-center">
						<div class="mr-4">Segment:</div>
						<SuperDropdown v-if="getSegmentTypeType() == 'Loyalty Segment'" :options="segmentOptions"
							placeholder="Select a loyalty segment" :model-value="getSegmentType()"
							@update:model-value="value => setSegmentType(value)" />
						<SuperDropdown v-if="getSegmentTypeType() == 'Shopify Segment'" :options="shopifySegmentOptions"
							optionLabelKey="name"
							optionValueKey="id"
							placeholder="Select a Shopify segment" :model-value="getShopifySegmentType()"
							@update:model-value="value => setShopifySegmentType(value)" />
					</div>
				</div>

				<div v-if="abtest == 'true'">
				<div class="mt-6 text-slate-800 text-base font-semibold font-['Open Sans'] leading-normal tracking-wide">
					Distribution</div>
				<div class="mt-3 text-slate-800 text-xs font-normal font-['Open Sans'] leading-none">
					What percentage of the target segment do you want to receive this variant?
				</div>
				<div
					class="mt-2 ml-4 w-40 h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex">
						<div class="w-full self-stretch justify-start items-center gap-2 flex">
							<input type="text"
							placeholder="50"
								class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
								maxlength="40" />%
						</div>
					</div>
				</div>

				<div class="flex mt-4 justify-end">
					<CancelButton @click="isOpen = false" cta="Cancel"></CancelButton>
					<LightSecondaryButton cta="Save" @click="saveCampaignSettings(); isOpen = false;">
					</LightSecondaryButton>
				</div>
			</div>
		</div>


		<div class="mt-20 mb-10 text-zinc-500 text-2xl sm:text-5xl font-medium font-['Inter'] leading-10">Insights</div>
		<div class="flex flex-wrap test-container p-0 m-0" v-if="insights">
			<div class="test">
				<NumberChart title="Campaign ROI" :number="campaign_roi" tip="The return on investment of this campaign (revenue-discounts/discounts)"/>
			</div>
			<div class="test">
				<NumberChart title="Campaign Revenue" :number="campaign_revenue" tip="The revenue generated from this campaign"/>
			</div>
			<div class="test">
				<NumberChart title="Campaign Spend" :number="campaign_spend"  tip="The total discounts given from this campaign"/>
			</div>
			<div class="test">
				<NumberChart title="Interactions" number="Coming Soon" numberStyles="opacity: 0.25" />
			</div>
		</div>

		<div class="relative justify-center items-center text-center" v-if="!insights">
			<img src="../images/CampaignEmptyState.png" alt="Descriptive text of the image"
				class="w-full h-full object-cover" />

			<div class="absolute inset-0 bg-opacity-50 flex flex-col items-center justify-center text-center p-4">
				<svg width="32" height="42" viewBox="0 0 32 42" fill="none" xmlns="http://www.w3.org/2000/svg"
					class="md:w-9 md:h-10 sm:w-6 sm:h-7">
					<path fill-rule="evenodd" clip-rule="evenodd"
						d="M16 0C17.2624 0 18.2857 1.02335 18.2857 2.28571V38.8571C18.2857 40.1195 17.2624 41.1429 16 41.1429C14.7376 41.1429 13.7143 40.1195 13.7143 38.8571V2.28571C13.7143 1.02335 14.7376 0 16 0ZM29.7143 13.7143C30.9767 13.7143 32 14.7376 32 16V38.8571C32 40.1195 30.9767 41.1429 29.7143 41.1429C28.4519 41.1429 27.4286 40.1195 27.4286 38.8571V16C27.4286 14.7376 28.4519 13.7143 29.7143 13.7143ZM2.28571 22.8571C3.54808 22.8571 4.57143 23.8805 4.57143 25.1429V38.8571C4.57143 40.1195 3.54808 41.1429 2.28571 41.1429C1.02335 41.1429 0 40.1195 0 38.8571V25.1429C0 23.8805 1.02335 22.8571 2.28571 22.8571Z"
						fill="#E95D83" />
				</svg>

				<p class="text-ralprimary-dark font-bold md:text-xl lg:text-2xl sm:text-normal mt-4">Your campaign
					highlights will show up within 24 hours after launching.</p>
				<!--
			<p class="text-ralblack-primary text-xl mt-4">You will get insights like this once you launch a campaign. Not sure how to launch a campaign? Learn how.
				<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path fill-rule="evenodd" clip-rule="evenodd" d="M3.75 5.25C3.55109 5.25 3.36032 5.32902 3.21967 5.46967C3.07902 5.61032 3 5.80109 3 6V14.25C3 14.4489 3.07902 14.6397 3.21967 14.7803C3.36032 14.921 3.55109 15 3.75 15H12C12.1989 15 12.3897 14.921 12.5303 14.7803C12.671 14.6397 12.75 14.4489 12.75 14.25V9.75C12.75 9.33579 13.0858 9 13.5 9C13.9142 9 14.25 9.33579 14.25 9.75V14.25C14.25 14.8467 14.0129 15.419 13.591 15.841C13.169 16.2629 12.5967 16.5 12 16.5H3.75C3.15326 16.5 2.58097 16.2629 2.15901 15.841C1.73705 15.419 1.5 14.8467 1.5 14.25V6C1.5 5.40326 1.73705 4.83097 2.15901 4.40901C2.58097 3.98705 3.15326 3.75 3.75 3.75H8.25C8.66421 3.75 9 4.08579 9 4.5C9 4.91421 8.66421 5.25 8.25 5.25H3.75Z" fill="#202020" fill-opacity="0.8"/>
				<path fill-rule="evenodd" clip-rule="evenodd" d="M10.5 2.25C10.5 1.83579 10.8358 1.5 11.25 1.5H15.75C16.1642 1.5 16.5 1.83579 16.5 2.25V6.75C16.5 7.16421 16.1642 7.5 15.75 7.5C15.3358 7.5 15 7.16421 15 6.75V3H11.25C10.8358 3 10.5 2.66421 10.5 2.25Z" fill="#202020" fill-opacity="0.8"/>
				<path fill-rule="evenodd" clip-rule="evenodd" d="M16.2803 1.71967C16.5732 2.01256 16.5732 2.48744 16.2803 2.78033L8.03033 11.0303C7.73744 11.3232 7.26256 11.3232 6.96967 11.0303C6.67678 10.7374 6.67678 10.2626 6.96967 9.96967L15.2197 1.71967C15.5126 1.42678 15.9874 1.42678 16.2803 1.71967Z" fill="#202020" fill-opacity="0.8"/>
				</svg>
			</p>
			-->
			</div>
		</div>




		<div v-if="perkSummaryData?.length > 0">
			<div class="mt-20">
				<div class="flex">
					<div>
						<div class="text-zinc-500 text-2xl sm:text-5xl font-medium font-['Inter'] leading-[65px]">
							Give Out Perks
							<!--
						<Tooltip bg="dark" size="md" position="bottom">
							<div class="text-xs whitespace-nowrap text-white">Shoppers can earn rewards directly through a Way to Earn, or they can redeem points for a reward that's available in the Points Shop.</div>
						</Tooltip>
						-->
						</div>
						Members that are part of this campaign, will always get these perks while it's active
					</div>
					<div class="flex-grow"></div>
					<div class="flex-shrink-0">
						<PrimaryButton cta="add" size="normal" icon="true" @click="addPerk()"></PrimaryButton>
					</div>

				</div>
			</div>
			<div class="my-10">
				<div v-if="isPerkLoading == true" role="status"
					class="p-3 space-y-4 bg-gray-300 dark:bg-gray-600 w-full rounded-2xl shadow animate-pulse mb-6 sm:mb-0">
				</div>

				<div class="flex flex-col items-center justify-center h-22 mb-2"
					v-if="perkSummaryData.length <= 0 && isPerkLoading == false">
					<svg width="540" height="30" viewBox="0 0 501 30" fill="none" xmlns="http://www.w3.org/2000/svg">
						<g opacity="0.7">
							<g filter="url(#filter0_d_605_5769)">
								<rect x="6" y="4" width="501" height="17.0606" rx="8.53032" fill="white" fill-opacity="0.75"
									shape-rendering="crispEdges" />
								<rect x="6.5" y="4.5" width="500" height="16.0606" rx="8.03032" stroke="#D6D1FD"
									shape-rendering="crispEdges" />
							</g>
							<rect x="387" y="10" width="110" height="5" rx="2.5" fill="url(#paint0_linear_605_5769)" />
							<rect x="237" y="10" width="110" height="5" rx="2.5" fill="url(#paint1_linear_605_5769)" />
							<rect x="47" y="10" width="110" height="5" rx="2.5" fill="url(#paint2_linear_605_5769)" />
						</g>
						<defs>
							<filter id="filter0_d_605_5769" x="0" y="0" width="513" height="29.0605"
								filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
								<feFlood flood-opacity="0" result="BackgroundImageFix" />
								<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
									result="hardAlpha" />
								<feOffset dy="2" />
								<feGaussianBlur stdDeviation="3" />
								<feComposite in2="hardAlpha" operator="out" />
								<feColorMatrix type="matrix"
									values="0 0 0 0 0.0509804 0 0 0 0 0.0392157 0 0 0 0 0.172549 0 0 0 0.08 0" />
								<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_605_5769" />
								<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_605_5769" result="shape" />
							</filter>
							<linearGradient id="paint0_linear_605_5769" x1="430" y1="-0.555557" x2="433.874" y2="26.1776"
								gradientUnits="userSpaceOnUse">
								<stop stop-color="#D9D9D9" />
								<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
							</linearGradient>
							<linearGradient id="paint1_linear_605_5769" x1="280" y1="-0.555557" x2="283.874" y2="26.1776"
								gradientUnits="userSpaceOnUse">
								<stop stop-color="#D9D9D9" />
								<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
							</linearGradient>
							<linearGradient id="paint2_linear_605_5769" x1="90" y1="-0.555557" x2="93.8744" y2="26.1776"
								gradientUnits="userSpaceOnUse">
								<stop stop-color="#D9D9D9" />
								<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
							</linearGradient>
						</defs>
					</svg>

					<p class="text-xl text-ralblack-primary mt-2">You haven't added any perks yet.</p>
					<div class="inline-flex items-center px-4 py-2 mt-2 bg-gradient-to-bl from-indigo-800 to-indigo-600 rounded-3xl cursor-pointer hover:bg-blue-600 focus:outline-none focus:ring"
						@click="addPerk()">

						<svg width="22" height="23" viewBox="0 0 40 41" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path
								d="M13.3333 20.5H20M20 20.5H26.6667M20 20.5V27.1667M20 20.5V13.8333M20 35.5C11.7157 35.5 5 28.7843 5 20.5C5 12.2157 11.7157 5.5 20 5.5C28.2843 5.5 35 12.2157 35 20.5C35 28.7843 28.2843 35.5 20 35.5Z"
								stroke="white" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" />
						</svg>

						<span class="ml-2 text-white">add</span>
					</div>
				</div>
				<div class="mt-10" v-else-if="perkSummaryData.length > 0 && isPerkLoading == false">
					<div
						class="overflow-x-auto bg-white bg-opacity-75 rounded-2xl shadow border border-ralprimary-light border-opacity-50 flex-grow transition-all duration-300 ease-in-out overflow-hidden">
						<WTERewardTable header1="Perk Type" header2="Status" :tableData="perkSummaryData" :allowDelete=true
							@editClicked="handlePerkSummaryEdit" @toggleChanged="handlePerkSummaryToggle"
							@deleteClicked="async (event) => handleWTEDelete(event, 'perk')" />
					</div>
				</div>
			</div>
		</div>


		<div class="mt-20">
			<div class="flex">
				<div>
					<div class="text-zinc-500 text-2xl sm:text-5xl font-medium font-['Inter'] leading-[65px]">Ways to Earn
					</div>
					Manage the ways customers can earn points.
				</div>
				<div class="flex-grow"></div>
				<div class="flex-shrink-0">
					<PrimaryButton cta="add" size="normal" icon="true" @click="addWayToEarn()"></PrimaryButton>
				</div>
			</div>
		</div>

		<div class="mt-10">
			<div v-if="isWTELoading == true" role="status"
				class="p-3 space-y-4 bg-gray-300 dark:bg-gray-600 w-full rounded-2xl shadow animate-pulse mb-6 sm:mb-0">
			</div>

			<div v-if="wteRecommendations?.length === 0"
				class="max-w-sm rounded-lg border-2 border-dashed border-gray-300 bg-white p-6 shadow-md"
				style="max-width: 400px;">
				<div class="text-center text-gray-500">
					<div class="flex justify-center items-center mb-2">
						<p class="text-md font-medium mr-2">No Current Copilot Recommendations</p>
						<img src="../images/magic.svg" alt="Magic" width="20" height="20" />
					</div>
					<p class="text-sm">They will show up here when they are available.</p>
				</div>
			</div>
			<div v-if="wteRecommendations?.length > 0" class="flex flex-wrap -mx-2">
				<div
					v-for="(wte, index) in wteRecommendations.slice(0, 3)"
					:key="wte.id"
					class="px-2 w-full sm:w-1/3 mb-4 min-w-[380px]">
					<RecommendationCard
						:recommendation="wte"
						title="Add way to earn"
						:index="index"
						:fadeOut="wte.fadeOut"
						:icon="wte.icon"
						@addRecommendation="addRecommendedWTE"
						@ignoreRecommendation="ignoreRecommendedWTE"
					/>
				</div>
			</div>

			<div class="flex flex-col items-center justify-center h-22 mb-2"
				v-if="wteSummaryData.length <= 0 && isWTELoading == false">
				<svg width="540" height="30" viewBox="0 0 501 30" fill="none" xmlns="http://www.w3.org/2000/svg">
					<g opacity="0.7">
						<g filter="url(#filter0_d_605_5769)">
							<rect x="6" y="4" width="501" height="17.0606" rx="8.53032" fill="white" fill-opacity="0.75"
								shape-rendering="crispEdges" />
							<rect x="6.5" y="4.5" width="500" height="16.0606" rx="8.03032" stroke="#D6D1FD"
								shape-rendering="crispEdges" />
						</g>
						<rect x="387" y="10" width="110" height="5" rx="2.5" fill="url(#paint0_linear_605_5769)" />
						<rect x="237" y="10" width="110" height="5" rx="2.5" fill="url(#paint1_linear_605_5769)" />
						<rect x="47" y="10" width="110" height="5" rx="2.5" fill="url(#paint2_linear_605_5769)" />
					</g>
					<defs>
						<filter id="filter0_d_605_5769" x="0" y="0" width="513" height="29.0605"
							filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
							<feFlood flood-opacity="0" result="BackgroundImageFix" />
							<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
								result="hardAlpha" />
							<feOffset dy="2" />
							<feGaussianBlur stdDeviation="3" />
							<feComposite in2="hardAlpha" operator="out" />
							<feColorMatrix type="matrix"
								values="0 0 0 0 0.0509804 0 0 0 0 0.0392157 0 0 0 0 0.172549 0 0 0 0.08 0" />
							<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_605_5769" />
							<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_605_5769" result="shape" />
						</filter>
						<linearGradient id="paint0_linear_605_5769" x1="430" y1="-0.555557" x2="433.874" y2="26.1776"
							gradientUnits="userSpaceOnUse">
							<stop stop-color="#D9D9D9" />
							<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
						</linearGradient>
						<linearGradient id="paint1_linear_605_5769" x1="280" y1="-0.555557" x2="283.874" y2="26.1776"
							gradientUnits="userSpaceOnUse">
							<stop stop-color="#D9D9D9" />
							<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
						</linearGradient>
						<linearGradient id="paint2_linear_605_5769" x1="90" y1="-0.555557" x2="93.8744" y2="26.1776"
							gradientUnits="userSpaceOnUse">
							<stop stop-color="#D9D9D9" />
							<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
						</linearGradient>
					</defs>
				</svg>

				<p class="text-xl text-ralblack-primary mt-2">You haven't added a way to earn yet.</p>
				<div class="inline-flex items-center px-4 py-2 mt-2 bg-gradient-to-bl from-indigo-800 to-indigo-600 rounded-3xl cursor-pointer hover:bg-blue-600 focus:outline-none focus:ring"
					@click="addWayToEarn()">

					<svg width="22" height="23" viewBox="0 0 40 41" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path
							d="M13.3333 20.5H20M20 20.5H26.6667M20 20.5V27.1667M20 20.5V13.8333M20 35.5C11.7157 35.5 5 28.7843 5 20.5C5 12.2157 11.7157 5.5 20 5.5C28.2843 5.5 35 12.2157 35 20.5C35 28.7843 28.2843 35.5 20 35.5Z"
							stroke="white" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" />
					</svg>

					<span class="ml-2 text-white">add</span>
				</div>
			</div>
			<div class="mt-10" v-else-if="wteSummaryData.length > 0 && isWTELoading == false">
				<div
					class="overflow-x-auto bg-white bg-opacity-75 rounded-2xl shadow border border-ralprimary-light border-opacity-50 flex-grow transition-all duration-300 ease-in-out overflow-hidden">
					<WTERewardTable header1="Way to Earn" header2="Status" :tableData="wteSummaryData" :allowDelete=true
						@editClicked="handleWTESummaryEdit" @toggleChanged="handleWTESummaryToggle"
						@deleteClicked="async (event) => handleWTEDelete(event, 'wte')"/>
				</div>
			</div>
		</div>


		<div class="mt-20">
			<div class="flex">
				<div>
					<div class="text-zinc-500 text-2xl sm:text-5xl font-medium font-['Inter'] leading-[65px]">
						Redeem Points For These Rewards
						<!--
					<Tooltip bg="dark" size="md" position="bottom">
						<div class="text-xs whitespace-nowrap text-white">Shoppers can earn rewards directly through a Way to Earn, or they can redeem points for a reward that's available in the Points Shop.</div>
					</Tooltip>
					-->
					</div>
					Members can use points to buy the rewards below.
				</div>
				<div class="flex-grow"></div>
				<div class="flex-shrink-0">
					<PrimaryButton cta="add" size="normal" icon="true" @click="addReward()"></PrimaryButton>
				</div>

			</div>
		</div>
		<div class="my-10">
			<div v-if="isRewardLoading == true" role="status"
				class="p-3 space-y-4 bg-gray-300 dark:bg-gray-600 w-full rounded-2xl shadow animate-pulse mb-6 sm:mb-0">
			</div>
			<div v-if="rewardRecommendations?.length === 0"
				class="max-w-sm rounded-lg border-2 border-dashed border-gray-300 bg-white p-6 shadow-md"
				style="max-width: 400px;">
				<div class="text-center text-gray-500">
					<div class="flex justify-center items-center mb-2">
						<p class="text-md font-medium mr-2">No Current Copilot Recommendations</p>
						<img src="../images/magic.svg" alt="Magic" width="20" height="20" />
					</div>
					<p class="text-sm">They will show up here when they are available.</p>
				</div>
			</div>
			<div v-if="rewardRecommendations?.length > 0" class="flex flex-wrap -mx-2">
				<div
					v-for="(shopItem, index) in rewardRecommendations.slice(0, 3)"
					:key="shopItem.id"
					class="px-2 w-full sm:w-1/3 mb-4 min-w-[380px]">
					<RecommendationCard
						:recommendation="shopItem"
						title="Add Redeemable Reward"
						:index="index"
						:fadeOut="shopItem.fadeOut"
						:icon="shopItem.icon"
						@addRecommendation="addRecommendedShopItem"
						@ignoreRecommendation="ignoreRecommendedShopItem"
					/>
				</div>
			</div>
			<div class="flex flex-col items-center justify-center h-22 mb-2"
				v-if="rewardSummaryData.length <= 0 && isRewardLoading == false">
				<svg width="540" height="30" viewBox="0 0 501 30" fill="none" xmlns="http://www.w3.org/2000/svg">
					<g opacity="0.7">
						<g filter="url(#filter0_d_605_5769)">
							<rect x="6" y="4" width="501" height="17.0606" rx="8.53032" fill="white" fill-opacity="0.75"
								shape-rendering="crispEdges" />
							<rect x="6.5" y="4.5" width="500" height="16.0606" rx="8.03032" stroke="#D6D1FD"
								shape-rendering="crispEdges" />
						</g>
						<rect x="387" y="10" width="110" height="5" rx="2.5" fill="url(#paint0_linear_605_5769)" />
						<rect x="237" y="10" width="110" height="5" rx="2.5" fill="url(#paint1_linear_605_5769)" />
						<rect x="47" y="10" width="110" height="5" rx="2.5" fill="url(#paint2_linear_605_5769)" />
					</g>
					<defs>
						<filter id="filter0_d_605_5769" x="0" y="0" width="513" height="29.0605"
							filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
							<feFlood flood-opacity="0" result="BackgroundImageFix" />
							<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
								result="hardAlpha" />
							<feOffset dy="2" />
							<feGaussianBlur stdDeviation="3" />
							<feComposite in2="hardAlpha" operator="out" />
							<feColorMatrix type="matrix"
								values="0 0 0 0 0.0509804 0 0 0 0 0.0392157 0 0 0 0 0.172549 0 0 0 0.08 0" />
							<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_605_5769" />
							<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_605_5769" result="shape" />
						</filter>
						<linearGradient id="paint0_linear_605_5769" x1="430" y1="-0.555557" x2="433.874" y2="26.1776"
							gradientUnits="userSpaceOnUse">
							<stop stop-color="#D9D9D9" />
							<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
						</linearGradient>
						<linearGradient id="paint1_linear_605_5769" x1="280" y1="-0.555557" x2="283.874" y2="26.1776"
							gradientUnits="userSpaceOnUse">
							<stop stop-color="#D9D9D9" />
							<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
						</linearGradient>
						<linearGradient id="paint2_linear_605_5769" x1="90" y1="-0.555557" x2="93.8744" y2="26.1776"
							gradientUnits="userSpaceOnUse">
							<stop stop-color="#D9D9D9" />
							<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
						</linearGradient>
					</defs>
				</svg>

				<p class="text-xl text-ralblack-primary mt-2">You haven't added any redeemable rewards yet.</p>
				<div class="inline-flex items-center px-4 py-2 mt-2 bg-gradient-to-bl from-indigo-800 to-indigo-600 rounded-3xl cursor-pointer hover:bg-blue-600 focus:outline-none focus:ring"
					@click="addReward()">

					<svg width="22" height="23" viewBox="0 0 40 41" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path
							d="M13.3333 20.5H20M20 20.5H26.6667M20 20.5V27.1667M20 20.5V13.8333M20 35.5C11.7157 35.5 5 28.7843 5 20.5C5 12.2157 11.7157 5.5 20 5.5C28.2843 5.5 35 12.2157 35 20.5C35 28.7843 28.2843 35.5 20 35.5Z"
							stroke="white" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" />
					</svg>

					<span class="ml-2 text-white">add</span>
				</div>
			</div>
			<div class="mt-10" v-else-if="rewardSummaryData.length > 0 && isRewardLoading == false">
				<div
					class="overflow-x-auto bg-white bg-opacity-75 rounded-2xl shadow border border-ralprimary-light border-opacity-50 flex-grow transition-all duration-300 ease-in-out overflow-hidden">
					<WTERewardTable header1="Reward Type" header2="Status" :tableData="rewardSummaryData" :allowDelete=true
						@editClicked="handleShopItemSummaryEdit" @toggleChanged="handleShopItemSummaryToggle"
						@deleteClicked="async (event) => handleWTEDelete(event, 'shop-item')"/>
				</div>
			</div>
		</div>



		<div v-if="!perkSummaryData?.length && !isProd">
			<div class="mt-20">
				<div class="flex">
					<div>
						<div class="text-zinc-500 text-2xl sm:text-5xl font-medium font-['Inter'] leading-[65px]">
							Give Out Perks
							<!--
						<Tooltip bg="dark" size="md" position="bottom">
							<div class="text-xs whitespace-nowrap text-white">Shoppers can earn rewards directly through a Way to Earn, or they can redeem points for a reward that's available in the Points Shop.</div>
						</Tooltip>
						-->
						</div>
						Members that are part of this campaign, will always get these perks while it's active
					</div>
					<div class="flex-grow"></div>
					<div class="flex-shrink-0">
						<PrimaryButton cta="add" size="normal" icon="true" @click="addPerk()"></PrimaryButton>
					</div>

				</div>
			</div>
			<div class="my-10">
				<div v-if="isPerkLoading == true" role="status"
					class="p-3 space-y-4 bg-gray-300 dark:bg-gray-600 w-full rounded-2xl shadow animate-pulse mb-6 sm:mb-0">
				</div>

				<div class="flex flex-col items-center justify-center h-22 mb-2"
					v-if="perkSummaryData.length <= 0 && isPerkLoading == false">
					<svg width="540" height="30" viewBox="0 0 501 30" fill="none" xmlns="http://www.w3.org/2000/svg">
						<g opacity="0.7">
							<g filter="url(#filter0_d_605_5769)">
								<rect x="6" y="4" width="501" height="17.0606" rx="8.53032" fill="white" fill-opacity="0.75"
									shape-rendering="crispEdges" />
								<rect x="6.5" y="4.5" width="500" height="16.0606" rx="8.03032" stroke="#D6D1FD"
									shape-rendering="crispEdges" />
							</g>
							<rect x="387" y="10" width="110" height="5" rx="2.5" fill="url(#paint0_linear_605_5769)" />
							<rect x="237" y="10" width="110" height="5" rx="2.5" fill="url(#paint1_linear_605_5769)" />
							<rect x="47" y="10" width="110" height="5" rx="2.5" fill="url(#paint2_linear_605_5769)" />
						</g>
						<defs>
							<filter id="filter0_d_605_5769" x="0" y="0" width="513" height="29.0605"
								filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
								<feFlood flood-opacity="0" result="BackgroundImageFix" />
								<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
									result="hardAlpha" />
								<feOffset dy="2" />
								<feGaussianBlur stdDeviation="3" />
								<feComposite in2="hardAlpha" operator="out" />
								<feColorMatrix type="matrix"
									values="0 0 0 0 0.0509804 0 0 0 0 0.0392157 0 0 0 0 0.172549 0 0 0 0.08 0" />
								<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_605_5769" />
								<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_605_5769" result="shape" />
							</filter>
							<linearGradient id="paint0_linear_605_5769" x1="430" y1="-0.555557" x2="433.874" y2="26.1776"
								gradientUnits="userSpaceOnUse">
								<stop stop-color="#D9D9D9" />
								<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
							</linearGradient>
							<linearGradient id="paint1_linear_605_5769" x1="280" y1="-0.555557" x2="283.874" y2="26.1776"
								gradientUnits="userSpaceOnUse">
								<stop stop-color="#D9D9D9" />
								<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
							</linearGradient>
							<linearGradient id="paint2_linear_605_5769" x1="90" y1="-0.555557" x2="93.8744" y2="26.1776"
								gradientUnits="userSpaceOnUse">
								<stop stop-color="#D9D9D9" />
								<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
							</linearGradient>
						</defs>
					</svg>

					<p class="text-xl text-ralblack-primary mt-2">You haven't added any perks yet.</p>
					<div class="inline-flex items-center px-4 py-2 mt-2 bg-gradient-to-bl from-indigo-800 to-indigo-600 rounded-3xl cursor-pointer hover:bg-blue-600 focus:outline-none focus:ring"
						@click="addPerk()">

						<svg width="22" height="23" viewBox="0 0 40 41" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path
								d="M13.3333 20.5H20M20 20.5H26.6667M20 20.5V27.1667M20 20.5V13.8333M20 35.5C11.7157 35.5 5 28.7843 5 20.5C5 12.2157 11.7157 5.5 20 5.5C28.2843 5.5 35 12.2157 35 20.5C35 28.7843 28.2843 35.5 20 35.5Z"
								stroke="white" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" />
						</svg>

						<span class="ml-2 text-white">add</span>
					</div>
				</div>
				<div class="mt-10" v-else-if="perkSummaryData?.length > 0 && isPerkLoading == false">
					<div
						class="overflow-x-auto bg-white bg-opacity-75 rounded-2xl shadow border border-ralprimary-light border-opacity-50 flex-grow transition-all duration-300 ease-in-out overflow-hidden">
						<WTERewardTable header1="Perk Type" header2="Status" :tableData="perkSummaryData"
							@editClicked="handlePerkSummaryEdit" @toggleChanged="handlePerkSummaryToggle" />
					</div>
				</div>
			</div>
		</div>
	</div>

	<ModalBlank id="info-modal" :modalOpen="settingModalOpen" @close-modal="settingModalOpen = false">
		<div class="p-5 flex space-x-4">
			<div class="w-10 h-10 rounded-full flex items-center justify-center shrink-0 bg-indigo-100">
				<svg class="w-4 h-4 shrink-0 fill-current text-indigo-500" viewBox="0 0 16 16">
					<path
						d="M8 0C3.6 0 0 3.6 0 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8zm1 12H7V7h2v5zM8 6c-.6 0-1-.4-1-1s.4-1 1-1 1 .4 1 1-.4 1-1 1z" />
				</svg>
			</div>
			<div>
				<div class="mb-2">
					<div class="text-lg font-semibold text-ralblack-primary">Launch Campaign?</div>
				</div>
				<div class="text-sm mb-10">
					<div class="space-y-2">
						<p class="text-ralblack-secondary">You have made changes to your campaign settings but have not
							saved them. <br><br>Are you sure you want to launch your campaign before saving?</p>
					</div>
				</div>
				<div class="flex flex-wrap justify-end space-x-2">
					<CancelButton @click="settingModalOpen = false" cta="Cancel"></CancelButton>
					<button v-if="this.campaign.active"
						class="btn-sm bg-ralwarning-dark hover:bg-opacity-75 text-black rounded-full transitiona-all duration-300"
						@click="resetDirtySettings(); disable();">Yes, disable it.</button>

					<button v-if="!this.campaign.active"
						class="btn-sm bg-ralwarning-dark hover:bg-opacity-75 text-black rounded-full transitiona-all duration-300"
						@click="resetDirtySettings(); launch();">Yes, launch it.</button>
				</div>
			</div>
		</div>
	</ModalBlank>

	<ModalBlank id="info-modal" :modalOpen="aiModelOpen" @close-modal="aiModelOpen = false">
		<div class="p-5 flex flex-col space-x-4 justify-center">
			<div class="flex justify-center">
				<RaleonLoader />
			</div>
			<div>

				<div class="flex justify-center mb-2 mt-10">
					<div class="text-lg font-semibold text-ralblack-primary flex items-center">
						<svg width="24" height="26" viewBox="0 0 24 26" fill="none" xmlns="http://www.w3.org/2000/svg"
							class="mr-2" v-if="aiTextIndex == 0">
							<path
								d="M8.14838 4.24398L8.31532 4.32745C8.99 4.67522 9.53949 5.22471 9.88726 5.89939L9.97073 6.06632C10.3046 6.7271 11.2505 6.7271 11.5914 6.06632L11.6748 5.89939C12.0226 5.22471 12.5721 4.67522 13.2468 4.32745L13.4137 4.24398C14.0745 3.91011 14.0745 2.96416 13.4137 2.62334L13.2468 2.53988C12.5721 2.1921 12.0226 1.64262 11.6748 0.967932L11.5914 0.801C11.2575 0.140226 10.3115 0.140226 9.97073 0.801L9.88726 0.967932C9.53949 1.64262 8.99 2.1921 8.31532 2.53988L8.14838 2.62334C7.48761 2.95721 7.48761 3.90316 8.14838 4.24398Z"
								fill="#E86AD6" />
							<path
								d="M9.46298 10.7613C10.1446 10.4135 10.1446 9.4467 9.46298 9.09893L8.44747 8.57726C7.54325 8.11124 6.80597 7.37396 6.33995 6.46974L5.81829 5.45424C5.47051 4.7726 4.5037 4.7726 4.15592 5.45424L3.63426 6.46974C3.16824 7.37396 2.43095 8.11124 1.52673 8.57726L0.51123 9.09893C-0.17041 9.4467 -0.17041 10.4135 0.51123 10.7613L1.52673 11.283C2.43095 11.749 3.16824 12.4863 3.63426 13.3905L4.15592 14.406C4.5037 15.0876 5.47051 15.0876 5.81829 14.406L6.33995 13.3905C6.80597 12.4863 7.54325 11.749 8.44747 11.283L9.46298 10.7613Z"
								fill="#E86AD6" />
							<path
								d="M22.5115 14.9207L21.4821 14.3921C20.2371 13.7591 19.2424 12.7645 18.6095 11.5194L18.0809 10.49C17.6148 9.5719 16.6828 9.00155 15.6534 9.00155C14.624 9.00155 13.6919 9.5719 13.2259 10.49L12.6973 11.5194C12.0643 12.7645 11.0697 13.7591 9.82466 14.3921L8.79525 14.9207C7.87712 15.3867 7.30677 16.3188 7.30677 17.3482C7.30677 18.3776 7.87712 19.3096 8.79525 19.7756L9.82466 20.3043C11.0697 20.9372 12.0643 21.9319 12.6973 23.1769L13.2259 24.2063C13.6919 25.1244 14.624 25.6948 15.6534 25.6948C16.6828 25.6948 17.6148 25.1244 18.0809 24.2063L18.6095 23.1769C19.2424 21.9319 20.2371 20.9372 21.4821 20.3043L22.5115 19.7756C23.4296 19.3096 24 18.3776 24 17.3482C24 16.3188 23.4296 15.3867 22.5115 14.9207ZM21.5656 17.9185L20.5362 18.4471C18.8947 19.2818 17.587 20.5894 16.7524 22.2309L16.2237 23.2604C16.0638 23.5734 15.7716 23.6081 15.6534 23.6081C15.5351 23.6081 15.243 23.5734 15.083 23.2604L14.5544 22.2309C13.7197 20.5894 12.4121 19.2818 10.7706 18.4471L9.7412 17.9185C9.4282 17.7585 9.39342 17.4664 9.39342 17.3482C9.39342 17.2299 9.4282 16.9378 9.7412 16.7778L10.7706 16.2492C12.4121 15.4145 13.7197 14.1069 14.5544 12.4654L15.083 11.436C15.243 11.123 15.5351 11.0882 15.6534 11.0882C15.7716 11.0882 16.0638 11.123 16.2237 11.436L16.7524 12.4654C17.587 14.1069 18.8947 15.4145 20.5362 16.2492L21.5656 16.7778C21.8786 16.9378 21.9133 17.2299 21.9133 17.3482C21.9133 17.4664 21.8786 17.7585 21.5656 17.9185Z"
								fill="#E86AD6" />
						</svg>
						<TextFadeEffect :textList="aiLoadText" @text-complete="aiModelOpen = false"
							@text-index="aiTextIndex = $event" :speed="1000"></TextFadeEffect>
					</div>
				</div>
			</div>
		</div>
	</ModalBlank>

	<ModalBlank id="delete-modal" :modal-open="deleteModalOpen" @close-modal="this.deleteModalOpen = false">
		<div class="flex flex-col px-5 py-4">
			<div class="text-xl">{{ deleteModalText }}</div>
			<div class="mt-10 flex justify-end gap-3">
				<LightSecondaryButton cta="Cancel" @click="deleteModalOpen = false"></LightSecondaryButton>
				<PrimaryButton
					size="xs"
					cta="Delete"
					@click="deleteWTE()"
					:disabled="isDeleting"
					:showSpinner="isDeleting"
					:spinnerText="'Deleting...'">
				</PrimaryButton>
			</div>
		</div>
	</ModalBlank>

	<StatusMessage :message=status.message :status=status.type @resetStatus="status.type = 'nope'"></StatusMessage>
</template>

<script>
import NumberChart from '../components/NumberChart.ts.vue';
import RaleonTable from '../components/RaleonTable.ts.vue';
import ModalBlank from '../components/ModalBlank.vue';
import CardContainer from '../components/CardContainer.ts.vue';
import SuperDropdown from '../../client/components/SuperDropdown.ts.vue';
import * as Utils from '../../client-old/utils/Utils';
import EditableHeader from '../components/EditableHeader.ts.vue'
import PrimaryButton from '../components/PrimaryButton.ts.vue';
import ProgramActive from '../components/ProgramActive.ts.vue';
import LightSecondaryButton from '../components/LightSecondaryButton.ts.vue';
import CancelButton from '../components/CancelButton.ts.vue';
import ToggleItem from '../components/ToggleItem.ts.vue';
import StatusMessage from '../components/StatusMessage.ts.vue';
import Tooltip from '../components/Tooltip.ts.vue';
import GeneratedText from '../components/GeneratedText.ts.vue';
import RaleonLoader from '../components/RaleonLoader.ts.vue'
import TextFadeEffect from '../components/TextFadeEffect.ts.vue'
import { getMetric } from '../services/metrics.js';
import PreviewLoyaltyProgram from '../components/PreviewLoyalty.ts.vue';
import * as OrganizationSettings from '../services/organization-settings.js'
import WTERewardTable from '../components/WTERewardTable.vue';
import { customerIOTrackEvent } from '../services/customerio.js';
import RecommendationCard from '../components/RecommendationCard.ts.vue';
import { deleteWteOrShopItem } from '../services/ways-to-earn.js';

const URL_DOMAIN = Utils.URL_DOMAIN;

const RECOMMENDATION_STATES = {
	APPROVED_RECOMMENDATION: 'APPROVED_RECOMMENDATION',
	IGNORED_RECOMMENDATION: 'IGNORED',
	RECOMMENDED: 'RECOMMENDED',
	NONE: 'NONE'

}

export default {
	props: ['campaignId', 'abtest'],
	data() {
		return {
			campaign: { name: 'Untitled Campaign' },
			wteColumns: [
				{ name: 'Name', value: 'name' },
				{ name: 'Conditions', value: 'conditions' },
				{ name: 'Rewards', value: 'effects' }
			],
			wteData: [],
			rewardShopColumns: [
				{ name: 'Name', value: 'name' }
			],
			rewardShopData: [],
			isSettingsModalOpen: false,
			targetSegment: 'New Users',
			targetSegmentType: 'Loyalty Segment',
			targetShopifySegment: null,
			shopifySegmentOptions: [],
			insights: false, //set true when insights are being generated
			isOpen: false,
			aiHeight: '20em',
			scheduleType: null,
			status: { message: '', type: 'nope' },
			cScheduleType: null,
			cStartdate: null,
			cEnddate: null,
			cSegment: null,
			settingModalOpen: false,
			isLoading: true,
			showRaleon: false,
			aiCampaignStep: 1,
			campaign_roi: "--",
			campaign_revenue: "--",
			campaign_spend: "--",
			aiCampaignUserPrompt: '',
			uiCustomerActions: [],
			uiCustomerRewards: [],
			perkSummaryData: [],
			aiResult: null,
			isAiOpen: false,
			showAi: true,
			aiModelOpen: false,
			aiLoadText: ["Copilot is analyzing your store...", "Creating your custom program...", "Your loyalty program is ready!"],
			aiTextIndex: 0,
			wteSummaryData: [],
			rewardSummaryData: [],
			deleteModalOpen: false,
			toDelete: {},
			deleteType: '',
			isDeleting: false,
			isWTELoading: true,
			isRewardLoading: true,
			isPerkLoading: true,
			copilotThinkText: [
				['Mixing up some loyalty magic...', 'Thinking about recent trends...', 'Working on a few ideas...', 'Considering a reward or two...', 'Summarizing it for you...'],
				['Copilot is thinking about your ask...', 'Brainstorming what could work...', 'Looking at best practices...', 'Coming up with ways to earn...', 'Getting the details ready for you...'],
				['Crafting a strategy based on your request...', 'Navigating a sea of possibilities...', 'Sifting through analytics...', 'Putting together ideas...', 'Polishing up a strategy for you...'],
			],
			useCopilotThinkText: null,
			useCopilotDoneText: ['Getting everything ready...', 'Setting up ways to earn...', 'Setting up rewards...'],
		}
	},
	components: {
		NumberChart,
		RaleonTable,
		SuperDropdown,
		ModalBlank,
		EditableHeader,
		PrimaryButton,
		ProgramActive,
		LightSecondaryButton,
		CancelButton,
		ToggleItem,
		StatusMessage,
		CardContainer,
		Tooltip,
		GeneratedText,
		RaleonLoader,
		PreviewLoyaltyProgram,
		OrganizationSettings,
		TextFadeEffect,
		WTERewardTable,
		RecommendationCard,
	},
	async mounted() {
		const campaign = this.fetchCampaign().catch();
		await this.fetchCampaignSummary();
		this.getMetrics();
		const wte = this.fetchWte().catch();
		const rewards = this.fetchShopRewards().catch();
		this.fetchShopifySegments().catch();

		let firstCampaignView = await OrganizationSettings.getOrganizationSetting('firstCampaignView');

		if (firstCampaignView == 'false' || firstCampaignView == null) {
			let res1 = await OrganizationSettings.updateOrganizationSetting('firstCampaignView', 'true');
			this.aiModelOpen = true;
		}
		// this.triggerRipples();

		Promise.all([wte, rewards]).then(() => {
			if (this.wteData.length == 0 || this.rewardShopData.length == 0) {
				this.isAiOpen = true;
				this.$forceUpdate();
			}
		});

		const randomIndex = Math.floor(Math.random() * this.copilotThinkText.length);
		this.useCopilotThinkText = this.copilotThinkText[randomIndex];
	},
	beforeDestroy() {
		clearInterval(this.aiInterval); // Clear the interval when the component is destroyed
	},
	computed: {
		isProd() {
			return location.hostname === 'app.raleon.io';
		},
		campaignStatus() {
			if (!this.campaign.active) {
				return 'draft';
			}

			if (new Date() < new Date(this.campaign.startdate)) {
				return 'scheduled';
			} else if (new Date() > new Date(this.campaign.enddate)) {
				return 'ended';
			} else {
				return 'active';
			}
		},
		campaignLaunchable() {
			return !this.campaign.active;
		},
		userName() {
			if (localStorage.getItem('firstName') != '' && localStorage.getItem('firstName') != null)
				return localStorage.getItem('firstName');
			else
				return "My friend";
		},
		segmentOptions() {
			if (this.scheduleType === 'Ongoing') {
				// Exclude 'Everyone' if 'Ongoing' is selected
				return ['New Users', 'Growth', 'Loyal', 'Not Loyal'];
			} else {
				// Include 'Everyone' otherwise
				return ['Everyone', 'New Users', 'Growth', 'Loyal', 'Not Loyal'];
			}
		},
		segmentTypeOptions() {
			return ['Loyalty Segment', 'Shopify Segment'];
		}
	},
	methods: {
		async addRecommendedShopItem(shopItem, index) {
			this.$router.push(
				`/campaign/${this.campaignId}/new-shop-reward/edit/${shopItem.id}?recommended=true`
			);
		},
		async ignoreRecommendedShopItem(shopItem, index) {
			this.rewardRecommendations[index].fadeOut = true;
			let whereClause = {
				id: shopItem.id
			}
			let payload = {
				recommendationState: RECOMMENDATION_STATES.IGNORED_RECOMMENDATION,
				active: false
			}
			const queryString = `where=${encodeURIComponent(JSON.stringify(whereClause))}`;
			const response = await fetch(`${URL_DOMAIN}/loyalty-campaigns/${this.campaignId}/loyalty-redemption-shop-items?${queryString}`, {
				method: 'PATCH',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify(payload),
			});

			if (response.ok) {
				//Show success message
				await this.fetchCampaignSummary();
				customerIOTrackEvent('Ignored Recommended Reward');

				this.status.type = 'success';
				this.status.message = 'Reward has been Ignored';
			}
			else {
				//Show error message
				this.status.type = 'fail';
				this.status.message = 'Failed to Ignore. Please try again.';
			}
		},
		async addRecommendedWTE(wte, index) {
			this.$router.push(
				`/campaign/${this.campaignId}/new-ways-to-earn/edit/${wte.id}?recommended=true`
			);
		},
		async ignoreRecommendedWTE(wte, index) {
			this.wteRecommendations[index].fadeOut = true;
			let whereClause = {
				id: wte.id
			}
			let payload = {
				recommendationState: RECOMMENDATION_STATES.IGNORED_RECOMMENDATION,
				active: false
			}
			const queryString = `where=${encodeURIComponent(JSON.stringify(whereClause))}`;
			const response = await fetch(`${URL_DOMAIN}/loyalty-campaigns/${this.campaignId}/loyalty-earns?${queryString}`, {
				method: 'PATCH',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify(payload),
			});

			if (response.ok) {
				//Show success message
				await this.fetchCampaignSummary();
				customerIOTrackEvent('Ignored Recommended WTE');

				this.status.type = 'success';
				this.status.message = 'WTE has been Ignored';
			}
			else {
				//Show error message
				this.status.type = 'fail';
				this.status.message = 'Failed to Ignore. Please try again.';
			}
		},
		handleWTESummaryEdit(clickData) {
			this.$router.push(`/campaign/${this.campaignId}/new-ways-to-earn/edit/${clickData.id}`);
		},
		async handleWTESummaryToggle(clickData) {
			let whereClause = {
				id: clickData.id
			}
			let payload = {
				active: clickData.toggle
			}
			const queryString = `where=${encodeURIComponent(JSON.stringify(whereClause))}`;
			const response = await fetch(`${URL_DOMAIN}/loyalty-campaigns/${this.campaignId}/loyalty-earns?${queryString}`, {
				method: 'PATCH',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify(payload),
			});

			if (response.ok) {
				//Show success message
				await this.fetchCampaignSummary();
				if (clickData.toggle)
					customerIOTrackEvent('WTE Is Live');
				else
					customerIOTrackEvent('WTE Is Hidden');

				this.status.type = 'success';
				this.status.message = clickData.toggle ? 'If your campaign is live, WTE is now visible to your shoppers.' : 'WTE is now hidden from your shoppers.';
			}
			else {
				//Show error message
				this.status.type = 'fail';
				this.status.message = 'Failed to update. Please try again.';
			}
		},
		handleWTEDelete(clickData, type) {
			console.log(`deleting ${type} ${JSON.stringify(clickData)}`);
			this.toDelete = clickData;
			this.deleteModalText = `Are you sure you want to delete ${this.toDelete.title}?`;
			this.deleteModalOpen = true;
			this.deleteType = type;
		},
		async deleteWTE() {
			this.isDeleting = true;
			const response = await deleteWteOrShopItem(this.deleteType, this.toDelete.id);
			if (response.status == 'success') {
				this.status.type = 'success';
				this.status.message = `${this.toDelete.title} has been deleted.`;
				await this.fetchCampaignSummary();
				customerIOTrackEvent('Deleted Shop Item');
			} else {
				this.status.type = 'fail';
				this.status.message = `Failed to delete ${this.toDelete.title}. Please try again.`;
			}
			this.deleteModalOpen = false;
			this.deleteModalText = '';
			this.toDelete = {};
			this.deleteType = '';
			this.isDeleting = false;
		},
		handleShopItemSummaryEdit(clickData) {
			this.$router.push(`/campaign/${this.campaignId}/new-shop-reward/edit/${clickData.id}`);
		},
		async handleShopItemSummaryToggle(clickData) {
			console.log(clickData);
			//this.$router.push(`/campaign/${this.campaignId}/shop-reward`);
			let whereClause = {
				id: clickData.id
			}
			let payload = {
				active: clickData.toggle
			}
			const queryString = `where=${encodeURIComponent(JSON.stringify(whereClause))}`;
			const response = await fetch(`${URL_DOMAIN}/loyalty-campaigns/${this.campaignId}/loyalty-redemption-shop-items?${queryString}`, {
				method: 'PATCH',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify(payload),
			});

			if (response.ok) {
				//Show success message
				await this.fetchCampaignSummary();
				if (clickData.toggle)
					customerIOTrackEvent('Reward Is Live');
				else
					customerIOTrackEvent('Reward Is Hidden');

				this.status.type = 'success';
				this.status.message = clickData.toggle ? 'If your campaign is live, Reward is now visible to your shoppers.' : 'Reward is now hidden from your shoppers.';
			}
			else {
				//Show error message
				this.status.type = 'fail';
				this.status.message = 'Failed to update. Please try again.';
			}

		},
		handlePerkSummaryEdit(clickData) {
			this.$router.push(`/campaign/${this.campaignId}/new-perk-reward/edit/${clickData.id}`);
		},
		async handlePerkSummaryToggle(clickData) {
			console.log(clickData);
			//this.$router.push(`/campaign/${this.campaignId}/perk-reward`);
			let whereClause = {
				id: clickData.id
			}
			let payload = {
				active: clickData.toggle
			}
			const queryString = `where=${encodeURIComponent(JSON.stringify(whereClause))}`;
			const response = await fetch(`${URL_DOMAIN}/loyalty-campaigns/${this.campaignId}/static-effects?${queryString}`, {
				method: 'PATCH',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify(payload),
			});

			if (response.ok) {
				//Show success message
				await this.fetchCampaignSummary();
				if (clickData.toggle)
					customerIOTrackEvent('Perk Is Live');
				else
					customerIOTrackEvent('Perk Is Hidden');

				this.status.type = 'success';
				this.status.message = clickData.toggle ? 'If your campaign is live, Perk is now visible to your shoppers.' : 'Perk is now hidden from your shoppers.';
			}
			else {
				//Show error message
				this.status.type = 'fail';
				this.status.message = 'Failed to update. Please try again.';
			}

		},
		resetAICampaign() {
			this.aiCampaignStep = 1;
			customerIOTrackEvent('Copilot Campaign: Edit Prompt');
		},
		async setAIStep(step) {
			this.aiCampaignStep = step;
			if (step == 2) {
				if (!this.uiCustomerActions.length) {
					fetch(`${URL_DOMAIN}/wte/customer-actions?campaignId=${this.campaignId}`, {
						method: 'GET',
						credentials: 'omit',
						mode: 'cors',
						headers: {
							'Content-Type': 'application/json',
							Authorization: `Bearer ${localStorage.getItem('token')}`,
						},
					}).then(async response => {
						const result = await response.json();

						this.uiCustomerActions = result;
					}).catch();

				}

				if (!this.uiCustomerRewards.length) {
					fetch(`${URL_DOMAIN}/wte/all-customer-rewards`, {
						method: 'GET',
						credentials: 'omit',
						mode: 'cors',
						headers: {
							'Content-Type': 'application/json',
							Authorization: `Bearer ${localStorage.getItem('token')}`,
						},
					}).then(async response => {
						const result = await response.json();

						this.uiCustomerRewards = result;
					}).catch();

				}

				const response = await fetch(`${URL_DOMAIN}/loyalty-campaigns/${this.campaignId}/ai-generate`, {
					method: 'POST',
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					},
					body: JSON.stringify({
						userPrompt: this.aiCampaignUserPrompt,
					})
				});

				try {
					const json = await response.json();
					if (json.error) {
						this.aiCampaignStep = 1;

						return;
					}
					customerIOTrackEvent('Copilot Campaign: Generate');
					this.aiCampaignStep = 3;
					this.aiResult = json;
					this.$forceUpdate();
					await new Promise(resolve => setTimeout(resolve, 100));
					const textHeight = this.$refs.aiTextContainer?.getBoundingClientRect()?.height;
					this.aiHeight = `${textHeight + 100}px`;
					this.$forceUpdate();
				} catch (e) {
					console.error(e);
					this.aiCampaignStep = 1;
				}
			} else if (step == 4) {
				this.aiHeight = '30em';
				customerIOTrackEvent('Copilot Campaign: Add to Campaign');
				const response = await fetch(`${URL_DOMAIN}/loyalty-campaigns/${this.campaignId}/ai-persist`, {
					method: 'POST',
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					},
					body: JSON.stringify({
						wteJson: this.aiResult.loyaltyEarn,
						rewardJson: this.aiResult.loyaltyRedemptionShopItem
					})
				});

				const json = await response.json();

				location.reload();
			}
		},
		getSegmentLabel() {
			return this.targetShopifySegment
				? this.shopifySegmentOptions.find((segment) => segment.id === this.targetShopifySegment)?.name
				: this.getSegmentType();
		},
		getSegmentType() {
			return this.targetSegment;
		},
		setSegmentType(segmentType) {
			this.targetSegment = segmentType;
			this.targetShopifySegment = null;
		},
		getSegmentTypeType() {
			return this.targetSegmentType;
		},
		setSegmentTypeType(segmentTypeType) {
			this.targetSegmentType = segmentTypeType;
			this.targetShopifySegment = null;
			this.targetSegment = null;
		},
		getShopifySegmentType() {
			return this.targetShopifySegment;
		},
		setShopifySegmentType(shopifySegmentType) {
			this.targetSegment = null;
			this.targetShopifySegment = shopifySegmentType;
		},
		getScheduleType() {
			if (this.campaign.evergreen) {
				return "Ongoing";
			}
			else if (this.campaign.startdate || this.campaign.enddate)
				return "Duration";
			else {
				return "Not Specified";
			}
		},
		setScheduleType(type) {
			this.scheduleType = type;
			if (type == 'Ongoing') {
				this.campaign.evergreen = true;
				this.campaign.startdate = null;
				this.campaign.enddate = null;
			}
			else if (type == 'Duration') {
				this.campaign.evergreen = false;
			}
		},
		addWayToEarn() {
			customerIOTrackEvent('Started to Add Way to Earn');
			this.$router.push(`/campaign/${this.campaignId}/new-ways-to-earn/add`);
		},
		editWayToEarn(editId) {
			this.$router.push(`/campaign/${this.campaignId}/new-ways-to-earn/edit/${editId}`);
		},
		addReward() {
			customerIOTrackEvent('Started to Add Reward');
			this.$router.push(`/campaign/${this.campaignId}/new-shop-reward/add`);
		},
		addPerk(tier) {
			customerIOTrackEvent('Started to Add Perk');
			this.$router.push(`/campaign/${this.campaignId}/new-perk-reward/add`);
		},
		editShopItem(editId) {
			this.$router.push(`/campaign/${this.campaignId}/new-shop-reward/edit/${editId}`);
		},
		toggleAccordion() {
			this.isOpen = !this.isOpen;
		},
		async fetchCampaign() {
			this.isLoading = true;
			const response = await fetch(`${URL_DOMAIN}/loyalty-campaigns/${this.campaignId}`, {
				method: 'GET',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				}
			});

			if (response.ok && response.status >= 200 && response.status < 300) {
				const result = await response.json();

				this.campaign = {
					...result,
					startdate: detimeify(result.startdate),
					enddate: detimeify(result.enddate),
				};
				this.targetSegment = result.loyaltySegment;
				this.targetShopifySegment = result.loyaltySegmentType;
				if (result.loyaltySegmentType) {
					this.targetSegmentType = 'Shopify Segment';
					this.targetSegment = null;
				}
				this.setScheduleType(this.getScheduleType());

				this.setDirtySettings(this.campaign);
				this.isLoading = false;
			}
		},
		async fetchCampaignSummary() {
			const response = await fetch(`${URL_DOMAIN}/loyalty-campaigns/${this.campaignId}/summary`, {
				method: 'GET',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				}
			});
			if (response.ok && response.status >= 200 && response.status < 300) {
				const result = await response.json();
				this.wteSummaryData = result?.summary?.wtes;
				this.rewardSummaryData = result?.summary?.rewards;
				this.perkSummaryData = result?.summary?.perks;
				this.isWTELoading = false;
				this.isRewardLoading = false;
				this.isPerkLoading = false;
				this.wteRecommendations = result?.summary?.recommendedWTEs;
				this.rewardRecommendations = result?.summary?.recommendedShopItems;
			}
		},
		async fetchWte() {
			const wteResponse = await fetch(`${URL_DOMAIN}/ui-loyalty-earn-details/${this.campaignId}`, {
				method: 'GET',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
			});

			if (wteResponse.ok && wteResponse.status >= 200 && wteResponse.status < 300) {
				const result = await wteResponse.json();
				console.log('Ways to earn', result);

				this.wteData = result.map((item) => {
					return {
						name: item.name,
						active: item.active,
						conditions: item.earnConditions ? item.earnConditions.length : 0,
						effects: item.earnEffects ? item.earnEffects.length : 0,
						href: `/campaign/${this.campaignId}/new-ways-to-earn/edit/${item.id}`,
						target: '_top'
					};
				});
			}
		},
		async fetchShopRewards() {

			const rewardShopResposne = await fetch(`${URL_DOMAIN}/loyalty-campaigns/${this.campaignId}/loyalty-redemption-shop-items`, {
				method: 'GET',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
			});

			if (rewardShopResposne.ok && rewardShopResposne.status >= 200 && rewardShopResposne.status < 300) {
				const result = await rewardShopResposne.json();

				this.rewardShopData = result.map((item) => {
					return {
						name: item.name,
						active: item.active,
						href: `/campaign/${this.campaignId}/new-shop-reward/edit/${item.id}`,
						target: '_top',
						description: item.description,
						// conditions: item.conditions?.map((condition) => `${condition.type}: ${condition.value}`).join(', ') || '',
						// rewards: item.rewards?.map((reward) => `${reward.type}: ${reward.value}`).join(', ') || '',
					};
				});
			}
		},

		async fetchShopifySegments() {

			const segmentResponse = await fetch(`${URL_DOMAIN}/all-shopify-segments`, {
				method: 'GET',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
			});

			if (segmentResponse.ok && segmentResponse.status >= 200 && segmentResponse.status < 300) {
				const result = await segmentResponse.json();
				this.shopifySegmentOptions = result;

				// this.targetShopifySegment = result.find((segment) => segment.id === this.targetShopifySegmentId);

				// this.$forceUpdate();
			}
		},
		async saveCampaignName() {
			const response = await fetch(`${URL_DOMAIN}/loyalty-campaigns/${this.campaignId}`, {
				method: 'PATCH',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify({
					name: this.campaign.name,
				}),
			});

			if (!response.ok || response.status < 200 || response.status >= 300) {
				console.error('Campaign update failed', response);
			}
		},
		async saveCampaignSettings() {
			let payload = {};
			if (this.campaign.startdate) {
				payload.startdate = entimeify(this.campaign.startdate);
			}
			if (this.campaign.enddate) {
				payload.enddate = entimeify(this.campaign.enddate);
			}
			if (this.targetSegment) {
				payload.loyaltySegment = this.targetSegment;
				payload.loyaltySegmentType = '';
			}
			if (this.targetShopifySegment) {
				payload.loyaltySegmentType = this.targetShopifySegment;
				payload.loyaltySegment = '';
			}

			payload.evergreen = this.campaign.evergreen;

			const response = await fetch(`${URL_DOMAIN}/loyalty-campaigns/${this.campaignId}`, {
				method: 'PATCH',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify(payload),
			});

			if (!response.ok || response.status < 200 || response.status >= 300) {
				console.error('Campaign update failed', response);
				this.status.message = 'Your campaign settings could not be saved.';
				this.status.type = 'fail';
			} else {
				this.status.message = 'Your campaign settings were saved successfully.';
				this.status.type = 'success';
				this.setDirtySettings(payload);
			}
		},
		campaignLauncher(toggle) {
			if (this.checkDirtySettings()) {
				this.settingModalOpen = true;
				return;
			}

			if (toggle) {
				customerIOTrackEvent('Campaign Enabled');
				this.launch();
			}
			else {
				customerIOTrackEvent('Campaign Disabled');
				this.disable();
			}
		},
		checkDirtySettings() {
			return this.cStartdate !== this.campaign.startdate ||
				this.cEnddate !== this.campaign.enddate ||
				this.cSegment !== this.targetSegment ||
				this.cScheduleType !== this.scheduleType;
		},
		resetDirtySettings() {
			this.campaign.startdate = this.cStartdate;
			this.campaign.enddate = this.cEnddate;
			this.targetSegment = this.cSegment;
			this.setScheduleType(this.cScheduleType);
			this.settingModalOpen = false;
		},
		setDirtySettings(data) {
			this.cStartdate = detimeify(data.startdate);
			this.cEnddate = detimeify(data.enddate);

			if (data.loyaltySegment == null) {
				this.setSegmentType('Growth');
				this.cSegment = 'Growth';
			}
			else
				this.cSegment = data.loyaltySegment;

			this.cScheduleType = this.getScheduleType();
		},
		async launch() {
			const response = await fetch(`${URL_DOMAIN}/loyalty-campaigns/${this.campaignId}`, {
				method: 'PATCH',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify({
					active: true
				}),
			});

			if (!response.ok || response.status < 200 || response.status >= 300) {
				console.error('Campaign update failed', response);
				this.status.message = 'Your campaign could not go live.';
				this.status.type = 'fail';
			} else {
				this.campaign.active = true;
				this.status.message = 'Your campaign was activated!';
				this.status.type = 'success';
			}
		},
		async disable() {
			const response = await fetch(`${URL_DOMAIN}/loyalty-campaigns/${this.campaignId}`, {
				method: 'PATCH',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify({
					active: false
				}),
			});

			if (!response.ok || response.status < 200 || response.status >= 300) {
				console.error('Campaign update failed', response);
				this.status.message = 'Your campaign could not be disabled.';
				this.status.type = 'fail';
			} else {
				this.campaign.active = false;
				this.status.message = 'You have de-activated your campaign.';
				this.status.type = 'success';
			}
		},
		async getMetrics() {

			let data = await getMetric("campaign_roi", 'latest', '', 'day', 'sum', this.campaignId);
			if (data == "Metrics still loading...") return console.log('No data for metric', 'campaign_roi');
			if (data.body.data.length > 0) {
				const lastDataItem = data.body.data[data.body.data.length - 1];

				if (lastDataItem.metrics) {
					const metricKeys = ['campaign_roi', 'campaign_revenue', 'campaign_spend'];
					for (const metricKey of metricKeys) {
						if (lastDataItem.metrics[metricKey]) {
							const metricName = metricKey.replace('_', ' ');
							const prefix = lastDataItem.metrics[metricKey].prefix || '';
							const value = Math.round(lastDataItem.metrics[metricKey].value) || 0;
							const suffix = lastDataItem.metrics[metricKey].suffix || '';

							this[metricKey] = prefix + Number(value).toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 }) + suffix;
						} else {
							console.error('No metrics found in the last data item');
						}
					}
					this.insights = true;
				} else {
					console.error('No metrics found in the last data item');
				}
			} else {
				console.error('No data returned for metric:', 'campaign_roi');
			}

		},
		getFullSummary(campaign) {
			return `${campaign.summaryHtml}


				<p class="mt-6">
					<strong>Ways to Earn:</strong>
					<ul class="ml-5">
						${campaign.loyaltyEarn.map((earn) => {
				return `<li>
								<span>${earn.name}</span> - <span>${this.generateWteText(earn)}</span>
							</li>`;
			}).join('')}
					</ul>
				</p>

				<p class="mt-6">

					<strong>Rewards:</strong>
					<ul class="ml-5">
						${campaign.loyaltyRedemptionShopItem.map((reward) => {
				return `<li>
								<span>${reward.name}</span> <span>(${reward.price} Points) - ${this.generateRewardText(reward)}</span>
							</li>`;
			}).join('')}
					</ul>
				</p>

				`;
		},
		generateWteText(earn) {
			const conditions = earn.earnCondition.map((condition) => {
				switch (condition.type) {
					case 'dollar-spent':
						return this.generatePointsPerDollarSummary(condition);
					case 'welcome-bonus':
						return 'Join Program';
					case 'referral-bonus':
						return 'Referral Bonus';
					case 'birthday-bonus':
						return 'Birthday Bonus';
					case 'nth-purchase':
						return this.generateNthPurchaseSummary(condition);
					case 'timed-purchase':
						return this.generateNthPurchaseSummary(condition);
					default:
						return this.generateUiSummary(condition);
				}
			}).join(' and ');
			return ` ${conditions} -> ${earn.earnEffect.map((effect) => {
				return this.generateRewardText(effect);
			}).join(', ')
				}`;
		},
		generateRewardText(reward) {
			const type = reward.type || reward?._121_loyaltyRewardDefinition?._121_rewardCoupon.amountType;

			let text = ' ';

			if (type === 'dollars-off-coupon') {
				text += '$';
			}

			if (reward._121_loyaltyRewardDefinition && reward._121_loyaltyRewardDefinition._121_rewardCoupon) {
				text += `${reward._121_loyaltyRewardDefinition._121_rewardCoupon.amount
					} ${''
					}`;
			} else if (reward.points || reward.pointsPerDollar) {
				text += `${reward.points || reward.pointsPerDollar
					}`;
			}

			const uiReward = this.uiCustomerRewards.find((uiReward) => uiReward.type === type);
			if (uiReward) {
				text += ` ${uiReward.label}`;
			}


			return text;
		},
		generatePointsPerDollarSummary(condition) {
			return `Each $${condition.amount} spent`;
		},
		generateNthPurchaseSummary(condition) {
			return `${ordinal_suffix_of(condition.amount)} Purchase`;
		},
		generateUiSummary(condition) {
			const uiCondition = this.uiCustomerActions.find((uiCondition) => uiCondition.type === condition.type);
			if (uiCondition) {
				return uiCondition.name;
			} else {
				return condition.type;
			}
		},
	},
}

function detimeify(date) {
	if (!date || !date.includes('T')) {
		return date;
	}

	return date.split('T')[0];
}

function entimeify(date) {
	if (date.includes('T')) {
		return date;
	}

	return `${date}T00:00:00.000Z`;
}

function ordinal_suffix_of(i) {
	let j = i % 10,
		k = i % 100;
	if (j === 1 && k !== 11) {
		return i + "st";
	}
	if (j === 2 && k !== 12) {
		return i + "nd";
	}
	if (j === 3 && k !== 13) {
		return i + "rd";
	}
	return i + "th";
}
</script>
<style scoped>
.rotate-90 {
	transform: rotate(90deg);
	transition: transform 0.3s;
}

.test-container {
	margin: 0 -1em;
}

.test {
	width: 50%;
	padding-right: 0.4em;
}

@media all and (max-width: 768px) {
	.test-container {
		width: 100%;
		margin: 0;
		margin-left: -0.5em;
	}

	.test {
		width: 100%;
		padding: 0;
		/* padding-left: 0.4em; */
	}
}

.campaign-name {
	font-family: 'Inter', sans-serif;
	font-style: normal;
	font-weight: 500;
	font-size: 40px;
	line-height: 65px;
	/* identical to box height, or 162% */
	letter-spacing: -0.04em;
	text-transform: uppercase;

	color: #5E48F8;
	/* -webkit-background-clip: text; */
	/* -webkit-text-fill-color: transparent; */
	/* background-clip: text; */
	/* text-fill-color: transparent; */
}

.no-focus-outline {
	box-shadow: none !important;
	outline: none !important;
}

.accordion-animation-enter-active,
.accordion-animation-leave-active {
	transition: opacity 0.5s, transform 0.5s;
	overflow: hidden;
}

.accordion-animation-enter,
.accordion-animation-leave-to {
	opacity: 0;
	transform: scaleY(0);
}

.accordion-animation-enter-to,
.accordion-animation-leave {
	opacity: 1;
	transform: scaleY(1);
}

.accordion-body {
	transition: max-height 0.5s ease-in-out, opacity 0.5s ease-in-out;
}

.fade-enter-active,
.fade-leave-active {
	transition-property: opacity, transform;
	transition-duration: 500ms;
	transition-timing-function: ease-in-out;
}

.fade-enter-from,
.fade-leave-to {
	opacity: 0;
	transform: translateX(10px);
}

.fade-enter-to,
.fade-leave-from {
	opacity: 1;
	transform: translateX(0);
}
</style>

