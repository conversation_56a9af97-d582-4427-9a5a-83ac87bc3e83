<template>

	<SuperModal>
		<SuperModalHeader />
		<div class="flex p-7" style="justify-content: space-between;">
			<h1>Onboard Test</h1>
		</div>

	</SuperModal>
</template>

<script>

	import * as Utils from '../../client-old/utils/Utils';
	import SuperModal from '../components/SuperModal.ts.vue';
	import SuperModalHeader from '../components/SuperModalHeader.ts.vue';


	const URL_DOMAIN = Utils.URL_DOMAIN;

	export default {
		components: {
			SuperModal,
			SuperModalHeader
		},
		async mounted() {
			console.log('test home');

			const response = await fetch(`${URL_DOMAIN}/onboard/domain-metadata`, {
				method: 'GET',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				}
			});

			if (response.ok && response.status >= 200 && response.status < 300) {
				const result = await response.json();

				this.rowData = result;

				console.log(result);
			}
		},
		data() {
			return {}
		}
	}
</script>
<style scoped>
.super-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 10000;

	background: linear-gradient(144.1deg, #F8F8F6 26.87%, #D2C5E4 58.37%, #916FC6 88.28%);
}

h1 {
	text-transform: uppercase;
	font-size: 3em;
	font-weight: 500;
	color: grey;
}
.big-button {
	font-size: 3em;
	border-radius: 1em;
	padding: 0 0.5em;
	text-transform: uppercase;
}
.big-button > svg {
	margin-right: 0.25em;
}
.test {
	width: 50%;
	padding: 0em 0.4em;
}

@media all and (max-width: 768px) {
	.test {
		width: 100%;
	}
}
</style>

