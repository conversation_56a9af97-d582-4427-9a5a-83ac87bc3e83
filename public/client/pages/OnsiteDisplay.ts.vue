<template>
	<ProgramActive></ProgramActive>

	<div class="p-2 sm:p-7 mr-24 flex flex-col items-center justify-center" v-if="!isFeatureAvailable && !isTrialEnded">
		<img src="../images/VIPTeirPromo.png" width="800">
		<a class="text-ralprimary-dark font-bold md:text-xl lg:text-2xl sm:text-normal mt-4 font-[Inter]" href="/loyalty/settings/plans">Upgrade to Loyalty Plan</a>
		<p class="text-ralblack-primary text-lg mt-4 flex items-center font-[Inter] mb-4 w-1/2 text-center">
			Loyalty programs powered by Raleon help brands increase repeat purchase rate and customer lifetime value.
		</p>
		<PrimaryButton
					cta="Upgrade Loyalty Plan"
					size="xs"
					@click="() => this.$router.push('/loyalty/settings/plans')"
					/>
	</div>

	<div class="bg-gradient-to-tr from-[#4F3EAC] to-[#5E48F8] p-4 w-full justify-center shadow-lg flex items-center" v-if="!isFeatureAvailable && isTrialEnded">
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 14H11V9.8L12.6 11.4L14 10L10 6L6 10L7.4 11.4L9 9.8V14ZM10 20C8.61667 20 7.31667 19.7375 6.1 19.2125C4.88333 18.6875 3.825 17.975 2.925 17.075C2.025 16.175 1.3125 15.1167 0.7875 13.9C0.2625 12.6833 0 11.3833 0 10C0 8.61667 0.2625 7.31667 0.7875 6.1C1.3125 4.88333 2.025 3.825 2.925 2.925C3.825 2.025 4.88333 1.3125 6.1 0.7875C7.31667 0.2625 8.61667 0 10 0C11.3833 0 12.6833 0.2625 13.9 0.7875C15.1167 1.3125 16.175 2.025 17.075 2.925C17.975 3.825 18.6875 4.88333 19.2125 6.1C19.7375 7.31667 20 8.61667 20 10C20 11.3833 19.7375 12.6833 19.2125 13.9C18.6875 15.1167 17.975 16.175 17.075 17.075C16.175 17.975 15.1167 18.6875 13.9 19.2125C12.6833 19.7375 11.3833 20 10 20ZM10 18C12.2333 18 14.125 17.225 15.675 15.675C17.225 14.125 18 12.2333 18 10C18 7.76667 17.225 5.875 15.675 4.325C14.125 2.775 12.2333 2 10 2C7.76667 2 5.875 2.775 4.325 4.325C2.775 5.875 2 7.76667 2 10C2 12.2333 2.775 14.125 4.325 15.675C5.875 17.225 7.76667 18 10 18Z" fill="#FFA3DF"/>
        </svg>
        <div class="text-white ml-2">Your trial has expired and your program has been deactivated.</div>
        <div class="px-2 py-2 text-white text-sm border-[#FFF] border rounded-full ml-4 hover:text-black hover:bg-[#FFF] hover:cursor-pointer transitional-bg delay-200" @click="() => this.$router.push('/loyalty/settings/plans')">
                    Choose a Plan
        </div>
    </div>

	<div class="p-2 sm:p-7" v-if="isFeatureAvailable || isTrialEnded">
		<h1 class="text-3xl sm:text-6xl font-sans font-medium opacity-70 text-ralblack-primary">Branding</h1>
		<div class="m-3 flex mb-10">
			<span class="text-neutral-800 text-opacity-80 text-sm font-medium font-['Inter'] mr-2">
				Branding your loyalty sidebar and onsite displays are how you can make sure to engage your customers to
				make them more loyal.
			</span>
			<LearnMoreText text="Learn more" url="https://docs.raleon.io/docs/the-loyalty-panel"></LearnMoreText>
			<span class="text-neutral-500 text-sm font-medium font-['Inter']">&nbsp;</span>
		</div>

		<div class="flex flex-wrap mt-10">
			<div v-for="(option, index) in onsiteOptions" :key="index"
				class="w-[330px] bg-white rounded-2xl border border-violet-300 bg-opacity-75 overflow-hidden shadow-md mb-2 px-4 py-4 justify-start mr-8 mb-8">
				<img :src="option.image" :alt="option.name" @click="openSetup(option.editURL)" class="cursor-pointer">

				<div class="px-4">
					<div class="flex justify-between">
						<span
							class="opacity-75 text-slate-800 text-base font-semibold font-['Inter'] leading-normal tracking-wide">{{
				option.name }}</span>
				<Tooltip v-if="option.tooltip" bg="dark" size="md" position="top" class="ml-2">
						<div class="text-xs whitespace-nowrap text-white">
							{{ option.tooltip }}
						</div>
					</Tooltip>
						<LightSecondaryButton :cta="option.buttonText" @click="openSetup(option.editURL)" :isDisabled="option.disabled" />
					</div>
					<ToggleItem v-if="option.canBeActivated" @toggleChange="(value) => toggleOption(index, value)" :isDisabled="option.disabled"
						:state="option.isActive" showLabel=true onLabel="Active" offLabel="Inactive">
					</ToggleItem>

				</div>
			</div>
		</div>
	</div>
</template>

<script>
import * as Utils from '../../client-old/utils/Utils';
import CardContainer from '../components/CardContainer.ts.vue';
import LightSecondaryButton from '../components/LightSecondaryButton.ts.vue'
import PrimaryButton from '../components/PrimaryButton.ts.vue';
import ToggleItem from '../components/ToggleItem.ts.vue';
import StatusMessage from '../components/StatusMessage.ts.vue';
import LearnMoreText from '../components/LearnMoreText.ts.vue';
import ProgramActive from '../components/ProgramActive.ts.vue';
import PromoBoxTs from './subpages/PromoBox.ts.vue';

// Images
import SidebarImage from '../images/SidebarOnsite.png'
import LauncherImage from '../images/LauncherOnsite.png'
import StickyBarImage from '../images/StickyBar.png'
import MenuNavigationImage from '../images/MenuNav.png'
import NotificationsImage from '../images/Notifications.png'
import LandingPageImage from '../images/LandingPage.png'
import { isFeatureAvailable } from '../services/features';
import Tooltip from '../components/Tooltip.ts.vue';
import { Crisp } from 'crisp-sdk-web';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	components: {
		CardContainer,
		LightSecondaryButton,
		StatusMessage,
		ToggleItem,
		PrimaryButton,
		LearnMoreText,
		ProgramActive,
		Tooltip
	},
	data() {
		return {
			onsiteOptions: [
				{
					name: 'Loyalty Sidebar',
					image: SidebarImage,
					canBeActivated: false,
					editURL: '/loyalty/branding?tab=sidebar',
					isActive: false,
					buttonText: 'Setup'
				},
				{
					name: 'Loyalty Launcher',
					image: LauncherImage,
					canBeActivated: true,
					editURL: '/loyalty/branding?tab=launcher',
					isActive: false,
					buttonText: 'Setup'
				},
				//   {
				// 	name: 'Loyalty Sticky Bar',
				// 	image: StickyBarImage,
				// 	canBeActivated: false,
				// 	editURL: '/edit-sticky-bar',
				// 	isActive: false,
				// 	buttonText: 'Add'
				//   },
				{
					name: 'Loyalty Notifications',
					image: NotificationsImage,
					canBeActivated: true,
					editURL: '/loyalty/notifications',
					isActive: false,
					buttonText: 'Setup',
					disabled: true,
					tooltip: 'Upgrade your Plan to enable this.'
				},
				{
					name: 'Referrals',
					image: 'https://raleoncdn.s3.us-east-1.amazonaws.com/ReferralsOnsite.png',
					canBeActivated: false,
					editURL: '/loyalty/referral-program',
					isActive: false,
					buttonText: 'Setup'
				},
				{
					name: 'Loyalty Menu Navigation',
					image: MenuNavigationImage,
					canBeActivated: false,
					editURL: '/loyalty/edit-menu-navigation',
					isActive: false,
					buttonText: 'Configure'
				},
				{
					name: 'Product Page Promo Box',
					image: MenuNavigationImage,
					canBeActivated: false,
					editURL: '/loyalty/promo-box',
					isActive: false,
					buttonText: 'Configure'
				},
				{
					name: 'Loyalty Landing Page',
					image: LandingPageImage,
					canBeActivated: false,
					editURL: '/loyalty/landing-page',
					isActive: false,
					buttonText: 'Configure'
				},
				// {
				// 	name: 'Referrals in Storefront',
				// 	image: LandingPageImage,
				// 	canBeActivated: false,
				// 	editURL: '/loyalty-branding/referral-program',
				// 	isActive: false,
				// 	buttonText: 'Setup'
				// }
			],
			programId: -1,
			programName: '',
			programStatus: false,
			launcherActive: false,
			notificationsActive: false,
		}
	},
	async mounted() {
		await this.fetchProgramDetails();
		this.updateOnsiteOptions();

		this.onsiteOptions.find(x => x.editURL === '/loyalty/notifications').disabled = !isFeatureAvailable('loyalty-notifications');
		if (isFeatureAvailable('loyalty-notifications')) {
			this.onsiteOptions.find(x => x.editURL === '/loyalty/notifications').tooltip = undefined;
		}
	},
	computed: {
		isFeatureAvailable() {
			return isFeatureAvailable('loyalty-app');
		},
		isTrialEnded() {
			const freeTrialData = JSON.parse(localStorage.getItem('freeTrialData'));

			return freeTrialData != {} && !freeTrialData.hasPaidPlan && freeTrialData.daysLeft < 0
		}
	},
	methods: {
		async fetchProgramDetails() {
			try {
				const response = await fetch(`${URL_DOMAIN}/loyalty-programs`, {
					method: 'GET',
					credentials: 'omit',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Access-Control-Allow-Origin': '*',
						'Content-Type': 'application/json'
					}
				});
				const jsonResponse = await response.json();

				if (jsonResponse && jsonResponse.length > 0) {
					const program = jsonResponse[0];
					this.programName = program.name;
					this.programId = program.id;
					this.programStatus = program.active;
					this.launcherActive = program.launcherActive ?? true;
					this.notificationsActive = program.notificationsActive ?? false;
				}
			} catch (error) {
				console.error('Error fetching program details:', error);
			}
		},
		updateOnsiteOptions() {
			this.onsiteOptions[1].isActive = this.launcherActive;
			this.onsiteOptions[2].isActive = this.notificationsActive;
		},
		openSetup(editURL) {
			console.log(`Opening setup for ${editURL}`);
			this.$router.push(editURL);
			// this.$router.push(editURL);
		},
		async toggleOption(index, value) {
			if (this.onsiteOptions[index].name == 'Loyalty Launcher') {
				this.launcherActive = value;
				await this.updateProgramDetails();
			}

			if (this.onsiteOptions[index].name == 'Loyalty Notifications') {
				this.notificationsActive = value;
				await this.updateProgramDetails();
			}
			this.onsiteOptions[index].isActive = value;
		},
		async updateProgramDetails() {
			try {
				const programDetails = {
					launcherActive: this.launcherActive,
					notificationsActive: this.notificationsActive
				};

				console.log('Updating program details:', programDetails);

				const response = await fetch(`${URL_DOMAIN}/loyalty-programs/${this.programId}`, {
					method: 'PATCH',
					credentials: 'omit',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Access-Control-Allow-Origin': '*',
						'Content-Type': 'application/json'
					},
					body: JSON.stringify(programDetails)
				});

				if (!response.ok) {
					throw new Error('Failed to update program details');
				}

				console.log(`Loyalty Launcher is now ${this.launcherActive ? 'active' : 'inactive'}`);
			} catch (error) {
				console.error('Error updating program details:', error);
				// Here you might want to show an error message to the user
			}
		},
		openDocs(integration) {
			window.open(integration.docURL, '_blank');
		},
		openChat() {
			Crisp.chat.open();
		},
	},
}
</script>

<style>
	Tooltip {
		position: absolute
	}
</style>
