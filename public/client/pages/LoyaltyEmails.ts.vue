<template>
	<StatusMessage :message=status.message :status=status.type @resetStatus="status.type = 'nope'"></StatusMessage>

	<div class="p-2 sm:p-7 mr-24 flex flex-col items-center justify-center" v-if="!isFeatureAvailable && !isTrialEnded">
		<img src="../images/EmailTrial.png" width="800">
		<a class="text-ralprimary-dark font-bold md:text-xl lg:text-2xl sm:text-normal mt-4 font-[Inter]" href="/loyalty/settings/plans">Built-in Email Automation</a>
		<p class="text-ralblack-primary text-lg mt-4 flex items-center font-[Inter] mb-4 w-1/2 text-center">
			Our Built-in email automation communicates with your customers automatically at key moments, like when a reward is earned or on their birthday.
		</p>
		<PrimaryButton
					cta="Upgrade Loyalty Plan"
					size="xs"
					@click="() => this.$router.push('/loyalty/settings/plans')"
					/>
	</div>

	<div class="bg-gradient-to-tr from-[#4F3EAC] to-[#5E48F8] p-4 w-full justify-center shadow-lg flex items-center" v-if="!isFeatureAvailable && isTrialEnded">
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 14H11V9.8L12.6 11.4L14 10L10 6L6 10L7.4 11.4L9 9.8V14ZM10 20C8.61667 20 7.31667 19.7375 6.1 19.2125C4.88333 18.6875 3.825 17.975 2.925 17.075C2.025 16.175 1.3125 15.1167 0.7875 13.9C0.2625 12.6833 0 11.3833 0 10C0 8.61667 0.2625 7.31667 0.7875 6.1C1.3125 4.88333 2.025 3.825 2.925 2.925C3.825 2.025 4.88333 1.3125 6.1 0.7875C7.31667 0.2625 8.61667 0 10 0C11.3833 0 12.6833 0.2625 13.9 0.7875C15.1167 1.3125 16.175 2.025 17.075 2.925C17.975 3.825 18.6875 4.88333 19.2125 6.1C19.7375 7.31667 20 8.61667 20 10C20 11.3833 19.7375 12.6833 19.2125 13.9C18.6875 15.1167 17.975 16.175 17.075 17.075C16.175 17.975 15.1167 18.6875 13.9 19.2125C12.6833 19.7375 11.3833 20 10 20ZM10 18C12.2333 18 14.125 17.225 15.675 15.675C17.225 14.125 18 12.2333 18 10C18 7.76667 17.225 5.875 15.675 4.325C14.125 2.775 12.2333 2 10 2C7.76667 2 5.875 2.775 4.325 4.325C2.775 5.875 2 7.76667 2 10C2 12.2333 2.775 14.125 4.325 15.675C5.875 17.225 7.76667 18 10 18Z" fill="#FFA3DF"/>
        </svg>
        <div class="text-white ml-2">Your trial has expired and your program has been deactivated.</div>
        <div class="px-2 py-2 text-white text-sm border-[#FFF] border rounded-full ml-4 hover:text-black hover:bg-[#FFF] hover:cursor-pointer transitional-bg delay-200" @click="() => this.$router.push('/loyalty/settings/plans')">
                    Choose a Plan
        </div>
    </div>

	<div class="m-3 sm:m-10 sm:m-7" v-if="isFeatureAvailable || isTrialEnded">
		<div class="flex flex-col sm:flex-row items-center justify-between">
			<div class="flex justify-center items-center relative">
				<div class="text-3xl sm:text-6xl font-sans font-medium opacity-70 text-ralblack-primary">
					Email Automation
				</div>
			</div>
		</div>
		<div class="flex mb-10 mt-2 ml-1">
			<LearnMoreText text="Learn more" url="https://docs.raleon.io/docs/sending-loyalty-emails-with-raleon"></LearnMoreText>
			<span class="text-neutral-500 text-sm font-medium">&nbsp;</span>
			<span class="text-neutral-800 text-opacity-80 text-sm font-medium">about sending Loyalty Emails</span>
		</div>

		<div v-if="!isLoadingEventData && freeTrialData != {} && !freeTrialData.hasPaidPlan && freeTrialData.daysLeft < 0" class="bg-[#222141] rounded-2xl p-4 mt-12 mb-8">
			<div class="inline-flex items-center mb-4">
				<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
					<path d="M9 14H11V9.8L12.6 11.4L14 10L10 6L6 10L7.4 11.4L9 9.8V14ZM10 20C8.61667 20 7.31667 19.7375 6.1 19.2125C4.88333 18.6875 3.825 17.975 2.925 17.075C2.025 16.175 1.3125 15.1167 0.7875 13.9C0.2625 12.6833 0 11.3833 0 10C0 8.61667 0.2625 7.31667 0.7875 6.1C1.3125 4.88333 2.025 3.825 2.925 2.925C3.825 2.025 4.88333 1.3125 6.1 0.7875C7.31667 0.2625 8.61667 0 10 0C11.3833 0 12.6833 0.2625 13.9 0.7875C15.1167 1.3125 16.175 2.025 17.075 2.925C17.975 3.825 18.6875 4.88333 19.2125 6.1C19.7375 7.31667 20 8.61667 20 10C20 11.3833 19.7375 12.6833 19.2125 13.9C18.6875 15.1167 17.975 16.175 17.075 17.075C16.175 17.975 15.1167 18.6875 13.9 19.2125C12.6833 19.7375 11.3833 20 10 20ZM10 18C12.2333 18 14.125 17.225 15.675 15.675C17.225 14.125 18 12.2333 18 10C18 7.76667 17.225 5.875 15.675 4.325C14.125 2.775 12.2333 2 10 2C7.76667 2 5.875 2.775 4.325 4.325C2.775 5.875 2 7.76667 2 10C2 12.2333 2.775 14.125 4.325 15.675C5.875 17.225 7.76667 18 10 18Z" fill="#FFA3DF"/>
				</svg>
				<p class="text-white text-lg ml-2">Upgrade for Email Automation</p>
			</div>
			<p class="text-white text-base">
			Raleon's built-in email automation is a key way to keep your customers engaged with your loyalty program and increase repeat purchase rate. Wanting more? Check out our email integrations.
			</p>
			<div class="mt-4 flex justify-left">
				<PrimaryButton
					cta="Upgrade to Growth"
					size="xs"
					@click="() => this.$router.push('/loyalty/integrations')"
				/>
			</div>
		</div>

		<div v-if="!isLoadingEventData && freeTrialData != {} && !freeTrialData.hasPaidPlan && freeTrialData.daysLeft > 0"
		class="bg-[#222141] rounded-2xl p-4 mt-12 mb-8">
			<div class="inline-flex items-center mb-4">
				<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#FFA3DF"><path d="M320-160h320v-120q0-66-47-113t-113-47q-66 0-113 47t-47 113v120Zm160-360q66 0 113-47t47-113v-120H320v120q0 66 47 113t113 47ZM160-80v-80h80v-120q0-61 28.5-114.5T348-480q-51-32-79.5-85.5T240-680v-120h-80v-80h640v80h-80v120q0 61-28.5 114.5T612-480q51 32 79.5 85.5T720-280v120h80v80H160Zm320-80Zm0-640Z"/></svg>
				<p class="text-white text-lg ml-2">Growth Plan Feature (Available During Trial)</p>
			</div>
			<p class="text-white text-base">
			Raleon's built-in email automation is a key way to keep your customers engaged with your loyalty program and increase repeat purchase rate. Wanting more? Check out our email integrations.
			</p>
			<p class="text-white text-base italic mt-4">Upgrade to keep this feature after your trial.</p>
			<div class="mt-4 flex justify-left">
				<PrimaryButton
					cta="Upgrade to Growth"
					size="xs"
					@click="() => this.$router.push('/loyalty/integrations')"
				/>
			</div>
		</div>

		<div class="my-4">
			<div class="mt-2">
				<div
					class="overflow-x-auto bg-white bg-opacity-75 rounded-2xl shadow border border-ralprimary-light border-opacity-50 flex-grow transition-all duration-300 ease-in-out overflow-hidden">
					<WTERewardTable
						header1="Event Type"
						header2="Status"
						:tableData="loyaltyEventData"
						:hideSort="true"
						@editClicked="handleLoyaltyEventClicked"
						@toggleChanged="handleLoyaltyEventActiveToggle"
					/>
					<div class="flex flex-col items-center justify-center h-22 mb-2"
						v-if="loyaltyEventData.length <= 0 && !isLoadingEventData">
						<svg width="540" height="30" viewBox="0 0 501 30" fill="none" xmlns="http://www.w3.org/2000/svg">
							<g opacity="0.7">
								<g filter="url(#filter0_d_605_5769)">
									<rect x="6" y="4" width="501" height="17.0606" rx="8.53032" fill="white"
										fill-opacity="0.75" shape-rendering="crispEdges" />
									<rect x="6.5" y="4.5" width="500" height="16.0606" rx="8.03032" stroke="#D6D1FD"
										shape-rendering="crispEdges" />
								</g>
								<rect x="387" y="10" width="110" height="5" rx="2.5" fill="url(#paint0_linear_605_5769)" />
								<rect x="237" y="10" width="110" height="5" rx="2.5" fill="url(#paint1_linear_605_5769)" />
								<rect x="47" y="10" width="110" height="5" rx="2.5" fill="url(#paint2_linear_605_5769)" />
							</g>
							<defs>
								<filter id="filter0_d_605_5769" x="0" y="0" width="513" height="29.0605"
									filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
									<feFlood flood-opacity="0" result="BackgroundImageFix" />
									<feColorMatrix in="SourceAlpha" type="matrix"
										values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
									<feOffset dy="2" />
									<feGaussianBlur stdDeviation="3" />
									<feComposite in2="hardAlpha" operator="out" />
									<feColorMatrix type="matrix"
										values="0 0 0 0 0.0509804 0 0 0 0 0.0392157 0 0 0 0 0.172549 0 0 0 0.08 0" />
									<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_605_5769" />
									<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_605_5769"
										result="shape" />
								</filter>
								<linearGradient id="paint0_linear_605_5769" x1="430" y1="-0.555557" x2="433.874"
									y2="26.1776" gradientUnits="userSpaceOnUse">
									<stop stop-color="#D9D9D9" />
									<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
								</linearGradient>
								<linearGradient id="paint1_linear_605_5769" x1="280" y1="-0.555557" x2="283.874"
									y2="26.1776" gradientUnits="userSpaceOnUse">
									<stop stop-color="#D9D9D9" />
									<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
								</linearGradient>
								<linearGradient id="paint2_linear_605_5769" x1="90" y1="-0.555557" x2="93.8744" y2="26.1776"
									gradientUnits="userSpaceOnUse">
									<stop stop-color="#D9D9D9" />
									<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
								</linearGradient>
							</defs>
						</svg>
					</div>
				</div>
			</div>
		</div>

	<div class="bg-ralinfo-light rounded-2xl p-4 mt-4">
		<div class="inline-flex items-center mb-4">
			<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368"><path d="M240-80q-33 0-56.5-23.5T160-160v-400q0-33 23.5-56.5T240-640h360v-80q0-50-35-85t-85-35q-42 0-73.5 25.5T364-751q-4 14-16.5 22.5T320-720q-17 0-28.5-11t-8.5-26q14-69 69-116t128-47q83 0 141.5 58.5T680-720v80h40q33 0 56.5 23.5T800-560v400q0 33-23.5 56.5T720-80H240Zm0-80h480v-400H240v400Zm240-120q33 0 56.5-23.5T560-360q0-33-23.5-56.5T480-440q-33 0-56.5 23.5T400-360q0 33 23.5 56.5T480-280ZM240-160v-400 400Z"/></svg>
			<p class="text-ralblack-primary text-lg ml-2">Looking for More Advanced Email Automation?</p>
		</div>
		<p class="text-ralblack-primary text-base">Take your email marketing to the next level with our Klaviyo integration. Get access to powerful features like email template editing, custom flows, and more.</p>
		<div class="mt-4 flex justify-center">
			<PrimaryButton
				cta="Setup Klaviyo Integration"
				size="xs"
				@click="() => this.$router.push('/loyalty/integrations')"
			/>
		</div>
	</div>
	</div>
</template>

<script lang="ts">

import StatusMessage from '../components/StatusMessage.ts.vue';
import LearnMoreText from '../components/LearnMoreText.ts.vue';
import PrimaryButton from '../components/PrimaryButton.ts.vue';
import WTERewardTable from '../components/WTERewardTable.vue';
import ModalBlank from '../components/ModalBlank.vue';
import { getLoyaltyEvents, activeToggleLoyaltyEvent } from '../services/loyalty-emails.js';
import { isFeatureAvailable, freeTrialInfo } from '../services/features.js';

export default {
	name: 'LoyaltyEmails',
	components: {
		StatusMessage,
		LearnMoreText,
		PrimaryButton,
		WTERewardTable,

	},
	data() {
		return {
			status: {},
			loyaltyEventData: [],
			isLoadingEventData: false,
			pricingModal: true,
			freeTrialData: {},
		};
	},

	async mounted() {
		this.freeTrialData = await freeTrialInfo();
		this.isLoadingEventData = true;
		let loyaltyEvents = await getLoyaltyEvents();
		if (loyaltyEvents && loyaltyEvents.length > 0) {
			loyaltyEvents = loyaltyEvents.filter(
				event => !EXCLUDED_LOYALTY_EVENTS.includes(event.name)
			);
		}
		this.loyaltyEventData = loyaltyEvents.map(event => ({
			id: event.id,
			name: event.name,
			title: this.getEventDisplayName(event),
			subtitle: event.description,
			dataStructure: event.dataStructure,
			toggle: event.active,
			configured: event.configured,
		}));
		this.isLoadingEventData = false;
	},
	computed: {
		isFeatureAvailable() {
			return isFeatureAvailable('email-automation');
		},
		isTrialEnded() {
			const freeTrialData = JSON.parse(localStorage.getItem('freeTrialData'));

			return freeTrialData != {} && !freeTrialData.hasPaidPlan && freeTrialData.daysLeft < 0
		},
	},
	methods: {
		handleLoyaltyEventClicked(event) {
			this.$router.push('/loyalty/email?eventName=' + event.name);
		},
		async handleLoyaltyEventActiveToggle(event) {
			try {
				await activeToggleLoyaltyEvent(event.name, event.toggle);
				this.status = {
					type: 'success',
					message: `${event.title} email ${event.toggle ? 'activated' : 'deactivated'}`,
				};
			} catch (e) {
				console.error(e);
				this.status = {
					type: 'fail',
					message: `Failed to activate ${event.title} email`,
				};
			}
		},
		getEventDisplayName(event) {
			switch (event.name) {
				case 'reward_given':
					return 'Reward Given';
				case 'referral_completed':
					return 'Referral Completed';
				case 'send_referral':
					return 'Send Referral';
				case 'point_balance_change':
					return 'Points Earned';
				default:
					return event.friendlyName.replace('Raleon ', '');
			}
		},
	}
};

const EXCLUDED_LOYALTY_EVENTS = ['offer_received', 'next_reward_update']

</script>


<style>

	.no-focus-outline {
		box-shadow: none !important;
		outline: none !important;
	}

</style>
