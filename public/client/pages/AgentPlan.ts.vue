<template>
  <div class="px-6 pt-6 pb-4 bg-white shadow-sm sm:flex-row">
    <div class="flex items-center">
      <div
        class="mr-4 shrink-0 cursor-pointer"
        @click="this.$router.push('/ai-strategist/planning')"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          height="24px"
          viewBox="0 -960 960 960"
          width="24px"
          fill="#5f6368"
        >
          <path
            d="m313-440 224 224-57 56-320-320 320-320 57 56-224 224h487v80H313Z"
          />
        </svg>
      </div>

      <div class="flex-1">
        <div class="flex items-center justify-between">
          <div
            class="text-3xl sm:text-3xl font-sans font-medium opacity-70 text-ralblack-primary"
          >
            {{ currentPlan.name }}
          </div>

          <div class="flex w-full sm:w-auto justify-start gap-2">
            <!-- Download CSV Button -->
            <button
              @click="downloadCampaignsCSV"
              :disabled="!sortedCampaigns.length"
              class="inline-flex items-center px-3 py-1.5 border rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-60"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 mr-1.5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                />
              </svg>
              Download
            </button>

            <PrimaryButton
              :cta="
                campaignsGenerating
                  ? 'Generating...'
                  : currentPlan.inProgress
                    ? 'View On Calendar'
                    : 'Generate Campaigns'
              "
              :disabled="campaignsGenerating"
              size="xs"
              @click="
                !campaignsGenerating && handleMakeItHappen(currentPlan.id)
              "
            />
          </div>
        </div>

        <!-- View mode toggle moved to header -->
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-2 text-sm mb-3">
            <span
              class="px-2.5 py-1 rounded-full bg-purple-50 text-purple-600 font-medium"
            >
              {{
                new Date(currentPlan.startdate).toLocaleDateString('en-US', {
                  month: 'short',
                  day: 'numeric',
                  year: 'numeric',
                })
              }}
              -
              {{
                new Date(currentPlan.enddate).toLocaleDateString('en-US', {
                  month: 'short',
                  day: 'numeric',
                  year: 'numeric',
                })
              }}
            </span>
            <span class="px-2.5 py-1 rounded-full bg-gray-50 text-gray-600">
              {{ currentPlan.businessGoal }}
            </span>
          </div>

          <!-- Table view is now the only option -->
        </div>
      </div>
    </div>
  </div>

  <div class="p-2 sm:p-7">
    <Transition
      appear
      enter-active-class="transition duration-300 ease-out"
      enter-from-class="transform scale-95 opacity-0"
      enter-to-class="transform scale-100 opacity-100"
    >
      <div class="space-y-4 relative">
        <div class="w-full">
          <!-- Replace progress tracker with component -->
          <EmailCampaignProgressTracker :current-step="currentStep" />
          <div class="bg-white rounded-xl overflow-visible">
            <!-- Replace Summary Section with the PlanSummary component -->
            <PlanSummary
              :description="currentPlan.description"
              :userPrompt="formattedUserPrompt"
              :dataSummary="currentPlan.plannerPlanVersions?.[0]?.dataSummary"
            />

            <!-- Plan Generation Status Alert (Removed - Replaced with animation below) -->

            <!-- New Plan Generation Animation -->
            <div
              v-if="isPlanStillGenerating"
              class="px-8 py-6 bg-indigo-50 border-t border-indigo-100 generating-card overflow-hidden"
            >
              <div class="flex flex-col items-center relative z-10">
                <div class="flex items-center gap-2 mb-3">
                  <svg
                    class="animate-spin h-5 w-5 text-indigo-600"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      class="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      stroke-width="4"
                    ></circle>
                    <path
                      class="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  <div class="text-lg font-medium text-indigo-700">
                    Generating Plan Details...
                  </div>
                </div>
                <p class="text-sm text-indigo-600 text-center mb-4">
                  The AI is creating the initial plan structure and campaign
                  ideas. This might take a minute.
                </p>
                <!-- Simple indeterminate progress bar -->
                <div
                  class="w-full max-w-md bg-white rounded-full h-2 overflow-hidden"
                >
                  <div
                    class="bg-indigo-600 h-full rounded-full indeterminate-progress"
                  ></div>
                </div>
              </div>
            </div>

            <!-- Campaign Generation Progress Component -->
            <CampaignGenerationProgress
              :campaignsGenerating="campaignsGenerating"
              :currentPlan="currentPlan"
              :planId="currentPlan.id"
              @progress-update="updateProgress"
              @generation-complete="handleGenerationComplete"
              @plan-updated="updatePlan"
            />

            <!-- Campaigns Section -->
            <div class="px-8 py-6">
              <div class="space-y-8">
                <!-- Loading skeleton -->
                <div v-if="isLoading" class="space-y-4">
                  <div
                    v-for="i in 3"
                    :key="i"
                    class="bg-white rounded-lg p-4 border border-gray-200 animate-pulse"
                  >
                    <div class="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
                    <div class="h-8 bg-gray-200 rounded w-3/4 mb-4"></div>
                    <div class="h-4 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
                <!-- Table View -->
                <CampaignTable
                  v-else
                  :campaigns="sortedCampaigns"
                  :inProgress="currentPlan.inProgress"
                  :archivedCampaigns="archivedCampaigns"
                  :highlightedCampaigns="highlightedCampaigns"
                  @edit-field="handleEditField"
                  @delete="handleDeleteCampaign"
                  @navigate="navigateToCampaignTask"
                />

                <div
                  class="flex justify-center items-center mb-4"
                  v-if="!currentPlan.inProgress"
                >
                  <div
                    @click="handleAddCampaign"
                    :class="{
                      'opacity-50 cursor-not-allowed': isGeneratingCampaign,
                    }"
                    :disabled="isGeneratingCampaign"
                    class="inline-flex whitespace-nowrap items-center border font-semibold py-2 px-4 rounded-full md:mt-0 lg:mt-0 xl:mt-0 mt-4 transition-all duration-300 cursor-pointer border-ralprimary-dark hover:text-white hover:bg-ralsecondary-start"
                  >
                    <template v-if="isGeneratingCampaign">
                      <svg
                        class="animate-spin -ml-1 mr-3 h-4 w-4 opacity-75"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          class="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          stroke-width="4"
                        ></circle>
                        <path
                          class="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      <span>Generating campaign...</span>
                    </template>
                    <template v-else>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="14"
                        height="14"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        class="mr-1.5"
                      >
                        <path
                          d="m21.64 3.64-1.28-1.28a1.21 1.21 0 0 0-1.72 0L2.36 18.64a1.21 1.21 0 0 0 0 1.72l1.28 1.28a1.2 1.2 0 0 0 1.72 0L21.64 5.36a1.2 1.2 0 0 0 0-1.72"
                        ></path>
                        <path d="m14 7 3 3"></path>
                        <path d="M5 6v4"></path>
                        <path d="M19 14v4"></path>
                        <path d="M10 2v2"></path>
                        <path d="M7 8H3"></path>
                        <path d="M21 16h-4"></path>
                        <path d="M11 3H9"></path>
                      </svg>
                      <span class="text-xs font-semibold font-['Inter']">
                        Add Campaign Idea
                      </span>
                    </template>
                  </div>
                </div>

                <!-- Action Bar -->
                <div
                  class="px-8 py-4 flex items-center justify-between bg-white border-t border-gray-100"
                >
                  <div class="flex items-center gap-4">
                    <button
                      class="inline-flex items-center justify-center px-4 py-2 rounded-md transition-colors"
                      :class="{
                        'bg-red-50 text-red-600 hover:bg-red-100':
                          !currentPlan.archived,
                        'bg-green-50 text-green-600 hover:bg-green-100':
                          currentPlan.archived,
                      }"
                      @click="
                        currentPlan.archived
                          ? unarchivePlan(currentPlan.id)
                          : handleArchivePlan(currentPlan.id)
                      "
                    >
                      <svg
                        class="w-4 h-4 mr-2"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      >
                        <path
                          d="M3 6h18M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"
                        />
                      </svg>
                      {{
                        currentPlan.archived ? 'Unarchive Plan' : 'Archive Plan'
                      }}
                    </button>

                    <!-- Download CSV Button (bottom) -->
                    <button
                      @click="downloadCampaignsCSV"
                      :disabled="!sortedCampaigns.length"
                      class="inline-flex items-center px-3 py-1.5 border rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-60"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-4 w-4 mr-1.5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                        />
                      </svg>
                      Download
                    </button>
                  </div>
                  <PrimaryButton
                    :cta="
                      campaignsGenerating
                        ? 'Generating...'
                        : currentPlan.inProgress
                          ? 'View On Calendar'
                          : 'Generate Campaigns'
                    "
                    :disabled="campaignsGenerating"
                    size="xs"
                    @click="
                      !campaignsGenerating && handleMakeItHappen(currentPlan.id)
                    "
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Transition>
    <!-- <div class="px-4 py-6 sm:px-8">
      <CommentSection
        :planId="$route.params.planId"
        :urlDomain="URL_DOMAIN"
      />
    </div> -->
    <ModalBlank
      id="confirmation-modal"
      :modalOpen="isConfirmationModalOpen"
      @close-modal="isConfirmationModalOpen = false"
    >
      <div class="p-5 flex flex-col space-y-4">
        <div class="text-lg font-semibold text-ralblack-primary">
          {{ confirmationModalTitle }}
        </div>
        <div class="text-gray-600">
          {{ confirmationModalMessage }}
        </div>
        <div class="flex justify-end gap-3 mt-4">
          <button
            @click="handleConfirmationModalConfirm"
            class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
          >
            Confirm
          </button>
          <button
            @click="isConfirmationModalOpen = false"
            class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
          >
            Cancel
          </button>
        </div>
      </div>
    </ModalBlank>
  </div>
</template>

<script>
import PrimaryButton from '../components/PrimaryButton.ts.vue'
import * as Utils from '../../client-old/utils/Utils';
import ModalBlank from '../components/ModalBlank.vue';
import EmailCampaignProgressTracker from '../components/EmailCampaignProgressTracker.ts.vue';
import PlanSummary from '../components/PlanSummary.ts.vue';
import CampaignGenerationProgress from '../components/agent-plan/CampaignGenerationProgress.ts.vue';
import CampaignCard from '../components/agent-plan/CampaignCard.ts.vue';
import CampaignTable from '../components/agent-plan/CampaignTable.ts.vue';
import CommentSection from '../components/CommentSection.ts.vue';
import { customerIOTrackEvent } from '../services/customerio.js';
const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	name: 'MarketingPlan',

	components: {
		PrimaryButton,
		ModalBlank,
		EmailCampaignProgressTracker,
		PlanSummary,
		CampaignGenerationProgress,
		CampaignCard,
		CampaignTable,
		CommentSection
	},

	data() {
		return {
			// Manage dialog, popup and main items
			showAddCampaignDialog: false,
			currentPlan: {},
			editingState: { type: null, campaignId: null }, // This is initialized but later may be set to null
			editValue: '',
			isSaving: false,
			isDropdownOpen: false,
			isArchiving: false,
			campaignsGenerating: false,
				showUserPrompt: false,
			isGeneratingCampaign: false,
			archivedCampaigns: new Set(),
			removedCampaigns: new Set(),
				highlightedCampaigns: new Set(),
			handleClickOutside: () => {},
                        generationProgress: 0,
                        // Loading state for initial plan fetch
                        isLoading: true,
                        // Set to null for automatic detection, 'card' or 'table' to force that view
                        forceViewMode: 'table',
			// Modal-related properties that were accidentally removed
			isConfirmationModalOpen: false,
			confirmationModalTitle: '',
			confirmationModalMessage: '',
			confirmationModalAction: null,
			pollingIntervalId: null // Added for polling
		}
	},

	computed: {
		formattedUserPrompt() {
			if (!this.currentPlan?.plannerPlanVersions?.[0]?.userPrompt) return '';

			const prompt = this.currentPlan.plannerPlanVersions[0].userPrompt;

			// Check if this is a month planning prompt
			if (prompt.includes('Plan a month of email campaigns with these details:')) {
				// Split prompt into lines (not used directly but kept for future reference)
				// const lines = prompt.split('\n');
				let formattedPrompt = '📅 Monthly Email Plan\n\n';

				// Extract values using regex to handle different formatting cases
				const emailCountMatch = prompt.match(/Number of emails: (.*?)(?=-|$)/);
				const planningMonthMatch = prompt.match(/Planning month: (.*?)(?=-|$)/);
				const guidanceMatch = prompt.match(/Additional guidance\/topics: (.*?)(?=$)/);

				if (emailCountMatch) {
					formattedPrompt += `📧 **Number of emails:** ${emailCountMatch[1].trim()}\n\n`;
				}

				if (planningMonthMatch) {
					formattedPrompt += `🗓️ **Planning for:** ${planningMonthMatch[1].trim()}\n\n`;
				}

				if (guidanceMatch) {
					formattedPrompt += `🎯 **Guidance & Topics:** ${guidanceMatch[1].trim()}`;
				}

				return formattedPrompt;
			}

			// Return original prompt for other cases
			return prompt;
		},

		sortedCampaigns() {
			if (!this.currentPlan) {
				return [];
			}
			if (!this.currentPlan.plannerPlanVersions || this.currentPlan.plannerPlanVersions.length === 0) {
				return [];
			}
			let campaigns = this.currentPlan.plannerPlanVersions[0].plannerCampaigns;
			if(!campaigns) return [];
			return campaigns.sort((a, b) => new Date(a.scheduledDate) - new Date(b.scheduledDate));
		},

		shouldShowTableView() {
			// Always use table view
			return true;
		},



		isPlanStillGenerating() {
			console.log('Checking if plan is still generating...');
			console.log('Current plan:', this.currentPlan);

			// Check if the plan has a generationStatus field
			if (this.currentPlan.generationStatus !== undefined) {
				// If generationStatus is 'processing', the plan is still being generated
				return this.currentPlan.generationStatus === 'processing';
			}

			// For backward compatibility with plans that don't have generationStatus
			// Check if the plan is in progress and has campaigns that are still processing
			if (this.currentPlan.inProgress) {
				const campaigns = this.currentPlan.plannerPlanVersions[0]?.plannerCampaigns || [];

				// If there are no campaigns yet, the plan is still being generated
				if (campaigns.length === 0) {
					return true;
				}

				// Check if any campaign tasks are still processing
				const hasProcessingTasks = campaigns.some(campaign =>
					campaign.task && campaign.task.status === 'Processing'
				);

				return hasProcessingTasks;
			}

			// If the plan is not in progress, it's not being generated
			return false;
		},

		currentStep() {
			// If plan is archived, we consider it as "done"
			if (this.currentPlan.archived) {
				return 4;
			}

			// If campaigns are generating, we're at step 2
			if (this.campaignsGenerating) {
				return 2;
			}

			// If plan is in progress, we're at step 3
			if (this.currentPlan.inProgress) {
				// Check if all tasks are complete
				const campaigns = this.currentPlan.plannerPlanVersions[0]?.plannerCampaigns || [];
				const allTasksComplete = campaigns.length > 0 &&
					campaigns.every(campaign => campaign.taskStatus === 'Complete');

				// If all tasks are complete, we're at step 4
				if (allTasksComplete) {
					return 4;
				}

				return 3;
			}

			// Default: we're at planning stage (step 1)
			return 1;
		},
	},

	methods: {
		isArchived(campaignId) {
			return this.archivedCampaigns.has(campaignId);
		},

		isFullyRemoved(campaignId) {
			return this.removedCampaigns.has(campaignId);
		},
		showConfirmationModal(title, message, action) {
			this.confirmationModalTitle = title;
			this.confirmationModalMessage = message;
			this.confirmationModalAction = action;
			this.isConfirmationModalOpen = true;
		},

		// Handle the "Confirm" button click
		handleConfirmationModalConfirm() {
			if (this.confirmationModalAction) {
				this.confirmationModalAction(); // Execute the stored action
			}
			this.isConfirmationModalOpen = false; // Close the modal
		},
		async unarchivePlan(planId) {
			this.showConfirmationModal(
				'Unarchive Plan',
				'Are you sure you want to unarchive this plan?',
				async () => {
					try {
						const response = await fetch(
							`${URL_DOMAIN}/organization-planner-plans/${planId}/unarchive`,
							{
								method: 'PATCH',
								headers: {
									Authorization: `Bearer ${localStorage.getItem('token')}`,
									'Content-Type': 'application/json',
								},
							}
						);

						const result = await response.json();

						if (result.status === 200) {
							this.currentPlan.archived = false;
						} else {
							console.error('Failed to unarchive plan:', result.message);
						}
					} catch (error) {
						console.error('Error unarchiving plan:', error);
					}
				}
			);
		},

		async handleMakeItHappen(planId) {
			customerIOTrackEvent('AI Marketing Strategist - Make it happen');
			try {
				// If the plan is already in progress, redirect to task list with this plan filtered
				if (this.currentPlan.inProgress) {
					// Get the plan version ID
					const planVersionId = this.currentPlan.plannerPlanVersions[0].id;

					// Use replace instead of push to avoid stacking history entries
					this.$router.replace({
						path: '/ai-strategist/tasks',
						query: { plan: planVersionId }
					});
					return;
				}

				// Start work on the plan if not already in progress
				await this.startWorkOnPlan(planId);
			} catch (error) {
				console.error('Error making it happen:', error);
				alert('Error making it happen: ' + error.message);
			}
		},
		async startWorkOnPlan(planId) {
			this.showConfirmationModal(
				'Start Work on Plan?',
				'This will get your AI team to turn each campaign in this plan into an actual project with its segment, creative briefs, email content, and more.',
				async () => {
					try {
						// Set generating state to true
						this.campaignsGenerating = true;
						this.generationProgress = 0;

						const response = await fetch(
							`${URL_DOMAIN}/organization-planner-plans/${planId}/start-work`,
							{
								method: 'PATCH',
								headers: {
									Authorization: `Bearer ${localStorage.getItem('token')}`,
									'Content-Type': 'application/json',
								},
							}
						);

						const result = await response.json();

						if (result.status !== 200) {
							console.error('Failed to start work on plan:', result.message);
							this.showConfirmationModal(
								'Error',
								'Failed to start work on plan: ' + result.message
							);
							this.campaignsGenerating = false;
						}
					} catch (error) {
						console.error('Error starting work on plan:', error);
						this.showConfirmationModal(
							'Error',
							'Error starting work on plan: ' + error.message
						);
						this.campaignsGenerating = false;
					}
				}
			);
		},

		// New methods to handle events from CampaignGenerationProgress component
		updateProgress(progress) {
			this.generationProgress = progress;
		},

		handleGenerationComplete() {
			this.campaignsGenerating = false;
		},

		updatePlan(plan) {
			this.currentPlan = plan;

			// Sync task.status with campaign.taskStatus for all campaigns
			const campaigns = this.currentPlan.plannerPlanVersions[0]?.plannerCampaigns || [];
			campaigns.forEach(campaign => {
				if (campaign.task) {
					campaign.taskStatus = campaign.task.status;
				}
			});
		},
		async handleArchivePlan(planId) {
			this.showConfirmationModal(
				'Archive Plan',
				'Are you sure you want to archive this plan?',
				async () => {
					try {
						const response = await fetch(
							`${URL_DOMAIN}/organization-planner-plans/${planId}/archive`,
							{
								method: 'PATCH',
								headers: {
									Authorization: `Bearer ${localStorage.getItem('token')}`,
									'Content-Type': 'application/json',
								},
							}
						);

						const result = await response.json();

						if (result.status === 200) {
							this.$router.push('/ai-strategist/planning');
						} else {
							console.error('Failed to archive plan:', result.message);
						}
					} catch (error) {
						console.error('Error archiving plan:', error);
					}
				}
			);
		},
		async handleDeleteCampaign(campaignId) {
			this.showConfirmationModal(
				'Delete Campaign',
				'Are you sure you want to delete this campaign?',
				async () => {
					try {
						// Set the archived state
						this.archivedCampaigns.add(campaignId);
						this.removedCampaigns.add(campaignId);

						// Wait for the skipped message to show and the animation to complete
						setTimeout(async () => {
							// Remove the campaign from the backend
							const response = await fetch(
								`${URL_DOMAIN}/planner/campaign/${campaignId}`,
								{
									method: 'DELETE',
									headers: {
										Authorization: `Bearer ${localStorage.getItem('token')}`,
										'Content-Type': 'application/json',
									},
								}
							);

							const result = await response.json();

							// Mark as fully removed
							this.removedCampaigns.add(campaignId);

							if (result.status === 200) {
								// Remove the campaign from the frontend state
								const versionIndex = this.currentPlan.plannerPlanVersions.findIndex(
									version => version.plannerCampaigns.some(campaign => campaign.id === campaignId)
								);

								if (versionIndex !== -1) {
									this.currentPlan.plannerPlanVersions[versionIndex].plannerCampaigns =
										this.currentPlan.plannerPlanVersions[versionIndex].plannerCampaigns.filter(
											campaign => campaign.id !== campaignId
										);

									// Force a re-render
									this.$forceUpdate();
								}
							} else {
								console.error('Failed to delete campaign:', result.message);
							}
						}, 1000); // Wait for 1 second before fully removing
					} catch (error) {
						console.error('Error deleting campaign:', error);
					}
				}
			);
		},

		toggleDropdown() {
			this.isDropdownOpen = !this.isDropdownOpen;
		},

		handleAddCampaignManually() {
			this.isDropdownOpen = false;
			console.log('Add campaign manually clicked');
		},


		formatDate(dateString) {
			if (!dateString) return '';

			// The direct string parsing approach prevents timezone issues
			if (typeof dateString === 'string' && dateString.match(/^\d{4}-\d{2}-d{2}$/)) {
				// Extract year, month, day directly from string
				const [year, month, day] = dateString.split('-').map(num => parseInt(num, 10));

				// Map month number to month name (avoiding Date object timezone issues)
				const monthNames = [
					"January", "February", "March", "April", "May", "June",
					"July", "August", "September", "October", "November", "December"
				];

				// Format directly without using Date object
				return `${monthNames[month-1]} ${day}, ${year}`;
			}

			// Fallback to UTC-based Date object for other date formats
			try {
				// Create a UTC date to avoid timezone shifts
				const date = new Date(dateString);
				// Force UTC timezone display
				return date.toLocaleDateString('en-US', {
					month: 'long',
					day: 'numeric',
					year: 'numeric',
					timeZone: 'UTC' // Force UTC timezone
				});
			} catch (error) {
				console.error('Error formatting date in AgentPlan:', error, dateString);
				return dateString; // Return original string as fallback
			}
		},

		startEditing(type, campaignId) {
			// Don't start editing if the plan is in progress
			if (this.currentPlan.inProgress) {
				return;
			}

			customerIOTrackEvent('AI Marketing Strategist - Started Edit');
			let campaigns = this.currentPlan?.plannerPlanVersions?.[0]?.plannerCampaigns;
			if (!campaigns) {
				console.error('No campaigns found in the plan data');
				return;
			}

			console.log('Starting edit mode:', type, campaignId);
			const campaign = campaigns.find(c => c.id === campaignId);
			if (!campaign) {
				console.log('No campaign found');
				return;
			}

			this.editingState = { type, campaignId };
			if (type === 'scheduledDate') {
				// For date editing, we want to make sure we don't adjust for timezone
				// Use direct string parsing instead of Date object to avoid timezone issues
				if (typeof campaign.scheduledDate === 'string' && campaign.scheduledDate.match(/^\d{4}-\d{2}-\d{2}$/)) {
					// If it's already in YYYY-MM-DD format, use it directly
					this.editValue = campaign.scheduledDate;
					console.log('Using direct date string for edit:', this.editValue);
				} else {
					// For other formats, try to convert safely
					try {
						// First try to get just the date part if it's an ISO string
						if (typeof campaign.scheduledDate === 'string' && campaign.scheduledDate.includes('T')) {
							this.editValue = campaign.scheduledDate.split('T')[0];
						} else {
							// Create a UTC date and format carefully
							const date = new Date(campaign.scheduledDate);
							// Use UTC methods to prevent timezone shifts
							const year = date.getUTCFullYear();
							const month = String(date.getUTCMonth() + 1).padStart(2, '0');
							const day = String(date.getUTCDate()).padStart(2, '0');
							this.editValue = `${year}-${month}-${day}`;
						}
						console.log('Converted date for edit:', this.editValue);
					} catch (error) {
						console.error('Error processing date for edit:', error);
						// Fallback
						const date = new Date(campaign.scheduledDate);
						this.editValue = date.toISOString().split('T')[0];
					}
				}
			} else {
				this.editValue = campaign[type] || '';  // Add fallback to empty string if property doesn't exist
			}
			console.log('Edit value:', this.editValue);

			this.$nextTick(() => {
				const refName = `${type}Input-${campaignId}`;
				const input = this.$refs[refName];
				if (input) {
					const inputElement = Array.isArray(input) ? input[0] : input;
					inputElement?.focus();
					if (type !== 'scheduledDate' && inputElement?.setSelectionRange) {
						inputElement.setSelectionRange(0, 0);
					}
				}
			});
		},

		async handleAddCampaign() {
			// Don't allow adding campaigns if the plan is in progress
			if (this.currentPlan.inProgress) {
				return;
			}

			this.isGeneratingCampaign = true;
			try {
				const versionId = this.currentPlan.plannerPlanVersions[0].id;
				const response = await fetch(`${URL_DOMAIN}/planner/version/${versionId}/campaign`, {
					method: 'POST',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Content-Type': 'application/json'
					}
				});

				const result = await response.json();

				if (result.status === 200) {
					// Add the new campaign to the current version's campaigns
					this.currentPlan.plannerPlanVersions[0].plannerCampaigns.push(result.data);

					// Force a re-render of the campaigns
					this.$nextTick(() => {
						const newCampaignEl = document.querySelector(`[data-campaign-id="${result.data.id}"]`);
						if (newCampaignEl) {
							console.log("New campaign element found:", newCampaignEl);
							newCampaignEl.classList.add('highlight-new-campaign');
							setTimeout(() => {
								newCampaignEl.classList.remove('highlight-new-campaign');
							}, 8000);
							// Scroll to the new campaign element
							newCampaignEl.scrollIntoView({ behavior: 'smooth', block: 'center' });
						} else {
							console.log("Element not found, retrying...");
							setTimeout(() => {
								const retryElement = document.querySelector(`[data-campaign-id="${result.data.id}"]`);
								if (retryElement) {
									console.log("New campaign element found on retry:", retryElement);
									retryElement.classList.add('highlight-new-campaign');
									setTimeout(() => {
										retryElement.classList.remove('highlight-new-campaign');
									}, 8000);
									retryElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
								} else {
									console.log("Element still not found after retry.");
								}
							}, 100); // Retry after 100ms
						}
					});
				}
			} catch (error) {
				console.error('Failed to add campaign:', error);
			} finally {
				this.isGeneratingCampaign = false;
			}
		},

		// Download campaigns as CSV
		downloadCampaignsCSV() {
			customerIOTrackEvent('AI Marketing Strategist - Download CSV');

			if (!this.sortedCampaigns || this.sortedCampaigns.length === 0) {
				console.warn('No campaigns to download');
				return;
			}

			// Define CSV headers to match eHouse format
			const headers = [
				'Item',
				'Due date',
				'Send Time',
				'Brief',
				'Segments',
				'Type',
				'Copy Due Date',
				'Art Due Date'
			];

			// Create CSV content
			let csvContent = headers.join(',') + '\n';

			// Add campaign data
			this.sortedCampaigns.forEach(campaign => {
				// Escape and format fields to handle commas and quotes in text
				const formatField = (field) => {
					if (field === null || field === undefined) return '';
					const stringField = String(field);
					if (stringField.includes(',') || stringField.includes('"') || stringField.includes('\n')) {
						return '"' + stringField.replace(/"/g, '""') + '"';
					}
					return stringField;
				};

				// Get the scheduled date for calculations
				const scheduledDate = new Date(campaign.scheduledDate);

				// Calculate Copy and Art Due Dates (12 and 9 days before scheduled date)
				const copyDueDate = new Date(scheduledDate);
				copyDueDate.setDate(scheduledDate.getDate() - 12);

				const artDueDate = new Date(scheduledDate);
				artDueDate.setDate(scheduledDate.getDate() - 9);

				// Format dates for CSV
				const formatDate = (date) => {
					const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
					return `${months[date.getMonth()]} ${date.getDate()}, ${date.getFullYear()}`;
				};

				// Create row with required fields
				const row = [
					formatField(campaign.name), // Item
					formatField(formatDate(scheduledDate)), // Due date
					formatField('10:00 AM'), // PS Send Time (hardcoded for now)
					'', // Brief (left blank as requested)
					formatField(campaign.targetSegment), // Segments
					formatField(campaign.type || 'Email'), // Type (defaulting to Email if not specified)
					formatField(formatDate(copyDueDate)), // Copy Due Date
					formatField(formatDate(artDueDate)) // Art Due Date
				];

				csvContent += row.join(',') + '\n';
			});

			// Create a blob and download the file
			const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
			const url = URL.createObjectURL(blob);
			const link = document.createElement('a');
			link.href = url;
			link.setAttribute('download', `${this.currentPlan.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_campaigns.csv`);
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);
		},

		async handleSave(type, campaignId) {
			if (this.isSaving || !this.editValue) {
				return;
			}

			this.isSaving = true;

			try {
				const updates = {};
				if (type === 'name') {
					updates.name = this.editValue;
				} else if (type === 'description') {
					updates.description = this.editValue;
				} else if (type === 'scheduledDate') {
					// No need to convert - use the date string directly
					// The date picker already gives us YYYY-MM-DD format
					console.log('Saving date directly:', this.editValue);

					// Ensure it's a proper YYYY-MM-DD string before saving
					if (this.editValue.match(/^\d{4}-\d{2}-\d{2}$/)) {
						updates.scheduledDate = this.editValue;
					} else {
						// Only if the format is not as expected, try to reformat
						// This should rarely happen with the date input
						console.warn('Date not in expected format:', this.editValue);
						const parts = this.editValue.split('-');
						if (parts.length === 3) {
							const [year, month, day] = parts.map(p => parseInt(p, 10));
							updates.scheduledDate = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
						} else {
							throw new Error('Invalid date format');
						}
					}
				} else if (type === 'promotionDescription') {
					updates.promotionDescription = this.editValue;
				} else if (type === 'targetSegment') {
					updates.targetSegment = this.editValue;
				} else if (type === 'type') {
					updates.type = this.editValue;
				} else {
					// For any other field type, use the type directly as the property name
					updates[type] = this.editValue;
				}

				// API call to update campaign
				const response = await fetch(`${URL_DOMAIN}/planner/campaign/${campaignId}`, {
					method: 'PATCH',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Content-Type': 'application/json',
					},
					body: JSON.stringify(updates),
				});

				const result = await response.json();

				if (result.status === 200) {
					// Update the local campaign data
					let campaigns = this.currentPlan?.plannerPlanVersions?.[0]?.plannerCampaigns;
					if (campaigns) {
						const campaignIndex = campaigns.findIndex(c => c.id === campaignId);

						if (campaignIndex !== -1) {
							// For scheduledDate, ensure we keep our properly formatted date
							if (type === 'scheduledDate') {
								// Store the original formatted date we sent to avoid any timezone conversions
								campaigns[campaignIndex].scheduledDate = updates.scheduledDate;
								console.log('Updated campaign with saved date:', updates.scheduledDate);
							} else {
								// For other fields, update with API response data
								Object.keys(updates).forEach(key => {
									if (result.data && result.data[key] !== undefined) {
										campaigns[campaignIndex][key] = result.data[key];
									} else if (updates[key] !== undefined) {
										campaigns[campaignIndex][key] = updates[key];
									}
								});
							}
							// Force a re-render
							this.$forceUpdate();
						}
					} else {
						console.error('Failed to find campaigns array in the plan data');
					}
				} else {
					console.error('Failed to update campaign:', result.message);
				}
			} catch (error) {
				console.error('Error saving campaign update:', error);
			} finally {
				this.isSaving = false;
				this.editingState = { type: null, campaignId: null }; // Reset to an object with null values instead of null
				this.editValue = '';
			}
		},

		isEditing(type, campaignId) {
			// Add null check for editingState to prevent errors
			return this.editingState && this.editingState.type === type && this.editingState.campaignId === campaignId;
		},

		async navigateToCampaignTask(campaignId) {
			try {
				// Find the campaign to get its task id
				const campaign = this.currentPlan.plannerPlanVersions[0].plannerCampaigns.find(c => c.id === campaignId);

				if (!campaign || !campaign.task) {
					console.error('Campaign or task not found:', campaignId);
					return;
				}

				// Get the plan version ID to pass as query parameter
				const planVersionId = this.currentPlan.plannerPlanVersions[0].id;

				// Navigate to the campaign task view using the task.id
				this.$router.push({
					path: `/ai-strategist/tasks/${campaign.task.id}`,
					query: { planId: planVersionId }
				});
			} catch (error) {
				console.error('Error navigating to campaign task:', error);
			}
		},

		getGenerationStatusText() {
			if (!this.currentPlan.inProgress) {
				return 'Your AI team is preparing to generate campaigns...';
			}

			const campaigns = this.currentPlan.plannerPlanVersions[0]?.plannerCampaigns || [];
			const totalTasks = campaigns.length;

			if (totalTasks === 0) {
				return 'Setting up your campaign tasks...';
			}

			const completedTasks = campaigns.filter(campaign =>
				campaign.task && campaign.task.status !== 'Processing'
			).length;

			if (completedTasks === 0) {
				return 'Starting to design your campaigns...';
			} else if (completedTasks === totalTasks) {
				return 'All campaigns generated successfully!';
			} else {
				return `Generating campaigns: ${completedTasks} of ${totalTasks} complete`;
			}
		},

		// Add this new method to determine the horizontal position for scrolling animation
		getRolePosition(role) {
			// Calculate position to center the active role
			// Each role has a width of 96px (w-24 = 6rem = 96px)
			const roleWidth = 96; // Width of one role element including margins

			switch(role) {
				case 'analyst': return -1.5 * roleWidth;
				case 'strategist': return -0.5 * roleWidth;
				case 'writer': return 0.5 * roleWidth;
				case 'designer': return 1.5 * roleWidth;
				default: return 0;
			}
		},

		toggleViewMode(mode) {
			// Toggle the view mode between 'card' and 'table'
			this.forceViewMode = mode;

			// Force a re-render to ensure view changes
			this.$nextTick(() => {
				// Reset any editing state when switching views
				this.editingState = { type: null, campaignId: null };
				this.editValue = '';
			});
		},

		// Method to poll for plan status updates
		async pollPlanStatus() {
			const planId = this.$route.params.planId;
			try {
				// Get current campaign IDs before fetching update
				const existingCampaignIds = new Set(
					this.currentPlan?.plannerPlanVersions?.[0]?.plannerCampaigns?.map(c => c.id) || []
				);

				console.log('Polling for plan status update...');
				const response = await fetch(`${URL_DOMAIN}/organizations/organization-planner-plans/${planId}`, {
					method: 'GET',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Content-Type': 'application/json'
					}
				});
				const data = await response.json();

				if (data && data[0]) {
					const updatedPlan = data[0];
					let newCampaignIds = [];

					// Process dates and find new campaigns
					if (updatedPlan.plannerPlanVersions && updatedPlan.plannerPlanVersions.length > 0) {
						const updatedCampaigns = updatedPlan.plannerPlanVersions[0].plannerCampaigns || [];
						updatedCampaigns.forEach(campaign => {
							// Date processing
							if (campaign.scheduledDate) {
								if (typeof campaign.scheduledDate === 'string' &&
									(campaign.scheduledDate.includes(' ') || campaign.scheduledDate.includes('T'))) {
									let datePart;
									if (campaign.scheduledDate.includes('T')) {
										datePart = campaign.scheduledDate.split('T')[0];
									} else if (campaign.scheduledDate.includes(' ')) {
										datePart = campaign.scheduledDate.split(' ')[0];
									}
									if (datePart && datePart.match(/^\d{4}-\d{2}-\d{2}$/)) {
										campaign.scheduledDate = datePart;
									}
								}
							}
							// Sync task status
							if (campaign.task) {
								campaign.taskStatus = campaign.task.status;
							}
							// Check if campaign is new
							if (!existingCampaignIds.has(campaign.id)) {
								newCampaignIds.push(campaign.id);
							}
						});
					}

					// Update the current plan data reactively
					this.currentPlan = updatedPlan;

					// Highlight new campaigns
					if (newCampaignIds.length > 0) {
						console.log('New campaigns found:', newCampaignIds);
						newCampaignIds.forEach(id => this.highlightedCampaigns.add(id));

						this.$nextTick(() => {
							newCampaignIds.forEach(id => {
								// Scrolling removed as per request
								// const newCampaignEl = document.querySelector(`[data-campaign-id="${id}"]`);
								// if (newCampaignEl) {
								// 	newCampaignEl.scrollIntoView({ behavior: 'smooth', block: 'center' });
								// }
								// Remove highlight after delay
								setTimeout(() => {
									this.highlightedCampaigns.delete(id);
								}, 8000); // 8 seconds, matching manual add
							});
						});
					}

					// Check if plan generation is specifically 'Complete'
					if (updatedPlan.generationStatus === 'Complete') {
						console.log('Plan generation status is Complete. Stopping polling.');
						if (this.pollingIntervalId) {
							clearInterval(this.pollingIntervalId);
							this.pollingIntervalId = null;
						}
						this.campaignsGenerating = false; // Update generating state
					}
				} else {
					console.error('Invalid data received during polling:', data);
				}
			} catch (error) {
				console.error('Error polling plan status:', error);
				// Stop polling on error
				if (this.pollingIntervalId) {
					clearInterval(this.pollingIntervalId);
					this.pollingIntervalId = null;
				}
			}
		},

		// Add new method to handle edits from child components
		handleEditField({ field, campaignId, value }) {
			const updates = {};
			updates[field] = value;

			this.saveFieldUpdate(updates, campaignId);
		},

		// Method to save field updates (extracted from handleSave)
		async saveFieldUpdate(updates, campaignId) {
			if (this.isSaving) {
				return;
			}

			this.isSaving = true;

			try {
				// API call to update campaign
				const response = await fetch(`${URL_DOMAIN}/planner/campaign/${campaignId}`, {
					method: 'PATCH',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Content-Type': 'application/json',
					},
					body: JSON.stringify(updates),
				});

				const result = await response.json();

				if (result.status === 200) {
					// Update the local campaign data
					let campaigns = this.currentPlan?.plannerPlanVersions?.[0]?.plannerCampaigns;
					if (campaigns) {
						const campaignIndex = campaigns.findIndex(c => c.id === campaignId);

						if (campaignIndex !== -1) {
							// For scheduledDate, ensure we keep our properly formatted date
							if (updates.scheduledDate) {
								// Store the original formatted date we sent to avoid any timezone conversions
								campaigns[campaignIndex].scheduledDate = updates.scheduledDate;
							} else {
								// For other fields, update with API response data
								Object.keys(updates).forEach(key => {
									if (result.data && result.data[key] !== undefined) {
										campaigns[campaignIndex][key] = result.data[key];
									} else if (updates[key] !== undefined) {
										campaigns[campaignIndex][key] = updates[key];
									}
								});
							}
							// Force a re-render
							this.$forceUpdate();
						}
					} else {
						console.error('Failed to find campaigns array in the plan data');
					}
				} else {
					console.error('Failed to update campaign:', result.message);
				}
			} catch (error) {
				console.error('Error saving campaign update:', error);
                        } finally {
                                this.isSaving = false;
                        }
                },
	},

	async mounted() {
		customerIOTrackEvent('Viewed AI Marketing Strategist Plan');
		console.log('AgentPlan mounted ****************************************************************');
		this.isLoading = true;
		this.handleClickOutside = (event) => {
			if (!event.target.closest('.relative')) {
				this.isDropdownOpen = false;
			}
		};

		const planId = this.$route.params.planId;

		try {
			let response = await fetch(`${URL_DOMAIN}/organizations/organization-planner-plans/${planId}`, {
				method: 'GET',
				withCredentials: true,
				credentials: 'omit',
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Access-Control-Allow-Origin': '*',
					'Content-Type': 'application/json'
				}
			});

			let data = await response.json();

			// Make sure we have valid data before proceeding
			if (!data || !data[0]) {
				console.error('Invalid plan data received from API:', data);
				return;
			}

			// Process the data to fix date formatting issues before assigning to currentPlan
			if (data[0] && data[0].plannerPlanVersions && data[0].plannerPlanVersions.length > 0 &&
				data[0].plannerPlanVersions[0].plannerCampaigns) {
				// Fix the date formats for each campaign
				data[0].plannerPlanVersions[0].plannerCampaigns.forEach(campaign => {
					if (campaign.scheduledDate) {
						// Handle ISO format with timezone info (like 2025-11-19 00:00:00+00)
						if (typeof campaign.scheduledDate === 'string' &&
							(campaign.scheduledDate.includes(' ') || campaign.scheduledDate.includes('T'))) {

							// Extract just the date part (YYYY-MM-DD)
							let datePart;
							if (campaign.scheduledDate.includes('T')) {
								datePart = campaign.scheduledDate.split('T')[0];
							} else if (campaign.scheduledDate.includes(' ')) {
								datePart = campaign.scheduledDate.split(' ')[0];
							}

							if (datePart && datePart.match(/^\d{4}-\d{2}-\d{2}$/)) {
								console.log(`Fixing date for campaign ${campaign.id}: ${campaign.scheduledDate} -> ${datePart}`);
								campaign.scheduledDate = datePart;
							}
						}
					}
				});
			}

			this.currentPlan = data[0];
			console.log('Plan loaded:', this.currentPlan);

			// Make sure we initialize editingState properly
			this.editingState = { type: null, campaignId: null };

			// Handle existing archived campaigns and sync task status
			if (this.currentPlan.plannerPlanVersions && this.currentPlan.plannerPlanVersions.length > 0 &&
				this.currentPlan.plannerPlanVersions[0].plannerCampaigns) {
				// Handle archived campaigns
				const archivedCampaigns = this.currentPlan.plannerPlanVersions[0].plannerCampaigns.filter(c => c.archived);
				archivedCampaigns.forEach(campaign => {
					this.archivedCampaigns.add(campaign.id);
				});

				// Sync task.status with campaign.taskStatus for all campaigns
				const campaigns = this.currentPlan.plannerPlanVersions[0].plannerCampaigns;
				campaigns.forEach(campaign => {
					if (campaign.task) {
						campaign.taskStatus = campaign.task.status;
					}
				});
			}

			// Start polling if the plan is still generating after initial load
			if (this.isPlanStillGenerating) {
				console.log('Plan is still generating, starting polling...');
				this.pollingIntervalId = setInterval(this.pollPlanStatus, 5000); // Poll every 5 seconds
			}
			this.isLoading = false;
		} catch (error) {
			console.error('Error loading plan data:', error);
			this.isLoading = false;
		}

		document.addEventListener('click', this.handleClickOutside);
	},

	beforeUnmount() {
		document.removeEventListener('click', this.handleClickOutside);
		// Clear the polling interval if it exists
		if (this.pollingIntervalId) {
			clearInterval(this.pollingIntervalId);
			this.pollingIntervalId = null;
			console.log('Polling stopped on component unmount.');
		}
	}
}
</script>

<style>
.no-focus-outline {
  box-shadow: none !important;
  outline: none !important;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Archive transition animations */
.archive-enter-active,
.archive-leave-active {
  transition: all 0.5s ease;
}

.archive-enter-from,
.archive-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

@keyframes highlightNew {
  0% {
    background-color: rgba(147, 51, 234, 0.1);
    transform: scale(1);
  }

  50% {
    background-color: rgba(147, 51, 234, 0.2);
    transform: scale(1.02);
  }

  100% {
    background-color: transparent;
    transform: scale(1);
  }
}

.highlight-new-campaign {
  animation: highlightNew 8s ease-out;
}

/* Generating Card Shine Animation */
.generating-card {
  position: relative;
}

.generating-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 200%;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(167, 139, 250, 0),
    rgba(167, 139, 250, 0.1),
    rgba(167, 139, 250, 0)
  );
  animation: shine 2s infinite linear;
  z-index: 1;
}

@keyframes shine {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Indeterminate Progress Bar Animation */
.indeterminate-progress {
  animation: indeterminate 1.5s infinite linear;
  transform-origin: 0% 50%;
}

@keyframes indeterminate {
  0% {
    transform: translateX(0) scaleX(0);
  }
  50% {
    transform: translateX(0) scaleX(0.5);
  }
  100% {
    transform: translateX(100%) scaleX(0.5);
  }
}
</style>
