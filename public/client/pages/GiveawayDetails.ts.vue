<template>
	<ProgramActive></ProgramActive>
	<div class="m-3 sm:m-10 sm:m-7">
		<div class="flex flex-col sm:flex-row items-center justify-between">

			<EditableHeader
				v-if="!isLoading"
				:header-text="this.giveaway.name"
				@updated-header="(header) => {this.giveaway.name = header; updateGiveawayDetails();}"
			/>
			<div v-if="isLoading" role="status"
				class="p-3 space-y-4 bg-gray-300 dark:bg-gray-600 w-56 rounded-2xl shadow animate-pulse mb-6 sm:mb-0"></div>
			<div class="flex-grow"></div>

			<div class="flex w-full sm:w-auto justify-start">
				<div v-if="isLoading" role="status"
					class="p-3 space-y-4 bg-gray-300 dark:bg-gray-600 w-56 rounded-2xl shadow animate-pulse"></div>

				<div v-if="!isLoading">
					<div class="py-2 justify-start items-center gap-2.5 inline-flex">
						<!-- SVG and Launch Campaign Text -->
						<div class="flex items-center pr-5">
							<svg xmlns="http://www.w3.org/2000/svg" height="24" fill="#4B5563" viewBox="0 -960 960 960"
								width="24">
								<path
									d="M284-506q14-28 29-54t33-52l-56-11-84 84 78 33Zm482-275q-70 2-149.5 41T472-636q-42 42-75 90t-49 90l114 113q42-16 90-49t90-75q65-65 104-144t41-149q0-4-1.5-8t-4.5-7q-3-3-7-4.5t-8-1.5ZM546-541q-23-23-23-56.5t23-56.5q23-23 57-23t57 23q23 23 23 56.5T660-541q-23 23-57 23t-57-23Zm-34 262 33 79 84-84-11-56q-26 18-52 32.5T512-279Zm351-534q8 110-36 214.5T688-399l20 99q4 20-2 39t-20 33L560-102q-15 15-36 11.5T495-114l-61-143-171-171-143-61q-20-8-24-29t11-36l126-126q14-14 33.5-20t39.5-2l99 20q95-95 199.5-139T819-857q8 1 16 4.5t14 9.5q6 6 9.5 14t4.5 16ZM157-321q35-35 85.5-35.5T328-322q35 35 34.5 85.5T327-151q-48 48-113.5 57T82-76q9-66 18-131.5T157-321Zm57 56q-17 17-23.5 41T180-175q25-4 49-10t41-23q12-12 13-29t-11-29q-12-12-29-11.5T214-265Z" />
							</svg>
							<span class="ml-2">Launch Giveaway</span>
						</div>
						<ToggleItem
							@toggleChange="launchGiveaway"
							:state="giveaway.launched"
							:is-disabled="isEnded"
							showLabel=true
							onLabel="Active"
							offLabel="Inactive">
						</ToggleItem>
					</div>
				</div>
			</div>

				<div class="flex w-full sm:w-auto justify-start">
					<div
						v-if="isLoading"
						role="status"
						class="p-3 space-y-4 bg-gray-300 dark:bg-gray-600 w-56 rounded-2xl shadow animate-pulse">
					</div>
				</div>
			</div>

			<div v-if="isLoading" role="status"
				class="w-full p-4 space-y-4 border bg-white border-ralprimary-light opacity-75 border-opacity-50 rounded-2xl shadow animate-pulse mt-2">
				<div class="flex items-center justify-between">
					<div class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24"></div>
					<div class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24"></div>
					<div class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24"></div>
					<div class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24"></div>
				</div>
			</div>

			<div
				v-if="!isLoading"
				class="bg-ralinfo-light rounded-2xl p-4 mt-4 text-white bg-gradient-to-br"
				:class="giveaway?.launched ? 'from-[#15803D] to-[#09632A]' : 'from-[#555] to-[#666]'"
			>
				<div class="flex items-center px-4 py-1 pb-8">
					<div class="flex flex-col w-full">
						<div class="text-base font-semibold font-['Open Sans'] leading-normal tracking-wide">{{ liveStatusTitle }}</div>
						<div class="mt-2 text-xs font-normal font-['Open Sans'] leading-normal">
							{{ liveStatusDescription }}
						</div>

					</div>
				</div>
			</div>

			<div
				v-if="!isLoading"
				class="mt-10 p-5 bg-white bg-opacity-75 rounded-2xl shadow border border-ralprimary-light border-opacity-50 hover:border-opacity-90 hover:shadow-lg">

				<div class="flex items-center px-4 py-1">
					<div class="flex flex-col w-full">
						<div class="text-slate-800 text-base font-semibold font-['Open Sans'] leading-normal tracking-wide">Schedule</div>
						<div class="mt-2 text-slate-800 text-xs font-normal font-['Open Sans'] leading-none">
							Set the start and end date of the giveaway. We've defaulted them to today and 30 days from now.
						</div>

						<div class="mt-6 py-3 flex flex-col md:flex-row gap-10">
							<div class="flex flex-col">
								<label for="startDateInput" class="text-sm font-bold">Start Date</label>
								<input
									id="startDateInput"
									class="form-input w-full"
									:class="{'bg-gray-200 text-gray-400 cursor-not-allowed': ((isStarted && this.giveaway?.launched) || isEnded) && !isGiveawayDirty}"

									type="date"
									:max="this.giveaway.endDate"
									v-model="this.giveaway.startDate"
									@change="this.isGiveawayDirty = true"
									data-cy="start-date-input"
									:disabled="((isStarted && this.giveaway?.launched) || isEnded) && !isGiveawayDirty"
									placeholder="Enter start date"/>
							</div>
							<div class="flex flex-col">
								<label for="endDateInput" class="text-sm font-bold">End Date</label>
								<input
									id="endDateInput"
									class="form-input w-full"
									type="date"
									:min="minTodayStartDate"
									v-model="this.giveaway.endDate"
									@change="this.isGiveawayDirty = true"
									:disabled="isEnded && !isGiveawayDirty"
									:class="{'bg-gray-200 text-gray-400 cursor-not-allowed': isEnded && !isGiveawayDirty}"
									data-cy="end-date-input"
									placeholder="Enter end date"/>
							</div>

							<div class="flex flex-col items-center justify-end" v-if="showDownloadButton">

								<LightSecondaryButton cta="Download Results" size="xs" icon="true" @click="downloadResults()"></LightSecondaryButton>
							</div>
						</div>
						<div class="flex mt-4 justify-end ml-auto">
							<LightSecondaryButton cta="Save" @click="updateGiveawayDetails()" :is-disabled="isEnded && !isGiveawayDirty">
							</LightSecondaryButton>
						</div>
					</div>
				</div>
			</div>

		<div class="mt-20">
			<div class="flex">
				<div>
					<div class="text-zinc-500 text-2xl sm:text-5xl font-medium font-['Inter'] leading-[65px]">Ways to Enter
					</div>
					Manage the ways customers can enter a giveaway.
				</div>
				<div class="flex-grow"></div>
				<div class="flex-shrink-0">
					<PrimaryButton cta="add" size="normal" icon="true" @click="addWayToEarn()"></PrimaryButton>
				</div>
			</div>
		</div>

		<div class="mt-10">
			<div v-if="isWTELoading" role="status"
				class="p-3 space-y-4 bg-gray-300 dark:bg-gray-600 w-full rounded-2xl shadow animate-pulse mb-6 sm:mb-0">
			</div>

			<div class="flex flex-col items-center justify-center h-22 mb-2"
				v-if="wteSummaryData.length <= 0 && isWTELoading == false">
				<svg width="540" height="30" viewBox="0 0 501 30" fill="none" xmlns="http://www.w3.org/2000/svg">
					<g opacity="0.7">
						<g filter="url(#filter0_d_605_5769)">
							<rect x="6" y="4" width="501" height="17.0606" rx="8.53032" fill="white" fill-opacity="0.75"
								shape-rendering="crispEdges" />
							<rect x="6.5" y="4.5" width="500" height="16.0606" rx="8.03032" stroke="#D6D1FD"
								shape-rendering="crispEdges" />
						</g>
						<rect x="387" y="10" width="110" height="5" rx="2.5" fill="url(#paint0_linear_605_5769)" />
						<rect x="237" y="10" width="110" height="5" rx="2.5" fill="url(#paint1_linear_605_5769)" />
						<rect x="47" y="10" width="110" height="5" rx="2.5" fill="url(#paint2_linear_605_5769)" />
					</g>
					<defs>
						<filter id="filter0_d_605_5769" x="0" y="0" width="513" height="29.0605"
							filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
							<feFlood flood-opacity="0" result="BackgroundImageFix" />
							<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
								result="hardAlpha" />
							<feOffset dy="2" />
							<feGaussianBlur stdDeviation="3" />
							<feComposite in2="hardAlpha" operator="out" />
							<feColorMatrix type="matrix"
								values="0 0 0 0 0.0509804 0 0 0 0 0.0392157 0 0 0 0 0.172549 0 0 0 0.08 0" />
							<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_605_5769" />
							<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_605_5769" result="shape" />
						</filter>
						<linearGradient id="paint0_linear_605_5769" x1="430" y1="-0.555557" x2="433.874" y2="26.1776"
							gradientUnits="userSpaceOnUse">
							<stop stop-color="#D9D9D9" />
							<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
						</linearGradient>
						<linearGradient id="paint1_linear_605_5769" x1="280" y1="-0.555557" x2="283.874" y2="26.1776"
							gradientUnits="userSpaceOnUse">
							<stop stop-color="#D9D9D9" />
							<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
						</linearGradient>
						<linearGradient id="paint2_linear_605_5769" x1="90" y1="-0.555557" x2="93.8744" y2="26.1776"
							gradientUnits="userSpaceOnUse">
							<stop stop-color="#D9D9D9" />
							<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
						</linearGradient>
					</defs>
				</svg>

				<p class="text-xl text-ralblack-primary mt-2">You haven't added a way to enter yet.</p>
				<div class="inline-flex items-center px-4 py-2 mt-2 bg-gradient-to-bl from-indigo-800 to-indigo-600 rounded-3xl cursor-pointer hover:bg-blue-600 focus:outline-none focus:ring"
					@click="addWayToEarn()">

					<svg width="22" height="23" viewBox="0 0 40 41" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path
							d="M13.3333 20.5H20M20 20.5H26.6667M20 20.5V27.1667M20 20.5V13.8333M20 35.5C11.7157 35.5 5 28.7843 5 20.5C5 12.2157 11.7157 5.5 20 5.5C28.2843 5.5 35 12.2157 35 20.5C35 28.7843 28.2843 35.5 20 35.5Z"
							stroke="white" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" />
					</svg>

					<span class="ml-2 text-white">add</span>
				</div>
			</div>
			<div class="mt-10" v-else-if="wteSummaryData.length > 0 && isWTELoading == false">
				<div
					class="overflow-x-auto bg-white bg-opacity-75 rounded-2xl shadow border border-ralprimary-light border-opacity-50 flex-grow transition-all duration-300 ease-in-out overflow-hidden">
					<WTERewardTable
						header1="Way to Enter"
						header2="Status"
						:tableData="wteSummaryData"
						:hideStatusToggle="true"
						@editClicked="handleWTESummaryEdit"
						@toggleChanged="handleWTESummaryToggle" />
				</div>
			</div>
		</div>
	</div>
	<StatusMessage :message=status.message :status=status.type @resetStatus="status.type = 'nope'"></StatusMessage>
</template>

<script>
import RaleonTable from '../components/RaleonTable.ts.vue';
import ModalBlank from '../components/ModalBlank.vue';
import CardContainer from '../components/CardContainer.ts.vue';
import SuperDropdown from '../../client/components/SuperDropdown.ts.vue';
import * as Utils from '../../client-old/utils/Utils';
import EditableHeader from '../components/EditableHeader.ts.vue'
import PrimaryButton from '../components/PrimaryButton.ts.vue';
import ProgramActive from '../components/ProgramActive.ts.vue';
import LightSecondaryButton from '../components/LightSecondaryButton.ts.vue';
import CancelButton from '../components/CancelButton.ts.vue';
import ToggleItem from '../components/ToggleItem.ts.vue';
import StatusMessage from '../components/StatusMessage.ts.vue';
import Tooltip from '../components/Tooltip.ts.vue';
import GeneratedText from '../components/GeneratedText.ts.vue';
import RaleonLoader from '../components/RaleonLoader.ts.vue'
import TextFadeEffect from '../components/TextFadeEffect.ts.vue'
import PreviewLoyaltyProgram from '../components/PreviewLoyalty.ts.vue';
import WTERewardTable from '../components/WTERewardTable.vue';
import { customerIOTrackEvent } from '../services/customerio.js';
import moment from 'moment-timezone';

import {
	getGiveaway,
	updateGiveaway,
	getVipTiers,
	getFoundationalCampaign,
	getGiveawayWtes,
	activateGiveaway,
} from '../services/giveaway.js';

const URL_DOMAIN = Utils.URL_DOMAIN;
export default {
	props: ['giveawayId'],
	data() {
		return {
			savedGiveaway: { name: 'Untitled Giveaway'},
			giveaway: { name: 'Untitled Giveaway' },
			earn: {},
			wteColumns: [
				{ name: 'Name', value: 'name' },
				{ name: 'Conditions', value: 'conditions' },
				{ name: 'Rewards', value: 'effects' }
			],
			wteData: {},
			status: { message: '', type: 'nope' },
			isLoading: true,
			wteSummaryData: [],
			perkSummaryData: [],
			isWTELoading: true,
			isPerkLoading: true,
			wteModalOpen: false,
			selectedTier: {},
			vipTiers: [],
			isDownloading: false,
			isGiveawayDirty: false
		}
	},
	components: {
		RaleonTable,
		SuperDropdown,
		ModalBlank,
		EditableHeader,
		PrimaryButton,
		ProgramActive,
		LightSecondaryButton,
		CancelButton,
		ToggleItem,
		StatusMessage,
		CardContainer,
		Tooltip,
		GeneratedText,
		RaleonLoader,
		PreviewLoyaltyProgram,
		TextFadeEffect,
		WTERewardTable,
	},
	async mounted() {
		this.isLoading = true;
		await this.loadGiveaway();
		await this.loadGiveawayWtes();
		this.isWTELoading = false;
		this.vipTiers = await getVipTiers();
		this.isLoading = false;
	},
	computed: {
		isProd() {
			return location.hostname === 'app.raleon.io';
		},
		isStarted() {
			const startDate = new Date(new Date(this.savedGiveaway.startDate).toISOString().substring(0, 10) + ' 00:00:00');
			return startDate < new Date();
		},
		isEnded() {
			const endDate = new Date(new Date(this.savedGiveaway.endDate).toISOString().substring(0, 10) + ' 23:59:59');
			return endDate < new Date();
		},
		showDownloadButton() {
			return this.savedGiveaway.launched && this.isEnded && !this.isGiveawayDirty;
		},
		today () {
			return this.toLocalDateString(new Date().toISOString());
		},
		minTodayStartDate () {

			const startDate = new Date(new Date(this.giveaway.startDate).toISOString().substring(0, 10) + ' 00:00:00');
			const startDatePassed = startDate < new Date();

			return startDatePassed ? this.today : this.giveaway.startDate;
		},
		liveStatusTitle() {
			const launched = this.savedGiveaway?.launched;

			if (this.isEnded) {
				return launched ? 'Giveaway Completed!' : 'Giveaway Was Never Completed';
			} else if (this.isStarted) {
				return launched ? 'Giveaway Active!' : 'Giveaway Disabled';
			} else {
				return launched ? 'Giveaway Scheduled!' : 'Giveaway Not Scheduled';
			}
		},
		liveStatusDescription() {
			const launched = this.savedGiveaway?.launched;

			if (this.isEnded) {
				return launched
					? 'Your giveaway has finished. You can download all the customers who participated along with their number of entries below.'
					: 'This giveaway was not launched by the end date, did not complete, and no entries were generated.\nSince the end date has passed, it can no longer be launched.';
			} else if (this.isStarted) {
				return launched
					? 'Your giveaway is currently active. Customers will see and receive entries per the Ways To Enter below.'
					: 'This giveaway is past the start date, but is currently disabled and not accessible to customers. In order to activate it, turn the launch switch above to active';
			} else {
				return launched
					? 'Your giveaway is scheduled to launch. The giveaway will automatically activate on the upcoming start date, and automatically conclude on the end date'
					: 'This giveaway is not currently scheduled to launch, and will not become accessible to customers when the start date passes. In order to schedule it, turn the launch switch above to active.';
			}
		}
	},
	methods: {
		async loadGiveaway() {
			this.savedGiveaway = await getGiveaway(this.giveawayId);
			this.savedGiveaway.startDate = this.toLocalDateString(this.savedGiveaway.startDate);
			this.savedGiveaway.endDate = this.toLocalDateString(this.savedGiveaway.endDate);
			this.giveaway = {...this.savedGiveaway};
		},
		async updateGiveawayDetails() {
			const startDate = new Date(new Date(this.giveaway.startDate).toISOString().substring(0, 10) + ' 00:00').toISOString();
			const endDate = new Date(new Date(this.giveaway.endDate).toISOString().substring(0, 10) + ' 23:59:59').toISOString();
			try {
				await updateGiveaway({
					...this.giveaway,
					startDate,
					endDate,
				});

				const today = new Date().toISOString();
				if (this.giveaway.launched && startDate <= today && endDate >= today) {
					await activateGiveaway(this.giveawayId, true);
					await this.loadGiveawayWtes();
				} else if (this.giveaway.launched && startDate >= today && endDate >= today) {
					await activateGiveaway(this.giveawayId, false, true);
					await this.loadGiveawayWtes();
				} else if (endDate <= today) {
					// this should only get called when testing, when manually overriding the date
					// await activateGiveaway(this.giveawayId, false, true);
					await this.loadGiveawayWtes();
				}

				this.status.type = 'success';
				this.status.message = 'Giveaway details updated successfully.';
				this.savedGiveaway = {...this.giveaway};
				this.isGiveawayDirty = false;
			} catch(e) {
				console.log(`error updating giveaway`, e);
				this.status.type = 'fail';
				this.status.message = 'Failed to update. Please try again.';
			}
		},
		toLocalDateString(dateString) {
			const date = new Date(dateString);
			const localYear = date.getFullYear();
			const localMonth = (date.getMonth() + 1).toString().padStart(2, '0');
			const localDay = date.getDate().toString().padStart(2, '0');
			return `${localYear}-${localMonth}-${localDay}`;
		},
		async loadGiveawayWtes() {
			const result = await getGiveawayWtes(this.giveawayId);
			this.wteSummaryData = result.summary?.wtes;
		},
		async launchGiveaway() {
			try {
				await activateGiveaway(this.giveawayId, !this.giveaway.launched);
				this.giveaway.launched = !this.giveaway.launched;
				this.savedGiveaway.launched = !this.savedGiveaway.launched;
				await this.loadGiveawayWtes();
				this.status.type = 'success';
				this.status.message = `Giveaway ${this.giveaway.launched ? 'launched' : 'deactivated' } successfully.`;
			} catch(e) {
				console.log(`error updating giveaway`, e);
				this.status.type = 'fail';
				this.status.message = `Failed to ${this.giveaway.launched ? 'launch' : 'deactivate' } giveaway. Please try again.`;
			}
		},
		handleWTESummaryEdit(clickData) {
			console.log(`edit clicked`, clickData);
			this.$router.push(`/campaign/${clickData.campaignId}/new-giveaway-wte/edit/${clickData.id}?giveawayId=${this.giveawayId}`);
		},
		async handleWTESummaryToggle(clickData) {
			let whereClause = {
				id: clickData.id
			}
			let payload = {
				active: clickData.toggle
			}
			const queryString = `where=${encodeURIComponent(JSON.stringify(whereClause))}`;
			const response = await fetch(`${URL_DOMAIN}/loyalty-campaigns/${this.campaignId}/loyalty-earns?${queryString}`, {
				method: 'PATCH',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify(payload),
			});

			if (response.ok) {
				//Show success message
				await this.fetchCampaignSummary();
				if (clickData.toggle)
					customerIOTrackEvent('WTE Is Live');
				else
					customerIOTrackEvent('WTE Is Hidden');

				this.status.type = 'success';
				this.status.message = clickData.toggle ? 'If your campaign is live, WTE is now visible to your shoppers.' : 'WTE is now hidden from your shoppers.';
			}
			else {
				//Show error message
				this.status.type = 'fail';
				this.status.message = 'Failed to update. Please try again.';
			}
		},
		async addWayToEarn() {
			customerIOTrackEvent('Started to Add Way to Enter');
			const foundationalCampaign = await getFoundationalCampaign(this.giveawayId);
			if (!foundationalCampaign) {
				this.status.message = 'You need to create a loyalty program before you can add ways to enter.';
				this.status.type = 'fail';
				return;
			}
			this.$router.push(`/campaign/${foundationalCampaign.id}/new-giveaway-wte/add?giveawayId=${this.giveawayId}`);
		},
		async downloadResults(row) {
			this.isDownloading = true;

			let response;
			try {
				response = await fetch(`${Utils.URL_DOMAIN}/giveaway/${this.giveawayId}/results`, {
					method: 'GET',
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					}
				});
				response = await response.text();
			} catch (err) {
				console.log("Error: " + JSON.stringify(err));

				this.isDownloading = false;

				return;
			}

			const blob = new Blob([response], { type: 'text/csv' });
			const url = window.URL.createObjectURL(blob);
			const a = document.createElement('a');
			a.href = url;
			a.download = `giveaway-${this.giveaway.name}-results.csv`;
			a.click();
			window.URL.revokeObjectURL(url);

			this.isDownloading = false;
		},
	},
}
</script>
