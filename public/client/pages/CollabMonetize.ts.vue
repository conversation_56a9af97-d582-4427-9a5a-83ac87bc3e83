<template>
	<div class="p-2 sm:p-7">
		<div class="flex justify-between">
			<div>
				<h1 class="text-3xl sm:text-6xl font-sans font-medium opacity-70 text-ralblack-primary">Monetize Collaborations</h1>
				<div class="ml-1 mt-3 flex mb-10">
					<span class="text-neutral-800 text-opacity-80 text-sm font-medium font-['Inter'] mr-2">
					Increase your revenue through Raleon's loyalty network by collaborating with other amazing brands.
					</span>
					<LearnMoreText text="Learn more" url="https://docs.raleon.io/docs/the-loyalty-panel"></LearnMoreText>
				<span class="text-neutral-500 text-sm font-medium font-['Inter']">&nbsp;</span>
				</div>
			</div>
			<div class="items-center flex">
				<LightSecondaryButton cta="Manage Wallet"></LightSecondaryButton>
			</div>
		</div>

		<div class="border-b border-gray-300 mb-6">
			<div class="flex space-x-8">

				<div class="whitespace-nowrap py-4 px-1 font-medium text-sm focus:outline-none hover:border-b-2 hover:text-ralprimary-light hover:border-ralprimary-light transition-all duration-300 cursor-pointer"
				@click.stop="this.overview = true; this.collabLive = false; this.collabReq = false;"
				:class="{'text-ralprimary-light border-ralprimary-light border-b-2 ': this.overview == true}"
				>
					Overview
				</div>

				<!--
				<div class="whitespace-nowrap py-4 px-1 border-b-1 font-medium text-sm focus:outline-none hover:border-b-2 hover:text-ralprimary-light hover:border-ralprimary-light transition-all duration-300 cursor-pointer"
				@click.stop="this.overview = false; this.collabLive = true; this.collabReq = false;"
				:class="{'text-ralprimary-light border-ralprimary-light border-b-2 ': this.collabLive == true}"
				>
					Live Collabs (1)
				</div>
				-->

				<div class="whitespace-nowrap py-4 px-1 border-b-1 font-medium text-sm focus:outline-none hover:border-b-2 hover:text-ralprimary-light hover:border-ralprimary-light transition-all duration-300 cursor-pointer"
				@click.stop="this.overview = false; this.collabLive = false; this.collabReq = true;"
				:class="{'text-ralprimary-light border-ralprimary-light border-b-2 ': this.collabReq == true}"
				>
					Collab Requests (3)
				</div>
			</div>
		</div>

		<div class="" v-if="this.overview">
			<div class="container mx-auto p-4">
				<!-- Metrics Cards -->
				<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
				<div class="bg-white p-4 rounded-lg shadow-md">
					<p class="text-gray-600 flex items-center mb-2">
					Collaborations Claimed
					<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2 text-gray-400" viewBox="0 0 24 24" fill="currentColor">
						<path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zM11 17h2v-2h-2v2zm0-4h2V7h-2v6z"/>
					</svg>
					</p>
					<p class="text-3xl font-bold">125</p>
					<div class="flex justify-between mt-14">
						<p class="text-gray-600">Total Impressions</p>
						<p class="text-gray-600">3,854</p>
					</div>
				</div>
				<div class="bg-white p-4 rounded-lg shadow-md">
					<p class="text-gray-600 flex items-center mb-2">
					Collab Conversions
					<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2 text-gray-400" viewBox="0 0 24 24" fill="currentColor">
						<path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zM11 17h2v-2h-2v2zm0-4h2V7h-2v6z"/>
					</svg>
					</p>
					<p class="text-3xl font-bold">109</p>
					<div class="flex justify-between mt-14">
						<p class="text-gray-600">Conversion Rate</p>
						<p class="text-gray-600">87%</p>
					</div>
				</div>
				<div class="bg-white p-4 rounded-lg shadow-md">
					<p class="text-gray-600 flex items-center mb-2">
					Points Sold
					<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2 text-gray-400" viewBox="0 0 24 24" fill="currentColor">
						<path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zM11 17h2v-2h-2v2zm0-4h2V7h-2v6z"/>
					</svg>
					</p>
					<p class="text-3xl font-bold">62,500</p>
					<div class="flex justify-between mt-14">
						<p class="text-gray-600">Total Point Cost</p>
						<p class="text-gray-600">$17,152</p>
					</div>
				</div>
				<div class="bg-gray-200 p-4 rounded-lg shadow-md">
					<p class="text-gray-600 flex justify-between mb-2">
					Total Revenue
					<LightSecondaryButton cta="Manage"></LightSecondaryButton>
					</p>
					<p class="text-3xl font-bold">$19,527</p>
					<div class="flex justify-between mt-6">
						<p class="text-gray-600">Revenue from Conv.</p>
						<p class="text-gray-600">$2,375</p>
					</div>
					<div class="flex justify-between">
						<p class="text-gray-600">Wallet Balance</p>
						<p class="text-gray-600">$19,527</p>
					</div>
				</div>
				</div>
				<button class="text-gray-600 border border-gray-300 px-3 py-1 rounded-lg">Explain metrics</button>

				<!-- Content -->
				<div class="mt-6">
					<h2 class="text-3xl sm:text-3xl font-sans font-medium opacity-70 text-ralblack-primary mb-4">Live Collaborations (1)</h2>

					<!-- card -->
					<div class="bg-white max-w-sm rounded-2xl border border-violet-300 bg-opacity-85 overflow-hidden shadow-md mb-2 px-4 py-4 hover:shadow-lg transition-all duratio-300  flex flex-col justify-between">
						<div>
						<div class="flex justify-between items-start mb-4">
							<img src="https://via.placeholder.com/50" alt="simple.ai" class="w-10 h-10">
							<button class="text-gray-400 hover:text-gray-600">
							<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-6 h-6">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
							</svg>
							</button>
						</div>
						<h3 class="text-xl font-bold">Mosi Tea</h3>
						<p class="text-gray-500">The best on-the-go tea infuser and premium tea on the planet.</p>

						<div class="mt-4">
							<p class="font-semibold">Quality</p>
							<p class="text-gray-600">👍 Similar geographic location.</p>
							<p class="text-gray-600">👍 Offer has performed well with other brands.</p>
							<p class="text-gray-600">👎 Lower product fit.</p>
						</div>

						<div class="mt-4">
							<p class="font-semibold">Summary</p>
							<p class="text-gray-600">Started on <span class="text-sm">5/01/2025</span></p>
							<p class="text-gray-600"><span class="font-bold">125</span> claims to date</p>
							<p class="text-gray-600"><span class="font-bold">$1,852</span> in revenue earned</p>
						</div>
						<div class="mt-4">
							<p class="font-semibold">Collab Offer Summary</p>
							<div class="flex">
								<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368" class="mr-2"><path d="M560-440q-50 0-85-35t-35-85q0-50 35-85t85-35q50 0 85 35t35 85q0 50-35 85t-85 35ZM280-320q-33 0-56.5-23.5T200-400v-320q0-33 23.5-56.5T280-800h560q33 0 56.5 23.5T920-720v320q0 33-23.5 56.5T840-320H280Zm80-80h400q0-33 23.5-56.5T840-480v-160q-33 0-56.5-23.5T760-720H360q0 33-23.5 56.5T280-640v160q33 0 56.5 23.5T360-400Zm440 240H120q-33 0-56.5-23.5T40-240v-440h80v440h680v80ZM280-400v-320 320Z"/></svg>
								<p class="text-gray-600">
								CPA <span class="font-bold text-green-600">$19</span> per customer</p>
							</div>
							<div class="flex">
								<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368" class="mr-2"><path d="m520-260 140-140q11-11 17.5-26t6.5-32q0-34-24-58t-58-24q-19 0-37.5 11T520-492q-30-28-47-38t-35-10q-34 0-58 24t-24 58q0 17 6.5 32t17.5 26l140 140Zm336-130L570-104q-12 12-27 18t-30 6q-15 0-30-6t-27-18L103-457q-11-11-17-25.5T80-513v-287q0-33 23.5-56.5T160-880h287q16 0 31 6.5t26 17.5l352 353q12 12 17.5 27t5.5 30q0 15-5.5 29.5T856-390ZM513-160l286-286-353-354H160v286l353 354ZM260-640q25 0 42.5-17.5T320-700q0-25-17.5-42.5T260-760q-25 0-42.5 17.5T200-700q0 25 17.5 42.5T260-640Zm220 160Z"/></svg>
								<p class="text-gray-600">
								Earn <span class="font-bold text-green-600">$3</span> for 400 points</p>
							</div>
							<div class="flex">
								<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368"><path d="m354-287 126-76 126 77-33-144 111-96-146-13-58-136-58 135-146 13 111 97-33 143Zm126 18L314-169q-11 7-23 6t-21-8q-9-7-14-17.5t-2-23.5l44-189-147-127q-10-9-12.5-20.5T140-571q4-11 12-18t22-9l194-17 75-178q5-12 15.5-18t21.5-6q11 0 21.5 6t15.5 18l75 178 194 17q14 2 22 9t12 18q4 11 1.5 22.5T809-528L662-401l44 189q3 13-2 23.5T690-171q-9 7-21 8t-23-6L480-269Zm0-201Z"/></svg>
								<p class="text-gray-600">
								$10 off first order with Mosi Tea.</p>
							</div>
						</div>
						</div>
						<div class="flex justify-end mt-4">
						<LightSecondaryButton cta="Cancel"></LightSecondaryButton>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div class="" v-if="this.collabLive">
			<div class="bg-white max-w-sm rounded-2xl border border-violet-300 bg-opacity-85 overflow-hidden shadow-md mb-2 px-4 py-4 hover:shadow-lg transition-all duratio-300  flex flex-col justify-between">
				<div>
				<div class="flex justify-between items-start mb-4">
					<img src="https://via.placeholder.com/50" alt="simple.ai" class="w-10 h-10">
					<button class="text-gray-400 hover:text-gray-600">
					<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-6 h-6">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
					</svg>
					</button>
				</div>
				<h3 class="text-xl font-bold">Mosi Tea</h3>
				<p class="text-gray-500">The best on-the-go tea infuser and premium tea on the planet.</p>
				<div class="mt-4">
					<p class="font-semibold">Summary</p>
					<p class="text-gray-600">Started on <span class="text-sm">5/01/2025</span></p>
					<p class="text-gray-600"><span class="font-bold">125</span> claims to date</p>
					<p class="text-gray-600"><span class="font-bold">$1,852</span> in revenue earned</p>
				</div>
				<div class="mt-4">
					<p class="font-semibold">Collab Offer Summary</p>
					<div class="flex">
						<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368" class="mr-2"><path d="M560-440q-50 0-85-35t-35-85q0-50 35-85t85-35q50 0 85 35t35 85q0 50-35 85t-85 35ZM280-320q-33 0-56.5-23.5T200-400v-320q0-33 23.5-56.5T280-800h560q33 0 56.5 23.5T920-720v320q0 33-23.5 56.5T840-320H280Zm80-80h400q0-33 23.5-56.5T840-480v-160q-33 0-56.5-23.5T760-720H360q0 33-23.5 56.5T280-640v160q33 0 56.5 23.5T360-400Zm440 240H120q-33 0-56.5-23.5T40-240v-440h80v440h680v80ZM280-400v-320 320Z"/></svg>
						<p class="text-gray-600">
						CPA <span class="font-bold text-green-600">$19</span> per customer</p>
					</div>
					<div class="flex">
						<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368" class="mr-2"><path d="m520-260 140-140q11-11 17.5-26t6.5-32q0-34-24-58t-58-24q-19 0-37.5 11T520-492q-30-28-47-38t-35-10q-34 0-58 24t-24 58q0 17 6.5 32t17.5 26l140 140Zm336-130L570-104q-12 12-27 18t-30 6q-15 0-30-6t-27-18L103-457q-11-11-17-25.5T80-513v-287q0-33 23.5-56.5T160-880h287q16 0 31 6.5t26 17.5l352 353q12 12 17.5 27t5.5 30q0 15-5.5 29.5T856-390ZM513-160l286-286-353-354H160v286l353 354ZM260-640q25 0 42.5-17.5T320-700q0-25-17.5-42.5T260-760q-25 0-42.5 17.5T200-700q0 25 17.5 42.5T260-640Zm220 160Z"/></svg>
						<p class="text-gray-600">
						Earn <span class="font-bold text-green-600">$3</span> for 400 points</p>
					</div>
					<div class="flex">
						<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368"><path d="m354-287 126-76 126 77-33-144 111-96-146-13-58-136-58 135-146 13 111 97-33 143Zm126 18L314-169q-11 7-23 6t-21-8q-9-7-14-17.5t-2-23.5l44-189-147-127q-10-9-12.5-20.5T140-571q4-11 12-18t22-9l194-17 75-178q5-12 15.5-18t21.5-6q11 0 21.5 6t15.5 18l75 178 194 17q14 2 22 9t12 18q4 11 1.5 22.5T809-528L662-401l44 189q3 13-2 23.5T690-171q-9 7-21 8t-23-6L480-269Zm0-201Z"/></svg>
						<p class="text-gray-600">
						$10 off first order with Mosi Tea.</p>
					</div>
				</div>
				</div>
				<div class="flex justify-end mt-4">
				<LightSecondaryButton cta="Cancel"></LightSecondaryButton>
				</div>
			</div>
		</div>
		<div class="" v-if="this.collabReq">
			<div class="flex items-center space-x-4 mb-4">
			<input type="text" placeholder="Search offers" class="w-1/3 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none">
			<select class="w-1/3 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none">
				<option>Select Categories</option>
			</select>
			<select class="w-1/3 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none">
				<option>Select Countries</option>
			</select>
			<button class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none w-40">Clear filters</button>
			</div>
			<div class="flex justify-end mb-4">

			</div>
			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
			<!-- Card 1 -->
			<div class=" bg-white rounded-2xl border border-violet-300 bg-opacity-85 overflow-hidden shadow-md mb-2 px-4 py-4 hover:shadow-lg transition-all duratio-300  flex flex-col justify-between">
				<div>
				<div class="flex justify-between items-start mb-4">
					<img src="https://via.placeholder.com/50" alt="simple.ai" class="w-10 h-10">
					<button class="text-gray-400 hover:text-gray-600">
					<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-6 h-6">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
					</svg>
					</button>
				</div>
				<h3 class="text-xl font-bold">Mosi Tea</h3>
				<p class="text-gray-500">The best on-the-go tea infuser and premium tea on the planet.</p>
				<div class="mt-4">
					<p class="font-semibold">Quality</p>
					<p class="text-gray-600">👍 Similar geographic location.</p>
					<p class="text-gray-600">👍 Offer has performed well with other brands.</p>
					<p class="text-gray-600">👎 Lower product fit.</p>
				</div>
				<div class="mt-4">
					<p class="font-semibold">Collab Offer Summary</p>
					<div class="flex">
						<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368" class="mr-2"><path d="M560-440q-50 0-85-35t-35-85q0-50 35-85t85-35q50 0 85 35t35 85q0 50-35 85t-85 35ZM280-320q-33 0-56.5-23.5T200-400v-320q0-33 23.5-56.5T280-800h560q33 0 56.5 23.5T920-720v320q0 33-23.5 56.5T840-320H280Zm80-80h400q0-33 23.5-56.5T840-480v-160q-33 0-56.5-23.5T760-720H360q0 33-23.5 56.5T280-640v160q33 0 56.5 23.5T360-400Zm440 240H120q-33 0-56.5-23.5T40-240v-440h80v440h680v80ZM280-400v-320 320Z"/></svg>
						<p class="text-gray-600">
						CPA <span class="font-bold text-green-600">$19</span> per customer</p>
					</div>
					<div class="flex">
						<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368" class="mr-2"><path d="m520-260 140-140q11-11 17.5-26t6.5-32q0-34-24-58t-58-24q-19 0-37.5 11T520-492q-30-28-47-38t-35-10q-34 0-58 24t-24 58q0 17 6.5 32t17.5 26l140 140Zm336-130L570-104q-12 12-27 18t-30 6q-15 0-30-6t-27-18L103-457q-11-11-17-25.5T80-513v-287q0-33 23.5-56.5T160-880h287q16 0 31 6.5t26 17.5l352 353q12 12 17.5 27t5.5 30q0 15-5.5 29.5T856-390ZM513-160l286-286-353-354H160v286l353 354ZM260-640q25 0 42.5-17.5T320-700q0-25-17.5-42.5T260-760q-25 0-42.5 17.5T200-700q0 25 17.5 42.5T260-640Zm220 160Z"/></svg>
						<p class="text-gray-600">
						Earn <span class="font-bold text-green-600">$3</span> for 400 points</p>
					</div>
					<div class="flex">
						<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368"><path d="m354-287 126-76 126 77-33-144 111-96-146-13-58-136-58 135-146 13 111 97-33 143Zm126 18L314-169q-11 7-23 6t-21-8q-9-7-14-17.5t-2-23.5l44-189-147-127q-10-9-12.5-20.5T140-571q4-11 12-18t22-9l194-17 75-178q5-12 15.5-18t21.5-6q11 0 21.5 6t15.5 18l75 178 194 17q14 2 22 9t12 18q4 11 1.5 22.5T809-528L662-401l44 189q3 13-2 23.5T690-171q-9 7-21 8t-23-6L480-269Zm0-201Z"/></svg>
						<p class="text-gray-600">
						$10 off first order with Mosi Tea.</p>
					</div>
				</div>
				</div>
				<div class="flex justify-end mt-4">
				<PrimaryButton cta="Accept" size="xs"></PrimaryButton>
				</div>
			</div>
			<!-- Card 2 -->
			<div class=" bg-white rounded-2xl border border-violet-300 bg-opacity-85 overflow-hidden shadow-md mb-2 px-4 py-4 hover:shadow-lg transition-all duratio-300  flex flex-col justify-between">
				<div>
				<div class="flex justify-between items-start mb-4">
					<img src="https://via.placeholder.com/50" alt="simple.ai" class="w-10 h-10">
					<button class="text-gray-400 hover:text-gray-600">
					<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-6 h-6">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
					</svg>
					</button>
				</div>
				<h3 class="text-xl font-bold">Protein Gummies</h3>
				<p class="text-gray-500">Gummies that meet you your after-workout protein needs.</p>
				<div class="mt-4">
					<p class="font-semibold">Quality</p>
					<p class="text-gray-600">👍 Similar geographic location.</p>
					<p class="text-gray-600">👎 Lower than average loyalty.</p>
					<p class="text-gray-600">👎 Lower product fit.</p>
				</div>
				<div class="mt-4">
					<p class="font-semibold">Collab Offer Summary</p>
					<div class="flex">
						<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368" class="mr-2"><path d="M560-440q-50 0-85-35t-35-85q0-50 35-85t85-35q50 0 85 35t35 85q0 50-35 85t-85 35ZM280-320q-33 0-56.5-23.5T200-400v-320q0-33 23.5-56.5T280-800h560q33 0 56.5 23.5T920-720v320q0 33-23.5 56.5T840-320H280Zm80-80h400q0-33 23.5-56.5T840-480v-160q-33 0-56.5-23.5T760-720H360q0 33-23.5 56.5T280-640v160q33 0 56.5 23.5T360-400Zm440 240H120q-33 0-56.5-23.5T40-240v-440h80v440h680v80ZM280-400v-320 320Z"/></svg>
						<p class="text-gray-600">
						CPA <span class="font-bold text-green-600">$22</span> per customer</p>
					</div>
					<div class="flex">
						<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368" class="mr-2"><path d="m520-260 140-140q11-11 17.5-26t6.5-32q0-34-24-58t-58-24q-19 0-37.5 11T520-492q-30-28-47-38t-35-10q-34 0-58 24t-24 58q0 17 6.5 32t17.5 26l140 140Zm336-130L570-104q-12 12-27 18t-30 6q-15 0-30-6t-27-18L103-457q-11-11-17-25.5T80-513v-287q0-33 23.5-56.5T160-880h287q16 0 31 6.5t26 17.5l352 353q12 12 17.5 27t5.5 30q0 15-5.5 29.5T856-390ZM513-160l286-286-353-354H160v286l353 354ZM260-640q25 0 42.5-17.5T320-700q0-25-17.5-42.5T260-760q-25 0-42.5 17.5T200-700q0 25 17.5 42.5T260-640Zm220 160Z"/></svg>
						<p class="text-gray-600">
						Earn <span class="font-bold text-green-600">$5</span> for 600 points</p>
					</div>
					<div class="flex">
						<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368"><path d="m354-287 126-76 126 77-33-144 111-96-146-13-58-136-58 135-146 13 111 97-33 143Zm126 18L314-169q-11 7-23 6t-21-8q-9-7-14-17.5t-2-23.5l44-189-147-127q-10-9-12.5-20.5T140-571q4-11 12-18t22-9l194-17 75-178q5-12 15.5-18t21.5-6q11 0 21.5 6t15.5 18l75 178 194 17q14 2 22 9t12 18q4 11 1.5 22.5T809-528L662-401l44 189q3 13-2 23.5T690-171q-9 7-21 8t-23-6L480-269Zm0-201Z"/></svg>
						<p class="text-gray-600">
						$10 off first order and a free gummy hat.</p>
					</div>
				</div>
				</div>
				<div class="flex justify-end mt-4">
				<PrimaryButton cta="Accept" size="xs"></PrimaryButton>
				</div>
			</div>
			<!-- Card 3 -->
			<div class=" bg-white rounded-2xl border border-violet-300 bg-opacity-85 overflow-hidden shadow-md mb-2 px-4 py-4 hover:shadow-lg transition-all duratio-300  flex flex-col justify-between">
				<div>
				<div class="flex justify-between items-start mb-4">
					<img src="https://via.placeholder.com/50" alt="simple.ai" class="w-10 h-10">
					<button class="text-gray-400 hover:text-gray-600">
					<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-6 h-6">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
					</svg>
					</button>
				</div>
				<h3 class="text-xl font-bold">Haven</h3>
				<p class="text-gray-500">Premium gym bags for athletes and regular gym goers.</p>
				<div class="mt-4">
					<p class="font-semibold">Quality</p>
					<p class="text-gray-600">👍 Very high loyalty.</p>
					<p class="text-gray-600">👍 Similar geographic location.</p>
					<p class="text-gray-600">👎 Newer offers.</p>
				</div>
				<div class="mt-4">
					<p class="font-semibold">Collab Offer Summary</p>
					<div class="flex">
						<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368" class="mr-2"><path d="M560-440q-50 0-85-35t-35-85q0-50 35-85t85-35q50 0 85 35t35 85q0 50-35 85t-85 35ZM280-320q-33 0-56.5-23.5T200-400v-320q0-33 23.5-56.5T280-800h560q33 0 56.5 23.5T920-720v320q0 33-23.5 56.5T840-320H280Zm80-80h400q0-33 23.5-56.5T840-480v-160q-33 0-56.5-23.5T760-720H360q0 33-23.5 56.5T280-640v160q33 0 56.5 23.5T360-400Zm440 240H120q-33 0-56.5-23.5T40-240v-440h80v440h680v80ZM280-400v-320 320Z"/></svg>
						<p class="text-gray-600">
						CPA <span class="font-bold text-green-600">$30</span> per customer</p>
					</div>
					<div class="flex">
						<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368" class="mr-2"><path d="m520-260 140-140q11-11 17.5-26t6.5-32q0-34-24-58t-58-24q-19 0-37.5 11T520-492q-30-28-47-38t-35-10q-34 0-58 24t-24 58q0 17 6.5 32t17.5 26l140 140Zm336-130L570-104q-12 12-27 18t-30 6q-15 0-30-6t-27-18L103-457q-11-11-17-25.5T80-513v-287q0-33 23.5-56.5T160-880h287q16 0 31 6.5t26 17.5l352 353q12 12 17.5 27t5.5 30q0 15-5.5 29.5T856-390ZM513-160l286-286-353-354H160v286l353 354ZM260-640q25 0 42.5-17.5T320-700q0-25-17.5-42.5T260-760q-25 0-42.5 17.5T200-700q0 25 17.5 42.5T260-640Zm220 160Z"/></svg>
						<p class="text-gray-600">
						Earn <span class="font-bold text-green-600">$10</span> for 1,000 points</p>
					</div>
					<div class="flex">
						<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368"><path d="m354-287 126-76 126 77-33-144 111-96-146-13-58-136-58 135-146 13 111 97-33 143Zm126 18L314-169q-11 7-23 6t-21-8q-9-7-14-17.5t-2-23.5l44-189-147-127q-10-9-12.5-20.5T140-571q4-11 12-18t22-9l194-17 75-178q5-12 15.5-18t21.5-6q11 0 21.5 6t15.5 18l75 178 194 17q14 2 22 9t12 18q4 11 1.5 22.5T809-528L662-401l44 189q3 13-2 23.5T690-171q-9 7-21 8t-23-6L480-269Zm0-201Z"/></svg>
						<p class="text-gray-600">
						Personalize your gym bag.</p>
					</div>
				</div>
				</div>
				<div class="flex justify-end mt-4">
				<PrimaryButton cta="Accept" size="xs"></PrimaryButton>
				</div>
			</div>
			<!-- end card -->
			</div>
		</div>


	</div>

</template>

<script>

import * as Utils from '../../client-old/utils/Utils';
import CardContainer from '../components/CardContainer.ts.vue';
import LightSecondaryButton from '../components/LightSecondaryButton.ts.vue'
import PrimaryButton from '../components/PrimaryButton.ts.vue';
import ToggleItem from '../components/ToggleItem.ts.vue';
import StatusMessage from '../components/StatusMessage.ts.vue';
import LearnMoreText from '../components/LearnMoreText.ts.vue';
import { Crisp } from "crisp-sdk-web";
import { useRoute } from 'vue-router';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	components: {
		CardContainer,
		LightSecondaryButton,
		StatusMessage,
		ToggleItem,
		PrimaryButton,
		LearnMoreText
	},
	async mounted() {

	},
	data() {
		return {
			collabReq: false,
			collabLive: false,
			overview: true,
		}
	},
	methods: {
		openDocs(integration) {
			window.open(integration.docURL, '_blank');
		},
		openChat() {
			Crisp.chat.open();
		},
	},
}
</script>
