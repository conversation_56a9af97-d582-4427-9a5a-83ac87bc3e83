<template>
	<div class="p-2 sm:p-7 xl:mr-24">
		<div class="flex">
			<div class="text-neutral-800 text-opacity-70 sm:text-4xl md:text-6xl lg:text-7xl font-normal font-['Open Sans']">{{
				dashboard.title }}</div>
		</div>

		<div class="inline-flex mt-4 items-center cursor-pointer" @click="this.$router.push('/loyalty/analytics');">
			<svg width="24" height="24" viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg"
				class="hover:text-ralprimary-dark transition-color duration-300">
				<path
					d="M15.3333 12L10.3333 17M10.3333 17L15.3333 22M10.3333 17H23.6667M32 17C32 8.71573 25.2843 2 17 2C8.71573 2 2 8.71573 2 17C2 25.2843 8.71573 32 17 32C25.2843 32 32 25.2843 32 17Z"
					stroke="#202020" stroke-opacity="0.8" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" />
			</svg>
			<span class="ml-3 text-lg transition-color duration-300">Analytics</span>
		</div>

		<div v-if="hasNoData" class="flex flex-col items-center justify-center h-22 mb-2 mt-10">
				<svg xmlns="http://www.w3.org/2000/svg" height="52" viewBox="0 -960 960 960" width="52" fill="#5A16C9"><path d="M280-280h80v-280h-80v280Zm160 0h80v-400h-80v400Zm160 0h80v-160h-80v160ZM200-120q-33 0-56.5-23.5T120-200v-560q0-33 23.5-56.5T200-840h560q33 0 56.5 23.5T840-760v560q0 33-23.5 56.5T760-120H200Zm0-80h560v-560H200v560Zm0-560v560-560Z"/></svg>

				<p class="text-xl text-ralblack-primary mt-2">Your loyalty performance insights will show up within 24 hours.</p>
		</div>

		<div v-if="!hasNoData" class="flex flex-wrap mt-12">
			<div class="w-[100%] lg:pr-4 pb-4" v-for="chart of dashboard.charts" :class="{'lg:w-[50%]': !chart.wide}">
				<div v-if="isLoading"
					class="flex justify-center w-[100%] bg-white rounded-2xl shadow border border-violet-200 p-6 pl-2 pb-2">

					<div role="status" class="max-w-sm w-[100%] p-4 animate-pulse md:p-6 dark:border-gray-700">
						<div class="h-2.5 bg-gray-200 rounded-full dark:bg-gray-700 w-32 mb-2.5"></div>
						<div class="w-48 h-2 mb-10 bg-gray-200 rounded-full dark:bg-gray-700"></div>
						<div class="flex items-baseline mt-4">
							<div class="w-full bg-gray-200 rounded-t-lg h-72 dark:bg-gray-700"></div>
							<div class="w-full h-56 ms-6 bg-gray-200 rounded-t-lg dark:bg-gray-700"></div>
							<div class="w-full bg-gray-200 rounded-t-lg h-72 ms-6 dark:bg-gray-700"></div>
							<div class="w-full h-64 ms-6 bg-gray-200 rounded-t-lg dark:bg-gray-700"></div>
							<div class="w-full bg-gray-200 rounded-t-lg h-80 ms-6 dark:bg-gray-700"></div>
							<div class="w-full bg-gray-200 rounded-t-lg h-72 ms-6 dark:bg-gray-700"></div>
							<div class="w-full bg-gray-200 rounded-t-lg h-80 ms-6 dark:bg-gray-700"></div>
						</div>
						<span class="sr-only">Loading...</span>
					</div>

				</div>
				<div v-if="!isLoading" class="w-[100%] bg-white rounded-2xl shadow border border-violet-200 p-6 pl-2 pb-2">
					<div class="ml-4 flex">
						<div class="flex-col">
							<div class="text-zinc-400 text-lg font-normal font-['Inter']">{{ chart.title }}</div>
							<div class="flex items-center" v-if="chart.titleValue">
								<div class="text-slate-800 text-xl font-bold font-['Inter']">{{
									getChartData(chart)?.[chart.titleValue.key] }}</div>
								<div v-if="chart.titleValue.subtitle"
									class="ml-2 text-zinc-400 text-sm font-normal font-['Inter']">{{
										chart.titleValue.subtitle }}</div>
							</div>
						</div>
						<div class="flex-grow"></div>

						<div v-if="chart.topRight.type === 'legend'">
							<div class="flex-col">
								<div v-for="series of chart.series" class="flex items-center align-center">
									<div class="w-2.5 h-2.5 rounded-lg mr-2" :style="{background: series.color}"></div>
									<div class="text-gray-500 text-sm font-normal font-['Inter']">{{ series.name }}</div>
								</div>
							</div>
						</div>
						<div v-if="chart.topRight.type === 'trend'">
							<div class="flex flex-col items-end">
								<div class="flex">
									<div class="text-slate-800 text-base font-bold font-['Inter'] mr-2"> {{
										((getChartData(chart)?.[chart.topRight.valueKey] || 0) * 100).toFixed(1) }} %</div>
									<div class="w-6 h-5 relative">
										<div v-if="getChartData(chart)?.[chart.topRight.valueKey] > 0"
											class="w-6 h-5 left-0 top-0 absolute bg-green-300 rounded-full">
											<svg width="26" height="22" viewBox="0 0 26 22" fill="none"
												xmlns="http://www.w3.org/2000/svg">
												<ellipse cx="12.8253" cy="11.1217" rx="12.5844" ry="10.8781"
													fill="#9DD098" />
												<path
													d="M15.6491 9.96284L15.3114 10.2547C13.5045 11.8167 11.6974 13.3787 9.8901 14.941C9.5245 15.257 9.09252 15.3262 8.66107 15.1536C8.24542 14.9847 8.01732 14.6755 8.0779 14.3016C8.13233 14.0315 8.27976 13.7819 8.50092 13.5853C10.2483 12.0475 12.0184 10.5293 13.7816 9.00519L14.1156 8.71648C14.0078 8.70056 13.8991 8.68961 13.79 8.6837C13.0831 8.68096 12.3756 8.68688 11.6691 8.67914C11.1097 8.67368 10.7235 8.3408 10.7161 7.87814C10.7088 7.37359 11.0591 7.03525 11.6444 7.03206C13.2704 7.02295 14.8956 7.02189 16.5199 7.02887C17.1768 7.03206 17.5467 7.33898 17.5588 7.90046C17.5886 9.2942 17.6064 10.6881 17.612 12.0821C17.6141 12.5931 17.2295 12.8936 16.6421 12.8895C16.0879 12.885 15.7234 12.5771 15.7065 12.0808C15.6886 11.4915 15.687 10.9018 15.677 10.3121C15.6749 10.2192 15.6612 10.1281 15.6491 9.96284Z"
													fill="black" />
											</svg>
										</div>
										<div v-if="!(getChartData(chart)?.[chart.topRight.valueKey] > 0)"
											class="w-6 h-5 left-0 top-0 absolute bg-red-300 rounded-full">
											<svg width="26" height="22" viewBox="0 0 26 22" fill="none"
												xmlns="http://www.w3.org/2000/svg" style="transform: rotate(180deg);">
												<ellipse cx="12.8253" cy="11.1217" rx="12.5844" ry="10.8781" fill="" />
												<path
													d="M15.6491 9.96284L15.3114 10.2547C13.5045 11.8167 11.6974 13.3787 9.8901 14.941C9.5245 15.257 9.09252 15.3262 8.66107 15.1536C8.24542 14.9847 8.01732 14.6755 8.0779 14.3016C8.13233 14.0315 8.27976 13.7819 8.50092 13.5853C10.2483 12.0475 12.0184 10.5293 13.7816 9.00519L14.1156 8.71648C14.0078 8.70056 13.8991 8.68961 13.79 8.6837C13.0831 8.68096 12.3756 8.68688 11.6691 8.67914C11.1097 8.67368 10.7235 8.3408 10.7161 7.87814C10.7088 7.37359 11.0591 7.03525 11.6444 7.03206C13.2704 7.02295 14.8956 7.02189 16.5199 7.02887C17.1768 7.03206 17.5467 7.33898 17.5588 7.90046C17.5886 9.2942 17.6064 10.6881 17.612 12.0821C17.6141 12.5931 17.2295 12.8936 16.6421 12.8895C16.0879 12.885 15.7234 12.5771 15.7065 12.0808C15.6886 11.4915 15.687 10.9018 15.677 10.3121C15.6749 10.2192 15.6612 10.1281 15.6491 9.96284Z"
													fill="black" />
											</svg>
										</div>
									</div>
								</div>
								<div
									class="text-right text-gray-500 text-sm font-normal font-['Inter'] uppercase tracking-wide">
									{{ chart.topRight.comparison }}</div>
							</div>
						</div>
					</div>

					<div class="chart-container" :id="`dashboard-chart-${chart.key}`">

					</div>

					<div
						style="position: relative; left: 100%; margin-left: -20em; margin-top: -1em; height: 1em; width: 20em; background: white">
					</div>
				</div>
			</div>
		</div>

		<div class="mt-6" v-for="table in dashboard.tables">
			<div class="text-neutral-800 text-opacity-70 text-5xl font-normal font-['Open Sans'] mt-6" v-if="table">{{
				table.title }}</div>
			<RaleonTable class="mt-6" :column-headers="table.headers" :row-data="getTableData(table)?.rowData"
				v-if="table" />
		</div>

	</div>
</template>

<script>

import * as Utils from '../../client-old/utils/Utils';
import RaleonTable from '../components/RaleonTable.ts.vue';
import Highcharts from 'highcharts';
import { getMetric } from '../services/metrics.js';
import { customerIOTrackEvent } from '../services/customerio.js';


const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	components: {
		RaleonTable
	},
	async mounted() {
		await this.fetchData();
		customerIOTrackEvent('Viewed Dashboard ' + this.dashboard.title);

		if (this.dashboard.key === 'segmentation') {
			this.markOnboardingStepComplete();
		}

		this.dashboard.charts.forEach(chart => {
			switch (chart.type) {
				case 'highcharts-line':
					return this.highchartLine(chart);
				case 'highcharts-bar':
					return this.highchartBar(chart);
				case 'highcharts-multiline':
					return this.highchartMultiline(chart);
				case 'highcharts-column':
					return this.highchartColumn(chart);
				case 'highcharts-pie':
					return this.highchartPie(chart);
			}
		});
	},
	data() {
		return {
			isLoading: true,
			hasNoData: false,
			isHovering: false,
			dashboard: dashboards.find(d => d.key === this.$route.params.dashboardId),
			data: {
				charts: [
					{
						key: 'roi',
						//value: '33%',
						trend: .013,
						// x: {
						// 	min: Date.UTC(2023, 1, 0),
						// 	max: Date.UTC(2023, 7, 0),
						// },
						y: {
							// min: 0,
							max: 4000,
							// tickInterval: 1000,
						},
						series: [{
							key: 'data',
							data: [
								[Date.UTC(2023, 1, 0), 4000],
								[Date.UTC(2023, 2, 0), 500],
								[Date.UTC(2023, 3, 0), 900],
								[Date.UTC(2023, 4, 0), 600],
								[Date.UTC(2023, 5, 0), 2000],
								[Date.UTC(2023, 6, 0), 900],
								[Date.UTC(2023, 7, 0), 3000],
							]
						}]
					},
					{
						key: 'active_members',
						value: '9,845',
						series: [{
							key: 'new-members',
							data: [
								[Date.UTC(2023, 1, 0), 6000],
								[Date.UTC(2023, 2, 0), 900],
								[Date.UTC(2023, 3, 0), 6000],
								[Date.UTC(2023, 4, 0), 800],
								[Date.UTC(2023, 5, 0), 900],
								[Date.UTC(2023, 6, 0), 8000],
							]
						}, {
							key: 'active-members',
							data: [
								[Date.UTC(2023, 1, 0), 6000],
								[Date.UTC(2023, 2, 0), 700],
								[Date.UTC(2023, 3, 0), 7000],
								[Date.UTC(2023, 4, 0), 500],
								[Date.UTC(2023, 5, 0), 8000],
								[Date.UTC(2023, 6, 0), 900],
							]
						}]
					},
					{
						key: 'revenue-by-segment',
						value: '$3,971,424',
						x: {
							values: ['Q1', 'Q2', 'Q3', 'Q4']
						},
						series: [{
							key: 'masculine',
							data: [
								['Q1', 925321],
								['Q2', 1250000],
								['Q3', 891568],
								['Q4', 904535]
							]
						}, {
							key: 'feminine',
							data: [
								['Q1', 602543],
								['Q2', 785652],
								['Q3', 588456],
								['Q4', 652523]
							]
						}]
					},
					{
						key: 'engagement-by-segment',
						value: '85%',
						x: {
							values: ['Q1', 'Q2', 'Q3', 'Q4']
						},
						series: [{
							key: 'masculine',
							data: [
								['Q1', 88],
								['Q2', 65],
								['Q3', 68],
								['Q4', 85]
							]
						}, {
							key: 'feminine',
							data: [
								['Q1', 72],
								['Q2', 50],
								['Q3', 56],
								['Q4', 70]
							]
						}]
					},
					{
						key: 'repeat-probability',
						value: '55%',
						x: {
							values: ['2nd', '3rd', '4th', '5th', '6th', '7th', '8th', '9th', '10+']
						},
						series: [{
							key: 'member',
							data: [
								['2nd', 60],
								['3rd', 50],
								['4th', 40],
								['5th', 30],
								['6th', 25],
								['7th', 20],
								['8th', 15],
								['9th', 10],
								['10+', 7],
							]
						}, {
							key: 'non-member',
							data: [
								['2nd', 23],
								['3rd', 7],
								['4th', 4],
								['5th', 3],
								['6th', 2],
								['7th', 1],
								['8th', 1],
								['9th', 1],
								['10+', 1],
							]
						}]
					},
					{
						key: 'average-purchase-count',
						value: '5',
						series: [{
							key: 'new-members',
							data: [
								[Date.UTC(2023, 1, 0), 6],
								[Date.UTC(2023, 2, 0), 0.9],
								[Date.UTC(2023, 3, 0), 6],
								[Date.UTC(2023, 4, 0), 0.8],
								[Date.UTC(2023, 5, 0), 0.9],
								[Date.UTC(2023, 6, 0), 8],
							]
						}, {
							key: 'active-members',
							data: [
								[Date.UTC(2023, 1, 0), 6],
								[Date.UTC(2023, 2, 0), 0.7],
								[Date.UTC(2023, 3, 0), 7],
								[Date.UTC(2023, 4, 0), 0.5],
								[Date.UTC(2023, 5, 0), 8],
								[Date.UTC(2023, 6, 0), 0.9],
							]
						}]
					}
				],
				tables: [{
					key: 'rewards',
					rowData: []
				}]
			}
		}
	},
	methods: {
		async markOnboardingStepComplete() {
			let url = `/onboarding-tasks/Member Insights/states`;
			const response = await fetch(`${URL_DOMAIN}${url}`, {
				method: 'PATCH',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify({
					state: 'Verified'
				})
			});
		},
		async fetchData() {
			try {
				const metrics = {
					'loyalty_revenue_daily': ['member_revenue'],
					'active_members': ['active_members', 'new_members'],
					'roi': ['roi'],
					'loyalty_points_balance': ['totalpoints'],
				};

				const metricPromises = [];

				for (const metric in metrics) {
					console.log("Fetching data for metric:", metric);
					const today = this.getCurrentDateFormatted();
					const thirtyDaysAgo = this.getDate30DaysAgoFormatted();

					const promise = getMetric(metric, thirtyDaysAgo, today, 'day', 'trend')
						.then(data => {
							console.log(metric + " data:", data);

							let seriesData = {};

							data.body.data.forEach(item => {
								const [year, month, day] = item.group_label.split("-").map(Number);
								const utcDate = Date.UTC(year, month - 1, day);

								metrics[metric].forEach(key => {
									const value = item.metrics[key]?.value || 0;

									if (!seriesData[key]) {
										seriesData[key] = [];
									}

									seriesData[key].push([utcDate, Math.round(value * 100) / 100]);
								});
							});

							let series = Object.keys(seriesData).map(key => ({
								key: key,
								data: seriesData[key]
							}));

							let trend = 0;
							metrics[metric].forEach(key => {
								if (seriesData[key] && seriesData[key].length >= 2) {
									const lastValue = seriesData[key][seriesData[key].length - 1][1];
									const secondLastValue = seriesData[key][seriesData[key].length - 2][1];
									const keyTrend = ((lastValue - secondLastValue) / secondLastValue);
									console.log("lastValue:", lastValue, "secondLastValue:", secondLastValue, "keyTrend:", keyTrend);
									trend = keyTrend;
								}
							});

							return {
								key: metric,
								value: seriesData[metrics[metric][0]] ? seriesData[metrics[metric][0]][seriesData[metrics[metric][0]].length - 1][1] : 0,
								trend: Math.round(trend * 100) / 100,
								series: series
							};
						});

					metricPromises.push(promise);
				}
				console.log("getting chart data");

				const chartsData = await Promise.all(metricPromises);
				console.log("chartsData:", chartsData);
				this.data.charts = chartsData;
				this.isLoading = false;

			} catch (error) {
				console.error('Error fetching data:', error);
				this.hasNoData = true;
				this.isLoading = false;
			}
		},
		getChartData(chartConfig) {
			return this.data.charts.find(x => x.key === chartConfig.key);
		},
		getTableData(tableConfig) {
			return this.data.tables.find(x => x.key === tableConfig.key);
		},
		highchartLine(chart) {
			const chartData = this.getChartData(chart);

			Highcharts.chart(`dashboard-chart-${chart.key}`, {
				chart: {
					zoomType: 'x'
				},
				title: {
					text: undefined,
					align: 'left'
				},
				subtitle: {
					text: undefined,
					align: 'left'
				},
				xAxis: {
					type: 'datetime',
					min: chartData.x?.min,
					max: chartData.x?.max,
					gridLineWidth: 1,
					labels: {
						formatter: function () {
							return Highcharts.dateFormat('%m-%d', this.value);
						}
					}
				},
				yAxis: {
					title: {
						text: undefined
					},
					min: chartData.y?.min,
					max: chartData.y?.max,
					labels: {
						formatter: function () {
							return this.value;
						}
					},
					gridLineDashStyle: 'Dash',
					gridLineWidth: 2,
					tickInterval: chartData.y?.tickInterval
				},
				legend: {
					enabled: false
				},
				plotOptions: {
					area: {
						color: '#9254F6',
						fillColor: {
							linearGradient: {
								x1: 0,
								y1: 0,
								x2: 0,
								y2: 1
							},
							stops: [
								[0, '#D3DDFFFF'],
								[1, '#C6D2FD00'],
							]
						},
						marker: {
							radius: 0
						},
						lineWidth: 3,
						states: {
							hover: {
								lineWidth: 3,
								marker: {
									radius: 6
								}
							}
						},
						threshold: null
					}
				},

				series: chart.series.map(series => {
					const foundSeries = chartData.series.find(x => x.key === series.key);
					const seriesData = foundSeries ? foundSeries.data : [];
					return {
						color: series.color,
						name: series.name,
						borderRadius: 20,
						data: seriesData
					};
				})

			});
		},
		highchartBar(chart) {
			const chartData = this.getChartData(chart);

			Highcharts.chart(`dashboard-chart-${chart.key}`, {
				chart: {
					zoomType: 'x',
					type: 'bar'
				},
				title: {
					text: undefined,
					align: 'left'
				},
				subtitle: {
					text: undefined,
					align: 'left'
				},
				xAxis: {
					categories: chartData.x?.values,
				},
				yAxis: {
					title: {
						text: undefined
					},
					min: chartData.y?.min,
					max: chartData.y?.max,
					// labels: {
					// 	formatter: function() {
					// 		return this.value / 1000000 + 'M';
					// 	}
					// },
					gridLineDashStyle: 'Dash',
					gridLineWidth: 2,
					tickInterval: chartData.y?.tickInterval
				},
				legend: {
					enabled: false
				},
				plotOptions: {
					area: {
						color: '#9254F6',
						fillColor: {
							linearGradient: {
								x1: 0,
								y1: 0,
								x2: 0,
								y2: 1
							},
							stops: [
								[0, '#D3DDFFFF'],
								[1, '#C6D2FD00'],
							]
						},
						marker: {
							radius: 0
						},
						lineWidth: 3,
						states: {
							hover: {
								lineWidth: 3,
								marker: {
									radius: 6
								}
							}
						},
						threshold: null
					}
				},

				series: chart.series.map(series => ({
					color: series.color,
					name: series.name,
					borderRadius: 20,
					data: chartData.series.find(x => x.key === series.key).data
				}))
			});
		},
		highchartColumn(chart) {
			const chartData = this.getChartData(chart);

			Highcharts.chart(`dashboard-chart-${chart.key}`, {
				chart: {
					zoomType: 'x',
					type: 'column'
				},
				title: {
					text: undefined,
					align: 'left'
				},
				subtitle: {
					text: undefined,
					align: 'left'
				},
				xAxis: {
					categories: chartData.x?.values,
				},
				yAxis: {
					title: {
						text: undefined
					},
					min: chartData.y?.min,
					max: chartData.y?.max,
					// labels: {
					// 	formatter: function() {
					// 		return this.value / 1000000 + 'M';
					// 	}
					// },
					gridLineDashStyle: 'Dash',
					gridLineWidth: 2,
					tickInterval: chartData.y?.tickInterval
				},
				legend: {
					enabled: false
				},
				plotOptions: {
					area: {
						color: '#9254F6',
						fillColor: {
							linearGradient: {
								x1: 0,
								y1: 0,
								x2: 0,
								y2: 1
							},
							stops: [
								[0, '#D3DDFFFF'],
								[1, '#C6D2FD00'],
							]
						},
						marker: {
							radius: 0
						},
						lineWidth: 3,
						states: {
							hover: {
								lineWidth: 3,
								marker: {
									radius: 6
								}
							}
						},
						threshold: null
					}
				},

				series: chart.series.map(series => ({
					color: series.color,
					name: series.name,
					borderRadius: 20,
					data: chartData.series.find(x => x.key === series.key).data
				}))
			});
		},
		highchartPie(chart) {
			const chartData = this.getChartData(chart);

			Highcharts.chart(`dashboard-chart-${chart.key}`, {
				chart: {
					zoomType: 'x',
					type: 'pie'
				},
				title: {
					text: undefined,
					align: 'left'
				},
				subtitle: {
					text: undefined,
					align: 'left'
				},
				legend: {
					enabled: false
				},
				plotOptions: {
					area: {
						color: '#9254F6',
						fillColor: {
							linearGradient: {
								x1: 0,
								y1: 0,
								x2: 0,
								y2: 1
							},
							stops: [
								[0, '#D3DDFFFF'],
								[1, '#C6D2FD00'],
							]
						},
						marker: {
							radius: 0
						},
						lineWidth: 3,
						states: {
							hover: {
								lineWidth: 3,
								marker: {
									radius: 6
								}
							}
						},
						threshold: null
					}
				},

				series: chart.series.map(series => ({
					allowPointSelect: true,
					cursor: 'pointer',
					dataLabels: [{
						enabled: true,
						distance: 20
					}, {
						enabled: true,
						distance: -40,
						format: '{point.percentage:.1f}%',
						style: {
							fontSize: '1.2em',
							textOutline: 'none',
							opacity: 0.7
						},
						filter: {
							operator: '>',
							property: 'percentage',
							value: 10
						}
					}],
					color: series.color,
					name: series.name,
					borderRadius: 20,
					data: chartData.series.find(x => x.key === series.key).data
				}))
			});
		},
		highchartMultiline(chart) {
			const chartData = this.getChartData(chart);

			Highcharts.chart(`dashboard-chart-${chart.key}`, {
				chart: {
					zoomType: 'x',
				},
				title: {
					text: undefined,
					align: 'left'
				},
				subtitle: {
					text: undefined,
					align: 'left'
				},
				xAxis: {
					type: 'datetime',
					min: chartData.x?.min,
					max: chartData.x?.max,
					gridLineWidth: 1,
					labels: {
						formatter: function () {
							return Highcharts.dateFormat('%m-%d', this.value);
						}
					}
				},
				yAxis: {
					title: {
						text: undefined
					},
					min: chartData.y?.min,
					max: chartData.y?.max,
					// labels: {
					// 	formatter: function() {
					// 		return this.value / 1000 + 'k';
					// 	}
					// },
					gridLineDashStyle: 'Dash',
					tickInterval: chartData.y?.tickInterval
				},
				legend: {
					enabled: false
				},
				plotOptions: {
					line: {
						color: '#9254F6',
						marker: {
							radius: 0
						},
						lineWidth: 4,
						states: {
							hover: {
								lineWidth: 3,
								marker: {
									radius: 6
								}
							}
						},
						threshold: null
					}
				},

				series: chart.series.map(series => ({
					name: series.name,
					color: series.color,
					data: chartData.series.find(x => x.key === series.key).data
				}))
			});
		},
		getCurrentDateFormatted() {
			const now = new Date();
			now.setDate(now.getDate() + 1);

			const year = now.getFullYear();
			const month = (now.getMonth() + 1).toString().padStart(2, '0');
			const day = now.getDate().toString().padStart(2, '0');
			return `${year}-${month}-${day}`;
		},
		getDate30DaysAgoFormatted() {
			const now = new Date();
			now.setDate(now.getDate() - 30);

			const year = now.getFullYear();
			const month = (now.getMonth() + 1).toString().padStart(2, '0');
			const day = now.getDate().toString().padStart(2, '0');

			return `${year}-${month}-${day}`;
		}
	}
}

export const dashboards = [
	{
		key: 'loyalty',
		title: 'Loyalty Performance',
		charts: [
			{
				key: 'roi',
				type: 'highcharts-line',
				title: 'Return on Loyalty Spend',
				topRight: {
					type: 'trend',
					valueKey: 'trend',
					comparison: 'DAILY % CHANGE'
				},
				series: [{
					key: 'roi',
					name: 'ROLS'
				}],
				titleValue: {
					key: 'value'
				}
			},
			{
				key: 'loyalty_revenue_daily',
				type: 'highcharts-multiline',
				title: 'Revenue from Loyalty Members',
				topRight: 'trend',
				topRight: {
					type: 'trend',
					valueKey: 'trend',
					comparison: 'DAILY % CHANGE'
				},
				series: [{ key: 'member_revenue', name: 'Loyalty Revenue' }],
				titleValue: {
					key: 'value'
				}
			}, {
				key: 'loyalty_points_balance',
				type: 'highcharts-line',
				title: 'Points Outstanding',
				topRight: {
					type: 'trend',
					valueKey: 'trend',
					comparison: 'DAILY % CHANGE'
				},
				series: [{
					key: 'totalpoints',
					name: 'Points Outstanding'
				}],
				titleValue: {
					key: 'value'
				}
			}, {
				key: 'active_members',
				title: 'Member Activity',
				type: 'highcharts-line',
				topRight: {
					type: 'legend',
				},
				series: [{
					key: 'new_members',
					name: 'New Members',
					color: '#962DFF'
				}, {
					key: 'active_members',
					name: 'Active Members',
					color: '#FCB5C3'
				}],
				titleValue: {
					key: 'value',
					subtitle: 'active members'
				}
			}],
		// tables: [{
		// 	title: 'Top Rewards',
		// 	key: 'rewards',
		// 	headers: [
		// 		{
		// 			name: 'NAME',
		// 			tooltip: 'NAME',
		// 			value: 'NAME'
		// 		}, {
		// 			name: 'STATUS',
		// 			tooltip: 'STATUS',
		// 			value: 'STATUS'
		// 		}, {
		// 			name: 'REVENUE',
		// 			tooltip: 'REVENUE',
		// 			value: 'REVENUE'
		// 		}, {
		// 			name: 'EARN REWARDS',
		// 			tooltip: 'EARN REWARDS',
		// 			value: 'EARN REWARDS'
		// 		}, {
		// 			name: 'EARN ENGAGEMENT',
		// 			tooltip: 'EARN ENGAGEMENT',
		// 			value: 'EARN ENGAGEMENT'
		// 		}
		// 	]
		// }]
	},
	/*{
		key: 'segmentation',
		title: 'Member Insights',
		charts: [
			{
				key: 'revenue-by-segment',
				title: 'Revenue by Segment',
				type: 'highcharts-column',
				topRight: {
					type: 'legend'
				},
				series: [
					{
						key: 'masculine',
						name: 'Member',
						color: '#962DFF'
					}, {
						key: 'feminine',
						name: 'Non-Member',
						color: '#FCB5C3'
					}
				],
				titleValue: {
					key: 'value',
					subtitle: 'member revenue'
				}
			}, {
				key: 'engagement-by-segment',
				type: 'highcharts-column',
				title: 'Engagement by Segment',
				topRight: {
					type: 'legend'
				},
				series: [
					{
						key: 'masculine',
						name: 'Member',
						color: '#962DFF'
					}, {
						key: 'feminine',
						name: 'Member',
						color: '#FCB5C3'
					}
				],
				titleValue: {
					key: 'value',
					subtitle: 'member engagement'
				}
			}, {
				key: 'repeat-probability',
				type: 'highcharts-column',
				wide: true,
				title: 'Chance to Make a Repeat Purchase',
				topRight: {
					type: 'legend'
				},
				series: [
					{
						key: 'member',
						name: 'Member',
						color: '#962DFF'
					}, {
						key: 'non-member',
						name: 'Non-Member',
						color: '#FCB5C3'
					}
				],
				titleValue: {
					key: 'value',
					subtitle: 'for member second purchase'
				}
			}, {
				key: 'average-purchase-count',
				type: 'highcharts-multiline',
				title: 'Average Number of Purchases',
				topRight: {
					type: 'legend'
				},
				series: [
					{
						key: 'new-members',
						name: 'New Members',
						color: '#962DFF'
					}, {
						key: 'active-members',
						name: 'Active Members',
						color: '#FCB5C3'
					}
				],
				titleValue: {
					key: 'value',
				}
			}, {
				key: 'loyalty_points_balance',
				type: 'highcharts-line',
				title: 'Average Loyalty Score',
				topRight: {
					type: 'trend',
					valueKey: 'trend',
					comparison: 'DAILY % CHANGE'
				},
				series: [{
					key: 'data'
				}],
				titleValue: {
					key: 'value'
				}
			},
		]
	}*/
];
</script>
<style>.apexcharts-theme-light {
	min-height: 400px;
}
</style>
