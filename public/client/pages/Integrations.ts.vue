<template>
	<StatusMessage :message=status.message :status=status.type @resetStatus="status.type = 'nope'"></StatusMessage>

	<div class="p-2 sm:p-7 mr-24 flex flex-col items-center justify-center" v-if="!isFeatureAvailable && !isTrialEnded">
		<img src="../images/UpgradeIntegrations.png" width="584">
		<a class="text-ralprimary-dark font-bold md:text-xl lg:text-2xl sm:text-normal mt-4 font-[Inter]" href="/loyalty/settings/plans">Unlock Integrations</a>
		<p class="text-ralblack-primary text-lg mt-4 flex items-center font-[Inter] mb-4 w-1/2 text-center">
			Get access to the most popular integrations like Klaviyo, Judge.me, and more.
		</p>
		<PrimaryButton
					cta="Upgrade Loyalty Plan"
					size="xs"
					@click="() => this.$router.push('/loyalty/settings/plans')"
					/>
	</div>

	<div class="bg-gradient-to-tr from-[#4F3EAC] to-[#5E48F8] p-4 w-full justify-center shadow-lg flex items-center" v-if="!isFeatureAvailable && isTrialEnded">
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 14H11V9.8L12.6 11.4L14 10L10 6L6 10L7.4 11.4L9 9.8V14ZM10 20C8.61667 20 7.31667 19.7375 6.1 19.2125C4.88333 18.6875 3.825 17.975 2.925 17.075C2.025 16.175 1.3125 15.1167 0.7875 13.9C0.2625 12.6833 0 11.3833 0 10C0 8.61667 0.2625 7.31667 0.7875 6.1C1.3125 4.88333 2.025 3.825 2.925 2.925C3.825 2.025 4.88333 1.3125 6.1 0.7875C7.31667 0.2625 8.61667 0 10 0C11.3833 0 12.6833 0.2625 13.9 0.7875C15.1167 1.3125 16.175 2.025 17.075 2.925C17.975 3.825 18.6875 4.88333 19.2125 6.1C19.7375 7.31667 20 8.61667 20 10C20 11.3833 19.7375 12.6833 19.2125 13.9C18.6875 15.1167 17.975 16.175 17.075 17.075C16.175 17.975 15.1167 18.6875 13.9 19.2125C12.6833 19.7375 11.3833 20 10 20ZM10 18C12.2333 18 14.125 17.225 15.675 15.675C17.225 14.125 18 12.2333 18 10C18 7.76667 17.225 5.875 15.675 4.325C14.125 2.775 12.2333 2 10 2C7.76667 2 5.875 2.775 4.325 4.325C2.775 5.875 2 7.76667 2 10C2 12.2333 2.775 14.125 4.325 15.675C5.875 17.225 7.76667 18 10 18Z" fill="#FFA3DF"/>
        </svg>
        <div class="text-white ml-2">Your trial has expired and your program has been deactivated.</div>
        <div class="px-2 py-2 text-white text-sm border-[#FFF] border rounded-full ml-4 hover:text-black hover:bg-[#FFF] hover:cursor-pointer transitional-bg delay-200" @click="() => this.$router.push('/loyalty/settings/plans')">
                    Choose a Plan
        </div>
    </div>

	<div class="p-2 sm:p-7" v-if="isFeatureAvailable || isTrialEnded">
		<div class="text-neutral-800 text-opacity-70 sm:text-4xl md:text-6xl lg:text-7xl font-normal font-['Open Sans']">Integrations</div>

		<!-- <div v-if="freeTrialRunning"
			class="bg-[#222141] rounded-2xl p-4 mt-12 mb-8">
			<div class="inline-flex items-center mb-4">
				<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#FFA3DF"><path d="M320-160h320v-120q0-66-47-113t-113-47q-66 0-113 47t-47 113v120Zm160-360q66 0 113-47t47-113v-120H320v120q0 66 47 113t113 47ZM160-80v-80h80v-120q0-61 28.5-114.5T348-480q-51-32-79.5-85.5T240-680v-120h-80v-80h640v80h-80v120q0 61-28.5 114.5T612-480q51 32 79.5 85.5T720-280v120h80v80H160Zm320-80Zm0-640Z"/></svg>
				<p class="text-white text-lg ml-2">Strategist Plan Feature (Available During Trial)</p>
			</div>
			<p class="text-white text-base">
			Focus on connecting Klaviyo and Shopify to increase the knowledge Raleon has to help craft better emails.
			</p>
			<p class="text-white text-base italic mt-4">Upgrade to keep this feature after your trial.</p>
			<div class="mt-4 flex justify-left">
				<PrimaryButton
					cta="Upgrade Plan"
					size="xs"
					@click="() => this.$router.push('/loyalty/integrations')"
				/>
			</div>
		</div> -->

		<!-- Primary Integrations (Shopify and Klaviyo) -->
		<CardContainer class="mt-8" v-for="integration in primaryIntegrations" :key="integration.id">
			<div class="flex items-center justify-between">

				<div class="flex items-center">
					<div class="mr-4">
						<img :src="integration.imageURL" width="30" height="21" />
					</div>

					<div class="flex flex-col align-top">
						<div class="text-ralblack-primary text-lg font-semibold font-['Inter']">
							{{ integration.name }}
						</div>
						<div class="text-ralblack-primary text-base font-medium font-['Inter']">
							{{ integration.description }}
						</div>
						<a :href="integration.docURL" target="_blank"
						class="font-['Inter'] mt-2 text-ralprimary-main text-sm font-semibold inline cursor-pointer hover:underline whitespace-nowrap">
							{{ integration.docURLText }}</a>
					</div>
				</div>

				<div v-if="integration.showConnectButton" class="flex flex-col items-end">
					<LightSecondaryButton cta="Connect" v-if="!integration.connected" @click="connect(integration)">
					</LightSecondaryButton>
					<LightSecondaryButton cta="Disconnect" v-if="integration.connected" @click="disconnect(integration)">
					</LightSecondaryButton>
					<div v-if="integration.connected && integration.canBeActivated" class="mt-4">
						<ToggleItem @toggleChange="toggleActive(integration)" :state="integration.enabled" showLabel=true
							onLabel="Active" offLabel="Inactive"></ToggleItem>
					</div>
				</div>
				<div v-else-if="!integration.customComponent" class="flex flex-col items-end">
					<LightSecondaryButton cta="Read Docs" @click="openDocs(integration)"> </LightSecondaryButton>
				</div>
				<div v-else-if="!integration.editing" class="flex flex-col items-end">
					<LightSecondaryButton cta="Configure" @click="editIntegration(integration, true)"></LightSecondaryButton>
					<div v-if="integration.canBeActivated" class="mt-4">
						<ToggleItem @toggleChange="toggleActive(integration)" :state="integration.enabled" showLabel=true
							onLabel="Active" offLabel="Inactive"></ToggleItem>
					</div>
				</div>
				<div v-else-if="integration.editing" class="flex flex-col items-end">
					<LightSecondaryButton cta="Collapse" @click="editIntegration(integration, false)"> </LightSecondaryButton>
					<div v-if="integration.canBeActivated" class="mt-4">
						<ToggleItem @toggleChange="toggleActive(integration)" :state="integration.enabled" showLabel=true
							onLabel="Active" offLabel="Inactive"></ToggleItem>
					</div>
				</div>
			</div>
			<div v-if="integration.customComponent && integration.editing">
				<component
					:is="integration.customComponent"
					@set-status="this.setStatus">
				</component>
			</div>
		</CardContainer>

		<!-- Other Integrations - Collapsible Section -->
		<div class="mt-8" v-if="otherIntegrations.length > 0">
			<div class="bg-white rounded-lg border shadow-sm">
				<button @click="showOtherIntegrations = !showOtherIntegrations"
					class="w-full px-6 py-4 flex items-center justify-between hover:bg-gray-50 transition-colors">
					<h3 class="text-lg font-semibold text-ralblack-primary">Other Integrations</h3>
					<svg class="w-5 h-5 text-gray-500 transition-transform"
						:class="{ 'rotate-180': showOtherIntegrations }"
						fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
					</svg>
				</button>

				<div v-show="showOtherIntegrations" class="border-t">
					<div v-for="integration in otherIntegrations" :key="integration.id" class="border-b last:border-b-0">
						<div class="p-6">
							<div class="flex items-center justify-between">
								<div class="flex items-center">
									<div class="mr-4">
										<img :src="integration.imageURL" width="30" height="21" />
									</div>

									<div class="flex flex-col align-top">
										<div class="text-ralblack-primary text-lg font-semibold font-['Inter']">
											{{ integration.name }}
										</div>
										<div class="text-ralblack-primary text-base font-medium font-['Inter']">
											{{ integration.description }}
										</div>
										<a :href="integration.docURL" target="_blank"
										class="font-['Inter'] mt-2 text-ralprimary-main text-sm font-semibold inline cursor-pointer hover:underline whitespace-nowrap">
											{{ integration.docURLText }}</a>
									</div>
								</div>

								<div v-if="integration.showConnectButton" class="flex flex-col items-end">
									<LightSecondaryButton cta="Connect" v-if="!integration.connected" @click="connect(integration)">
									</LightSecondaryButton>
									<LightSecondaryButton cta="Disconnect" v-if="integration.connected" @click="disconnect(integration)">
									</LightSecondaryButton>
									<div v-if="integration.connected && integration.canBeActivated" class="mt-4">
										<ToggleItem @toggleChange="toggleActive(integration)" :state="integration.enabled" showLabel=true
											onLabel="Active" offLabel="Inactive"></ToggleItem>
									</div>
								</div>
								<div v-else-if="!integration.customComponent" class="flex flex-col items-end">
									<LightSecondaryButton cta="Read Docs" @click="openDocs(integration)"> </LightSecondaryButton>
								</div>
								<div v-else-if="!integration.editing" class="flex flex-col items-end">
									<LightSecondaryButton cta="Edit" @click="editIntegration(integration, true)"></LightSecondaryButton>
									<div v-if="integration.canBeActivated" class="mt-4">
										<ToggleItem @toggleChange="toggleActive(integration)" :state="integration.enabled" showLabel=true
											onLabel="Active" offLabel="Inactive"></ToggleItem>
									</div>
								</div>
								<div v-else-if="integration.editing" class="flex flex-col items-end">
									<LightSecondaryButton cta="Collapse" @click="editIntegration(integration, false)"> </LightSecondaryButton>
									<div v-if="integration.canBeActivated" class="mt-4">
										<ToggleItem @toggleChange="toggleActive(integration)" :state="integration.enabled" showLabel=true
											onLabel="Active" offLabel="Inactive"></ToggleItem>
									</div>
								</div>
							</div>
							<div v-if="integration.customComponent && integration.editing" class="mt-4">
								<component
									:is="integration.customComponent"
									@set-status="this.setStatus">
								</component>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div
			class="mx-auto rounded-2xl border border-violet-300 border-dashed verflow-hidden border-2 mb-2 px-6 py-6 text-center mt-8">
			<div class="text-ralblack-primary text-sm font-medium font-['Inter']">
				Is there an integration you're looking for?
				<span
					class="font-['Inter'] mt-2 text-ralprimary-main text-sm font-semibold inline cursor-pointer hover:underline whitespace-nowrap"
					@click="openChat()">
					Let us know so we can add it!
				</span>
			</div>
		</div>
	</div>
</template>

<script>

import * as Utils from '../../client-old/utils/Utils';
import CardContainer from '../components/CardContainer.ts.vue';
import LightSecondaryButton from '../components/LightSecondaryButton.ts.vue'
import ToggleItem from '../components/ToggleItem.ts.vue';
import StatusMessage from '../components/StatusMessage.ts.vue';
import StampedReviews from '../components/integrations/StampedReviews.ts.vue';
import Klaviyo from '../components/integrations/Klaviyo.ts.vue';
import Skio from '../components/integrations/Skio.ts.vue';
import Stay from '../components/integrations/Stay.ts.vue';
import Prive from '../components/integrations/Prive.ts.vue';
import Loop from '../components/integrations/Loop.ts.vue';
import { customerIOTrackEvent } from '../services/customerio.js';
import { useRoute } from 'vue-router';
import { isFeatureAvailable } from '../services/features.js';
import PrimaryButton from '../components/PrimaryButton.ts.vue';
import Sendlane from '../components/integrations/Sendlane.ts.vue';
import ShopifyConnection from '../components/integrations/ShopifyConnection.ts.vue';
import { Crisp } from 'crisp-sdk-web';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	components: {
		CardContainer,
		LightSecondaryButton,
		StatusMessage,
		ToggleItem,
		StampedReviews,
		Klaviyo,
		Skio,
		Sendlane,
		Stay,
		Prive,
		PrimaryButton,
		Loop,
		ShopifyConnection
	},
	async mounted() {
		customerIOTrackEvent('Integrations Viewed');
		const route = useRoute();
		await this.getAvailableIntegrations();

		if (route.query.code && route.query.name) {
			this.exchangeCode(route.query.code, route.query.name);
			const urlWithoutQuery = window.location.pathname;
    		window.history.pushState({}, '', urlWithoutQuery);
		}
	},
	data() {
		return {
			status: {},
			isConnected: false,
			isActive: true,
			activeState: true,
			availableIntegrations: [],
			exchangingCode: false,
			showOtherIntegrations: false,
		}
	},
	computed: {
		isFeatureAvailable() {
			return isFeatureAvailable('integrations');
		},
		isTrialEnded() {
			const freeTrialData = JSON.parse(localStorage.getItem('freeTrialData'));

			return freeTrialData != {} && !freeTrialData.hasPaidPlan && freeTrialData.daysLeft < 0
		},
		freeTrialRunning() {
			const freeTrialData = JSON.parse(localStorage.getItem('freeTrialData'));

			if(freeTrialData != {} && !freeTrialData.hasPaidPlan && freeTrialData.daysLeft > 0)
				return true;
			else
				return false;
		},
		primaryIntegrations() {
			return this.availableIntegrations.filter(integration =>
				integration.name === 'Shopify' || integration.name === 'Klaviyo'
			);
		},
		otherIntegrations() {
			return this.availableIntegrations.filter(integration =>
				integration.name !== 'Shopify' && integration.name !== 'Klaviyo'
			);
		}
	},
	methods: {
		openDocs(integration) {
			customerIOTrackEvent('Integration: Open Docs ' + integration.name);
			window.open(integration.docURL, '_blank');
		},
		editIntegration(integration, editing) {
			integration.editing = editing;
		},
		openChat() {
			Crisp.chat.open();
		},
		setStatus(data) {
			this.status.type = data.type;
			this.status.message = data.message;
		},
		async getAvailableIntegrations() {
			const response = await fetch(`${URL_DOMAIN}/integrations`, {
				method: 'GET',
				headers: {
					'Content-Type': 'application/json',
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
				}
			});
			const jsonresponse = await response.json();
			console.log("Response from integration list:", jsonresponse);

			// Modify Shopify integration to use custom component
			jsonresponse.forEach(integration => {
				if (integration.name === 'Shopify') {
					integration.showConnectButton = false;
					integration.customComponent = 'ShopifyConnection';
					integration.editing = false; // Start collapsed
				}
			});

			this.availableIntegrations = jsonresponse;
		},
		async connect(selectedIntegration) {
			customerIOTrackEvent('Integration: Connect ' + selectedIntegration.name);
			let url = selectedIntegration.oAuthURL;
			if (selectedIntegration.name === 'Shopify') {
				url = url.replace('{STORE_URL}', localStorage.getItem('externalDomain'));
				url = url.replace('{ORGID}', localStorage.getItem('userOrgId'));
			}
			window.location.href = url;
		},
		async toggleActive(integration) {
			integration.enabled = !integration.enabled;
			const response = await fetch(`${URL_DOMAIN}${integration.enableDisableURL}`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify({
					enabled: integration.enabled
				}),
			});
			const jsonresponse = await response.json();
			console.log("Response from exchange:", jsonresponse);
			if (jsonresponse.success) {
				await this.getAvailableIntegrations();
				this.setStatus({
					type: 'success',
					message: "Successfully toggled activation for this integration.",
				});
			}
			else {
				integration.enabled = !integration.enabled;
				let message = integration.errorMessage || "Something went wrong toggling activation for this integration. Please try again, or reach out to Raleon support for assistance.";
				this.setStatus({
					type: 'fail',
					message: message,
				});
			}
		},
		async disconnect(selectedIntegration) {
			if (selectedIntegration.name === 'Shopify') {
				let url = localStorage.getItem('externalDomain');
				window.location.href = `https://${url}/admin/apps/raleon`;
				return;
			}
			const response = await fetch(`${URL_DOMAIN}/integration/disconnect`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify({
					integrationId: selectedIntegration.id
				})
			});
			const jsonresponse = await response.json();
			console.log("Response from disconnect:", jsonresponse);
			if (jsonresponse.success) {
				await this.getAvailableIntegrations();
				this.setStatus({
					type: 'success',
					message: "Successfully Disconnected Integration.",
				});
			}
			else {
				this.setStatus({
					type: 'fail',
					message: "Failed to Disconnect Integration. Please try again, or reach out to Raleon support for assistance.",
				});
			}
		},
		async exchangeCode(code, name) {
			console.log("Exchanging code:", code, name)
			this.exchangingCode = true;

			//Find the integration that matches the name
			const integration = this.availableIntegrations.find(integration => integration.name.toLowerCase() === name.toLowerCase());

			if (!integration) {
				//Need to show error here???
				this.setStatus({
					type: 'fail',
					message: "Can't find integration to connect to. Please try again, or reach out to Raleon support for assistance.",
				});
				return;
			}

			// Logic to handle the exchange of code for token
			console.log("Exchanging code for token:", code);
			const response = await fetch(`${URL_DOMAIN}${integration.exchangeApiUrl}`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify({
					code: code,
				}),
			});
			const jsonresponse = await response.json();
			console.log("Response from exchange:", jsonresponse);
			if (jsonresponse.success) {
				await this.getAvailableIntegrations();
			}
			else {
				this.setStatus({
					type: 'fail',
					message: "Something went wrong activating this integration. Please try again, or reach out to Raleon support for assistance.",
				});
			}

			this.exchangingCode = false;
		}
	},
}
</script>
