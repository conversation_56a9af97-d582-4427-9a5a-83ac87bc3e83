<template>
	<ProgramActive></ProgramActive>
	<div class="p-2 sm:p-7 mr-24 flex flex-col items-center justify-center" v-if="!isFeatureAvailable && !isTrialEnded">
		<img src="../images/VIPTeirPromo.png" width="800">
		<a class="text-ralprimary-dark font-bold md:text-xl lg:text-2xl sm:text-normal mt-4 font-[Inter]" href="/loyalty/settings/plans">Upgrade to Loyalty Plan</a>
		<p class="text-ralblack-primary text-lg mt-4 flex items-center font-[Inter] mb-4 w-1/2 text-center">
			Loyalty programs powered by Raleon help brands increase repeat purchase rate and customer lifetime value.
		</p>
		<PrimaryButton
					cta="Upgrade Loyalty Plan"
					size="xs"
					@click="() => this.$router.push('/loyalty/settings/plans')"
					/>
	</div>

	<div class="bg-gradient-to-tr from-[#4F3EAC] to-[#5E48F8] p-4 w-full justify-center shadow-lg flex items-center" v-if="!isFeatureAvailable && isTrialEnded">
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 14H11V9.8L12.6 11.4L14 10L10 6L6 10L7.4 11.4L9 9.8V14ZM10 20C8.61667 20 7.31667 19.7375 6.1 19.2125C4.88333 18.6875 3.825 17.975 2.925 17.075C2.025 16.175 1.3125 15.1167 0.7875 13.9C0.2625 12.6833 0 11.3833 0 10C0 8.61667 0.2625 7.31667 0.7875 6.1C1.3125 4.88333 2.025 3.825 2.925 2.925C3.825 2.025 4.88333 1.3125 6.1 0.7875C7.31667 0.2625 8.61667 0 10 0C11.3833 0 12.6833 0.2625 13.9 0.7875C15.1167 1.3125 16.175 2.025 17.075 2.925C17.975 3.825 18.6875 4.88333 19.2125 6.1C19.7375 7.31667 20 8.61667 20 10C20 11.3833 19.7375 12.6833 19.2125 13.9C18.6875 15.1167 17.975 16.175 17.075 17.075C16.175 17.975 15.1167 18.6875 13.9 19.2125C12.6833 19.7375 11.3833 20 10 20ZM10 18C12.2333 18 14.125 17.225 15.675 15.675C17.225 14.125 18 12.2333 18 10C18 7.76667 17.225 5.875 15.675 4.325C14.125 2.775 12.2333 2 10 2C7.76667 2 5.875 2.775 4.325 4.325C2.775 5.875 2 7.76667 2 10C2 12.2333 2.775 14.125 4.325 15.675C5.875 17.225 7.76667 18 10 18Z" fill="#FFA3DF"/>
        </svg>
        <div class="text-white ml-2">Your trial has expired and your program has been deactivated.</div>
        <div class="px-2 py-2 text-white text-sm border-[#FFF] border rounded-full ml-4 hover:text-black hover:bg-[#FFF] hover:cursor-pointer transitional-bg delay-200" @click="() => this.$router.push('/loyalty/settings/plans')">
                    Choose a Plan
        </div>
    </div>

	<div class="m-3 sm:m-10 sm:m-7" v-if="isFeatureAvailable || isTrialEnded">
		<div class="w-full flex flex-col sm:flex-row items-start justify-between lg:mb-4">
			<h1 class="text-3xl sm:text-6xl font-sans font-medium opacity-70 text-ralblack-primary">Referrals</h1>
			<div class="flex-shrink-0 mt-2">
				<PrimaryButton
					cta="Save"
					@click="save"
					:disabled="disableSave"
					size="small"
					:showSpinner="isSaving">
				</PrimaryButton>
			</div>
		</div>
		<div class="container mx-auto">
			<div class="flex flex-col lg:flex-row">
				<div class="w-full lg:flex-grow lg:min-w-0 lg:pr-4 mb-4 lg:mb-0">
					<div v-if="!loading"
						class="bg-white rounded-2xl border shadow border-ralprimary-light pl-4 pr-4 pb-4 border-opacity-50 hover:border-opacity-90 hover:shadow-lg flex-grow transition-all duration-300 ease-in-out overflow-hidden">
						<label class="flex text-ralsecondary-start text-2xl font-normal font-['Inter'] mt-4">Enable Referral Program</label>
						<div
							class="relative inline-block w-10 mr-2 mt-3 align-middle select-none transition duration-200 ease-in">
							<input type="checkbox" id="referralsActive" :disabled="disableEnable"
								class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
								v-model="referralsActive" />
							<label for="referralsActive"
								class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
						</div>
						<div class="mt-4 text-ralgray-dark text-sm font-normal font-['Inter'] leading-none">
							When you're finished here, go to Referral Branding to customize the look and feel of the referral program.
							<LightSecondaryButton
								cta="Referral Styling"
								@click="navigateToBranding"
								class="mx-2">
							</LightSecondaryButton>
						</div>
					</div>
					<div v-if="!loading"
						class="bg-white rounded-2xl border shadow border-ralprimary-light pl-4 pr-4 pb-4 border-opacity-50 hover:border-opacity-90 hover:shadow-lg mt-10 flex-grow transition-all duration-300 ease-in-out overflow-hidden"
						>

							<div class="flex text-ralsecondary-start text-2xl font-normal font-['Inter'] mt-4">
								Referral Reward
							</div>
							<div class="flex flex-col lg:flex-row lg:items-center mt-4">
								<div class="">
									<div class="text-slate-800 text-base font-semibold font-['Inter'] leading-normal">
									Choose your reward type
									</div>

									<div class="w-full self-stretch justify-start items-center flex mt-2">
										<select
											class="w-52 h-12 rounded-lg border border-ralbackground-dark-line border-opacity-20 flex"
											v-model="referralReward">
											<option
												v-for="item in referralOptions"
												:value="item.value"
											>
												{{ item.label }}
											</option>
										</select>
									</div>
								</div>
								<div class="mt-6 lg:mt-0 lg:ml-20">
									<div class="text-slate-800 text-base font-semibold font-['Inter'] leading-normal">
									Reward Value
									</div>
									<div
										class="mt-2 h-12 px-4 py-3 bg-white rounded-lg border border-ralbackground-dark-line border-opacity-20 justify-start lg:items-center lg:gap-2 inline-flex">
										{{dynamicReferralLabel}}
										<input
											type="text"
											v-model="referralRewardValue"
											class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
											maxlength="40" />
									</div>
								</div>
							</div>

							<div class="mt-8 text-zinc-900 text-xl font-normal font-['Open Sans'] leading-loose flex-grow truncate">
								Reward Look & Feel
							</div>
							<div class="text-ralgray-dark text-xs font-normal font-['Inter']">
								This is what the reward will look like to your customer when they get it.
							</div>

							<div class="text-sm text-ralgray-dark font-semibold font-['Inter'] leading-normal tracking-wide mt-8">
								Reward Name
								<span class="text-rose-500">*</span>
							</div>
							<!-- <div class="mb-3 text-black text-sm font-normal italic font-['Inter']">
								{{ reward.title }} with amount {{ reward.amount }}
							</div>  -->
							<div class="mb-3 text-ralgray-dark text-xs font-normal font-['Inter']">
								This will be shown to describe the particular reward to the shopper!
							</div>

							<div
								class="mb-8 mr-3 w-full h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex"
								:class="{
									'border-ralerror-dark': false,
									'border-gray-400': false,
								}"
							>
								<div class="w-full mr-3 self-stretch justify-start items-center gap-2 flex">
									<input
										v-model="referralRewardName"
										class="no-focus-outline border-none w-full text-gray-600 text-sm font-semibold font-['Inter'] leading-normal appearance-none outline-none"
										maxlength="40"
									/>
								</div>
							</div>
							<div class="text-sm text-ralgray-dark font-semibold font-['Inter'] leading-normal tracking-wide">
								Reward Description
								<span class="text-rose-500">*</span>
							</div>
							<div class="mb-3 text-ralgray-dark text-xs font-normal font-['Inter'] leading-none">
								Describe the reward to provide the customer more details about what they're receiving.
							</div>
							<div
								class="mb-8 w-full h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex"
								:class="{
									'border-ralerror-dark': false,
									'border-gray-400': false,
								}"
							>
								<div class="w-full mr-3 self-stretch justify-start items-center gap-2 flex">
									<input
										v-model="referralRewardDescription"
										class="no-focus-outline border-none w-full text-gray-600 text-sm font-semibold font-['Inter'] leading-normal appearance-none outline-none"
										maxlength="80"
									/>
								</div>
							</div>
							<div class="text-sm text-ralgray-dark font-semibold font-['Inter'] leading-normal tracking-wide">
								Upload Reward Image
								<span class="text-rose-500">*</span>
							</div>
							<div class="text-ralgray-dark text-xs font-normal font-['Inter'] leading-none">
								This image will be shown to the customer to represent the reward.
							</div>
							<div class="flex flex-wrap mt-3">
								<div class="reward-image-wrapper">
									<div class="reward-image-container">
										<img v-if="this.referralImageURL.endsWith('WTERewardDefault.jpg')"
											class="rounded-3xl border object-center object-cover border-red-500"
											:src="this.referralImageURL"
											width="20"
											height="20" />
										<img v-else
											:src="this.referralImageURL"
											class="rounded-3xl border object-center object-cover"
											width="20"
											height="20"
										/>
									</div>
								</div>
								<div class="flex flex-col flex-grow items-start justify-center">
									<div class="w-full ml-4">
										<ImageUpload @imageSelected="(event) => { this.referralImageURL = event.url; }"/>
									</div>
								</div>
							</div>


							<div class="pt-6" v-if="referralReward != 'points'">
								<div class="text-sm text-ralgray-dark font-semibold font-['Inter'] leading-normal tracking-wide">
									Minimum Order Total
									<span class="text-rose-500">*</span>
								</div>
								<div class="mb-3 text-ralgray-dark text-xs font-normal font-['Inter'] leading-none">
									Set the minimum order value required to use the coupon the referrer gets when friends make first purchases.
								</div>
								<div
									class="mt-2 h-12 px-4 py-3 bg-white rounded-lg border border-ralbackground-dark-line border-opacity-20 justify-start lg:items-center lg:gap-2 inline-flex">
									{{  currencyPrefix }}
									<input
										type="text"
										v-model="referralRewardMinimumOrderTotal"
										class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
										maxlength="40" />
								</div>
							</div>
					</div>

					<div v-if="!loading"
					class="bg-white rounded-2xl border shadow border-ralprimary-light pl-4 pr-4 pb-4 border-opacity-50 hover:border-opacity-90 hover:shadow-lg mt-10 flex-grow transition-all duration-300 ease-in-out overflow-hidden"
					>
						<div class="flex text-ralsecondary-start text-2xl font-normal font-['Inter'] mt-4">
							Friend Reward
						</div>
						<div class="flex flex-col lg:flex-row lg:items-center mt-4">
							<div>
								<div class=" text-slate-800 text-base font-semibold font-['Inter'] leading-normal">
								Choose your reward type
								</div>

								<div class="w-full self-stretch justify-start items-center gap-2 flex mt-2">
									<select
										class="w-52 h-12 px-2 rounded-lg border border-ralbackground-dark-line border-opacity-20 flex justify-center"
										v-model="friendReward">
										<option
											v-for="item in referralOptions"
											:value="item.value"
										>
											{{ item.label }}
										</option>
									</select>
								</div>
							</div>
							<div class="mt-6 lg:mt-0 lg:ml-20">
								<div class="text-slate-800 text-base font-semibold font-['Inter'] leading-normal">
								Reward Value
								</div>
								<div
								class="mt-2 h-12 px-4 py-3 bg-white rounded-lg border border-ralbackground-dark-line border-opacity-20 justify-start items-center gap-2 inline-flex">
										{{dynamicFriendLabel}}
										<input type="text"
											v-model="friendRewardValue"
											@input="debouncedSave"
											class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
											maxlength="40" />
								</div>
							</div>
						</div>

						<div class="transition-opacity duration-300 ease-in-out">
							<div class="mt-8 text-zinc-900 text-xl font-normal font-['Open Sans'] leading-loose flex-grow truncate">
								Friend Reward Look & Feel
							</div>
							<div class="text-ralgray-dark text-xs font-normal font-['Inter']">
								This is what the reward will look like to your customer when they get it.
							</div>

							<div class="text-sm text-ralgray-dark font-semibold font-['Inter'] leading-normal tracking-wide mt-8">
								Reward Name
								<span class="text-rose-500">*</span>
							</div>
							<!-- <div class="mb-3 text-black text-sm font-normal italic font-['Inter']">
								{{ reward.title }} with amount {{ reward.amount }}
							</div>  -->
							<div class="mb-3 text-ralgray-dark text-xs font-normal font-['Inter']">
								This will be shown to describe the particular reward to the shopper!
							</div>

							<div
								class="mb-8 mr-3 w-full h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex"
								:class="{
									'border-ralerror-dark': false,
									'border-gray-400': false,
								}"
							>
								<div class="w-full mr-3 self-stretch justify-start items-center gap-2 flex">
									<input
										v-model="friendRewardName"
										class="no-focus-outline border-none w-full text-gray-600 text-sm font-semibold font-['Inter'] leading-normal appearance-none outline-none"
										maxlength="40"
									/>
								</div>
							</div>
							<div class="text-sm text-ralgray-dark font-semibold font-['Inter'] leading-normal tracking-wide">
								Reward Description
								<span class="text-rose-500">*</span>
							</div>
							<div class="mb-3 text-ralgray-dark text-xs font-normal font-['Inter'] leading-none">
								Describe the reward to provide the customer more details about what they're receiving.
							</div>
							<div
								class="mb-8 w-full h-12 px-4 py-3 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2 inline-flex"
								:class="{
									'border-ralerror-dark': false,
									'border-gray-400': false,
								}"
							>
								<div class="w-full mr-3 self-stretch justify-start items-center gap-2 flex">
									<input
										v-model="friendRewardDescription"
										class="no-focus-outline border-none w-full text-gray-600 text-sm font-semibold font-['Inter'] leading-normal appearance-none outline-none"
										maxlength="80"
									/>
								</div>
							</div>
							<div class="text-sm text-ralgray-dark font-semibold font-['Inter'] leading-normal tracking-wide">
								Upload Reward Image
								<span class="text-rose-500">*</span>
							</div>
							<div class="text-ralgray-dark text-xs font-normal font-['Inter'] leading-none">
								This image will be shown to the customer to represent the reward.
							</div>

							<div class="flex flex-wrap mt-3">
								<div class="reward-image-wrapper">
									<div class="reward-image-container">
										<img v-if="this.friendRewardImageURL.endsWith('WTERewardDefault.jpg')"
											class="rounded-3xl border object-center object-cover border-red-500"
											:src="this.friendRewardImageURL"
											width="20"
											height="20" />
										<img v-else
											:src="this.friendRewardImageURL"
											class="rounded-3xl border object-center object-cover"
											width="20"
											height="20"
										/>
									</div>
								</div>
								<div class="flex flex-col flex-grow items-start justify-center">
									<div class="w-full ml-4">
										<ImageUpload @imageSelected="(event) => { this.friendRewardImageURL = event.url; }"/>
									</div>
								</div>
							</div>



							<div class="pt-6" v-if="friendReward != 'points'">
								<div class="text-sm text-ralgray-dark font-semibold font-['Inter'] leading-normal tracking-wide">
									Minimum Order Total
									<span class="text-rose-500">*</span>
								</div>
								<div class="mb-3 text-ralgray-dark text-xs font-normal font-['Inter'] leading-none">
									Set the minimum order value required to use the coupon the friend gets by following the link.
								</div>
								<div
									class="mt-2 h-12 px-4 py-3 bg-white rounded-lg border border-ralbackground-dark-line border-opacity-20 justify-start lg:items-center lg:gap-2 inline-flex">
									{{  currencyPrefix }}
									<input
										type="text"
										v-model="friendRewardMinimumOrderTotal"
										class="no-focus-outline border-none w-full text-gray-600 text-base font-semibold font-['Inter'] leading-normal appearance-none outline-none"
										maxlength="40" />
								</div>
							</div>
						</div>
					</div>
				</div>

				<div
					v-if="!loading"
					class="w-full lg:w-[440px] lg:flex-shrink-0">
					<WTELoyaltyPreview
						:earn="earn"
						:isLoading="loading"
						:isShopReward="false"
						:shouldNotTransform="true"
						class="w-full sticky hidden md:block"
					/>
				</div>
			</div>
		</div>
	</div>

	<StatusMessage
		:message=status.message
		:status=status.type
		@resetStatus="status.type = 'nope'">
	</StatusMessage>

</template>

<script>
import CardContainer from '../components/CardContainer.ts.vue';
import SuperDropdown from '../components/SuperDropdown.ts.vue';
import * as Utils from '../../client-old/utils/Utils';
import PrimaryButton from '../components/PrimaryButton.ts.vue';
import ProgramActive from '../components/ProgramActive.ts.vue';
import LightSecondaryButton from '../components/LightSecondaryButton.ts.vue';
import CancelButton from '../components/CancelButton.ts.vue';
import ToggleItem from '../components/ToggleItem.ts.vue';
import StatusMessage from '../components/StatusMessage.ts.vue';
import Tooltip from '../components/Tooltip.ts.vue';
import ImageUpload from '../components/ImageUpload.ts.vue';
import WTELoyaltyPreview from './subpages/WTELoyaltyPreview.ts.vue';
import { customerIOTrackEvent } from '../services/customerio.js';
import PreviewLoyaltyProgram from '../components/PreviewLoyalty.ts.vue';
import { isFeatureAvailable } from '../services/features.js';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	data() {
		return {
			referralOptions: [{ label: 'Percent Discount', value: 'percent-discount' }, { label: 'Fixed Amount Off', value: 'dollars-off-coupon' }, { label: 'Points', value: 'points'}],
			referralReward: "dollars-off-coupon",
			friendReward: "dollars-off-coupon",
			referralsActive: false,
			referralRewardValue: 10,
			friendRewardValue: 10,
			friendRewardMinimumOrderTotal: 10,
			referralRewardName: '',
			referralRewardDescription: '',
			referralRewardMinimumOrderTotal: 10,
			referralImageURL: 'https://raleoncdn.s3.us-east-1.amazonaws.com/WTERewardDefault.jpg',
			friendRewardName: '',
			friendRewardDescription: '',
			friendRewardImageURL: 'https://raleoncdn.s3.us-east-1.amazonaws.com/WTERewardDefault.jpg',
			loading: false,
			hasUnsavedChanges: false,
			earn: {},
			programId: 0,
			status: { 'message': null, type: 'nope' },
			currencyPrefix: '$',
			isSaving: false
		}
	},
	components: {
		PrimaryButton,
		LightSecondaryButton,
		CancelButton,
		ToggleItem,
		StatusMessage,
		CardContainer,
		Tooltip,
		ImageUpload,
		WTELoyaltyPreview,
		SuperDropdown,
		PreviewLoyaltyProgram
	},
	async mounted() {
		this.loading = true;
		customerIOTrackEvent('Referral Viewed');
		await this.loadReferralProgram();
		this.activateWatchers();
		this.loading = false;
	},
	watch: {},
	created() {
		window.addEventListener('beforeunload', this.beforeUnloadHandler);
	},
	beforeRouteLeave(to, from, next) {
		if (this.hasUnsavedChanges && !confirm('You have unsaved changes. Are you sure you want to leave?')) {
			next(false);
		} else {
			window.removeEventListener('beforeunload', this.beforeUnloadHandler);
			this.hasUnsavedChanges = false;
			next();
		}
	},
	beforeDestroy() {
		window.removeEventListener('beforeunload', this.beforeUnloadHandler);
	},
	referralsActive(newValue) {
		if (newValue) {
			customerIOTrackEvent('Referral Program Enabled');
		}
		else {
			customerIOTrackEvent('Referral Program Disabled');
		}
	},
	computed: {
		isFeatureAvailable() {
			return isFeatureAvailable('referrals');
		},
		isTrialEnded() {
			const freeTrialData = JSON.parse(localStorage.getItem('freeTrialData'));

			return freeTrialData != {} && !freeTrialData.hasPaidPlan && freeTrialData.daysLeft < 0
		},
		dynamicReferralLabel() {
			if(this.referralReward == 'dollars-off-coupon') {
				return this.currencyPrefix;
			} else if (this.referralReward == 'points') {
				return '';
			} else return '%';
		},
		dynamicFriendLabel() {
			if(this.friendReward == 'dollars-off-coupon') {
				return this.currencyPrefix;
			} else if (this.friendReward == 'points') {
				return '';
			} else return '%';
		},
		disableSave() {
			return (
				this.isSaving || !this.referralReward || !this.friendReward ||
				!this.referralRewardValue || !this.friendRewardValue ||
				!this.referralRewardName || !this.referralRewardDescription ||
				!this.referralImageURL || !this.friendRewardName ||
				!this.friendRewardDescription || !this.friendRewardImageURL
			);
		},
		disableEnable() {
			const hasDefaultImage = this.referralImageURL.endsWith('WTERewardDefault.jpg') || this.friendRewardImageURL.endsWith('WTERewardDefault.jpg');
			return (
				this.isSaving || !this.referralReward || !this.friendReward ||
				!this.referralRewardValue || !this.friendRewardValue ||
				!this.referralRewardName || !this.referralRewardDescription ||
				!this.referralImageURL || !this.friendRewardName ||
				!this.friendRewardDescription || !this.friendRewardImageURL ||
				hasDefaultImage
			);
		}
	},
	methods: {
		setReferralReward(value) {
			this.referralReward = value;
		},
		setFriendReward(value) {
			this.friendReward = value;
		},
		navigateToBranding() {
			this.$router.push('/loyalty/referral-program');
		},
		activateWatchers() {
			const propertiesToWatch = [
				'referralReward', 'friendReward',
				'referralRewardValue', 'friendRewardValue',
				'referralRewardName', 'referralRewardDescription',
				'referralImageURL', 'friendRewardName',
				'friendRewardDescription', 'friendRewardImageURL', 'referralsActive'
			];

			propertiesToWatch.forEach(property => {
				this.$watch(property, async () => {
					await this.transformDataForPreview();
					this.hasUnsavedChanges = true;
				});
			});
		},
		async isReferralProgramActive() {
			const response = await fetch(`${URL_DOMAIN}/loyalty-programs`, {
				method: 'GET',
				withCreditentials: true,
				credentials: 'omit',
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Access-Control-Allow-Origin': '*',
					'Content-Type': 'application/json'
				}
			});
			let jsonresponse = await response.json();

			this.referralsActive = jsonresponse[0].referralsActive;
			this.programId = jsonresponse[0].id;
			if(this.referralsActive == null) {
				this.referralsActive = false;
			}
			return this.referralsActive;
		},
		async loadReferralProgram() {
			this.loading = true;

			try {
				const response = await fetch(`${URL_DOMAIN}/referral-program`, {
					method: 'GET',
					withCreditentials: true,
					credentials: 'omit',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Access-Control-Allow-Origin': '*',
						'Content-Type': 'application/json'
					}
				});
				const { referrerBonus, referredCustomerBonus, active, currencyPrefix } = await response.json();
				this.currencyPrefix = currencyPrefix;
				this.referralReward = referrerBonus.type;
				this.friendReward = referredCustomerBonus.type;
				this.referralRewardName = referrerBonus.name;
				this.referralRewardDescription = referrerBonus.description;
				this.referralRewardValue = referrerBonus.amount;
				this.referralRewardMinimumOrderTotal = referrerBonus.minimumOrderTotal;
				this.referralImageURL = referrerBonus.imageURL;
				this.friendRewardName = referredCustomerBonus.name;
				this.friendRewardDescription = referredCustomerBonus.description;
				this.friendRewardValue = referredCustomerBonus.amount;
				this.friendRewardImageURL = referredCustomerBonus.imageURL;
				this.friendRewardMinimumOrderTotal = referredCustomerBonus.minimumOrderTotal;
				this.referralsActive = active;

				await this.getEarnForPreview();

			} catch (e) {
				console.error(e);
			} finally {
				this.loading = false;
			}
		},
		async save(hideSuccess = false) {
			this.isSaving = true;
			const response = await fetch(`${URL_DOMAIN}/referral-program`, {
				method: 'PUT',
				withCreditentials: true,
				credentials: 'omit',
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Access-Control-Allow-Origin': '*',
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					referrerBonus: {
						type: this.referralReward,
						amount: this.referralRewardValue,
						name: this.referralRewardName,
						description: this.referralRewardDescription,
						imageURL: this.referralImageURL,
						minimumOrderTotal: this.referralRewardMinimumOrderTotal
					},
					referredCustomerBonus: {
						type: this.friendReward,
						amount: this.friendRewardValue,
						name: this.friendRewardName,
						description: this.friendRewardDescription,
						imageURL: this.friendRewardImageURL,
						minimumOrderTotal: this.friendRewardMinimumOrderTotal,
					},
					active: this.referralsActive
				})
			});

			this.isSaving = false;
			if (response.ok && response.status > 200 && response.status < 300) {
				if (!hideSuccess) {
					this.status.type = 'success';
					this.status.message = 'Referral program saved successfully.'
				}
				this.hasUnsavedChanges = false;
			} else {
				this.status.type = 'fail';
				this.status.message = 'Referral program could not be saved. Please check that all information is provided.'
				this.hasUnsavedChanges = true;
			}

			if (this.earn?.earnConditions && this.earn.earnConditions.length) {
				await this.getEarnForPreview();
			}
		},
		async getEarnForPreview() {
			const previewResponse = await fetch(`${URL_DOMAIN}/referral-program/preview`, {
				method: 'GET',
				withCreditentials: true,
				credentials: 'omit',
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Access-Control-Allow-Origin': '*',
					'Content-Type': 'application/json'
				}
			});
			const foundationalCampaign = await previewResponse.json();
			this.earn = foundationalCampaign.loyaltyEarns?.[0];
		},
		async transformDataForPreview() {
			const response = await fetch(`${URL_DOMAIN}/referral-program/preview`, {
				method: 'POST',
				withCreditentials: true,
				credentials: 'omit',
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Access-Control-Allow-Origin': '*',
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					referrerBonus: {
						type: this.referralReward,
						amount: this.referralRewardValue,
						name: this.referralRewardName,
						description: this.referralRewardDescription,
						imageURL: this.referralImageURL,
						minimumOrderTotal: this.referralRewardMinimumOrderTotal
					},
					referredCustomerBonus: {
						type: this.friendReward,
						amount: this.friendRewardValue,
						name: this.friendRewardName,
						description: this.friendRewardDescription,
						imageURL: this.friendRewardImageURL,
						minimumOrderTotal: this.friendRewardMinimumOrderTotal,
					},
				})
			});
			const foundationalCampaign = await response.json();
			this.earn = foundationalCampaign.loyaltyEarns?.[0];
		},
		beforeUnloadHandler(event) {
			if (this.hasUnsavedChanges) {
				event.preventDefault();
				event.returnValue = '';
				return '';
			}
		},
	},
}
</script>
<style scoped>

.reward-image-wrapper {
	width: 100%;
	max-width: 150px;
}

.reward-image-container {
	position: relative;
	width: 100%;
	padding-top: 100%;
}

.reward-image-container img {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.campaign-name {
	font-family: 'Inter', sans-serif;
	font-style: normal;
	font-weight: 500;
	font-size: 40px;
	line-height: 65px;
	letter-spacing: -0.04em;
	text-transform: uppercase;
	color: #5E48F8;
}

.no-focus-outline {
	box-shadow: none !important;
	outline: none !important;
}

.toggle-checkbox {
	opacity: 0;
	width: 0;
	height: 0;
}

.toggle-label {
	display: block;
	overflow: hidden;
	cursor: pointer;
	height: 24px;
	/* Height of the toggle */
	padding: 0;
	line-height: 24px;
	background: #bbb;
	/* Background of the toggle */
	border-radius: 24px;
	transition: background-color 0.2s;
}

.toggle-label:after {
	content: "";
	position: absolute;
	top: 2px;
	left: 2px;
	width: 20px;
	/* Width of the toggle handle */
	height: 20px;
	/* Height of the toggle handle */
	border-radius: 20px;
	background: #fff;
	/* Background of the toggle handle */
	transition: 0.2s;
}

.toggle-checkbox:checked+.toggle-label {
	background-color: #68d391;
}

.toggle-checkbox:checked+.toggle-label:after {
	transform: translateX(18px);
}

.toggle-checkbox:checked {
	right: 0;
}

.toggle-checkbox:checked+.toggle-label {
	background-color: #68d391;
}

.toggle-checkbox {
	right: 4px;
}

.toggle-label {
	transition: background-color 0.2s;
}

.spinner {
  border: 6px solid #f3f3f3;
  border-top: 6px solid #15803D;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.w-full.sticky {
	position: sticky;
	top: 0;
	height: calc(100vh - 140px);
}

</style>
