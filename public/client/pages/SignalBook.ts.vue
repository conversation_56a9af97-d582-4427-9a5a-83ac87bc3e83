<template>
	<div class="flex flex-col p-6 bg-white shadow-sm sm:flex-row items-center justify-between">

		<div>
			<div class="text-3xl sm:text-3xl font-sans font-medium opacity-70 text-ralblack-primary">
				Signal Library
			</div>
			<p class="mt-1 text-gray-500">Manage and configure AI-powered behavioral signals</p>
		</div>
	</div>

	<div class="min-h-screen bg-[#F5F5F5] p-6">

    <div>

      <!-- Custom Signal Upsell -->
      <div class="bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-100 rounded-lg p-4 mb-6 shadow-sm">
        <div class="flex justify-between items-center">
          <div class="flex items-center gap-3">
            <div class="bg-purple-100 p-2 rounded-lg shadow-sm">
				<svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="#9333ea" class="size-6">
					<path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z" />
				</svg>
            </div>
            <div>
              <h3 class="font-medium text-gray-900">Create Custom Signals</h3>
              <p class="text-sm text-gray-600">Unlock the ability to create your own custom behavioral signals</p>
            </div>
          </div>
          <PrimaryButton cta="Request Custom Signal" size="xs" @click="requestCustomSignal" />
        </div>
      </div>

      <!-- Filters -->
	  <div class="mb-2 max-w-2xl">
			<div class="relative group">
				<div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none text-gray-400">
				<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
					<circle cx="11" cy="11" r="8"></circle>
					<line x1="21" y1="21" x2="16.65" y2="16.65"></line>
				</svg>
				</div>
				<input
				type="text"
				v-model="searchQuery"
				placeholder="Search signals by name or description..."
				class="w-full pl-12 pr-4 py-3 text-base rounded-xl border-0 bg-white shadow-sm ring-1 ring-gray-200 transition-shadow duration-200 placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 hover:ring-gray-300"
				/>
			</div>
		</div>
    </div>

    <!-- Table -->
    <div class="bg-white rounded-2xl border shadow-sm border-ralprimary-light border-opacity-50 mt-4 flex-grow transition-all duration-300 ease-in-out overflow-hidden">
      <table class="w-full">
        <thead>
          <tr class="border-b border-gray-200 bg-gray-50">
            <th class="text-left py-3 px-4 text-sm font-medium text-gray-600 w-[250px]">Signal Name</th>
            <th class="text-left py-3 px-4 text-sm font-medium text-gray-600 w-[400px]">Description</th>
            <th class="text-left py-3 px-4 text-sm font-medium text-gray-600">Type</th>
            <!--<th class="text-center py-3 px-4 text-sm font-medium text-gray-600">Status</th> -->
			<!--
            <th class="text-right py-3 px-4 text-sm font-medium text-gray-600 w-[100px]">
              <span class="invisible">Edit</span>
            </th>
			-->
          </tr>
        </thead>
        <tbody>
			<tr
			v-for="signal in filteredSignals"
			:key="signal.id"
			class="border-b border-gray-200 group hover:bg-gray-50 h-[80px]"
			>
            <td class="py-3 px-4 font-medium">{{ signal.name }}</td>
            <td class="py-3 px-4 text-gray-500">{{ signal.description }}</td>
            <td class="py-3 px-4">
              <span
                :class="[
                  'items-center px-4 py-1.5 border rounded-full text-sm font-medium cursor-pointer mb-2 mr-2 inline-flex whitespace-nowrap transition-all duration-200',
                  getTypeColor(signal.type)
                ]"
              >
                {{ getTypeName(signal.type) }}
              </span>
            </td>
			<!--
            <td class="py-3 px-4 text-center">
              <ToggleItem
                v-model="signal.active"
                class="shadow-sm"
              />
            </td>
			-->
			<!--
            <td class="py-3 px-4 text-center">
              <div class="w-[40px] inline-block">
                <button
                  class="hidden group-hover:inline-flex items-center px-3 py-1 rounded-md hover:bg-gray-100 text-gray-600"
                  @click="editSignal(signal)"
                >
                  Edit
                </button>
              </div>
            </td>
			-->
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  </template>

  <script>
  import { ref, computed } from 'vue';
  import * as Utils from '../../client-old/utils/Utils';
  import PrimaryButton from '../components/PrimaryButton.ts.vue';
  import ToggleItem from '../components/ToggleItem.ts.vue';
import { Crisp } from 'crisp-sdk-web';
  const URL_DOMAIN = Utils.URL_DOMAIN;

  export default {
  name: 'SignalLibrary',
  components: {
    PrimaryButton,
    ToggleItem
  },

  data() {
    return {
      searchQuery: '',
      signals: [
        {
          id: 1,
          name: "At risk to churn",
          description: "AI-predicted likelihood of customer churning in next 30 days",
          active: true,
          category: "Retention"
        },
        {
          id: 2,
          name: "Replenishment desire",
          description: "Customer likely needs to repurchase based on buying patterns",
          active: true,
          category: "Purchase Behavior"
        },
        {
          id: 3,
          name: "Subscription upsell",
          description: "Good candidate for subscription tier upgrade",
          active: true,
          category: "Subscription"
        }
      ]
    }
  },

  computed: {
	filteredSignals() {
		if (!this.searchQuery) return this.signals;

		const query = this.searchQuery.toLowerCase().trim();
		return this.signals.filter(signal =>
			signal.name.toLowerCase().includes(query) ||
			(signal.description && signal.description.toLowerCase().includes(query))
		);
	}
  },

  methods: {
	async fetchSignals() {
			try {
				this.isLoading = true;
				const response = await fetch(`${URL_DOMAIN}/signals`, {
					method: 'GET',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`, // Adjust if using token-based auth
						'Content-Type': 'application/json',
					},
				});

				if (response.ok) {
					const data = await response.json();
					console.log("Signals:", data);
					return data;
				} else {
					console.error("Failed to fetch signals:", response.statusText);
				}
			} catch (error) {
				console.error("Error fetching signals:", error);
			} finally {
				this.isLoading = false;
			}
		},
    getTypeColor(type) {
      const colors = {
        'raleon': 'bg-purple-100 text-purple-800',
        'rule': 'bg-red-100 text-red-800',
        'model': 'bg-blue-100 text-blue-800'
      }


      return colors[type] || 'bg-gray-100 text-gray-800'
    },
	getTypeName(type) {
    	if(type == 'raleon')
			return "Raleon AI";
		else if(type == 'model')
			return "Raleon Insight";
		else if(type == 'rules')
			return "Basic Rules";
		else
			return "Basic Rules";
    },

    editSignal(signal) {
      console.log('Edit signal:', signal)
    },

    requestCustomSignal() {
	  Crisp.session.pushEvent("showNewMessage", {
		message: "I would like to request a custom signal"
	  })
	  Crisp.chat.open();
    }
  },
  async mounted() {
	this.signals = await this.fetchSignals();
	},
}

</script>
