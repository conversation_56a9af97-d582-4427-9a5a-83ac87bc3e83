<template>

	<ProgramActive></ProgramActive>

	<div class="m-3 sm:m-7">
		<h1 class="text-3xl sm:text-6xl mb-16 font-sans font-medium opacity-70 text-ralblack-primary">
			Loyalty Overview
		</h1>

		<h2 class="text-2xl sm:text-5xl mt-10 mb-4 font-sans font-medium opacity-70 text-ralblack-primary">KPI</h2>

		<div class="bg-white rounded-2xl border shadow border-ralprimary-light border-opacity-50 p-4" v-if="isLoyaltyHighlightsLoading">
			<div class="flex flex-col items-center justify-center p-4">
				<svg width="32" height="42" viewBox="0 0 32 42" fill="none" xmlns="http://www.w3.org/2000/svg" class="md:w-9 md:h-10 sm:w-6 sm:h-7">
					<path fill-rule="evenodd" clip-rule="evenodd" d="M16 0C17.2624 0 18.2857 1.02335 18.2857 2.28571V38.8571C18.2857 40.1195 17.2624 41.1429 16 41.1429C14.7376 41.1429 13.7143 40.1195 13.7143 38.8571V2.28571C13.7143 1.02335 14.7376 0 16 0ZM29.7143 13.7143C30.9767 13.7143 32 14.7376 32 16V38.8571C32 40.1195 30.9767 41.1429 29.7143 41.1429C28.4519 41.1429 27.4286 40.1195 27.4286 38.8571V16C27.4286 14.7376 28.4519 13.7143 29.7143 13.7143ZM2.28571 22.8571C3.54808 22.8571 4.57143 23.8805 4.57143 25.1429V38.8571C4.57143 40.1195 3.54808 41.1429 2.28571 41.1429C1.02335 41.1429 0 40.1195 0 38.8571V25.1429C0 23.8805 1.02335 22.8571 2.28571 22.8571Z" fill="#E95D83"/>
				</svg>
				<p class="text-ralprimary-dark font-bold md:text-xl lg:text-2xl sm:text-normal mt-4 font-[Inter]">Your key metrics will show up within 24 hours.</p>
				<p class="text-ralblack-primary text-lg mt-4 flex items-center font-[Inter]">Key metrics help you understand how your program is performing. <a href="https://docs.raleon.io/docs/program-return-on-investment" class="flex items-center ml-1 text-ralprimary-main hover:text-ralprimary-highlighted" target="_blank">Learn more
					<svg width="18" height="18" viewBox="0 0 18 18" fill="none" class="ml-1" xmlns="http://www.w3.org/2000/svg">
						<path fill-rule="evenodd" clip-rule="evenodd" d="M3.75 5.25C3.55109 5.25 3.36032 5.32902 3.21967 5.46967C3.07902 5.61032 3 5.80109 3 6V14.25C3 14.4489 3.07902 14.6397 3.21967 14.7803C3.36032 14.921 3.55109 15 3.75 15H12C12.1989 15 12.3897 14.921 12.5303 14.7803C12.671 14.6397 12.75 14.4489 12.75 14.25V9.75C12.75 9.33579 13.0858 9 13.5 9C13.9142 9 14.25 9.33579 14.25 9.75V14.25C14.25 14.8467 14.0129 15.419 13.591 15.841C13.169 16.2629 12.5967 16.5 12 16.5H3.75C3.15326 16.5 2.58097 16.2629 2.15901 15.841C1.73705 15.419 1.5 14.8467 1.5 14.25V6C1.5 5.40326 1.73705 4.83097 2.15901 4.40901C2.58097 3.98705 3.15326 3.75 3.75 3.75H8.25C8.66421 3.75 9 4.08579 9 4.5C9 4.91421 8.66421 5.25 8.25 5.25H3.75Z" fill="#5A16C9" fill-opacity="0.8"/>
						<path fill-rule="evenodd" clip-rule="evenodd" d="M10.5 2.25C10.5 1.83579 10.8358 1.5 11.25 1.5H15.75C16.1642 1.5 16.5 1.83579 16.5 2.25V6.75C16.5 7.16421 16.1642 7.5 15.75 7.5C15.3358 7.5 15 7.16421 15 6.75V3H11.25C10.8358 3 10.5 2.66421 10.5 2.25Z" fill="#5A16C9" fill-opacity="0.8"/>
						<path fill-rule="evenodd" clip-rule="evenodd" d="M16.2803 1.71967C16.5732 2.01256 16.5732 2.48744 16.2803 2.78033L8.03033 11.0303C7.73744 11.3232 7.26256 11.3232 6.96967 11.0303C6.67678 10.7374 6.67678 10.2626 6.96967 9.96967L15.2197 1.71967C15.5126 1.42678 15.9874 1.42678 16.2803 1.71967Z" fill="#5A16C9" fill-opacity="0.8"/>
					</svg>
				</a>
				</p>
			</div>
		</div>

		<div class="flex flex-wrap" v-if="!isLoyaltyHighlightsLoading">
			<div class="test">
				<NumberChart title="Revenue from Loyalty" :number="revenueFromLoyalty" change="" tip="The Revenue from Loyalty Members since activating the loyalty program." />
			</div>
			<div class="test">
				<NumberChart title="Outstanding Point Balance" :number="loyaltyPointsBalance" change="" tip="The amount of points given to customers for purchasing."/>
			</div>
			<div class="test">
				<NumberChart title="Loyalty Adoption Rate" :number="loyaltyAdoptionRate" change="" tip="The percentage of customers that are partcipating in the loyalty program since activation." />
			</div>
			<div class="test">
				<NumberChart title="Active Members" :number="activeMembers" change="" tip="The number of customers participating in the loyalty program who have purchased in the last 30 days." />
			</div>
		</div>

		<div class="mt-10 py-5">
			<h2 class="text-2xl sm:text-5xl mt-10 mb-10 font-sans font-medium opacity-70 text-ralblack-primary">Member
				Insights</h2>
			<div class="relative w-full empty-campaign justify-center items-center">
				<Charts/>
			</div>
		</div>

		<!-- <div class="mt-10 py-5">

			<div class="flex justify-between items-center mb-4">

				<h2 class="text-2xl sm:text-5xl mb-10 font-sans font-medium opacity-70 text-ralblack-primary">Campaigns</h2>

				<PrimaryButton cta="add" icon="true" @click="addCampaign();"></PrimaryButton>
			</div>

			<Campaigns :campaignTable="true"></Campaigns>
		</div> -->

	</div>
</template>

<script>
import NumberChart from '../components/NumberChart.ts.vue';
import PrimaryButton from '../components/PrimaryButton.ts.vue';
import LightSecondaryButton from '../components/LightSecondaryButton.ts.vue';
import ProgramActive from '../components/ProgramActive.ts.vue';
import ToggleItem from '../components/ToggleItem.ts.vue';
import Charts from './Charts.ts.vue';
import Campaigns from './Campaigns.ts.vue';
import ImageUpload from '../components/ImageUpload.ts.vue';
import * as Utils from '../../client-old/utils/Utils';
import { getMetric } from '../services/metrics.js';
import * as CurrencyUtils from '../services/currency.js';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	components: {
		NumberChart,
		Campaigns,
		PrimaryButton,
		LightSecondaryButton,
		ProgramActive,
		ToggleItem,
		Charts,
		ImageUpload
	},
	data() {
		return {
			loyaltyPointsBalance: "--",
			revenueFromLoyalty: "--",
			loyaltyAdoptionRate: "--",
			activeMembers: "--",
			testRes: '',
			onboardingTasks: [],
			onboardingTaskInterval: null,
			isFetchingOnboardingTasks: false,
		};
	},
	created() {
		this.getMetrics();
		this.setPlanDetails();
	},
	async mounted() {
		this.onboardingTaskInterval = setInterval(async () => {
			if (!this.onboardingTasks.length || this.isOnboarding) {
				if (this.isFetchingOnboardingTasks) return;
				this.isFetchingOnboardingTasks = true;
				try { await this.fetchOnboardingStepStatus() } catch (e) { console.error(e) }
				this.isFetchingOnboardingTasks = false;
			} else {
				clearInterval(this.onboardingTaskInterval);
			}
		}, 3000);
	},
	computed: {
		isOnboarding() {
			return this.onboardingTasks.some(x => !x.state || !x.state?.state?.includes('erified'))
		},
		isLoyaltyHighlightsLoading() {
			if(this.revenueFromLoyalty !== '--' && this.revenueFromLoyalty !== '--' && this.loyaltyAdoptionRate !== '--' && this.activeMembers !== '--')
				return false;
			else
				return true;
		},
		userName() {
			if(localStorage.getItem('firstName') != '' && localStorage.getItem('firstName') != null)
				return localStorage.getItem('firstName');
			else
				return "Welcome new Raleon user";
		}
	},
	methods: {
		async fetchOnboardingStepStatus() {
			let url = `/onboarding-tasks/states`;
			const response = await fetch(`${URL_DOMAIN}${url}`, {
				method: 'GET',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
			});

			this.onboardingTasks = await response.json();
			this.onboardingTasks = this.onboardingTasks
				.sort((a,b) => a.priority - b.priority)
				.filter(x => !x.type.includes('nsights'));

		},
		navigate(path) {
			this.$router.push(path);
		},
		async addCampaign() {
			const programId = await this.createLoyaltyProgram();
			const campaignId = await this.createDefaultLoyaltyCampaign(programId);

			//This isn't ideal but I cannot figure out how to make router in SPA not start at the top of the page
			//tried scroll behaviour and scroll to top but neither worked
			//this.$router.push(`/campaign/${campaignId}`);
			window.location.href = `/campaign/${campaignId}`;
		},
		async createDefaultLoyaltyCampaign(programId) {
			var payload = {
				name: "Untitled Campaign",
				evergreen: true,
				loyaltySegment: "Everyone",
				active: false
			}
			let url = `/loyalty-programs/${programId}/loyalty-campaigns`;
			const response = await fetch(`${URL_DOMAIN}${url}`, {
				method: 'POST',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify(payload),
			});

			let data = await response.json();
			console.log(data);
			return data.id;
		},
		async createLoyaltyProgram() {
			let jsonresponse = {};
			try {
				const response = await fetch(`${URL_DOMAIN}/loyalty-programs`, {
					method: 'GET',
					withCreditentials: true,
					credentials: 'omit',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Access-Control-Allow-Origin': '*',
						'Content-Type': 'application/json'
					}
				});
				jsonresponse = await response.json();
				console.log("jsonresponse: " + JSON.stringify(jsonresponse));
			} catch (err) {
				console.log("Error: " + JSON.stringify(err));
			}

			if (jsonresponse.length > 0) {
				return jsonresponse[0].id;
			} else {
				var payload = {
					name: "Default Program",
					active: false
				}

				let url = `/loyalty-programs`;
				const response = await fetch(`${URL_DOMAIN}${url}`, {
					method: 'POST',
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					},
					body: JSON.stringify(payload),
				});

				let data = await response.json();

				var payloadPoints = {
					name: "Points",
					conversionToUSD: 100
				}
				let urlPoints = `/loyalty-programs/${data.id}/loyalty-currencies`;
				const responsePoints = await fetch(`${URL_DOMAIN}${urlPoints}`, {
					method: 'POST',
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					},
					body: JSON.stringify(payloadPoints),
				});

				let dataPoints = await responsePoints.json();
				console.log('Currency', dataPoints);

				console.log(data);
				return data.id;
			}
		},
		async getMetrics() {
			await Promise.all([
				this.fetchMetricAndUpdate('loyalty_revenue', 'revenueFromLoyalty', 'member_revenue'),
				this.fetchMetricAndUpdate('loyalty_points_balance', 'loyaltyPointsBalance', 'totalpoints'),
				this.fetchMetricAndUpdate('loyalty_adoption_rate', 'loyaltyAdoptionRate', 'adoption_rate'),
				this.fetchMetricAndUpdate('active_members_month', 'activeMembers', 'active_members'),
			]);
		},

		async setPlanDetails() {
			const response = await fetch(`${URL_DOMAIN}/organizations/external-plan-details`, {
				method: 'POST',
				withCreditentials: true,
				credentials: 'omit',
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Access-Control-Allow-Origin': '*',
					'Content-Type': 'application/json'
				}
			});
		},
		async fetchMetricAndUpdate(metricName, dataProperty, metricKey) {

			let data = await getMetric(metricName, 'latest', '', 'day', 'sum');
			if(data == "Metrics still loading...") return console.log('No data for metric', metricName);
			if (data.body.data.length > 0) {
				const lastDataItem = data.body.data[0];
				console.log('lastDataItem', lastDataItem);
				console.log('lastDataItem.metrics', lastDataItem.metrics);
				if (lastDataItem.metrics && lastDataItem.metrics[metricKey]) {
					const prefix = lastDataItem.metrics[metricKey].prefix || '';
					const value = lastDataItem.metrics[metricKey].value || 0;
					const suffix = lastDataItem.metrics[metricKey].suffix || '';

					this[dataProperty] = await CurrencyUtils.replaceDollarWithCurrency(prefix + Number(value).toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 }) + suffix);
				} else {
					console.error('No metrics found in the last data item');
				}
			} else {
				console.error('No data returned for metric:', metricName);
			}

		},
	}
}
</script>
<style scoped>.test-container {
	margin: 0.5em;
}

.test {
	width: 50%;
	padding: 0em 0.4em;
}

@media all and (max-width: 768px) {
	.test {
		width: 100%;
	}
}
</style>

