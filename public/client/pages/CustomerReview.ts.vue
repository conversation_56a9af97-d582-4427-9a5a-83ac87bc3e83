<template>
	<div class="p-2 sm:p-7">
		<h1 class="text-3xl sm:text-6xl font-sans font-medium opacity-70 text-ralblack-primary">Review Receipts</h1>

		<div class="flex flex-col px-4 py-2 mt-12">
			<div class="flex">
			<div class="flex-1">Customer</div>
			<div class="flex-1">Submitted On</div>
			<div class="flex-1">Points Earned</div>
			<div class="flex-1">Score</div>
			<div class="flex-1">Status</div>
			</div>
  		</div>

		<div
			class="bg-white bg-opacity-75 rounded-2xl shadow border border-ralprimary-light border-opacity-50 hover:border-opacity-90 hover:shadow-lg flex-grow transition-all duration-300 ease-in-out overflow-hidden"
			:class="{'border-opacity-90': isOpen}" :style="{
				height: isOpen ? '34em' : '3em'
			}">
			<div class="flex px-4 py-2 cursor-pointer" @click="isOpen = !isOpen">
				<div class="text-zinc-900 text-base font-normal font-['Open Sans'] leading-loose flex-grow truncate">

					<div class="flex flex-col">
						<!-- Table Header -->
						<div class="flex">
						<div class="flex-1">Nathan Snell</div>
						<div class="flex-1">4/29/2024</div>
						<div class="flex-1">231</div>
						<div class="flex-1">

							<span class="inline-flex items-center px-3 py-0.5 rounded-full text-sm font-medium bg-green-100 text-green-800">
								<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24"
								fill="currentColor"
								class="mr-1"
								><path d="m354-287 126-76 126 77-33-144 111-96-146-13-58-136-58 135-146 13 111 97-33 143Zm126 18L314-169q-11 7-23 6t-21-8q-9-7-14-17.5t-2-23.5l44-189-147-127q-10-9-12.5-20.5T140-571q4-11 12-18t22-9l194-17 75-178q5-12 15.5-18t21.5-6q11 0 21.5 6t15.5 18l75 178 194 17q14 2 22 9t12 18q4 11 1.5 22.5T809-528L662-401l44 189q3 13-2 23.5T690-171q-9 7-21 8t-23-6L480-269Zm0-201Z"/></svg>
								87
							</span>


						</div>
						<div class="flex-1">
							<span class="inline-flex items-center bg-green-700 text-white px-3 py-1 text-xs font-bold rounded-full">
								<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24" fill="#FFFFFF"
								class="mr-2"
								><path d="m424-408-86-86q-11-11-28-11t-28 11q-11 11-11 28t11 28l114 114q12 12 28 12t28-12l226-226q11-11 11-28t-11-28q-11-11-28-11t-28 11L424-408Zm56 328q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-80q134 0 227-93t93-227q0-134-93-227t-227-93q-134 0-227 93t-93 227q0 134 93 227t227 93Zm0-320Z"/></svg>
							Auto-Approved
							</span>
						</div>
						</div>
					</div>

				</div>

			</div>
			<div class="transition-all p-4 justify-center" :class="isOpen ? 'opacity-100' : 'opacity-0'"
				:style="{height: isOpen ? (openHeight ? openHeight : '100%') : '0'}">


				<div class="flex flex-col items-center justify-center">
				<transition name="fade" mode="out-in">
					<div key="transitionWrapper">
						Receipt Details
						<div class="w-96 h-96 overflow-hidden bg-gray-200 flex justify-center items-center rounded-md">
							<img src="../images/ReceiptEg.jpg" class="h-full w-auto">
						</div>
					</div>
				</transition>

				<div class="mt-4">
					<LightSecondaryButton cta="Reject" />
				</div>
				</div>
			</div>
		</div>

		<!-- next item -->
		<div
			class="mt-2 bg-white bg-opacity-75 rounded-2xl shadow border border-ralprimary-light border-opacity-50 hover:border-opacity-90 hover:shadow-lg flex-grow transition-all duration-300 ease-in-out overflow-hidden"
			:class="{'border-opacity-90': isOpen2}" :style="{
				height: isOpen2 ? '34em' : '3em'
			}">
			<div class="flex px-4 py-2 cursor-pointer" @click="isOpen2 = !isOpen2">
				<div class="text-zinc-900 text-base font-normal font-['Open Sans'] leading-loose flex-grow truncate">

					<div class="flex flex-col">
						<!-- Table Header -->
						<div class="flex">
						<div class="flex-1">Adam Larson</div>
						<div class="flex-1">4/21/2024</div>
						<div class="flex-1">183</div>
						<div class="flex-1">

							<span class="inline-flex items-center px-3 py-0.5 rounded-full text-sm font-medium bg-yellow-400 text-slate-800">
								<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24"
								fill="#363636"
								class="mr-1"
								><path d="m354-287 126-76 126 77-33-144 111-96-146-13-58-136-58 135-146 13 111 97-33 143Zm126 18L314-169q-11 7-23 6t-21-8q-9-7-14-17.5t-2-23.5l44-189-147-127q-10-9-12.5-20.5T140-571q4-11 12-18t22-9l194-17 75-178q5-12 15.5-18t21.5-6q11 0 21.5 6t15.5 18l75 178 194 17q14 2 22 9t12 18q4 11 1.5 22.5T809-528L662-401l44 189q3 13-2 23.5T690-171q-9 7-21 8t-23-6L480-269Zm0-201Z"/></svg>
								67
							</span>


						</div>
						<div class="flex-1">
							<span class="inline-flex items-center bg-yellow-500 text-slate-800 px-3 py-1 text-xs font-bold rounded-full">
								<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24"
								fill="#363636"
								class="mr-2"
								><path d="M480-280q17 0 28.5-11.5T520-320q0-17-11.5-28.5T480-360q-17 0-28.5 11.5T440-320q0 17 11.5 28.5T480-280Zm0-160q17 0 28.5-11.5T520-480v-160q0-17-11.5-28.5T480-680q-17 0-28.5 11.5T440-640v160q0 17 11.5 28.5T480-440Zm0 360q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-80q134 0 227-93t93-227q0-134-93-227t-227-93q-134 0-227 93t-93 227q0 134 93 227t227 93Zm0-320Z"/></svg>
								Manual Review
							</span>
						</div>
						</div>
					</div>

				</div>

			</div>
			<div class="transition-all p-4 justify-center" :class="isOpen2 ? 'opacity-100' : 'opacity-0'"
				:style="{height: isOpen2 ? (openHeight ? openHeight : '100%') : '0'}">


				<div class="flex flex-col items-center justify-center">
				<transition name="fade" mode="out-in">
					<div key="transitionWrapper">
						Receipt Details
						<div class="w-96 h-96 overflow-hidden bg-gray-200 flex justify-center items-center rounded-md">
							<img src="../images/ReceiptEg.jpg" class="h-full w-auto">
						</div>
					</div>
				</transition>

				<div class="flex mt-4">
					<LightSecondaryButton cta="Reject" />
					<div class="ml-2">
						<PrimaryButton cta="Approve" size="xs" />
					</div>
				</div>
				</div>
			</div>
		</div>
	</div>

</template>

<script>

import * as Utils from '../../client-old/utils/Utils';
import CardContainer from '../components/CardContainer.ts.vue';
import LightSecondaryButton from '../components/LightSecondaryButton.ts.vue'
import PrimaryButton from '../components/PrimaryButton.ts.vue';
import ToggleItem from '../components/ToggleItem.ts.vue';
import StatusMessage from '../components/StatusMessage.ts.vue';
import { Crisp } from "crisp-sdk-web";
import { useRoute } from 'vue-router';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	components: {
		CardContainer,
		LightSecondaryButton,
		StatusMessage,
		ToggleItem,
		PrimaryButton
	},
	async mounted() {

	},
	data() {
		return {
			isOpen: false,
			isOpen2: false,
			openHeight: '10rem',
		}
	},
	methods: {
		openDocs(integration) {
			window.open(integration.docURL, '_blank');
		},
		openChat() {
			Crisp.chat.open();
		},
	},
}
</script>
