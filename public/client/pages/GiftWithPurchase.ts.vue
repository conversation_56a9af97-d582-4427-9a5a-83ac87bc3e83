<template>
	<StatusMessage :message=status.message :status=status.type @resetStatus="status.type = 'nope'"></StatusMessage>

	<div class="p-2 sm:p-7 mr-24 flex flex-col items-center justify-center" v-if="!isFeatureAvailable && !isTrialEnded">
		<img src="../images/UpgradeGWP.png" width="584">
		<a class="text-ralprimary-dark font-bold md:text-xl lg:text-2xl sm:text-normal mt-4 font-[Inter]"
			href="/loyalty/settings/plans">Unlock Gift With Purchase</a>
		<p class="text-ralblack-primary text-lg mt-4 flex items-center font-[Inter] mb-4 w-1/2 text-center">
			Gift With Purchase promotions help brands increase AOV and customer lifetime value.
		</p>
		<PrimaryButton cta="Upgrade to Gift With Purchase Plan" size="xs"
			@click="() => this.$router.push('/loyalty/settings/plans')" />
	</div>


	<div class="bg-gradient-to-tr from-[#4F3EAC] to-[#5E48F8] p-4 w-full justify-center shadow-lg flex items-center"
		v-if="!isFeatureAvailable && isTrialEnded">
		<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M9 14H11V9.8L12.6 11.4L14 10L10 6L6 10L7.4 11.4L9 9.8V14ZM10 20C8.61667 20 7.31667 19.7375 6.1 19.2125C4.88333 18.6875 3.825 17.975 2.925 17.075C2.025 16.175 1.3125 15.1167 0.7875 13.9C0.2625 12.6833 0 11.3833 0 10C0 8.61667 0.2625 7.31667 0.7875 6.1C1.3125 4.88333 2.025 3.825 2.925 2.925C3.825 2.025 4.88333 1.3125 6.1 0.7875C7.31667 0.2625 8.61667 0 10 0C11.3833 0 12.6833 0.2625 13.9 0.7875C15.1167 1.3125 16.175 2.025 17.075 2.925C17.975 3.825 18.6875 4.88333 19.2125 6.1C19.7375 7.31667 20 8.61667 20 10C20 11.3833 19.7375 12.6833 19.2125 13.9C18.6875 15.1167 17.975 16.175 17.075 17.075C16.175 17.975 15.1167 18.6875 13.9 19.2125C12.6833 19.7375 11.3833 20 10 20ZM10 18C12.2333 18 14.125 17.225 15.675 15.675C17.225 14.125 18 12.2333 18 10C18 7.76667 17.225 5.875 15.675 4.325C14.125 2.775 12.2333 2 10 2C7.76667 2 5.875 2.775 4.325 4.325C2.775 5.875 2 7.76667 2 10C2 12.2333 2.775 14.125 4.325 15.675C5.875 17.225 7.76667 18 10 18Z"
				fill="#FFA3DF" />
		</svg>
		<div class="text-white ml-2">Your trial has expired and Gift with Purchase has been deactivated.</div>
		<div class="px-2 py-2 text-white text-sm border-[#FFF] border rounded-full ml-4 hover:text-black hover:bg-[#FFF] hover:cursor-pointer transitional-bg delay-200"
			@click="() => this.$router.push('/loyalty/settings/plans')">
			Choose a Plan
		</div>
	</div>

	<div class="p-2 sm:p-7 mr-24 flex flex-col items-center justify-center" v-if="isFeatureAvailable && !isShopifyConnected">
		<img src="https://cdn.shopify.com/shopifycloud/brochure/assets/brand-assets/shopify-logo-primary-logo-456baa801ee66a0a435671082365958316831c9960c480451dd0330bcdae304f.svg" width="584">
		<h2 class="text-ralprimary-dark font-bold md:text-xl lg:text-2xl sm:text-normal mt-4 font-[Inter]">Connect Shopify for Gift With Purchase</h2>
		<p class="text-ralblack-primary text-lg mt-4 flex items-center font-[Inter] mb-4 w-1/2 text-center">
			To set up gift with purchase promotions, connect your Shopify store to enable product synchronization and order processing.
		</p>
		<PrimaryButton
					cta="Connect Shopify"
					size="xs"
					@click="() => this.$router.push('/integrations')"
					/>
	</div>

	<div v-if="isFeatureAvailable && isShopifyConnected" class="m-3 sm:m-10 sm:m-7">
		<div class="flex w-full">
			<div class="flex flex-col flex-row md:flex-col items-start justify-between">
				<div class="flex justify-center items-center relative">
					<div class="text-3xl sm:text-6xl font-sans font-medium opacity-70 text-ralblack-primary">
						Gift With Purchase
					</div>
				</div>
				<div class="flex mb-2 ml-1">
					<LearnMoreText text="Learn more" url="https://docs.raleon.io/docs/gift-with-purchase">
					</LearnMoreText>
					<span class="text-neutral-500 text-sm font-medium">&nbsp;</span>
					<span class="text-neutral-800 text-opacity-80 text-sm font-medium">about setting up Gift With
						Purchase</span>
				</div>
			</div>
			<div class="flex mt-4 ml-auto">
				<ToggleItem v-if="showForceDiscountCode" @toggleChange="(active) => forceDiscountCode = active"
					:state="forceDiscountCode" :showLabel=true onLabel="Force Discount Code"
					offLabel="Force Discount Code" class="mr-5" @click.stop>
				</ToggleItem>
				<PrimaryButton v-if="isFeatureAvailable"
					:cta="!promotionalCampaign || !promotionalCampaign?.active ? 'Enable' : 'Disable'"
					@click="handleToggle()" :disabled="isSaving" size="small" class="mr-2 sm:mr-7"
					:showSpinner="isSaving">
				</PrimaryButton>
			</div>
		</div>
	</div>

	<div v-if="isFeatureAvailable && isShopifyConnected" class="m-3 sm:m-10 sm:m-7">
		<div class="flex flex-wrap" v-if="(promotionalCampaign && promotionalCampaign?.active) || giftsAdded != '--'">
			<div class="test">
				<NumberChart title="Revenue" :number="giftsRevenue" change="" :isLoading="isLoadingMetrics"
					tip="The Revenue from orders containing a free gift." />
			</div>
			<div class="test">
				<NumberChart title="Redeemed" :number="giftsRedeemed" change="" :isLoading="isLoadingMetrics"
					tip="The number of times a free gift was purchased." />
			</div>
			<div class="flex mb-2 ml-1">
				<span class="text-neutral-500 text-sm font-medium italic">Metrics update daily.</span>
			</div>
		</div>
		<!-- <div class="bg-ralinfo-light rounded-2xl p-4 mt-12 mb-8">
			<div class="inline-flex items-center mb-4">
				<svg xmlns="http://www.w3.org/2000/svg" height="25px" viewBox="0 -960 960 960" width="24px">
					<path
						d="M160-160v-360q-33 0-56.5-23.5T80-600v-80q0-33 23.5-56.5T160-760h128q-5-9-6.5-19t-1.5-21q0-50 35-85t85-35q23 0 43 8.5t37 23.5q17-16 37-24t43-8q50 0 85 35t35 85q0 11-2 20.5t-6 19.5h128q33 0 56.5 23.5T880-680v80q0 33-23.5 56.5T800-520v360q0 33-23.5 56.5T720-80H240q-33 0-56.5-23.5T160-160Zm400-680q-17 0-28.5 11.5T520-800q0 17 11.5 28.5T560-760q17 0 28.5-11.5T600-800q0-17-11.5-28.5T560-840Zm-200 40q0 17 11.5 28.5T400-760q17 0 28.5-11.5T440-800q0-17-11.5-28.5T400-840q-17 0-28.5 11.5T360-800ZM160-680v80h280v-80H160Zm280 520v-360H240v360h200Zm80 0h200v-360H520v360Zm280-440v-80H520v80h280Z" />
				</svg>
				<p class="text-ralblack-primary text-lg ml-2">Advanced Gift with Purchase</p>
			</div>
			<p class="text-ralblack-primary text-base">Sign up for first access to our advanced gift with purchase beta.
			</p>
			<div class="mt-4 flex justify-center">
				<PrimaryButton cta="Request Access" size="xs" @click="openIntercom" />
			</div>
		</div> -->
		<CardContainer class="mt-8">
			<div class="flex flex-col justify-center items-start justify-between">
				<label class="mb-2 text-sm font-semibold font-['Inter'] leading-normal text-gray-600">
					Gift With Purchase Mode
				</label>
				<div
					class="w-1/2 h-12 px-4 py-1 mb-5 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2">
					<div class="w-full mr-3 self-stretch justify-start items-center gap-2">
						<select
							class="form-input w-full border-none shadow-none text-sm font-semibold font-['Inter'] leading-normal disabled:opacity-50 disabled:cursor-not-allowed"
							:class="{'text-gray-400': !selectedMode, 'text-gray-600': selectedMode}"
							v-model="selectedMode" :disabled="promotionalCampaign && promotionalCampaign?.active">
							<option value="basic">Basic - Single Gift</option>
							<option value="choose_gift">Choose a Gift - Multiple Options</option>
						</select>
					</div>
				</div>

				<template v-if="selectedMode === 'basic'">
					<label for="gwp-product-select"
						class="mb-2 text-sm font-semibold font-['Inter'] leading-normal text-gray-600">
						Choose Free Gift
					</label>
				</template>
				<template v-else>
					<label for="gwp-product-select"
						class="mb-2 text-sm font-semibold font-['Inter'] leading-normal text-gray-600">
						Choose Gift Options (Up to 4)
					</label>
				</template>
				<template v-if="selectedMode === 'basic'">
						<div v-if="isLoadingProducts" class="w-1/2 h-12 px-4 py-1 mb-5 bg-gray-200 animate-pulse rounded-lg"></div>
						<div v-else class="w-1/2 h-12 px-4 py-1 mb-5 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2">
							<div class="w-full mr-3 self-stretch justify-start items-center gap-2">
								<select id="gwp-product-select"
									class="form-input w-full border-none shadow-none text-sm font-semibold font-['Inter'] leading-normal disabled:opacity-50 disabled:cursor-not-allowed"
									:class="{'text-gray-400': !selectedProduct, 'text-gray-600': selectedProduct}"
									v-model="selectedProduct"
									:disabled="promotionalCampaign && promotionalCampaign?.active">
									<option disabled value="">Select a product</option>
									<option v-for="product in products" :key="product.id" :value="product">
										{{ product.title }}
									</option>
								</select>
							</div>
						</div>
					</template>
					<template v-else>
						<div v-if="isLoadingProducts">
							<div v-for="i in 2" :key="i" class="mb-4">
								<div class="w-1/2 h-12 bg-gray-200 animate-pulse rounded-lg mb-2"></div>
								<div class="w-1/2 h-12 bg-gray-200 animate-pulse rounded-lg opacity-75"></div>
							</div>
						</div>
						<div v-else class="w-1/2 mb-5">
							<div v-for="(giftProduct, index) in selectedGiftProducts" :key="index" class="mb-4">
								<div class="flex items-center gap-4">
									<div class="flex-grow">
										<select
											class="w-full h-12 px-4 py-1 bg-white rounded-lg border border-gray-400 text-sm font-semibold font-['Inter'] leading-normal disabled:opacity-50 disabled:cursor-not-allowed"
											:class="{'text-gray-400': !giftProduct.product, 'text-gray-600': giftProduct.product}"
											v-model="giftProduct.product"
											:disabled="promotionalCampaign && promotionalCampaign?.active"
											@change="handleProductChange(index)">
											<option disabled value="">Select a product</option>
											<option v-for="product in availableProducts(index)" :key="product.id"
												:value="product">
												{{ product.title }}
											</option>
										</select>
									</div>
									<button @click="removeGiftProduct(index)" class="text-red-600 hover:text-red-800"
										:disabled="promotionalCampaign && promotionalCampaign?.active">
										<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none"
											viewBox="0 0 24 24" stroke="currentColor">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
												d="M6 18L18 6M6 6l12 12" />
										</svg>
									</button>
								</div>
								<div v-if="giftProduct.product && giftProduct.product.variants?.length > 1" class="mt-2">
									<select
										class="w-full h-12 px-4 py-1 bg-white rounded-lg border border-gray-400 text-sm font-semibold font-['Inter'] leading-normal disabled:opacity-50 disabled:cursor-not-allowed"
										:class="{'text-gray-400': !giftProduct.variant, 'text-gray-600': giftProduct.variant}"
										v-model="giftProduct.variant"
										:key="selectedVariant?.id"
										:disabled="promotionalCampaign && promotionalCampaign?.active">
										<option disabled value="">Choose a variant</option>
										<option value="any" v-if="giftProduct.product.variants.length > 1">ANY</option>
										<option v-for="variant in giftProduct.product.variants" :key="variant.id"
											:value="variant">
											{{ variant.title }}
										</option>
									</select>
								</div>
							</div>
							<button @click="addGiftProduct"
								class="mt-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
								:disabled="selectedGiftProducts.length >= 4 || (promotionalCampaign && promotionalCampaign?.active)">
								Add Gift Option
							</button>
						</div>
					</template>

				<label v-if="selectedProduct && selectedProduct.variants?.length > 1" for="gwp-product-select"
					class="mb-2 text-sm font-semibold font-['Inter'] leading-normal text-gray-600">
					Choose Free Gift Variant
				</label>
				<div v-if="selectedProduct && selectedProduct.variants?.length > 1"
					class="w-1/2 h-12 px-4 py-1 mb-5 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2">
					<div class="w-full mr-3 self-stretch justify-start items-center gap-2">
						<select id="gwp-product-variant-select"
							class="form-input w-full border-none shadow-none text-sm font-semibold font-['Inter'] leading-normal disabled:opacity-50 disabled:cursor-not-allowed"
							:class="{'text-gray-400': !selectedVariant, 'text-gray-600': selectedVariant}"
							v-model="selectedVariant" :disabled="promotionalCampaign && promotionalCampaign?.active"
							:key="selectedVariant?.id">
							<option disabled value="">Choose a variant</option>
							<option v-for="variant in selectedProduct.variants" :key="variant.id" :value="variant">
								{{ variant.title }}
							</option>
						</select>
					</div>
				</div>
			</div>
		</CardContainer>

		<CardContainer class="mt-8">
			<p class="text-xl font-bold mb-4">Optional Configuration</p>
			<label for="gwp-min-order-amount"
				class="mb-4 text-sm font-semibold font-['Inter'] leading-normal text-gray-600">
				Set Minimum Order Amount
			</label>
			<div
				class="w-1/2 h-12 px-4 py-1 mt-2 mb-5 bg-white rounded-lg border border-gray-400 justify-start items-center gap-2">
				<div class="w-full mr-3 self-stretch justify-start items-center gap-2 no-focus-outline">
					<input id="gwp-min-order-amount" v-model="minOrderAmount" type="number"
						placeholder="Minimum Order Amount (optional)"
						class="no-focus-outline border-none w-full text-gray-600 text-sm font-semibold font-['Inter'] leading-normal appearance-none focus:outline-none outline-none placeholder-gray-400 disabled:opacity-50 disabled:cursor-not-allowed"
						:disabled="promotionalCampaign && promotionalCampaign?.active" />
				</div>
			</div>

			<label v-if="!showForceDiscountCode || forceDiscountCode" for="gwp-min-order-amount"
				class="mb-4 text-sm font-semibold font-['Inter'] leading-normal text-gray-600">
				Disable coupon stacking with Free Gift
			</label>
			<div class="w-1/2 h-12 py-1 mt-2 mb-5 justify-start items-center gap-2"
				v-if="!showForceDiscountCode || forceDiscountCode">
				<input id="gwp-disable-coupon-stacking" v-model="disableCouponStacking" type="checkbox"
					class="appearance-none text-gray-600 leading-normal rounded-sm no-focus-outline focus:outline-none outline-none placeholder-gray-400 disabled:opacity-50 disabled:cursor-not-allowed"
					:disabled="promotionalCampaign && promotionalCampaign?.active" />
			</div>
		</CardContainer>
	</div>
</template>

<script>
import { computed, ref, watch, watchEffect, onMounted } from 'vue'
import StatusMessage from '../components/StatusMessage.ts.vue';
import LearnMoreText from '../components/LearnMoreText.ts.vue';
import CardContainer from '../components/CardContainer.ts.vue';
import PrimaryButton from '../components/PrimaryButton.ts.vue';
import NumberChart from '../components/NumberChart.ts.vue';
import ToggleItem from '../components/ToggleItem.ts.vue';
import { getPromotionalCampaigns, getFreeGift, deletePromoFreeGift, getProducts, createPromoFreeGift } from '../services/promotional-campaigns.js';
import { getExtenstions } from '../services/extensions.js';
import { getCurrentOrg } from '../services/organization.js';
import { isFeatureAvailable } from '../services/features.js';
import { getMetric } from '../services/metrics.js';
import * as CurrencyUtils from '../services/currency.js';
import { Crisp } from 'crisp-sdk-web';
import * as Utils from '../../client-old/utils/Utils';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	name: 'GiftWithPurchase',
	components: {
		StatusMessage,
		LearnMoreText,
		CardContainer,
		PrimaryButton,
		ToggleItem,
		NumberChart
	},
	computed: {
		isFeatureAvailable() {
			return isFeatureAvailable('gwp-features');
		},
		isTrialEnded() {
			const freeTrialData = JSON.parse(localStorage.getItem('freeTrialData'));

			return freeTrialData != {} && !freeTrialData.hasPaidPlan && freeTrialData.daysLeft < 0
		},
		isShopifyConnected() {
			return this.shopifyConnected;
		}
	},
	data() {
		return {
			giftsAdded: "--",
			giftsRedeemed: "--",
			giftsConversion: "--",
			giftsRevenue: "--",
			isLoadingMetrics: true,
			isLoadingProducts: false,
			shopifyConnected: true
		};
	},
	setup() {
		const extension = ref(null);
		const status = ref('');
		const statusText = ref('');
		const products = ref([]);
		const selectedMode = ref('basic');
		const selectedProduct = ref('');
		const selectedVariant = ref('');
		const selectedGiftProducts = ref([]);
		const minOrderAmount = ref('');
		const isSaving = ref(false);
		const freeGiftExtension = ref('');
		const promotionalCampaign = ref(null);
		const showForceDiscountCode = ref(false);
		const forceDiscountCode = ref(false);
		const disableCouponStacking = ref(false);

		const availableProducts = (currentIndex) => {
			return products.value.filter(product =>
				!selectedGiftProducts.value.some((giftProduct, index) =>
					index !== currentIndex && giftProduct.product && giftProduct.product.id === product.id
				)
			);
		};

		const addGiftProduct = () => {
			if (selectedGiftProducts.value.length < 4) {
				selectedGiftProducts.value.push({
					product: '',
					variant: ''
				});
			}
		};

		const removeGiftProduct = (index) => {
			selectedGiftProducts.value.splice(index, 1);
		};

		const handleProductChange = (index) => {
			const giftProduct = selectedGiftProducts.value[index];
			if (giftProduct.product) {
				if (giftProduct.product.variants?.length === 1) {
					giftProduct.variant = giftProduct.product.variants[0];
				} else {
					giftProduct.variant = '';
				}
			}
		};

		async function fetchExtensions() {
			let extensionsResponse = await getExtenstions();
			extensionsResponse = extensionsResponse.filter(x => x.endpoint == '/extensions/free-gift');
			extension.value = extensionsResponse[0];
			let promotionalCampaigns = await getPromotionalCampaigns();
			promotionalCampaign.value = promotionalCampaigns[0];

			if (!products.value.length) {
				const shopProducts = await getProducts();
				products.value = shopProducts;
			}
			if (promotionalCampaigns.length) {
				const data = await getFreeGift(promotionalCampaigns[0].id);
				freeGiftExtension.value = data;
				disableCouponStacking.value = data?.config?.disableCouponStacking;
				forceDiscountCode.value = data?.config?.forceDiscountCode;
			} else {
				promotionalCampaign.value = { active: false };
				freeGiftExtension.value = { enabled: false };
				}
		}

		watchEffect(() => {
			console.log("watchEffect");
			if (products.value.length && freeGiftExtension.value?.config) {
				const config = freeGiftExtension.value.config;

				// Reset selections first
				selectedMode.value = 'basic';
				selectedProduct.value = '';
				selectedVariant.value = '';
				selectedGiftProducts.value = [];

				// Handle legacy schema format
				if (!config.mode) {
					minOrderAmount.value = config.minimumOrderAmount || 0;

					// Legacy format conversion
					const legacyVariantId = config.freeProductVariantId?.match(/\d+/)?.[0]; // Extract numeric ID
					const legacyPrice = parseFloat(config.productVariantPrice) || 0;

					if (legacyVariantId) {
						for (const product of products.value) {
							const matchingVariant = product.variants.find(v => v.id === legacyVariantId);
							if (matchingVariant) {
								selectedProduct.value = product;
								selectedVariant.value = matchingVariant
								break;
							}
						}
					}
				} else {
					// New schema format
					selectedMode.value = config.mode;
					minOrderAmount.value = config.minimumOrderAmount || 0;

					if (config.giftOptions?.length) {
						if (config.mode === 'basic') {
							console.log("watchEffect basic");
							// Basic mode should have exactly 1 gift option
							const option = config.giftOptions[0];
							const variantId = option.productVariantId?.match(/\d+/)?.[0]; // Extract numeric ID
							const price = parseFloat(option.price) || 0;

							console.log("watchEffect basic variantId", variantId);

							if (variantId) {
								console.log(products.value);
								for (const product of products.value) {
									console.log("watchEffect basic product", product.variants);
									const matchingVariant = product.variants.find(v => v.id === variantId);
									if (matchingVariant) {
										console.log("watchEffect basic matchingVariant", matchingVariant);
										selectedProduct.value = product;
										// Set both reactively
										selectedProduct.value = product;
										selectedVariant.value = matchingVariant;
										console.log("watchEffect basic selectedVariant", selectedVariant.value);
										break;
									}
								}
							}
						} else if (config.mode === 'choose_gift') {
							// Choose gift mode can have multiple options
							const giftOptionsByProduct = {};

							// First pass: group options by product ID
							config.giftOptions.forEach(option => {
								const variantId = option.productVariantId?.match(/\d+/)?.[0];
								if (variantId) {
									for (const product of products.value) {
										const matchingVariant = product.variants.find(v => v.id === variantId);
										if (matchingVariant) {
											if (!giftOptionsByProduct[product.id]) {
												giftOptionsByProduct[product.id] = {
													product,
													variants: new Set(),
													totalVariants: product.variants.length
												};
											}
											giftOptionsByProduct[product.id].variants.add(matchingVariant.id);
											break;
										}
									}
								}
							});

							// Second pass: create final gift products array
							selectedGiftProducts.value = Object.values(giftOptionsByProduct).map(({ product, variants, totalVariants }) => {
								// If all variants of a product are present, set variant to 'any'
								if (variants.size === totalVariants) {
									return {
										product,
										variant: 'any'
									};
								} else {
									// Get the first variant ID from the set
									const variantId = Array.from(variants)[0];
									return {
										product,
										variant: product.variants.find(v => v.id === variantId)
									};
								}
							}).filter(opt => opt.product && opt.variant);
						}
					}
				}
			}
		});

		watch(selectedProduct, (newVal, oldVal) => {
			if (newVal.variants?.length === 1) {
				selectedVariant.value = newVal.variants[0];
			} else if (!selectedVariant.value || !newVal.variants.find(v => v.id === selectedVariant?.value?.id)) {
				selectedVariant.value = '';
			}
		});

		onMounted(async () => {
			fetchExtensions();
		});

		getCurrentOrg().then(org => {
			showForceDiscountCode.value = org?.externalPlanDetails?.shopifyPlus;
		});

		const handleToggle = async () => {
			isSaving.value = true;
			promotionalCampaign.value.active = !promotionalCampaign.value.active;

			if (promotionalCampaign.value.active) {
				if (selectedMode.value === 'basic' && !selectedVariant.value?.id) {
					status.value = { type: 'fail', message: 'Failed to save. Please select a product and variant.' };
					isSaving.value = false;
					promotionalCampaign.value.active = false;
					return;
				}
				if (selectedMode.value === 'choose_gift') {
					const invalidGifts = selectedGiftProducts.value.some(gift => {
						if (!gift.product) return true;
						if (gift.product.variants?.length > 0 && !gift.variant && gift.variant !== 'any') return true;
						return false;
					});
					if (selectedGiftProducts.value.length === 0 || invalidGifts) {
						status.value = { type: 'fail', message: 'Failed to save. Please add at least one gift option and ensure all variants are selected where applicable.' };
						isSaving.value = false;
						promotionalCampaign.value.active = false;
						return;
					}
				}

				const giftOptions = selectedMode.value === 'choose_gift'
					? selectedGiftProducts.value.flatMap(gift => {
						if (gift.variant === 'any') {
							// When ANY is selected, add all variants of the product
							return gift.product.variants.map(variant => ({
								productVariantId: variant.id,
								price: parseFloat(variant.price)
							}));
						}
						return [{
							productVariantId: gift.product.variants?.length > 0 ? gift.variant.id : gift.product.variants[0].id,
							price: parseFloat(gift.product.variants?.length > 0 ? gift.variant.price : gift.product.variants[0].price)
						}];
					})
					: [];

				const body = JSON.stringify({
					functionId: extension.value.functionId,
					minimumOrderAmount: minOrderAmount.value || 0,
					forceDiscountCode: forceDiscountCode.value,
					disableCouponStacking: disableCouponStacking.value,
					isShopifyPlus: showForceDiscountCode.value,
					mode: selectedMode.value,
					giftOptions: selectedMode.value === 'basic'
						? [{
							productVariantId: selectedVariant.value.id,
							price: parseFloat(selectedVariant.value.price)
						}]
						: giftOptions.map(option => ({
							productVariantId: option.productVariantId,
							price: parseFloat(option.price)
						}))
				});

				const response = await createPromoFreeGift(body);
				if (response.error && response.error.statusCode > 200) {
					status.value = { type: 'fail', message: 'Failed to save. Ensure all required fields are filled out.' };
					console.error('Error enabling extension: ', response.error.message);
					promotionalCampaign.value.active = false;
				} else {
					status.value = { type: 'success', message: 'Gift with purchase saved and is live.' };
				}

			} else {
				const response = await deletePromoFreeGift(promotionalCampaign.value.id);
				if (response.error && response.error.statusCode > 200) {
					status.value = { type: 'fail', message: 'Failed to disable Gift With Purchase.' };
					console.error('Error disabling extension: ', response.error.message);
					promotionalCampaign.value.active = false;
				} else {
					status.value = { type: 'success', message: 'Gift with purchase has been disabled.' };
					promotionalCampaign.value = null;
				}
			}
			isSaving.value = false;

			await fetchExtensions();
		}

		const openIntercom = () => {
			Crisp.chat.open();
		}

		return {
			extension,
			status,
			statusText,
			fetchExtensions,
			products,
			selectedMode,
			selectedProduct,
			selectedVariant,
			selectedGiftProducts,
			minOrderAmount,
			isSaving,
			handleToggle,
			openIntercom,
			showForceDiscountCode,
			forceDiscountCode,
			disableCouponStacking,
			promotionalCampaign,
			addGiftProduct,
			removeGiftProduct,
			handleProductChange,
			availableProducts
		}
	},
	mounted() {
		this.checkShopifyConnection();
		this.fetchExtensions();
		this.getMetrics();
	},
	methods: {
		async checkShopifyConnection() {
			try {
				const response = await fetch(`${URL_DOMAIN}/integration/shopify/connected`, {
					method: 'GET',
					headers: {
						'Content-Type': 'application/json',
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
					}
				});
				if (!response.ok) {
					throw new Error('Failed to check Shopify connection');
				}
				const data = await response.json();
				this.shopifyConnected = data.connected;
			} catch (error) {
				console.error('Error checking Shopify connection:', error);
				this.shopifyConnected = false;
			}
		},
		async getMetrics() {
			this.isLoadingMetrics = true;
			let data = await getMetric('gwp_metrics', 'latest', '', 'day', 'sum');
			await this.fetchMetricAndUpdate(data, 'giftsRedeemed', 'gifts_redeemed');
			await this.fetchMetricAndUpdate(data, 'giftsAdded', 'gifts_added_to_cart');
			await this.fetchMetricAndUpdate(data, 'giftsConversion', 'gwp_conversion_rate');
			await this.fetchMetricAndUpdate(data, 'giftsRevenue', 'revenue_with_gwp');
			this.isLoadingMetrics = false;
		},
		async fetchMetricAndUpdate(data, dataProperty, metricKey) {
			if (data == "Metrics still loading...") return console.log('No data for metric: gwp_metrics');
			if (data.body.data.length > 0) {
				const lastDataItem = data.body.data[0];
				console.log('lastDataItem', lastDataItem);
				console.log('lastDataItem.metrics', lastDataItem.metrics);
				if (lastDataItem.metrics && lastDataItem.metrics[metricKey]) {
					const prefix = lastDataItem.metrics[metricKey].prefix || '';
					const value = lastDataItem.metrics[metricKey].value || 0;
					const suffix = lastDataItem.metrics[metricKey].suffix || '';

					if (prefix == '$') {
						this[dataProperty] = await CurrencyUtils.replaceDollarWithCurrency(prefix + Number(value).toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 }) + suffix);
					}
					else {
						this[dataProperty] = prefix + value + suffix;
					}
				} else {
					console.error('No metrics found in the last data item');
				}
			} else {
				console.error('No data returned for metric:', metricName);
			}

		}
	}
}
</script>
<style scoped>
.test-container {
	margin: 0.5em;
}

.test {
	width: 50%;
	padding: 0em 0.4em;
}

@media all and (max-width: 768px) {
	.test {
		width: 100%;
	}
}
</style>
