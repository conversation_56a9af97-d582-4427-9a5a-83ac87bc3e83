<template>
	<ProgramActive></ProgramActive>
	<div class="p-2 sm:p-7 mr-24 flex flex-col items-center justify-center" v-if="!isFeatureAvailable && !isTrialEnded">
		<img src="../images/VIPTeirPromo.png" width="800">
		<a class="text-ralprimary-dark font-bold md:text-xl lg:text-2xl sm:text-normal mt-4 font-[Inter]" href="/loyalty/settings/plans">Upgrade to Loyalty Plan</a>
		<p class="text-ralblack-primary text-lg mt-4 flex items-center font-[Inter] mb-4 w-1/2 text-center">
			Loyalty programs powered by Raleon help brands increase repeat purchase rate and customer lifetime value.
		</p>
		<PrimaryButton
					cta="Upgrade Loyalty Plan"
					size="xs"
					@click="() => this.$router.push('/loyalty/settings/plans')"
					/>
	</div>


	<div class="bg-gradient-to-tr from-[#4F3EAC] to-[#5E48F8] p-4 w-full justify-center shadow-lg flex items-center" v-if="!isFeatureAvailable && isTrialEnded">
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 14H11V9.8L12.6 11.4L14 10L10 6L6 10L7.4 11.4L9 9.8V14ZM10 20C8.61667 20 7.31667 19.7375 6.1 19.2125C4.88333 18.6875 3.825 17.975 2.925 17.075C2.025 16.175 1.3125 15.1167 0.7875 13.9C0.2625 12.6833 0 11.3833 0 10C0 8.61667 0.2625 7.31667 0.7875 6.1C1.3125 4.88333 2.025 3.825 2.925 2.925C3.825 2.025 4.88333 1.3125 6.1 0.7875C7.31667 0.2625 8.61667 0 10 0C11.3833 0 12.6833 0.2625 13.9 0.7875C15.1167 1.3125 16.175 2.025 17.075 2.925C17.975 3.825 18.6875 4.88333 19.2125 6.1C19.7375 7.31667 20 8.61667 20 10C20 11.3833 19.7375 12.6833 19.2125 13.9C18.6875 15.1167 17.975 16.175 17.075 17.075C16.175 17.975 15.1167 18.6875 13.9 19.2125C12.6833 19.7375 11.3833 20 10 20ZM10 18C12.2333 18 14.125 17.225 15.675 15.675C17.225 14.125 18 12.2333 18 10C18 7.76667 17.225 5.875 15.675 4.325C14.125 2.775 12.2333 2 10 2C7.76667 2 5.875 2.775 4.325 4.325C2.775 5.875 2 7.76667 2 10C2 12.2333 2.775 14.125 4.325 15.675C5.875 17.225 7.76667 18 10 18Z" fill="#FFA3DF"/>
        </svg>
        <div class="text-white ml-2">Your trial has expired and your program has been deactivated.</div>
        <div class="px-2 py-2 text-white text-sm border-[#FFF] border rounded-full ml-4 hover:text-black hover:bg-[#FFF] hover:cursor-pointer transitional-bg delay-200" @click="() => this.$router.push('/loyalty/settings/plans')">
                    Choose a Plan
        </div>
    </div>

	<div class="p-2 sm:p-7 mr-24 flex flex-col items-center justify-center" v-if="isFeatureAvailable && !isShopifyConnected">
		<img src="https://cdn.shopify.com/shopifycloud/brochure/assets/brand-assets/shopify-logo-primary-logo-456baa801ee66a0a435671082365958316831c9960c480451dd0330bcdae304f.svg" width="584">
		<h2 class="text-ralprimary-dark font-bold md:text-xl lg:text-2xl sm:text-normal mt-4 font-[Inter]">Connect Shopify for Loyalty Program</h2>
		<p class="text-ralblack-primary text-lg mt-4 flex items-center font-[Inter] mb-4 w-1/2 text-center">
			To set up and manage your loyalty program, connect your Shopify store to enable points tracking and rewards redemption.
		</p>
		<PrimaryButton
					cta="Connect Shopify"
					size="xs"
					@click="() => this.$router.push('/integrations')"
					/>
	</div>

	<div v-if="isFeatureAvailable && isShopifyConnected" class="m-3 sm:m-7">
		<div class="flex flex-col sm:flex-row items-center justify-between">

			<div class="flex justify-center items-center relative">
				<div class="text-3xl sm:text-6xl font-sans font-medium opacity-70 text-ralblack-primary">
					Loyalty Program
				</div>
			</div>
			<div class="flex-grow"></div>
			<div class="flex w-full sm:w-auto justify-end sm:justify-start">
				<div v-if="isLoading" role="status"
					class="p-3 space-y-4 bg-gray-300 dark:bg-gray-600 w-56 rounded-2xl shadow animate-pulse"></div>

				<div v-if="!isLoading" class="flex mt-1 w-full sm:w-auto justify-end">
					<PreviewLoyaltyProgram />
				</div>
			</div>
		</div>
		<div class="bg-ralinfo-light rounded-2xl p-4 mt-4">
			<div class="inline-flex items-center mb-4">
				<svg class="w-4 h-4 fill-current text-ralrpimary-ultralight mr-2 opacity-50" viewBox="0 0 16 16">
					<path
						d="M8 0C3.6 0 0 3.6 0 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8zm0 12c-.6 0-1-.4-1-1s.4-1 1-1 1 .4 1 1-.4 1-1 1zm1-3H7V4h2v5z" />
				</svg>

				<p class="text-ralblack-primary text-base font-[Inter]">Summary of your enabled Loyalty Program</p>
			</div>
			<ul v-for="summary in summaryText"
				class="text-ralblack-primary text-sm font-[Inter] list-disc pl-4 md:pl-6 lg:pl-8">
				<li v-html="summary"></li>
			</ul>
		</div>

		<div class="mt-10">
			<div class="flex">
				<div>
					<div class="text-zinc-500 text-2xl sm:text-5xl font-medium font-['Inter'] leading-[65px]">
						Setup Points Per Dollar
					</div>

				</div>
				<div class="flex-grow"></div>

			</div>
		</div>

		<div class="mt-4">
			<div class="mt-2">
				<div
					@click="editPointsProgram"
					class="overflow-x-auto bg-white bg-opacity-75 rounded-2xl shadow hover:cursor-pointer hover:bg-indigo-50 border border-ralprimary-light border-opacity-50 flex-grow transition-all duration-300 ease-in-out overflow-hidden">

					<div class="flex items-center ml-4 mr-8 justify-between">

						<div class="flex items-center min-h-[75px]">
							<svg fill="#15803D" slot="dollar-spent" xmlns="http://www.w3.org/2000/svg"
								height="33" viewBox="0 -960 960 960" width="33">
								<path
									d="M237-120q-23 0-44.5-16T164-175q-25-84-41-145.5t-25.5-108Q88-475 84-511t-4-69q0-92 64-156t156-64h200q27-36 68.5-58t91.5-22q25 0 42.5 17.5T720-820q0 6-1.5 12t-3.5 11q-4 11-7.5 22t-5.5 24l91 91h47q17 0 28.5 11.5T880-620v210q0 13-7.5 23T852-372l-85 28-50 167q-8 26-29 41.5T640-120h-80q-33 0-56.5-23.5T480-200h-80q0 33-23.5 56.5T320-120h-83Zm3-80h80v-80h240v80h80l62-206 98-33v-141h-40L620-720q0-20 2.5-39t7.5-37q-29 8-51 27.5T547-720H300q-58 0-99 41t-41 99q0 41 21 140.5T240-200Zm400-320q17 0 28.5-11.5T680-560q0-17-11.5-28.5T640-600q-17 0-28.5 11.5T600-560q0 17 11.5 28.5T640-520Zm-160-80q17 0 28.5-11.5T520-640q0-17-11.5-28.5T480-680H360q-17 0-28.5 11.5T320-640q0 17 11.5 28.5T360-600h120Zm0 102Z" />
							</svg>

							<div class="m-4" v-if="!this.isLoading">
								<div class="text-sm font-semibold font-['Inter'] text-ralsecondary-start">When a customer places an order</div>
								<div class="text-sm text-ralgray-dark">They earn {{ this.ppdSummaryData[0]?.pointsPerDollar || 1 }} point for every {{replaceCurrencyTagsSync('<currencyPrefix>1<currencyPostfix>')}} spent.</div>
							</div>
						</div>

						<LightSecondaryButton
							cta="Edit"
							@click="editPointsProgram">
						</LightSecondaryButton>
					</div>

				</div>
			</div>
		</div>

		<div class="mt-10">
			<div class="flex">
				<div>
					<div class="text-zinc-500 text-2xl sm:text-5xl font-medium font-['Inter'] leading-[65px]">
						Ways to Earn
					</div>
				</div>
				<div class="flex-grow"></div>
			</div>
		</div>
		<div class="mt-4">
			<div v-if="wteRecommendations.length === 0"
				class="max-w-sm rounded-lg border-2 border-dashed border-gray-300 bg-white p-6 shadow-md"
				style="max-width: 400px;">
				<div class="text-center text-gray-500">
					<div class="flex justify-center items-center mb-2">
						<p class="text-md font-medium mr-2">No Current Copilot Recommendations</p>
						<img src="../images/magic.svg" alt="Magic" width="20" height="20" />
					</div>
					<p class="text-sm">They will show up here when they are available.</p>
				</div>
			</div>
			<div v-else class="flex flex-wrap -mx-2">
				<div
					v-for="(wte, index) in wteRecommendations.slice(0, 3)"
					:key="wte.id"
					class="px-2 w-full sm:w-1/3 mb-4 min-w-[380px]">
					<RecommendationCard
						:recommendation="wte"
						title="Add way to earn"
						:index="index"
						:fadeOut="wte.fadeOut"
						:icon="wte.icon"
						@addRecommendation="addRecommendedWTE"
						@ignoreRecommendation="ignoreRecommendedWTE"
					/>
				</div>
			</div>
		</div>

		<div class="mt-4">
			<div class="flex justify-end">
				<PrimaryButton cta="add" size="xs" icon="true" @click="addWayToEarn()"></PrimaryButton>
			</div>
			<div class="mt-2">
				<div
					class="overflow-x-auto bg-white bg-opacity-75 rounded-2xl shadow border border-ralprimary-light border-opacity-50 flex-grow transition-all duration-300 ease-in-out overflow-hidden">
					<WTERewardTable header1="Way to Earn" header2="Status" header3="Sort" :tableData="wteSummaryData" :allowDelete=true
						@editClicked="handleWTESummaryEdit" @toggleChanged="handleWTESummaryToggle" @priorityChanged="updatePriorityEarn"
						@deleteClicked="async (event) => handleWTEDelete(event, 'wte')" :rowIdPrefix="'wte'" :highlightedItemId="this.highlightedItemId"/>
					<div class="flex flex-col items-center justify-center h-22 mb-2"
						v-if="wteSummaryData.length <= 0 && isWTELoading == false">
						<svg width="540" height="30" viewBox="0 0 501 30" fill="none" xmlns="http://www.w3.org/2000/svg">
							<g opacity="0.7">
								<g filter="url(#filter0_d_605_5769)">
									<rect x="6" y="4" width="501" height="17.0606" rx="8.53032" fill="white"
										fill-opacity="0.75" shape-rendering="crispEdges" />
									<rect x="6.5" y="4.5" width="500" height="16.0606" rx="8.03032" stroke="#D6D1FD"
										shape-rendering="crispEdges" />
								</g>
								<rect x="387" y="10" width="110" height="5" rx="2.5" fill="url(#paint0_linear_605_5769)" />
								<rect x="237" y="10" width="110" height="5" rx="2.5" fill="url(#paint1_linear_605_5769)" />
								<rect x="47" y="10" width="110" height="5" rx="2.5" fill="url(#paint2_linear_605_5769)" />
							</g>
							<defs>
								<filter id="filter0_d_605_5769" x="0" y="0" width="513" height="29.0605"
									filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
									<feFlood flood-opacity="0" result="BackgroundImageFix" />
									<feColorMatrix in="SourceAlpha" type="matrix"
										values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
									<feOffset dy="2" />
									<feGaussianBlur stdDeviation="3" />
									<feComposite in2="hardAlpha" operator="out" />
									<feColorMatrix type="matrix"
										values="0 0 0 0 0.0509804 0 0 0 0 0.0392157 0 0 0 0 0.172549 0 0 0 0.08 0" />
									<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_605_5769" />
									<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_605_5769"
										result="shape" />
								</filter>
								<linearGradient id="paint0_linear_605_5769" x1="430" y1="-0.555557" x2="433.874"
									y2="26.1776" gradientUnits="userSpaceOnUse">
									<stop stop-color="#D9D9D9" />
									<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
								</linearGradient>
								<linearGradient id="paint1_linear_605_5769" x1="280" y1="-0.555557" x2="283.874"
									y2="26.1776" gradientUnits="userSpaceOnUse">
									<stop stop-color="#D9D9D9" />
									<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
								</linearGradient>
								<linearGradient id="paint2_linear_605_5769" x1="90" y1="-0.555557" x2="93.8744" y2="26.1776"
									gradientUnits="userSpaceOnUse">
									<stop stop-color="#D9D9D9" />
									<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
								</linearGradient>
							</defs>
						</svg>

						<p class="text-xl text-ralblack-primary mt-2">You haven't added a way to earn yet.</p>
					</div>
				</div>
			</div>
		</div>


		<div class="mt-10">
			<div class="flex">
				<div>
					<div class="text-zinc-500 text-2xl sm:text-5xl font-medium font-['Inter'] leading-[65px]">
						Redeem Points For These Rewards
					</div>
				</div>
				<div class="flex-grow"></div>
			</div>
		</div>
		<div class="mt-4">
			<div v-if="rewardRecommendations.length === 0"
				class="max-w-sm rounded-lg border-2 border-dashed border-gray-300 bg-white p-6 shadow-md"
				style="max-width: 400px;">
				<div class="text-center text-gray-500">
					<div class="flex justify-center items-center mb-2">
						<p class="text-md font-medium mr-2">No Current Copilot Recommendations</p>
						<img src="../images/magic.svg" alt="Magic" width="20" height="20" />
					</div>
					<p class="text-sm">They will show up here when they are available.</p>
				</div>
			</div>
			<div v-else class="flex flex-wrap -mx-2">
				<div v-for="(shopItem, index) in rewardRecommendations.slice(0, 3)" :key="shopItem.id"
					class="px-2 w-full sm:w-1/3 mb-4 min-w-[380px]">
					<RecommendationCard
						:recommendation="shopItem"
						title="Add Redeemable Reward"
						:index="index"
						:fadeOut="shopItem.fadeOut"
						:icon="shopItem.icon"
						@addRecommendation="addRecommendedShopItem"
						@ignoreRecommendation="ignoreRecommendedShopItem"
					/>
					<!-- <div class="max-w-sm rounded-lg border bg-white p-6 shadow-md fade-out-transition"
						:class="{'opacity-0': shopItem.fadeOut}" style="max-width: 400px;">
						<div class="mb-4">
							<span class="block text-xs font-medium text-gray-500 tracking-wider">
								Add Redeemable Reward
							</span>
						</div>
						<div class="flex items-center mb-4">
							<div class="p-2 rounded-full bg-green-100">
								<div class="p-2 rounded-full bg-green-100" v-html="shopItem.icon"></div>
							</div>
							<div class="ml-3">
								<p class="text-md font-medium text-gray-900">
									{{ shopItem.title }}
								</p>
							</div>
						</div>
						<div class="flex justify-between items-center">
							<div class="flex items-center">
								<button class="text-xs font-semibold text-gray-400 hover:text-gray-500">
									Copilot Recommended
								</button>
								<img src="../images/magic.svg" alt="Magic" class="ml-2" width="20" height="20" />
							</div>
							<div>
								<button class="text-sm font-semibold text-gray-400 hover:text-gray-500 mr-4"
									@click="ignoreRecommendedShopItem(shopItem, index)">
									Ignore
								</button>
								<PrimaryButton cta="Apply" size="xs" icon="false"
									@click="addRecommendedShopItem(shopItem, index)"></PrimaryButton>
							</div>
						</div>
					</div> -->
				</div>
			</div>

		</div>
		<div class="my-4">
			<div class="flex justify-end">
				<PrimaryButton cta="add" size="xs" icon="true" @click="addReward()"></PrimaryButton>
			</div>
			<div class="mt-2">
				<div
					class="overflow-x-auto bg-white bg-opacity-75 rounded-2xl shadow border border-ralprimary-light border-opacity-50 flex-grow transition-all duration-300 ease-in-out overflow-hidden">
					<WTERewardTable header1="Reward Type" header2="Status" header3="Sort" :tableData="rewardSummaryData" :allowDelete=true
						@editClicked="handleShopItemSummaryEdit" @toggleChanged="handleShopItemSummaryToggle" @priorityChanged="updatePriorityReward"
						@deleteClicked="async (event) => handleWTEDelete(event, 'shop-item')" :rowIdPrefix="'si'" :highlightedItemId="this.highlightedItemId" />
					<div class="flex flex-col items-center justify-center h-22 mb-2"
						v-if="rewardSummaryData.length <= 0 && isRewardLoading == false">
						<svg width="540" height="30" viewBox="0 0 501 30" fill="none" xmlns="http://www.w3.org/2000/svg">
							<g opacity="0.7">
								<g filter="url(#filter0_d_605_5769)">
									<rect x="6" y="4" width="501" height="17.0606" rx="8.53032" fill="white"
										fill-opacity="0.75" shape-rendering="crispEdges" />
									<rect x="6.5" y="4.5" width="500" height="16.0606" rx="8.03032" stroke="#D6D1FD"
										shape-rendering="crispEdges" />
								</g>
								<rect x="387" y="10" width="110" height="5" rx="2.5" fill="url(#paint0_linear_605_5769)" />
								<rect x="237" y="10" width="110" height="5" rx="2.5" fill="url(#paint1_linear_605_5769)" />
								<rect x="47" y="10" width="110" height="5" rx="2.5" fill="url(#paint2_linear_605_5769)" />
							</g>
							<defs>
								<filter id="filter0_d_605_5769" x="0" y="0" width="513" height="29.0605"
									filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
									<feFlood flood-opacity="0" result="BackgroundImageFix" />
									<feColorMatrix in="SourceAlpha" type="matrix"
										values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
									<feOffset dy="2" />
									<feGaussianBlur stdDeviation="3" />
									<feComposite in2="hardAlpha" operator="out" />
									<feColorMatrix type="matrix"
										values="0 0 0 0 0.0509804 0 0 0 0 0.0392157 0 0 0 0 0.172549 0 0 0 0.08 0" />
									<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_605_5769" />
									<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_605_5769"
										result="shape" />
								</filter>
								<linearGradient id="paint0_linear_605_5769" x1="430" y1="-0.555557" x2="433.874"
									y2="26.1776" gradientUnits="userSpaceOnUse">
									<stop stop-color="#D9D9D9" />
									<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
								</linearGradient>
								<linearGradient id="paint1_linear_605_5769" x1="280" y1="-0.555557" x2="283.874"
									y2="26.1776" gradientUnits="userSpaceOnUse">
									<stop stop-color="#D9D9D9" />
									<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
								</linearGradient>
								<linearGradient id="paint2_linear_605_5769" x1="90" y1="-0.555557" x2="93.8744" y2="26.1776"
									gradientUnits="userSpaceOnUse">
									<stop stop-color="#D9D9D9" />
									<stop offset="1" stop-color="#D9D9D9" stop-opacity="0" />
								</linearGradient>
							</defs>
						</svg>

						<p class="text-xl text-ralblack-primary mt-2">You haven't added any redeemable rewards yet.</p>
					</div>
				</div>
			</div>
		</div>
	</div>

	<StatusMessage :message=status.message :status=status.type @resetStatus="status.type = 'nope'"></StatusMessage>

	<ModalBlank id="delete-modal" :modal-open="deleteModalOpen" @close-modal="this.deleteModalOpen = false">
		<div class="flex flex-col px-5 py-4">
			<div class="text-xl">{{ deleteModalText }}</div>
			<div class="mt-10 flex justify-end gap-3">
				<LightSecondaryButton cta="Cancel" @click="deleteModalOpen = false"></LightSecondaryButton>
				<PrimaryButton
					size="xs"
					cta="Delete"
					@click="deleteWTE()"
					:disabled="isDeleting"
					:showSpinner="isDeleting"
					:spinnerText="'Deleting...'">
				</PrimaryButton>
			</div>
		</div>
	</ModalBlank>
</template>

<script>
import NumberChart from '../components/NumberChart.ts.vue';
import RaleonTable from '../components/RaleonTable.ts.vue';
import ModalBlank from '../components/ModalBlank.vue';
import CardContainer from '../components/CardContainer.ts.vue';
import SuperDropdown from '../../client/components/SuperDropdown.ts.vue';
import * as Utils from '../../client-old/utils/Utils';
import EditableHeader from '../components/EditableHeader.ts.vue'
import PrimaryButton from '../components/PrimaryButton.ts.vue';
import ProgramActive from '../components/ProgramActive.ts.vue';
import LightSecondaryButton from '../components/LightSecondaryButton.ts.vue';
import CancelButton from '../components/CancelButton.ts.vue';
import ToggleItem from '../components/ToggleItem.ts.vue';
import StatusMessage from '../components/StatusMessage.ts.vue';
import Tooltip from '../components/Tooltip.ts.vue';
import GeneratedText from '../components/GeneratedText.ts.vue';
import RaleonLoader from '../components/RaleonLoader.ts.vue'
import TextFadeEffect from '../components/TextFadeEffect.ts.vue'
import PreviewLoyaltyProgram from '../components/PreviewLoyalty.ts.vue';
import * as OrganizationSettings from '../services/organization-settings.js'
import WTERewardTable from '../components/WTERewardTable.vue';
import RecommendationCard from '../components/RecommendationCard.ts.vue';
import { customerIOTrackEvent } from '../services/customerio.js';
import * as CurrencyUtils from '../services/currency.js';
import { deleteWteOrShopItem } from '../services/ways-to-earn.js';
import { isFeatureAvailable } from '../services/features.js';

const URL_DOMAIN = Utils.URL_DOMAIN;
export const RECOMMENDATION_STATES = {
	APPROVED_RECOMMENDATION: 'APPROVED_RECOMMENDATION',
	IGNORED_RECOMMENDATION: 'IGNORED',
	RECOMMENDED: 'RECOMMENDED',
	NONE: 'NONE'
}

export default {
	props: [],
	data() {
		return {
			campaign: { name: 'Untitled Campaign' },
			wteColumns: [
				{ name: 'Name', value: 'name' },
				{ name: 'Conditions', value: 'conditions' },
				{ name: 'Rewards', value: 'effects' }
			],
			wteData: [],
			rewardShopColumns: [
				{ name: 'Name', value: 'name' }
			],
			rewardShopData: [],
			isSettingsModalOpen: false,
			targetSegment: 'Everyone',
			insights: false, //set true when insights are being generated
			isOpen: false,
			scheduleType: null,
			status: { message: '', type: 'nope' },
			cScheduleType: null,
			cStartdate: null,
			cEnddate: null,
			cSegment: null,
			settingModalOpen: false,
			isLoading: true,
			showRaleon: false,
			uiCustomerActions: [],
			uiCustomerRewards: [],
			wteSummaryData: [],
			ppdSummaryData: [],
			wteRecommendations: [],
			rewardRecommendations: [],
			rewardSummaryData: [],
			isWTELoading: true,
			isRewardLoading: true,
			summaryText: [
				'Apply AI recommendations or craft your own!'
			],
			deleteModalOpen: false,
			toDelete: {},
			deleteType: '',
			isDeleting: false,
			highlightedItemId: '',
			shopifyConnected: true,
		}
	},
	components: {
		NumberChart,
		RaleonTable,
		SuperDropdown,
		ModalBlank,
		EditableHeader,
		PrimaryButton,
		ProgramActive,
		LightSecondaryButton,
		CancelButton,
		ToggleItem,
		StatusMessage,
		CardContainer,
		Tooltip,
		GeneratedText,
		RaleonLoader,
		PreviewLoyaltyProgram,
		OrganizationSettings,
		TextFadeEffect,
		WTERewardTable,
		RecommendationCard,
	},
	async mounted() {
		customerIOTrackEvent('Foundation Loyalty Program Viewed');

		await this.checkShopifyConnection();

		if (this.isShopifyConnected) {
			await this.fetchCampaign().catch();
			await this.fetchCampaignSummary();

			const wte = this.fetchWte().catch();
			const rewards = this.fetchShopRewards().catch();

			let firstCampaignView = await OrganizationSettings.getOrganizationSetting('firstCampaignView');

			if (firstCampaignView == 'false' || firstCampaignView == null) {
				let res1 = await OrganizationSettings.updateOrganizationSetting('firstCampaignView', 'true');
				this.aiModelOpen = true;
			}

			Promise.all([wte, rewards]).then(() => {
				if (this.wteData.length == 0 || this.rewardShopData.length == 0) {
					this.$forceUpdate();
				}
			});

			const scrollToEl = this.$route.query['sto'];
			if (scrollToEl) {
				this.scrollToItem(scrollToEl);
				this.highlightedItemId = scrollToEl;
			}

			let url = `/onboarding-tasks/Loyalty Program/states`;
			const response = await fetch(`${URL_DOMAIN}${url}`, {
				method: 'PATCH',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify({
					state: 'Verified'
				})
			});
		}
	},
	beforeDestroy() {

	},
	computed: {
		isFeatureAvailable() {
			return isFeatureAvailable('points');
		},
		isTrialEnded() {
			const freeTrialData = JSON.parse(localStorage.getItem('freeTrialData'));

			return freeTrialData != {} && !freeTrialData.hasPaidPlan && freeTrialData.daysLeft < 0
		},
		isShopifyConnected() {
			return this.shopifyConnected;
		}
	},
	methods: {
		async checkShopifyConnection() {
			try {
				const response = await fetch(`${URL_DOMAIN}/integration/shopify/connected`, {
					method: 'GET',
					headers: {
						'Content-Type': 'application/json',
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
					}
				});
				if (!response.ok) {
					throw new Error('Failed to check Shopify connection');
				}
				const data = await response.json();
				this.shopifyConnected = data.connected;
			} catch (error) {
				console.error('Error checking Shopify connection:', error);
				this.shopifyConnected = false;
			}
		},
		replaceCurrencyTagsSync(text) {
			return CurrencyUtils.replaceCurrencyTagsSync(text);
		},
		editPointsProgram() {
			if(this.ppdSummaryData[0]?.id)
			{
				this.$router.push(`/campaign/${this.campaignId}/new-points/edit/${this.ppdSummaryData[0]?.id}?foundational=true`);
			}
			else {
				this.addPoints();
			}
		},
		scrollToItem(itemId) {
			this.$nextTick(() => {
				const itemElement = document.getElementById(`${itemId}`);
				if (itemElement) {
					itemElement.scrollIntoView({ behavior: 'auto', block: 'center' });
				}
			});
		},
		async addRecommendedShopItem(shopItem, index) {
			this.$router.push(
				`/campaign/${this.campaignId}/new-shop-reward/edit/${shopItem.id}?foundational=true&recommended=true`
			);
		},
		async ignoreRecommendedShopItem(shopItem, index) {
			this.rewardRecommendations[index].fadeOut = true;
			let whereClause = {
				id: shopItem.id
			}
			let payload = {
				recommendationState: RECOMMENDATION_STATES.IGNORED_RECOMMENDATION,
				active: false
			}
			const queryString = `where=${encodeURIComponent(JSON.stringify(whereClause))}`;
			const response = await fetch(`${URL_DOMAIN}/loyalty-campaigns/${this.campaignId}/loyalty-redemption-shop-items?${queryString}`, {
				method: 'PATCH',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify(payload),
			});

			if (response.ok) {
				//Show success message
				await this.fetchCampaignSummary();
				customerIOTrackEvent('Ignored Recommended Reward');

				this.status.type = 'success';
				this.status.message = 'Reward has been Ignored';
			}
			else {
				//Show error message
				this.status.type = 'fail';
				this.status.message = 'Failed to Ignore. Please try again.';
			}
		},
		async addRecommendedWTE(wte, index) {
			this.$router.push(
				`/campaign/${this.campaignId}/new-ways-to-earn/edit/${wte.id}?foundational=true&recommended=true`
			);
		},
		async ignoreRecommendedWTE(wte, index) {
			this.wteRecommendations[index].fadeOut = true;
			let whereClause = {
				id: wte.id
			}
			let payload = {
				recommendationState: RECOMMENDATION_STATES.IGNORED_RECOMMENDATION,
				active: false
			}
			const queryString = `where=${encodeURIComponent(JSON.stringify(whereClause))}`;
			const response = await fetch(`${URL_DOMAIN}/loyalty-campaigns/${this.campaignId}/loyalty-earns?${queryString}`, {
				method: 'PATCH',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify(payload),
			});

			if (response.ok) {
				//Show success message
				await this.fetchCampaignSummary();
				customerIOTrackEvent('Ignored Recommended WTE');

				this.status.type = 'success';
				this.status.message = 'WTE has been Ignored';
			}
			else {
				//Show error message
				this.status.type = 'fail';
				this.status.message = 'Failed to Ignore. Please try again.';
			}
		},
		handlePointsSummaryEdit(clickData) {
			this.$router.push(`/campaign/${this.campaignId}/new-points/edit/${clickData.id}?foundational=true`);
		},
		handleWTESummaryEdit(clickData) {
			this.$router.push(`/campaign/${this.campaignId}/new-ways-to-earn/edit/${clickData.id}?foundational=true`);
		},
		handleWTEDelete(clickData, type) {
			this.toDelete = clickData;
			this.deleteModalText = `Are you sure you want to delete ${this.toDelete.title}?`;
			this.deleteModalOpen = true;
			this.deleteType = type;
		},
		async deleteWTE() {
			this.isDeleting = true;
			const response = await deleteWteOrShopItem(this.deleteType, this.toDelete.id);
			if (response.status == 'success') {
				this.status.type = 'success';
				this.status.message = `${this.toDelete.title} has been deleted.`;
				await this.fetchCampaignSummary();
				customerIOTrackEvent('Deleted Shop Item');
			} else {
				this.status.type = 'fail';
				this.status.message = `Failed to delete ${this.toDelete.title}. Please try again.`;
			}
			this.deleteModalOpen = false;
			this.deleteModalText = '';
			this.toDelete = {};
			this.deleteType = '';
			this.isDeleting = false;
		},
		async handleWTESummaryToggle(clickData) {
			let whereClause = {
				id: clickData.id
			}
			let payload = {
				active: clickData.toggle
			}
			const queryString = `where=${encodeURIComponent(JSON.stringify(whereClause))}`;
			const response = await fetch(`${URL_DOMAIN}/loyalty-campaigns/${this.campaignId}/loyalty-earns?${queryString}`, {
				method: 'PATCH',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify(payload),
			});

			if (response.ok) {
				//Show success message
				await this.fetchCampaignSummary();
				if (clickData.toggle)
					customerIOTrackEvent('WTE Is Live');
				else
					customerIOTrackEvent('WTE Is Hidden');

				this.status.type = 'success';
				this.status.message = clickData.toggle ? 'If your program is live, WTE is now visible to your shoppers.' : 'WTE is now hidden from your shoppers.';
			}
			else {
				//Show error message
				this.status.type = 'fail';
				this.status.message = 'Failed to update. Please try again.';
			}
		},
		async updatePriorityEarn(clickData) {
			const response = await fetch(`${URL_DOMAIN}/loyalty-campaigns/${this.campaignId}/bulk-update-loyalty-earns`, {
				method: 'PATCH',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify(clickData),
			});

			if (response.ok) {
				this.status.type = 'success';
				this.status.message = 'Sort order saved.';
			}
			else {
				this.status.type = 'fail';
				this.status.message = 'Failed to update sort order. Please try again.';
			}
		},
		async updatePriorityReward(clickData) {
			const response = await fetch(`${URL_DOMAIN}/loyalty-campaigns/${this.campaignId}/bulk-update-loyalty-redemption-shop-items`, {
				method: 'PATCH',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify(clickData),
			});

			if (response.ok) {
				this.status.type = 'success';
				this.status.message = 'Sort order saved.';
			}
			else {
				this.status.type = 'fail';
				this.status.message = 'Failed to update sort order. Please try again.';
			}
		},
		handleShopItemSummaryEdit(clickData) {
			this.$router.push(`/campaign/${this.campaignId}/new-shop-reward/edit/${clickData.id}?foundational=true`);
		},
		async handleShopItemSummaryToggle(clickData) {
			let whereClause = {
				id: clickData.id
			}
			let payload = {
				active: clickData.toggle
			}
			const queryString = `where=${encodeURIComponent(JSON.stringify(whereClause))}`;
			const response = await fetch(`${URL_DOMAIN}/loyalty-campaigns/${this.campaignId}/loyalty-redemption-shop-items?${queryString}`, {
				method: 'PATCH',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify(payload),
			});

			if (response.ok) {
				//Show success message
				await this.fetchCampaignSummary();
				if (clickData.toggle)
					customerIOTrackEvent('Reward Is Live');
				else
					customerIOTrackEvent('Reward Is Hidden');

				this.status.type = 'success';
				this.status.message = clickData.toggle ? 'If your Program is live, Reward is now visible to your shoppers.' : 'Reward is now hidden from your shoppers.';
			}
			else {
				//Show error message
				this.status.type = 'fail';
				this.status.message = 'Failed to update. Please try again.';
			}
		},
		addPoints() {
			customerIOTrackEvent('Started to Add Points');
			this.$router.push(`/campaign/${this.campaignId}/new-points/add?foundational=true`);
		},
		addWayToEarn() {
			customerIOTrackEvent('Started to Add Way to Earn');
			this.$router.push(`/campaign/${this.campaignId}/new-ways-to-earn/add?foundational=true`);
		},
		addReward() {
			customerIOTrackEvent('Started to Add Reward');
			this.$router.push(`/campaign/${this.campaignId}/new-shop-reward/add?foundational=true`);
		},
		async fetchCampaign() {
			this.isLoading = true;
			const response = await fetch(`${URL_DOMAIN}/loyalty-campaigns/foundational`, {
				method: 'GET',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				}
			});

			if (response.ok && response.status >= 200 && response.status < 300) {
				const result = await response.json();
				this.campaignId = result.id;

				this.campaign = {
					...result,
					startdate: detimeify(result.startdate),
					enddate: detimeify(result.enddate),
				};
				this.targetSegment = result.loyaltySegment;
				this.isLoading = false;
			}
		},
		async fetchCampaignSummary() {
			this.isLoading = true;
			const response = await fetch(`${URL_DOMAIN}/loyalty-campaigns/${this.campaignId}/summary`, {
				method: 'GET',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				}
			});
			if (response.ok && response.status >= 200 && response.status < 300) {
				const result = await response.json();
				this.wteSummaryData = result?.summary?.wtes;
				this.ppdSummaryData = result?.summary?.ppds;
				this.rewardSummaryData = result?.summary?.rewards;
				this.isWTELoading = false;
				this.isRewardLoading = false;
				this.wteRecommendations = result?.summary?.recommendedWTEs;
				this.rewardRecommendations = result?.summary?.recommendedShopItems;

				this.summaryText = [];
				for (let i = 0; i < this.wteSummaryData?.length; i++) {
					if (this.wteSummaryData[i].toggle) {
						this.summaryText.push(this.wteSummaryData[i].longSummary)
					}

				}

				for (let i = 0; i < this.rewardSummaryData?.length; i++) {
					if (this.rewardSummaryData[i].toggle) {
						this.summaryText.push(this.rewardSummaryData[i].longSummary)
					}
				}

				if (this.summaryText.length == 0) {
					this.summaryText.push('Apply AI recommendations or craft your own!')
				}
			}
			this.isLoading = false;
		},
		async fetchWte() {
			const wteResponse = await fetch(`${URL_DOMAIN}/ui-loyalty-earn-details/${this.campaignId}`, {
				method: 'GET',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
			});

			if (wteResponse.ok && wteResponse.status >= 200 && wteResponse.status < 300) {
				const result = await wteResponse.json();
				console.log('Ways to earn', result);

				this.wteData = result.filter(x => x?.earnConditions?.[0]?.type != 'dollar-spent').map((item) => {
					return {
						name: item.name,
						active: item.active,
						conditions: item.earnConditions ? item.earnConditions.length : 0,
						effects: item.earnEffects ? item.earnEffects.length : 0,
						priority: item.priority,
						href: `/campaign/${this.campaignId}/new-ways-to-earn/edit/${item.id}?foundational=true`,
						target: '_top'
					};
				});
			}
		},
		async fetchShopRewards() {
			const rewardShopResposne = await fetch(`${URL_DOMAIN}/loyalty-campaigns/${this.campaignId}/loyalty-redemption-shop-items`, {
				method: 'GET',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
			});

			if (rewardShopResposne.ok && rewardShopResposne.status >= 200 && rewardShopResposne.status < 300) {
				const result = await rewardShopResposne.json();

				this.rewardShopData = result.map((item) => {
					return {
						name: item.name,
						active: item.active,
						href: `/campaign/${this.campaignId}/new-shop-reward/edit/${item.id}?foundational=true`,
						target: '_top',
						description: item.description,
						priority: item.priority,
						// conditions: item.conditions?.map((condition) => `${condition.type}: ${condition.value}`).join(', ') || '',
						// rewards: item.rewards?.map((reward) => `${reward.type}: ${reward.value}`).join(', ') || '',
					};
				});
			}
		},
	},
}

function detimeify(date) {
	if (!date || !date.includes('T')) {
		return date;
	}

	return date.split('T')[0];
}
</script>
<style scoped>
.rotate-90 {
	transform: rotate(90deg);
	transition: transform 0.3s;
}

.test-container {
	margin: 0 -1em;
}

.test {
	width: 50%;
	padding-right: 0.4em;
}

@media all and (max-width: 768px) {
	.test-container {
		width: 100%;
		margin: 0;
		margin-left: -0.5em;
	}

	.test {
		width: 100%;
		padding: 0;
		/* padding-left: 0.4em; */
	}
}

.campaign-name {
	font-family: 'Inter', sans-serif;
	font-style: normal;
	font-weight: 500;
	font-size: 40px;
	line-height: 65px;
	/* identical to box height, or 162% */
	letter-spacing: -0.04em;
	text-transform: uppercase;

	color: #5E48F8;
	/* -webkit-background-clip: text; */
	/* -webkit-text-fill-color: transparent; */
	/* background-clip: text; */
	/* text-fill-color: transparent; */
}

.no-focus-outline {
	box-shadow: none !important;
	outline: none !important;
}

.accordion-animation-enter-active,
.accordion-animation-leave-active {
	transition: opacity 0.5s, transform 0.5s;
	overflow: hidden;
}

.accordion-animation-enter,
.accordion-animation-leave-to {
	opacity: 0;
	transform: scaleY(0);
}

.accordion-animation-enter-to,
.accordion-animation-leave {
	opacity: 1;
	transform: scaleY(1);
}

.accordion-body {
	transition: max-height 0.5s ease-in-out, opacity 0.5s ease-in-out;
}

.fade-enter-active,
.fade-leave-active {
	transition-property: opacity, transform;
	transition-duration: 500ms;
	transition-timing-function: ease-in-out;
}

.fade-enter-from,
.fade-leave-to {
	opacity: 0;
	transform: translateX(10px);
}

.fade-enter-to,
.fade-leave-from {
	opacity: 1;
	transform: translateX(0);
}

.fade-out-transition {
	transition: opacity 0.5s ease-out;
}

.fade-out {
	opacity: 0;
}
</style>

