p<template>
	<div class="p-2 sm:p-7 mr-24 flex flex-col items-center justify-center" v-if="!isFeatureAvailable">
		<img src="../images/ai_segment_upsell.jpg" width="800">
		<a class="text-ralprimary-dark font-bold md:text-xl lg:text-2xl sm:text-normal mt-4 font-[Inter]" href="/loyalty/settings/plans">AI Segmentation</a>
		<p class="text-ralblack-primary text-lg mt-4 flex items-center font-[Inter] mb-4 w-1/2 text-center">
			Our AI segmentation helps you create faster, higher performing segments by analyzing your customer data and identifying patterns.
		</p>
		<PrimaryButton
			cta="Upgrade Loyalty Plan"
			size="xs"
			@click="() => this.$router.push('/loyalty/settings/plans')"
		/>
	</div>

	<div class="bg-gradient-to-tr from-[#4F3EAC] to-[#5E48F8] p-4 w-full justify-center shadow-lg flex items-center" v-if="!isFeatureAvailable && isTrialEnded">
	       <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
	           <path d="M9 14H11V9.8L12.6 11.4L14 10L10 6L6 10L7.4 11.4L9 9.8V14ZM10 20C8.61667 20 7.31667 19.7375 6.1 19.2125C4.88333 18.6875 3.825 17.975 2.925 17.075C2.025 16.175 1.3125 15.1167 0.7875 13.9C0.2625 12.6833 0 11.3833 0 10C0 8.61667 0.2625 7.31667 0.7875 6.1C1.3125 4.88333 2.025 3.825 2.925 2.925C3.825 2.025 4.88333 1.3125 6.1 0.7875C7.31667 0.2625 8.61667 0 10 0C11.3833 0 12.6833 0.2625 13.9 0.7875C15.1167 1.3125 16.175 2.025 17.075 2.925C17.975 3.825 18.6875 4.88333 19.2125 6.1C19.7375 7.31667 20 8.61667 20 10C20 11.3833 19.7375 12.6833 19.2125 13.9C18.6875 15.1167 17.975 16.175 17.075 17.075C16.175 17.975 15.1167 18.6875 13.9 19.2125C12.6833 19.7375 11.3833 20 10 20ZM10 18C12.2333 18 14.125 17.225 15.675 15.675C17.225 14.125 18 12.2333 18 10C18 7.76667 17.225 5.875 15.675 4.325C14.125 2.775 12.2333 2 10 2C7.76667 2 5.875 2.775 4.325 4.325C2.775 5.875 2 7.76667 2 10C2 12.2333 2.775 14.125 4.325 15.675C5.875 17.225 7.76667 18 10 18Z" fill="#FFA3DF"/>
	       </svg>
	       <div class="text-white ml-2">Your trial has expired and your program has been deactivated.</div>
               <div class="px-2 py-2 text-white text-sm border-[#FFF] border rounded-full ml-4 hover:text-black hover:bg-[#FFF] hover:cursor-pointer transitional-bg delay-200" @click="() => this.$router.push('/loyalty/settings/plans')">
                   Choose a Plan
               </div>
           </div>

           <div class="flex flex-col p-6 bg-white shadow-sm sm:flex-row items-center justify-between relative" v-if="isFeatureAvailable || isTrialEnded">
	       <div class="flex-grow">
	   		<div class="text-3xl sm:text-3xl font-sans font-medium opacity-70 text-ralblack-primary mb-2">
	   			AI Segments
	   		</div>

	   		<div class="flex mb-2">
	   			<span class="text-neutral-800 text-opacity-80 text-sm font-medium">AI Segmentation is used to create faster, higher performing segments.</span>
	   			<span class="text-neutral-500 text-sm font-medium">&nbsp;</span>
	   			<LearnMoreText text="Learn more" url="https://docs.raleon.io/docs/what-is-ai-segmentation"></LearnMoreText>
	   		</div>

	   			<!-- Place the <data-progress/> component here -->
	   			<data-progress class="mt-2" />
	   		</div>
                        <div class="absolute top-6 right-6" v-if="loading == 'false' && isShopifyConnected">
	   			<PrimaryButton cta="Create Segment" size="xs" @click="createSegment" />
	   		</div>
	   	</div>

        <div class="p-2 sm:p-7">
        <div class="min-h-screen" v-if="loading == 'true' && isShopifyConnected">
		<!-- Main Content Area -->
		<div class="max-w-6xl">
		<div class="space-y-6">
			<!-- Progress Indicator -->
			<div class="bg-white rounded-lg p-8 shadow-sm border border-purple-100">
			<div class="max-w-md mx-auto space-y-6">
				<!-- Pulsing circle -->
				<div class="flex justify-center">
				<div class="relative">
					<!-- Background pulse circles -->
					<div class="w-16 h-16 bg-purple-700 rounded-full animate-pulse opacity-20"></div>
					<div class="absolute inset-0 w-16 h-16 bg-purple-500 rounded-full animate-ping opacity-20"></div>
					<div class="absolute inset-0 w-16 h-16 border-4 border-purple-300 rounded-full animate-pulse"></div>

					<!-- Robot emoji with fade animation -->
					<div class="absolute inset-0 flex items-center justify-center">
					<span class="text-2xl robot-fade">🤖</span>
					</div>
				</div>
				</div>

				<!-- Loading text -->
				<div class="text-center space-y-2">
					<div class="h-7 relative"> <!-- Fixed height container to prevent layout shift -->
						<transition
						name="fade-message"
						mode="out-in"
						>
						<h2
							:key="currentMessageIndex"
							class="text-lg font-medium text-gray-900 absolute w-full"
						>
							{{ currentLoadingMessage }}
						</h2>
						</transition>
					</div>
					<p class="text-sm text-gray-500">We're processing your data to create intelligent segments</p>
				</div>

				<!-- Progress bar -->
				<div class="relative h-1 bg-purple-100 rounded-full overflow-hidden">
				<div class="absolute inset-y-0 left-0 bg-purple-700 w-1/3 progress-bar"></div>
				</div>
			</div>
			</div>

			<!-- Skeleton Segments -->
				<div class="flex flex-col">
					<div
						v-for="i in 3"
						:key="i"
						class="bg-white rounded-lg p-6 border border-purple-100 shadow-sm mb-4"
						:style="{
						animation: `fadeIn 0.6s ease-out ${i * 0.2}s both`
						}"
					>
						<!-- Skeleton header -->
						<div class="space-y-4">
							<div class="h-4 bg-purple-100 rounded-full w-2/3 animate-pulse"></div>
							<div class="h-3 bg-purple-50 rounded-full w-1/2 animate-pulse"></div>
						</div>

						<!-- Skeleton stats -->
						<div class="mt-6 grid grid-cols-2 gap-4">
							<div class="space-y-2">
								<div class="h-3 bg-purple-50 rounded-full w-3/4 animate-pulse"></div>
								<div class="h-4 bg-purple-100 rounded-full w-1/2 animate-pulse"></div>
							</div>
							<div class="space-y-2">
								<div class="h-3 bg-purple-50 rounded-full w-3/4 animate-pulse"></div>
								<div class="h-4 bg-purple-100 rounded-full w-1/2 animate-pulse"></div>
							</div>
						</div>
					</div>
				</div>
		</div>
		</div>
  </div>

                <div class="border-b border-gray-300 mb-6" v-if="isFeatureAvailable">
                        <div class="mb-2 max-w-2xl" v-if="loading == 'false' && isShopifyConnected">
				<div class="relative group">
					<div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none text-gray-400">
					<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
						<circle cx="11" cy="11" r="8"></circle>
						<line x1="21" y1="21" x2="16.65" y2="16.65"></line>
					</svg>
					</div>
					<input
					type="text"
					v-model="searchQuery"
					placeholder="Search segments by name or description..."
					class="w-full pl-12 pr-4 py-3 text-base rounded-xl border-0 bg-white shadow-sm ring-1 ring-gray-200 transition-shadow duration-200 placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 hover:ring-gray-300"
					/>
				</div>
			</div>

			<div class="flex space-x-8">

				<div class="whitespace-nowrap py-4 px-1 font-medium text-sm focus:outline-none hover:border-b-2 hover:text-ralprimary-light hover:border-ralprimary-light transition-all duration-300 cursor-pointer"
				@click.stop="this.overview = true; this.archived = false;"
				:class="{'text-ralprimary-light border-ralprimary-light border-b-2 ': this.overview == true}"
				>
					Active
				</div>

				<div class="whitespace-nowrap py-4 px-1 border-b-1 font-medium text-sm focus:outline-none hover:border-b-2 hover:text-ralprimary-light hover:border-ralprimary-light transition-all duration-300 cursor-pointer"
				@click.stop="this.overview = false; this.archived = true;"
				:class="{'text-ralprimary-light border-ralprimary-light border-b-2 ': this.archived == true}"
				>
					Archived
				</div>
			</div>
		</div>
	<div class="relative shadow rounded-xl">
	    <div class="absolute -inset-1 bg-gradient-to-r from-purple-200/20 via-purple-300/20 to-purple-200/20 rounded-xl blur-xl opacity-50" />
		<div class="absolute -inset-1 bg-gradient-to-r from-purple-200/20 via-purple-300/20 to-purple-200/20 rounded-xl blur-xl opacity-50" />
		<div class="relative rounded-xl overflow-hidden backdrop-blur-sm border border-white/40">
		<div class="absolute inset-0 bg-gradient-to-br from-white/80 via-purple-50/80 to-white/90" />

			<div class="relative p-6">
				<!-- Carousel Slides -->
				<div class="overflow-hidden">
				<div
					class="flex transition-transform duration-300 ease-in-out"
					:style="{ transform: `translateX(-${currentSlide * 100}%)` }"
				>
					<div
					v-for="(slide, index) in slides"
					:key="index"
					class="w-full flex-shrink-0"
					>
					<div class="flex justify-between items-start">
						<div class="flex-1">
						<div class="flex items-center space-x-2 mb-1">
							<div class="px-2 py-1 rounded-full bg-purple-100 text-purple-600 text-xs font-medium backdrop-blur-sm">
							{{ slide.tag }}
							</div>
							<span class="text-sm text-gray-500">{{ slide.subtitle }}</span>
						</div>
						<h3 class="text-lg font-medium mb-2">{{ slide.title }}</h3>
						<p class="text-gray-600 text-sm mb-4 max-w-3xl">
							{{ slide.description }}
						</p>
						<div class="flex items-center space-x-6">
							<div class="flex items-center space-x-2 cursor-pointer" @click.stop="guideModelOpen = true; videoGuide = slide.videoKey; customerIOTrackEvent('AI Segment Videos');">
							<svg xmlns="http://www.w3.org/2000/svg" height="32px" viewBox="0 -960 960 960" width="32px" fill="#5818C7"><path d="m426-330 195-125q14-9 14-25t-14-25L426-630q-15-10-30.5-1.5T380-605v250q0 18 15.5 26.5T426-330Zm54 250q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-80q134 0 227-93t93-227q0-134-93-227t-227-93q-134 0-227 93t-93 227q0 134 93 227t227 93Zm0-320Z"/></svg>
							<span class="text-sm text-ralprimary-main underline">{{ slide.cta }}</span>
							</div>
						</div>
						</div>
					</div>
					</div>
				</div>
				</div>

				<!-- Pagination Dots -->
				<div class="flex justify-center space-x-2 mt-4">
				<button
					v-for="(slide, index) in slides"
					:key="index"
					class="w-2 h-2 rounded-full transition-colors"
					:class="{
					'bg-purple-600': currentSlide === index,
					'bg-purple-300': currentSlide !== index,
					}"
					@click="goToSlide(index)"
				></button>
				</div>

				<!-- Next Arrow -->
				<button
				@click="nextSlide"
				class="absolute right-6 top-1/2 transform -translate-y-1/2 p-2 bg-white/80 rounded-full shadow-md hover:bg-white transition-colors"
				>
				<svg xmlns="http://www.w3.org/2000/svg" height="20px" viewBox="0 -960 960 960" width="20px" fill="#5818C7">
					<path d="M504-480 320-664l56-56 240 240-240 240-56-56 184-184Z"/>
				</svg>
				</button>
			</div>
		</div>
  </div>

		<!-- Connect Shopify message when not connected -->
		<div class="bg-white rounded-xl border border-purple-100 p-6 mt-6" v-if="isFeatureAvailable && !isShopifyConnected">
			<div class="flex items-center justify-between">
				<div class="flex-1">
					<div class="flex items-center space-x-3 mb-3">
						<img src="https://cdn.shopify.com/shopifycloud/brochure/assets/brand-assets/shopify-logo-primary-logo-456baa801ee66a0a435671082365958316831c9960c480451dd0330bcdae304f.svg" width="120" height="34" alt="Shopify">
						<div class="px-2 py-1 rounded-full bg-orange-100 text-orange-600 text-xs font-medium">
							Integration Required
						</div>
					</div>
					<h3 class="text-lg font-medium text-gray-900 mb-2">Connect Shopify for AI Segments</h3>
					<p class="text-gray-600 text-sm mb-4">
						To create AI-driven segments, connect your Shopify store to sync customer data and unlock intelligent segmentation.
					</p>
				</div>
				<div class="ml-6">
					<PrimaryButton
						cta="Connect Shopify"
						size="xs"
						@click="() => this.$router.push('/integrations')"
					/>
				</div>
			</div>
		</div>

		<div class="" v-if="this.overview || this.archived">

			<!-- <data-progress class="mt-4"></data-progress> -->

			<!-- Onboarding Card
			<div class="relative shadow rounded-xl">
				<div class="absolute -inset-1 bg-gradient-to-r from-purple-200/20 via-purple-300/20 to-purple-200/20 rounded-xl blur-xl opacity-50" />
				<div class="relative rounded-xl overflow-hidden backdrop-blur-sm border border-white/40">
				<div class="absolute inset-0 bg-gradient-to-br from-white/80 via-purple-50/80 to-white/90" />

				<div class="absolute left-0 top-0 bottom-0 w-[3px] bg-gradient-to-b from-purple-400 to-purple-600" />

				<div class="relative p-6">
					<div class="flex justify-between items-start">
					<div class="flex-1">
						<div class="flex items-center space-x-2 mb-1">
						<div class="px-2 py-1 rounded-full bg-purple-100 text-purple-600 text-xs font-medium backdrop-blur-sm">
							Guide
						</div>
						<span class="text-sm text-gray-500">Recommended first step</span>
						</div>

						<h3 class="text-lg font-medium mb-2">Set up your AI segmented abandoned cart flow</h3>
						<p class="text-gray-600 text-sm mb-4 max-w-3xl">
						Learn how to use predictive segments to automatically recover more abandoned carts and increase revenue
						</p>

						<div class="flex items-center space-x-6">

						<div class="flex items-center space-x-2">
							<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5818C7"><path d="m426-330 195-125q14-9 14-25t-14-25L426-630q-15-10-30.5-1.5T380-605v250q0 18 15.5 26.5T426-330Zm54 250q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-80q134 0 227-93t93-227q0-134-93-227t-227-93q-134 0-227 93t-93 227q0 134 93 227t227 93Zm0-320Z"/></svg>
							<span class="text-sm text-gray-500">5 min setup</span>
						</div>
						</div>
					</div>

					<div class="flex items-center space-x-4">
						<button
						class="flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors shadow-sm"
						>
						<span>Get Started</span>
						<svg xmlns="http://www.w3.org/2000/svg" height="18px" viewBox="0 -960 960 960" width="18px" fill="#FFFFFF"><path d="M647-440H200q-17 0-28.5-11.5T160-480q0-17 11.5-28.5T200-520h447L451-716q-12-12-11.5-28t12.5-28q12-11 28-11.5t28 11.5l264 264q6 6 8.5 13t2.5 15q0 8-2.5 15t-8.5 13L508-188q-11 11-27.5 11T452-188q-12-12-12-28.5t12-28.5l195-195Z"/></svg>
						</button>

						<button
						@click="isVisible = false"
						class="p-1 hover:bg-white/40 rounded-full transition-colors"
						>
							<svg xmlns="http://www.w3.org/2000/svg" height="21px" viewBox="0 -960 960 960" width="21px" fill="#9ca3af"><path d="M480-424 284-228q-11 11-28 11t-28-11q-11-11-11-28t11-28l196-196-196-196q-11-11-11-28t11-28q11-11 28-11t28 11l196 196 196-196q11-11 28-11t28 11q11 11 11 28t-11 28L536-480l196 196q11 11 11 28t-11 28q-11 11-28 11t-28-11L480-424Z"/></svg>
						</button>
					</div>
					</div>
				</div>
				</div>
			</div>
			-->

                        <div class="mt-6" v-if="loading == 'false' && segments?.length == 0 && isShopifyConnected">
				<div class="flex flex-col">
					<div
						v-for="i in 3"
						:key="i"
						class="bg-white rounded-lg p-6 border border-purple-100 shadow-sm mb-4"
						:style="{
						animation: `fadeIn 0.6s ease-out ${i * 0.2}s both`
						}"
					>
						<!-- Skeleton header -->
						<div class="space-y-4">
							<div class="h-4 bg-purple-100 rounded-full w-2/3 animate-pulse"></div>
							<div class="h-3 bg-purple-50 rounded-full w-1/2 animate-pulse"></div>
						</div>

						<!-- Skeleton stats -->
						<div class="mt-6 grid grid-cols-2 gap-4">
							<div class="space-y-2">
								<div class="h-3 bg-purple-50 rounded-full w-3/4 animate-pulse"></div>
								<div class="h-4 bg-purple-100 rounded-full w-1/2 animate-pulse"></div>
							</div>
							<div class="space-y-2">
								<div class="h-3 bg-purple-50 rounded-full w-3/4 animate-pulse"></div>
								<div class="h-4 bg-purple-100 rounded-full w-1/2 animate-pulse"></div>
							</div>
						</div>
					</div>
				</div>
			</div>

                        <div class="mt-6" v-if="loading == 'false' && isShopifyConnected">
				<!-- Segment card -->
				<div class="bg-white rounded-2xl p-4 border shadow border-ralprimary-light border-opacity-50 hover:border-opacity-90 hover:shadow-lg hover:cursor-pointer mt-4 flex-grow transition-all duration-300 ease-in-out overflow-visible segment-card relative"

				v-for="(segment, index) in filteredSegments"
				@click="this.$router.push('/ai-segments/segments/' + segment.id);"
				:style="{
					animationDelay: `${index * 100}ms`,
				}"
				:class="{
					'z-50': isSignalPopoverOpen(index),
					'z-10': !isSignalPopoverOpen(index),
				}"
				>
					<!-- header area -->
					<div class="flex justify-between items-start">
						<div class="">
							<div class="text-xl font-semibold">{{ segment.name }}</div>
							<div class="mt-2 text-sm w-2/3">
								{{ segment.description }}
							</div>
						</div>

						<div class="flex items-center">
							<!-- <span class="inline-flex items-center gap-1 px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-50 text-orange-700 border border-orange-200"
							v-if="(emailPixelTrackingEnabled == 'true' && segment.emailopenrate == '0%') || segment.profileCount == 0">
								<span class="relative flex h-2 w-2">
								<span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-orange-800 opacity-75"></span>
								<span class="relative inline-flex rounded-full h-2 w-2 bg-orange-800"></span>
								</span>
								No sends detected yet
							</span> -->

							<div class="mr-4 flex items-center" v-if="emailPixelTrackingEnabled == 'true' && segment.emailopenrate != '0%' && segment.profileCount != 0">
								<svg width="20" height="16" viewBox="0 0 20 16" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path d="M2 16C1.45 16 0.979167 15.8042 0.5875 15.4125C0.195833 15.0208 0 14.55 0 14V2C0 1.45 0.195833 0.979167 0.5875 0.5875C0.979167 0.195833 1.45 0 2 0H18C18.55 0 19.0208 0.195833 19.4125 0.5875C19.8042 0.979167 20 1.45 20 2V14C20 14.55 19.8042 15.0208 19.4125 15.4125C19.0208 15.8042 18.55 16 18 16H2ZM10 9L2 4V14H18V4L10 9ZM10 7L18 2H2L10 7ZM2 4V2V14V4Z" fill="#565656"/>
								</svg>

								<span class="text-sm font-bold text-gray-500 ml-2">
								{{segment.emailopenrate}}
								</span>
							</div>

							<div class="mr-4 flex items-center" v-if="segment.sending">
								<svg width="18" height="17" viewBox="0 0 18 17" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path d="M1 7H2C2.28333 7 2.52083 7.09583 2.7125 7.2875C2.90417 7.47917 3 7.71667 3 8C3 8.28333 2.90417 8.52083 2.7125 8.7125C2.52083 8.90417 2.28333 9 2 9H1C0.716667 9 0.479167 8.90417 0.2875 8.7125C0.0958333 8.52083 0 8.28333 0 8C0 7.71667 0.0958333 7.47917 0.2875 7.2875C0.479167 7.09583 0.716667 7 1 7ZM2.7 12.4L3.4 11.7C3.6 11.5 3.83333 11.4042 4.1 11.4125C4.36667 11.4208 4.6 11.5167 4.8 11.7C5 11.9 5.10417 12.1375 5.1125 12.4125C5.12083 12.6875 5.025 12.925 4.825 13.125L4.125 13.825C3.925 14.025 3.6875 14.1208 3.4125 14.1125C3.1375 14.1042 2.9 14 2.7 13.8C2.51667 13.6 2.42083 13.3667 2.4125 13.1C2.40417 12.8333 2.5 12.6 2.7 12.4ZM3.4 4.3L2.7 3.6C2.5 3.4 2.40417 3.16667 2.4125 2.9C2.42083 2.63333 2.51667 2.4 2.7 2.2C2.9 2 3.1375 1.89583 3.4125 1.8875C3.6875 1.87917 3.925 1.975 4.125 2.175L4.825 2.875C5.025 3.075 5.12083 3.3125 5.1125 3.5875C5.10417 3.8625 5 4.1 4.8 4.3C4.6 4.48333 4.36667 4.57917 4.1 4.5875C3.83333 4.59583 3.6 4.5 3.4 4.3ZM15.3 16.3L11.25 12.25L10.5 14.5C10.4667 14.6167 10.4042 14.7042 10.3125 14.7625C10.2208 14.8208 10.125 14.85 10.025 14.85C9.925 14.85 9.82917 14.8167 9.7375 14.75C9.64583 14.6833 9.58333 14.5917 9.55 14.475L7.4 7.325C7.36667 7.19167 7.37083 7.05833 7.4125 6.925C7.45417 6.79167 7.51667 6.68333 7.6 6.6C7.68333 6.51667 7.79167 6.45417 7.925 6.4125C8.05833 6.37083 8.19167 6.36667 8.325 6.4L15.525 8.55C15.6417 8.58333 15.7292 8.64583 15.7875 8.7375C15.8458 8.82917 15.875 8.925 15.875 9.025C15.875 9.125 15.85 9.22083 15.8 9.3125C15.75 9.40417 15.6667 9.46667 15.55 9.5L13.3 10.3L17.3 14.3C17.5 14.5 17.6 14.7333 17.6 15C17.6 15.2667 17.5 15.5 17.3 15.7L16.7 16.3C16.5 16.5 16.2667 16.6 16 16.6C15.7333 16.6 15.5 16.5 15.3 16.3ZM8 2V1C8 0.716667 8.09583 0.479167 8.2875 0.2875C8.47917 0.0958333 8.71667 0 9 0C9.28333 0 9.52083 0.0958333 9.7125 0.2875C9.90417 0.479167 10 0.716667 10 1V2C10 2.28333 9.90417 2.52083 9.7125 2.7125C9.52083 2.90417 9.28333 3 9 3C8.71667 3 8.47917 2.90417 8.2875 2.7125C8.09583 2.52083 8 2.28333 8 2ZM13.175 2.875L13.9 2.15C14.0833 1.96667 14.3125 1.87083 14.5875 1.8625C14.8625 1.85417 15.1 1.95 15.3 2.15C15.4833 2.33333 15.5792 2.5625 15.5875 2.8375C15.5958 3.1125 15.5083 3.35 15.325 3.55L14.6 4.3C14.4167 4.5 14.1875 4.59583 13.9125 4.5875C13.6375 4.57917 13.4 4.48333 13.2 4.3C13 4.1 12.8958 3.8625 12.8875 3.5875C12.8792 3.3125 12.975 3.075 13.175 2.875Z" fill="#5F6368"/>
								</svg>

								<span class="text-sm font-bold text-gray-500 ml-2">
								{{segment.emailctr}}
								</span>
							</div>

							<!--
							<div class="inline-flex items-center px-3 py-1 rounded-full border border-green-500 bg-green-50">
								<div class="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
								<span class="text-sm font-medium text-gray-700">Optimized</span>
							</div>
							-->
						</div>
					</div>

					<!-- stat area -->
					<div class="flex items-center justify-between mt-4">

						<!-- <div class="">
							<div class="inline-flex items-center px-3 py-1 rounded-full border border-gray-100 bg-gray-100">
								<span class="text-sm font-medium text-gray-500">{{segment.tag}}</span>
							</div>
						</div> -->


						<div class="flex items-center gap-2">
							<!-- <div class="inline-flex items-center px-4 py-1.5 border rounded-full text-sm font-medium cursor-pointer mr-2 transition-all duration-200 hover:shadow-md bg-green-100 text-green-800 border-green-200 hover:bg-green-50">{{segment.tag}}</div> -->

							<div
								v-if="segment?.organizationSegmentDetails" class="relative popover-container flex"
							>

								<div v-if="segment?.organizationSegmentDetails?.length" class="flex flex-col">
									<SignalBadge
										:signal="segment?.organizationSegmentDetails?.[0]?.metricSegment"
										:showNub="false"
										:margin="false"
										section="test"
									></SignalBadge>
									<!-- <div class="items-center px-4 py-1.5 border rounded-full text-sm font-medium cursor-pointer mb-2 mr-2 inline-block transition-all duration-200 hover:shadow-md bg-green-100 text-green-800 border-green-200 hover:bg-green-50">{{ segment?.organizationSegmentDetails?.[0]?.metricSegment?.name }}</div> -->
								</div>

								<button
									v-if="segment?.organizationSegmentDetails?.length > 1"
									@mouseover="event => openSignalPopover(event, index)"
									@mouseleave="closeSignalPopover(index)"
									class="inline-flex items-center px-2 py-1 rounded-md text-sm text-gray-600 hover:bg-gray-100"
									:class="{ 'bg-gray-100': isSignalPopoverOpen(index) }"
								>
									<svg class="w-4 h-4 mr-1" viewBox="0 0 24 24">
									<path d="M8 12a2 2 0 1 1-4 0 2 2 0 0 1 4 0zM14 12a2 2 0 1 1-4 0 2 2 0 0 1 4 0zM20 12a2 2 0 1 1-4 0 2 2 0 0 1 4 0z" />
									</svg>
									{{ segment?.organizationSegmentDetails?.length - 1}} more


									<div v-show="isSignalPopoverOpen(index)"
										@mouseleave="closeSignalPopover(index)"
										class="absolute right-0 mt-8 bg-white rounded-lg shadow-lg p-3 border border-gray-200 whitespace-nowrap"
										style="z-index: 1000;"
									>
										<div class="flex flex-col">

											<SignalBadge
												v-for="(segmentDetail, index) in segment?.organizationSegmentDetails?.slice?.(1)"
												:signal="segmentDetail?.metricSegment"
												:showNub="false"
												section="test"
											></SignalBadge>
											<!-- <div
												v-for="(segmentDetail, index) in segment?.organizationSegmentDetails"
												class="items-center px-4 py-1.5 border rounded-full text-sm font-medium cursor-pointer mb-2 mr-2 inline-block transition-all duration-200 hover:shadow-md bg-green-100 text-green-800 border-green-200 hover:bg-green-50">{{ segmentDetail?.metricSegment?.name }}</div> -->
										</div>
									</div>
								</button>

							</div>
						</div>

						<div class="flex">
							<div className="flex items-center mr-6 min-third">
								<div className="text-4xl font-bold mr-3">{{formatNumberCompact(segment.profileCount)}}</div>

								<div className="text-xs text-gray-600">
									<span className="font-semibold"># Customers</span>

								</div>
							</div>

							<div className="flex items-center mr-6 min-third">
								<div class="text-4xl font-bold mr-3">{{segment.avgltv}}</div>

								<div className="text-xs text-gray-600">
									<span className="font-semibold">Avg. LTV</span>
									<br />
									All time
								</div>
							</div>

							<div className="flex items-center min-third">
								<div class="text-4xl font-bold mr-3">{{segment.revenue}}</div>

								<div className="text-xs text-gray-600">
									<span className="font-semibold">Revenue</span>
									<br />
									Last 90 days
								</div>
							</div>

						</div>
					</div>
				</div>
				<!-- // -->
			</div>

		</div>

		<div class="" v-if="recommendations">

			<div class="bg-white rounded-2xl border shadow border-ralprimary-light border-opacity-50 hover:border-opacity-90 hover:shadow-lg mt-4 flex-grow transition-all duration-300 ease-in-out overflow-hidden">
				<div class="flex p-4 items-center justify-between">
					<div class="flex items-center">
						<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368"><path d="M40-272q0-34 17.5-62.5T104-378q62-31 126-46.5T360-440q66 0 130 15.5T616-378q29 15 46.5 43.5T680-272v32q0 33-23.5 56.5T600-160H120q-33 0-56.5-23.5T40-240v-32Zm800 112H738q11-18 16.5-38.5T760-240v-40q0-44-24.5-84.5T666-434q51 6 96 20.5t84 35.5q36 20 55 44.5t19 53.5v40q0 33-23.5 56.5T840-160ZM360-480q-66 0-113-47t-47-113q0-66 47-113t113-47q66 0 113 47t47 113q0 66-47 113t-113 47Zm400-160q0 66-47 113t-113 47q-11 0-28-2.5t-28-5.5q27-32 41.5-71t14.5-81q0-42-14.5-81T544-792q14-5 28-6.5t28-1.5q66 0 113 47t47 113ZM120-240h480v-32q0-11-5.5-20T580-306q-54-27-109-40.5T360-360q-56 0-111 13.5T140-306q-9 5-14.5 14t-5.5 20v32Zm240-320q33 0 56.5-23.5T440-640q0-33-23.5-56.5T360-720q-33 0-56.5 23.5T280-640q0 33 23.5 56.5T360-560Zm0 320Zm0-400Z"/></svg>
						<div class="ml-2">Recent Taco Seasoning Buyers</div>
					</div>
					<PrimaryButton cta="Activate" size="xs" />
				</div>

				<div class="w-full bg-gray-200 h-[1px]"></div>

				<div class="p-4">
					<div class="flex">
						<div class="w-1/2 flex flex-col">
							<div class="flex items-center mb-6">
								<svg xmlns="http://www.w3.org/2000/svg" height="18px" viewBox="0 -960 960 960" width="18px" fill="#5f6368"><path d="M40-272q0-34 17.5-62.5T104-378q62-31 126-46.5T360-440q66 0 130 15.5T616-378q29 15 46.5 43.5T680-272v32q0 33-23.5 56.5T600-160H120q-33 0-56.5-23.5T40-240v-32Zm800 112H738q11-18 16.5-38.5T760-240v-40q0-44-24.5-84.5T666-434q51 6 96 20.5t84 35.5q36 20 55 44.5t19 53.5v40q0 33-23.5 56.5T840-160ZM360-480q-66 0-113-47t-47-113q0-66 47-113t113-47q66 0 113 47t47 113q0 66-47 113t-113 47Zm400-160q0 66-47 113t-113 47q-11 0-28-2.5t-28-5.5q27-32 41.5-71t14.5-81q0-42-14.5-81T544-792q14-5 28-6.5t28-1.5q66 0 113 47t47 113ZM120-240h480v-32q0-11-5.5-20T580-306q-54-27-109-40.5T360-360q-56 0-111 13.5T140-306q-9 5-14.5 14t-5.5 20v32Zm240-320q33 0 56.5-23.5T440-640q0-33-23.5-56.5T360-720q-33 0-56.5 23.5T280-640q0 33 23.5 56.5T360-560Zm0 320Zm0-400Z"/></svg>
								<div class="ml-2">1,284 profiles</div>
							</div>

							<div class="flex items-center mb-6">
								<svg xmlns="http://www.w3.org/2000/svg" height="18px" viewBox="0 -960 960 960" width="18px" fill="#5f6368"><path d="M108-255q-12-12-11.5-28.5T108-311l211-214q23-23 57-23t57 23l103 104 208-206h-64q-17 0-28.5-11.5T640-667q0-17 11.5-28.5T680-707h160q17 0 28.5 11.5T880-667v160q0 17-11.5 28.5T840-467q-17 0-28.5-11.5T800-507v-64L593-364q-23 23-57 23t-57-23L376-467 164-255q-11 11-28 11t-28-11Z"/></svg>
								<div class="ml-2">$285 Avg. LTV</div>
							</div>

							<div class="flex items-center mb-2">
								<svg xmlns="http://www.w3.org/2000/svg" height="18px" viewBox="0 -960 960 960" width="18px" fill="#5f6368"><path d="M240-80q-33 0-56.5-23.5T160-160v-480q0-33 23.5-56.5T240-720h80q0-66 47-113t113-47q66 0 113 47t47 113h80q33 0 56.5 23.5T800-640v480q0 33-23.5 56.5T720-80H240Zm0-80h480v-480h-80v80q0 17-11.5 28.5T600-520q-17 0-28.5-11.5T560-560v-80H400v80q0 17-11.5 28.5T360-520q-17 0-28.5-11.5T320-560v-80h-80v480Zm160-560h160q0-33-23.5-56.5T480-800q-33 0-56.5 23.5T400-720ZM240-160v-480 480Z"/></svg>
								<div class="ml-2">3 Avg. # purchases per profile</div>
							</div>
						</div>

						<div className="space-y-2 w-1/2 ml-4">
							<p className="text-lg font-medium text-gray-900">Segment Signals</p>
							<ul className="space-y-1">
								<li className="flex items-center space-x-2 text-sm text-gray-600">
									<svg xmlns="http://www.w3.org/2000/svg" height="18px" viewBox="0 -960 960 960" width="18px" fill="#5f6368"><path d="M504-480 348-636q-11-11-11-28t11-28q11-11 28-11t28 11l184 184q6 6 8.5 13t2.5 15q0 8-2.5 15t-8.5 13L404-268q-11 11-28 11t-28-11q-11-11-11-28t11-28l156-156Z"/></svg>
									<span>Purchased taco seasoning in last 30 days</span>
								</li>
								<li className="flex items-center space-x-2 text-sm text-gray-600">
									<svg xmlns="http://www.w3.org/2000/svg" height="18px" viewBox="0 -960 960 960" width="18px" fill="#5f6368"><path d="M504-480 348-636q-11-11-11-28t11-28q11-11 28-11t28 11l184 184q6 6 8.5 13t2.5 15q0 8-2.5 15t-8.5 13L404-268q-11 11-28 11t-28-11q-11-11-11-28t11-28l156-156Z"/></svg>
									<span>Haven't bought other seasonings</span>
								</li>
								<li className="flex items-center space-x-2 text-sm text-gray-600">
									<svg xmlns="http://www.w3.org/2000/svg" height="18px" viewBox="0 -960 960 960" width="18px" fill="#5f6368"><path d="M504-480 348-636q-11-11-11-28t11-28q11-11 28-11t28 11l184 184q6 6 8.5 13t2.5 15q0 8-2.5 15t-8.5 13L404-268q-11 11-28 11t-28-11q-11-11-11-28t11-28l156-156Z"/></svg>
									<span>Engagement rate 15% above normal</span>
								</li>
							</ul>
						</div>
					</div>

					<div className="space-y-6 mt-6">
						<div>
							<div className="flex items-center space-x-2 mb-3">
								<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368"><path d="M260-320q47 0 91.5 10.5T440-278v-394q-41-24-87-36t-93-12q-36 0-71.5 7T120-692v396q35-12 69.5-18t70.5-6Zm260 42q44-21 88.5-31.5T700-320q36 0 70.5 6t69.5 18v-396q-33-14-68.5-21t-71.5-7q-47 0-93 12t-87 36v394Zm-40 118q-48-38-104-59t-116-21q-42 0-82.5 11T100-198q-21 11-40.5-1T40-234v-482q0-11 5.5-21T62-752q46-24 96-36t102-12q58 0 113.5 15T480-740q51-30 106.5-45T700-800q52 0 102 12t96 36q11 5 16.5 15t5.5 21v482q0 23-19.5 35t-40.5 1q-37-20-77.5-31T700-240q-60 0-116 21t-104 59ZM280-494Z"/></svg>
								<h3 className="text-lg font-medium text-gray-900">Educational Content</h3>
							</div>
							<div className="space-y-4">
								<div key={idx} className="rounded-lg border border-green-100 overflow-hidden">
									<div className="bg-green-50 p-3">
										<p className="font-medium text-sm mb-2 text-gray-900">5 Street Taco Recipes Beyond Basic Ground Beef</p>
										<p className="text-sm text-gray-600 mb-1">💡 Recipe content gets 2.4x higher engagement</p>
										<!--
										<div className="flex items-center space-x-1 text-sm text-green-700">
											<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368"><path d="m422-232 207-248H469l29-227-185 267h139l-30 208Zm-62-128H236q-24 0-35.5-21.5T203-423l299-430q10-14 26-19.5t33 .5q17 6 25 21t6 32l-32 259h155q26 0 36.5 23t-6.5 43L416-100q-11 13-27 17t-31-3q-15-7-23.5-21.5T328-139l32-221Zm111-110Z"/></svg>
											<span>32% avg. open rate on similar content</span>
										</div>
										-->
									</div>
									<div className="border-t border-green-100">
										<div className="p-3 bg-white">
											<div className="text-sm text-gray-600 whitespace-pre-line line-clamp-4">
												Loving your new taco seasoning? Let's take your taco nights from good to unforgettable with these authentic street-style recipes that go way beyond basic ground beef.
												<br /><br/>
												🔥 Street Taco All-Stars:...
											</div>
										</div>
										<div className="px-3 py-2 bg-gray-50 border-t border-green-100">
										<button className="inline-flex items-center space-x-2 text-sm font-medium text-green-600 hover:text-green-700">
											<svg width="18" height="20" viewBox="0 0 18 20" fill="none" xmlns="http://www.w3.org/2000/svg">
												<path d="M6.11129 2.95392L6.23649 3.01652C6.7425 3.27735 7.15461 3.68946 7.41545 4.19548L7.47805 4.32068C7.72845 4.81626 8.43791 4.81626 8.69352 4.32068L8.75612 4.19548C9.01695 3.68946 9.42907 3.27735 9.93508 3.01652L10.0603 2.95392C10.5559 2.70352 10.5559 1.99406 10.0603 1.73844L9.93508 1.67584C9.42907 1.41501 9.01695 1.0029 8.75612 0.496884L8.69352 0.371685C8.44312 -0.123895 7.73366 -0.123895 7.47805 0.371685L7.41545 0.496884C7.15461 1.0029 6.7425 1.41501 6.23649 1.67584L6.11129 1.73844C5.61571 1.98884 5.61571 2.6983 6.11129 2.95392Z" fill="#16a34a"/>
												<path d="M7.09723 7.84191C7.60846 7.58108 7.60846 6.85596 7.09723 6.59513L6.3356 6.20388C5.65744 5.85437 5.10448 5.30141 4.75496 4.62324L4.36372 3.86161C4.10288 3.35038 3.37777 3.35038 3.11694 3.86161L2.72569 4.62324C2.37618 5.30141 1.82321 5.85437 1.14505 6.20388L0.383423 6.59513C-0.127808 6.85596 -0.127808 7.58108 0.383423 7.84191L1.14505 8.23315C1.82321 8.58267 2.37618 9.13563 2.72569 9.81379L3.11694 10.5754C3.37777 11.0867 4.10288 11.0867 4.36372 10.5754L4.75496 9.81379C5.10448 9.13563 5.65744 8.58267 6.3356 8.23315L7.09723 7.84191Z" fill="#16a34a"/>
												<path d="M16.8836 10.9615L16.1116 10.565C15.1778 10.0903 14.4318 9.3443 13.9571 8.41052L13.5606 7.63846C13.2111 6.94986 12.5121 6.5221 11.74 6.5221C10.968 6.5221 10.2689 6.94986 9.91943 7.63846L9.52297 8.41052C9.04825 9.3443 8.30227 10.0903 7.3685 10.565L6.59643 10.9615C5.90784 11.311 5.48007 12.01 5.48007 12.7821C5.48007 13.5541 5.90784 14.2532 6.59643 14.6027L7.3685 14.9991C8.30227 15.4738 9.04825 16.2198 9.52297 17.1536L9.91943 17.9257C10.2689 18.6143 10.968 19.042 11.74 19.042C12.5121 19.042 13.2111 18.6143 13.5606 17.9257L13.9571 17.1536C14.4318 16.2198 15.1778 15.4738 16.1116 14.9991L16.8836 14.6027C17.5722 14.2532 18 13.5541 18 12.7821C18 12.01 17.5722 11.311 16.8836 10.9615ZM16.1742 13.2098L15.4021 13.6063C14.171 14.2323 13.1903 15.213 12.5643 16.4441L12.1678 17.2162C12.0478 17.4509 11.8287 17.477 11.74 17.477C11.6514 17.477 11.4323 17.4509 11.3123 17.2162L10.9158 16.4441C10.2898 15.213 9.30908 14.2323 8.07796 13.6063L7.3059 13.2098C7.07115 13.0898 7.04507 12.8707 7.04507 12.7821C7.04507 12.6934 7.07115 12.4743 7.3059 12.3543L8.07796 11.9578C9.30908 11.3318 10.2898 10.3511 10.9158 9.11998L11.3123 8.34792C11.4323 8.11317 11.6514 8.08709 11.74 8.08709C11.8287 8.08709 12.0478 8.11317 12.1678 8.34792L12.5643 9.11998C13.1903 10.3511 14.171 11.3318 15.4021 11.9578L16.1742 12.3543C16.4089 12.4743 16.435 12.6934 16.435 12.7821C16.435 12.8707 16.4089 13.0898 16.1742 13.2098Z" fill="#16a34a"/>
											</svg>

											<span>Generate Full Content</span>
										</button>
										</div>
									</div>
								</div>
							</div>

							<!-- Promotions -->
						</div>
					</div>

					<div className="grid mt-6">
						<div className="space-y-4 mt-4">
							<div className="flex items-center space-x-2">
								<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368"><path d="M856-390 570-104q-12 12-27 18t-30 6q-15 0-30-6t-27-18L103-457q-11-11-17-25.5T80-513v-287q0-33 23.5-56.5T160-880h287q16 0 31 6.5t26 17.5l352 353q12 12 17.5 27t5.5 30q0 15-5.5 29.5T856-390ZM513-160l286-286-353-354H160v286l353 354ZM260-640q25 0 42.5-17.5T320-700q0-25-17.5-42.5T260-760q-25 0-42.5 17.5T200-700q0 25 17.5 42.5T260-640Zm220 160Z"/></svg>
							<h3 className="text-lg font-medium text-gray-900">Promotional Content</h3>
							</div>
							<div className="space-y-4">
							<div className="rounded-lg border border-blue-100 overflow-hidden">
								<div className="bg-blue-50 p-3">
									<p className="font-medium text-sm mb-2 text-gray-900">Buy any 2 seasonings, get a free recipe book</p>
									<p className="text-sm text-gray-600 mb-1">💡 Cross-sell offers convert at 12% higher with this segment</p>
									<!--
									<div className="flex items-center space-x-1 text-sm text-green-700">
										<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368"><path d="m422-232 207-248H469l29-227-185 267h139l-30 208Zm-62-128H236q-24 0-35.5-21.5T203-423l299-430q10-14 26-19.5t33 .5q17 6 25 21t6 32l-32 259h155q26 0 36.5 23t-6.5 43L416-100q-11 13-27 17t-31-3q-15-7-23.5-21.5T328-139l32-221Zm111-110Z"/></svg>
										<span>32% avg. open rate on similar content</span>
									</div>
									-->
								</div>
								<div className="border-t border-blue-100">
									<div className="p-3 bg-white">
										<div className="text-sm text-gray-600 whitespace-pre-line line-clamp-4">
											You've mastered our taco seasoning (fantastic choice, by the way!) – ready to explore more flavors?
											<br /><br/>
											🎁 For 48 Hours Only:...
										</div>
									</div>
									<div className="px-3 py-2 bg-gray-50 border-t border-blue-100">
									<button className="inline-flex items-center space-x-2 text-sm font-medium text-blue-600 hover:text-blue-700">
										<svg width="18" height="20" viewBox="0 0 18 20" fill="none" xmlns="http://www.w3.org/2000/svg">
											<path d="M6.11129 2.95392L6.23649 3.01652C6.7425 3.27735 7.15461 3.68946 7.41545 4.19548L7.47805 4.32068C7.72845 4.81626 8.43791 4.81626 8.69352 4.32068L8.75612 4.19548C9.01695 3.68946 9.42907 3.27735 9.93508 3.01652L10.0603 2.95392C10.5559 2.70352 10.5559 1.99406 10.0603 1.73844L9.93508 1.67584C9.42907 1.41501 9.01695 1.0029 8.75612 0.496884L8.69352 0.371685C8.44312 -0.123895 7.73366 -0.123895 7.47805 0.371685L7.41545 0.496884C7.15461 1.0029 6.7425 1.41501 6.23649 1.67584L6.11129 1.73844C5.61571 1.98884 5.61571 2.6983 6.11129 2.95392Z" fill="#2563eb"/>
											<path d="M7.09723 7.84191C7.60846 7.58108 7.60846 6.85596 7.09723 6.59513L6.3356 6.20388C5.65744 5.85437 5.10448 5.30141 4.75496 4.62324L4.36372 3.86161C4.10288 3.35038 3.37777 3.35038 3.11694 3.86161L2.72569 4.62324C2.37618 5.30141 1.82321 5.85437 1.14505 6.20388L0.383423 6.59513C-0.127808 6.85596 -0.127808 7.58108 0.383423 7.84191L1.14505 8.23315C1.82321 8.58267 2.37618 9.13563 2.72569 9.81379L3.11694 10.5754C3.37777 11.0867 4.10288 11.0867 4.36372 10.5754L4.75496 9.81379C5.10448 9.13563 5.65744 8.58267 6.3356 8.23315L7.09723 7.84191Z" fill="#2563eb"/>
											<path d="M16.8836 10.9615L16.1116 10.565C15.1778 10.0903 14.4318 9.3443 13.9571 8.41052L13.5606 7.63846C13.2111 6.94986 12.5121 6.5221 11.74 6.5221C10.968 6.5221 10.2689 6.94986 9.91943 7.63846L9.52297 8.41052C9.04825 9.3443 8.30227 10.0903 7.3685 10.565L6.59643 10.9615C5.90784 11.311 5.48007 12.01 5.48007 12.7821C5.48007 13.5541 5.90784 14.2532 6.59643 14.6027L7.3685 14.9991C8.30227 15.4738 9.04825 16.2198 9.52297 17.1536L9.91943 17.9257C10.2689 18.6143 10.968 19.042 11.74 19.042C12.5121 19.042 13.2111 18.6143 13.5606 17.9257L13.9571 17.1536C14.4318 16.2198 15.1778 15.4738 16.1116 14.9991L16.8836 14.6027C17.5722 14.2532 18 13.5541 18 12.7821C18 12.01 17.5722 11.311 16.8836 10.9615ZM16.1742 13.2098L15.4021 13.6063C14.171 14.2323 13.1903 15.213 12.5643 16.4441L12.1678 17.2162C12.0478 17.4509 11.8287 17.477 11.74 17.477C11.6514 17.477 11.4323 17.4509 11.3123 17.2162L10.9158 16.4441C10.2898 15.213 9.30908 14.2323 8.07796 13.6063L7.3059 13.2098C7.07115 13.0898 7.04507 12.8707 7.04507 12.7821C7.04507 12.6934 7.07115 12.4743 7.3059 12.3543L8.07796 11.9578C9.30908 11.3318 10.2898 10.3511 10.9158 9.11998L11.3123 8.34792C11.4323 8.11317 11.6514 8.08709 11.74 8.08709C11.8287 8.08709 12.0478 8.11317 12.1678 8.34792L12.5643 9.11998C13.1903 10.3511 14.171 11.3318 15.4021 11.9578L16.1742 12.3543C16.4089 12.4743 16.435 12.6934 16.435 12.7821C16.435 12.8707 16.4089 13.0898 16.1742 13.2098Z" fill="#2563eb"/>
										</svg>

										<span>Generate Full Content</span>
									</button>
									</div>
								</div>
							</div>
						</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<div v-if="segmentModalTest" class="fixed inset-0 z-50 flex bg-black bg-opacity-50">
    <div
      class="relative w-full h-full bg-white shadow-lg overflow-y-auto"
    >
      <!-- Modal Content -->
      <div class="">
		<div class="fixed inset-0 bg-gray-50 text-gray-900 overflow-auto">
			<!-- Header -->
			<div class="bg-white border-b border-gray-200">
				<div class="">
					<div class="py-6 px-10">
					<div class="flex justify-between items-center">
						<div>
							<h2 class="text-2xl font-semibold text-gray-900">Create New Segment</h2>
							<input
								type="text"
								v-model="segmentName"
								class="mt-4 w-96 px-4 py-2 rounded-md bg-white border border-gray-300 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
								placeholder="Enter segment name..."
							/>
							<input
								type="text"
								v-model="segmentDescription"
								class=" ml-4 mt-4 w-96 px-4 py-2 rounded-md bg-white border border-gray-300 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
								placeholder="Enter segment description..."
							/>
						</div>
						<div class="flex items-center">
							<PrimaryButton cta="Save" size="xs" class="mr-2" :disabled="!isNameValid" @click="saveSegment"/>
							<button class="p-2 hover:bg-gray-100 rounded-full transition-colors" @click="segmentModalTest = false">
								<svg xmlns="http://www.w3.org/2000/svg" height="32px" viewBox="0 -960 960 960" width="32px" fill="#9ca3af"><path d="M480-424 284-228q-11 11-28 11t-28-11q-11-11-11-28t11-28l196-196-196-196q-11-11-11-28t11-28q11-11 28-11t28 11l196 196 196-196q11-11 28-11t28 11q11 11 11 28t-11 28L536-480l196 196q11 11 11 28t-11 28q-11 11-28 11t-28-11L480-424Z"/></svg>
							</button>
						</div>
					</div>
					</div>
				</div>

				<div className="bg-gray-50 px-10 py-4 border-t border-gray-200">
					<div className="flex items-center justify-between">
					<div className="flex items-center gap-10">
						<div>
						<div className="text-sm font-medium text-gray-500 max-w-44 min-w-44 mb-1">Estimated Audience</div>
							<div v-if="isCalculating" class="flex items-center space-x-2">
								<div class="h-6 w-14 bg-gray-200 rounded-md animate-pulse"></div>
								<div class="h-6 w-4 bg-gray-200 rounded-md animate-pulse"></div>
								<div class="h-6 w-14 bg-gray-200 rounded-md animate-pulse"></div>
							</div>
							<div v-else-if="audienceSize">
					<div className="text-xl font-semibold text-gray-900">
						{{ formatNumber(audienceSize.min) }} - {{ formatNumber(audienceSize.max) }}
						<span v-if="audienceSize.hasIntegrationSignals">*</span>
					</div>
					<div v-if="audienceSize.hasIntegrationSignals" class="text-sm text-gray-500 mt-1">
						* estimated size does not include/exclude audience from integration(s)
					</div>
				</div>
							<div className="text-xl font-semibold text-gray-900" v-else>-</div>
						</div>

							<div className="h-8 w-px bg-gray-300" />
						<!--
							<div>
							<div className="text-sm font-medium text-gray-500 mb-1">Label</div>
							<div className="flex items-center gap-2">
								<span className="text-xl font-semibold text-gray-900">Value</span>
								<span className="text-sm text-green-600">Trend</span>
							</div>
							</div>
						-->
					</div>
					<!--
					<div className="flex items-center gap-2 text-sm text-green-600">
						<div className="w-2 h-2 bg-green-500 rounded-full" />
						Audience quality score: High
					</div>
					-->
					</div>
				</div>
			</div>

			<div class="px-10 mb-10">
				<SignalManager
					v-model:audience-size="audienceSize"
					@calculating="handleCalculating"
					ref="signalManager"
				/>
			</div>
		</div>
      </div>
	  <!-- // Modal Content -->
    </div>
  	</div>


	<ModalBlank id="info-modal" :modalOpen="loadingModalOpen" @close-modal="loadingModalOpen = false">
		<div class="p-5 flex flex-col space-x-4 justify-center">
			<div class="flex justify-center">
				<RaleonLoader />
			</div>
			<div>

				<div class="flex justify-center mb-2 mt-10">
					<div class="text-lg font-semibold text-ralblack-primary flex items-center">
						<svg width="24" height="26" viewBox="0 0 24 26" fill="none" xmlns="http://www.w3.org/2000/svg"
							class="mr-2" v-if="aiTextIndex == 0">
							<path
								d="M8.14838 4.24398L8.31532 4.32745C8.99 4.67522 9.53949 5.22471 9.88726 5.89939L9.97073 6.06632C10.3046 6.7271 11.2505 6.7271 11.5914 6.06632L11.6748 5.89939C12.0226 5.22471 12.5721 4.67522 13.2468 4.32745L13.4137 4.24398C14.0745 3.91011 14.0745 2.96416 13.4137 2.62334L13.2468 2.53988C12.5721 2.1921 12.0226 1.64262 11.6748 0.967932L11.5914 0.801C11.2575 0.140226 10.3115 0.140226 9.97073 0.801L9.88726 0.967932C9.53949 1.64262 8.99 2.1921 8.31532 2.53988L8.14838 2.62334C7.48761 2.95721 7.48761 3.90316 8.14838 4.24398Z"
								fill="#E86AD6" />
							<path
								d="M9.46298 10.7613C10.1446 10.4135 10.1446 9.4467 9.46298 9.09893L8.44747 8.57726C7.54325 8.11124 6.80597 7.37396 6.33995 6.46974L5.81829 5.45424C5.47051 4.7726 4.5037 4.7726 4.15592 5.45424L3.63426 6.46974C3.16824 7.37396 2.43095 8.11124 1.52673 8.57726L0.51123 9.09893C-0.17041 9.4467 -0.17041 10.4135 0.51123 10.7613L1.52673 11.283C2.43095 11.749 3.16824 12.4863 3.63426 13.3905L4.15592 14.406C4.5037 15.0876 5.47051 15.0876 5.81829 14.406L6.33995 13.3905C6.80597 12.4863 7.54325 11.749 8.44747 11.283L9.46298 10.7613Z"
								fill="#E86AD6" />
							<path
								d="M22.5115 14.9207L21.4821 14.3921C20.2371 13.7591 19.2424 12.7645 18.6095 11.5194L18.0809 10.49C17.6148 9.5719 16.6828 9.00155 15.6534 9.00155C14.624 9.00155 13.6919 9.5719 13.2259 10.49L12.6973 11.5194C12.0643 12.7645 11.0697 13.7591 9.82466 14.3921L8.79525 14.9207C7.87712 15.3867 7.30677 16.3188 7.30677 17.3482C7.30677 18.3776 7.87712 19.3096 8.79525 19.7756L9.82466 20.3043C11.0697 20.9372 12.0643 21.9319 12.6973 23.1769L13.2259 24.2063C13.6919 25.1244 14.624 25.6948 15.6534 25.6948C16.6828 25.6948 17.6148 25.1244 18.0809 24.2063L18.6095 23.1769C19.2424 21.9319 20.2371 20.9372 21.4821 20.3043L22.5115 19.7756C23.4296 19.3096 24 18.3776 24 17.3482C24 16.3188 23.4296 15.3867 22.5115 14.9207ZM21.5656 17.9185L20.5362 18.4471C18.8947 19.2818 17.587 20.5894 16.7524 22.2309L16.2237 23.2604C16.0638 23.5734 15.7716 23.6081 15.6534 23.6081C15.5351 23.6081 15.243 23.5734 15.083 23.2604L14.5544 22.2309C13.7197 20.5894 12.4121 19.2818 10.7706 18.4471L9.7412 17.9185C9.4282 17.7585 9.39342 17.4664 9.39342 17.3482C9.39342 17.2299 9.4282 16.9378 9.7412 16.7778L10.7706 16.2492C12.4121 15.4145 13.7197 14.1069 14.5544 12.4654L15.083 11.436C15.243 11.123 15.5351 11.0882 15.6534 11.0882C15.7716 11.0882 16.0638 11.123 16.2237 11.436L16.7524 12.4654C17.587 14.1069 18.8947 15.4145 20.5362 16.2492L21.5656 16.7778C21.8786 16.9378 21.9133 17.2299 21.9133 17.3482C21.9133 17.4664 21.8786 17.7585 21.5656 17.9185Z"
								fill="#E86AD6" />
						</svg>
						Creating your Segment
					</div>
				</div>
			</div>
		</div>
	</ModalBlank>

	<div v-if="guideModelOpen" class="fixed inset-0 bg-slate-900 bg-opacity-30 z-50 transition-opacity" aria-hidden="true"></div>
	  <div v-if="guideModelOpen"  class="fixed inset-0 z-50 overflow-hidden flex items-center my-4 justify-center transform px-4 sm:px-6" role="dialog" aria-modal="true">
      <div ref="modalContent" class="bg-white rounded shadow-lg overflow-auto w-650 h-430 p-4">

		<div v-if="videoGuide == 'repeat-playbook'">
			<iframe width="640" height="378" src="https://www.loom.com/embed/4e19040f27f44f36ba47b04ee34568fb?sid=017195b4-0b43-4a04-ab5b-293b1d4580a6" frameborder="0" webkitallowfullscreen mozallowfullscreen allowfullscreen></iframe>
		</div>
		<div v-else-if="videoGuide == 'promo-playbook'">
			<iframe width="640" height="378" src="https://www.loom.com/embed/6e7a807f77b7453eb8114453de7fb30e?sid=26cb0507-0c0c-468a-a74f-93e56d30ece4" frameborder="0" webkitallowfullscreen mozallowfullscreen allowfullscreen></iframe>
		</div>
		<div v-else-if="videoGuide == 'winback-playbook'">
			<iframe width="640" height="378" src="https://www.loom.com/embed/99a81429f61e449e8dc51f7deb0943b8?sid=c942f277-f2cc-4a6c-ab2d-001370b09397" frameborder="0" webkitallowfullscreen mozallowfullscreen allowfullscreen></iframe>
		</div>

		<div class="flex flex-wrap justify-end space-x-2 mt-4">
			<CancelButton @click="guideModelOpen = false" cta="Done"></CancelButton>
			</div>
      </div>
    </div>

</template>

<script>

import * as Utils from '../../client-old/utils/Utils';
import CardContainer from '../components/CardContainer.ts.vue';
import LightSecondaryButton from '../components/LightSecondaryButton.ts.vue'
import PrimaryButton from '../components/PrimaryButton.ts.vue';
import ToggleItem from '../components/ToggleItem.ts.vue';
import StatusMessage from '../components/StatusMessage.ts.vue';
import LearnMoreText from '../components/LearnMoreText.ts.vue';
import CancelButton from '../components/CancelButton.ts.vue';
import EditableHeader from '../components/EditableHeader.ts.vue'
import RaleonLoader from '../components/RaleonLoader.ts.vue';
import TextFadeEffect from '../components/TextFadeEffect.ts.vue';
import ModalBlank from '../components/ModalBlank.vue';
import Tooltip from '../components/Tooltip.ts.vue';
import SignalManager from '../components/SignalManager.ts.vue';
import CustomDropdown from '../components/CustomDropdown.ts.vue'
import SignalEditorPopup from '../components/SignalEditor.ts.vue';
import * as OrganizationSettings from '../services/organization-settings.js';
import DataProgress from '../components/DataProgress.ts.vue';
import { customerIOTrackEvent } from '../services/customerio.js';
import { useRoute } from 'vue-router';
import SignalBadge from '../components/SignalBadge.ts.vue';
import SignalBadgeTs from '../components/SignalBadge.ts.vue';
import { isFeatureAvailable, freeTrialInfo } from '../services/features.js';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	components: {
		CardContainer,
		LightSecondaryButton,
		StatusMessage,
		ToggleItem,
		PrimaryButton,
		LearnMoreText,
		CancelButton,
		EditableHeader,
		RaleonLoader,
		TextFadeEffect,
		ModalBlank,
		Tooltip,
		SignalManager,
		CustomDropdown,
		SignalEditorPopup,
		SignalBadge,
		DataProgress
	},
	// props: {
	// 	loading: {
	// 	type: Boolean,
	// 	required: true
	// 	}
	// },
	data() {
		return {
			guideModelOpen: false,
			videoGuide: '',
			insights: false,
			recommendations: false,
			overview: true,
			archived: false,
			partners: false,
			segmentName: 'Untitled Segment',
			generateTest: false,
			launchModelOpen: false,
			campaign: { name: 'Likely to buy in next 30 days' },
			hoveredRowIndex: 0,
			aiLoadText: ["Analyzing your audience", "Taking a look at your product...", "Playing match-maker...", "Clipping some coupons...", "Your Smart Offer preview is ready!"],
			aiTextIndex: 0,
			newSegmentModal: false,
			segmentModalTest: false,
			audienceSize: null,
			isCalculating: false,
			segments: [],
			signals: [],
			isGenerating: false,
			loading: (this.$route.path.split('/').reverse()[0] !== 'overview').toString(),
			loadingMessages: [
				'Analyzing Your Customers',
				'Processing Engagement Data',
				'Building Smarter Segments',
				'Reviewing Results'
			],
			currentMessageIndex: 0,
			loadingModalOpen: false,
			searchQuery: '',
                        emailPixelTrackingEnabled: false,
                        shopifyConnected: true,
                        signalPopoversOpen: [],
			currentSlide: 0,
			slides: [
				{
				tag: 'Guide',
				subtitle: 'Recommended first step',
				title: 'Create a segment to win back more customers',
				description: 'Learn how to create an AI segment targeting customers you can win back, and sync it to Klavio for campaigns or flows.',
				cta: 'Watch 2 min setup',
				type: 'watch',
				videoKey: 'winback-playbook'
				},
				{
				tag: 'Guide',
				subtitle: '',
				title: 'Create a segment that predicts when a customer will respond to a promotion',
				description: 'Learn how to create a segment that targets customers likely to respond to a promotion, and sync it to Klaviyo for campaigns or flows.',
				cta: 'Watch 3 min guide',
				videoKey: 'promo-playbook'
				},
				{
				tag: 'Guide',
				subtitle: 'Flows',
				title: 'Create a segment to drive repeat purchases',
				description: 'Learn how to create an AI segment targeting repeat purchases that you can sync to Klaviyo and use in flows',
				cta: 'Watch 4 min guide',
				videoKey: 'repeat-playbook'
				},
				],
			}
	},
	computed: {
		currentLoadingMessage() {
			return this.loadingMessages[this.currentMessageIndex]
		},
		filteredSegments() {
			const query = this.searchQuery.toLowerCase().trim();
			return this.segments.filter(segment => {
				const matchesSearch = !query ||
					segment.name.toLowerCase().includes(query) ||
					(segment.description && segment.description.toLowerCase().includes(query));
				const matchesTab = this.archived ? segment.archived : !segment.archived;
				return matchesSearch && matchesTab;
			});
		},
		isFeatureAvailable() {
			return isFeatureAvailable('ai-segments');
		},
		isTrialEnded() {
			const freeTrialData = JSON.parse(localStorage.getItem('freeTrialData'));
			return freeTrialData != {} && !freeTrialData.hasPaidPlan && freeTrialData.daysLeft < 0;
		},
                isNameValid() {
                        return this.segmentName && this.segmentName.trim() !== '';
                },
                isShopifyConnected() {
                        return this.shopifyConnected;
                }
        },
        async mounted() {
                customerIOTrackEvent('Viewed AI Segment Overview');

                await this.checkShopifyConnection();

                if (!this.isShopifyConnected) {
                        return;
                }

		// Cycle through loading messages
		this.messageInterval = setInterval(() => {
		this.currentMessageIndex = (this.currentMessageIndex + 1) % this.loadingMessages.length
		}, 3000)

		document.body.classList.add('content-loaded');
		this.segments = await this.fetchSegments();

		// Always generate segments, but only show loading if there are none
		if (!this.segments.length) {
			this.loading = 'true';
		}

		await this.generateSegments();

		if (!this.segments.length) {
			await new Promise(r => setTimeout(r, 3000));
			this.segments = await this.fetchSegments();
			this.loading = 'false';
		}

		this.emailPixelTrackingEnabled = await OrganizationSettings.getOrganizationSetting('emailPixel');
  	},
	beforeUnmount() {
		clearInterval(this.messageInterval)
	},
	methods: {
		createSegment() {
			this.segmentModalTest = true;
			customerIOTrackEvent('Start Create AI Segment');
		},
		nextSlide() {
		this.currentSlide = (this.currentSlide + 1) % this.slides.length;
		},
		prevSlide() {
		this.currentSlide = (this.currentSlide - 1 + this.slides.length) % this.slides.length;
		},
		goToSlide(index) {
		this.currentSlide = index;
		},
		handleCalculating(calculating) {
			this.isCalculating = calculating
		},
		formatNumber(num) {
			return new Intl.NumberFormat().format(num)
		},
                formatNumberCompact(num){
                        return new Intl.NumberFormat('en-US', { notation: 'compact', maximumSignificantDigits: 3 }).format(num)
                },
                async checkShopifyConnection() {
                        try {
                                const response = await fetch(`${URL_DOMAIN}/integration/shopify/connected`, {
                                        method: 'GET',
                                        headers: {
                                                'Content-Type': 'application/json',
                                                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                                        }
                                });
                                if (!response.ok) {
                                        throw new Error('Failed to check Shopify connection');
                                }
                                const data = await response.json();
                                this.shopifyConnected = data.connected;
                        } catch (error) {
                                console.error('Error checking Shopify connection:', error);
                                this.shopifyConnected = false;
                        }
                },
                async saveSegment() {
			console.log("Saving segment...");
			customerIOTrackEvent('Created AI Segment');
			try {
				const { positive, negative } = this.$refs.signalManager.signals;
				const body = {
					name: this.segmentName,
					description: this.segmentDescription,
					positive,
					negative
				};

				const response = await fetch(`${URL_DOMAIN}/organization-segment`, {
					method: 'POST',
					headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Content-Type': 'application/json'
					},
					body: JSON.stringify(body)
				});

				if (response.ok) {
					console.log("Segment saved successfully");
					this.loadingModalOpen = true;
					this.segments = await this.fetchSegments();

					this.$router.push(`/ai-segments/segments/${this.segments[this.segments.length - 1].id}`);
					this.segmentModalTest = false;
				} else {
					console.error("Failed to save segment:", response.statusText);
				}
			} catch (error) {
			console.error("Error saving segment:", error);
			}
		},
		async fetchSegments() {
			try {
				this.isLoading = true;
				const response = await fetch(`${URL_DOMAIN}/organization-segment`, {
					method: 'GET',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`, // Adjust if using token-based auth
						'Content-Type': 'application/json',
					},
				});

				if (response.ok) {
					const data = await response.json();
					console.log("Segments:", data);


					this.signalPopoversOpen = data.map(() => false);

					return data;
				} else {
					console.error("Failed to fetch segments:", response.statusText);
				}
			} catch (error) {
				console.error("Error fetching segments:", error);
			} finally {
				this.isLoading = false;
			}
		},
		async generateSegments() {
			try {
				const response = await fetch(`${URL_DOMAIN}/generate-organization-segments`, {
					method: 'POST',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`, // Adjust if using token-based auth
						'Content-Type': 'application/json',
					}
				});

				if (response.ok) {
					// const data = await response.json();
					// console.log("Segments:", data);
					// return data;
					return;
				} else {
					console.error("Failed to generate segments:", response.statusText);
				}
			} catch (error) {
				console.error("Error generating segments:", error);
			} finally {
			}
		},
		openSignalPopover(event, index) {
			this.signalPopoversOpen.forEach((x, i) => {
				this.signalPopoversOpen[i] = i === index;
			});

			event.stopPropagation();
			event.preventDefault();

			return false;
		},
		closeSignalPopover(index) {
			this.signalPopoversOpen[index] = false;
		},
		isSignalPopoverOpen(index) {
			return this.signalPopoversOpen[index];
		}
    },
}
</script>

<style scoped>
.no-focus-outline {
	box-shadow: none !important;
	outline: none !important;
}

.numberChart {
	color: rgba(32, 32, 32, 0.80);
		font-family: Inter;
		font-size: 3rem;
		font-style: normal;
		font-weight: 600;
		line-height: normal;
		letter-spacing: -2.4px;
		text-transform: uppercase;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes progress {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

@keyframes robotFade {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.progress-bar {
  animation: progress 2s ease-in-out infinite;
}

.animate-spin-slow {
  animation: spin 3s linear infinite;
}

.robot-fade {
  animation: robotFade 2s ease-in-out infinite;
  /* Ensures smooth emoji rendering */
  -webkit-font-smoothing: antialiased;
  /* Prevents selection of the emoji */
  user-select: none;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Message transition animations */
.fade-message-enter-active,
.fade-message-leave-active {
  transition: all 0.5s ease;
}

.fade-message-enter-from {
  opacity: 0;
  transform: translateY(10px);
}

.fade-message-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

.fade-message-enter-to,
.fade-message-leave-from {
  opacity: 1;
  transform: translateY(0);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Initial state for segments */
.segment-card {
  opacity: 0;
  transform: translateY(20px);
}

/* Animation trigger when content is loaded */
.content-loaded .segment-card {
  animation: fadeInUp 0.6s ease-out forwards;
}

/* Hover effects */
.segment-card {
  transition: all 0.2s ease-out;
}

.segment-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: rgba(147, 51, 234, 0.3);
}
</style>
