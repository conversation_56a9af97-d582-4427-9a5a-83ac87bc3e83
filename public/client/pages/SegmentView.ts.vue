<template>
	<div class="p-2 sm:p-7 flex flex-col sm:flex-row items-top justify-between bg-white border-b border-bg-gray-50">

		<div>
			<div>
				<div v-if="isLoading" role="status"
					class="p-3 bg-gray-300 dark:bg-gray-600 w-56 rounded-2xl shadow animate-pulse mb-6 sm:mb-0"></div>

				<EditableHeader :header-text="campaign.name"
					@updated-header="(header) => {campaign.name = header; saveCampaignName();}" v-if="!isLoading" />

				<div class="text-base w-4/5">{{ campaign.description }}</div>
			</div>

			<div class="flex mt-2">
				<button class="flex p-0 border-none bg-none focus:outline-none items-center"
					@click="this.$router.push('/ai-segments/overview');">
					<svg width="18" height="18" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path
							d="M18.3333 15L13.3333 20M13.3333 20L18.3333 25M13.3333 20H26.6667M35 20C35 11.7157 28.2843 5 20 5C11.7157 5 5 11.7157 5 20C5 28.2843 11.7157 35 20 35C28.2843 35 35 28.2843 35 20Z"
							stroke="#202020" stroke-opacity="0.8" stroke-width="4" stroke-linecap="round"
							stroke-linejoin="round"></path>
					</svg>
					<span class="text-neutral-500 text-base ml-2">Segments</span>
				</button>
			</div>
		</div>

		<div class="flex-grow"></div>

		<ToastStatus :status="status" :text="statusText" @clear-status="clearStatus()" />

		<div class="flex w-full sm:w-auto justify-start">
			<div v-if="isLoading" role="status"
				class="p-3 space-y-4 bg-gray-300 dark:bg-gray-600 w-56 rounded-2xl shadow animate-pulse"></div>

			<div v-if="!isLoading">
				<div class="py-2 justify-start items-center gap-2.5 inline-flex">
					<!-- SVG and Launch Campaign Text -->
					<div class="flex items-center pr-5">
						<div class="flex space-x-2">

							<!-- Download Button - styled like Archive button -->
							<button
								@click="downloadSegmentMembers"
								:disabled="isDownloading"
								class="inline-flex items-center px-3 py-1.5 border rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-60">
								<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
								</svg>
								{{ isDownloading ? 'Downloading...' : 'Download Segment' }}
							</button>

							<!-- Archive Button -->
							<button v-if="!segment.archived"
								@click="archiveSegment"
								class="inline-flex items-center px-3 py-1.5 border rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
								<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
								</svg>
								Archive
							</button>

							<!-- Unarchive Button -->
							<!-- <button v-else
								@click="unarchiveSegment"
								class="inline-flex items-center px-3 py-1.5 border rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
								<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4l3-3m0 0l3 3m-3-3v7" />
								</svg>
								Unarchive
							</button> -->

							<!-- Klaviyo Sync Button -->
							<div class="flex gap-2">
								<div v-if="segment.id && !segment.externalId && klaviyoMetricCompleted">
									<div v-if="!klaviyoMetricCompleted" class="mb-2">
										<div class="flex items-center p-2 text-sm text-yellow-800 bg-yellow-50 border border-yellow-200 rounded">
											<svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
												<path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
											</svg>
											<span>Profile properties are still being synced. Segments will not fully sync until complete</span>
										</div>
									</div>
									<PrimaryButton cta="Sync to Klaviyo" size="xs" @click="handleKlaviyoSync" /> <!-- :disabled="!klaviyoMetricCompleted" />-->
								</div>
							</div>
							<div v-if="segment.externalId && segment.externalSyncDate"
								className="flex items-center border rounded-md px-3 py-1.5 bg-green-50 border-green-200">
								<svg xmlns="http://www.w3.org/2000/svg" height="16px" viewBox="0 -960 960 960"
									width="16px" fill="#16a34a" class="mr-2">
									<path
										d="m382-354 339-339q12-12 28-12t28 12q12 12 12 28.5T777-636L410-268q-12 12-28 12t-28-12L182-440q-12-12-11.5-28.5T183-497q12-12 28.5-12t28.5 12l142 143Z" />
								</svg>
								<div className="flex flex-col">
									<span className="text-sm font-medium text-green-800">Synced to Klaviyo</span>
									<span className="text-xs text-green-600">First sync: {{ formattedDate }}</span>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<div class="p-2 sm:p-7">
		<!-- <div class="flex space-x-8">
				<div class="bg-white w-1/2 rounded-2xl items-center border border-violet-300 bg-opacity-85 overflow-hidden shadow-md mb-2 p-4 hover:shadow-lg transition-all duratio-300  flex flex-col justify-between">
					<div class="text-zinc-400 text-lg font-normal font-['Inter'] mb-4">Revenue from Segment</div>
					<img src="../images/SegmentRevenue.png" width="552" />
				</div>

				<div class="bg-white w-1/2 rounded-2xl items-center border border-violet-300 bg-opacity-85 overflow-hidden shadow-md mb-2 p-4 hover:shadow-lg transition-all duratio-300  flex flex-col justify-between">
					<div class="text-zinc-400 text-lg font-normal font-['Inter'] mb-4">Avg. LTV and AOV</div>
					<img src="../images/SegmentLTV.png" width="532" class="mb-4"/>
				</div>
			</div> -->

		<div class="bg-white w-full rounded-2xl items-center border border-violet-300 bg-opacity-85 overflow-hidden shadow-md mb-6 p-4 transition-all duratio-300 justify-left"
			v-if="segment?.organizationSegmentDetails">

			<div class="flex justify-between">
				<div class="text-zinc-400 text-lg font-normal font-['Inter'] mb-4">Segment Definition</div>
				<div v-if="data?.isEspConfigured === false" class="text-sm text-ralprimary-main cursor-pointer"
					@click="launchModelOpen = true">Connect your
					ESP to sync segment -></div>
			</div>

			<div class="flex">
				<SignalBadge
					v-for="detail of segment?.organizationSegmentDetails"
					:key="detail?.metricSegment?.id"
					:signal="detail?.metricSegment"
					:showNub="false"
					:margin="true"
					:showTypePrefix="true"
					:is-negative="!detail?.include"
					section="segment-view"
				/>
			</div>
		</div>

		<div v-if="!segment?.organizationSegmentDetails"
			class="w-full bg-white rounded-2xl border border-violet-300 bg-opacity-75 shadow-md mb-2">
			<div class="flex justify-between items-center p-6" :style="{animation: 'fadeIn 0.6s ease-out both'}">
				<div v-for="i in 4" :key="i" class="flex-1 space-y-2">
					<div class="h-3 bg-purple-50 rounded-full w-24 animate-pulse"></div>
					<div class="h-8 bg-purple-100 rounded-full w-32 animate-pulse"></div>
				</div>
			</div>
		</div>

		<div class="">

			<div v-if="isLoading"
				class="w-full bg-white rounded-2xl border border-violet-300 bg-opacity-75 shadow-md mb-2">
				<div class="flex justify-between items-center p-6" :style="{animation: 'fadeIn 0.6s ease-out both'}">
					<div v-for="i in 4" :key="i" class="flex-1 space-y-2">
						<div class="h-3 bg-purple-50 rounded-full w-24 animate-pulse"></div>
						<div class="h-8 bg-purple-100 rounded-full w-32 animate-pulse"></div>
					</div>
				</div>
			</div>

			<div v-if="!isLoading" class="flex flex-col space-y-6">
				<!-- First Row -->
				<div class="flex space-x-6">
					<!-- Profiles in Segment -->
					<div
						class="w-1/4 flex gap-40 bg-white rounded-2xl border border-violet-300 bg-opacity-75 shadow-md justify-center text-center">
						<div class="mt-4 mb-2">
							<div class="flex justify-center mb-2">
								<div>Profiles in Segment</div>
							</div>
							<div class="flex justify-center items-cemter flex-col items-center">
								<div class="flex flex-col justify-start mb-2">
									<span class="numberChart" v-if="!customers?.length && !isLoading">N/A</span>
									<span class="numberChart" v-else>
										{{ totalCustomerCount.toLocaleString() }}<span v-if="data?.hasIntegrationSignals">*</span>
									</span>
								</div>
							</div>
						</div>
					</div>

					<!-- Total Lifetime Spend -->
					<div
						class="w-1/4 flex gap-40 bg-white rounded-2xl border border-violet-300 bg-opacity-75 shadow-md justify-center text-center">
						<div class="mt-4 mb-2">
							<div class="flex justify-center mb-2">
								<div>Total Lifetime Spend</div>
							</div>
							<div class="flex justify-center items-cemter flex-col items-center">
								<div class="flex flex-col justify-start mb-2">
									<span class="numberChart" v-if="!customers?.length && !isLoading">N/A</span>
									<span class="numberChart" v-else>${{ Math.round(totalSpend).toLocaleString()
										}}</span>
								</div>
							</div>
						</div>
					</div>

					<!-- Avg. AOV -->
					<div
						class="w-1/4 flex gap-40 bg-white rounded-2xl border border-violet-300 bg-opacity-75 shadow-md justify-center text-center">
						<div class="mt-4 mb-2">
							<div class="flex justify-center mb-2">
								<div>Avg. AOV</div>
							</div>
							<div class="flex justify-center items-cemter flex-col items-center">
								<div class="flex flex-col justify-start mb-2">
									<span class="numberChart" v-if="!customers?.length && !isLoading">N/A</span>
									<span class="numberChart" v-else>${{ Math.round(averageAov).toLocaleString()
										}}</span>
								</div>
							</div>
						</div>
					</div>

					<!-- Avg. LTV -->
					<div
						class="w-1/4 flex gap-40 bg-white rounded-2xl border border-violet-300 bg-opacity-75 shadow-md justify-center text-center">
						<div class="mt-4 mb-2">
							<div class="flex justify-center mb-2">
								<div>Avg. LTV</div>
							</div>
							<div class="flex justify-center items-cemter flex-col items-center">
								<div class="flex flex-col justify-start mb-2">
									<span class="numberChart" v-if="!customers?.length && !isLoading">N/A</span>
									<span class="numberChart" v-else>${{ Math.round(averageLtv).toLocaleString()
										}}</span>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- Second Row -->
				<div v-if="emailPixelTrackingEnabled === 'true'" class="flex space-x-6">
					<!-- Unique Opens Last 30 -->
					<div
						class="w-1/4 flex gap-40 bg-white rounded-2xl border border-violet-300 bg-opacity-75 shadow-md justify-center text-center">
						<div class="mt-4 mb-2">
							<div class="flex justify-center mb-2">
								<div>Unique Email Opens Last 30</div>
							</div>
							<div class="flex justify-center items-center flex-col items-center">
								<div class="flex flex-col justify-start mb-2">
									<span class="numberChart" v-if="!customers?.length && !isLoading">N/A</span>
									<span class="numberChart" v-else>{{ emailOpenUniquesL30.toLocaleString() }}</span>
								</div>
							</div>
						</div>
					</div>

					<!-- Total Opens Last 30 -->
					<div
						class="w-1/4 flex gap-40 bg-white rounded-2xl border border-violet-300 bg-opacity-75 shadow-md justify-center text-center">
						<div class="mt-4 mb-2">
							<div class="flex justify-center mb-2">
								<div>Total Email Opens Last 30</div>
							</div>
							<div class="flex justify-center items-center flex-col items-center">
								<div class="flex flex-col justify-start mb-2">
									<span class="numberChart" v-if="!customers?.length && !isLoading">N/A</span>
									<span class="numberChart" v-else>{{ emailOpenTotalL30.toLocaleString() }}</span>
								</div>
							</div>
						</div>
					</div>

					<!-- Unique Opens Last 90 -->
					<div
						class="w-1/4 flex gap-40 bg-white rounded-2xl border border-violet-300 bg-opacity-75 shadow-md justify-center text-center">
						<div class="mt-4 mb-2">
							<div class="flex justify-center mb-2">
								<div>Unique Email Opens Last 90</div>
							</div>
							<div class="flex justify-center items-center flex-col items-center">
								<div class="flex flex-col justify-start mb-2">
									<span class="numberChart" v-if="!customers?.length && !isLoading">N/A</span>
									<span class="numberChart" v-else>{{ emailOpenUniquesL90.toLocaleString() }}</span>
								</div>
							</div>
						</div>
					</div>

					<!-- Total Opens Last 90 -->
					<div
						class="w-1/4 flex gap-40 bg-white rounded-2xl border border-violet-300 bg-opacity-75 shadow-md justify-center text-center">
						<div class="mt-4 mb-2">
							<div class="flex justify-center mb-2">
								<div>Total Email Opens Last 90</div>
							</div>
							<div class="flex justify-center items-center flex-col items-center">
								<div class="flex flex-col justify-start mb-2">
									<span class="numberChart" v-if="!customers?.length && !isLoading">N/A</span>
									<span class="numberChart" v-else>{{ emailOpenTotalL90.toLocaleString() }}</span>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div v-if="data?.hasIntegrationSignals" class="text-sm text-gray-500 mt-4 ml-2">
				* profile count does not include/exclude audience from integration(s)
			</div>
		</div>

		<div class="mt-6">
			<h2 class="text-3xl sm:text-3xl font-sans font-medium opacity-70 text-ralblack-primary mb-4">Profile List
			</h2>

			<div class="bg-white rounded-lg border shadow-sm overflow-hidden" v-if="isloading">
				<table class="w-full">
					<thead>
						<tr class="border-b">
							<th v-for="i in 5" :key="i" class="p-4">
								<div class="h-4 bg-purple-100 rounded-full w-24 animate-pulse"></div>
							</th>
						</tr>
					</thead>
					<tbody>
						<tr v-for="i in 5" :key="i" class="border-b"
							:style="{animation: `fadeIn 0.6s ease-out ${i * 0.2}s both`}">
							<td class="p-4">
								<div class="flex items-center gap-3">
									<div class="h-10 w-10 rounded-full bg-purple-100 animate-pulse"></div>
									<div class="space-y-2">
										<div class="h-4 bg-purple-100 rounded-full w-32 animate-pulse"></div>
										<div class="h-3 bg-purple-50 rounded-full w-24 animate-pulse"></div>
									</div>
								</div>
							</td>
							<td class="p-4">
								<div class="h-4 bg-purple-100 rounded-full w-40 animate-pulse"></div>
							</td>
							<td class="p-4">
								<div class="h-4 bg-purple-100 rounded-full w-20 animate-pulse"></div>
							</td>
							<td class="p-4">
								<div class="h-4 bg-purple-100 rounded-full w-28 animate-pulse"></div>
							</td>
							<td class="p-4">
								<div class="h-4 bg-purple-100 rounded-full w-16 animate-pulse"></div>
							</td>
						</tr>
					</tbody>
				</table>
			</div>

			<div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden" v-else-if="!isloading">
				<table class="min-w-full divide-y divide-gray-200">
					<thead class="bg-gray-50">
						<tr>
							<th scope="col"
								class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Name</th>
							<th scope="col"
								class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Email</th>
							<th scope="col"
								class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">#
								of Purchases</th>
							<th scope="col"
								class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Lifetime Spend</th>
							<th scope="col"
								class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Revenue Last 90</th>
							<th scope="col"
								class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								LTV</th>
							<th scope="col"
								class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								AOV</th>
							<!-- <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Interaction</th> -->
							<th scope="col"
								class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Created Date</th>
						</tr>
					</thead>
					<tbody class="bg-white divide-y divide-gray-200">
						<tr v-for="customer in customers" :key="customer.id"
							class="hover:bg-indigo-50 hover:cursor-pointer">
							<td class="px-6 py-4 whitespace-nowrap">
								<a href="#" @click="openCustomer(customer)"
									class="text-indigo-600 hover:text-indigo-900">{{ customer.name }}</a>
							</td>
							<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ customer.email }}</td>
							<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ customer.purchases }}</td>
							<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
								${{ Math.round(customer.spend).toLocaleString() }}</td>
							<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${{
								Math.round(customer.revenue
									|| 0).toLocaleString() }}</td>
							<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${{ Math.round(customer.ltv ||
								0).toLocaleString() }}</td>
							<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${{ Math.round(customer.aov ||
								0).toLocaleString() }}</td>
							<!-- <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{customer.interaction}}</td> -->
							<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ customer.added }}</td>
						</tr>
					</tbody>
				</table>
				<div v-if="(customers?.length <= 0 || customers?.length == null) && !isLoading" class="p-4 text-base">
					No profiles match this segment yet.
				</div>
				<nav class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6"
					aria-label="Pagination">
					<div class="hidden sm:block">
						<p class="text-sm text-gray-700">
							Showing <span class="font-medium">{{ pageStart }}</span> to <span class="font-medium">{{
								pageEnd }}</span> of
							<span class="font-medium">{{ totalCustomerCount }}</span> profile results
							<!-- Page <span class="font-medium">{{ pageIndex + 1 }}</span>. <span class="font-medium">{{ totalCustomerCount }}</span> total customer results -->
						</p>
					</div>
					<div class="flex-1 flex justify-between sm:justify-end">
						<button @click="previousPage" :disabled="pageIndex <= 1"
							class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
							<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px"
								fill="#5f6368">
								<path
									d="m432-480 156 156q11 11 11 28t-11 28q-11 11-28 11t-28-11L348-452q-6-6-8.5-13t-2.5-15q0-8 2.5-15t8.5-13l184-184q11-11 28-11t28 11q11 11 11 28t-11 28L432-480Z" />
							</svg>
							Previous
						</button>
						<button @click="nextPage" :disabled="pageIndex >= totalPages"
							class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
							Next
							<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px"
								fill="#5f6368">
								<path
									d="M504-480 348-636q-11-11-11-28t11-28q11-11 28-11t28 11l184 184q6 6 8.5 13t2.5 15q0 8-2.5 15t-8.5 13L404-268q-11 11-28 11t-28-11q-11-11-11-28t11-28l156-156Z" />
							</svg>
						</button>
					</div>
				</nav>
			</div>
		</div>
	</div>

	<ModalBlank id="info-modal" :modalOpen="launchModelOpen" @close-modal="launchModelOpen = false">
		<div class="p-5">
			<div class="mb-2">
				<div class="text-lg font-semibold text-ralblack-primary">Connect Your Email Service Provider</div>
			</div>
			<div class="text-sm">
				<div class="space-y-2">
					<p class="text-ralblack-secondary">Select your email service provider below to be taken to begin
						integration setup.
					</p>

					<div class="p-6 flex justify-center">
						<div class="grid grid-cols-2 gap-6">
							<router-link to="/loyalty/integrations">
								<Button
									class="p-6 flex items-center justify-center rounded-lg gap-4 hover:scale-102 hover:shadow-lg transition-all duration-200 group border-2">
									<div
										class="rounded-full p-4 group-hover:scale-110 transition-transform duration-200">
										<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" height="32"
											xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
											viewBox="0 0 581 172" style="" xml:space="preserve">
											<g>
												<path d="M329.9,34.4c3.1,0,6.1-1.2,8.3-3.3c2.2-2.1,3.5-5.1,3.6-8.1c0-3.1-1.3-6.1-3.5-8.3c-2.2-2.2-5.2-3.5-8.3-3.5
													c-3.1,0.1-6,1.3-8.2,3.6c-2.2,2.2-3.4,5.2-3.3,8.2c0,3,1.2,5.9,3.4,8.1C324,33.1,326.9,34.3,329.9,34.4L329.9,34.4z M410.5,45.4
													h28.6v2.3c-1.6,0.3-3.1,0.8-4.5,1.6c-2.6,1.2-7.8,7.1-11.9,17c-6.8,17.1-13.9,37.4-21.4,60.6l-2.8,8.8c-1.2,4-2.3,6.6-2.8,8.1
													c-0.5,1.6-1.2,4-2.4,6.9c-0.7,2.2-1.6,4.4-2.7,6.4c-1.4,2.6-4,7.9-6.1,9.5c-3.3,2.8-8.2,5.9-14.3,5.4c-11.9,0-20.8-8.8-20.9-19.2
													c0-7.1,4.5-11.8,11.3-11.8c4.9,0,9.2,2.6,9.2,8.1c0,4-4,8.1-4,10.2c0,5.4,3.1,7.9,9.2,7.9c4.9,0,8.9-3.1,11.9-9.3
													c4-7.1,4.4-14.9,1-23.6l-25.1-66c-5.8-15.2-10.1-20.2-15.5-20.8v-2.3h39.6v2.3c-4.7,0.5-7.1,3.3-7.1,8.3c0,3.6,1.4,9.2,4,16.3
													l4.7,12.8c5.4,13.9,9.8,26,12,33.6c5.1-15.8,9.8-29.6,14.3-41.7c3.3-9,4.9-15.4,4.9-19.2c0-6.8-3.7-9.9-9.2-9.9L410.5,45.4
													L410.5,45.4z M135.2,129.7c-5.1-0.9-9.4-5.4-9.4-14.9V0L97,6.2v2.4c4.9-0.5,9.8,3.8,9.8,13v93.2c0,9-4.9,14.2-9.8,14.9
													c-0.5,0.1-0.9,0.1-1.4,0.2c-2.5,0.2-5-0.3-7.3-1.3c-3.9-1.7-7.1-4.7-9.8-9.1L65.2,98.4c-2.8-4.5-7-7.9-11.9-9.8
													c-4.9-1.9-10.3-2.2-15.4-0.8l15-16.5c11.3-12.5,21.8-20.4,31.7-23.7v-2.3h-33v2.3c8.5,3.3,8,10.6-1.8,22L28.8,94V0L0,6.2v2.4
													c4.9,0,9.8,4.8,9.8,13.3v92.8C9.8,125,5,129,0,129.7v2.3h38.2v-2.3c-6.3-0.9-9.4-5.7-9.4-14.9V97.7l8.2-9l19.8,32.4
													c4.7,7.8,9.1,10.9,16,10.9h66.4v-1.8C139.3,130.2,137.4,130.1,135.2,129.7L135.2,129.7z M214.4,118.8V80
													c-0.4-25.3-11.1-36.9-35.6-36.9c-7.8-0.1-15.4,2.5-21.6,7.3c-6.5,4.9-9.6,10.4-9.6,16.8c0,6.2,3.5,10.9,9.2,10.9
													c6.1,0,10.5-3.5,10.5-8.3c0-3.6-2.4-8.7-2.4-12.1c0-6.2,4.7-11.6,12.9-11.6c10.5,0,18,7.8,18,25.1v10.4l-8.7,2.1
													c-4.5,0.9-8.4,1.8-11.3,2.6c-3,0.9-6.8,2.1-11.3,3.8c-9.1,3.5-13.9,6.8-18.1,13c-2.1,3-3.1,6.6-3.1,10.2c0,14.4,10.1,21,24.2,21
													c11.2,0,23-5.9,28.4-17c0.1,3.5,0.9,6.9,2.5,10.1c5.9,11.9,25.5,4.9,25.5,4.9V130C215.1,131.3,214.4,121.2,214.4,118.8L214.4,118.8
													z M195.8,106.8c0,5.7-2.1,10.4-6.3,13.7c-4,3.3-8.2,5-12.6,5c-8.5,0-14.1-5.5-14.1-15.8c0-4.8,2.7-9.3,4.9-11.9
													c1.8-1.8,3.8-3.3,6.1-4.3c3-1.6,4.4-2.4,6.5-3.3l8-2.9c4-1.6,6.4-2.4,7.5-2.9L195.8,106.8L195.8,106.8z M581,45.4h-67.8V0H581
													l-14.2,22.7L581,45.4L581,45.4z M443.1,120.9c-8.4-8.6-13.1-20.2-12.9-32.3c-0.1-5.9,1-11.8,3.2-17.3c2.2-5.5,5.5-10.5,9.7-14.8
													c8.6-9,19-13.5,31.3-13.5c12,0,22.5,4.5,31.1,13.5c4.2,4.2,7.6,9.2,9.8,14.7c2.3,5.5,3.4,11.4,3.3,17.4c0.1,6-1,11.9-3.3,17.4
													c-2.3,5.5-5.6,10.6-9.8,14.8c-8.6,8.8-19,13.4-31.1,13.4C462.2,134.3,451.7,129.8,443.1,120.9L443.1,120.9z M490.1,58
													c-3.4-6.7-8-10.6-13.3-11.7c-10.8-2.2-20.3,8.9-23.9,26.4c-1.5,7.9-1.9,15.9-1.1,23.8c0.8,8,3,15.7,6.6,22.9
													c3.5,6.7,8,10.6,13.3,11.7c10.8,2.2,20.6-9.3,24.2-27C498.9,89.3,497.4,71.3,490.1,58L490.1,58L490.1,58z">
												</path>
												<path
													d="M340,114.8V45.4h-61.4v2.1c8.2,1.2,12.1,7.4,8.4,17.3c-19.2,51.8-18,49.5-19.2,53.6c-1.2-4-4-13.8-8.5-26.1
													c-4.5-12.3-7.5-20.4-8.7-24.1c-4.7-14.4-3.1-19.7,4.5-20.6v-2.3h-39.8v2.3c5.9,1.2,11.2,8,15.5,20.1l6.1,15.8
													c6.7,17,14.6,40.5,17.2,48.5h13.2c4.3-12.3,21.3-61.5,23.6-66.5c2.5-5.7,5.3-10,8.4-13c1.5-1.6,3.3-2.9,5.4-3.7
													c2-0.8,4.2-1.3,6.4-1.2c0,0,9.6,0,9.6,9.2v58.1c0,9.7-4.7,14.2-9.6,14.9v2.3h38v-2.3C344.2,129,340,124.5,340,114.8L340,114.8z">
												</path>
											</g>
										</svg>
									</div>
									<div class="text-center">

									</div>
								</Button>
							</router-link>
							<router-link to="/loyalty/integrations">
								<Button
									class="p-6 flex flex-col items-center justify-center rounded-lg gap-4 hover:scale-102 hover:shadow-lg transition-all duration-200 group border-2">
									<div
										class="rounded-full p-4 group-hover:scale-110 transition-transform duration-200">
										<svg width="165" height="33" viewBox="0 0 165 33" fill="none"
											xmlns="http://www.w3.org/2000/svg">
											<g clip-path="url(#clip0_898_9247)">
												<path
													d="M32.8661 23.7862L34.5853 21.109C36.044 22.2659 38.1949 22.9333 40.3532 22.9333C42.5115 22.9333 43.6279 22.2733 43.6279 21.0793C43.6279 19.8854 42.5636 19.3588 40.0481 19.0918C35.7835 18.6988 33.3498 16.9041 33.3498 14.0638C33.3498 11.2235 36.2152 9.06543 40.4574 9.06543C42.9432 9.06543 45.1313 9.69579 46.9547 10.9491L45.3322 13.604C43.9331 12.6918 42.2957 12.2098 40.6212 12.2172C38.2693 12.2172 37.0041 12.8772 37.0041 14.0712C37.0041 15.1317 37.9642 15.6953 40.3532 15.9549C45.1983 16.4888 47.3119 18.0388 47.3119 21.1164C47.3119 24.194 44.4912 26.2112 40.2416 26.2112C37.5772 26.2409 34.9723 25.3954 32.8438 23.801"
													fill="black" />
												<path fill-rule="evenodd" clip-rule="evenodd"
													d="M52.0675 19.0253H65.1663C65.6947 12.9071 62.5093 9.13232 56.7414 9.13232C51.6656 9.13232 48.2495 12.5733 48.2495 17.631C48.2495 22.6888 51.7326 26.2633 57.1358 26.2633C59.9565 26.2633 62.5763 25.1657 64.1988 23.4229L61.9511 21.2352C60.9613 22.2586 59.1007 22.9261 57.24 22.9261C54.3524 22.9261 52.3652 21.4058 52.0675 19.0178V19.0253ZM61.6237 16.1479H52.1047C52.5662 13.7302 54.2556 12.3434 56.853 12.3434C59.5695 12.3434 61.3632 13.8341 61.6311 16.1479H61.6237Z"
													fill="black" />
												<path
													d="M82.9319 16.7707V25.8034H79.2181V17.008C79.2181 14.1306 77.7221 12.4768 75.2363 12.4768C72.5198 12.4768 70.6294 14.4643 70.6294 17.2082V25.8108H66.9082V9.46595H70.2276L70.2573 11.6166C71.6491 9.96282 73.6064 9.13965 75.9285 9.13965C80.1409 9.13965 82.9319 12.1209 82.9319 16.7781"
													fill="black" />
												<path fill-rule="evenodd" clip-rule="evenodd"
													d="M101.947 25.8034V2.1167H98.2334V11.2161C96.9384 9.89607 94.914 9.10256 92.5622 9.10256C87.9181 9.10256 84.502 12.4768 84.502 17.6384C84.502 22.7999 87.9181 26.1742 92.5622 26.1742C95.1522 26.1742 97.3402 25.2175 98.598 23.6231L98.6278 25.8108H101.947V25.8034ZM93.2246 12.4768C96.1792 12.4768 98.2334 14.5904 98.2334 17.6384C98.2334 20.6863 96.1718 22.7999 93.2246 22.7999C90.2773 22.7999 88.2158 20.6863 88.2158 17.6384C88.2158 14.5904 90.2699 12.4768 93.2246 12.4768Z"
													fill="black" />
												<path d="M108.415 2.1167H104.701V25.8034H108.415V2.1167Z"
													fill="black" />
												<path fill-rule="evenodd" clip-rule="evenodd"
													d="M124.371 25.8035H127.691V9.46611H124.371L124.342 11.639C123.084 10.052 120.926 9.08789 118.373 9.08789C113.565 9.08789 110.246 12.5957 110.246 17.6237C110.246 22.6517 113.565 26.1595 118.373 26.1595C120.888 26.1595 123.084 25.2028 124.342 23.6084L124.371 25.7961V25.8035ZM118.968 12.477C121.93 12.477 123.977 14.5906 123.977 17.6385C123.977 20.6865 121.923 22.8001 118.968 22.8001C116.014 22.8001 113.959 20.6865 113.959 17.6385C113.959 14.5906 116.006 12.477 118.968 12.477Z"
													fill="black" />
												<path
													d="M146.468 16.7707V25.8034H142.755V17.008C142.755 14.1306 141.259 12.4768 138.773 12.4768C136.056 12.4768 134.166 14.4643 134.166 17.2082V25.8108H130.452V9.46595H133.764L133.794 11.6166C135.186 9.96282 137.143 9.13965 139.465 9.13965C143.677 9.13965 146.461 12.1209 146.461 16.7781"
													fill="black" />
												<path fill-rule="evenodd" clip-rule="evenodd"
													d="M151.849 19.0253H164.948C165.476 12.9071 162.291 9.13232 156.523 9.13232C151.447 9.13232 148.031 12.5733 148.031 17.631C148.031 22.6888 151.514 26.2633 156.918 26.2633C159.738 26.2633 162.358 25.1657 163.981 23.4229L161.733 21.2352C160.743 22.2586 158.882 22.9261 157.022 22.9261C154.134 22.9261 152.147 21.4058 151.849 19.0178V19.0253ZM161.405 16.1479H151.886C152.348 13.7302 154.037 12.3434 156.635 12.3434C159.366 12.3434 161.145 13.8341 161.413 16.1479H161.405Z"
													fill="black" />
												<path
													d="M24.8056 16.5186C24.7535 15.2949 24.1804 14.1529 23.3171 13.2852C23.3171 13.2852 18.2636 8.30906 15.7704 5.8247C15.6513 5.6838 15.5322 5.54289 15.4057 5.41682L11.8482 1.87198C10.0248 0.0550611 7.04033 0.040229 5.19459 1.8349L1.42125 5.51323C0.483494 6.4254 -0.0225956 7.65645 -0.000268087 8.96908C0.0220594 10.2298 0.587689 11.4164 1.48823 12.2989L13.8725 24.6242L9.81638 28.6659C9.35494 29.1332 8.54371 29.1257 8.07483 28.6659L4.36103 24.9728C4.07077 24.6836 3.95169 24.2905 4.02612 23.8752C4.07077 23.6231 4.21218 23.3932 4.39824 23.2078L9.12422 18.506L7.06265 15.6361L1.89757 20.7753C0.959813 21.7098 0.461166 22.9705 0.520706 24.3053C0.572804 25.529 1.14588 26.671 2.0092 27.5387L5.49973 31.0168C6.3705 31.8845 7.51665 32.4481 8.74466 32.5C10.0843 32.5593 11.357 32.0625 12.2947 31.1281L17.5938 25.8553L23.4361 20.0412C24.3739 19.1067 24.8725 17.846 24.813 16.5111L24.8056 16.5186ZM3.52003 8.63536C3.56468 8.38322 3.71353 8.14591 3.8996 7.96792L7.64317 4.31926C8.11949 3.85205 8.90095 3.85205 9.36983 4.32667L13.039 7.97534C13.4334 8.36839 13.5227 8.9765 13.1878 9.52528L8.41719 14.2715L3.85494 9.73293C3.56468 9.4437 3.43816 9.04324 3.52003 8.63536ZM20.9429 17.5865L16.3434 22.1621L10.8955 16.741L15.5099 12.1505C15.8001 11.8613 16.0457 11.535 16.2467 11.1865L20.9057 15.8289C21.0917 16.0143 21.2332 16.2442 21.2778 16.4963C21.3522 16.9042 21.2257 17.3047 20.9429 17.5939V17.5865Z"
													fill="#FF3C78" />
											</g>
											<defs>
												<clipPath id="clip0_898_9247">
													<rect width="165" height="32" fill="white"
														transform="translate(0 0.5)" />
												</clipPath>
											</defs>
										</svg>

									</div>

								</Button>
								<div class="text-center italic">
									Coming soon!
								</div>
							</router-link>
						</div>
					</div>
					<div class="flex flex-wrap justify-end mt-4">
						<CancelButton @click="launchModelOpen = false" cta="Cancel"></CancelButton>
					</div>
				</div>
			</div>
		</div>
	</ModalBlank>

	<!-- Export Notification Modal -->
	<ModalBlank id="export-notification-modal" :modalOpen="exportModalOpen" @close-modal="exportModalOpen = false">
		<div class="p-5">
			<div class="mb-5 text-center">
				<div class="inline-flex justify-center items-center w-12 h-12 rounded-full bg-green-100 mb-4">
					<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
					</svg>
				</div>
				<div class="text-lg font-semibold text-ralblack-primary mb-2">Export Request Received</div>
				<p class="text-ralblack-secondary">
					Your segment export is being processed. Once complete, it will be emailed to your registered email address.
				</p>
			</div>
			<div class="flex justify-center">
				<PrimaryButton size="small" @click="exportModalOpen = false" cta="Got it"></PrimaryButton>
			</div>
		</div>
	</ModalBlank>
</template>

<script>

import * as Utils from '../../client-old/utils/Utils';
import CardContainer from '../components/CardContainer.ts.vue';
import LightSecondaryButton from '../components/LightSecondaryButton.ts.vue'
import PrimaryButton from '../components/PrimaryButton.ts.vue';
import ToggleItem from '../components/ToggleItem.ts.vue';
import StatusMessage from '../components/StatusMessage.ts.vue';
import LearnMoreText from '../components/LearnMoreText.ts.vue';
import CancelButton from '../components/CancelButton.ts.vue';
import EditableHeader from '../components/EditableHeader.ts.vue'
import RaleonLoader from '../components/RaleonLoader.ts.vue';
import TextFadeEffect from '../components/TextFadeEffect.ts.vue';
import ModalBlank from '../components/ModalBlank.vue';
import Tooltip from '../components/Tooltip.ts.vue';
import ToastStatus from '../../client-old/pages/component/ToastStatus.vue'
import { customerIOTrackEvent } from '../services/customerio.js';
import * as ******************** from '../services/organization-settings.js';
import SignalBadge from '../components/SignalBadge.ts.vue';

import { useRoute } from 'vue-router';
import axios from 'axios';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
		components: {
		CardContainer,
		LightSecondaryButton,
		StatusMessage,
		ToggleItem,
		PrimaryButton,
		LearnMoreText,
		CancelButton,
		EditableHeader,
		RaleonLoader,
		TextFadeEffect,
		ModalBlank,
		Tooltip,
		ToastStatus,
		SignalBadge,
	},
	data() {
		return {
			insights: false,
			history: false,
			overview: true,
			partners: false,
			isLoading: false,
			isDownloading: false,
			exportModalOpen: false,  // New state for export notification modal
			segment: {},
			generateTest: false,
			launchModelOpen: false,
			campaign: { name: 'Likely to buy in next 30 days' },
			testLoading: true,
			hoveredRowIndex: 0,
			aiLoadText: ["Analyzing your audience", "Taking a look at your product...", "Playing match-maker...", "Clipping some coupons...", "Your Smart Offer preview is ready!"],
			aiTextIndex: 0,
			pageIndex: 1,
			pageStart: 0,
			pageEnd: 0,
			totalCustomerCount: 0,
			percentageEmailOpens: 0,
			emailOpenUniquesL30: 0,
			emailOpenUniquesL90: 0,
			emailOpenTotalL30: 0,
			emailOpenTotalL90: 0,
			averageAov: 0,
			averageLtv: 0,
			totalRevenue: 0,
			totalSpend: 0,
			customers: [],
			status: '',
			statusText: '',
			emailPixelTrackingEnabled: false,
			klaviyoMetricCompleted: false
		}
	},
	computed: {
		formattedDate() {
			const date = new Date(this.segment.externalSyncDate);
			const day = String(date.getDate()).padStart(2, '0');
			const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are zero-indexed
			const year = date.getFullYear();
			return `${month}/${day}/${year}`;
		}
	},
	methods: {
		clearStatus() {
			this.status = '';
			this.statusText = '';
		},

		// Modified download segment members method
		async downloadSegmentMembers() {
			if (!this.segment?.id) return;

			try {
				this.isDownloading = true;

				// Make API call to request the export
				const response = await fetch(`${URL_DOMAIN}/export-segment-shoppers?segmentId=${this.segment.id}`, {
					method: 'POST',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`
					}
				});

				if (!response.ok) {
					throw new Error('Failed to request segment export');
				}

				// Show success notification
				this.status = 'success';
				this.statusText = 'Export request received';
				customerIOTrackEvent('Segment Export Requested');

				// Show the export notification modal
				this.exportModalOpen = true;

			} catch (error) {
				console.error('Error requesting segment export:', error);
				this.status = 'fail';
				this.statusText = 'Failed to request segment export';
			} finally {
				this.isDownloading = false;
			}
		},

		async checkKlaviyoMetric() {
			try {
				const response = await fetch(`${URL_DOMAIN}/organization-metrics-status`, {
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`
					}
				});
				const data = await response.json();
				this.klaviyoMetricCompleted = data.metrics.some(
					metric => metric.name === 'klaviyo_sync_attributes' && metric.completed
				);
			} catch (error) {
				console.error('Failed to check Klaviyo metric:', error);
			}
		},
		async saveCampaignName() {
			await fetch(`${URL_DOMAIN}/organization-segment/${this.segment.id}`, {
				method: 'PATCH',
				headers: {
					'Content-Type': 'application/json',
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify({
					name: this.campaign.name,
					// description: this.campaign.description,
				})
			});

		},
		getGradientClass(grade) {
			switch (grade) {
				case 'A':
					return 'bg-gradient-to-r from-green-400 to-green-500';
				case 'B':
					return 'bg-gradient-to-r from-blue-400 to-blue-500';
				case 'C':
					return 'bg-gradient-to-r from-orange-400 to-orange-500';
				case 'D':
					return 'bg-gradient-to-r from-red-400 to-red-500';
				case 'F':
					return 'bg-gradient-to-r from-gray-400 to-gray-500';
				default:
					return 'bg-gradient-to-r from-gray-400 to-gray-500';
			}
		},

		async fetchPage() {
			this.isLoading = true;
			try {
				const segmentId = this.$route.params.id;
				const response = await fetch(`${URL_DOMAIN}/organization-segment/${segmentId}?page=${this.pageIndex}`, {
					method: 'GET',
					headers: {
						'Content-Type': 'application/json',
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
					}
				});

				this.data = await response.json();
				this.customers = this.data.shoppers?.map?.(x => ({
					id: x.id,
					name: x.first_name + ' ' + x.last_name,
					email: x.email,
					purchases: x.orders_count || 0,
					added: x.created_at
						? (new Date(x.created_at).getMonth() + 1) + '/' + new Date(x.created_at).getDate() + '/' + new Date(x.created_at).getFullYear()
						: '',
					interaction: (new Date().getMonth() + 1) + '/' + new Date().getDate() + '/' + new Date().getFullYear(),
					spend: x.customer?.ltv || x.ltv || 0,
					ltv: x.customer?.ltv || x.ltv || 0,
					aov: x.customer?.aov || x.aov || 0,
					revenue: x.customer?.revenue || x.revenue || 0
				})) || [];

				this.segment = this.data;

				// Use aggregates from backend
				const aggregates = this.data.aggregates || {};
				this.averageLtv = aggregates.averageLtv || 0;
				this.averageAov = aggregates.averageAov || 0;
				this.totalRevenue = aggregates.totalRevenue || 0;
				this.totalSpend = aggregates.totalSpend || 0;
				this.totalCustomerCount = aggregates.totalCount || 0;
				this.emailOpenUniquesL30 = aggregates.emailOpenUniquesL30 || 0;
				this.emailOpenUniquesL90 = aggregates.emailOpenUniquesL90 || 0;
				this.emailOpenTotalL30 = aggregates.emailOpenTotalL30 || 0;
				this.emailOpenTotalL90 = aggregates.emailOpenTotalL90 || 0;

				// Use pagination from backend
				const pagination = this.data.pagination || {};
				this.pageStart = ((pagination.currentPage || 1) - 1) * (pagination.pageSize || 10) + 1;
				this.pageEnd = Math.min((pagination.currentPage || 1) * (pagination.pageSize || 10), pagination.totalItems || 0);
				this.totalPages = pagination.totalPages || 0;
				this.pageIndex = pagination.currentPage || 1;

				this.campaign.name = this.data.name;
				this.campaign.description = this.data.description;
			} catch (error) {
				console.error('Error fetching segment data:', error);
			} finally {
				this.isLoading = false;
			}

		},
		nextPage() {
			if (this.pageIndex < this.totalPages) {
				this.pageIndex++;
				this.fetchPage().catch(console.error);
			}
		},
		previousPage() {
			if (this.pageIndex > 1) {
				this.pageIndex--;
				this.fetchPage().catch(console.error);
			}
		},
		async handleKlaviyoSync() {
			customerIOTrackEvent('AI Segment - Klavio Sync');
			try {
				await this.klavioSync();
				await this.fetchPage();
			} catch (error) {
				console.error('Failed to sync with Klaviyo:', error);
			}
		},

		async klavioSync() {
			this.isLoading = true;
			try {
				const connectionResponse = await this.checkKlaviyoConnection();
				if (connectionResponse.connected) {
					await this.syncOrganizationSegment();
				} else {
					console.error('Klaviyo integration is not connected');
					this.launchModelOpen = true;
				}
			} catch (error) {
				console.error('An error occurred:', error);
			} finally {
				this.isLoading = false;
			}
		},

		async checkKlaviyoConnection() {
			const response = await fetch(`${URL_DOMAIN}/integration/klaviyo/connected`, {
				method: 'GET',
				headers: {
					'Content-Type': 'application/json',
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
				}
			});
			if (!response.ok) {
				throw new Error('Failed to check Klaviyo connection');
			}
			return response.json();
		},

		async syncOrganizationSegment() {
			const response = await fetch(`${URL_DOMAIN}/organization-segment/${this.segment.id}/integration-sync`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
				}
			});
			const data = await response.json();
			if (!response.ok) {
				this.status = 'fail';
				this.statusText = data.error?.message || 'Failed to sync organization segment';
				throw new Error(`Klaviyo Error: ${this.statusText}`);
			}
			this.status = 'success';
			this.statusText = 'Successfully synced to Klaviyo';
		},

		openCustomer(customer) {
			window.open(`/customer/${customer.id}`, '_blank');
		},


		async archiveSegment() {
			try {
				const response = await fetch(`${URL_DOMAIN}/organization-segment/${this.segment.id}`, {
					method: 'PATCH',
					headers: {
						'Content-Type': 'application/json',
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
					},
					body: JSON.stringify({
						archived: true,
						archivedDate: new Date().toISOString()
					})
				});

				if (response.ok) {
					this.status = 'success';
					this.statusText = 'Segment archived successfully';
					this.segment.archived = true;
					this.segment.archivedDate = new Date().toISOString();
					this.$router.push('/ai-segments/overview');
				} else {
					this.status = 'fail';
					this.statusText = 'Failed to archive segment';
				}
			} catch (error) {
				console.error('Error archiving segment:', error);
				this.status = 'fail';
				this.statusText = 'Failed to archive segment';
			}
		},

		// async unarchiveSegment() {
		// 	try {
		// 		const response = await fetch(`${URL_DOMAIN}/organization-segment/${this.segment.id}`, {
		// 			method: 'PATCH',
		// 			headers: {
		// 				'Content-Type': 'application/json',
		// 				'Authorization': `Bearer ${localStorage.getItem('token')}`,
		// 			},
		// 			body: JSON.stringify({
		// 				archived: false,
		// 				archivedDate: null
		// 			})
		// 		});

		// 		if (response.ok) {
		// 			this.status = 'success';
		// 			this.statusText = 'Segment unarchived successfully';
		// 			this.segment.archived = false;
		// 			this.segment.archivedDate = null;
		// 			this.$router.push('/ai-segments/overview');
		// 		} else {
		// 			this.status = 'fail';
		// 			this.statusText = 'Failed to unarchive segment';
		// 		}
		// 	} catch (error) {
		// 		console.error('Error unarchiving segment:', error);
		// 		this.status = 'fail';
		// 		this.statusText = 'Failed to unarchive segment';
		// 	}
		// },
	},
		async mounted() {
			customerIOTrackEvent('Viewed AI Segment');
			this.fetchPage().catch();
			await this.checkKlaviyoMetric();
			this.emailPixelTrackingEnabled = await ********************.getOrganizationSetting('emailPixel');
		},
}
</script>

<style scoped>
.no-focus-outline {
	box-shadow: none !important;
	outline: none !important;
}

.numberChart {
	color: rgba(32, 32, 32, 0.80);
	font-family: Inter;
	font-size: 2rem;
	font-style: normal;
	font-weight: 600;
	line-height: normal;
	letter-spacing: -2.4px;
	text-transform: uppercase;
}
</style>
