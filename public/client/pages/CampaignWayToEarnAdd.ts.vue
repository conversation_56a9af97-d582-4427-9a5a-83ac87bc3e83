<template>
	<SuperModal>
		<SuperModalHeader :go-home-on-logo-click="true" :collapse-logo="true" :showHeaderBackground="true"
			:show-close-button="true" :back-button-href="backButtonHref || '/loyalty-program'">
			<span v-if="!isShopReward" class="w-full sm:w-auto text-3xl sm:text-5xl">Create Way to Earn</span>
			<span v-if="isShopReward" class="w-full sm:w-auto text-3xl sm:text-5xl">Create Reward</span>

			<div class="flex-grow"></div>

			<div class="flex flex-row lg:items-start lg:justify-end items-center justify-between w-full md:w-1/2 lg:w-1/3">
				<div class="flex flex-row items-center pr-2 sm:pr-5 justify-center flex-grow h-20">
					<PreviewLoyaltyProgram />
				</div>
				<!-- <div class="flex flex-col items-center justify-center flex-shrink flex-grow mr-2 sm:mr-7">
					<div class="text-black text-opacity-50 text-sm font-medium font-['Inter'] leading-10">
						Visible To Shopper
					</div>
					<ToggleItem @toggleChange="toggleChange" :state="isToggleEnabled" :isDisabled="isToggleDisabled"
						:showLabel="true" :offLabel="InActive" :onLabel="Active"></ToggleItem>
				</div> -->
				<div class="flex-shrink-0">
					<PrimaryButton cta="Save" @click="enable" size="small" class="mr-2 sm:mr-7"></PrimaryButton>
				</div>
			</div>
		</SuperModalHeader>

		<div class="w-full flex justify-center mt-6 overflow-auto">
    		<div class="flex flex-col gap-4 items-center md:ml-44">
				<CampaignWTEActivity
					:earn="this.earn"
					:is-shop-reward="this.isShopReward"
					:wte-summary-data="this.wteSummaryData"
				/>

				<template v-for="(reward, index) of this.earn.rewards">
					<CampaignWTERewards
						:earn="this.earn"
						:is-single-reward="this.isShopReward"
						:wte-summary-data="this.wteSummaryData"
						:index="index"
						@is-complete="loadDefaultsWTE()"
					/>
				</template>
				<div v-if="(earn.rewards?.length < earn.condition?.maxRewardsForThisAction)">
					<div
						class="w-full h-28 rounded-2xl border-4 border-violet-200 border-dashed flex justify-center items-center cursor-pointer hover:border-solid hover:border-ralpurp-dark hover:bg-white hover:shadow-lg transition-all duration-500 ease-in-out opacity-75"
						@click="addReward()">
						<div class="text-ralblack-secondary text-opacity-75 hover:text-opacity-100 text-3xl font-bold font-['Inter'] leading-loose">
							+ Add Reward
						</div>
					</div>
				</div>
				<!-- </template> -->
				<!-- <TabSwitcher :ref="tabSwitcher" :tabs="getTabs" :tabsEnabled="tabsEnabled" /> -->

				<CampaignWTELivePreview
					:earn="this.earn"
					:is-shop-reward="this.isShopReward"
					:wte-summary-data="this.wteSummaryData"
				/>
			</div>
			<WTESummary
				v-if="!isShopReward"
				:wte-data="this.wteSummaryData.wte"
				:editing-reward-data="editingRewardData"
				:reward-list-data="this.wteSummaryData.rewards"
				class="hidden md:block"
			/>
		</div>

		<StatusMessage :message=status.message :status=status.type @resetStatus="status.type = 'nope'"></StatusMessage>
	</SuperModal>
</template>

<script>

import * as Utils from '../../client-old/utils/Utils';
import SuperModal from '../components/SuperModal.ts.vue';
import ToggleItem from '../components/ToggleItem.ts.vue';
import SuperModalHeader from '../components/SuperModalHeader.ts.vue';
import TabSwitcher from '../components/TabSwitcher.ts.vue';
import StatusMessage from '../components/StatusMessage.ts.vue'
import CampaignWTEActivity from './subpages/CampaignWTEActivity.ts.vue';
import CampaignWTERewards from './subpages/CampaignWTERewards.ts.vue';
import CampaignWTELivePreview from './subpages/CampaignWTELivePreview.ts.vue';
import CampaignShopItemAdd from './subpages/CampaignShopItemAdd.ts.vue';
import PrimaryButton from '../components/PrimaryButton.ts.vue';
import Tooltip from '../components/Tooltip.ts.vue';
import WTESummary from '../components/WTESummary.ts.vue';
import PreviewLoyaltyProgram from '../components/PreviewLoyalty.ts.vue';
import { customerIOTrackEvent } from '../services/customerio.js';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	props: ['campaignId', 'wayToEarnId', 'isFoundationalCampaign'],
	components: {
		SuperModal,
		SuperModalHeader,
		TabSwitcher,
		StatusMessage,
		ToggleItem,
		PrimaryButton,
		Tooltip,
		PreviewLoyaltyProgram,
		CampaignWTEActivity,
		CampaignWTERewards,
		CampaignWTELivePreview,
		WTESummary,
	},
	watch: {
		'earn.rewards': {
			handler: function (newVal, oldVal) {
				this.generateWTESummary();
				//Lets see if there are any missing names
				for (let i = 0; i < newVal?.length; i++) {
					if (this.isShopReward) {
						if (this.earn.rewards[0].name || this.earn.rewards[0].description || this.earn.rewards[0].imageURL) {
							break;
						}
						if (this.earn.rewards && (this.earn.rewards[0].amount > 0 || this.earn.rewards[0].type == 'free-shipping')) {
							clearTimeout(this.predictShopItemTextTimeout);
							this.predictShopItemTextTimeout = setTimeout(() => {
								this.predictShopItemText();
							}, 1000);
							break;
						}
					}
					else {
						if (this.earn.rewards && (this.earn.rewards[0].amount > 0 || this.earn.rewards[0].type == 'free-shipping')) {
							clearTimeout(this.predictWTETextTimeout);
							this.predictWTETextTimeout = setTimeout(() => {
								this.loadDefaultsWTE();
							}, 1000);
							break;
						}
					}

				}
			},
			deep: true
		},
		'earn.name': {
			handler: function (newVal, oldVal) {
				this.generateWTESummary();
			}
		},
		'earn.condition': {
			handler: function (newVal, oldVal) {
				this.generateWTESummary();
			},
			deep: true
		},
		'$route.params.wayToEarnId'(newVal, oldVal) {
			if (newVal !== oldVal) {
				this.localWayToEarnId = newVal;
			}
		}
	},

	computed: {
		isShopReward() {
			return this.$route.path.includes('shop-reward');
		},

		isToggleEnabled() {
			if (this.isShopReward && this.earn.shopItem) {
				if (this.earn.shopItem.active === undefined)
					this.earn.shopItem.active = true;

				return this.earn.shopItem?.active;
			}
			else {
				if (this.earn.active === undefined)
					this.earn.active = true;

				return this.earn.active;
			}
		},

		isToggleDisabled() {
			return !this.localWayToEarnId || this.localWayToEarnId == {} || this.localWayToEarnId == 0;
		},

		getWTEActivityProps() {
			return {
				earn: this.earn,
				isShopReward: this.isShopReward,
				wteSummaryData: this.wteSummaryData,
			}
		},
		getRewardForCustomerProps() {
			return {
				earn: this.earn,
				isSingleReward: this.isShopReward,
				wteSummaryData: this.wteSummaryData,
			}
		},

		getStyleAndPreviewProps() {
			return {
				earn: this.earn,
				isShopReward: this.isShopReward,
				wteSummaryData: this.wteSummaryData,
			}
		},
		getTabs() {
			return [{
				name: (this.isShopReward ? '1. Reward Cost' : '1. Customer Action'),
				component: this.isShopReward ? CampaignShopItemAdd : CampaignWTEActivity,
				props: this.getWTEActivityProps,
				eventListeners: {
					isComplete: (isComplete) => {
						this.tabsEnabled[1] = isComplete;
					}
				},
			}, {
				name: '2. Reward for Customer',
				component: CampaignWTERewards,
				props: this.getRewardForCustomerProps,
				eventListeners: {
					isComplete: (isComplete) => {
						this.tabsEnabled[2] = isComplete;
					}
				},
			}, {
				name: '3. Style & Preview',
				component: CampaignWTELivePreview,
				props: this.getStyleAndPreviewProps,
				eventListeners: {
					isComplete: (isComplete) => {

					},
					enable: () => {
						this.enable();
					}
				},
			}]
		},
		backButtonHref() {
			if(!this.isFoundationalCampaign){
				return `/loyalty/campaign/${this.campaignId}`;
			}
			else return `/loyalty/program`;
		}
	},
	async mounted() {
		const response = await fetch(`${URL_DOMAIN}/loyalty-campaigns/${this.campaignId}`, {
			method: 'GET',
			credentials: 'omit',
			mode: 'cors',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${localStorage.getItem('token')}`,
			}
		});

		if (response.ok && response.status >= 200 && response.status < 300) {
			const result = await response.json();

			this.campaign = result;
			this.earn.campaign = result;
			if (!this.earn.rewards || this.earn?.rewards?.length == 0) {
				this.addReward();
			}

		}


		if (this.localWayToEarnId && this.localWayToEarnId > 0 && !this.isShopReward) {
			const wteResponse = await fetch(`${URL_DOMAIN}/wte/${this.localWayToEarnId}/load`, {
				method: 'GET',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
			});

			if (wteResponse.ok && wteResponse.status >= 200 && wteResponse.status < 300) {
				const wteData = await wteResponse.json();
				this.earn = wteData.earn;
				this.earn.predictionComplete = true;
				this.earn.condition = wteData.condition; //Need to make this work with more than 1
				this.earn.rewards = [...wteData.rewards];
				this.earn.campaign = this.campaign;
				//this.wteData = wteData;
			}
		}
		else if (this.localWayToEarnId && this.localWayToEarnId > 0 && this.isShopReward) {
			const shopItemResponse = await fetch(`${URL_DOMAIN}/shopitem/${this.localWayToEarnId}/load`, {
				method: 'GET',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
			});

			if (shopItemResponse.ok && shopItemResponse.status >= 200 && shopItemResponse.status < 300) {
				const shopItemData = await shopItemResponse.json();
				this.earn.shopItem = shopItemData.shopitem;
				this.earn.shopItemConfig = shopItemData.shopItemConfig;
				this.earn.rewards = shopItemData.rewards;
				this.earn.predictionComplete = true;
				this.earn.campaign = this.campaign;
			}
		}
		else if (this.isShopReward && !this.localWayToEarnId) {
			this.earn.shopItem = {
				name: '',
				description: '',
				imageURL: '',
			};
		}
	},
	data() {
		const earn = {};
		const isShopReward = this.$route.path.includes('shop-reward');

		return {
			earn,
			campaign: {},
			wteData: {},
			isShopReward: isShopReward,
			status: { 'message': null, type: 'nope' },
			tabsEnabled: {
				0: true,
				1: false,
				2: false,
			},
			localWayToEarnId: this.wayToEarnId,
			shopItemPredictionRunning: false,
			predictShopItemTextTimeout: null,
			predictWTETextTimeout: null,
			wteSummaryData: {},
		}
	},
	methods: {
		completeTab(index) {
			const tabs = this.getTabs;
			if (!tabs || tabs.length <= 0) return;
			tabs[index].completed = true;
			if (tabs.length > index + 1) {
				tabs[index].enabled = true;
			}
		},
		addReward() {
			if (!this.earn.rewards) this.earn.rewards = [];
			this.earn.rewards.push({
				restrictions: [],
			});
		},
		async toggleChange(val) {
			if (this.isShopReward) {
				let whereClause = {
					id: this.localWayToEarnId
				}
				let payload = {
					active: val
				}
				const queryString = `where=${encodeURIComponent(JSON.stringify(whereClause))}`;
				const response = await fetch(`${URL_DOMAIN}/loyalty-campaigns/${this.campaignId}/loyalty-redemption-shop-items?${queryString}`, {
					method: 'PATCH',
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					},
					body: JSON.stringify(payload),
				});

				const result = await response.json();
				if (response.ok) {
					if (val)
						customerIOTrackEvent('Reward Is Live');
					else
						customerIOTrackEvent('Reward Is Hidden');

					this.status.type = 'success';
					this.status.message = val ? 'If your campaign is live, Reward is now visible to your shoppers.' : 'Reward is now hidden from your shoppers.';
				}
				else {
					this.status.type = 'fail';
					this.status.message = 'Failed to update. Please try again.';
				}
			}
			else {
				let whereClause = {
					id: this.localWayToEarnId
				}
				let payload = {
					active: val
				}
				const queryString = `where=${encodeURIComponent(JSON.stringify(whereClause))}`;
				const response = await fetch(`${URL_DOMAIN}/loyalty-campaigns/${this.campaignId}/loyalty-earns?${queryString}`, {
					method: 'PATCH',
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					},
					body: JSON.stringify(payload),
				});

				const result = await response.json();
				if (response.ok) {
					if (val)
						customerIOTrackEvent('WTE Is Live');
					else
						customerIOTrackEvent('WTE Is Hidden');

					this.status.type = 'success';
					this.status.message = val ? 'If your campaign is live, this is now visible to your shoppers.' : 'Way to Earn is now hidden from your shoppers.';
				}
				else {
					this.status.type = 'fail';
					this.status.message = 'Failed to update. Please try again.';
				}
			}
		},
		async enable() {
			if (this.isShopReward) {
				let success = await this.saveShopReward();
				if (success) {
					//history.back();
					this.status.type = 'success';
					this.status.message = 'Reward saved successfully.'
				}
				else {
					this.status.type = 'fail';
					this.status.message = 'Unable to enable. Please check that all information is provided.'
				}
			} else {
				let success = await this.saveWayToEarn();
				if (success) {
					//history.back();
					this.status.type = 'success';
					this.status.message = 'Way to earn saved successfully.'
				}
				else {
					this.status.type = 'fail';
					this.status.message = 'Unable to enable. Please check that all information is provided.'
				}
			}

		},
		async saveShopReward() {
			let payload = {
				shopitem: {
					name: this.earn.shopItem.name,
					description: this.earn.shopItem.description,
					imageURL: this.earn.shopItem.imageURL,
				},
				shopItemConfig: this.earn.shopItemConfig,
				rewards: this.earn.rewards,
				campaignId: this.earn.campaign.id,
				currencyId: this.earn.campaign.loyaltyProgram.loyaltyCurrencies[0].id,
				programId: this.earn.campaign.loyaltyProgramId
			}
			if (this.localWayToEarnId && this.localWayToEarnId > 0) {
				payload.shopitem.id = this.localWayToEarnId;
			}
			const response = await fetch(`${URL_DOMAIN}/shopitem/enable`, {
				method: 'POST',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify(payload),
			});

			const result = await response.json();

			this.localWayToEarnId = result.shopItemId;

			return (result.status == 'success');
		},
		async predictShopItemText() {
			if (this.shopItemPredictionRunning) return;
			this.earn.shopItemPredictionRunning = true;
			let payload = {
				shopItem: {
					name: this.earn.shopItem.name,
					description: this.earn.shopItem.description,
					imageURL: this.earn.shopItem.imageURL,
				},
				shopItemConfig: this.earn.shopItemConfig,
				rewards: this.earn.rewards,
			}

			console.log("Predicting shop item text", payload);

			var result = null;
			try {
				const response = await fetch(`${URL_DOMAIN}/shopitem/predict`, {
					method: 'POST',
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					},
					body: JSON.stringify(payload),
				});

				result = await response.json();
			} catch (error) {
				console.error("Couldn't predict: ", error);
				result = null;
				this.earn.predictionComplete = true; //so we show inputs even if our prediction failed
			}

			console.log("Prediction completed");
			this.earn.shopItem = result.shopItem;
			this.earn.shopItemPredictionRunning = false;
		},

		async generateWTESummary() {
			let payload = {
				earn: {
					name: this.earn?.name,
					description: this.earn?.description,
					imageURL: this.earn?.imageURL,
					predictionComplete: false,
				},
				condition: [this.earn?.condition],
				programId: this.earn?.campaign?.loyaltyProgramId,
				campaignId: this.earn?.campaign?.id,
				rewards: this.earn?.rewards,
			}
			try {
				const response = await fetch(`${URL_DOMAIN}/wte/action-summary`, {
					method: 'POST',
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					},
					body: JSON.stringify(payload),
				});

				let result = await response.json();
				if (response.ok && result) {
					this.wteSummaryData = result;
				}
			} catch (error) {
				console.error("Couldn't generate summary: ", error);
				result = null;
			}
		},

		async loadDefaultsWTE() {
			let payload = {
				earn: {
					name: this.earn.name,
					description: this.earn.description,
					imageURL: this.earn.imageURL,
					predictionComplete: false,
				},
				condition: [this.earn.condition],
				rewards: this.earn.rewards,
				campaignId: this.earn.campaign.id,
				currencyId: this.earn.campaign.loyaltyProgram.loyaltyCurrencies[0].id,
				programId: this.earn.campaign.loyaltyProgramId,
				name: this.earn.name,
				description: this.earn.description,
			}
			if (this.localWayToEarnId && this.localWayToEarnId > 0) {
				payload.earn.id = this.localWayToEarnId;
			}

			var result = null;
			try {
				const response = await fetch(`${URL_DOMAIN}/wte/predict`, {
					method: 'POST',
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					},
					body: JSON.stringify(payload),
				});

				result = await response.json();
			} catch (error) {
				console.error("Couldn't predict: ", error);
				result = null;
				this.earn.predictionComplete = true; //so we show inputs even if our prediction failed
			}

			console.log("Prediction completed");
			this.earn.predictionComplete = true;
			this.earn.name = this.earn.name || result.name;
			this.earn.description = this.earn.description || result.description;
			this.earn.imageURL = this.earn.imageURL || result.imageURL;

			for (let i = 0; i < result.rewards?.length; i++) {
				this.earn.rewards[i].name = this.earn.rewards[i].name || result.rewards[i].name;
				this.earn.rewards[i].description = this.earn.rewards[i].description || result.rewards[i].description;
				this.earn.rewards[i].imageURL = this.earn.rewards[i].imageURL || result.rewards[i].imageURL;
			}
		},
		async saveWayToEarn() {
			let payload = {
				earn: {
					name: this.earn.name,
					description: this.earn.description,
					imageURL: this.earn.imageURL,
				},
				condition: [this.earn.condition],
				rewards: this.earn.rewards,
				campaignId: this.earn.campaign.id,
				currencyId: this.earn.campaign.loyaltyProgram.loyaltyCurrencies[0].id,
				programId: this.earn.campaign.loyaltyProgramId
			}
			if (this.localWayToEarnId && this.localWayToEarnId > 0) {
				payload.earn.id = this.localWayToEarnId;
			}
			const response = await fetch(`${URL_DOMAIN}/wte/enable`, {
				method: 'POST',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify(payload),
			});

			const result = await response.json();
			this.localWayToEarnId = result.wayToEarnId;
			return (result.status == 'success');
		},
	}
}
</script>
<style scoped>
.super-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 10000;

	background: linear-gradient(144deg, #FAF8F5 20.89%, #E2E8F8 53.59%, #BCCAFD 88.28%);
}

h1 {
	text-transform: uppercase;
	font-size: 3em;
	font-weight: 500;
	color: grey;
}

.big-button {
	font-size: 3em;
	border-radius: 1em;
	padding: 0 0.5em;
	text-transform: uppercase;
}

.big-button>svg {
	margin-right: 0.25em;
}

.test {
	width: 50%;
	padding: 0em 0.4em;
}

@media all and (max-width: 768px) {
	.test {
		width: 100%;
	}
}

.continue-button {
	box-sizing: border-box;

	/* Auto layout */
	display: flex;
	flex-direction: row;
	justify-content: flex-end;
	align-items: center;
	padding: 2px 22px;
	gap: 10px;

	height: 69px;
	right: 16px;
	top: calc(50% - 69px/2 - 395.5px);

	border: 2px solid #FFFFFF;
	border-radius: 110px;
}

.continue-button>span {
	/* DONE & CONTINUE */

	height: 65px;

	font-family: 'Inter';
	font-style: normal;
	font-weight: 500;
	font-size: 52px;
	line-height: 65px;
	/* identical to box height, or 125% */
	text-align: right;
	letter-spacing: -0.04em;

	color: rgba(255, 255, 255, 0.75);
}

.continue-button> :before {
	/* Inside auto layout */
	flex: none;
	order: 0;
	flex-grow: 0;
}
</style>

