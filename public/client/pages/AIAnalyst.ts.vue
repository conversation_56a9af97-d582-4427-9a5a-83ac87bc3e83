<template>
	<div class="flex flex-col p-6 bg-white shadow-sm sm:flex-row items-center justify-between">

		<div>
			<div class="text-3xl sm:text-3xl font-sans font-medium opacity-70 text-ralblack-primary">
				Marketing Analyst
			</div>
			<p class="mt-1 text-gray-500">AI powered analysis of customer behavior.</p>
		</div>
	</div>

	<div class="min-h-screen bg-[#F5F5F5] p-6">

    <div class="max-w-7xl mx-auto">

      <!-- Cards -->
      <div class="space-y-16">
        <section v-for="(section, idx) in sections" :key="section.id" class="grid grid-cols-12 gap-8">
          <!-- Left Column -->
          <div class="col-span-5 space-y-8">
            <div>
              <h2 class="text-2xl font-bold text-gray-900 mb-2">{{ section.title }}</h2>
              <p class="text-gray-600 text-lg">{{ section.subtitle }}</p>
              <div class="flex items-center gap-4 mt-3 text-sm text-gray-500">

				<!--
                <div class="flex items-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 24 24">
                    <path fill="currentColor" d="M19 4h-1V2h-2v2H8V2H6v2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2zm0 16H5V10h14v10zM5 8V6h14v2H5z" />
                  </svg>
                  {{ section.dueDate }}
                </div>
                <div class="flex items-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 24 24">
                    <path fill="currentColor" d="M12 4a4 4 0 0 1 4 4a4 4 0 0 1-4 4a4 4 0 0 1-4-4a4 4 0 0 1 4-4m0 10c4.42 0 8 1.79 8 4v2H4v-2c0-2.21 3.58-4 8-4z" />
                  </svg>
                  {{ section.owner }}
                </div>
				-->
              </div>
            </div>

            <!-- Insights Cards -->
            <div class="space-y-6">
              <div v-for="(insight, insightIdx) in section.insights" :key="insightIdx"
                class="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow border-l-4"
                :class="{
                  'border-red-500': insight.impact === 'high',
                  'border-orange-400': insight.impact === 'medium',
                  'border-blue-400': insight.impact === 'low'
                }"
              >
                <div class="p-6 space-y-6">
                  <div class="space-y-4">
                    <div class="flex items-center gap-3">
                      <span class="px-3 py-1 rounded-full text-xs font-medium"
                        :class="{
                          'bg-red-500 text-white': insight.impact === 'high',
                          'bg-orange-400 text-white': insight.impact === 'medium',
                          'bg-blue-400 text-white': insight.impact === 'low'
                        }"
                      >
                        {{ insight.impact.toUpperCase() }} IMPACT
                      </span>
                    </div>

                    <h3 class="text-lg font-semibold text-gray-900">{{ insight.insight }}</h3>

                    <div class="space-y-3 text-gray-600">
                      <div>
                        <div class="text-sm font-bold text-gray-700 mb-1">Analysis</div>
                        <p>{{ insight.rootCause }}</p>
                      </div>
                      <div>
                        <div class="text-sm font-bold text-gray-700 mb-1">Opportunity</div>
                        <p>{{ insight.opportunity }}</p>
                      </div>
                    </div>

					<!-- Recommendations
                    <div class="space-y-3">
                      <div class="text-sm font-medium text-gray-700">Recommended Actions</div>
                      <div v-for="(action, actionIdx) in insight.actions" :key="actionIdx"
                        class="rounded-lg p-4 cursor-pointer transition-colors"
                        :class="{
                          'bg-red-50': insight.impact === 'high',
                          'bg-orange-50': insight.impact === 'medium',
                          'bg-blue-50': insight.impact === 'low'
                        }"
                        @click="toggleExpandedAction(`${insightIdx}-${actionIdx}`)"
                      >
                        <div class="flex items-center justify-between">
                          <div class="flex items-center gap-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple-600" viewBox="0 0 24 24">
                              <path fill="currentColor" d="M8 5v14l11-7z" />
                            </svg>
                            <span class="text-gray-900 font-medium">{{ action.title }}</span>
                          </div>
                          <svg xmlns="http://www.w3.org/2000/svg" :class="`h-5 w-5 text-gray-400 transition-transform ${
                            expandedAction === `${insightIdx}-${actionIdx}` ? 'rotate-90' : ''
                          }`" viewBox="0 0 24 24">
                            <path fill="currentColor" d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z" />
                          </svg>
                        </div>

                        <div v-if="expandedAction === `${insightIdx}-${actionIdx}`" class="mt-4 space-y-3 pl-8">
                          <div v-for="(step, stepIdx) in action.steps" :key="stepIdx" class="flex items-center gap-3 text-gray-600">
                            <div class="h-2 w-2 rounded-full bg-purple-400"></div>
                            {{ step }}
                          </div>
                          <div class="flex items-center gap-4 mt-4 pt-3 border-t border-gray-200 text-sm text-gray-500">
                            <div class="flex items-center gap-2">
                              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 24 24">
                                <path fill="currentColor" d="M19 4h-1V2h-2v2H8V2H6v2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2zm0 16H5V10h14v10zM5 8V6h14v2H5z" />
                              </svg>
                              {{ action.dueDate }}
                            </div>
                            <div class="flex items-center gap-2">
                              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 24 24">
                                <path fill="currentColor" d="M12 4a4 4 0 0 1 4 4a4 4 0 0 1-4 4a4 4 0 0 1-4-4a4 4 0 0 1 4-4m0 10c4.42 0 8 1.79 8 4v2H4v-2c0-2.21 3.58-4 8-4z" />
                              </svg>
                              {{ action.assignee }}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
					-->
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Right Column (Charts) -->
          <div class="col-span-7">
            <div class="space-y-6 sticky top-8">
              <div class="bg-white rounded-lg shadow-sm p-8">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">{{ section.charts.primary.title }}</h3>
					<div class="my-4 overflow-x-hidden bg-white rounded-2xl">

						<img src="../images/purchase_count_ex.png" v-if="section.charts.primary.title == 'Purchase Count Burndown'">

					</div>
              </div>

              <div class="bg-white rounded-lg shadow-sm p-8">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">{{ section.charts.secondary.title }}</h3>
                <img src="../images/conversion_ex.png" v-if="section.charts.secondary.title == 'Conversion Rate by Category'">
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>

  	</div>
  </template>

  <script>
  import { ref, computed } from 'vue';
  import * as Utils from '../../client-old/utils/Utils';
  import PrimaryButton from '../components/PrimaryButton.ts.vue';
  import ToggleItem from '../components/ToggleItem.ts.vue';
  import BarChart from '../components/charts/BarChart.ts.vue';
import LineChart from '../components/charts/LineChart.ts.vue';
import StackedBarChart from '../components/charts/StackedBarChart.ts.vue';
import { Crisp } from 'crisp-sdk-web';
  const URL_DOMAIN = Utils.URL_DOMAIN;

  export default {
  name: 'SignalLibrary',
  components: {
    PrimaryButton,
    ToggleItem,
	BarChart,
	LineChart,
	StackedBarChart
  },

  data() {
    return {
      sections: [
        {
          id: "new-customer-retention",
          title: "Email Marketing",
          subtitle: "Top opportunities center on re-engaging one-time buyers, enhancing the welcome series with best-selling products, and implementing category-specific targeting to capitalize on varying conversion rates across product lines.",
          dueDate: "Next 7 days",
          owner: "Marketing Team",
          priority: "Urgent",
          charts: {
            primary: {
              title: "Purchase Count Burndown",
              type: "line",
              data: [
                { month: 'Aug', rate: 32 },
                { month: 'Sep', rate: 28 },
                { month: 'Oct', rate: 25 },
                { month: 'Nov', rate: 22 },
                { month: 'Dec', rate: 18 }
              ]
            },
            secondary: {
              title: "Conversion Rate by Category",
              type: "comparison",
              metrics: [
                { label: "Email", value: "42%", change: "+8%" },
                { label: "SMS", value: "38%", change: "+5%" },
                { label: "Push", value: "21%", change: "-2%" }
              ]
            }
          },
          insights: [
            {
              impact: 'high',
              insight: "Enhanced Post-Purchase Cross-Sell",
              rootCause: " High drop-off after first purchase indicates limited re-engagement. Customers aren’t being guided to try new products quickly enough.",
              opportunity: "Deploy a post-purchase flow (triggered ~2 weeks after delivery) suggesting second-purchase favorites—e.g., highlight sweatshirts or shorts with styling tips. A well-timed email can capture the momentum while a customer is still engaged.",
              actions: [
                {
                  title: "Create Email Sequence",
                  steps: [
                    "Design 5-part welcome series",
                    "Set up tracking pixels",
                    "Configure automation rules"
                  ],
                  dueDate: "Next 3 days",
                  assignee: "Email Team"
                },
                {
                  title: "Set Up A/B Test",
                  steps: [
                    "Define test variables",
                    "Set up control group",
                    "Configure analytics"
                  ],
                  dueDate: "Next week",
                  assignee: "Analytics Team"
                }
              ]
            },
            {
              impact: 'medium',
              insight: "Category-Focused Segmentation",
              rootCause: "Conversion rates vary slightly by category (Accessories at 6.13% vs. Outerwear at 5.94%). Some customers respond more strongly to certain categories.",
              opportunity: "Segment subscribers by their browsing/purchase history (e.g., those who browsed outerwear but didn’t buy) and send tailored emails promoting best sellers in that category, potentially increasing conversion efficiency.",
              actions: [
                {
                  title: "Create Content Calendar",
                  steps: [
                    "Map product features",
                    "Draft email templates",
                    "Schedule content"
                  ],
                  dueDate: "Next 5 days",
                  assignee: "Content Team"
                }
              ]
            }
          ]
        },
        {
          id: "subscription-behavior",
          title: "Promotion Bundling",
          subtitle: "Strategic pricing optimization through targeted discounting and product bundling can enhance margins and basket size, replacing broad discounts with data-driven product pairings based on first-purchase patterns.",
          dueDate: "Next 14 days",
          owner: "Product Team",
          priority: "High",
          charts: {
            primary: {
              title: "Top First Purchase Baskets",
              type: "line",
              data: [
                { purchase: '1st', rate: 8 },
                { purchase: '2nd', rate: 15 },
                { purchase: '3rd', rate: 32 },
                { purchase: '4th', rate: 38 },
                { purchase: '5th', rate: 40 }
              ]
            },
            secondary: {
              title: "",
              type: "comparison",
              metrics: [
                { label: "Peak Conversion", value: "40%", change: "+5%" },
                { label: "Time to Convert", value: "45 days", change: "-5 days" },
                { label: "Retention Rate", value: "85%", change: "+3%" }
              ]
            }
          },
          insights: [
            {
              impact: 'high',
              insight: "Bundling & Complete the Look Offers",
              rootCause: " Popular first-purchase items (Hats, Blouses) can be paired with second-purchase “repeat driver” items (Sweatshirts, Shorts) but aren’t currently marketed together.",
              opportunity: "Bundle complementary items or run “complete the look” promotions. For instance, pair a Hat with a Scarf or a Blouse with a Sweater to increase AOV and repeat purchase rates.",
              actions: [
                {
                  title: "Design Bundle Strategy",
                  steps: [
                    "Analyze top product combinations",
                    "Set bundle pricing",
                    "Create promotional assets"
                  ],
                  dueDate: "Next week",
                  assignee: "Product Marketing"
                }
              ]
            }
          ]
        }
      ],
      expandedAction: null,
		purchaseCountData: {
			series: [
				{
				name: 'Purchase Count', // Name of the series
				data: [500000, 300760, 176200, 101930, 50800, 25580, 10460] // Data points
				}
			],
			categories: ['1', '2', '3', '4', '5', '6', '7'] // X-axis labels
		},
    };
  },

  computed: {

  },

  methods: {
    getImpactStyles(impact) {
      const styles = {
        high: {
          badge: 'bg-gradient-to-r from-red-500 to-pink-500 text-white',
          border: 'border-l-4 border-red-500',
          bg: 'bg-red-50'
        },
        medium: {
          badge: 'bg-gradient-to-r from-orange-400 to-pink-400 text-white',
          border: 'border-l-4 border-orange-400',
          bg: 'bg-orange-50'
        },
        low: {
          badge: 'bg-gradient-to-r from-blue-400 to-indigo-400 text-white',
          border: 'border-l-4 border-blue-400',
          bg: 'bg-blue-50'
        }
      };
      return styles[impact] || styles.medium;
    },
    toggleExpandedAction(actionId) {
      this.expandedAction = this.expandedAction === actionId ? null : actionId;
    }
  },
  async mounted() {

  },
}

</script>
