<template>
	<div class="m-3 sm:m-10 sm:m-7">
		<div class="text-neutral-800 text-opacity-70 sm:text-4xl md:text-6xl lg:text-7xl font-normal font-['Open Sans']">Analytics</div>
		<div class="my-3">
			<LearnMoreText text="Read about making data-driven loyalty decisions" url="https://docs.raleon.io/docs/program-return-on-investment" />
			<span class="text-slate-800 text-sm font-medium font-['Inter']"> in our Loyalty Academy.</span>
		</div>


		<div class="my-12 flex items-center justify-start flex-wrap overflow-x-hidden">
			<router-link
				v-if="!isStrategist"
				class="
					w-[100%] sm:w-[469px] sm:min-w-[469px] h-96 bg-white rounded-2xl shadow
					border border-violet-200 px-10 py-7 cursor-pointer m-[10px] hover:m-[7px] hover:border-4 hover:border-violet-800 box-content
					mx-0 sm:mx-[10px] overflow-hidden"
				:to="`analytics/loyalty-performance`"
			>
				<div class="text-slate-800 text-3xl font-bold font-['Open Sans']">Loyalty Performance</div>
				<div class="text-black text-base font-semibold font-['Open Sans'] leading-normal tracking-wide mt-2">Optimize your program with Raleon's analytics to maximize growth.</div>
				<div class="mt-5 relative h-[100%] w-[100%] overflow-hidden">
					<div class="w-[100%] h-px border border-dashed border-slate-200 relative"></div>
					<div class="w-[100%] h-px border border-dashed border-slate-200 relative top-[3.5em]"></div>
					<div class="w-[100%] h-px border border-dashed border-slate-200 relative top-[7em]"></div>
					<div class="w-[100%] h-px border border-dashed border-slate-200 relative top-[10.5em]"></div>
					<div class="w-[100%] h-px border border-dashed border-slate-200 relative top-[14em]"></div>
					<div class="w-[230px] h-px origin-top-left rotate-90 border border-slate-200 relative top-[-6px]"></div>
					<div class="w-[230px] h-px origin-top-left rotate-90 border border-slate-200 relative left-[6em] top-[-7px]"></div>
					<div class="w-[230px] h-px origin-top-left rotate-90 border border-slate-200 relative left-[12em] top-[-8px]"></div>
					<div class="w-[230px] h-px origin-top-left rotate-90 border border-slate-200 relative left-[18em] top-[-9px]"></div>
					<div class="w-[230px] h-px origin-top-left rotate-90 border border-slate-200 relative left-[24em] top-[-10px]"></div>
					<div class="w-[230px] h-px origin-top-left rotate-90 border border-slate-200 relative left-[470px] top-[-11px]"></div>
					<svg class="absolute" width="470" height="212" viewBox="0 0 470 212" fill="none" xmlns="http://www.w3.org/2000/svg">
						<g style="mix-blend-mode:multiply" opacity="0.33">
						<path d="M351.983 156.556L387.908 51.7099L469.061 105.732V212H0.748291V0.88623L80.0052 183.869L154.708 156.446L229.066 187.832L311.088 105.732L351.983 156.556Z" fill="url(#paint0_linear_895_15022)"/>
						</g>
						<defs>
						<linearGradient id="paint0_linear_895_15022" x1="234.905" y1="94.4749" x2="234.905" y2="212" gradientUnits="userSpaceOnUse">
						<stop stop-color="#D3DDFF"/>
						<stop offset="0.46679" stop-color="#C6D2FD"/>
						<stop offset="1" stop-color="#E5EAFC" stop-opacity="0.31"/>
						</linearGradient>
						</defs>
					</svg>

					<svg class="absolute" width="468" height="191" viewBox="0 0 468 191" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path d="M466.441 106.402L384.968 52.927L349.363 157.67L307.196 106.402L225.635 189.292L151.365 157.67L77.4422 184.859L1.53066 2.46191" stroke="url(#paint0_linear_895_15023)" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
						<defs>
						<linearGradient id="paint0_linear_895_15023" x1="233.986" y1="2.46191" x2="233.986" y2="189.292" gradientUnits="userSpaceOnUse">
						<stop stop-color="#9254F6"/>
						<stop offset="1" stop-color="#6D3AFF"/>
						</linearGradient>
						</defs>
					</svg>

				</div>
			</router-link>
			<router-link
				class="
					w-[100%] sm:w-[469px] sm:min-w-[469px] h-96 bg-white rounded-2xl shadow
					border border-violet-200 px-10 py-7 cursor-pointer m-[10px] hover:m-[7px] hover:border-4 hover:border-violet-800 box-content
					mx-0 sm:mx-[10px] overflow-hidden"
				:to="isStrategist ? `/ai-strategist/analytics/member-insights` : `analytics/member-insights`"
			>
				<div class="text-slate-800 text-3xl font-bold font-['Open Sans']">Customer Insights</div>
				<div class="text-black text-base font-semibold font-['Open Sans'] leading-normal tracking-wide mt-2">View how Loyalty impacts Repeat Purchases, AOV, and CLTV.</div>
				<div class="mt-5 relative h-[100%] w-[100%] overflow-hidden">
					<div class="w-[100%] h-px border border-dashed border-slate-200 relative"></div>
					<div class="w-[100%] h-px border border-dashed border-slate-200 relative top-[3.5em]"></div>
					<div class="w-[100%] h-px border border-dashed border-slate-200 relative top-[7em]"></div>
					<div class="w-[100%] h-px border border-dashed border-slate-200 relative top-[10.5em]"></div>
					<div class="w-[100%] h-px border border-dashed border-slate-200 relative top-[14em]"></div>
					<div class="w-[230px] h-px origin-top-left rotate-90 border border-slate-200 relative top-[-6px]"></div>
					<div class="w-[230px] h-px origin-top-left rotate-90 border border-slate-200 relative left-[6em] top-[-7px]"></div>
					<div class="w-[230px] h-px origin-top-left rotate-90 border border-slate-200 relative left-[12em] top-[-8px]"></div>
					<div class="w-[230px] h-px origin-top-left rotate-90 border border-slate-200 relative left-[18em] top-[-9px]"></div>
					<div class="w-[230px] h-px origin-top-left rotate-90 border border-slate-200 relative left-[24em] top-[-10px]"></div>
					<div class="w-[230px] h-px origin-top-left rotate-90 border border-slate-200 relative left-[470px] top-[-11px]"></div>
					<svg class="absolute" width="470" height="212" viewBox="0 0 470 212" fill="none" xmlns="http://www.w3.org/2000/svg">
						<g style="mix-blend-mode:multiply" opacity="0.33">
						<path d="M351.983 156.556L387.908 51.7099L469.061 105.732V212H0.748291V0.88623L80.0052 183.869L154.708 156.446L229.066 187.832L311.088 105.732L351.983 156.556Z" fill="url(#paint0_linear_895_15022)"/>
						</g>
						<defs>
						<linearGradient id="paint0_linear_895_15022" x1="234.905" y1="94.4749" x2="234.905" y2="212" gradientUnits="userSpaceOnUse">
						<stop stop-color="#D3DDFF"/>
						<stop offset="0.46679" stop-color="#C6D2FD"/>
						<stop offset="1" stop-color="#E5EAFC" stop-opacity="0.31"/>
						</linearGradient>
						</defs>
					</svg>

					<svg class="absolute" width="468" height="191" viewBox="0 0 468 191" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path d="M466.441 106.402L384.968 52.927L349.363 157.67L307.196 106.402L225.635 189.292L151.365 157.67L77.4422 184.859L1.53066 2.46191" stroke="url(#paint0_linear_895_15023)" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
						<defs>
						<linearGradient id="paint0_linear_895_15023" x1="233.986" y1="2.46191" x2="233.986" y2="189.292" gradientUnits="userSpaceOnUse">
						<stop stop-color="#9254F6"/>
						<stop offset="1" stop-color="#6D3AFF"/>
						</linearGradient>
						</defs>
					</svg>

				</div>
			</router-link>

			<router-link
				v-if="isStrategist"
				class="
					w-[100%] sm:w-[469px] sm:min-w-[469px] h-96 bg-white rounded-2xl shadow
					border border-violet-200 px-10 py-7 cursor-pointer m-[10px] hover:m-[7px] hover:border-4 hover:border-violet-800 box-content
					mx-0 sm:mx-[10px] overflow-hidden"
				:to="`/ai-strategist/analytics/retention-insights`"
			>
				<div class="text-slate-800 text-3xl font-bold font-['Open Sans']">Behavior Insights</div>
				<div class="text-black text-base font-semibold font-['Open Sans'] leading-normal tracking-wide mt-2">Analyze customer retention patterns and strategies.</div>
				<div class="mt-5 relative h-[100%] w-[100%] overflow-hidden">
					<div class="w-[100%] h-px border border-dashed border-slate-200 relative"></div>
					<div class="w-[100%] h-px border border-dashed border-slate-200 relative top-[3.5em]"></div>
					<div class="w-[100%] h-px border border-dashed border-slate-200 relative top-[7em]"></div>
					<div class="w-[100%] h-px border border-dashed border-slate-200 relative top-[10.5em]"></div>
					<div class="w-[100%] h-px border border-dashed border-slate-200 relative top-[14em]"></div>
					<div class="w-[230px] h-px origin-top-left rotate-90 border border-slate-200 relative top-[-6px]"></div>
					<div class="w-[230px] h-px origin-top-left rotate-90 border border-slate-200 relative left-[6em] top-[-7px]"></div>
					<div class="w-[230px] h-px origin-top-left rotate-90 border border-slate-200 relative left-[12em] top-[-8px]"></div>
					<div class="w-[230px] h-px origin-top-left rotate-90 border border-slate-200 relative left-[18em] top-[-9px]"></div>
					<div class="w-[230px] h-px origin-top-left rotate-90 border border-slate-200 relative left-[24em] top-[-10px]"></div>
					<div class="w-[230px] h-px origin-top-left rotate-90 border border-slate-200 relative left-[470px] top-[-11px]"></div>
					<svg class="absolute" width="470" height="212" viewBox="0 0 470 212" fill="none" xmlns="http://www.w3.org/2000/svg">
						<g style="mix-blend-mode:multiply" opacity="0.33">
						<path d="M351.983 156.556L387.908 51.7099L469.061 105.732V212H0.748291V0.88623L80.0052 183.869L154.708 156.446L229.066 187.832L311.088 105.732L351.983 156.556Z" fill="url(#paint0_linear_895_15022)"/>
						</g>
						<defs>
						<linearGradient id="paint0_linear_895_15022" x1="234.905" y1="94.4749" x2="234.905" y2="212" gradientUnits="userSpaceOnUse">
						<stop stop-color="#D3DDFF"/>
						<stop offset="0.46679" stop-color="#C6D2FD"/>
						<stop offset="1" stop-color="#E5EAFC" stop-opacity="0.31"/>
						</linearGradient>
						</defs>
					</svg>

					<svg class="absolute" width="468" height="191" viewBox="0 0 468 191" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path d="M466.441 106.402L384.968 52.927L349.363 157.67L307.196 106.402L225.635 189.292L151.365 157.67L77.4422 184.859L1.53066 2.46191" stroke="url(#paint0_linear_895_15023)" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
						<defs>
						<linearGradient id="paint0_linear_895_15023" x1="233.986" y1="2.46191" x2="233.986" y2="189.292" gradientUnits="userSpaceOnUse">
						<stop stop-color="#9254F6"/>
						<stop offset="1" stop-color="#6D3AFF"/>
						</linearGradient>
						</defs>
					</svg>
				</div>
			</router-link>

			<router-link
				class="
					w-[100%] sm:w-[469px] sm:min-w-[469px] h-96 bg-white rounded-2xl shadow
					border border-violet-200 px-10 py-7 cursor-pointer m-[10px] hover:m-[7px] hover:border-4 hover:border-violet-800 box-content
					mx-0 sm:mx-[10px] overflow-hidden"
				:to="`analytics/vip-insights`"
				v-if="vipLive && !isStrategist"
			>
				<div class="text-slate-800 text-3xl font-bold font-['Open Sans']">VIP Tier Insights</div>
				<div class="text-black text-base font-semibold font-['Open Sans'] leading-normal tracking-wide mt-2">View how VIP Tiers impact AOV and CLTV.</div>
				<div class="mt-5 relative h-[100%] w-[100%] overflow-hidden">
					<div class="w-[100%] h-px border border-dashed border-slate-200 relative"></div>
					<div class="w-[100%] h-px border border-dashed border-slate-200 relative top-[3.5em]"></div>
					<div class="w-[100%] h-px border border-dashed border-slate-200 relative top-[7em]"></div>
					<div class="w-[100%] h-px border border-dashed border-slate-200 relative top-[10.5em]"></div>
					<div class="w-[100%] h-px border border-dashed border-slate-200 relative top-[14em]"></div>
					<div class="w-[230px] h-px origin-top-left rotate-90 border border-slate-200 relative top-[-6px]"></div>
					<div class="w-[230px] h-px origin-top-left rotate-90 border border-slate-200 relative left-[6em] top-[-7px]"></div>
					<div class="w-[230px] h-px origin-top-left rotate-90 border border-slate-200 relative left-[12em] top-[-8px]"></div>
					<div class="w-[230px] h-px origin-top-left rotate-90 border border-slate-200 relative left-[18em] top-[-9px]"></div>
					<div class="w-[230px] h-px origin-top-left rotate-90 border border-slate-200 relative left-[24em] top-[-10px]"></div>
					<div class="w-[230px] h-px origin-top-left rotate-90 border border-slate-200 relative left-[470px] top-[-11px]"></div>
					<svg class="absolute" width="470" height="212" viewBox="0 0 470 212" fill="none" xmlns="http://www.w3.org/2000/svg">
						<g style="mix-blend-mode:multiply" opacity="0.33">
						<path d="M351.983 156.556L387.908 51.7099L469.061 105.732V212H0.748291V0.88623L80.0052 183.869L154.708 156.446L229.066 187.832L311.088 105.732L351.983 156.556Z" fill="url(#paint0_linear_895_15022)"/>
						</g>
						<defs>
						<linearGradient id="paint0_linear_895_15022" x1="234.905" y1="94.4749" x2="234.905" y2="212" gradientUnits="userSpaceOnUse">
						<stop stop-color="#D3DDFF"/>
						<stop offset="0.46679" stop-color="#C6D2FD"/>
						<stop offset="1" stop-color="#E5EAFC" stop-opacity="0.31"/>
						</linearGradient>
						</defs>
					</svg>

					<svg class="absolute" width="468" height="191" viewBox="0 0 468 191" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path d="M466.441 106.402L384.968 52.927L349.363 157.67L307.196 106.402L225.635 189.292L151.365 157.67L77.4422 184.859L1.53066 2.46191" stroke="url(#paint0_linear_895_15023)" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
						<defs>
						<linearGradient id="paint0_linear_895_15023" x1="233.986" y1="2.46191" x2="233.986" y2="189.292" gradientUnits="userSpaceOnUse">
						<stop stop-color="#9254F6"/>
						<stop offset="1" stop-color="#6D3AFF"/>
						</linearGradient>
						</defs>
					</svg>

				</div>
			</router-link>

			<router-link
				v-if="!isStrategist"
				class="
					w-[100%] sm:w-[469px] sm:min-w-[469px] h-96 bg-white rounded-2xl shadow
					border border-violet-200 px-10 py-7 cursor-pointer m-[10px] hover:m-[7px] hover:border-4 hover:border-violet-800 box-content
					mx-0 sm:mx-[10px] overflow-hidden"
				:to="`analytics/referral-analytics`"
			>
				<div class="text-slate-800 text-3xl font-bold font-['Open Sans']">Referral Analytics</div>
				<div class="text-black text-base font-semibold font-['Open Sans'] leading-normal tracking-wide mt-2">View Referral Conversions and Revenue.</div>
				<div class="mt-5 relative h-[100%] w-[100%] overflow-hidden">
					<div class="w-[100%] h-px border border-dashed border-slate-200 relative"></div>
					<div class="w-[100%] h-px border border-dashed border-slate-200 relative top-[3.5em]"></div>
					<div class="w-[100%] h-px border border-dashed border-slate-200 relative top-[7em]"></div>
					<div class="w-[100%] h-px border border-dashed border-slate-200 relative top-[10.5em]"></div>
					<div class="w-[100%] h-px border border-dashed border-slate-200 relative top-[14em]"></div>
					<div class="w-[230px] h-px origin-top-left rotate-90 border border-slate-200 relative top-[-6px]"></div>
					<div class="w-[230px] h-px origin-top-left rotate-90 border border-slate-200 relative left-[6em] top-[-7px]"></div>
					<div class="w-[230px] h-px origin-top-left rotate-90 border border-slate-200 relative left-[12em] top-[-8px]"></div>
					<div class="w-[230px] h-px origin-top-left rotate-90 border border-slate-200 relative left-[18em] top-[-9px]"></div>
					<div class="w-[230px] h-px origin-top-left rotate-90 border border-slate-200 relative left-[24em] top-[-10px]"></div>
					<div class="w-[230px] h-px origin-top-left rotate-90 border border-slate-200 relative left-[470px] top-[-11px]"></div>
					<svg class="absolute" width="470" height="212" viewBox="0 0 470 212" fill="none" xmlns="http://www.w3.org/2000/svg">
						<g style="mix-blend-mode:multiply" opacity="0.33">
						<path d="M351.983 156.556L387.908 51.7099L469.061 105.732V212H0.748291V0.88623L80.0052 183.869L154.708 156.446L229.066 187.832L311.088 105.732L351.983 156.556Z" fill="url(#paint0_linear_895_15022)"/>
						</g>
						<defs>
						<linearGradient id="paint0_linear_895_15022" x1="234.905" y1="94.4749" x2="234.905" y2="212" gradientUnits="userSpaceOnUse">
						<stop stop-color="#D3DDFF"/>
						<stop offset="0.46679" stop-color="#C6D2FD"/>
						<stop offset="1" stop-color="#E5EAFC" stop-opacity="0.31"/>
						</linearGradient>
						</defs>
					</svg>

					<svg class="absolute" width="468" height="191" viewBox="0 0 468 191" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path d="M466.441 106.402L384.968 52.927L349.363 157.67L307.196 106.402L225.635 189.292L151.365 157.67L77.4422 184.859L1.53066 2.46191" stroke="url(#paint0_linear_895_15023)" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
						<defs>
						<linearGradient id="paint0_linear_895_15023" x1="233.986" y1="2.46191" x2="233.986" y2="189.292" gradientUnits="userSpaceOnUse">
						<stop stop-color="#9254F6"/>
						<stop offset="1" stop-color="#6D3AFF"/>
						</linearGradient>
						</defs>
					</svg>

				</div>
			</router-link>
		</div>
	</div>
</template>

<script>

	import * as Utils from '../../client-old/utils/Utils';
	import { dashboards } from './AnalyticsDashboard.ts.vue';
	import LearnMoreText from '../components/LearnMoreText.ts.vue';
	import { customerIOTrackEvent } from '../services/customerio.js';

	const URL_DOMAIN = Utils.URL_DOMAIN;

	export default {
		components: {
			LearnMoreText
		},
		props: {
			isStrategist: {
				type: Boolean,
				default: false
			}
		},
		async mounted() {
			customerIOTrackEvent('Analytics Viewed');
			await this.getVIPFeatureSetting();
		},
		data() {
			return {
				dashboards,
				vipLive: false
			}
		},
		methods: {
			async getVIPFeatureSetting() {
				const data = await fetch(`${URL_DOMAIN}/feature-setting/vip`, {
					method: 'GET',
					withCreditentials: true,
					credentials: 'omit',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Access-Control-Allow-Origin': '*',
						'Content-Type': 'application/json'
					},
				});
				const setting = await data.json();
				this.vipLive = setting.live || false;
			}
		}
	}
</script>
