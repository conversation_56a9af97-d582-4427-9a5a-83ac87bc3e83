<template>
	<div class="flex flex-col p-6 bg-white shadow-sm sm:flex-row items-center justify-between">

		<div class="text-3xl sm:text-3xl font-sans font-medium opacity-70 text-ralblack-primary">
			Intelligent Segments
		</div>

		<div class="flex w-full sm:w-auto justify-start" v-if="loading == 'false'">
			<PrimaryButton cta="Create Segment" size="xs" @click="segmentModalTest = true" />
		</div>
	</div>

	<div class="p-2 sm:p-7">

	<div class="min-h-screen" v-if="loading == 'true'">
		<!-- Main Content Area -->
		<div class="max-w-6xl">
		<div class="space-y-6">
			<!-- Progress Indicator -->
			<div class="bg-white rounded-lg p-8 shadow-sm border border-purple-100">
				<div class="max-w-md mx-auto space-y-6">
					<!-- Pulsing circle -->
					<div class="flex justify-center">
					<div class="relative">
						<!-- Background pulse circles -->
						<div class="w-16 h-16 bg-purple-700 rounded-full animate-pulse opacity-20"></div>
						<div class="absolute inset-0 w-16 h-16 bg-purple-500 rounded-full animate-ping opacity-20"></div>
						<div class="absolute inset-0 w-16 h-16 border-4 border-purple-300 rounded-full animate-pulse"></div>

						<!-- Robot emoji with fade animation -->
						<div class="absolute inset-0 flex items-center justify-center">
						<span class="text-2xl robot-fade">🤖</span>
						</div>
					</div>
					</div>

					<!-- Loading text -->
					<div class="text-center space-y-2">
						<div class="h-7 relative"> <!-- Fixed height container to prevent layout shift -->
							<transition
							name="fade-message"
							mode="out-in"
							>
							<h2
								:key="currentMessageIndex"
								class="text-lg font-medium text-gray-900 absolute w-full"
							>
								{{ currentLoadingMessage }}
							</h2>
							</transition>
						</div>
						<p class="text-sm text-gray-500">We're processing your data to create intelligent segments</p>
					</div>

					<!-- Progress bar -->
					<div class="relative h-1 bg-purple-100 rounded-full overflow-hidden">
					<div class="absolute inset-y-0 left-0 bg-purple-700 w-1/3 progress-bar"></div>
					</div>
				</div>

				<!--
				<div class="flex gap-4 mt-6">
					<div class="rounded-lg flex flex-col gap-4 shadow-lg p-4 w-1/2 border-gray-100 border-2">
						<div class="flex items-center">
							<svg xmlns="http://www.w3.org/2000/svg" height="32px" viewBox="0 -960 960 960" width="32px" fill="#5a16c9"><path d="m426-330 195-125q14-9 14-25t-14-25L426-630q-15-10-30.5-1.5T380-605v250q0 18 15.5 26.5T426-330Zm54 250q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-80q134 0 227-93t93-227q0-134-93-227t-227-93q-134 0-227 93t-93 227q0 134 93 227t227 93Zm0-320Z"/></svg>
							<div class="text-base font-semibold ml-2">Quick Start Guide</div>
						</div>
						<div class="text-sm">
							Learn how to start using AI Segmentation in less than 5 minutes.
						</div>
						<div class="">
							<a href="" class="text-purple-700 text-sm">Watch video -></a>
						</div>
					</div>

					<div class="rounded-lg flex flex-col gap-4 shadow-lg p-4 w-1/2 border-gray-100 border-2">
						<div class="flex items-center">
							<svg xmlns="http://www.w3.org/2000/svg" height="32px" viewBox="0 -960 960 960" width="32px" fill="#5a16c9"><path d="m480-455 51 31q11 7 21.5-1t7.5-21l-13-58 44-38q10-9 6.5-21T580-577l-58-5-24-55q-5-12-18-12t-18 12l-24 55-58 5q-14 2-17.5 14t6.5 21l44 38-13 58q-3 13 7.5 21t21.5 1l51-31Zm0 215-168 72q-40 17-76-6.5T200-241v-519q0-33 23.5-56.5T280-840h400q33 0 56.5 23.5T760-760v519q0 43-36 66.5t-76 6.5l-168-72Zm0-88 200 86v-518H280v518l200-86Zm0-432H280h400-200Z"/></svg>
							<div class="text-base font-semibold ml-2">AI Segment Playbooks</div>
						</div>
						<div class="text-sm">
							Take a look at our pre-built playbooks, designed to get you from strategy to tactics fast.
						</div>
						<div class="">
							<a href="" class="text-purple-700 text-sm">Read guide -></a>
						</div>
					</div>
				</div>
				-->
			</div>

			<!-- Skeleton Segments -->
			<div class="flex flex-col">
			<div
				v-for="i in 3"
				:key="i"
				class="bg-white rounded-lg p-6 border border-purple-100 shadow-sm mb-4"
				:style="{
				animation: `fadeIn 0.6s ease-out ${i * 0.2}s both`
				}"
			>
				<!-- Skeleton header -->
				<div class="space-y-4">
				<div class="h-4 bg-purple-100 rounded-full w-2/3 animate-pulse"></div>
				<div class="h-3 bg-purple-50 rounded-full w-1/2 animate-pulse"></div>
				</div>

				<!-- Skeleton stats -->
				<div class="mt-6 grid grid-cols-2 gap-4">
				<div class="space-y-2">
					<div class="h-3 bg-purple-50 rounded-full w-3/4 animate-pulse"></div>
					<div class="h-4 bg-purple-100 rounded-full w-1/2 animate-pulse"></div>
				</div>
				<div class="space-y-2">
					<div class="h-3 bg-purple-50 rounded-full w-3/4 animate-pulse"></div>
					<div class="h-4 bg-purple-100 rounded-full w-1/2 animate-pulse"></div>
				</div>
				</div>
			</div>
			</div>
		</div>
		</div>
  </div>

		<div class="border-b border-gray-300 mb-6">
			<div class="flex space-x-8">

				<div class="whitespace-nowrap py-4 px-1 font-medium text-sm focus:outline-none hover:border-b-2 hover:text-ralprimary-light hover:border-ralprimary-light transition-all duration-300 cursor-pointer"
				@click.stop="this.overview = true; recommendations = false;"
				:class="{'text-ralprimary-light border-ralprimary-light border-b-2 ': this.overview == true}"
				>
					Active
				</div>

				<div class="whitespace-nowrap flex items-center py-4 px-1 border-b-1 font-medium text-sm focus:outline-none hover:border-b-2 hover:text-ralprimary-light hover:border-ralprimary-light transition-all duration-300 cursor-pointer"
				@click.stop="this.overview = false; recommendations=true"
				:class="{'text-ralprimary-light border-ralprimary-light border-b-2 ': recommendations == true}"
				>
					Recommendations
					<div className="flex items-center justify-center w-5 h-5 ml-2 text-xs text-center font-semibold rounded-full bg-[#32316A] text-white">
					1
					</div>
				</div>

				<div class="whitespace-nowrap py-4 px-1 border-b-1 font-medium text-sm focus:outline-none hover:border-b-2 hover:text-ralprimary-light hover:border-ralprimary-light transition-all duration-300 cursor-pointer"
				@click.stop="this.overview = false; this.insights = true; this.history = false;"
				:class="{'text-ralprimary-light border-ralprimary-light border-b-2 ': this.insights == true}"
				>
					Archived <span class=""></span>
				</div>
			</div>
		</div>

		<div class="" v-if="this.overview">

			<div class="mt-6" v-if="loading == 'false'">
				<!-- Segment card -->
				<div class="relative bg-white rounded-2xl p-4 border shadow border-ralprimary-light border-opacity-50 hover:border-opacity-90 hover:shadow-lg hover:cursor-pointer mt-4 flex-grow transition-all duration-300 ease-in-out overflow-visible segment-card" :style="{ animationDelay: `${index * 100}ms`, zIndex: isPopoverOpen && activeSegment === index ? 50 : 1 }"
				v-for="(segment, index) in segments">

					<!-- header area -->
					<div class="flex justify-between items-start">
						<div class="">
							<div class="text-xl font-semibold">{{ segment.name }}</div>
							<div class="mt-2 text-sm w-2/3">
								{{ segment.description }}
							</div>
						</div>

						<div class="flex items-center">
							<span class="inline-flex items-center gap-1 px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-50 text-orange-700 border border-orange-200"
							v-if="!segment.sending">
								<span class="relative flex h-2 w-2">
								<span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-orange-800 opacity-75"></span>
								<span class="relative inline-flex rounded-full h-2 w-2 bg-orange-800"></span>
								</span>
								No sends yet
							</span>

							<div class="mr-4 flex items-center" v-if="segment.sending">
								<svg width="20" height="16" viewBox="0 0 20 16" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path d="M2 16C1.45 16 0.979167 15.8042 0.5875 15.4125C0.195833 15.0208 0 14.55 0 14V2C0 1.45 0.195833 0.979167 0.5875 0.5875C0.979167 0.195833 1.45 0 2 0H18C18.55 0 19.0208 0.195833 19.4125 0.5875C19.8042 0.979167 20 1.45 20 2V14C20 14.55 19.8042 15.0208 19.4125 15.4125C19.0208 15.8042 18.55 16 18 16H2ZM10 9L2 4V14H18V4L10 9ZM10 7L18 2H2L10 7ZM2 4V2V14V4Z" fill="#565656"/>
								</svg>

								<span class="text-sm font-bold text-gray-500 ml-2">
								{{segment.emailopenrate}}
								</span>
							</div>

							<div class="mr-4 flex items-center" v-if="segment.sending">
								<svg width="18" height="17" viewBox="0 0 18 17" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path d="M1 7H2C2.28333 7 2.52083 7.09583 2.7125 7.2875C2.90417 7.47917 3 7.71667 3 8C3 8.28333 2.90417 8.52083 2.7125 8.7125C2.52083 8.90417 2.28333 9 2 9H1C0.716667 9 0.479167 8.90417 0.2875 8.7125C0.0958333 8.52083 0 8.28333 0 8C0 7.71667 0.0958333 7.47917 0.2875 7.2875C0.479167 7.09583 0.716667 7 1 7ZM2.7 12.4L3.4 11.7C3.6 11.5 3.83333 11.4042 4.1 11.4125C4.36667 11.4208 4.6 11.5167 4.8 11.7C5 11.9 5.10417 12.1375 5.1125 12.4125C5.12083 12.6875 5.025 12.925 4.825 13.125L4.125 13.825C3.925 14.025 3.6875 14.1208 3.4125 14.1125C3.1375 14.1042 2.9 14 2.7 13.8C2.51667 13.6 2.42083 13.3667 2.4125 13.1C2.40417 12.8333 2.5 12.6 2.7 12.4ZM3.4 4.3L2.7 3.6C2.5 3.4 2.40417 3.16667 2.4125 2.9C2.42083 2.63333 2.51667 2.4 2.7 2.2C2.9 2 3.1375 1.89583 3.4125 1.8875C3.6875 1.87917 3.925 1.975 4.125 2.175L4.825 2.875C5.025 3.075 5.12083 3.3125 5.1125 3.5875C5.10417 3.8625 5 4.1 4.8 4.3C4.6 4.48333 4.36667 4.57917 4.1 4.5875C3.83333 4.59583 3.6 4.5 3.4 4.3ZM15.3 16.3L11.25 12.25L10.5 14.5C10.4667 14.6167 10.4042 14.7042 10.3125 14.7625C10.2208 14.8208 10.125 14.85 10.025 14.85C9.925 14.85 9.82917 14.8167 9.7375 14.75C9.64583 14.6833 9.58333 14.5917 9.55 14.475L7.4 7.325C7.36667 7.19167 7.37083 7.05833 7.4125 6.925C7.45417 6.79167 7.51667 6.68333 7.6 6.6C7.68333 6.51667 7.79167 6.45417 7.925 6.4125C8.05833 6.37083 8.19167 6.36667 8.325 6.4L15.525 8.55C15.6417 8.58333 15.7292 8.64583 15.7875 8.7375C15.8458 8.82917 15.875 8.925 15.875 9.025C15.875 9.125 15.85 9.22083 15.8 9.3125C15.75 9.40417 15.6667 9.46667 15.55 9.5L13.3 10.3L17.3 14.3C17.5 14.5 17.6 14.7333 17.6 15C17.6 15.2667 17.5 15.5 17.3 15.7L16.7 16.3C16.5 16.5 16.2667 16.6 16 16.6C15.7333 16.6 15.5 16.5 15.3 16.3ZM8 2V1C8 0.716667 8.09583 0.479167 8.2875 0.2875C8.47917 0.0958333 8.71667 0 9 0C9.28333 0 9.52083 0.0958333 9.7125 0.2875C9.90417 0.479167 10 0.716667 10 1V2C10 2.28333 9.90417 2.52083 9.7125 2.7125C9.52083 2.90417 9.28333 3 9 3C8.71667 3 8.47917 2.90417 8.2875 2.7125C8.09583 2.52083 8 2.28333 8 2ZM13.175 2.875L13.9 2.15C14.0833 1.96667 14.3125 1.87083 14.5875 1.8625C14.8625 1.85417 15.1 1.95 15.3 2.15C15.4833 2.33333 15.5792 2.5625 15.5875 2.8375C15.5958 3.1125 15.5083 3.35 15.325 3.55L14.6 4.3C14.4167 4.5 14.1875 4.59583 13.9125 4.5875C13.6375 4.57917 13.4 4.48333 13.2 4.3C13 4.1 12.8958 3.8625 12.8875 3.5875C12.8792 3.3125 12.975 3.075 13.175 2.875Z" fill="#5F6368"/>
								</svg>

								<span class="text-sm font-bold text-gray-500 ml-2">
								{{segment.emailctr}}
								</span>
							</div>

							<!--
							<div class="inline-flex items-center px-3 py-1 rounded-full border border-green-500 bg-green-50">
								<div class="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
								<span class="text-sm font-medium text-gray-700">Optimized</span>
							</div>
							-->
						</div>
					</div>

					<!-- stat area -->
					<div class="flex items-center justify-between mt-4">
						<div class="flex items-center gap-2">
							<div class="inline-flex items-center px-4 py-1.5 border rounded-full text-sm font-medium cursor-pointer mr-2 transition-all duration-200 hover:shadow-md bg-green-100 text-green-800 border-green-200 hover:bg-green-50">{{segment.tag}}</div>

							<div v-if="hasMoreSignals" class="relative popover-container">
								<button
									@click="togglePopover(index)"
									class="inline-flex items-center px-2 py-1 rounded-md text-sm text-gray-600 hover:bg-gray-100"
									:class="{ 'bg-gray-100': isPopoverOpen && activeSegment === index }"
								>
									<svg class="w-4 h-4 mr-1" viewBox="0 0 24 24">
									<path d="M8 12a2 2 0 1 1-4 0 2 2 0 0 1 4 0zM14 12a2 2 0 1 1-4 0 2 2 0 0 1 4 0zM20 12a2 2 0 1 1-4 0 2 2 0 0 1 4 0z" />
									</svg>
									2 more
								</button>

								<div v-show="isPopoverOpen && activeSegment === index"
									@mouseleave="handlePopoverMouseLeave"
									class="absolute right-0 mt-2 bg-white rounded-lg shadow-lg p-3 border border-gray-200 whitespace-nowrap"
									style="z-index: 1000;"
								>
									<div class="flex flex-col">
										<div class="items-center px-4 py-1.5 border rounded-full text-sm font-medium cursor-pointer mb-2 mr-2 inline-block transition-all duration-200 hover:shadow-md bg-green-100 text-green-800 border-green-200 hover:bg-green-50">One-Time Purchase</div>
										<div class="items-center px-4 py-1.5 border rounded-full text-sm font-medium cursor-pointer mb-2 mr-2 inline-block transition-all duration-200 hover:shadow-md bg-green-100 text-green-800 border-green-200 hover:bg-green-50">High Risk Customer</div>
									</div>
								</div>
							</div>
						</div>

						<div class="flex">
							<div className="flex items-center mr-6">
								<div className="text-4xl font-bold mr-3">{{segment.profileCount}}</div>
								<!--
								<div class="text-4xl font-bold mr-3" v-if="!segment.sending">
									<svg xmlns="http://www.w3.org/2000/svg" height="32px" viewBox="0 -960 960 960" width="32px" fill="#5f6368"><path d="m612-292 56-56-148-148v-184h-80v216l172 172ZM480-80q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-400Zm0 320q133 0 226.5-93.5T800-480q0-133-93.5-226.5T480-800q-133 0-226.5 93.5T160-480q0 133 93.5 226.5T480-160Z"/></svg>
								</div>
								-->

								<div className="text-xs text-gray-600">
									<span className="font-semibold"># Customers</span>
									<br />
									Today
								</div>
							</div>

							<div className="flex items-center mr-6">
								<div class="text-4xl font-bold mr-3">{{segment.avgltv}}</div>
								<!--
								<div class="text-4xl font-bold mr-3" v-if="!segment.sending">
									<svg xmlns="http://www.w3.org/2000/svg" height="32px" viewBox="0 -960 960 960" width="32px" fill="#5f6368"><path d="m612-292 56-56-148-148v-184h-80v216l172 172ZM480-80q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-400Zm0 320q133 0 226.5-93.5T800-480q0-133-93.5-226.5T480-800q-133 0-226.5 93.5T160-480q0 133 93.5 226.5T480-160Z"/></svg>
								</div>
								-->

								<div className="text-xs text-gray-600">
									<span className="font-semibold">Avg. LTV</span>
									<br />
									Last 30 days
								</div>
							</div>

							<div className="flex items-center">
								<div class="text-4xl font-bold mr-3" >{{segment.revenue}}</div>
								<!--
								<div class="text-4xl font-bold mr-3" v-if="!segment.sending">
									<svg xmlns="http://www.w3.org/2000/svg" height="32px" viewBox="0 -960 960 960" width="32px" fill="#5f6368"><path d="m612-292 56-56-148-148v-184h-80v216l172 172ZM480-80q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-400Zm0 320q133 0 226.5-93.5T800-480q0-133-93.5-226.5T480-800q-133 0-226.5 93.5T160-480q0 133 93.5 226.5T480-160Z"/></svg>
								</div>
								-->

								<div className="text-xs text-gray-600">
									<span className="font-semibold">Revenue</span>
									<br />
									Last 30 days
								</div>
							</div>

						</div>
					</div>
				</div>
				<!-- // -->
			</div>

		</div>

		<div class="" v-if="recommendations">

			<div class="bg-white rounded-2xl border shadow border-ralprimary-light border-opacity-50 hover:border-opacity-90 hover:shadow-lg mt-4 flex-grow transition-all duration-300 ease-in-out overflow-hidden">
				<div class="flex p-4 items-center justify-between">
					<div class="flex items-center">
						<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368"><path d="M40-272q0-34 17.5-62.5T104-378q62-31 126-46.5T360-440q66 0 130 15.5T616-378q29 15 46.5 43.5T680-272v32q0 33-23.5 56.5T600-160H120q-33 0-56.5-23.5T40-240v-32Zm800 112H738q11-18 16.5-38.5T760-240v-40q0-44-24.5-84.5T666-434q51 6 96 20.5t84 35.5q36 20 55 44.5t19 53.5v40q0 33-23.5 56.5T840-160ZM360-480q-66 0-113-47t-47-113q0-66 47-113t113-47q66 0 113 47t47 113q0 66-47 113t-113 47Zm400-160q0 66-47 113t-113 47q-11 0-28-2.5t-28-5.5q27-32 41.5-71t14.5-81q0-42-14.5-81T544-792q14-5 28-6.5t28-1.5q66 0 113 47t47 113ZM120-240h480v-32q0-11-5.5-20T580-306q-54-27-109-40.5T360-360q-56 0-111 13.5T140-306q-9 5-14.5 14t-5.5 20v32Zm240-320q33 0 56.5-23.5T440-640q0-33-23.5-56.5T360-720q-33 0-56.5 23.5T280-640q0 33 23.5 56.5T360-560Zm0 320Zm0-400Z"/></svg>
						<div class="ml-2">Recent Taco Seasoning Buyers</div>
					</div>
					<PrimaryButton cta="Activate" size="xs" />
				</div>

				<div class="w-full bg-gray-200 h-[1px]"></div>

				<div class="p-4">
					<div class="flex">
						<div class="w-1/2 flex flex-col">
							<div class="flex items-center mb-6">
								<svg xmlns="http://www.w3.org/2000/svg" height="18px" viewBox="0 -960 960 960" width="18px" fill="#5f6368"><path d="M40-272q0-34 17.5-62.5T104-378q62-31 126-46.5T360-440q66 0 130 15.5T616-378q29 15 46.5 43.5T680-272v32q0 33-23.5 56.5T600-160H120q-33 0-56.5-23.5T40-240v-32Zm800 112H738q11-18 16.5-38.5T760-240v-40q0-44-24.5-84.5T666-434q51 6 96 20.5t84 35.5q36 20 55 44.5t19 53.5v40q0 33-23.5 56.5T840-160ZM360-480q-66 0-113-47t-47-113q0-66 47-113t113-47q66 0 113 47t47 113q0 66-47 113t-113 47Zm400-160q0 66-47 113t-113 47q-11 0-28-2.5t-28-5.5q27-32 41.5-71t14.5-81q0-42-14.5-81T544-792q14-5 28-6.5t28-1.5q66 0 113 47t47 113ZM120-240h480v-32q0-11-5.5-20T580-306q-54-27-109-40.5T360-360q-56 0-111 13.5T140-306q-9 5-14.5 14t-5.5 20v32Zm240-320q33 0 56.5-23.5T440-640q0-33-23.5-56.5T360-720q-33 0-56.5 23.5T280-640q0 33 23.5 56.5T360-560Zm0 320Zm0-400Z"/></svg>
								<div class="ml-2">1,284 profiles</div>
							</div>

							<div class="flex items-center mb-6">
								<svg xmlns="http://www.w3.org/2000/svg" height="18px" viewBox="0 -960 960 960" width="18px" fill="#5f6368"><path d="M108-255q-12-12-11.5-28.5T108-311l211-214q23-23 57-23t57 23l103 104 208-206h-64q-17 0-28.5-11.5T640-667q0-17 11.5-28.5T680-707h160q17 0 28.5 11.5T880-667v160q0 17-11.5 28.5T840-467q-17 0-28.5-11.5T800-507v-64L593-364q-23 23-57 23t-57-23L376-467 164-255q-11 11-28 11t-28-11Z"/></svg>
								<div class="ml-2">$285 Avg. LTV</div>
							</div>

							<div class="flex items-center mb-2">
								<svg xmlns="http://www.w3.org/2000/svg" height="18px" viewBox="0 -960 960 960" width="18px" fill="#5f6368"><path d="M240-80q-33 0-56.5-23.5T160-160v-480q0-33 23.5-56.5T240-720h80q0-66 47-113t113-47q66 0 113 47t47 113h80q33 0 56.5 23.5T800-640v480q0 33-23.5 56.5T720-80H240Zm0-80h480v-480h-80v80q0 17-11.5 28.5T600-520q-17 0-28.5-11.5T560-560v-80H400v80q0 17-11.5 28.5T360-520q-17 0-28.5-11.5T320-560v-80h-80v480Zm160-560h160q0-33-23.5-56.5T480-800q-33 0-56.5 23.5T400-720ZM240-160v-480 480Z"/></svg>
								<div class="ml-2">3 Avg. # purchases per profile</div>
							</div>
						</div>

						<div className="space-y-2 w-1/2 ml-4">
							<p className="text-lg font-medium text-gray-900">Segment Signals</p>
							<ul className="space-y-1">
								<li className="flex items-center space-x-2 text-sm text-gray-600">
									<svg xmlns="http://www.w3.org/2000/svg" height="18px" viewBox="0 -960 960 960" width="18px" fill="#5f6368"><path d="M504-480 348-636q-11-11-11-28t11-28q11-11 28-11t28 11l184 184q6 6 8.5 13t2.5 15q0 8-2.5 15t-8.5 13L404-268q-11 11-28 11t-28-11q-11-11-11-28t11-28l156-156Z"/></svg>
									<span>Purchased taco seasoning in last 30 days</span>
								</li>
								<li className="flex items-center space-x-2 text-sm text-gray-600">
									<svg xmlns="http://www.w3.org/2000/svg" height="18px" viewBox="0 -960 960 960" width="18px" fill="#5f6368"><path d="M504-480 348-636q-11-11-11-28t11-28q11-11 28-11t28 11l184 184q6 6 8.5 13t2.5 15q0 8-2.5 15t-8.5 13L404-268q-11 11-28 11t-28-11q-11-11-11-28t11-28l156-156Z"/></svg>
									<span>Haven't bought other seasonings</span>
								</li>
								<li className="flex items-center space-x-2 text-sm text-gray-600">
									<svg xmlns="http://www.w3.org/2000/svg" height="18px" viewBox="0 -960 960 960" width="18px" fill="#5f6368"><path d="M504-480 348-636q-11-11-11-28t11-28q11-11 28-11t28 11l184 184q6 6 8.5 13t2.5 15q0 8-2.5 15t-8.5 13L404-268q-11 11-28 11t-28-11q-11-11-11-28t11-28l156-156Z"/></svg>
									<span>Engagement rate 15% above normal</span>
								</li>
							</ul>
						</div>
					</div>

					<div className="space-y-6 mt-6">
						<div>
							<div className="flex items-center space-x-2 mb-3">
								<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368"><path d="M260-320q47 0 91.5 10.5T440-278v-394q-41-24-87-36t-93-12q-36 0-71.5 7T120-692v396q35-12 69.5-18t70.5-6Zm260 42q44-21 88.5-31.5T700-320q36 0 70.5 6t69.5 18v-396q-33-14-68.5-21t-71.5-7q-47 0-93 12t-87 36v394Zm-40 118q-48-38-104-59t-116-21q-42 0-82.5 11T100-198q-21 11-40.5-1T40-234v-482q0-11 5.5-21T62-752q46-24 96-36t102-12q58 0 113.5 15T480-740q51-30 106.5-45T700-800q52 0 102 12t96 36q11 5 16.5 15t5.5 21v482q0 23-19.5 35t-40.5 1q-37-20-77.5-31T700-240q-60 0-116 21t-104 59ZM280-494Z"/></svg>
								<h3 className="text-lg font-medium text-gray-900">Educational Content</h3>
							</div>
							<div className="space-y-4">
								<div key={idx} className="rounded-lg border border-green-100 overflow-hidden">
									<div className="bg-green-50 p-3">
										<p className="font-medium text-sm mb-2 text-gray-900">5 Street Taco Recipes Beyond Basic Ground Beef</p>
										<p className="text-sm text-gray-600 mb-1">💡 Recipe content gets 2.4x higher engagement</p>
										<!--
										<div className="flex items-center space-x-1 text-sm text-green-700">
											<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368"><path d="m422-232 207-248H469l29-227-185 267h139l-30 208Zm-62-128H236q-24 0-35.5-21.5T203-423l299-430q10-14 26-19.5t33 .5q17 6 25 21t6 32l-32 259h155q26 0 36.5 23t-6.5 43L416-100q-11 13-27 17t-31-3q-15-7-23.5-21.5T328-139l32-221Zm111-110Z"/></svg>
											<span>32% avg. open rate on similar content</span>
										</div>
										-->
									</div>
									<div className="border-t border-green-100">
										<div className="p-3 bg-white">
											<div className="text-sm text-gray-600 whitespace-pre-line line-clamp-4">
												Loving your new taco seasoning? Let's take your taco nights from good to unforgettable with these authentic street-style recipes that go way beyond basic ground beef.
												<br /><br/>
												🔥 Street Taco All-Stars:...
											</div>
										</div>
										<div className="px-3 py-2 bg-gray-50 border-t border-green-100">
										<button className="inline-flex items-center space-x-2 text-sm font-medium text-green-600 hover:text-green-700">
											<svg width="18" height="20" viewBox="0 0 18 20" fill="none" xmlns="http://www.w3.org/2000/svg">
												<path d="M6.11129 2.95392L6.23649 3.01652C6.7425 3.27735 7.15461 3.68946 7.41545 4.19548L7.47805 4.32068C7.72845 4.81626 8.43791 4.81626 8.69352 4.32068L8.75612 4.19548C9.01695 3.68946 9.42907 3.27735 9.93508 3.01652L10.0603 2.95392C10.5559 2.70352 10.5559 1.99406 10.0603 1.73844L9.93508 1.67584C9.42907 1.41501 9.01695 1.0029 8.75612 0.496884L8.69352 0.371685C8.44312 -0.123895 7.73366 -0.123895 7.47805 0.371685L7.41545 0.496884C7.15461 1.0029 6.7425 1.41501 6.23649 1.67584L6.11129 1.73844C5.61571 1.98884 5.61571 2.6983 6.11129 2.95392Z" fill="#16a34a"/>
												<path d="M7.09723 7.84191C7.60846 7.58108 7.60846 6.85596 7.09723 6.59513L6.3356 6.20388C5.65744 5.85437 5.10448 5.30141 4.75496 4.62324L4.36372 3.86161C4.10288 3.35038 3.37777 3.35038 3.11694 3.86161L2.72569 4.62324C2.37618 5.30141 1.82321 5.85437 1.14505 6.20388L0.383423 6.59513C-0.127808 6.85596 -0.127808 7.58108 0.383423 7.84191L1.14505 8.23315C1.82321 8.58267 2.37618 9.13563 2.72569 9.81379L3.11694 10.5754C3.37777 11.0867 4.10288 11.0867 4.36372 10.5754L4.75496 9.81379C5.10448 9.13563 5.65744 8.58267 6.3356 8.23315L7.09723 7.84191Z" fill="#16a34a"/>
												<path d="M16.8836 10.9615L16.1116 10.565C15.1778 10.0903 14.4318 9.3443 13.9571 8.41052L13.5606 7.63846C13.2111 6.94986 12.5121 6.5221 11.74 6.5221C10.968 6.5221 10.2689 6.94986 9.91943 7.63846L9.52297 8.41052C9.04825 9.3443 8.30227 10.0903 7.3685 10.565L6.59643 10.9615C5.90784 11.311 5.48007 12.01 5.48007 12.7821C5.48007 13.5541 5.90784 14.2532 6.59643 14.6027L7.3685 14.9991C8.30227 15.4738 9.04825 16.2198 9.52297 17.1536L9.91943 17.9257C10.2689 18.6143 10.968 19.042 11.74 19.042C12.5121 19.042 13.2111 18.6143 13.5606 17.9257L13.9571 17.1536C14.4318 16.2198 15.1778 15.4738 16.1116 14.9991L16.8836 14.6027C17.5722 14.2532 18 13.5541 18 12.7821C18 12.01 17.5722 11.311 16.8836 10.9615ZM16.1742 13.2098L15.4021 13.6063C14.171 14.2323 13.1903 15.213 12.5643 16.4441L12.1678 17.2162C12.0478 17.4509 11.8287 17.477 11.74 17.477C11.6514 17.477 11.4323 17.4509 11.3123 17.2162L10.9158 16.4441C10.2898 15.213 9.30908 14.2323 8.07796 13.6063L7.3059 13.2098C7.07115 13.0898 7.04507 12.8707 7.04507 12.7821C7.04507 12.6934 7.07115 12.4743 7.3059 12.3543L8.07796 11.9578C9.30908 11.3318 10.2898 10.3511 10.9158 9.11998L11.3123 8.34792C11.4323 8.11317 11.6514 8.08709 11.74 8.08709C11.8287 8.08709 12.0478 8.11317 12.1678 8.34792L12.5643 9.11998C13.1903 10.3511 14.171 11.3318 15.4021 11.9578L16.1742 12.3543C16.4089 12.4743 16.435 12.6934 16.435 12.7821C16.435 12.8707 16.4089 13.0898 16.1742 13.2098Z" fill="#16a34a"/>
											</svg>

											<span>Generate Full Content</span>
										</button>
										</div>
									</div>
								</div>
							</div>

							<!-- Promotions -->
						</div>
					</div>

					<div className="grid mt-6">
						<div className="space-y-4 mt-4">
							<div className="flex items-center space-x-2">
								<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368"><path d="M856-390 570-104q-12 12-27 18t-30 6q-15 0-30-6t-27-18L103-457q-11-11-17-25.5T80-513v-287q0-33 23.5-56.5T160-880h287q16 0 31 6.5t26 17.5l352 353q12 12 17.5 27t5.5 30q0 15-5.5 29.5T856-390ZM513-160l286-286-353-354H160v286l353 354ZM260-640q25 0 42.5-17.5T320-700q0-25-17.5-42.5T260-760q-25 0-42.5 17.5T200-700q0 25 17.5 42.5T260-640Zm220 160Z"/></svg>
							<h3 className="text-lg font-medium text-gray-900">Promotional Content</h3>
							</div>
							<div className="space-y-4">
							<div className="rounded-lg border border-blue-100 overflow-hidden">
								<div className="bg-blue-50 p-3">
									<p className="font-medium text-sm mb-2 text-gray-900">Buy any 2 seasonings, get a free recipe book</p>
									<p className="text-sm text-gray-600 mb-1">💡 Cross-sell offers convert at 12% higher with this segment</p>
									<!--
									<div className="flex items-center space-x-1 text-sm text-green-700">
										<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368"><path d="m422-232 207-248H469l29-227-185 267h139l-30 208Zm-62-128H236q-24 0-35.5-21.5T203-423l299-430q10-14 26-19.5t33 .5q17 6 25 21t6 32l-32 259h155q26 0 36.5 23t-6.5 43L416-100q-11 13-27 17t-31-3q-15-7-23.5-21.5T328-139l32-221Zm111-110Z"/></svg>
										<span>32% avg. open rate on similar content</span>
									</div>
									-->
								</div>
								<div className="border-t border-blue-100">
									<div className="p-3 bg-white">
										<div className="text-sm text-gray-600 whitespace-pre-line line-clamp-4">
											You've mastered our taco seasoning (fantastic choice, by the way!) – ready to explore more flavors?
											<br /><br/>
											🎁 For 48 Hours Only:...
										</div>
									</div>
									<div className="px-3 py-2 bg-gray-50 border-t border-blue-100">
									<button className="inline-flex items-center space-x-2 text-sm font-medium text-blue-600 hover:text-blue-700">
										<svg width="18" height="20" viewBox="0 0 18 20" fill="none" xmlns="http://www.w3.org/2000/svg">
											<path d="M6.11129 2.95392L6.23649 3.01652C6.7425 3.27735 7.15461 3.68946 7.41545 4.19548L7.47805 4.32068C7.72845 4.81626 8.43791 4.81626 8.69352 4.32068L8.75612 4.19548C9.01695 3.68946 9.42907 3.27735 9.93508 3.01652L10.0603 2.95392C10.5559 2.70352 10.5559 1.99406 10.0603 1.73844L9.93508 1.67584C9.42907 1.41501 9.01695 1.0029 8.75612 0.496884L8.69352 0.371685C8.44312 -0.123895 7.73366 -0.123895 7.47805 0.371685L7.41545 0.496884C7.15461 1.0029 6.7425 1.41501 6.23649 1.67584L6.11129 1.73844C5.61571 1.98884 5.61571 2.6983 6.11129 2.95392Z" fill="#2563eb"/>
											<path d="M7.09723 7.84191C7.60846 7.58108 7.60846 6.85596 7.09723 6.59513L6.3356 6.20388C5.65744 5.85437 5.10448 5.30141 4.75496 4.62324L4.36372 3.86161C4.10288 3.35038 3.37777 3.35038 3.11694 3.86161L2.72569 4.62324C2.37618 5.30141 1.82321 5.85437 1.14505 6.20388L0.383423 6.59513C-0.127808 6.85596 -0.127808 7.58108 0.383423 7.84191L1.14505 8.23315C1.82321 8.58267 2.37618 9.13563 2.72569 9.81379L3.11694 10.5754C3.37777 11.0867 4.10288 11.0867 4.36372 10.5754L4.75496 9.81379C5.10448 9.13563 5.65744 8.58267 6.3356 8.23315L7.09723 7.84191Z" fill="#2563eb"/>
											<path d="M16.8836 10.9615L16.1116 10.565C15.1778 10.0903 14.4318 9.3443 13.9571 8.41052L13.5606 7.63846C13.2111 6.94986 12.5121 6.5221 11.74 6.5221C10.968 6.5221 10.2689 6.94986 9.91943 7.63846L9.52297 8.41052C9.04825 9.3443 8.30227 10.0903 7.3685 10.565L6.59643 10.9615C5.90784 11.311 5.48007 12.01 5.48007 12.7821C5.48007 13.5541 5.90784 14.2532 6.59643 14.6027L7.3685 14.9991C8.30227 15.4738 9.04825 16.2198 9.52297 17.1536L9.91943 17.9257C10.2689 18.6143 10.968 19.042 11.74 19.042C12.5121 19.042 13.2111 18.6143 13.5606 17.9257L13.9571 17.1536C14.4318 16.2198 15.1778 15.4738 16.1116 14.9991L16.8836 14.6027C17.5722 14.2532 18 13.5541 18 12.7821C18 12.01 17.5722 11.311 16.8836 10.9615ZM16.1742 13.2098L15.4021 13.6063C14.171 14.2323 13.1903 15.213 12.5643 16.4441L12.1678 17.2162C12.0478 17.4509 11.8287 17.477 11.74 17.477C11.6514 17.477 11.4323 17.4509 11.3123 17.2162L10.9158 16.4441C10.2898 15.213 9.30908 14.2323 8.07796 13.6063L7.3059 13.2098C7.07115 13.0898 7.04507 12.8707 7.04507 12.7821C7.04507 12.6934 7.07115 12.4743 7.3059 12.3543L8.07796 11.9578C9.30908 11.3318 10.2898 10.3511 10.9158 9.11998L11.3123 8.34792C11.4323 8.11317 11.6514 8.08709 11.74 8.08709C11.8287 8.08709 12.0478 8.11317 12.1678 8.34792L12.5643 9.11998C13.1903 10.3511 14.171 11.3318 15.4021 11.9578L16.1742 12.3543C16.4089 12.4743 16.435 12.6934 16.435 12.7821C16.435 12.8707 16.4089 13.0898 16.1742 13.2098Z" fill="#2563eb"/>
										</svg>

										<span>Generate Full Content</span>
									</button>
									</div>
								</div>
							</div>
						</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<div v-if="segmentModalTest" class="fixed inset-0 z-50 flex bg-black bg-opacity-50">
    <div
      class="relative w-full h-full bg-white shadow-lg overflow-y-auto"
    >
      <!-- Modal Content -->
      <div class="">
		<div class="fixed inset-0 bg-gray-50 text-gray-900 overflow-auto">
			<!-- Header -->
			<div class="bg-white border-b border-gray-200">
				<div class="">
					<div class="py-6 px-10">
					<div class="flex justify-between items-center">
						<div>
						<h2 class="text-2xl font-semibold text-gray-900">Create New Segment</h2>
						<input
							type="text"
							v-model="segmentName"
							class="mt-4 w-96 px-4 py-2 rounded-md bg-white border border-gray-300 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
							placeholder="Enter segment name..."
						/>
						</div>
						<div class="flex items-center">
							<PrimaryButton cta="Save" size="xs" class="mr-2" />
							<button class="p-2 hover:bg-gray-100 rounded-full transition-colors" @click="segmentModalTest = false">
								<svg xmlns="http://www.w3.org/2000/svg" height="32px" viewBox="0 -960 960 960" width="32px" fill="#9ca3af"><path d="M480-424 284-228q-11 11-28 11t-28-11q-11-11-11-28t11-28l196-196-196-196q-11-11-11-28t11-28q11-11 28-11t28 11l196 196 196-196q11-11 28-11t28 11q11 11 11 28t-11 28L536-480l196 196q11 11 11 28t-11 28q-11 11-28 11t-28-11L480-424Z"/></svg>
							</button>
						</div>
					</div>
					</div>
				</div>

				<div className="bg-gray-50 px-10 py-4 border-t border-gray-200">
					<div className="flex items-center justify-between">
					<div className="flex items-center gap-10">
						<div>
						<div className="text-sm font-medium text-gray-500 max-w-44 min-w-44 mb-1">Estimated Audience</div>
							<div v-if="isCalculating" class="flex items-center space-x-2">
								<div class="h-6 w-14 bg-gray-200 rounded-md animate-pulse"></div>
								<div class="h-6 w-4 bg-gray-200 rounded-md animate-pulse"></div>
								<div class="h-6 w-14 bg-gray-200 rounded-md animate-pulse"></div>
							</div>
							<div className="text-xl font-semibold text-gray-900" v-else-if="audienceSize"> {{ formatNumber(audienceSize.min) }} - {{ formatNumber(audienceSize.max) }}</div>
							<div className="text-xl font-semibold text-gray-900" v-else>-</div>
						</div>

							<div className="h-8 w-px bg-gray-300" />
						<!--
							<div>
							<div className="text-sm font-medium text-gray-500 mb-1">Label</div>
							<div className="flex items-center gap-2">
								<span className="text-xl font-semibold text-gray-900">Value</span>
								<span className="text-sm text-green-600">Trend</span>
							</div>
							</div>
						-->
					</div>
					<!--
					<div className="flex items-center gap-2 text-sm text-green-600">
						<div className="w-2 h-2 bg-green-500 rounded-full" />
						Audience quality score: High
					</div>
					-->
					</div>
				</div>
			</div>

			<div class="px-10 mb-10">
				<SignalManager
					v-model:audience-size="audienceSize"
					@calculating="handleCalculating"
				/>
			</div>
		</div>
      </div>
	  <!-- // Modal Content -->
    </div>
  	</div>
</template>

<script>

import * as Utils from '../../client-old/utils/Utils';
import CardContainer from '../components/CardContainer.ts.vue';
import LightSecondaryButton from '../components/LightSecondaryButton.ts.vue'
import PrimaryButton from '../components/PrimaryButton.ts.vue';
import ToggleItem from '../components/ToggleItem.ts.vue';
import StatusMessage from '../components/StatusMessage.ts.vue';
import LearnMoreText from '../components/LearnMoreText.ts.vue';
import CancelButton from '../components/CancelButton.ts.vue';
import EditableHeader from '../components/EditableHeader.ts.vue'
import RaleonLoader from '../components/RaleonLoader.ts.vue';
import TextFadeEffect from '../components/TextFadeEffect.ts.vue';
import ModalBlank from '../components/ModalBlank.vue';
import Tooltip from '../components/Tooltip.ts.vue';
import SignalManager from '../components/SignalManagerX.ts.vue';
import CustomDropdown from '../components/CustomDropdown.ts.vue'
import SignalEditorPopup from '../components/SignalEditor.ts.vue';

import { useRoute } from 'vue-router';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	components: {
		CardContainer,
		LightSecondaryButton,
		StatusMessage,
		ToggleItem,
		PrimaryButton,
		LearnMoreText,
		CancelButton,
		EditableHeader,
		RaleonLoader,
		TextFadeEffect,
		ModalBlank,
		Tooltip,
		SignalManager,
		CustomDropdown,
		SignalEditorPopup
	},
	props: {
		loading: {
		type: Boolean,
		required: true
		}
	},
	async mounted() {

	},
	data() {
		return {
			insights: false,
			recommendations: false,
			overview: true,
			partners: false,
			generateTest: false,
			launchModelOpen: false,
			campaign: { name: 'Likely to buy in next 30 days' },
			hoveredRowIndex: 0,
			aiLoadText: ["Analyzing your audience", "Taking a look at your product...", "Playing match-maker...", "Clipping some coupons...", "Your Smart Offer preview is ready!"],
			aiTextIndex: 0,
			newSegmentModal: false,
			segmentModalTest: false,
			isPopoverOpen: false,
			hasMoreSignals: true,
			audienceSize: null,
			isCalculating: false,
			segments: [
				{
					id: 1,
					name: "Likely to Buy Soon",
					description: "This segment has customers who have a high propensity to buy in in-category in the next ~30 days.",
					tag: "AI Managed",
					profileCount: "18,435",
					avgltv: "$245",
					revenue: "$23,814",
					emailopenrate: "22%",
					emailctr: "37%",
					sending: true,
				},
				{
					id: 2,
					name: "Active One-Time Buyers",
					description: "Customers who have only purchased once, but continue to engage with your brand’s emails and website.",
					tag: "AI Managed",
					profileCount: "11,155",
					avgltv: "$105",
					revenue: "$83,256",
					emailopenrate: "14%",
					emailctr: "26%",
					sending: true,
				},
				{
					id: 3,
					name: "BFCM Prep",
					description: "Customers who are seasonal buyers and use discounts.",
					tag: "Custom",
					profileCount: "1,624",
					avgltv: "$145",
					revenue: "$13,325",
					emailopenrate: "N/A",
					emailctr: "N/A",
					sending: true
				},
				{
					id: 4,
					name: "Upsell Opportunities",
					description: "Customers who have underspent compared to their peers and have a higher predicted spend potential.",
					tag: "AI Managed",
					profileCount: "8,334",
					avgltv: "$98",
					revenue: "$8,123",
					emailopenrate: "15%",
					emailctr: "23%",
					sending: true,
				},
			],
				loadingMessages: [
					'Analyzing Your Customers',
					'Processing Engagement Data',
					'Building Smarter Segments',
					'Reviewing Results'
				],
				currentMessageIndex: 0,
				}
	},
	computed: {
		currentLoadingMessage() {
			return this.loadingMessages[this.currentMessageIndex]
		},
	},
	mounted() {
		// Cycle through loading messages
		this.messageInterval = setInterval(() => {
		this.currentMessageIndex = (this.currentMessageIndex + 1) % this.loadingMessages.length
		}, 3000)

		document.body.classList.add('content-loaded');

		document.addEventListener('click', this.handleClickOutside)
  	},
	beforeUnmount() {
		clearInterval(this.messageInterval);
		document.removeEventListener('click', this.handleClickOutside);
	},
	methods: {
		handleCalculating(calculating) {
		this.isCalculating = calculating
		},

		formatNumber(num) {
		return new Intl.NumberFormat().format(num)
		},

		togglePopover(index) {
			this.isPopoverOpen = !this.isPopoverOpen
			this.activeSegment = this.isPopoverOpen ? index : null
		},

		handlePopoverMouseLeave() {
			setTimeout(() => {
			this.isPopoverOpen = false
			this.activeSegment = null
			}, 100) // Small delay to prevent flickering
		}
    },
}
</script>

<style scoped>
.no-focus-outline {
	box-shadow: none !important;
	outline: none !important;
}

.numberChart {
	color: rgba(32, 32, 32, 0.80);
		font-family: Inter;
		font-size: 3rem;
		font-style: normal;
		font-weight: 600;
		line-height: normal;
		letter-spacing: -2.4px;
		text-transform: uppercase;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes progress {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

@keyframes robotFade {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.progress-bar {
  animation: progress 2s ease-in-out infinite;
}

.animate-spin-slow {
  animation: spin 3s linear infinite;
}

.robot-fade {
  animation: robotFade 2s ease-in-out infinite;
  /* Ensures smooth emoji rendering */
  -webkit-font-smoothing: antialiased;
  /* Prevents selection of the emoji */
  user-select: none;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Message transition animations */
.fade-message-enter-active,
.fade-message-leave-active {
  transition: all 0.5s ease;
}

.fade-message-enter-from {
  opacity: 0;
  transform: translateY(10px);
}

.fade-message-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

.fade-message-enter-to,
.fade-message-leave-from {
  opacity: 1;
  transform: translateY(0);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Initial state for segments */
.segment-card {
  opacity: 0;
  transform: translateY(20px);
}

/* Animation trigger when content is loaded */
.content-loaded .segment-card {
  animation: fadeInUp 0.6s ease-out forwards;
}

/* Hover effects */
.segment-card {
  transition: all 0.2s ease-out;
}

.segment-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: rgba(147, 51, 234, 0.3);
}
</style>
