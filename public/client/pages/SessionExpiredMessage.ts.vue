<template>
	<div class="session-expired-overlay">
		<div class="session-expired-modal">
			<p>Your session has expired. Please sign in again.</p>
			<PrimaryButton
				cta="Sign In"
				size="xs"
				icon="false"
				class="mt-4 justify-center mx-auto"
				@click="redirectToSignIn">
			</PrimaryButton>
		</div>
	</div>
</template>

  <script>
import { useRoute, useRouter } from 'vue-router';
import PrimaryButton from '../components/PrimaryButton.ts.vue'
import { useSessionStore } from './useSessionStore.ts';
export default {
	components: {
		PrimaryButton,
	},
	setup() {
		const router = useRouter();
		const route = useRoute();
		const sessionStore = useSessionStore();

		function redirectToSignIn() {
			sessionStore.logOut();
			router.push('/signin');
		}

		return {
			redirectToSignIn,
			sessionStore,
		};
	},
};
  </script>

  <style scoped>
	.session-expired-overlay {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.5); /* Semi-transparent black background */
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.session-expired-modal {
		display: flex;
		flex-direction: column;
		background-color: white;
		padding: 20px;
		border-radius: 8px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	}

	.session-expired-modal p {
		margin-bottom: 10px;
	}

	.session-expired-modal button {
		background-color: #007bff;
		color: white;
		border: none;
		padding: 8px 16px;
		border-radius: 4px;
		cursor: pointer;
	}
  </style>
