<template>
	<ProgramActive v-if="isCampaignsPage"></ProgramActive>
	<div class="p-2 sm:p-7 mr-24 flex flex-col items-center justify-center" v-if="!isFeatureAvailable && !isTrialEnded">
		<img src="../images/UpgradeCampaigns.png" width="584">
		<a class="text-ralprimary-dark font-bold md:text-xl lg:text-2xl sm:text-normal mt-4 font-[Inter]" href="/loyalty/settings/plans">Personalized Loyalty Campaigns</a>
		<p class="text-ralblack-primary text-lg mt-4 flex items-center font-[Inter] mb-4 w-1/2 text-center">
			Target loyalty campaigns to specific segments, driving deeper engagement and repeat purchase rate.
		</p>
		<PrimaryButton
					cta="Upgrade to Loyalty Pro Plan"
					size="xs"
					@click="() => this.$router.push('/loyalty/settings/plans')"
					/>
	</div>

	<div class="bg-gradient-to-tr from-[#4F3EAC] to-[#5E48F8] p-4 w-full justify-center shadow-lg flex items-center" v-if="!isFeatureAvailable && isTrialEnded">
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 14H11V9.8L12.6 11.4L14 10L10 6L6 10L7.4 11.4L9 9.8V14ZM10 20C8.61667 20 7.31667 19.7375 6.1 19.2125C4.88333 18.6875 3.825 17.975 2.925 17.075C2.025 16.175 1.3125 15.1167 0.7875 13.9C0.2625 12.6833 0 11.3833 0 10C0 8.61667 0.2625 7.31667 0.7875 6.1C1.3125 4.88333 2.025 3.825 2.925 2.925C3.825 2.025 4.88333 1.3125 6.1 0.7875C7.31667 0.2625 8.61667 0 10 0C11.3833 0 12.6833 0.2625 13.9 0.7875C15.1167 1.3125 16.175 2.025 17.075 2.925C17.975 3.825 18.6875 4.88333 19.2125 6.1C19.7375 7.31667 20 8.61667 20 10C20 11.3833 19.7375 12.6833 19.2125 13.9C18.6875 15.1167 17.975 16.175 17.075 17.075C16.175 17.975 15.1167 18.6875 13.9 19.2125C12.6833 19.7375 11.3833 20 10 20ZM10 18C12.2333 18 14.125 17.225 15.675 15.675C17.225 14.125 18 12.2333 18 10C18 7.76667 17.225 5.875 15.675 4.325C14.125 2.775 12.2333 2 10 2C7.76667 2 5.875 2.775 4.325 4.325C2.775 5.875 2 7.76667 2 10C2 12.2333 2.775 14.125 4.325 15.675C5.875 17.225 7.76667 18 10 18Z" fill="#FFA3DF"/>
        </svg>
        <div class="text-white ml-2">Your trial has expired and your program has been deactivated.</div>
        <div class="px-2 py-2 text-white text-sm border-[#FFF] border rounded-full ml-4 hover:text-black hover:bg-[#FFF] hover:cursor-pointer transitional-bg delay-200" @click="() => this.$router.push('/loyalty/settings/plans')">
                    Choose a Plan
        </div>
    </div>

	<div v-if="isFeatureAvailable || isTrialEnded" class="m-3 sm:m-10 sm:m-7" :class="{'': campaignTable, 'm-3 sm:m-7': !campaignTable}">
		<div class="flex flex-col sm:flex-row items-start justify-between mb-12" v-if="!campaignTable">
			<div>
				<h1 class="text-3xl sm:text-6xl font-sans font-medium opacity-70 text-ralblack-primary">Campaigns</h1>
				<div class="text-ralblack-primary text-sm font-[Inter] mt-4 mb-4">
					<span>Add to your Loyalty Program with targeted promotions or limited time offers. Great for personalization, special events, and maximizing return on investment.</span>
					<LearnMoreText text="Learn more" url="https://www.raleon.io" class="ml-0 sm:ml-1"></LearnMoreText>
				</div>
			</div>
			<PrimaryButton v-if="isProgramLive" cta="add" icon="true" @click="addCampaign()"></PrimaryButton>
		</div>

		<div v-if="isProgramLive">
			<div v-if="isLoading || isLoading == null">
				<CampaignTableSkeletonLoader />
			</div>
			<div v-if="!isLoading">
				<RaleonTable class="mt-3"
					:clickable-rows="true"
					:column-headers="this.columnHeaders"
					:row-data="this.otherRowData" :showActiveOrNot="true" v-if="this.otherRowData.length > 0"
					@delete-row-clicked="handleDeleteCampaign"/>
				<div class="flex flex-col items-center justify-center h-22 mb-2" v-else-if="this.otherRowData.length <= 0 && !isLoading">
					<CampaignEmptyState />
					<p class="text-xl text-ralblack-primary mt-2">You haven't added any campaigns yet.</p>
					<div
						class="inline-flex items-center px-4 py-2 mt-2 bg-gradient-to-bl from-indigo-800 to-indigo-600 rounded-3xl cursor-pointer hover:bg-blue-600 focus:outline-none focus:ring"
						@click="addCampaign()">
						<svg width="22" height="23" viewBox="0 0 40 41" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path d="M13.3333 20.5H20M20 20.5H26.6667M20 20.5V27.1667M20 20.5V13.8333M20 35.5C11.7157 35.5 5 28.7843 5 20.5C5 12.2157 11.7157 5.5 20 5.5C28.2843 5.5 35 12.2157 35 20.5C35 28.7843 28.2843 35.5 20 35.5Z" stroke="white" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
						</svg>
						<span class="ml-2 text-white">add</span>
					</div>
				</div>
			</div>
		</div>
		<ProgramNotLive v-else></ProgramNotLive>
	</div>

	<ModalBlank
		id="campaign-delete-modal"
		:modal-open="deleteModalOpen"
		@close-modal="deleteModalOpen = false">

		<div class="flex flex-col px-5 py-4">
			<div class="text-xl">{{ deleteModalText }}</div>
			<div class="mt-10 flex justify-end gap-3">
				<LightSecondaryButton cta="Cancel" @click="deleteModalOpen = false"></LightSecondaryButton>
				<PrimaryButton
					size="xs"
					cta="Delete"
					@click="deleteCampaign()"
					:disabled="isDeleting"
					:showSpinner="isDeleting"
					:spinnerText="'Deleting...'">
				</PrimaryButton>
			</div>
		</div>
	</ModalBlank>
</template>

<script>

import * as Utils from '../../client-old/utils/Utils';
import RaleonTable from '../components/RaleonTable.ts.vue';
import ProgramNotLive from '../components/ProgramNotLive.ts.vue';
import PrimaryButton from '../components/PrimaryButton.ts.vue';
import LightSecondaryButton from '../components/LightSecondaryButton.ts.vue';
import ProgramActive from '../components/ProgramActive.ts.vue';
import LearnMoreText from '../components/LearnMoreText.ts.vue';
import PreviewLoyaltyProgram from '../components/PreviewLoyalty.ts.vue';
import { customerIOTrackEvent } from '../services/customerio.js';
import CampaignEmptyState from '../components/CampaignEmptyState.ts.vue';
import CampaignTableSkeletonLoader from '../components/CampaignTableSkeletonLoader.ts.vue';
import ModalBlank from '../components/ModalBlank.vue'
import { deleteCampaign } from '../services/campaigns.js';
import { isFeatureAvailable } from '../services/features.js';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	props: ['campaignTable'],
	components: {
		RaleonTable,
		PrimaryButton,
		LightSecondaryButton,
		ProgramActive,
		LearnMoreText,
		PreviewLoyaltyProgram,
		CampaignEmptyState,
		CampaignTableSkeletonLoader,
		ModalBlank,
		ProgramNotLive,
	},
	data() {
		return {
			isCampaignsPage: window.location.pathname === '/campaigns',
			columnHeaders: [
				{
					name: 'Name',
					tooltip: 'Name',
					value: 'name',
				},
				{
					name: 'Live Status',
					tooltip: 'Is active',
					value: 'liveStatus',
				},
				{
					name: 'Duration/Ongoing',
					tooltip: 'Is evergreen',
					value: 'ongoingStatus',
				},
			],
			rowData: [],
			foundationRowData: [],
			otherRowData: [],
			isLoading: true,
			deleteModalOpen: false,
			toDelete: {},
			deleteType: '',
			isDeleting: false,
			isProgramLive: true,
		}
	},
	computed: {
		isFeatureAvailable() {
			return isFeatureAvailable('campaigns');
		},
		isTrialEnded() {
			const freeTrialData = JSON.parse(localStorage.getItem('freeTrialData'));

			return freeTrialData != {} && !freeTrialData.hasPaidPlan && freeTrialData.daysLeft < 0
		}
	},
	async mounted() {
		this.isLoading = true;
		await this.checkProgramStatus();
		const result = await this.fetchCampaignData();
		this.isLoading = false;
		console.log(result);
	},
	methods: {
		async checkProgramStatus() {
			try {
				const response = await fetch(`${URL_DOMAIN}/loyalty-programs`, {
				method: 'GET',
				credentials: 'omit',
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Content-Type': 'application/json'
				}
				});
				const jsonResponse = await response.json();
				this.isProgramLive = jsonResponse[0]?.active || false;
			} catch (err) {
				console.error("Error checking program status:", err);
				this.isProgramLive = false;
			}
		},
		async fetchCampaignData() {
			const response = await fetch(`${URL_DOMAIN}/loyalty-campaigns?filter[include][]=vipTier`, {
				method: 'GET',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				}
			});

			if (response.ok && response.status >= 200 && response.status < 300) {
				const result = await response.json();

				this.rowData = result.map(x => ({
					...x,
					href: `/loyalty/campaign/${x.id}`,
					target: '_top',
				}));

				const foundationRowData = [];
				const otherRowData = [];
				for (let row of this.rowData) {
					if (row.loyaltySegment === 'Everyone' && row.evergreen) {
						foundationRowData.push(row);
					} else if (!row.vipTier) {
						otherRowData.push({
							...row,
							ongoingStatus: row.evergreen ? 'Ongoing' : `${new Date(row.startdate).toLocaleDateString('en-US')} - ${new Date(row.enddate).toLocaleDateString('en-US')}`,
							showDelete: true,
						});
					}
				}

				this.foundationRowData = foundationRowData;
				this.otherRowData = otherRowData;
			}
		},
		async addCampaign() {
			customerIOTrackEvent('Add Campaign Started');
			const programId = await this.createLoyaltyProgram();
			const campaignId = await this.createDefaultLoyaltyCampaign(programId);

			this.$router.push(`/loyalty/campaign/${campaignId}`);
		},
		async createLoyaltyProgram() {
			let jsonresponse = {};
			try {
				const response = await fetch(`${URL_DOMAIN}/loyalty-programs`, {
					method: 'GET',
					withCreditentials: true,
					credentials: 'omit',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Access-Control-Allow-Origin': '*',
						'Content-Type': 'application/json'
					}
				});
				jsonresponse = await response.json();
				console.log("jsonresponse: " + JSON.stringify(jsonresponse));
			} catch (err) {
				console.log("Error: " + JSON.stringify(err));
			}

			if (jsonresponse.length > 0) {
				return jsonresponse[0].id;
			} else {
				var payload = {
					name: "Main Program",
					active: false
				}

				let url = `/loyalty-programs`;
				const response = await fetch(`${URL_DOMAIN}${url}`, {
					method: 'POST',
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					},
					body: JSON.stringify(payload),
				});

				let data = await response.json();


				var payloadPoints = {
					name: "Points",
					conversionToUSD: 100
				}
				let urlPoints = `/loyalty-programs/${data.id}/loyalty-currencies`;
				const responsePoints = await fetch(`${URL_DOMAIN}${urlPoints}`, {
					method: 'POST',
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					},
					body: JSON.stringify(payloadPoints),
				});

				//let dataPoints = await response.json();

				console.log(data);
				return data.id;
			}
		},
		async createDefaultLoyaltyCampaign(programId) {
			var payload = {
				name: "Untitled Campaign",
				evergreen: true,
				loyaltySegment: "Growth",
				active: false
			}
			let url = `/loyalty-programs/${programId}/loyalty-campaigns`;
			const response = await fetch(`${URL_DOMAIN}${url}`, {
				method: 'POST',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify(payload),
			});

			let data = await response.json();
			console.log(data);
			return data.id;
		},
		handleDeleteCampaign(data) {
			this.toDelete = data;
			this.deleteModalText = `Are you sure you want to delete the campaign "${data.name}"? This will delete all associated ways to earn and rewards.`;
			this.deleteModalOpen = true;
		},
		async deleteCampaign() {
			this.isDeleting = true;
			console.log(`deleting campaign ${JSON.stringify(this.toDelete)}`);
			const response = await deleteCampaign(this.toDelete.id);
			if (response.status == 'success') {
				await this.fetchCampaignData();
				customerIOTrackEvent('Deleted Campaign');
			}
			this.deleteModalOpen = false;
			this.deleteModalText = '';
			this.toDelete = {};
			this.isDeleting = false;
		},
	}
}
</script>
<style scoped>
.big-button {
	font-size: 3em;
	border-radius: 1em;
	padding: 0 0.5em;
	text-transform: uppercase;
}

.big-button>svg {
	margin-right: 0.25em;
}

.test {
	width: 50%;
	padding: 0em 0.4em;
}

@media all and (max-width: 768px) {
	.test {
		width: 100%;
	}
}
</style>

