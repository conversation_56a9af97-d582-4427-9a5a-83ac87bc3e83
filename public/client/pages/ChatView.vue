<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <h1 class="text-2xl font-semibold text-gray-900 bg-gradient-to-r from-[#6E41FF] to-[#8A4FFF] bg-clip-text text-transparent">
          Chat Assistant
        </h1>
      </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="bg-white rounded-2xl shadow-sm border border-gray-200 h-[calc(100vh-12rem)]">
        <Chat />
      </div>
    </main>
  </div>
</template>

<script>
import Chat from '../components/Chat.vue';

export default {
  name: 'ChatView',
  components: {
    Chat
  }
};
</script>
