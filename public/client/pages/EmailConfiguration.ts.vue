<template>
	<StatusMessage :message=status.message :status=status.type @resetStatus="status.type = 'nope'"></StatusMessage>

	<div class="m-3 sm:m-7">
		<div class="flex align-center">
			<div class="w-full lg:w-[93%] flex sm:flex-row md:flex-col items-start justify-between lg:mb-5">
				<h1 class="text-3xl sm:text-6xl font-sans font-medium opacity-70 text-ralblack-primary">
					{{ eventNameHeader }}
				</h1>
				<div class="flex mb-10 mt-2 ml-1 align-center">
					<button class="flex p-0 border-none bg-none focus:outline-none" @click="back">
						<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path d="M18.3333 15L13.3333 20M13.3333 20L18.3333 25M13.3333 20H26.6667M35 20C35 11.7157 28.2843 5 20 5C11.7157 5 5 11.7157 5 20C5 28.2843 11.7157 35 20 35C28.2843 35 35 28.2843 35 20Z" stroke="#202020" stroke-opacity="0.8" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
						</svg>
						<span class="text-neutral-500 text-xl ml-2 mt-[5px]">Email Automation</span>
					</button>
				</div>
			</div>
			<div class="flex flex-col mt-7 mr-5">
				<ToggleItem
					@toggleChange="handleLoyaltyEventActiveToggle"
					:state="eventActive"
					:showLabel=true
					onLabel="Active"
					offLabel="Inactive"
					@click.stop>
				</ToggleItem>
			</div>
			<div class="flex-shrink-0 mt-2 md:mr-8">
				<PrimaryButton
					cta="Save"
					@click="saveTemplate"
					:disabled="isSaving"
					size="small"
					class="mr-2 sm:mr-7"
					:showSpinner="isSaving">
				</PrimaryButton>
			</div>
		</div>

		<div class="flex">
			<div class="flex flex-col w-3/5 justify-start">
				<!-- Email Preview -->
				<div class="bg-white rounded-2xl border shadow border-ralprimary-light pl-4 pr-4 pb-4 border-opacity-50 hover:border-opacity-90 hover:shadow-lg ml-4 transition-all duration-300 ease-in-out overflow-hidden">
					<label class="flex text-ralsecondary-start text-sm font-normal mt-4" for="subjectLine">Subject Line</label>
					<input
						id="subjectLine"
						type="text"
						v-model="emailSubject"
						class="h-10 w-full rounded-lg border border-ralbackground-dark-line border-opacity-20 text-sm mt-2"
					/>

					<label class="flex text-ralsecondary-start text-sm font-normal mt-4" for="fromName">From Name</label>
					<input
						id="fromName"
						type="text"
						v-model="fromName"
						class="h-10 w-full rounded-lg border border-ralbackground-dark-line border-opacity-20 text-sm mt-2"
					/>

					<div class="border-[0.5px] border-ralbackground-dark-line border-opacity-50 mt-12"></div>

					<div class="mt-4" v-if="!this.templatesLoading">
						<loyalty-email-template
							ref="emailTemplate"
							:template="this.template"
							:logoUrl="this.brandingData.logoUrl"
							:headerImageUrl="this.brandingData.headerImageUrl"
							:background-color="this.brandingData.backgroundColor"
							:title="this.brandingData.title"
							:title-font-size="this.brandingData.titleFontSize"
							:title-text-color="this.brandingData.titleTextColor"
							:subtitle="this.brandingData.subtitle"
							:subtitle-font-size="this.brandingData.subtitleFontSize"
							:subtitle-text-color="this.brandingData.subtitleTextColor"
							:button-url="this.brandingData.buttonUrl"
							:button-text="this.brandingData.buttonText"
							:button-text-color="this.brandingData.buttonTextColor"
							:button-background-color="this.brandingData.buttonBackgroundColor"
							:button-border-radius="this.brandingData.buttonBorderRadius"
							:company-name="this.brandingData.companyName"
							:company-address="this.brandingData.companyAddress"
							:unsubscribe-text="this.brandingData.unsubscribeText"
							:send-reason="this.brandingData.sendReason"
						>
						</loyalty-email-template>
					</div>
				</div>
			</div>

			<div class="flex flex-col w-2/5 justify-start ml-4 gap-4">
				<div
					class="bg-white rounded-2xl border shadow border-ralprimary-light pl-4 pr-4 pb-4 border-opacity-50 hover:border-opacity-90 hover:shadow-lg transition-all duration-300 ease-in-out overflow-hidden">
					<label class="flex text-ralsecondary-start text-md font-normal mt-4">Smart Send</label>

					<label class="flex text-ralsecondary-start text-xs mt-1 mb-2">Will not send an automated email to a customer if they have received an automated email from Raleon in the last 24 hours.</label>
					<ToggleItem
						@toggleChange="(active) => this.smartSend = active"
						:state="smartSend"
						:showLabel=true
						onLabel="On"
						offLabel="Off"
						@click.stop>
					</ToggleItem>
				</div>

				<div
					class="bg-white rounded-2xl border shadow border-ralprimary-light px-4 pb-4 border-opacity-50 hover:border-opacity-90 hover:shadow-lg transition-all duration-300 ease-in-out overflow-hidden">
					<div class="flex">
						<div class="flex flex-col">
							<label class="flex text-ralsecondary-start text-md font-normal my-4">Logo</label>
							<ImageUpload
								buttonText="Upload Logo"
								aspectRatio="auto"
								@imageSelected="(event) => {this.brandingData.logoUrl = event.url;}"
								popover-classes="w-full md:w-auto h-auto md:max-h-[100vh] lg:max-h-[50vw] lg:absolute lg:right-auto lg:w-[30vw] lg:h-auto"
							/>
						</div>
						<div class="my-auto mx-auto">
							<div v-if="this.templatesLoading" class="img-preview-skeleton">
								<div class="skeleton skeleton-preview-img"></div>
							</div>
							<img
								v-if="!this.templatesLoading && this.brandingData.logoUrl"
								:src="this.brandingData.logoUrl"
								alt="logo"
								class="h-20 w-20 mt-4 object-contain"/>
						</div>
					</div>
				</div>

				<div
					class="bg-white rounded-2xl border shadow border-ralprimary-light px-4 pb-4 border-opacity-50 hover:border-opacity-90 hover:shadow-lg transition-all duration-300 ease-in-out overflow-hidden">
					<div class="flex">
						<div class="flex flex-col">
							<label class="flex text-ralsecondary-start text-md font-normal my-4">Hero Image</label>
							<ImageUpload buttonText="Upload Hero" @imageSelected="(event) => {this.brandingData.headerImageUrl = event.url;}" />
						</div>
						<div class="my-auto mx-auto">
							<div v-if="this.templatesLoading" class="img-preview-skeleton">
								<div class="skeleton skeleton-preview-img"></div>
							</div>
							<img
								v-if="!this.templatesLoading && this.brandingData.headerImageUrl"
								:src="this.brandingData.headerImageUrl"
								alt="logo"
								class="h-20 w-20 mt-4 object-contain"/>
						</div>
					</div>
				</div>

				<div
					class="bg-white rounded-2xl border shadow border-ralprimary-light pl-4 pr-4 pb-4 border-opacity-50 hover:border-opacity-90 hover:shadow-lg transition-all duration-300 ease-in-out overflow-hidden">
					<label class="flex text-ralsecondary-start text-md font-normal mt-4">Content</label>
					<label class="flex text-ralsecondary-start text-sm font-normal mt-4">Title</label>
					<input
						type="text"
						v-model="this.brandingData.title"
						class="h-10 w-full rounded-lg border border-ralbackground-dark-line border-opacity-20 mt-2 text-sm" />

					<label class="flex text-ralsecondary-start text-sm font-normal mt-4">Title Font Size (in pixels)</label>
					<input
						type="number"
						v-model="computedTitleFontSize"
						class="h-10 w-full rounded-lg border border-ralbackground-dark-line border-opacity-20 mt-2 text-sm" />

					<div class="mt-4">
						<LvColorPicker
							class="color-picker text-sm"
							label="Title Text Color"
							v-model="this.brandingData.titleTextColor"
							:clearable="false"
							:key="'email-title-text-color' + brandingUpdateCounter"
							:value="computedTitleTextColor"
							:bottomBar="false" />
					</div>

					<label class="flex text-ralsecondary-start text-sm font-normal mt-4">Subtitle</label>
					<input
						type="text"
						v-model="this.brandingData.subtitle"
						class="h-10 w-full rounded-lg border border-ralbackground-dark-line border-opacity-20 mt-2 text-sm" />

					<label class="flex text-ralsecondary-start text-sm font-normal mt-4">Subtitle Font Size (in pixels)</label>
					<input
						type="number"
						v-model="computedSubtitleFontSize"
						class="h-10 w-full rounded-lg border border-ralbackground-dark-line border-opacity-20 mt-2 text-sm" />

					<div class="mt-4">
						<LvColorPicker
							class="color-picker text-sm"
							label="Subtitle Text Color"
							v-model="this.brandingData.subtitleTextColor"
							:clearable="false"
							:key="'email-subtitle-text-color' + brandingUpdateCounter"
							:value="computedSubtitleTextColor"
							:bottomBar="false" />
					</div>
				</div>

				<div
					class="bg-white rounded-2xl border shadow border-ralprimary-light pl-4 pr-4 pb-4 border-opacity-50 hover:border-opacity-90 hover:shadow-lg transition-all duration-300 ease-in-out overflow-hidden">
					<label class="flex text-ralsecondary-start text-md font-normal my-4">Button</label>

					<label class="flex text-ralsecondary-start text-sm font-normal mt-4">Button Url</label>
					<input
						type="text"
						v-model="this.brandingData.buttonUrl"
						class="h-10 w-full rounded-lg border border-ralbackground-dark-line border-opacity-20 mt-2 text-sm" />

					<label class="flex text-ralsecondary-start text-sm font-normal mt-4">Button Text</label>
					<input
						type="text"
						v-model="this.brandingData.buttonText"
						class="h-10 w-full rounded-lg border border-ralbackground-dark-line border-opacity-20 mt-2 text-sm" />

					<div class="mt-4">
						<LvColorPicker
							class="color-picker text-sm"
							label="Button Text Color"
							v-model="this.brandingData.buttonTextColor"
							:clearable="false"
							:key="'email-button-text-color' + brandingUpdateCounter"
							:value="computedButtonTextColor"
							:bottomBar="false" />
					</div>

					<div class="mt-4">
						<LvColorPicker
							class="color-picker text-sm"
							label="Button Background Color"
							v-model="this.brandingData.buttonBackgroundColor"
							:clearable="false"
							:key="'email-button-bg-color' + brandingUpdateCounter"
							:value="computedButtonBackgroundColor"
							:bottomBar="false" />
					</div>

					<label class="flex text-ralsecondary-start text-sm font-normal mt-4">Button Border Radius (in pixels)</label>
					<input
						type="number"
						v-model="computedButtonBorderRadius"
						class="h-10 w-full rounded-lg border border-ralbackground-dark-line border-opacity-20 mt-2 text-sm"
					/>
				</div>

				<div
					class="bg-white rounded-2xl border shadow border-ralprimary-light pl-4 pr-4 pb-4 border-opacity-50 hover:border-opacity-90 hover:shadow-lg transition-all duration-300 ease-in-out overflow-hidden">
					<label class="flex text-ralsecondary-start text-md font-normal my-4">Footer</label>

					<label class="flex text-ralsecondary-start text-sm font-normal mt-4">Company Name</label>
					<input
						type="text"
						v-model="this.brandingData.companyName"
						class="h-10 w-full rounded-lg border border-ralbackground-dark-line border-opacity-20 mt-2 text-sm" />

					<label class="flex text-ralsecondary-start text-sm font-normal mt-4">Company Address</label>
					<input
						type="text"
						v-model="this.brandingData.companyAddress"
						class="h-10 w-full rounded-lg border border-ralbackground-dark-line border-opacity-20 mt-2 text-sm" />

					<label class="flex text-ralsecondary-start text-sm font-normal mt-4">Unsubscribe Text</label>
					<input
						type="text"
						v-model="this.brandingData.unsubscribeText"
						class="h-10 w-full rounded-lg border border-ralbackground-dark-line border-opacity-20 mt-2 text-sm" />

					<label class="flex text-ralsecondary-start text-sm font-normal mt-4">Send Reason</label>
					<input
						type="text"
						v-model="this.brandingData.sendReason"
						class="h-10 w-full rounded-lg border border-ralbackground-dark-line border-opacity-20 mt-2 text-sm" />
				</div>
			</div>

		</div>

	</div>

</template>

<script lang="ts">

import StatusMessage from '../components/StatusMessage.ts.vue';
import LearnMoreText from '../components/LearnMoreText.ts.vue';
import PrimaryButton from '../components/PrimaryButton.ts.vue';
import LightSecondaryButton from '../components/LightSecondaryButton.ts.vue';
import ImageUpload from '../components/ImageUpload.ts.vue';
import ToggleItem from '../components/ToggleItem.ts.vue';
import LvColorPicker from 'lightvue/color-picker';
import { getEmailBrandingData } from '../email-templates/email-branding-data';
import '../email-templates/email-template';
import {
	getEmailTemplate,
	saveEmailBranding,
	loadEmailBranding,
	activeToggleLoyaltyEvent,
} from '../services/loyalty-emails.js';
import { getCurrentOrg } from '../services/organization.js';

export default {
	name: 'EmailConfiguration',
	props: ['eventName'],
	components: {
		StatusMessage,
		LearnMoreText,
		PrimaryButton,
		LightSecondaryButton,
		ImageUpload,
		ToggleItem,
		LvColorPicker,
	},
	data() {
		return {
			status: {},
			emailSendingActive: false,
			templatesLoading: false,
			emailSubject: '',
			fromName: '',
			eventActive: false,
			smartSend: true,
			isSaving: false,
			template: '',
			brandingData: getEmailBrandingData(),
			brandingUpdateCounter: 0,
		};
	},
	async mounted() {
		this.templatesLoading = true;
		const orgInfo = await getCurrentOrg();
		this.setDefaultData(orgInfo);
		const template = await getEmailTemplate();
		const emailBranding = await loadEmailBranding(this.eventName);
		console.log(emailBranding);
		if (emailBranding) {
			this.brandingData = {
				...getEmailBrandingData(),
				...JSON.parse(emailBranding.branding)
			};
			this.emailSubject = emailBranding.emailSubject;
			this.fromName = emailBranding.fromName;
			this.eventActive = emailBranding.active;
			this.smartSend = emailBranding.smartSend;
		}
		this.brandingUpdateCounter++;
		this.template = template.body;
		this.templatesLoading = false;
	},
	computed: {
		computedTitleFontSize: {
			get() {
				return parseInt(this.brandingData.titleFontSize);
			},
			set(value) {
				this.brandingData.titleFontSize = `${value}px`;
			}
		},
		computedSubtitleFontSize: {
			get() {
				return parseInt(this.brandingData.subtitleFontSize);
			},
			set(value) {
				this.brandingData.subtitleFontSize = `${value}px`;
			}
		},
		computedButtonBorderRadius: {
			get() {
				return parseInt(this.brandingData.buttonBorderRadius);
			},
			set(value) {
				this.brandingData.buttonBorderRadius = `${value}px`;
			}
		},
		computedTitleTextColor: {
			get() {
				return this.brandingData.titleTextColor;
			},
			set(value) {
				this.brandingData.titleTextColor = value;
			}
		},
		computedSubtitleTextColor: {
			get() {
				return this.brandingData.subtitleTextColor;
			},
			set(value) {
				this.brandingData.subtitleTextColor = value;
			}
		},
		computedButtonTextColor: {
			get() {
				return this.brandingData.buttonTextColor;
			},
			set(value) {
				this.brandingData.buttonTextColor = value;
			}
		},
		computedButtonBackgroundColor: {
			get() {
				return this.brandingData.buttonBackgroundColor;
			},
			set(value) {
				this.brandingData.buttonBackgroundColor = value;
			}
		},
		eventNameHeader() {
			switch (this.eventName) {
				case 'point_balance_change':
					return 'Points Earned Email';
				case 'raleon_joined_loyalty':
					return 'Joined Loyalty Email';
				case 'reward_expiring':
					return 'Reward Expiring Email';
				case 'birthday_reward_granted':
					return 'Birthday Reward Email';
				case 'reward_granted':
					return 'Reward Granted Email';
				case 'vip_tier_updated':
					return 'VIP Tier Achieved Email';
				default:
					return 'Loyalty Email';
			}
		},
	},
	methods: {
		async saveTemplate() {
			this.isSaving = true;
			const response = await saveEmailBranding(this.eventName, {
				branding: this.brandingData,
				emailSubject: this.emailSubject,
				fromName: this.fromName,
				smartSend: this.smartSend || false,
				active: this.eventActive,
			});
			if (response.statusCode !== 200) {
				this.status = { type: 'fail', message: 'Failed to save template' };
			} else {
				this.status = { type: 'success', message: 'Template saved successfully' };
			}
			this.isSaving = false;
		},
		back() {
			this.$router.push('/loyalty/communication');
		},
		async handleLoyaltyEventActiveToggle(active) {
			this.eventActive = active;
			const response = await activeToggleLoyaltyEvent(this.eventName, active);
			if (response.statusCode == 404) {
				const newTemplate = await this.saveTemplate();
				if (newTemplate.statusCode !== 200) {
					this.status = { type: 'fail', message: 'Failed to activate. Ensure all fields are filled out before activating.' };
					this.eventActive = !active;
				} else {
					this.status = {
						type: 'success',
						message: `${this.eventNameHeader} ${active ? 'activated' : 'deactivated'}`,
					};
				}
			} else {
				this.status = {
					type: 'success',
					message: `${this.eventNameHeader} ${active ? 'activated' : 'deactivated'}`,
				};
			}
		},
		setDefaultData(orgInfo: any) {
			this.brandingData.buttonUrl = `https://${orgInfo.externalDomain}`;
			this.brandingData.companyName = orgInfo.name;
			this.brandingData.companyAddress = '123 Orange Street, Orange County, CA 12345';
			this.brandingData.unsubscribeText = 'Don\'t want to receive these emails from us?';
			this.brandingData.sendReason = 'Sent as part of our loyalty program';
			switch (this.eventName) {
				case 'point_balance_change':
					this.brandingData.title = `You've earned {{balanceChange}} points`;
					this.brandingData.subtitle = 'Head to the store to use your points!';
					this.brandingData.buttonText = 'Shop Now';
					break;
				case 'raleon_joined_loyalty':
					this.brandingData.title = `Welcome to our loyalty program!`;
					this.brandingData.subtitle = 'Head to the store to claim your first reward!';
					this.brandingData.buttonText = 'Shop Now';
					break;
				case 'reward_expiring':
					this.brandingData.title = `Your {{rewardName}} is expiring soon!`;
					this.brandingData.subtitle = 'Head to the store before {{expirationDate}} to claim your reward!';
					this.brandingData.buttonText = 'Claim {{rewardName}}';
					this.brandingData.buttonUrl = `https://${orgInfo.externalDomain}?apply_coupon={{couponId}}`;
					break;
				case 'birthday_reward_granted':
					this.brandingData.title = `Happy Birthday!`;
					this.brandingData.subtitle = 'Head to the store to claim your birthday {{rewardName}}!';
					this.brandingData.buttonText = 'Claim Reward';
					this.brandingData.buttonUrl = `https://${orgInfo.externalDomain}?apply_coupon={{couponId}}`;
					break;
				case 'reward_granted':
					this.brandingData.title = `You've received a {{rewardName}}`;
					this.brandingData.subtitle = 'Head to the store before {{expirationDate}} to claim {{rewardName}}';
					this.brandingData.buttonText = 'Claim {{rewardName}}';
					this.brandingData.buttonUrl = `https://${orgInfo.externalDomain}?apply_coupon={{couponId}}`;
					break;
				case 'vip_tier_updated':
					this.brandingData.title = `You're now part of our {{vipTier}} VIP Tier`;
					this.brandingData.subtitle = `You need {{pointsToNextTier}} to get to the next tier`;
					this.brandingData.buttonText = 'Shop Now';
					this.brandingData.buttonUrl = `https://${orgInfo.externalDomain}`;
					break;
				default:
					this.brandingData.title = 'Welcome to our loyalty program!';
					this.brandingData.subtitle = 'Head over to the store to start earning rewards!';
					this.brandingData.buttonText = 'Shop Now';
					break;
			}
		},
	}
};

</script>


<style>

	.no-focus-outline {
		box-shadow: none !important;
		outline: none !important;
	}

	.skeleton {
		background-color: #f2f2f2;
		border-radius: 4px;
		width: 100%;
		height: 100%;
	}

	.skeleton-preview-img {
		background-color: #f2f2f2;
		width: 60px;
		height: 60px;
		margin-top: 20px;
	}

</style>
