<template>
	<div id="app-container" class="bg-[#F5F5F5]">
		<div v-if="showSupportBanner" class="support-banner" :style="{ display: bannerDisplay }">
			<div class="flex justify-between items-center px-8">
				<div class="flex-1 text-center pointer">Support Mode: You are in production</div>
				<div @click="hideBanner" class="cursor-pointer">
					<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-6 h-6">
						<path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
					</svg>
				</div>
			</div>
		</div>
		<div v-if="!showSidebar" id="background-logo">
			<svg width="680" height="1211" viewBox="0 0 680 1211" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path fill-rule="evenodd" clip-rule="evenodd"
					d="M1391.61 872.01L700.5 0L9.39485 872.01L137.842 1006.1L0 1172.77V1327H295.321L700.5 820.168L1105.68 1327H1401V1172.77L1263.16 1006.1L1391.61 872.01ZM700.5 325.738L207.011 922.459L152.259 865.303L700.5 173.554L1248.74 865.303L1193.99 922.459L700.5 325.738ZM107.769 1211.61L700.5 494.889L1293.23 1211.61V1219.15H1157.48L700.5 647.531L243.525 1219.15H107.769V1211.61Z"
					fill="#EEEAFA" fill-opacity="0.15" />
				<path d="M1001.9 1327L700.51 943.146L399.124 1327H536.179L700.51 1117.7L864.841 1327H1001.9Z" fill="#EEEAFA"
					fill-opacity="0.15" />
			</svg>
		</div>

		<div class="flex" :style="appAreaStyle" id="app-area">
			<div v-if="showSidebar && !isMobileView">
				<Sidebar />
			</div>
			<MobileSidebar v-if="isMobileView && showSidebar" />
			<content class="flex-1 bg-[#F5F5F5] overflow-y-auto" :class="{'content-without-sidebar': showSidebar}">
				<router-view :key="$route.fullPath" />
			</content>
		</div>

		<Teleport to="body">
			<SessionExpiredMessage v-if="showExpiredSessionDialog" />
			<ToastStatus :status="status" :text="statusText" @clear-status="clearStatus" @click="handleToastClick" :hide-timeout="-1" :clickable="true" />
		</Teleport>
	</div>
</template>

<script>
import Sidebar from './components/Sidebar.ts.vue';
import MobileSidebar from './components/SidebarMobile.ts.vue';
import SessionExpiredMessage from './pages/SessionExpiredMessage.ts.vue';
import { useSessionStore } from './pages/useSessionStore.ts';
import * as Utils from '../client-old/utils/Utils';
import {Crisp} from "crisp-sdk-web";
import ToastStatus from '../client-old/pages/component/ToastStatus.vue';
const URL_DOMAIN = Utils.URL_DOMAIN;

let segmentGenerationInterval;
export default {
	components: {
		Sidebar,
		SessionExpiredMessage,
		MobileSidebar,
		ToastStatus,
	},
	data() {
		const session = useSessionStore();

		return {
			session,
			showSupportBanner: false,
			bannerDisplay: 'block',
			isMobileView: false,
			generatingSegments: false,
			status: '',
			statusText: '',
			sessionInitialized: false
		};
	},
	async mounted() {
		await this.session.checkSession();
		this.sessionInitialized = true;
		this.setupSupportChat();
		this.checkMobileView();
		this.getFeatureStates();
		window.addEventListener('resize', this.checkMobileView);
		(async () => {
			const show = await this.shouldShowSupport();
			this.showSupportBanner = show;
		})();

		// Check Klaviyo sync progress and set up segments generation
		this.initializeSegmentGeneration();

		// Initialize prompt context caching
		//this.initializePromptCaching();
	},
	beforeUnmount() {
		window.removeEventListener('resize', this.checkMobileView);
		// Clear the interval when component is unmounted
		if (segmentGenerationInterval) {
			clearInterval(segmentGenerationInterval);
		}
	},
	computed: {
		showSidebar() {
			// Define the base paths where the sidebar should be closed
			const basePathsWithSidebarClosed = ['/signin', '/signup', '/onboarding', '/unsubscribe'];

			// Check if the current path starts with any of the base paths
			const isSidebarClosedPath = basePathsWithSidebarClosed.some(basePath =>
				this.$route.path.startsWith(basePath)
			);

			let token = localStorage.getItem('token');

			if (!token || this.$route.path == '/') {
				return false;
			}

			// The sidebar is shown unless the current path starts with a base path where it should be closed
			// or the 'sidebar' URL parameter is set to 'false'
			return !isSidebarClosedPath && this.$route.query.sidebar !== 'false';
		},
		appAreaStyle() {
			//this prevents scrolling when the sidebar is open
			if (this.showSidebar) {
				return {
					position: 'fixed',
					height: '100dvh',
					width: '100vw',
					zIndex: 1,
				};
			} else {
				// Adjust the style when the sidebar is not shown
				return {
					position: 'fixed',
					height: 'auto', // Set the height to 'auto' or any other value that fits your design when the sidebar is off
					width: '100vw',
					zIndex: 1,
				};
			}
		},
		showExpiredSessionDialog() {
			// Don't show dialog until session check is complete
			if (!this.sessionInitialized) {
				return false;
			}

			return this.session && !this.session.loggedIn &&
				this.$route.path !== '/' &&
				this.$route.path !== '/signin' &&
				this.$route.path !== '/login' &&
				this.$route.path !== '/signup' &&
				this.$route.path !== '/invite' &&
				this.$route.path !== '/reset-password' &&
				this.$route.path !== '/docsignin';
		},
	},
	watch: {
		$route(to, from) {
			this.setupSupportChat();
			this.getFeatureStates();
		},
	},
	methods: {
		clearStatus() {
			this.status = '';
			this.statusText = '';
		},
		handleToastClick() {
			if (this.status === 'fail' && this.statusText.includes('Klaviyo integration')) {
				this.$router.push('/loyalty/integrations');
			}
		},
		async initializePromptCaching() {
			try {
				// Initialize caching for prompt context and metrics data
				const response = await fetch(`${URL_DOMAIN}/initialize-prompt-cache`, {
					method: 'POST',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Content-Type': 'application/json',
					}
				});

				if (!response.ok) {
					console.error('Failed to initialize prompt caching:', response.statusText);
				} else {
					console.log('Prompt context caching initialized');
				}
			} catch (error) {
				console.error('Error initializing prompt caching:', error);
			}
		},
		async initializeSegmentGeneration() {
			try {
				const response = await fetch(`${URL_DOMAIN}/organization-segment/klaviyo-sync-progress`, {
					method: 'GET',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Content-Type': 'application/json',
					}
				});

				if (!response.ok) {
					throw new Error(`HTTP error! status: ${response.status}`);
				}

				const data = await response.json();
				const klaviyoSyncProgress = data.progress;

				if (klaviyoSyncProgress === -1) {
					this.status = 'fail';
					this.statusText = 'Klaviyo integration is not set up correctly. Click to configure.';
					return;
				}

				// Only generate segments and start interval if progress is not 100%
				if (klaviyoSyncProgress !== 100) {
					await this.generateSegments();

					// Set up interval to check progress and generate segments every minute
					segmentGenerationInterval = setInterval(async () => {
						// Check Klaviyo sync progress
						const progressResponse = await fetch(`${URL_DOMAIN}/organization-segment/klaviyo-sync-progress`, {
							method: 'GET',
							headers: {
								'Authorization': `Bearer ${localStorage.getItem('token')}`,
								'Content-Type': 'application/json',
							}
						});

						if (progressResponse.ok) {
							const progressData = await progressResponse.json();
							if (progressData.progress === -1) {
								this.status = 'fail';
								this.statusText = 'Klaviyo integration is not set up correctly. Click to configure.';
								if (segmentGenerationInterval) {
									clearInterval(segmentGenerationInterval);
								}
								return;
							}
							if (progressData.progress === 100) {
								// Clear interval if sync is complete
								if (segmentGenerationInterval) {
									clearInterval(segmentGenerationInterval);
								}
								return;
							}
						}

						// Generate segments if sync is not complete
						await this.generateSegments();
					}, 60000); // 60000ms = 1 minute
				}
			} catch (error) {
				console.error("Error checking Klaviyo sync progress:", error);
			}
		},

		async generateSegments() {
			// Prevent multiple simultaneous generations
			if (this.generatingSegments) return;

			try {
				this.generatingSegments = true;
				const response = await fetch(`${URL_DOMAIN}/generate-organization-segments`, {
					method: 'POST',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Content-Type': 'application/json',
					}
				});

				if (!response.ok) {
					console.error("Failed to generate segments:", response.statusText);
				}
			} catch (error) {
				console.error("Error generating segments:", error);
			} finally {
				this.generatingSegments = false;
			}
		},

		checkMobileView() {
			this.isMobileView = window.innerWidth < 768; // Adjust this breakpoint as needed
		},
		hideBanner() {
			console.log("Clicked");
			this.bannerDisplay = 'none';
		},
		async shouldShowSupport() {
			//If on localhost, don't show support banner
			if (window.location.hostname.includes('localhost')) {
				return false;
			}

			const userInfo = JSON.parse(localStorage.getItem('userInfo'));
			if(userInfo?.roles?.includes('raleon-admin')) {
				const response = await fetch(`${URL_DOMAIN}/admin/organizations`, {
				method: 'GET',
				headers: {
					'Content-Type': 'application/json',
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
				}
				});
				const jsonresponse = await response.json();
				const organizationIds = jsonresponse.map(org => org.id);
				console.log("Response from plans list:", organizationIds);
				const userOrgId = userInfo.organizationId;
				console.log("User organization id:", userOrgId);
				if (userOrgId && organizationIds.includes(userOrgId)) {
					console.log("User is in a production org");
					return true;
				}
				else {
					console.log("User is not in a production org");
					return false;
				}
			};
			console.log("User is not in a production org");
			return false;
		},
		async getFeatureStates() {
			const response = await fetch(`${URL_DOMAIN}/features`, {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
			}
			});
			const jsonresponse = await response.json();

			localStorage.setItem('newPlanid', jsonresponse.plan?.id);
			const newPlanId = localStorage.getItem('newPlanid') || null;
			const lastPlanId = localStorage.getItem('lastPlanId') || null;

			const planChanged = newPlanId != lastPlanId;

			localStorage.setItem('lastPlanId', jsonresponse.plan?.id);
			localStorage.setItem('featureStates', JSON.stringify(jsonresponse));

			if (planChanged) {
				window.location.reload();
			}
		},
		setupSupportChat() {
			if (localStorage.getItem('token')) {
				//TODO CRISP
				Crisp.configure('a38d7743-6c4f-4aca-9fbd-ba74e4239e12', {
					autoload: true
				})

				Crisp.user.setEmail(localStorage.getItem('email'));
				Crisp.user.setNickname(localStorage.getItem('firstName') + ' ' + localStorage.getItem('lastName'));
			}
		},
	}
}
</script>


<style scoped>
#app-container {
	position: fixed;
	width: 100vw;
	height: 100vh; /* Fallback in cases dvh isn't working */
	height: 100dvh;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;

	font-family: 'Inter', sans-serif;
}

#app-area {
	position: fixed;
	height: 100vh;
	width: 100vw;
	z-index: 1;
}

#background-logo {
	position: fixed;
	bottom: 0;
	right: 0;
	z-index: 0;
}

#app-area>content {
	position: relative;
	overflow: auto;
	width: 100%;
	height: 100%;
}

@media all and (max-width: 640px) {
	.content-without-sidebar {
		margin-top: 50px;
		margin-left: 10px;
		margin-right: 10px;
	}

	#app-area>content {
		height: calc(100% - 100px);
		/* Subtract the 100px top margin */
		width: calc(100% - 20px);
	}
}

.support-banner {
	position: fixed;
	top: 0;
	width: 100%;
	height: 50px;
	background-color: #4B5563;  /* Change the color as needed */
	color: white;
	text-align: center;
	line-height: 50px;  /* Vertically center the text */
	z-index: 1000;  /* Make sure it's on top */
}
</style>
