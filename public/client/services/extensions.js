import * as Utils from '../utils/utils.js';


export async function getExtenstions() {
	try {
		const response = await fetch(`${Utils.URL_DOMAIN}/extensions`, {
			method: 'GET',
			withCredentials: true,
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
			},
			credentials: 'omit'
		});
		return await response.json();
	} catch (error) {
		console.error('Error fetching extensions:', error);
	}
}

export async function getFreeGift() {
	const response = await fetch(`${Utils.URL_DOMAIN}/extensions/free-gift`, {
		method: 'GET',
		withCredentials: true,
		credentials: 'omit',
		headers: {
			'Authorization': `Bearer ${localStorage.getItem('token')}`,
			'Access-Control-Allow-Origin': '*',
			'Content-Type': 'application/json'
		},
	});
	return await response.json();
}

export async function createExtension(endpoint, body) {
	const response = await fetch(`${Utils.URL_DOMAIN}${endpoint}`, {
		method: 'POST',
		withCredentials: true,
		credentials: 'omit',
		headers: {
			'Authorization': `Bearer ${localStorage.getItem('token')}`,
			'Access-Control-Allow-Origin': '*',
			'Content-Type': 'application/json'
		},
		body
	});
	return await response.json();
}

export async function deleteExtension(extension) {
	const response = await fetch(`${Utils.URL_DOMAIN}${extension.endpoint}`, {
		method: 'DELETE',
		withCredentials: true,
		credentials: 'omit',
		headers: {
			'Authorization': `Bearer ${localStorage.getItem('token')}`,
			'Access-Control-Allow-Origin': '*',
			'Content-Type': 'application/json'
		},
		body: JSON.stringify({
			externalId: extension.externalId,
			extensionId: extension.extensionId,
			functionId: extension.functionId
		})
	});
	return await response.json();
}

export async function getProducts() {
	const response = await fetch(`${Utils.URL_DOMAIN}/shop-products`, {
		method: 'GET',
		credentials: 'omit',
		mode: 'cors',
		headers: {
			'Content-Type': 'application',
			Authorization: `Bearer ${localStorage.getItem('token')}`,
		},
	});
	return await response.json();
}
