import * as Utils from '../../client-old/utils/Utils';
const URL_DOMAIN = Utils.URL_DOMAIN;
let primaryCurrencyCache = null;

export async function formatNumberWithCurrency(num) {
	try {
		if (!primaryCurrencyCache) {
			await getPrimaryCurrency();
		}

		if(primaryCurrencyCache) {
			return `${primaryCurrencyCache.prefix}${num}${primaryCurrencyCache.postfix}`;
		}
	}
	catch (error) {
		console.log('Primary Currency Failure', error);
	}
	return num;
}

export function replaceCurrencyTagsSync(text) {
	let updatedText = text;
    if (!primaryCurrencyCache) {
		getPrimaryCurrency();

		// If primaryCurrencyCache is still null, return the original text without currency tags
		if (text) {
			updatedText = text
				.replace(/<currencyPrefix>/g, '')
				.replace(/<currencyPostfix>/g, '');
		}

		return updatedText;
    }

    // Replace <currencyPrefix> and <currencyPostfix> tags with actual values
	if (text) {
		updatedText = text
        	.replace(/<currencyPrefix>/g, primaryCurrencyCache.prefix || '')
        	.replace(/<currencyPostfix>/g, primaryCurrencyCache.postfix || '');
	}


    return updatedText;
}

export async function replaceCurrencyTags(text) {
    if (!primaryCurrencyCache) {
		await getPrimaryCurrency();
    }

    // Replace <currencyPrefix> and <currencyPostfix> tags with actual values
    const updatedText = text
        .replace(/<currencyPrefix>/g, primaryCurrencyCache.prefix || '')
        .replace(/<currencyPostfix>/g, primaryCurrencyCache.postfix || '');

    return updatedText;
}

async function getPrimaryCurrency() {
	try{
		const response = await fetch(`${URL_DOMAIN}/organization/primary-currency`, {
			method: 'GET',
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
			}
		});
		let jsonresponse = await response.json();
		primaryCurrencyCache = jsonresponse;
	} catch (error) {
		console.log("Can't get Primary Currency: " + JSON.stringify(error));
		primaryCurrencyCache = null;
	}

}

export async function returnPrimaryCurrency() {
	if (!primaryCurrencyCache) {
		await getPrimaryCurrency();
	}
	return primaryCurrencyCache;
}

export function clearPrimaryCurrencyCache() {
    primaryCurrencyCache = null;
}


export async function replaceDollarWithCurrency(text) {
    if (!primaryCurrencyCache) {
        await getPrimaryCurrency();
    }

    // Regular expression to match a dollar sign followed by any number
    const dollarRegex = /\$(\d+)/g;

    return text.replace(dollarRegex, (match, number) => {
        return `${primaryCurrencyCache.prefix || ''}${number}${primaryCurrencyCache.postfix || ''}`;
    });
}

export function convertCurrencyPlaceholdersToValues(text) {

	if (!primaryCurrencyCache) {
        return text;
    }
	const currencyPrefix = primaryCurrencyCache.prefix || "$";
	const currencySuffix = primaryCurrencyCache.suffix || "";

	return text?.replace?.(/\{{1,2}\s*(?:currency|\$)(?:_val|_value|_template)?\s*(?::\s*([^}]+)?)?\s*}{1,2}/gi, (match, amount) =>
		`${currencyPrefix}${(amount || '')}${currencySuffix}`
	);
}
