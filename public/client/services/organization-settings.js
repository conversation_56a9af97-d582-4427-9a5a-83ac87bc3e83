import * as Utils from '../../client-old/utils/Utils';
const URL_DOMAIN = Utils.URL_DOMAIN;

export async function getOrganizationSetting(key) {
	const response = await fetch(`${URL_DOMAIN}/organizations/organization-settings/${key}`, {
		method: 'GET',
		withCreditentials: true,
		credentials: 'omit',
		headers: {
			'Authorization': `Bearer ${localStorage.getItem('token')}`,
			'Access-Control-Allow-Origin': '*',
			'Content-Type': 'application/json'
		}
	});
	let jsonresponse = await response.json();

	if (response.ok) {
		return jsonresponse?.value;
	}
	return null;
}

export async function updateOrganizationSetting(key, value) {
	const response = await fetch(`${URL_DOMAIN}/organizations/organization-settings/upsert`, {
		method: 'POST',
		withCreditentials: true,
		credentials: 'omit',
		headers: {
			'Authorization': `Bearer ${localStorage.getItem('token')}`,
			'Access-Control-Allow-Origin': '*',
			'Content-Type': 'application/json'
		},
		body: JSON.stringify({
			key,
			value
		})
	});
	let jsonresponse = await response.json();

	if (response.ok) {
		return jsonresponse;
	}
	return null;
}
