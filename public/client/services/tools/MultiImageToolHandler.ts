import { BaseToolHandler } from '../toolHandlerService';
import { Emitter } from 'mitt';

interface ImageDetail {
  url: string;
  name?: string;
  loaded?: boolean; // This will be managed by the rendering component
}

export class MultiImageToolHandler extends BaseToolHandler {
  public readonly tag = 'multiimage'; // Unique tag for this tool

  constructor(emitter: Emitter<any>) {
    super(emitter);
    this.register(); // Explicitly register after tag is initialized
  }

  onStart(placeholderId: string, initialContent: string = ''): void {
    this.placeholderId = placeholderId;
    this.streamingContent = initialContent;
    console.log(`${this.tag} tool started. PlaceholderId: ${this.placeholderId}`);
  }

  onContent(contentChunk: string): void {
    if (!this.placeholderId) return;

    // Log chunk contents for debugging
    console.log(`${this.tag} tool received chunk: "${contentChunk}"`);

    // Accumulate content
    this.streamingContent += contentChunk;

    // Check if we can detect URLs in this specific chunk
    const urlPattern = /https?:\/\/[^\s"',]+/g;
    const urls = contentChunk.match(urlPattern);
    if (urls && urls.length > 0) {
      console.log(`${this.tag} tool found URLs in this chunk:`, urls);
    }

    // Log total accumulated content length
    console.log(`${this.tag} tool: Accumulated content length: ${this.streamingContent.length}`);
  }

  onEnd(): void {
    if (!this.placeholderId) {
      console.warn(`${this.tag} handler received end signal but was not started or already ended.`);
      return;
    }

    // Get the raw content and make a simplified version for debugging
    const rawContent = this.streamingContent.trim();
    const debugContent = rawContent.length > 100 ?
      rawContent.substring(0, 50) + '...' + rawContent.substring(rawContent.length - 50) :
      rawContent;

    console.log(`${this.tag} tool: Processing raw content (${rawContent.length} chars):`, debugContent);

    // Initialize empty images array
    let images: ImageDetail[] = [];

    // Enhanced extraction logic to find URLs in the content
    try {
      // First try to parse the content as JSON directly - could be a valid JSON array
      try {
        // Clean up the content to make it more JSON-friendly
        let cleanedContent = rawContent.trim();

        // Check if it starts with [ and ends with ] for an array
        if (cleanedContent.startsWith('[') && cleanedContent.endsWith(']')) {
          console.log(`${this.tag} tool: Content appears to be a JSON array, attempting to parse`);
          const parsedData = JSON.parse(cleanedContent);

          if (Array.isArray(parsedData) && parsedData.length > 0) {
            images = parsedData.map((img, index) => {
              // Validate each item
              const url = typeof img === 'object' && img !== null ? (img.url || '') :
                           typeof img === 'string' ? img : '';
              const name = typeof img === 'object' && img !== null && img.name ?
                           img.name : `Image ${index + 1}`;

              console.log(`${this.tag} tool: JSON direct parse - Image ${index+1}: URL=${url}, Name=${name}`);

              return {
                url: url,
                name: name,
                loaded: false
              };
            }).filter(img => !!img.url);

            console.log(`${this.tag} tool: Successfully parsed ${images.length} images directly from JSON array`);
          }
        }
      } catch (directJsonError) {
        console.log(`${this.tag} tool: Direct JSON parsing failed, trying regex approaches`, directJsonError);
      }

      // If direct JSON parsing didn't work, try regex approaches
      if (images.length === 0) {
        // Look for strings that look like cloudfront.net URLs
        const cloudFrontPattern = /https:\/\/[^\s"',]+\.cloudfront\.net\/[^\s"',]+/g;
        const cloudFrontMatches = rawContent.match(cloudFrontPattern);

        if (cloudFrontMatches && cloudFrontMatches.length > 0) {
          console.log(`${this.tag} tool: Found ${cloudFrontMatches.length} cloudfront URLs via regex:`);
          cloudFrontMatches.forEach((url, i) => console.log(`  ${i+1}: ${url}`));

          // Create an image object for each URL
          images = cloudFrontMatches.map((url, index) => {
            // Try to extract a name from the URL
            const nameParts = url.split('/');
            const fileName = nameParts[nameParts.length - 1];
            const nameMatch = fileName.match(/([^\/\.]+)\.(?:png|jpg|jpeg|gif|webp)$/i);
            const name = nameMatch ? nameMatch[1].replace(/-/g, ' ') : `Image ${index + 1}`;

            console.log(`${this.tag} tool: CloudFront image ${index+1}: URL=${url}, Name=${name}`);

            return {
              url: url,
              name: name,
              loaded: false
            };
          });

          console.log(`${this.tag} tool: Created ${images.length} image objects from CloudFront URLs`);
        } else {
          console.log(`${this.tag} tool: No cloudfront URLs found via regex`);
        }

        // If no cloudfront URLs found, try looking for any image URLs
        if (images.length === 0) {
          const imgPattern = /https:\/\/[^\s"',]+\.(jpg|jpeg|png|gif|webp)/gi;
          const imgMatches = rawContent.match(imgPattern);

          if (imgMatches && imgMatches.length > 0) {
            console.log(`${this.tag} tool: Found ${imgMatches.length} generic image URLs via regex:`);
            imgMatches.forEach((url, i) => console.log(`  ${i+1}: ${url}`));

            images = imgMatches.map((url, index) => {
              // Try to extract a name from the URL
              const nameParts = url.split('/');
              const fileName = nameParts[nameParts.length - 1];
              const nameMatch = fileName.match(/([^\/\.]+)\.(?:png|jpg|jpeg|gif|webp)$/i);
              const name = nameMatch ? nameMatch[1].replace(/-/g, ' ') : `Image ${index + 1}`;

              console.log(`${this.tag} tool: Generic image ${index+1}: URL=${url}, Name=${name}`);

              return {
                url: url,
                name: name,
                loaded: false
              };
            });

            console.log(`${this.tag} tool: Created ${images.length} image objects from generic image URLs`);
          }
        }

        // If still no images found, try to extract url:value pairs from JSON-like structures
        if (images.length === 0) {
          // More aggressive regex to find URL patterns in the content
          const urlPattern = /"(?:url|src)"\s*:\s*"([^"]+\.(jpg|jpeg|png|gif|webp))"/gi;
          let urlMatch;
          const urls: string[] = [];

          while ((urlMatch = urlPattern.exec(rawContent)) !== null) {
            if (urlMatch[1]) urls.push(urlMatch[1]);
          }

          if (urls.length > 0) {
            console.log(`${this.tag} tool: Found ${urls.length} URLs from json properties:`);
            urls.forEach((url, i) => console.log(`  ${i+1}: ${url}`));

            // Try to find associated names
            const namePattern = /"name"\s*:\s*"([^"]+)"/g;
            let nameMatch;
            const names: string[] = [];

            while ((nameMatch = namePattern.exec(rawContent)) !== null) {
              if (nameMatch[1]) names.push(nameMatch[1]);
            }

            images = urls.map((url, index) => {
              const name = index < names.length ? names[index] : `Image ${index + 1}`;
              console.log(`${this.tag} tool: JSON property image ${index+1}: URL=${url}, Name=${name}`);

              return {
                url: url,
                name: name,
                loaded: false
              };
            });

            console.log(`${this.tag} tool: Created ${images.length} image objects from JSON properties`);
          }
        }

        // Try to find JSON array in the content using a more flexible regex
        if (images.length === 0) {
          try {
            // Look for something that looks like a JSON array of objects
            const arrayRegex = /\[\s*\{[^\[\]]*\}\s*(?:,\s*\{[^\[\]]*\}\s*)*\]/g;
            const matches = rawContent.match(arrayRegex);

            if (matches && matches.length > 0) {
              console.log(`${this.tag} tool: Found ${matches.length} potential JSON arrays`);

              // Try parsing each match
              for (let i = 0; i < matches.length; i++) {
                try {
                  const jsonText = matches[i];
                  console.log(`${this.tag} tool: Attempting to parse JSON array ${i+1}:`,
                    jsonText.length > 100 ? jsonText.substring(0, 50) + '...' + jsonText.substring(jsonText.length - 50) : jsonText);

                  const parsedData = JSON.parse(jsonText);
                  if (Array.isArray(parsedData) && parsedData.length > 0) {
                    // Check if this array contains image-like objects
                    const hasImageUrls = parsedData.some(item => {
                      return typeof item === 'object' && item !== null &&
                             typeof item.url === 'string' && item.url.match(/\.(jpg|jpeg|png|gif|webp)/i);
                    });

                    if (hasImageUrls) {
                      images = parsedData.map((img, index) => ({
                        url: img.url || '',
                        name: img.name || `Image ${index + 1}`,
                        loaded: false
                      })).filter(img => !!img.url);

                      console.log(`${this.tag} tool: Parsed ${images.length} images from JSON array ${i+1}`);
                      break; // Exit loop if we found valid images
                    } else {
                      console.log(`${this.tag} tool: Array ${i+1} doesn't contain image URLs, skipping`);
                    }
                  }
                } catch (parseError) {
                  console.log(`${this.tag} tool: Failed to parse array ${i+1}:`, parseError);
                }
              }
            } else {
              console.log(`${this.tag} tool: No JSON arrays found with flexible regex`);
            }
          } catch (jsonError) {
            console.error(`${this.tag} tool: Error parsing JSON from content:`, jsonError);
          }
        }
      }
    } catch (extractionError) {
      console.error(`${this.tag} tool: Error extracting images:`, extractionError);
    }

    // Add mock images for debugging if no images were found
    if (images.length === 0 && process.env.NODE_ENV === 'development') {
      console.warn(`${this.tag} tool: No images found in content. Adding placeholder images for debugging.`);

      // This is only for development/debugging - these shouldn't appear in production
      images = [
        {
          url: 'https://via.placeholder.com/400x300/a855f7/ffffff?text=Test+Image+1',
          name: 'Test Image 1',
          loaded: false
        },
        {
          url: 'https://via.placeholder.com/400x300/a855f7/ffffff?text=Test+Image+2',
          name: 'Test Image 2',
          loaded: false
        }
      ];
    }

    // If we found images, emit the update event
    if (images.length > 0) {
      console.log(`${this.tag} tool: Emitting chat:update-segment for ${images.length} images with placeholderId: ${this.placeholderId}`);

      // Set up a direct emit to the Chat.vue component
      this.emitter.emit('chat:update-segment', {
        type: 'multiimage',
        sender: 'ai',
        images: images,
        timestamp: new Date(),
        placeholderId: this.placeholderId,
      });

      // Also emit as a text message to ensure it gets displayed
      this.emitter.emit('chat:update-segment', {
        type: 'text',
        sender: 'ai',
        content: `I've found these images:\n` +
          images.map((img, idx) => `${idx+1}. ${img.name || `Image ${idx+1}`}`).join('\n'),
        timestamp: new Date(),
      });

      console.log(`${this.tag} tool: Successfully emitted events with ${images.length} images`);
    } else {
      console.error(`${this.tag} tool: Could not extract any valid images from content`);

      // Emit fallback - empty images array with error flag
      this.emitter.emit('chat:update-segment', {
        type: 'multiimage',
        sender: 'ai',
        images: [{
          url: '',
          name: 'ERROR: No valid images found',
          loaded: true,
          error: true
        }],
        timestamp: new Date(),
        placeholderId: this.placeholderId,
      });

      // Also emit the raw content as a text message for debugging
      this.emitter.emit('chat:update-segment', {
        type: 'text',
        sender: 'ai',
        content: `Debug: Raw multiimage content length: ${rawContent.length} chars. First 100 chars: ${rawContent.substring(0, 100)}...`,
        timestamp: new Date(),
      });
    }

    // Clean up and complete
    this.emitter.emit(`${this.tag}:complete`, {
      placeholderId: this.placeholderId,
      finalContent: images,
      success: images.length > 0
    });

    // Reset state
    this.placeholderId = null;
    this.streamingContent = '';
  }
}
