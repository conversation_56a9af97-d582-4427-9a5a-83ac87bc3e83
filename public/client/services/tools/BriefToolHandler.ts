import { <PERSON><PERSON>oolHand<PERSON> } from '../toolHandlerService';
import { Emitter } from 'mitt';

// Define specific event types if desired for better type safety in the future
// type BriefEvents = {
//   'brief:streaming': { placeholderId: string; content: string };
//   'brief:complete': { placeholderId: string; finalContent: string };
//   'chat:update-segment': { segmentId: string; content: string; status?: string };
//   'artifact:open': { view: string; data?: any };
// };

export class BriefToolHandler extends BaseToolHandler {
  public readonly tag = 'brief'; // IMPORTANT: Ensure this tag matches the identifier used in ChatArtifact.ts.vue

  constructor(emitter: Emitter<any>) { // Or Emitter<BriefEvents>
    super(emitter);
    this.register(); // Explicitly register after tag is initialized
  }

  onStart(placeholderId: string, initialContent: string = ''): void {
    this.placeholderId = placeholderId;
    this.streamingContent = initialContent;
    this.emitter.emit(`chat:update-segment`, {
      segmentId: this.placeholderId,
      content: this.streamingContent,
      status: 'streaming'
    });
    this.emitter.emit(`${this.tag}:streaming`, { placeholderId: this.placeholderId, content: this.streamingContent });
    // console.log(`${this.tag} tool started with placeholderId: ${this.placeholderId}`);
  }

  onContent(contentChunk: string): void {
    if (!this.placeholderId) {
      // console.warn(`${this.tag} handler received content but was not started or already ended.`);
      return;
    }
    this.streamingContent += contentChunk;
    this.emitter.emit(`${this.tag}:streaming`, { placeholderId: this.placeholderId, content: this.streamingContent });
    this.emitter.emit(`chat:update-segment`, {
      segmentId: this.placeholderId,
      content: this.streamingContent,
      status: 'streaming'
    });
  }

  onEnd(): void {
    if (!this.placeholderId) {
      // console.warn(`${this.tag} handler received end signal but was not started or already ended.`);
      return;
    }

    // Clean up the content to ensure we have valid JSON
    let cleanedContent = this.streamingContent;

    // Extract just the JSON part if there's extra text
    const firstBrace = cleanedContent.indexOf('{');
    const lastBrace = cleanedContent.lastIndexOf('}');

    if (firstBrace !== -1 && lastBrace !== -1 && firstBrace < lastBrace) {
      // There's a valid JSON structure, extract it
      if (firstBrace > 0) {
        console.log(`BriefToolHandler: Found extra text before JSON: "${cleanedContent.substring(0, firstBrace)}"`);
      }
      cleanedContent = cleanedContent.substring(firstBrace, lastBrace + 1);
      console.log('BriefToolHandler: Cleaned brief JSON content');
    } else {
      console.log('BriefToolHandler: No valid JSON structure found in brief content');
    }

    // Emit the complete event with cleaned content
    this.emitter.emit(`${this.tag}:complete`, {
      placeholderId: this.placeholderId,
      finalContent: cleanedContent
    });

    // Update the chat segment
    this.emitter.emit(`chat:update-segment`, {
      toolTag: this.tag,
      segmentId: this.placeholderId,
      content: cleanedContent,
      status: 'complete'
    });

    // Also emit artifact:open to ensure the brief tab is shown
    this.emitter.emit('artifact:open', {
      view: 'brief',
      autoOpen: true
    });

    this.placeholderId = null;
  }
}
