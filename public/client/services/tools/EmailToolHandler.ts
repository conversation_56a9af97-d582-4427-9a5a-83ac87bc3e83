import { <PERSON><PERSON>ool<PERSON>and<PERSON> } from '../toolHandlerService';
import { Emitter } from 'mitt';

export class EmailToolHandler extends BaseToolHandler {
  public readonly tag = 'email'; // IMPORTANT: Ensure this tag matches the identifier used in ChatArtifact.ts.vue

  constructor(emitter: Emitter<any>) {
    super(emitter);
    this.register(); // Explicitly register after tag is initialized
  }

  onStart(placeholderId: string, initialContent: string = ''): void {
    this.placeholderId = placeholderId;
    this.streamingContent = initialContent;
    this.emitter.emit(`chat:update-segment`, {
      segmentId: this.placeholderId,
      content: this.streamingContent,
      status: 'streaming'
    });
    this.emitter.emit(`${this.tag}:streaming`, { placeholderId: this.placeholderId, content: this.streamingContent });
  }

  onContent(contentChunk: string): void {
    if (!this.placeholderId) return;
    this.streamingContent += contentChunk;
    this.emitter.emit(`${this.tag}:streaming`, { placeholderId: this.placeholderId, content: this.streamingContent });
    this.emitter.emit(`chat:update-segment`, {
      segmentId: this.placeholderId,
      content: this.streamingContent,
      status: 'streaming'
    });
  }

  onEnd(): void {
    if (!this.placeholderId) return;

    console.log(`EmailToolHandler: End of email tool content, placeholderId=${this.placeholderId}`);

    // Emit both the tool-specific event and the general update event
    this.emitter.emit(`${this.tag}:complete`, {
      placeholderId: this.placeholderId,
      finalContent: this.streamingContent
    });

    // Update the loading state via chat:update-segment
    this.emitter.emit(`chat:update-segment`, {
      toolTag: this.tag,
      segmentId: this.placeholderId,
      content: this.streamingContent,
      status: 'complete'
    });

    // Also emit artifact:open to ensure the email tab is shown
    this.emitter.emit('artifact:open', {
      view: 'email',
      autoOpen: true
    });

    this.placeholderId = null;
  }
}
