import { BaseTool<PERSON>andler } from '../toolHandlerService';
import { Emitter } from 'mitt';

export class ImageToolHandler extends BaseToolHandler {
  public readonly tag = 'image'; // Unique tag for this tool

  constructor(emitter: Emitter<any>) {
    super(emitter);
    this.register(); // Explicitly register after tag is initialized
  }

  onStart(placeholderId: string, initialContent: string = ''): void {
    this.placeholderId = placeholderId; // This ID might be used by ChatArtifact to track the tool call itself
    this.streamingContent = initialContent;
    // No initial chat segment is created by this handler directly.
    // The segment will be created when onEnd is called with the image data.
    // console.log(`${this.tag} tool started. PlaceholderId: ${this.placeholderId}`);
  }

  onContent(contentChunk: string): void {
    if (!this.placeholderId) return;
    // Image content is typically not streamed character by character.
    // It's usually a single block of JSON or a URL.
    // We'll accumulate it here and process fully in onEnd.
    this.streamingContent += contentChunk;
    // console.log(`${this.tag} tool received content chunk. Current accumulated: ${this.streamingContent.substring(0,50)}...`);
  }

  onEnd(): void {
    if (!this.placeholderId) {
      // console.warn(`${this.tag} handler received end signal but was not started or already ended.`);
      return;
    }

    let imageUrl = '';
    let imageName = 'Image'; // Default name
    const rawContent = this.streamingContent.trim();

    try {
      if (rawContent.startsWith('{') && rawContent.endsWith('}')) {
        const imageData = JSON.parse(rawContent);
        imageUrl = imageData.url || '';
        imageName = imageData.name || 'Image';
      } else {
        // Assume legacy format: content is just the URL
        const urlRegex = /(https?:\/\/[^\s<>"']+)/;
        const urlMatch = rawContent.match(urlRegex);
        if (urlMatch && urlMatch[1]) {
            imageUrl = urlMatch[1];
        } else {
             // If no URL is found in legacy format, try to use rawContent as URL directly
            if (rawContent.startsWith('http')) { // Basic check
                imageUrl = rawContent;
            }
        }
      }
    } catch (e) {
      console.error(`${this.tag} tool: Error parsing content:`, e, "Raw content:", rawContent);
      // Fallback: try to extract URL with regex if JSON parsing failed
        const urlRegex = /(https?:\/\/[^\s<>"']+)/;
        const urlMatch = rawContent.match(urlRegex);
        if (urlMatch && urlMatch[1]) {
            imageUrl = urlMatch[1];
        } else if (rawContent.startsWith('http')) {
             imageUrl = rawContent;
        }
    }

    if (imageUrl) {
      this.emitter.emit('chat:update-segment', {
        // segmentId will be generated by Chat.ts.vue when it creates the visual segment
        // placeholderId here refers to the tool call invocation, not the visual chat segment ID
        type: 'image', // New segment type
        sender: 'ai', // Assuming images come from AI
        imageUrl: imageUrl,
        imageName: imageName,
        timestamp: new Date(),
        // placeholderId: this.placeholderId, // Include if ChatArtifact needs to correlate
      });
      // console.log(`${this.tag} tool ended. Emitted chat:update-segment for image: ${imageName}`);
    } else {
      console.error(`${this.tag} tool: Could not extract a valid image URL from content:`, rawContent);
      this.emitter.emit('chat:update-segment', {
        type: 'text',
        sender: 'ai',
        content: `[Error: ${this.tag} tool failed to process image content.]`,
        timestamp: new Date(),
        // placeholderId: this.placeholderId,
      });
    }

    this.emitter.emit(`${this.tag}:complete`, { placeholderId: this.placeholderId, finalContent: {imageUrl, imageName} });
    this.placeholderId = null;
    this.streamingContent = '';
  }
}
