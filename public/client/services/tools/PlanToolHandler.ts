import { <PERSON><PERSON>ool<PERSON>and<PERSON> } from '../toolHandlerService';
import { Emitter } from 'mitt';

export class PlanToolHandler extends BaseToolHandler {
  public readonly tag = 'plan'; // IMPORTANT: Ensure this tag matches the identifier used in ChatArtifact.ts.vue

  constructor(emitter: Emitter<any>) {
    super(emitter);
    this.register(); // Explicitly register after tag is initialized
  }

  onStart(placeholderId: string, initialContent: string = ''): void {
    this.placeholderId = placeholderId;
    this.streamingContent = initialContent;
    this.emitter.emit(`chat:update-segment`, {
      segmentId: this.placeholderId,
      content: this.streamingContent,
      status: 'streaming'
    });
    this.emitter.emit(`${this.tag}:streaming`, { placeholderId: this.placeholderId, content: this.streamingContent });
  }

  onContent(contentChunk: string): void {
    if (!this.placeholderId) return;
    this.streamingContent += contentChunk;
    this.emitter.emit(`${this.tag}:streaming`, { placeholderId: this.placeholderId, content: this.streamingContent });
    this.emitter.emit(`chat:update-segment`, {
      segmentId: this.placeholderId,
      content: this.streamingContent,
      status: 'streaming'
    });
  }

  onEnd(): void {
    if (!this.placeholderId) return;
    this.emitter.emit(`${this.tag}:complete`, { placeholderId: this.placeholderId, finalContent: this.streamingContent });
    this.emitter.emit(`chat:update-segment`, {
      segmentId: this.placeholderId,
      content: this.streamingContent,
      status: 'complete'
    });
    // Example: If a plan tool opens an artifact
    // this.emitter.emit('artifact:open', { view: 'planViewer', data: { steps: this.streamingContent.split('\n') } });
    this.placeholderId = null;
  }
}
