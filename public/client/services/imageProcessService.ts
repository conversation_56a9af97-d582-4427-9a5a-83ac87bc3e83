// Extract the image processing logic into a separate service

// Create a new file: src/services/imageProcessingService.ts
export class ImageProcessingService {
	/**
	 * Extracts an image URL from content that may contain various formats
	 * @param rawContent - The raw content that may contain an image URL
	 * @returns Extracted image URL or empty string if none found
	 */
	static extractImageUrl(rawContent: string): string {
	  if (!rawContent || typeof rawContent !== 'string') {
		return '';
	  }

	  console.log("Raw image content before processing:", rawContent);

	  // Clean up the content - remove any newlines and extra whitespace
	  rawContent = rawContent.replace(/\n/g, '').trim();

	  // Try to extract a URL using regex
	  const urlMatch = rawContent.match(/(https?:\/\/[^\s<>"]+)/);
	  if (urlMatch) {
		console.log("Found URL using regex:", urlMatch[1]);
		return urlMatch[1];
	  }

	  // If no URL found, try to extract content between <image> tags
	  const tagMatch = rawContent.match(/<image>([\s\S]*?)<\/image>/);
	  if (tagMatch && tagMatch[1]) {
		// Clean up the content inside the tags
		const tagContent = tagMatch[1].replace(/\n/g, '').trim();

		// Try to find a URL in the tag content
		const innerUrlMatch = tagContent.match(/(https?:\/\/[^\s<>"]+)/);
		if (innerUrlMatch) {
		  console.log("Found URL inside image tag:", innerUrlMatch[1]);
		  return innerUrlMatch[1];
		} else {
		  console.log("Using content inside image tag as URL:", tagContent);
		  return tagContent;
		}
	  }

	  // If still no URL, clean up the raw content and use it
	  const cleanedContent = rawContent.replace(/<image>/g, '').replace(/<\/image>/g, '');
	  console.log("Using cleaned raw content as URL:", cleanedContent);
	  return cleanedContent;
	}

	/**
	 * Processes raw content to find and extract any inline image tags
	 * @param content - The content that may contain inline image tags
	 * @returns Object with modified content and any extracted image URLs
	 */
	static processInlineImageTags(content: string): {
	  modifiedContent: string;
	  imageUrls: string[];
	  hasImageTags: boolean;
	} {
	  if (!content) {
		return { modifiedContent: '', imageUrls: [], hasImageTags: false };
	  }

	  const imageTagRegex = /<image>([\s\S]*?)<\/image>/g;
	  let modifiedContent = content;
	  const imageUrls: string[] = [];

	  // Process any inline image tags in the text content
	  const imageMatches = [...content.matchAll(imageTagRegex)];
	  const hasImageTags = imageMatches.length > 0;

	  if (hasImageTags) {
		console.log('Found image tags in text:', imageMatches.length);

		// For each image tag found, extract the URL
		for (const match of imageMatches) {
		  let rawContent = match[1]?.trim() || '';
		  const imageUrl = this.extractImageUrl(rawContent);

		  if (imageUrl) {
			imageUrls.push(imageUrl);
			// Remove the image tag from the text content
			modifiedContent = modifiedContent.replace(match[0], '');
		  }
		}
	  }

	  return {
		modifiedContent: modifiedContent.trim(),
		imageUrls,
		hasImageTags
	  };
	}

	/**
	 * Attempts to safely parse JSON from string content
	 * @param content - The string content that may contain JSON
	 * @param jsonrepair - Optional jsonrepair function for repairing malformed JSON
	 * @returns Parsed JSON object or null if parsing fails
	 */
	static parseImageJson(content: string, jsonrepair?: (text: string) => string): any {
	  if (!content) return null;

	  try {
		// Try direct parsing first
		return JSON.parse(content);
	  } catch (e) {
		console.log("Direct JSON parse failed, trying to extract JSON array");

		// Try to extract a valid JSON array
		const arrayStartIndex = content.indexOf('[');
		const arrayEndIndex = content.lastIndexOf(']');

		if (arrayStartIndex !== -1 && arrayEndIndex !== -1 && arrayEndIndex > arrayStartIndex) {
		  try {
			const jsonContent = content.substring(arrayStartIndex, arrayEndIndex + 1);
			return JSON.parse(jsonContent);
		  } catch (arrayError) {
			console.error("Failed to parse extracted array:", arrayError);
		  }
		}

		// Try jsonrepair if available
		if (jsonrepair) {
		  try {
			const repairedJson = jsonrepair(content);
			return JSON.parse(repairedJson);
		  } catch (repairError) {
			console.error("JSON repair failed:", repairError);
		  }
		}

		// If all else fails, try to extract URLs directly
		const urlRegex = /(https?:\/\/[^\s"]+)/g;
		const urls = [...content.matchAll(urlRegex)].map(match => match[0]);

		if (urls.length > 0) {
		  console.log(`Extracted ${urls.length} URLs directly from content`);
		  return urls.map(url => ({ url }));
		}
	  }

	  return null;
	}
  }
