import * as Utils from '../../client-old/utils/Utils';
const URL_DOMAIN = Utils.URL_DOMAIN;
export function loadMetricsFromCache(key) {
	cleanupExpiredCacheItems();
	const cache = JSON.parse(localStorage.getItem('metricsCache'));
	if (!cache || !cache[key]) {
		return;
	}

	const cachedData = cache[key];
	if (isExpired(cachedData)) {
		console.log('isExpired', cachedData);
		delete cache[key];
		localStorage.setItem('metricsCache', JSON.stringify(cache));
		return;
	}

	return cachedData.data;
}

function isExpired(cachedItem) {
	const expirationCheckFunctions = {
		'day': (date) => new Date(date).setDate(new Date(date).getDate() + 1),
		'week': (date) => new Date(date).setDate(new Date(date).getDate() + 7),
		'month': (date) => new Date(date).setMonth(new Date(date).getMonth() + 1),
	};

	if (cachedItem.data.last_run_date && expirationCheckFunctions[cachedItem.data.run_frequency]) {
		const expirationDate = expirationCheckFunctions[cachedItem.data.run_frequency](cachedItem.data.last_run_date);
		console.log('expirationDate', expirationDate);
		return Date.now() > expirationDate;
	} else {
		const oneHour = 60 * 60 * 1000;
		const age = Date.now() - cachedItem.dateSet;
		return age > oneHour;
	}
}

function cleanupExpiredCacheItems() {
	const cache = JSON.parse(localStorage.getItem('metricsCache')) || {};
	let isCacheUpdated = false;

	Object.keys(cache).forEach(key => {
		if (isExpired(cache[key])) {
			delete cache[key];
			isCacheUpdated = true;
		}
	});

	if (isCacheUpdated) {
		localStorage.setItem('metricsCache', JSON.stringify(cache));
	}
}


export function updateMetricsCache(key, dataToCache) {
	const cache = JSON.parse(localStorage.getItem('metricsCache')) || {};
	cache[key] = {
		data: dataToCache,
		dateSet: Date.now()
	};
	localStorage.setItem('metricsCache', JSON.stringify(cache));
}

export async function getMetric(metricName, startDate, endDate, groupBy, calculation, campaignId) {
	if (campaignId == undefined) {
		campaignId = '';
	}
	let metricdata = loadMetricsFromCache(metricName + startDate + endDate + groupBy + calculation + campaignId);
	let campaignfilter = '';
	if (metricdata) {
		return metricdata;
	}
	if (campaignId) {
		campaignfilter = `&campaignId=${campaignId}`;
	}
	let url = `/metric?name=${metricName}&start-date=${startDate}&end-date=${endDate}&group-by=${groupBy}&calculation=${calculation}${campaignfilter}`;
	const response = await fetch(`${URL_DOMAIN}${url}`, {
		method: 'GET',
		credentials: 'omit',
		mode: 'cors',
		headers: {
			'Content-Type': 'application/json',
			Authorization: `Bearer ${localStorage.getItem('token')}`,
			'ngrok-skip-browser-warning': 'true'
		}
	});

	if (!response.ok) {
		throw new Error(`API request failed: ${response.status} ${response.statusText}`);
	}

	let data = await response.json();
	if (data.body.message == 'Internal server error') {
		console.log('Metrics still loading...');
		return 'Metrics still loading...';
	}
	updateMetricsCache(metricName + startDate + endDate + groupBy + calculation + campaignId, data);
	return data;
}
