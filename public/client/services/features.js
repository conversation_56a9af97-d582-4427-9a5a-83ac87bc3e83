import * as Utils from '../../client-old/utils/Utils';
const URL_DOMAIN = Utils.URL_DOMAIN;

export function isFeatureAvailable(featureName) {
	const state = localStorage.getItem('featureStates');
	if (!state) {
		return false;
	}

	const featureStates = JSON.parse(state);
	const featureState = featureStates?.features?.find?.(x => x?.featureId === featureName);

	return featureState && featureState.available;
}

export function isFeatureEnabled(featureName) {
	const state = localStorage.getItem('featureStates');
	if (!state) {
		return false;
	}

	const featureStates = JSON.parse(state);
	const featureState = featureStates?.features?.find?.(x => x?.featureId === featureName);

	return featureState && featureState.available && featureState.enabled;
}

export function isFeatureLive(featureName) {
	const state = localStorage.getItem('featureStates');
	if (!state) {
		return false;
	}

	const featureStates = JSON.parse(state);
	const featureState = featureStates?.features?.find?.(x => x?.featureId === featureName);

	return featureState && featureState.live;
}

export function isFeatureLiveAndEnabled(featureName) {
	const state = localStorage.getItem('featureStates');
	if (!state) {
		return false;
	}

	const featureStates = JSON.parse(state);
	const featureState = featureStates?.features?.find?.(x => x?.featureId === featureName);

	return featureState && featureState.available && featureState.enabled && featureState.live;
}

export async function freeTrialInfo() {
	const response = await fetch(`${URL_DOMAIN}/free-trial-info`, {
		method: 'GET',
		headers: {
			'Content-Type': 'application/json',
			'Authorization': `Bearer ${localStorage.getItem('token')}`,
		}
	});
	const jsonresponse = await response.json();
	return jsonresponse;
}
