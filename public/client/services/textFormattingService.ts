// public/client/services/textFormattingService.ts

/**
 * Service to handle text formatting in chat messages
 */
export class TextFormattingService {
  /**
   * Formats text by converting markdown-like syntax to HTML
   * Currently supports:
   * - **text** for bold text
   * - ## and ### for bold text
   * - ***text*** for smaller, dimmer text
   *
   * @param text The text to format
   * @returns Formatted HTML string
   */
  public static formatText(text?: string): string {
    if (!text) return '';

	//console.log('Formatting text:', text);

    // Process the text in multiple steps
    let formattedText = text;

    // Handle ***text*** for smaller, dimmer text - all instances get consistent styling
    // Process this BEFORE double asterisks to avoid partial matches
    formattedText = formattedText.replace(/\*\*\*(.*?)\*\*\*/g, (match, content) => {
      // Apply consistent loading message styling to all ***text*** content
      return `<div style="font-size: 0.85em; opacity: 0.8; line-height: 2.0; margin: 0.15em 0; text-align: left; font-style: italic; display: block;">${content}</div>`;
    });
    
    // Clean up any potential excessive newlines between consecutive styled blocks
    formattedText = formattedText.replace(/(<div style="font-size:0\.85em.*?<\/div>)\n+(<div style="font-size:0\.85em)/g, '$1$2');
    
    // Then replace **text** with <strong>text</strong> for bold
    formattedText = formattedText.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    
    // Using a different approach - handle each line completely
    formattedText = formattedText.split('\n').map(line => {
      // Check each line for heading patterns
      if (line.match(/^#\s+/)) {
        const headingText = line.replace(/^#\s+/, '');
        return `<span style="display:inline; font-size:1.4em; font-weight:700; color:#4B0082; border-bottom:1px solid #E9D5FF;">${headingText}</span>`;
      }
      
      if (line.match(/^##\s+/)) {
        const headingText = line.replace(/^##\s+/, '');
        return `<span style="display:inline; font-size:1.2em; font-weight:600; color:#6E41FF;">${headingText}</span>`;
      }
      
      if (line.match(/^###\s+/)) {
        const headingText = line.replace(/^###\s+/, '');
        return `<span style="display:inline; font-size:1.1em; font-weight:500; color:#8B5CF6;">${headingText}</span>`;
      }
      
      return line;
    }).join('\n');

    // Process image tags
    formattedText = formattedText.replace(/<imageref>([\s\S]*?)<\/imageref>/g, (match, content) => {
      try {
        const trimmedContent = content.trim();
        console.log('Image content to parse:', trimmedContent);

        // Check if content is JSON-like
        if (trimmedContent.startsWith('{') && trimmedContent.endsWith('}')) {
          interface ImageData {
            name?: string;
            url?: string;
          }

          let imageData: ImageData | null = null;

          // Try several methods to parse the JSON
          try {
            // 1. First try direct JSON parsing
            imageData = JSON.parse(trimmedContent);
            console.log('Successfully parsed with standard JSON.parse');
          } catch (e1) {
            console.warn('Standard JSON.parse failed:', e1);

            try {
              // 2. Try removing any escaped quotes (this might be the issue)
              const unescapedContent = trimmedContent.replace(/\\"/g, '"');
              console.log('Trying unescaped content:', unescapedContent);
              imageData = JSON.parse(unescapedContent);
              console.log('Successfully parsed after unescaping quotes');
            } catch (e2) {
              console.warn('Unescaping quotes failed:', e2);

              try {
                // 3. Try adding quotes to property names
                const quotedProps = trimmedContent.replace(/([{,]\s*)([a-zA-Z0-9_]+)(\s*:)/g, '$1"$2"$3');
                console.log('Trying with quoted props:', quotedProps);
                imageData = JSON.parse(quotedProps);
                console.log('Successfully parsed after adding quotes to props');
              } catch (e3) {
                console.warn('Adding quotes to props failed:', e3);

                try {
                  // 4. Try a more manual approach to extract name and url
                  const nameMatch = trimmedContent.match(/name\s*:\s*["']([^"']*)["']/i);
                  const urlMatch = trimmedContent.match(/url\s*:\s*["']([^"']*)["']/i);

                  if (nameMatch && urlMatch) {
                    console.log('Extracted values manually:', nameMatch[1], urlMatch[1]);
                    imageData = {
                      name: nameMatch[1],
                      url: urlMatch[1]
                    };
                  } else {
                    throw new Error('Failed to manually extract name and url');
                  }
                } catch (e4) {
                  console.error('All parsing methods failed');
                  throw e4;
                }
              }
            }
          }

          // If we found valid image data, use it
          if (imageData && (imageData.name || imageData.url)) {
            const imageName = imageData.name || 'Image';
            const imageUrl = imageData.url || '';

            // Create tooltip with image preview
            if (imageUrl) {
              return `<span class="inline-flex items-center px-2 py-1 bg-purple-100 text-purple-800 rounded-md relative group">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
                </svg>
                ${imageName}
                <div class="absolute hidden group-hover:block bottom-full left-0 p-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50 mb-2 max-w-xs">
                  <img src="${imageUrl}" alt="${imageName}" class="max-w-full max-h-48 rounded" />
                  <div class="text-xs text-center mt-1 text-gray-600">${imageName}</div>
                </div>
              </span>`;
            } else {
              return `<span class="inline-flex items-center px-2 py-1 bg-purple-100 text-purple-800 rounded-md">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
                </svg>
                ${imageName}
              </span>`;
            }
          } else {
            throw new Error('No valid image data found');
          }
        } else {
          // Legacy format - just the URL
          console.log('Using URL-only format');
          const imageUrl = trimmedContent;
          if (imageUrl) {
            return `<span class="inline-flex items-center px-2 py-1 bg-purple-100 text-purple-800 rounded-md relative group">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
              </svg>
              Image
              <div class="absolute hidden group-hover:block bottom-full left-0 p-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50 mb-2 max-w-xs">
                <img src="${imageUrl}" alt="Image preview" class="max-w-full max-h-48 rounded" />
                <div class="text-xs text-center mt-1 text-gray-600">Image preview</div>
              </div>
            </span>`;
          } else {
            return `<span class="inline-flex items-center px-2 py-1 bg-purple-100 text-purple-800 rounded-md">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
              </svg>
              Image
            </span>`;
          }
        }
      } catch (error) {
        console.error('Error parsing image JSON:', error);
        // Try to extract a URL even if parsing failed
        try {
          const urlMatch = content.match(/https?:\/\/[^\s"')>]+/);
          if (urlMatch && urlMatch[0]) {
            const imageUrl = urlMatch[0];
            return `<span class="inline-flex items-center px-2 py-1 bg-purple-100 text-purple-800 rounded-md relative group">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
              </svg>
              Image
              <div class="absolute hidden group-hover:block bottom-full left-0 p-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50 mb-2 max-w-xs">
                <img src="${imageUrl}" alt="Image preview" class="max-w-full max-h-48 rounded" />
                <div class="text-xs text-center mt-1 text-gray-600">Image preview</div>
              </div>
            </span>`;
          }
        } catch (e) {
          console.warn('Failed to extract URL from error case:', e);
        }

        // Fallback if URL extraction also failed
        return `<span class="inline-flex items-center px-2 py-1 bg-purple-100 text-purple-800 rounded-md">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
          </svg>
          Image
        </span>`;
      }
    });

    return formattedText;
  }

  /**
   * Checks if the text contains any formatting markers that need to be processed
   *
   * @param text The text to check
   * @returns Boolean indicating if text contains formatting markers
   */
  public static containsFormatting(text?: string): boolean {
    if (!text) return false;

    // Check for formatting patterns or image tags
    return (text.includes('**') && text.match(/\*\*(.*?)\*\*/g) !== null) ||
           (text.includes('***') && text.match(/\*\*\*(.*?)\*\*\*/g) !== null) ||
           (text.match(/^#\s+/m) !== null) ||
           (text.match(/^##\s+/m) !== null) ||
           (text.match(/^###\s+/m) !== null) ||
           (text.includes('<imageref>') && text.includes('</imageref>'));
  }
}
