import * as Utils from '../utils/utils.js';

export async function getPromotionalCampaigns() {
	try {
		const response = await fetch(`${Utils.URL_DOMAIN}/promotional-campaigns`, {
			method: 'GET',
			withCredentials: true,
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
			},
			credentials: 'omit'
		});
		return await response.json();
	} catch (error) {
		console.error('Error fetching promotional campaigns:', error);
	}
}

export async function getFreeGift(id) {
	const response = await fetch(`${Utils.URL_DOMAIN}/promotional-campaign/${id}/free-gift`, {
		method: 'GET',
		withCredentials: true,
		credentials: 'omit',
		headers: {
			'Authorization': `Bearer ${localStorage.getItem('token')}`,
			'Access-Control-Allow-Origin': '*',
			'Content-Type': 'application/json'
		},
	});
	return await response.json();
}

export async function createPromoFreeGift(body) {
	const response = await fetch(`${Utils.URL_DOMAIN}/promotional-campaign/free-gift`, {
		method: 'POST',
		withCredentials: true,
		credentials: 'omit',
		headers: {
			'Authorization': `Bearer ${localStorage.getItem('token')}`,
			'Access-Control-Allow-Origin': '*',
			'Content-Type': 'application/json'
		},
		body
	});
	return await response.json();
}

export async function deletePromoFreeGift(id) {
	const response = await fetch(`${Utils.URL_DOMAIN}/promotional-campaign/${id}/free-gift`, {
		method: 'DELETE',
		withCredentials: true,
		credentials: 'omit',
		headers: {
			'Authorization': `Bearer ${localStorage.getItem('token')}`,
			'Access-Control-Allow-Origin': '*',
			'Content-Type': 'application/json'
		}
	});
	return await response.json();
}

export async function getProducts() {
	const response = await fetch(`${Utils.URL_DOMAIN}/shop-products`, {
		method: 'GET',
		credentials: 'omit',
		mode: 'cors',
		headers: {
			'Content-Type': 'application',
			Authorization: `Bearer ${localStorage.getItem('token')}`,
		},
	});
	return await response.json();
}
