import * as Utils from '../utils/utils.js';


export async function getEmailTemplate() {
	let response;
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/loyalty-email-template`, {
			method: 'GET',
			credentials: 'omit',
			mode: 'cors',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${localStorage.getItem('token')}`,
			},
		});
		response = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return response;
}

export async function saveEmailBranding(eventType, emailData) {
	let response;
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/loyalty-email-branding`, {
			method: 'POST',
			credentials: 'omit',
			mode: 'cors',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${localStorage.getItem('token')}`,
			},
			body: JSON.stringify({
				eventType,
				emailSubject: emailData.emailSubject,
				fromName: emailData.fromName,
				brandingData: emailData.branding,
				smartSend: emailData.smartSend,
				active: emailData.active,
			}),
		});
		response = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return response;
}

export async function loadEmailBranding(eventName) {
	let response;
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/loyalty-email-branding?eventName=${eventName}`, {
			method: 'GET',
			credentials: 'omit',
			mode: 'cors',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${localStorage.getItem('token')}`,
			},
		});
		response = await response.json();
		if (response.statusCode === 404) {
			return null;
		}
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return response;
}

export async function getLoyaltyEvents() {
	let response;
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/loyalty-events`, {
			method: 'GET',
			credentials: 'omit',
			mode: 'cors',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${localStorage.getItem('token')}`,
			},
		});
		response = await response.json();
		if (response.statusCode === 404) {
			return null;
		}
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return response.body;
}

export async function activeToggleLoyaltyEvent(eventName, active) {
	let response;
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/loyalty-email-events/active-toggle`, {
			method: 'POST',
			credentials: 'omit',
			mode: 'cors',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${localStorage.getItem('token')}`,
			},
			body: JSON.stringify({ eventName, active }),
		});
		response = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return response;
}
