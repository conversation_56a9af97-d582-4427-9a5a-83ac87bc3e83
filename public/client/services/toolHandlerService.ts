import { Emitter } from 'mitt'; // Ensure mitt is installed or add it as a dependency.

export interface ToolHandler {
  readonly tag: string;
  onStart(placeholderId: string, initialContent?: string): void;
  onContent(content: string): void;
  onEnd(): void;
}

// Forward declaration for ToolHandlerRegistry
class ToolHandlerRegistryInternal {
  private static instance: ToolHandlerRegistryInternal;
  private handlers: Map<string, ToolHandler> = new Map();

  private constructor() {}

  public static getInstance(): ToolHandlerRegistryInternal {
    if (!ToolHandlerRegistryInternal.instance) {
      ToolHandlerRegistryInternal.instance = new ToolHandlerRegistryInternal();
    }
    return ToolHandlerRegistryInternal.instance;
  }

  public register(handler: ToolHandler): void {
    if (this.handlers.has(handler.tag)) {
      console.warn(`ToolHandler with tag "${handler.tag}" is already registered. Overwriting.`);
    }
    this.handlers.set(handler.tag, handler);
    console.log(`ToolHandler registered: ${handler.tag}`);
  }

  public getHandler(tag: string): ToolHandler | undefined {
    return this.handlers.get(tag);
  }

  public getAllHandlers(): ToolHandler[] {
    return Array.from(this.handlers.values());
  }
}


export abstract class BaseToolHandler implements ToolHandler {
  abstract readonly tag: string;
  protected placeholderId: string | null = null;
  protected streamingContent: string = '';
  protected emitter: Emitter<any>; // Consider defining specific event types for your application

  constructor(emitter: Emitter<any>, autoRegister: boolean = true) {
    this.emitter = emitter;
    // Defer registration until after the derived class is fully initialized
    if (autoRegister) {
      // Using setTimeout to ensure this runs after the constructor chain completes
      setTimeout(() => this.register(true), 0);
    }
  }

  // New method for explicit registration
  protected register(ignoreDuplicate = false): void {
    if (!this.tag) {
      console.error(`Failed to register tool handler: 'tag' property is not set`);
      return;
    }
    ToolHandlerRegistry.getInstance().register(this, ignoreDuplicate);
  }

  abstract onStart(placeholderId: string, initialContent?: string): void;
  abstract onContent(content: string): void;
  abstract onEnd(): void;
}

export class ToolHandlerRegistry {
  private static instance: ToolHandlerRegistry;
  private handlers: Map<string, ToolHandler> = new Map();

  private constructor() {}

  public static getInstance(): ToolHandlerRegistry {
    if (!ToolHandlerRegistry.instance) {
      ToolHandlerRegistry.instance = new ToolHandlerRegistry();
    }
    return ToolHandlerRegistry.instance;
  }

  public register(handler: ToolHandler, ignoreDuplicate = false): void {
	if (!handler || !handler.tag) {
	  console.error(`Failed to register tool handler: 'handler' or 'tag' property is not set`);
	  return;
	}

    if (this.handlers.has(handler.tag)) {
		if (ignoreDuplicate) {
			return;
		}
      console.warn(`ToolHandler with tag "${handler.tag}" is already registered. Overwriting.`);
    }
    this.handlers.set(handler.tag, handler);
    console.log(`ToolHandler registered: ${handler.tag}`);
  }

  public getHandler(tag: string): ToolHandler | undefined {
    return this.handlers.get(tag);
  }

  public getAllHandlers(): ToolHandler[] {
    return Array.from(this.handlers.values());
  }
}
