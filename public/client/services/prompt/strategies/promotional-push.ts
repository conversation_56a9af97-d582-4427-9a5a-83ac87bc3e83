export const promotionalPushStrategy = `For a focused promotional push campaign, I follow this strategic approach:

1. Pre-Promotion Analysis
* Assess inventory levels and identify target products
* Review margin structure to determine viable discount levels
* Check competitive landscape for similar promotions
* Analyze historical performance of similar promotions
* Define clear KPIs (revenue targets, sell-through goals, AOV targets)

2. Audience Segmentation Strategy
* VIP/High-value customers (early access, exclusive offers)
* Recent browsers (product-specific targeting)
* Previous promotion participants (behavior-based targeting)
* Dormant customers (win-back incentives)
* New subscribers (first-purchase incentives)
* Non-purchasers (higher incentives if appropriate)

3. Promotion Tiering
* Design a clear promotional structure:
  * Potential early access period for VIPs
  * Main promotion period
  * Extension period (if needed)
  * Last chance urgency window
* Consider escalating offers or changing mechanics through the promotion

4. Email Sequence Design
* Teaser announcement (create anticipation, no offer details)
* VIP early access (exclusivity messaging)
* Main launch announcement (clear value proposition)
* Mid-promotion highlight (feature different products/benefits)
* Last day/hours reminder (urgency creation)
* Post-promotion follow-up (thank you, what's next)

5. Offer Mechanics Selection
* Choose from:
  * Percentage or dollar amount discounts
  * Tiered discounting (spend more, save more)
  * Gift with purchase
  * Free shipping thresholds
  * Bundle offers
  * Limited-edition or exclusive products
* Select mechanics that align with brand positioning and profit goals

6. Creative Approach
* Develop a cohesive creative theme for the promotion
* Craft compelling subject lines that progressively build urgency
* Design mobile-optimized templates with clear CTAs
* Prepare multiple creative variations for A/B testing
* Ensure all creative elements reinforce the value proposition

7. Cross-Channel Integration
* Coordinate timing with social media, SMS, and other channels
* Create channel-specific versions of promotional assets
* Plan retargeting strategy for site visitors
* Update website banners and product pages with promotion
* Consider popup strategies for site visitors

8. Technical Preparation
* Set up and test promotional codes
* Confirm inventory visibility and tracking
* Prepare landing pages for each segment
* Set up tracking parameters for all links
* Test the complete purchase flow with promocodes

9. Risk Management
* Have a contingency plan for inventory issues
* Prepare for potential extension if targets aren't met
* Create backup creative if initial response is weak
* Set maximum redemption caps if margin protection is needed
* Plan customer service responses for common questions

10. Post-Campaign Analysis
* Compare performance across segments
* Analyze promotion impact on margin and AOV
* Evaluate new vs. returning customer ratio
* Measure impact on subsequent full-price purchasing
* Document learnings for future promotions

For timing and implementation:
* Avoid launching on Mondays (email volume) or Fridays (weekend disengagement)
* Schedule first announcement email for mid-morning (10-11 AM)
* Send urgent/reminder emails in late afternoon (2-4 PM)
* Ensure adequate spacing between emails (minimum 24-48 hours)
* Consider time zone issues for global audiences

This structured approach maximizes revenue while protecting brand value and customer experience during promotional periods.`;