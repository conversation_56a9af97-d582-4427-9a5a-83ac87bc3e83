import { thematicMonthStrategy } from './thematic-month';
import { nonThematicMonthStrategy } from './non-thematic-month';
import { promotionalPushStrategy } from './promotional-push';

export const planStrategies = {
  'thematic-month': {
    name: 'Thematic Month Strategy',
    description: 'A comprehensive strategy for planning content around major themes, holidays, and seasonal events.',
    strategy: thematicMonthStrategy
  },
  'non-thematic-month': {
    name: 'Non-Thematic Month Strategy',
    description: 'Strategy for months without major holidays or seasonal peaks, focusing on sustainable engagement.',
    strategy: nonThematicMonthStrategy
  },
  'promotional-push': {
    name: 'Promotional Push Strategy',
    description: 'A focused approach for planning promotional campaigns with maximum revenue impact.',
    strategy: promotionalPushStrategy
  }
};

export type StrategyKey = keyof typeof planStrategies;

export const getStrategyOptions = () => {
  return Object.entries(planStrategies).map(([key, value]) => ({
    value: key,
    label: value.name,
    description: value.description
  }));
};

export const getStrategyByKey = (key: StrategyKey) => {
  return planStrategies[key]?.strategy || '';
};

export default planStrategies;