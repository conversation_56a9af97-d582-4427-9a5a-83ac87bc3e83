import {ImageProcessingService} from './imageProcessService';
import {jsonrepair} from 'jsonrepair';

/**
 * Represents an extracted artifact from a message
 */
export interface Artifact {
  type:
    | 'brief_artifact'
    | 'email_artifact'
    | 'plan_artifact'
    | 'buildplan_trigger'
    | 'text'
    | 'image'
    | 'multiimage'
    | 'memory'
    | 'tool_message'
    | 'switch_mode';
  startIndex: number;
  endIndex: number;
  rawContent?: string;
  parsedContent?: any;
  content?: string;
}

/**
 * Represents a processed message segment that can be displayed in the chat
 */
export interface ChatMessageSegment {
  id: string;
  type:
    | 'text'
    | 'user'
    | 'brief_placeholder'
    | 'brief_artifact'
    | 'email_placeholder'
    | 'email_artifact'
    | 'plan_placeholder'
    | 'plan_artifact'
    | 'buildplan_trigger'
    | 'image'
    | 'generatedImage'
    | 'multiimage'
    | 'memory'
    | 'tool_message'
    | 'brief_error'
    | 'email_error'
    | 'historic_brief'
    | 'historic_email'
    | 'brief_content'
    | 'switch_mode';
  sender?: 'user' | 'ai';
  content?: string;
  rawContent?: string;
  isGenerating?: boolean;
  timestamp?: Date;
  design?: any;
  planData?: any;
  isHtml?: boolean;
  imageUrl?: string;
  imageLoaded?: boolean;
  imageError?: boolean;
  uploadProgress?: number;
  images?: Array<{
    url: string;
    name?: string;
    loaded?: boolean;
    error?: boolean;
  }>;
  memory?: {
    category: string;
    info: string;
  };
  switchModeData?: {
    message: string;
    mode: string;
    summary: string;
  };
  hasParseError?: boolean;
  // Error-related properties
  errorMessage?: string;
  errorDetails?: string;
  // Brief-related properties
  briefData?: {
    subjectLine: string;
    previewText: string;
    briefText: string;
  };
  // Email-related properties
  emailData?: any;
  artifactType?: 'brief' | 'email' | 'plan';
}

/**
 * Service responsible for extracting and processing artifacts from AI messages
 */
export class MessageArtifactService {
  /**
   * Process an AI message to extract artifacts and create chat segments
   * @param message The message to process
   * @param generateId Function to generate unique IDs for segments
   * @returns Array of processed chat segments
   */
  processAssistantMessage(
    message: any,
    generateId: () => string,
  ): {segments: ChatMessageSegment[]; artifactFound: boolean} {
    console.log('Processing assistant message:', message.id);

    // Get message content
    const content = message.content || '';
    let artifactFound = false;

    // Log the message content for debugging
    if (content.length > 500) {
      console.log(
        'Message content (truncated):',
        content.substring(0, 500) + '...',
      );
    } else {
      console.log('Message content:', content);
    }

    // Step 1: First, identify and extract all artifacts in the content
    let artifacts: Artifact[] = [];

    // Find all artifact tags in the content
    this.findAllArtifacts(content, artifacts);

    // Sort artifacts by their position in the text
    artifacts.sort((a, b) => a.startIndex - b.startIndex);

    // Step 2: Add text segments between artifacts
    let segments: Array<Artifact & {content?: string}> = [];
    let lastEndIndex = 0;

    // Process each artifact and the text before it
    for (const artifact of artifacts) {
      // Add text segment before this artifact if there is any
      if (artifact.startIndex > lastEndIndex) {
        const textSegment = {
          type: 'text' as const,
          startIndex: lastEndIndex,
          endIndex: artifact.startIndex,
          content: content.substring(lastEndIndex, artifact.startIndex).trim(),
        };

        if (textSegment.content) {
          segments.push(textSegment);
        }
      }

      // Add the artifact
      segments.push(artifact);
      lastEndIndex = artifact.endIndex;
    }

    // Add final text segment after the last artifact if needed
    if (lastEndIndex < content.length && artifacts.length > 0) {
      const finalText = content.substring(lastEndIndex).trim();
      if (finalText) {
        segments.push({
          type: 'text',
          startIndex: lastEndIndex,
          endIndex: content.length,
          content: finalText,
        });
      }
    } else if (artifacts.length === 0) {
      segments.push({
        type: 'text',
        startIndex: 0,
        endIndex: content.length,
        content: content.trim(),
      });
    } else {
      artifactFound = true;
    }

    // Step 3: Process each segment and convert to ChatMessageSegment format
    const chatSegments: ChatMessageSegment[] = [];
    for (const segment of segments) {
      const chatSegment = this.convertToChatSegment(
        segment,
        generateId,
        message.createdAt,
      );
      if (chatSegment) {
        chatSegments.push(chatSegment);
      }
    }

    // Process any image generation/editing results from LLM metadata
    this.processLLMMetadata(message, chatSegments, generateId);

    // Log the total number of segments after processing this message
    console.log(
      `Finished processing message ${message.id}. Total segments: ${chatSegments.length}`,
    );

    return {segments: chatSegments, artifactFound};
  }

  /**
   * Find all artifacts in the content by looking for specific tags
   */
  private findAllArtifacts(content: string, artifacts: Artifact[]): void {
    const findArtifacts = (pattern: RegExp, type: Artifact['type']) => {
      const matches = [...content.matchAll(pattern)];
      for (const match of matches) {
        if (match.index !== undefined) {
          const startIndex = match.index;
          const endIndex = startIndex + match[0].length;
          const rawContent = match[1]?.trim();

          artifacts.push({
            type,
            startIndex,
            endIndex,
            rawContent,
          });
        }
      }
    };

    // Find each type of artifact
    findArtifacts(/<brief>([\s\S]*?)<\/brief>/gm, 'brief_artifact');
    findArtifacts(/<email>([\s\S]*?)<\/email>/gm, 'email_artifact');
    findArtifacts(/<plan>([\s\S]*?)<\/plan>/gm, 'plan_artifact');
    findArtifacts(/<buildplan>[\s\S]*?<\/buildplan>/gm, 'buildplan_trigger');
    findArtifacts(/<image>([\s\S]*?)<\/image>/gm, 'image');
    findArtifacts(/<multiimage>([\s\S]*?)<\/multiimage>/gm, 'multiimage');
    findArtifacts(/<memory>([\s\S]*?)<\/memory>/gm, 'memory');
    findArtifacts(/<tool_message>([\s\S]*?)<\/tool_message>/gm, 'tool_message');
    findArtifacts(/<switch_mode>([\s\S]*?)<\/switch_mode>/gm, 'switch_mode');

    // Add image upload and generation tags
    findArtifacts(/<upload>[\s\S]*?<\/upload>/gm, 'text'); // We'll just remove these tags
    findArtifacts(/<ig>[\s\S]*?<\/ig>/gm, 'text'); // We'll just remove these tags
    findArtifacts(/<ie>[\s\S]*?<\/ie>/gm, 'text'); // We'll just remove these tags
  }

  /**
   * Convert an extracted artifact to a chat message segment
   */
  private convertToChatSegment(
    segment: Artifact,
    generateId: () => string,
    timestamp: string | Date,
  ): ChatMessageSegment | null {
    const newTimestamp = new Date(timestamp);

    if (segment.type === 'text' && segment.content) {
      // Process inline image tags in text content using ImageProcessingService
      const processedContent = ImageProcessingService.processInlineImageTags(
        segment.content,
      );

      // If image tags were found, create image segments for each URL
      if (
        processedContent.hasImageTags &&
        processedContent.imageUrls.length > 0
      ) {
        // For each image URL found, create a separate image segment
        const imageSegments: ChatMessageSegment[] = [];
        for (const imageUrl of processedContent.imageUrls) {
          if (imageUrl) {
            console.log(
              'Creating image segment from inline tag with URL:',
              imageUrl,
            );
            // Add the image segment
            imageSegments.push({
              id: generateId(),
              type: 'image',
              sender: 'ai',
              imageUrl: imageUrl,
              timestamp: newTimestamp,
              imageLoaded: false,
              imageError: false,
            });
          }
        }

        // If there's text left after removing image tags, add it as a text segment
        if (
          processedContent.hasImageTags &&
          processedContent.modifiedContent.trim()
        ) {
          imageSegments.push({
            id: generateId(),
            type: 'text',
            sender: 'ai',
            content: processedContent.modifiedContent.trim(),
            timestamp: newTimestamp,
          });
        }

        // Return the first segment and add the rest later
        return imageSegments[0];
      }

      // Add the text segment if there are no image tags
      return {
        id: generateId(),
        type: 'text',
        sender: 'ai',
        content: segment.content,
        timestamp: newTimestamp,
      };
    } else if (segment.type === 'brief_artifact' && segment.rawContent) {
      // Process brief artifact
      return {
        id: generateId(),
        type: 'brief_artifact',
        sender: 'ai',
        isGenerating: false,
        rawContent: segment.rawContent,
        timestamp: newTimestamp,
      };
    } else if (segment.type === 'email_artifact' && segment.rawContent) {
      // Process email artifact
      try {
        // Store raw JSON for later processing with generateEmailFromComponents
        // We don't directly set the parsed JSON as the design - that should be
        // handled by the generateEmailFromComponents function
        return {
          id: generateId(),
          type: 'email_artifact',
          sender: 'ai',
          isGenerating: false,
          rawContent: segment.rawContent, // Store the raw JSON for processing
          timestamp: newTimestamp,
        };
      } catch (e) {
        console.error('Error processing email artifact:', e);
        return null;
      }
    } else if (segment.type === 'plan_artifact' && segment.rawContent) {
      // Process plan artifact
      try {
        let planContent = segment.rawContent;

        // Extract JSON content if it exists
        const jsonStartIndex = planContent.indexOf('{');
        const jsonEndIndex = planContent.lastIndexOf('}');

        if (
          jsonStartIndex !== -1 &&
          jsonEndIndex !== -1 &&
          jsonEndIndex > jsonStartIndex
        ) {
          planContent = planContent.substring(jsonStartIndex, jsonEndIndex + 1);
        }

        // Try to repair and parse the JSON
        const repairedJson = jsonrepair(planContent);
        const planData = JSON.parse(repairedJson);

        const planSegmentId = generateId();
        console.log('Adding plan artifact with ID:', planSegmentId);
        return {
          id: planSegmentId,
          type: 'plan_artifact',
          sender: 'ai',
          isGenerating: false,
          planData: planData,
          rawContent: planContent,
          timestamp: newTimestamp,
          hasParseError: false,
        };
      } catch (e) {
        console.error('Error parsing plan JSON:', e);

        // Add plan artifact with error state
        const planSegmentId = generateId();
        console.log(
          'Adding plan artifact with parse error, ID:',
          planSegmentId,
        );
        return {
          id: planSegmentId,
          type: 'plan_artifact',
          sender: 'ai',
          isGenerating: false,
          rawContent: segment.rawContent,
          timestamp: newTimestamp,
          hasParseError: true,
        };
      }
    } else if (segment.type === 'buildplan_trigger') {
      // Add buildplan trigger
      return {
        id: generateId(),
        type: 'buildplan_trigger',
        sender: 'ai',
        isGenerating: false,
        timestamp: newTimestamp,
      };
    } else if (segment.type === 'image' && segment.rawContent) {
      // Extract image URL using ImageProcessingService
      const imageUrl = ImageProcessingService.extractImageUrl(
        segment.rawContent,
      );
      if (imageUrl) {
        return {
          id: generateId(),
          type: 'image',
          sender: 'ai',
          imageUrl: imageUrl,
          timestamp: newTimestamp,
          imageLoaded: false,
          imageError: false,
        };
      }
    } else if (segment.type === 'multiimage' && segment.rawContent) {
      try {
        const multiimageContent = segment.rawContent.trim();
        console.log('Processing multiimage content:', multiimageContent);

        // Use ImageProcessingService to parse the JSON
        const imageData = ImageProcessingService.parseImageJson(
          multiimageContent,
          jsonrepair,
        );

        if (Array.isArray(imageData)) {
          const multiimageId = generateId();
          return {
            id: multiimageId,
            type: 'multiimage',
            sender: 'ai',
            images: imageData.map((img: any) => ({
              url: typeof img === 'string' ? img : img.url || '',
              name: typeof img === 'string' ? '' : img.name || '',
              loaded: false,
              error: false,
            })),
            timestamp: newTimestamp,
          };
        } else if (typeof imageData === 'object' && imageData.url) {
          // Handle case where it's a single image object
          const multiimageId = generateId();
          return {
            id: multiimageId,
            type: 'multiimage',
            sender: 'ai',
            images: [
              {
                url: imageData.url,
                name: imageData.name || '',
                loaded: false,
                error: false,
              },
            ],
            timestamp: newTimestamp,
          };
        } else {
          console.error(
            'Multiimage content from tag is not an array or valid image object:',
            imageData,
          );
          return {
            id: generateId(),
            type: 'text',
            sender: 'ai',
            content: `Error processing images: Invalid format`,
            timestamp: newTimestamp,
          };
        }
      } catch (e) {
        console.error('Error processing multiimage content:', e);
        return {
          id: generateId(),
          type: 'text',
          sender: 'ai',
          content: `Error processing images: ${e.message}`,
          timestamp: newTimestamp,
        };
      }
    } else if (segment.type === 'memory' && segment.rawContent) {
      try {
        const memContent = jsonrepair(segment.rawContent.trim());
        const memory = JSON.parse(memContent);
        console.log('Processed memory content:', memory);
        return {
          id: generateId(),
          type: 'memory',
          sender: 'ai',
          memory,
          timestamp: newTimestamp,
        };
      } catch (e) {
        console.error('Error processing memory content:', e);
        return {
          id: generateId(),
          type: 'text',
          sender: 'ai',
          content: `Error processing memory: ${e.message}`,
          timestamp: newTimestamp,
        };
      }
    } else if (segment.type === 'tool_message' && segment.rawContent) {
      // Process tool message
      return {
        id: generateId(),
        type: 'tool_message',
        sender: 'ai',
        content: segment.rawContent.trim(),
        timestamp: newTimestamp,
      };
    } else if (segment.type === 'switch_mode' && segment.rawContent) {
      try {
        const switchModeContent = jsonrepair(segment.rawContent.trim());
        const switchModeData = JSON.parse(switchModeContent);
        console.log('Processed switch_mode content:', switchModeData);
        return {
          id: generateId(),
          type: 'switch_mode',
          sender: 'ai',
          switchModeData: {
            message: switchModeData.message || '',
            mode: switchModeData.mode || '',
            summary: switchModeData.summary || '',
          },
          timestamp: newTimestamp,
        };
      } catch (e) {
        console.error('Error processing switch_mode content:', e);
        return {
          id: generateId(),
          type: 'text',
          sender: 'ai',
          content: `Error processing switch mode: ${e.message}`,
          timestamp: newTimestamp,
        };
      }
    }

    return null;
  }

  /**
   * Process LLM metadata to extract image generation/editing results
   */
  private processLLMMetadata(
    message: any,
    chatSegments: ChatMessageSegment[],
    generateId: () => string,
  ): void {
    if (message.llmMetadata) {
      try {
        const metadata =
          typeof message.llmMetadata === 'string'
            ? JSON.parse(message.llmMetadata)
            : message.llmMetadata;

        // Handle generated images
        if (
          metadata.generatedImages &&
          Array.isArray(metadata.generatedImages)
        ) {
          metadata.generatedImages.forEach(
            (img: {url: string; revised_prompt?: string}) => {
              chatSegments.push({
                id: generateId(),
                type: 'generatedImage',
                sender: 'ai',
                imageUrl: img.url,
                content: img.revised_prompt
                  ? `Generated image based on revised prompt: ${img.revised_prompt}`
                  : undefined,
                timestamp: new Date(message.createdAt),
                imageLoaded: false,
                imageError: false,
              });
            },
          );
        }

        // Handle edited images
        if (metadata.editedImages && Array.isArray(metadata.editedImages)) {
          metadata.editedImages.forEach((img: {url: string}) => {
            chatSegments.push({
              id: generateId(),
              type: 'image',
              sender: 'ai',
              imageUrl: img.url,
              timestamp: new Date(message.createdAt),
              imageLoaded: false,
              imageError: false,
            });
          });
        }
      } catch (e) {
        console.error('Error processing LLM metadata for images:', e);
      }
    }
  }
}

// Create a singleton instance for easy importing
export const messageArtifactService = new MessageArtifactService();
