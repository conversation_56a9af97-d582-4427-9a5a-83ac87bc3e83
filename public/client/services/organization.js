var testMode = 0;
import * as Utils from '../utils/utils.js';
import * as OrganizationSettings from './organization-settings.js';

// Helper class for formatting prompt data
class PromptDataHelper {
	constructor() {
	  this.userType = '';
	  this.userGoal = '';
	  this.idealCustomer = '';
	  this.customerBehavior = '';
	  this.marketingSuccess = '';
	  this.hasSubscriptions = '';
	  this.customerProblems = '';
	  this.influencerCollaboration = '';
	  this.emailTemplates = [];
	  this.sampleLanguage = '';
	  this.description = '';
	  this.hasCustomComponents = false;
	  this.componentOverrides = {};
	}

	// Fetch data from the backend or local state
	async fetchData() {
	  const orgData = await getCurrentOrg();
	  this.sampleLanguage = orgData.sampleLanguage || '';
	  this.description = orgData.description || '';

	  // Fetch other settings (assuming these are stored in organization settings)
	  this.hasSubscriptions = (await OrganizationSettings.getOrganizationSetting('hasSubscriptions')) === 'true';
	  this.customerProblems = await OrganizationSettings.getOrganizationSetting('customerProblems');
	  this.influencerCollaboration = await OrganizationSettings.getOrganizationSetting('influencerCollaboration');
	  this.userType = await OrganizationSettings.getOrganizationSetting('UserType');
	  this.userGoal = await OrganizationSettings.getOrganizationSetting('UserGoal');
	  this.idealCustomer = await OrganizationSettings.getOrganizationSetting('idealCustomer');
	  this.customerBehavior = await OrganizationSettings.getOrganizationSetting('customerBehavior');
	  this.marketingSuccess = await OrganizationSettings.getOrganizationSetting('marketingSuccess');

	  // Fetch email templates
	  const emailTemplatesString = await OrganizationSettings.getOrganizationSetting('emailTemplates');
	  if (emailTemplatesString) {
		this.emailTemplates = JSON.parse(emailTemplatesString);
	  }

	  // Fetch component override settings
	  this.hasCustomComponents = (await OrganizationSettings.getOrganizationSetting('hasCustomComponents')) === 'true';

	  if (this.hasCustomComponents) {
		const componentOverridesString = await OrganizationSettings.getOrganizationSetting('componentOverrides');
		if (componentOverridesString) {
		  this.componentOverrides = JSON.parse(componentOverridesString);
		} else {
		  this.componentOverrides = {};
		}
	  } else {
		this.componentOverrides = {};
	  }

	  // Add other data fetching logic here (e.g., UserType, UserGoal, etc.)
	}

	// Format data into a prompt string
	formatPrompt(excludeFields = []) {
	  const data = {
		userType: this.userType,
		userGoal: this.userGoal,
		idealCustomer: this.idealCustomer,
		customerBehavior: this.customerBehavior,
		marketingSuccess: this.marketingSuccess,
		hasSubscriptions: this.hasSubscriptions,
		customerProblems: this.customerProblems,
		influencerCollaboration: this.influencerCollaboration,
		emailTemplates: this.emailTemplates,
		sampleLanguage: this.sampleLanguage,
		description: this.description,
	  };

	  // Filter out excluded fields
	  const filteredData = Object.keys(data).reduce((acc, key) => {
		if (!excludeFields.includes(key)) {
		  acc[key] = data[key];
		}
		return acc;
	  }, {});

	  // Format the data into a readable prompt
	  let prompt = 'Here is the general business context for the ecommerce store you can use to help:\n\n';
	  for (const [key, value] of Object.entries(filteredData)) {
		if (Array.isArray(value)) {
		  prompt += `- ${key}: ${value.map((item) => JSON.stringify(item)).join(', ')}\n`;
		} else {
		  prompt += `- ${key}: ${value}\n`;
		}
	  }

	  return prompt;
	}
  }

  // Export the helper class
  export const promptDataHelper = new PromptDataHelper();

export async function getOrgById(id) {
	let response = {};
	let jsonresponse = {};
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/organizations/${id}`, {
			method: 'GET',
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json',
				'ngrok-skip-browser-warning': true
			}
		});
		jsonresponse = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return jsonresponse;
}

export async function getCurrentOrg() {
	let response = {};
	let jsonresponse = {};
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/organization/current`, {
			method: 'GET',
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json',
				'ngrok-skip-browser-warning': true
			}
		});
		jsonresponse = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return jsonresponse;
}

//Generic function to patch an organization
export async function patchOrgById(id, data) {
	let response = {};
	let jsonresponse = {};
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/organizations/${id}`, {
			method: "PATCH",
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(data),
		});

		//Doesn't return JSON if it's a 204
		if (response.status == 204) {
			return {
				'status': 'success'
			};
		}
		//jsonresponse = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return jsonresponse;
}

export async function updateOrgById(id, orgName, profilePicUrl, questAlbumArtUrl, questBotName) {
	var data = {
		name: orgName,
	};

	if (profilePicUrl) {
		data.profilePicUrl = profilePicUrl;
	}
	if (questAlbumArtUrl) {
		data.questAlbumArtUrl = questAlbumArtUrl;
	}
	if (questBotName) {
		data.questBotName = questBotName;
	}

	let response = {};
	let jsonresponse = {};
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/organizations/${id}`, {
			method: "PATCH",
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(data),
		});
		jsonresponse = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return jsonresponse;
}

export async function updateOrgInteraction(id, eventName, value, iData) {
	Object.keys(iData.org).forEach(function (key, index) {
		console.log('FINDING STUFF ' + iData.org[key]);
		if (key == eventName) {
			console.log('FINDING MATCH');
			iData.org[key] = value;
		}
	});

	var striData = JSON.stringify(iData);
	var data = {
		interactionData: striData
	};

	let response = {};
	let jsonresponse = {};
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/organizations/${id}`, {
			method: "PATCH",
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(data),
		});
		jsonresponse = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return jsonresponse;
}

export async function resetOrgInteractions(id) {
	var iData = {
		org:
		{
			attribution: null,
			custom_events: null,
			audiences: null,
			teammates: null
		}
	}

	var striData = JSON.stringify(iData);
	var data = {
		interactionData: striData
	};

	let response = {};
	let jsonresponse = {};
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/organizations/${id}`, {
			method: "PATCH",
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(data),
		});
		jsonresponse = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return jsonresponse;
}

export async function setOrgAttribution(id, attrStatus) {
	var data = {
		attribution: attrStatus
	};

	let response = {};
	let jsonresponse = {};
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/organizations/${id}`, {
			method: "PATCH",
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(data),
		});
		jsonresponse = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return jsonresponse;
}

export async function createEnvAndUser(envName, envUsername) {
	//Add env
	var data = {
		name: envName,
		dev: false,
		beta: false,
		attribution: false
	};

	let response = {};
	let jsonresponse = {};
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/onboard/organization`, {
			method: "post",
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				"accept": "application/json",
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				"Content-Type": "application/json"
			},
			body: JSON.stringify(data),
		});
		jsonresponse = await response.json();
	} catch (err) {
		console.log("Org Error: " + JSON.stringify(err));
		throw err;
	}

	//Add user for it
	var userData1 = {
		email: envUsername,
		password: 'raleonBillions!'
	};

	let userResponse = {};
	let userJsonResponse = {};
	userResponse = await fetch(`${Utils.URL_DOMAIN}/user`, {
		method: "post",
		credentials: 'omit',
		headers: {
			"accept": "application/json",
			'Authorization': `Bearer ${localStorage.getItem('token')}`,
			'Access-Control-Allow-Origin': '*',
			"Content-Type": "application/json"
		},
		body: JSON.stringify(userData1),
	});
	userJsonResponse = await userResponse.json();

	var userData2 = {
		firstName: 'Raleon',
		lastName: 'Support',
		organizationId: jsonresponse.id,
		roles: ['admin', 'raleon-support']
	};

	let userResponseUpdate = {};
	userResponseUpdate = await fetch(`${Utils.URL_DOMAIN}/onboard/users/${userJsonResponse.id}`, {
		method: "PATCH",
		withCreditentials: true,
		credentials: 'omit',
		headers: {
			'Authorization': `Bearer ${localStorage.getItem('token')}`,
			"accept": "*/*",
			"Content-Type": "application/json"
		},
		body: JSON.stringify(userData2),
	});
}
