import * as Utils from '../utils/utils.js';

export async function getGiveaways() {
	let response;
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/giveaways`, {
			method: 'GET',
			credentials: 'omit',
			mode: 'cors',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${localStorage.getItem('token')}`,
			}
		});
		response = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));

	}
	return response;
}

export async function getGiveaway(id) {
	let response;
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/giveaway/${id}`, {
			method: 'GET',
			credentials: 'omit',
			mode: 'cors',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${localStorage.getItem('token')}`,
			}
		});
		response = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return response;
}

export async function createGiveaway(name, startDate, endDate) {
	if (!name) {
		throw new Error('Name is required');
	}
	if (!startDate) {
		startDate = new Date(new Date().toISOString().substring(0, 10) + ' 00:00').toISOString();
	}
	if (!endDate) {
		const eodToday = new Date(new Date().toISOString().substring(0, 10) + ' 23:59:59')
		eodToday.setDate(eodToday.getDate() + 30);
		endDate = eodToday.toISOString();
	}

	let response = await fetch(`${Utils.URL_DOMAIN}/giveaway`, {
		method: 'POST',
		withCreditentials: true,
		credentials: 'omit',
		headers: {
			'Authorization': `Bearer ${localStorage.getItem('token')}`,
			'Access-Control-Allow-Origin': '*',
			'Content-Type': 'application/json'
		},
		body: JSON.stringify({ name, startDate, endDate })
	});
	response = await response.json();

	if (!response || response.error) {
		console.log("Error: " + JSON.stringify(response.error.message));
		throw new Error(response.error.message);
	}
	return response;
}

export async function updateGiveaway(giveaway) {
	if (!giveaway) {
		throw new Error('Giveaway is required');
	}
	if (!giveaway.id) {
		throw new Error('Giveaway id is required');
	}

	let response = await fetch(`${Utils.URL_DOMAIN}/giveaway/${giveaway.id}`, {
		method: 'PATCH',
		withCreditentials: true,
		credentials: 'omit',
		headers: {
			'Authorization': `Bearer ${localStorage.getItem('token')}`,
			'Access-Control-Allow-Origin': '*',
			'Content-Type': 'application/json'
		},
		body: JSON.stringify(giveaway)
	});

	if (!response || !response.ok) {
		console.log("Error: " + JSON.stringify(response.error.message));
		throw new Error(response.error.message);
	}
	return response;
}

export async function getGiveawayCustomerActions() {
	let response;
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/giveaway/actions`, {
			method: 'GET',
			credentials: 'omit',
			mode: 'cors',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${localStorage.getItem('token')}`,
			}
		});
		response = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return response;
}

export async function getVipTiers() {
	let response;
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/tiers`, {
			method: 'GET',
			credentials: 'omit',
			mode: 'cors',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${localStorage.getItem('token')}`,
			}
		});
		response = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return response;
}

export async function getFoundationalCampaign(id) {
	let response;
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/giveaway/${id}/foundational-campaign`, {
			method: 'GET',
			credentials: 'omit',
			mode: 'cors',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${localStorage.getItem('token')}`,
			}
		});
		response = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return response;
}

export async function getGiveawayWtes(id) {
	let response;
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/giveaway/${id}/ways-to-earn`, {
			method: 'GET',
			credentials: 'omit',
			mode: 'cors',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${localStorage.getItem('token')}`,
			}
		});
		response = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));
	}
	return response;
}

export async function activateGiveaway(id, active, skipLaunchStatus = false) {
	const path = active ? `/launch-giveaway/${id}?skipLaunchStatus=${skipLaunchStatus}` : `/deactivate-giveaway/${id}?skipLaunchStatus=${skipLaunchStatus}`;

	let response;
	try {
		response = await fetch(`${Utils.URL_DOMAIN}${path}`, {
			method: 'POST',
			credentials: 'omit',
			mode: 'cors',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${localStorage.getItem('token')}`,
			}
		});
	} catch (e) {
		console.log("Error: " + JSON.stringify(e));
		throw e;
	}
	return response;
}
