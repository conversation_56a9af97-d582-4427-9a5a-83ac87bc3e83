import * as Utils from '../utils/utils.js';

export async function saveRecommendationState(id, apiPath, recommendationState) {
	const queryString = `where=${encodeURIComponent(JSON.stringify({id: id}))}`;
	apiPath = apiPath.startsWith('/') ? apiPath.substring(1) : apiPath;
	const response = await fetch(`${Utils.URL_DOMAIN}/${apiPath}?${queryString}`, {
		method: 'PATCH',
		credentials: 'omit',
		mode: 'cors',
		headers: {
			'Content-Type': 'application/json',
			Authorization: `Bearer ${localStorage.getItem('token')}`,
			'Content-Type': 'application/json',
			'ngrok-skip-browser-warning': true
		},
		body: JSON.stringify({ recommendationState }),
	});
	return await response.json();
}

export async function enableWteOrShopItem(payload, apiPath) {
	const response = await fetch(`${Utils.URL_DOMAIN}/${apiPath}/enable`, {
		method: 'POST',
		credentials: 'omit',
		mode: 'cors',
		headers: {
			'Content-Type': 'application/json',
			Authorization: `Bearer ${localStorage.getItem('token')}`,
		},
		body: JSON.stringify(payload),
	});

	return await response.json();
}

export async function deleteWteOrShopItem(apiPath, id) {
	const response = await fetch(`${Utils.URL_DOMAIN}/${apiPath}/${id}`, {
		method: 'DELETE',
		credentials: 'omit',
		mode: 'cors',
		headers: {
			'Content-Type': 'application',
			Authorization: `Bearer ${localStorage.getItem('token')}`,
		},
	});

	return await response.json();
}
