import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import * as currencyUtils from '../../services/currency.js';

// Mock the Utils module
vi.mock('../../client-old/utils/Utils', () => ({
	URL_DOMAIN: 'http://test-api.com',
}));

describe('Currency Utilities', () => {
	beforeEach(() => {
		// Reset the primaryCurrencyCache before each test
		currencyUtils.clearPrimaryCurrencyCache();

		// Mock localStorage
		vi.spyOn(Storage.prototype, 'getItem').mockReturnValue('test-token');

		// Mock fetch
		global.fetch = vi.fn();
	});

	afterEach(() => {
		vi.restoreAllMocks();
	});

	describe('formatNumberWithCurrency', () => {
		it('should format number with currency', async () => {
			global.fetch.mockResolvedValueOnce({
				json: vi.fn().mockResolvedValueOnce({ prefix: '$', postfix: '' }),
			});

			const result = await currencyUtils.formatNumberWithCurrency(100);
			expect(result).toBe('$100');
		});

		it('should return original number as string if currency fetch fails', async () => {
			global.fetch.mockRejectedValueOnce(new Error('Fetch failed'));

			const result = await currencyUtils.formatNumberWithCurrency(100);
			expect(result).toBe(100);
		});
	});

	describe('replaceCurrencyTagsSync', () => {
		it('should replace currency tags synchronously', async () => {
			global.fetch.mockResolvedValueOnce({
				json: vi.fn().mockResolvedValueOnce({ prefix: '€', postfix: '' }),
			});

			// Call getPrimaryCurrency to set the cache
			await currencyUtils.returnPrimaryCurrency();

			const result = currencyUtils.replaceCurrencyTagsSync('<currencyPrefix>100<currencyPostfix>');
			expect(result).toBe('€100');
		});

		it('should remove currency tags if currency is not fetched', () => {
			const result = currencyUtils.replaceCurrencyTagsSync('<currencyPrefix>100<currencyPostfix>');
			expect(result).toBe('100');
		});
	});

	describe('replaceCurrencyTags', () => {
		it('should replace currency tags asynchronously', async () => {
			global.fetch.mockResolvedValueOnce({
				json: vi.fn().mockResolvedValueOnce({ prefix: '£', postfix: '' }),
			});

			const result = await currencyUtils.replaceCurrencyTags('<currencyPrefix>100<currencyPostfix>');
			expect(result).toBe('£100');
		});
	});

	describe('returnPrimaryCurrency', () => {
		it('should return primary currency', async () => {
			const mockCurrency = { prefix: '¥', postfix: '' };
			global.fetch.mockResolvedValueOnce({
				json: vi.fn().mockResolvedValueOnce(mockCurrency),
			});

			const result = await currencyUtils.returnPrimaryCurrency();
			expect(result).toEqual(mockCurrency);
		});
	});

	describe('replaceDollarWithCurrency', () => {
		it('should replace dollar signs with currency', async () => {
			global.fetch.mockResolvedValueOnce({
				json: vi.fn().mockResolvedValueOnce({ prefix: '€', postfix: '' }),
			});

			const result = await currencyUtils.replaceDollarWithCurrency('The price is $100 and $200');
			expect(result).toBe('The price is €100 and €200');
		});

		it('should not replace text without dollar signs', async () => {
			global.fetch.mockResolvedValueOnce({
				json: vi.fn().mockResolvedValueOnce({ prefix: '€', postfix: '' }),
			});

			const result = await currencyUtils.replaceDollarWithCurrency('The price is 100 and 200');
			expect(result).toBe('The price is 100 and 200');
		});
	});
});
