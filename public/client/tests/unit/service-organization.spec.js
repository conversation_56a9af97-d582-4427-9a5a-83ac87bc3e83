import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { getOrgById, getCurrentOrg } from '../../services/organization.js';

vi.mock('../../utils/utils.js', () => ({
  URL_DOMAIN: 'http://test-api.com',
}));

describe('Organization Service', () => {
  beforeEach(() => {
    vi.spyOn(window.localStorage, 'getItem').mockReturnValue('test-token');
    global.fetch = vi.fn();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should fetch organization by id', async () => {
    const mockResponse = { id: 1, name: 'Test Org' };
    global.fetch.mockResolvedValueOnce({
      json: vi.fn().mockResolvedValueOnce(mockResponse),
    });

    const result = await getOrgById(1);

    expect(global.fetch).toHaveBeenCalledWith(
      'http://test-api.com/organizations/1',
      expect.objectContaining({
        method: 'GET',
        headers: expect.objectContaining({
          'Authorization': 'Bearer test-token',
        }),
      })
    );
    expect(result).toEqual(mockResponse);
  });

  it('should fetch current organization', async () => {
    const mockResponse = { id: 2, name: 'Current Org' };
    global.fetch.mockResolvedValueOnce({
      json: vi.fn().mockResolvedValueOnce(mockResponse),
    });

    const result = await getCurrentOrg();

    expect(global.fetch).toHaveBeenCalledWith(
      'http://test-api.com/organization/current',
      expect.objectContaining({
        method: 'GET',
        headers: expect.objectContaining({
          'Authorization': 'Bearer test-token',
        }),
      })
    );
    expect(result).toEqual(mockResponse);
  });
});
