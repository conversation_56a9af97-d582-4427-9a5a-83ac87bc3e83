{"openapi": "3.0.0", "info": {"title": "raleon-webapp", "version": "0.0.1", "description": "raleon_webapp", "contact": {"name": "", "email": "<EMAIL>"}}, "paths": {"/admin/invite-users": {"post": {"x-controller-name": "InviteController", "x-operation-name": "create", "tags": ["InviteController"], "responses": {"200": {"description": "Invite model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Invite"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NewInviteRequest"}}}}}, "operationId": "InviteController.create"}}, "/admin/org-users/{orgId}": {"get": {"x-controller-name": "InviteController", "x-operation-name": "getUsersAndInvites", "tags": ["InviteController"], "responses": {"200": {"description": "Return value of InviteController.getUsersAndInvites"}}, "parameters": [{"name": "orgId", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "InviteController.getUsersAndInvites"}}, "/app-info": {"get": {"x-controller-name": "SegmentsController", "x-operation-name": "getAppInfo", "tags": ["SegmentsController"], "responses": {"200": {"description": "Returns Name, Category, Addres, and Exchange for a given app", "content": {"application/json": {}}}}, "parameters": [{"name": "network", "in": "query", "schema": {"type": "string"}}], "operationId": "SegmentsController.getAppInfo"}}, "/audiences/count": {"get": {"x-controller-name": "AudienceController", "x-operation-name": "count", "tags": ["AudienceController"], "responses": {"200": {"description": "Audience count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "Audience.Where<PERSON><PERSON>er", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Audience>"}}}}], "operationId": "AudienceController.count"}}, "/audiences/{id}": {"put": {"x-controller-name": "AudienceController", "x-operation-name": "replaceById", "tags": ["AudienceController"], "responses": {"204": {"description": "Audience was updated"}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "number"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Audience"}}}, "x-parameter-index": 1}, "operationId": "AudienceController.replaceById"}, "patch": {"x-controller-name": "AudienceController", "x-operation-name": "updateById", "tags": ["AudienceController"], "responses": {"204": {"description": "Audience was updated"}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "number"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AudiencePartial"}}}, "x-parameter-index": 1}, "operationId": "AudienceController.updateById"}, "get": {"x-controller-name": "AudienceController", "x-operation-name": "findById", "tags": ["AudienceController"], "responses": {"200": {"description": "Audience instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AudienceWithRelations"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "number"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Audience.Filter"}}}}], "operationId": "AudienceController.findById"}, "delete": {"x-controller-name": "AudienceController", "x-operation-name": "deleteById", "tags": ["AudienceController"], "responses": {"204": {"description": "Audience was deleted"}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "number"}, "required": true}], "operationId": "AudienceController.deleteById"}}, "/audiences": {"post": {"x-controller-name": "AudienceController", "x-operation-name": "create", "tags": ["AudienceController"], "responses": {"200": {"description": "Audience instance created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Audience"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewAudience"}}}}, "operationId": "AudienceController.create"}, "patch": {"x-controller-name": "AudienceController", "x-operation-name": "updateAll", "tags": ["AudienceController"], "responses": {"200": {"description": "Count of Audience models updated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "Audience.Where<PERSON><PERSON>er", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Audience>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AudiencePartial"}}}}, "operationId": "AudienceController.updateAll"}, "get": {"x-controller-name": "AudienceController", "x-operation-name": "find", "tags": ["AudienceController"], "responses": {"200": {"description": "Array of Audience instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AudienceWithRelations"}}}}}}, "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Audience.Filter"}}}}], "operationId": "AudienceController.find"}}, "/dashboards/count": {"get": {"x-controller-name": "DashboardController", "x-operation-name": "count", "tags": ["DashboardController"], "responses": {"200": {"description": "Dashboard count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "Dashboard.WhereF<PERSON>er", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Dashboard>"}}}}], "operationId": "DashboardController.count"}}, "/dashboards/{id}/widgets": {"post": {"x-controller-name": "DashboardWidgetController", "x-operation-name": "create", "tags": ["DashboardWidgetController"], "responses": {"200": {"description": "Dashboard model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Widget"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "number"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewWidgetInDashboard"}}}, "x-parameter-index": 1}, "operationId": "DashboardWidgetController.create"}, "patch": {"x-controller-name": "DashboardWidgetController", "x-operation-name": "patch", "tags": ["DashboardWidgetController"], "responses": {"200": {"description": "Dashboard.Widget PATCH success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "number"}, "required": true}, {"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "Widget.<PERSON><PERSON><PERSON><PERSON>", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Widget>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WidgetPartial"}}}, "x-parameter-index": 1}, "operationId": "DashboardWidgetController.patch"}, "get": {"x-controller-name": "DashboardWidgetController", "x-operation-name": "find", "tags": ["DashboardWidgetController"], "responses": {"200": {"description": "Array of Dashboard has many Widget", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Widget"}}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "number"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true}}}}], "operationId": "DashboardWidgetController.find"}, "delete": {"x-controller-name": "DashboardWidgetController", "x-operation-name": "delete", "tags": ["DashboardWidgetController"], "responses": {"200": {"description": "Dashboard.Widget DELETE success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "number"}, "required": true}, {"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "Widget.<PERSON><PERSON><PERSON><PERSON>", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Widget>"}}}}], "operationId": "DashboardWidgetController.delete"}}, "/dashboards/{id}": {"put": {"x-controller-name": "DashboardController", "x-operation-name": "replaceById", "tags": ["DashboardController"], "responses": {"204": {"description": "Dashboard was updated"}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "number"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Dashboard"}}}, "x-parameter-index": 1}, "operationId": "DashboardController.replaceById"}, "patch": {"x-controller-name": "DashboardController", "x-operation-name": "updateById", "tags": ["DashboardController"], "responses": {"204": {"description": "Dashboard was updated"}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "number"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DashboardPartial"}}}, "x-parameter-index": 1}, "operationId": "DashboardController.updateById"}, "get": {"x-controller-name": "DashboardController", "x-operation-name": "findById", "tags": ["DashboardController"], "responses": {"200": {"description": "Dashboard instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DashboardWithRelations"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "number"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Dashboard.Filter"}}}}], "operationId": "DashboardController.findById"}, "delete": {"x-controller-name": "DashboardController", "x-operation-name": "deleteById", "tags": ["DashboardController"], "responses": {"204": {"description": "Dashboard was deleted"}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "number"}, "required": true}], "operationId": "DashboardController.deleteById"}}, "/dashboards": {"post": {"x-controller-name": "DashboardController", "x-operation-name": "create", "tags": ["DashboardController"], "responses": {"200": {"description": "Dashboard instance created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Dashboard"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewDashboard"}}}}, "operationId": "DashboardController.create"}, "patch": {"x-controller-name": "DashboardController", "x-operation-name": "updateAll", "tags": ["DashboardController"], "responses": {"200": {"description": "Count of Dashboard models updated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "Dashboard.WhereF<PERSON>er", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Dashboard>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DashboardPartial"}}}}, "operationId": "DashboardController.updateAll"}, "get": {"x-controller-name": "DashboardController", "x-operation-name": "find", "tags": ["DashboardController"], "responses": {"200": {"description": "Array of Dashboard instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DashboardWithRelations"}}}}}}, "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Dashboard.Filter"}}}}], "operationId": "DashboardController.find"}}, "/data-connections/count": {"get": {"x-controller-name": "DataConnectionsController", "x-operation-name": "count", "tags": ["DataConnectionsController"], "responses": {"200": {"description": "DataConnections count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "DataConnections.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<DataConnections>"}}}}], "operationId": "DataConnectionsController.count"}}, "/data-connections/{id}": {"put": {"x-controller-name": "DataConnectionsController", "x-operation-name": "replaceById", "tags": ["DataConnectionsController"], "responses": {"204": {"description": "DataConnections was updated"}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "number"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataConnections"}}}, "x-parameter-index": 1}, "operationId": "DataConnectionsController.replaceById"}, "patch": {"x-controller-name": "DataConnectionsController", "x-operation-name": "updateById", "tags": ["DataConnectionsController"], "responses": {"204": {"description": "DataConnections was updated"}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "number"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataConnectionsPartial"}}}, "x-parameter-index": 1}, "operationId": "DataConnectionsController.updateById"}, "get": {"x-controller-name": "DataConnectionsController", "x-operation-name": "findById", "tags": ["DataConnectionsController"], "responses": {"200": {"description": "DataConnections instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataConnectionsWithRelations"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "number"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataConnections.Filter"}}}}], "operationId": "DataConnectionsController.findById"}, "delete": {"x-controller-name": "DataConnectionsController", "x-operation-name": "deleteById", "tags": ["DataConnectionsController"], "responses": {"204": {"description": "DataConnections was deleted"}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "number"}, "required": true}], "operationId": "DataConnectionsController.deleteById"}}, "/data-connections": {"post": {"x-controller-name": "DataConnectionsController", "x-operation-name": "create", "tags": ["DataConnectionsController"], "responses": {"200": {"description": "DataConnections instance created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataConnections"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewDataConnections"}}}}, "operationId": "DataConnectionsController.create"}, "patch": {"x-controller-name": "DataConnectionsController", "x-operation-name": "updateAll", "tags": ["DataConnectionsController"], "responses": {"200": {"description": "Count of DataConnections models updated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "DataConnections.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<DataConnections>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataConnectionsPartial"}}}}, "operationId": "DataConnectionsController.updateAll"}, "get": {"x-controller-name": "DataConnectionsController", "x-operation-name": "find", "tags": ["DataConnectionsController"], "responses": {"200": {"description": "Array of DataConnections instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DataConnectionsWithRelations"}}}}}}, "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataConnections.Filter"}}}}], "operationId": "DataConnectionsController.find"}}, "/event/{orgId}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getEventsForOrgByAddress", "tags": ["MetricController"], "responses": {"200": {"description": "Gets events for a given address, within a given org", "content": {"application/json": {"schema": {}}}}}, "parameters": [{"name": "orgId", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "MetricController.getEventsForOrgByAddress"}}, "/event/{orgId}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getEventsForOrg", "tags": ["MetricController"], "responses": {"200": {"description": "Gets events for a given address, within a given org", "content": {"application/json": {"schema": {}}}}}, "parameters": [{"name": "orgId", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "MetricController.getEventsForOrg"}}, "/event": {"get": {"x-controller-name": "SegmentsController", "x-operation-name": "getEvents", "tags": ["SegmentsController"], "responses": {"200": {"description": "Returns Events that have been registered for the provided org", "content": {"application/json": {}}}}, "parameters": [{"name": "orgId", "in": "query", "schema": {"type": "string"}}], "operationId": "SegmentsController.getEvents"}}, "/invites/{id}": {"patch": {"x-controller-name": "InviteController", "x-operation-name": "updateById", "tags": ["InviteController"], "responses": {"204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "Invite PATCH success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "number"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvitePartial"}}}, "x-parameter-index": 1}, "operationId": "InviteController.updateById"}}, "/metric/action-by-type/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getProjectInteractionsByType", "tags": ["MetricController"], "responses": {"200": {"description": "average transfer size", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InteractionsByType"}}}}}, "parameters": [{"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "enddate", "in": "query", "schema": {"type": "string"}}, {"name": "startdate", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getProjectInteractionsByType"}}, "/metric/active-user-percent-activity/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getProjectActiveUserPercentActivity", "tags": ["MetricController"], "responses": {"200": {"description": "active users percent of overall wallet activity, expressed as percent", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActiveUserPercentActivity"}}}}}, "parameters": [{"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "enddate", "in": "query", "schema": {"type": "string"}}, {"name": "startdate", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getProjectActiveUserPercentActivity"}}, "/metric/active-user-transaction-count/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getActiveUserInteractionsCount", "tags": ["MetricController"], "responses": {"200": {"description": "number of interactions from active users", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActiveUserInteractionCount"}}}}}, "parameters": [{"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "enddate", "in": "query", "schema": {"type": "string"}}, {"name": "startdate", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getActiveUserInteractionsCount"}}, "/metric/active-users/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getProjectActiveUsers", "tags": ["MetricController"], "responses": {"200": {"description": "last-transaction-date, returns a date with the last time a transaction was detected", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActiveUsers"}}}}}, "parameters": [{"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "enddate", "in": "query", "schema": {"type": "string"}}, {"name": "startdate", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getProjectActiveUsers"}}, "/metric/activities-active/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getActiveUserActivities", "tags": ["MetricController"], "responses": {"200": {"description": "Active Users - Activities", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewUserActivities"}}}}}, "parameters": [{"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "enddate", "in": "query", "schema": {"type": "string"}}, {"name": "startdate", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getActiveUserActivities"}}, "/metric/activities-at-risk/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getProjectAtRiskActivities", "tags": ["MetricController"], "responses": {"200": {"description": "average transfer size", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AtRiskActivities"}}}}}, "parameters": [{"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "enddate", "in": "query", "schema": {"type": "string"}}, {"name": "startdate", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getProjectAtRiskActivities"}}, "/metric/activities-by-wallet/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getWalletActivity", "tags": ["MetricController"], "responses": {"200": {"description": "Return list of wallets with a limit", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewUserActivities"}}}}}, "parameters": [{"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "MetricController.getWalletActivity"}}, "/metric/activities-dormant/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getDormantUserActivities", "tags": ["MetricController"], "responses": {"200": {"description": "Dormant Users - Activities", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewUserActivities"}}}}}, "parameters": [{"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "enddate", "in": "query", "schema": {"type": "string"}}, {"name": "startdate", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getDormantUserActivities"}}, "/metric/activities-new-user/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getProjectNewUserActivities", "tags": ["MetricController"], "responses": {"200": {"description": "average transfer size", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewUserActivities"}}}}}, "parameters": [{"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "enddate", "in": "query", "schema": {"type": "string"}}, {"name": "startdate", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getProjectNewUserActivities"}}, "/metric/address-balance/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getAccountBalance", "tags": ["MetricController"], "responses": {"200": {"description": "Returns the USD value of an address, value is in USD, not including NFTs", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddressBalance"}}}}}, "parameters": [{"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "enddate", "in": "query", "schema": {"type": "string"}}, {"name": "startdate", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getAccountBalance"}}, "/metric/at-risk-users/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getProjectAtRiskUsers", "tags": ["MetricController"], "responses": {"200": {"description": "last-transaction-date, returns a date with the last time a transaction was detected", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AtRiskUsers"}}}}}, "parameters": [{"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "enddate", "in": "query", "schema": {"type": "string"}}, {"name": "startdate", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getProjectAtRiskUsers"}}, "/metric/avg-token-transfer/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getProjectAvgTokenTransfer", "tags": ["MetricController"], "responses": {"200": {"description": "average transfer size", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HighestValueWallet"}}}}}, "parameters": [{"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "enddate", "in": "query", "schema": {"type": "string"}}, {"name": "startdate", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getProjectAvgTokenTransfer"}}, "/metric/common-tokens/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getProjectCommonTokensHeld", "tags": ["MetricController"], "responses": {"200": {"description": "average transfer size", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonTokensHeld"}}}}}, "parameters": [{"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "enddate", "in": "query", "schema": {"type": "string"}}, {"name": "startdate", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getProjectCommonTokensHeld"}}, "/metric/dormant-users/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getProjectDormantUsers", "tags": ["MetricController"], "responses": {"200": {"description": "last-transaction-date, returns a date with the last time a transaction was detected", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DormantUsers"}}}}}, "parameters": [{"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "enddate", "in": "query", "schema": {"type": "string"}}, {"name": "startdate", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getProjectDormantUsers"}}, "/metric/last-transaction-date/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getAddressLastTransaction", "tags": ["MetricController"], "responses": {"200": {"description": "last-transaction-date, returns a date with the last time a transaction was detected", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddressLastTransactionDate"}}}}}, "parameters": [{"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "enddate", "in": "query", "schema": {"type": "string"}}, {"name": "startdate", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getAddressLastTransaction"}}, "/metric/lifetime-transaction/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getLifetimeTransactions", "tags": ["MetricController"], "responses": {"200": {"description": "Lifetime-transaction, always returns the most recent calculated result", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LifetimeTransaction"}}}}}, "parameters": [{"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "enddate", "in": "query", "schema": {"type": "string"}}, {"name": "startdate", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getLifetimeTransactions"}}, "/metric/most-active-time/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getProjectMostActiveTime", "tags": ["MetricController"], "responses": {"200": {"description": "most active time", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MostActiveTime"}}}}}, "parameters": [{"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "enddate", "in": "query", "schema": {"type": "string"}}, {"name": "startdate", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getProjectMostActiveTime"}}, "/metric/new-users/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getProjectNewUsers", "tags": ["MetricController"], "responses": {"200": {"description": "returns new user count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewUsers"}}}}}, "parameters": [{"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "enddate", "in": "query", "schema": {"type": "string"}}, {"name": "startdate", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getProjectNewUsers"}}, "/metric/nfts/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getProjectCommonNFTS", "tags": ["MetricController"], "responses": {"200": {"description": "average transfer size", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonTokensHeld"}}}}}, "parameters": [{"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "enddate", "in": "query", "schema": {"type": "string"}}, {"name": "startdate", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getProjectCommonNFTS"}}, "/metric/persona-active-count/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getPersonaActiveCount", "tags": ["MetricController"], "responses": {"200": {"description": "Persona Active Count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewUserActivities"}}}}}, "parameters": [{"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "enddate", "in": "query", "schema": {"type": "string"}}, {"name": "startdate", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getPersonaActiveCount"}}, "/metric/proof/download": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getMetricProofDownload", "tags": ["MetricController"], "responses": {"200": {"description": "Wallet Ids as proofs for a metric", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MetricProof"}}}}}, "parameters": [{"name": "address", "in": "query", "schema": {"type": "string"}}, {"name": "network", "in": "query", "schema": {"type": "string"}}, {"name": "metricName", "in": "query", "schema": {"type": "string"}}, {"name": "dateProcessed", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getMetricProofDownload"}}, "/metric/proof": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getMetricProof", "tags": ["MetricController"], "responses": {"200": {"description": "Wallet Ids as proofs for a metric", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MetricProof"}}}}}, "parameters": [{"name": "address", "in": "query", "schema": {"type": "string"}}, {"name": "network", "in": "query", "schema": {"type": "string"}}, {"name": "metricName", "in": "query", "schema": {"type": "string"}}, {"name": "dateProcessed", "in": "query", "schema": {"type": "string"}}, {"name": "page", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getMetricProof"}}, "/metric/recovered-users-last-1/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getRecoveredUsersLast1", "tags": ["MetricController"], "responses": {"200": {"description": "Recovered Users Over the Last 24 hours", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewUserActivities"}}}}}, "parameters": [{"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "enddate", "in": "query", "schema": {"type": "string"}}, {"name": "startdate", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getRecoveredUsersLast1"}}, "/metric/returning-users-last-1/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getReturningUsersLast1Day", "tags": ["MetricController"], "responses": {"200": {"description": "Returning Users over last 24 hours", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewUserActivities"}}}}}, "parameters": [{"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "enddate", "in": "query", "schema": {"type": "string"}}, {"name": "startdate", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getReturningUsersLast1Day"}}, "/metric/returning-users-last-7/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getReturningUsersLast7Day", "tags": ["MetricController"], "responses": {"200": {"description": "Returning Users over last 7 days", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewUserActivities"}}}}}, "parameters": [{"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "enddate", "in": "query", "schema": {"type": "string"}}, {"name": "startdate", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getReturningUsersLast7Day"}}, "/metric/saved-users-last-1/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getSavedUserList", "tags": ["MetricController"], "responses": {"200": {"description": "Saved Users Over the Last 24 hours, At Risk -> Active", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewUserActivities"}}}}}, "parameters": [{"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "enddate", "in": "query", "schema": {"type": "string"}}, {"name": "startdate", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getSavedUserList"}}, "/metric/segment-activities/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getSegmentActivities", "tags": ["MetricController"], "responses": {"200": {"description": "Activities based on a segment of wallets", "content": {"application/json": {"schema": {}}}}}, "parameters": [{"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "enddate", "in": "query", "schema": {"type": "string"}}, {"name": "startdate", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getSegmentActivities"}}, "/metric/segment-common-tokens/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getSegmentCommonTokens", "tags": ["MetricController"], "responses": {"200": {"description": "NFTs based on a segment of wallets", "content": {"application/json": {"schema": {}}}}}, "parameters": [{"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "enddate", "in": "query", "schema": {"type": "string"}}, {"name": "startdate", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getSegmentCommonTokens"}}, "/metric/segment-count/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getSegmentCount", "tags": ["MetricController"], "responses": {"200": {"description": "Count for a given segment, note address is the viewname in this case", "content": {"application/json": {"schema": {}}}}}, "parameters": [{"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "enddate", "in": "query", "schema": {"type": "string"}}, {"name": "startdate", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getSegmentCount"}}, "/metric/segment-model-persona/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getSegmentModelPersona", "tags": ["MetricController"], "responses": {"200": {"description": "NFTs based on a segment of wallets", "content": {"application/json": {"schema": {}}}}}, "parameters": [{"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "enddate", "in": "query", "schema": {"type": "string"}}, {"name": "startdate", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getSegmentModelPersona"}}, "/metric/segment-nfts/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getSegmentNFTs", "tags": ["MetricController"], "responses": {"200": {"description": "NFTs based on a segment of wallets", "content": {"application/json": {"schema": {}}}}}, "parameters": [{"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "enddate", "in": "query", "schema": {"type": "string"}}, {"name": "startdate", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getSegmentNFTs"}}, "/metric/segment-usd-value/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getSegmentUSDValue", "tags": ["MetricController"], "responses": {"200": {"description": "Count for a given segment, note address is the viewname in this case", "content": {"application/json": {"schema": {}}}}}, "parameters": [{"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "enddate", "in": "query", "schema": {"type": "string"}}, {"name": "startdate", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getSegmentUSDValue"}}, "/metric/token-balances/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getAddressTokenBalances", "tags": ["MetricController"], "responses": {"200": {"description": "token-balances, returns an array of ticker tokens, that corresponds with an array of usd_values", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddressTokenBalance"}}}}}, "parameters": [{"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "enddate", "in": "query", "schema": {"type": "string"}}, {"name": "startdate", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getAddressTokenBalances"}}, "/metric/token-holders-usd-value/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getProjectTotalTokenHolderUSD", "tags": ["MetricController"], "responses": {"200": {"description": "usd value of all token holders", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TotalUSDValueOfWallets"}}}}}, "parameters": [{"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "enddate", "in": "query", "schema": {"type": "string"}}, {"name": "startdate", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getProjectTotalTokenHolderUSD"}}, "/metric/token-info/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getTokenInfo", "tags": ["MetricController"], "responses": {"200": {"description": "Returns the token-info for a given project (supply, holders, etc)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewUserActivities"}}}}}, "parameters": [{"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "enddate", "in": "query", "schema": {"type": "string"}}, {"name": "startdate", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getTokenInfo"}}, "/metric/top-eth-holder/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getProjectHighestValueWalletETH", "tags": ["MetricController"], "responses": {"200": {"description": "highest valued wallet by eth", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HighestValueWallet"}}}}}, "parameters": [{"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "enddate", "in": "query", "schema": {"type": "string"}}, {"name": "startdate", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getProjectHighestValueWalletETH"}}, "/metric/total-token-holders/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getProjectTokenHolders", "tags": ["MetricController"], "responses": {"200": {"description": "average transfer size", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TotalTokenHolders"}}}}}, "parameters": [{"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "enddate", "in": "query", "schema": {"type": "string"}}, {"name": "startdate", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getProjectTokenHolders"}}, "/metric/unique-wallets/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getUniqueWallets", "tags": ["MetricController"], "responses": {"200": {"description": "number of unique wallets", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UniqueWallets"}}}}}, "parameters": [{"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "enddate", "in": "query", "schema": {"type": "string"}}, {"name": "startdate", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getUniqueWallets"}}, "/metric/usd-value-all-users/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getProjectUSDValue", "tags": ["MetricController"], "responses": {"200": {"description": "returns usd value of all users, not all tokens are counted", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewUsers"}}}}}, "parameters": [{"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "enddate", "in": "query", "schema": {"type": "string"}}, {"name": "startdate", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getProjectUSDValue"}}, "/metric/utm/campaigns/{orgid}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getKnownCampaigns", "tags": ["MetricController"], "responses": {"200": {"description": "Return list of known campaigns for a given org", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewUserActivities"}}}}}, "parameters": [{"name": "orgid", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "MetricController.getKnownCampaigns"}}, "/metric/utm/connections/{orgid}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getConnections", "tags": ["MetricController"], "responses": {"200": {"description": "Return list of connections by given org and campaign", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewUserActivities"}}}}}, "parameters": [{"name": "orgid", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "campaignid", "in": "query", "schema": {"type": "string"}}, {"name": "eventtype", "in": "query", "schema": {"type": "string"}}, {"name": "days", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getConnections"}}, "/metric/utm/content/{orgid}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "get<PERSON>ontent", "tags": ["MetricController"], "responses": {"200": {"description": "Return list of content by given org and campaign", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewUserActivities"}}}}}, "parameters": [{"name": "orgid", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "campaignid", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getContent"}}, "/metric/utm/conversion-percent/{orgid}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getConversionPercent", "tags": ["MetricController"], "responses": {"200": {"description": "Return percent of conversions for campaign", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewUserActivities"}}}}}, "parameters": [{"name": "orgid", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "campaignid", "in": "query", "schema": {"type": "string"}}, {"name": "projectid", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getConversionPercent"}}, "/metric/utm/conversions/{orgid}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getConversionsByDay", "tags": ["MetricController"], "responses": {"200": {"description": "Return list of conversions by given org and campaign", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewUserActivities"}}}}}, "parameters": [{"name": "orgid", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "campaignid", "in": "query", "schema": {"type": "string"}}, {"name": "projectid", "in": "query", "schema": {"type": "string"}}, {"name": "days", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getConversionsByDay"}}, "/metric/utm/conversions-count/{orgid}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getConversionCount", "tags": ["MetricController"], "responses": {"200": {"description": "Return count of conversions for campaign", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewUserActivities"}}}}}, "parameters": [{"name": "orgid", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "campaignid", "in": "query", "schema": {"type": "string"}}, {"name": "projectid", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getConversionCount"}}, "/metric/utm/events/{orgid}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getEvents", "tags": ["MetricController"], "responses": {"200": {"description": "Return list of events by given org and campaign", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewUserActivities"}}}}}, "parameters": [{"name": "orgid", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "campaignid", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getEvents"}}, "/metric/utm/new-engagers/{orgid}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getNewEngagers", "tags": ["MetricController"], "responses": {"200": {"description": "Return count of new-engagers", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewUserActivities"}}}}}, "parameters": [{"name": "orgid", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "campaignid", "in": "query", "schema": {"type": "string"}}, {"name": "projectid", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getNewEngagers"}}, "/metric/utm/sources/{orgid}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getSources", "tags": ["MetricController"], "responses": {"200": {"description": "Return list of sources by given org and campaign", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewUserActivities"}}}}}, "parameters": [{"name": "orgid", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "campaignid", "in": "query", "schema": {"type": "string"}}, {"name": "projectid", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getSources"}}, "/metric/utm/wallet-connections/{orgid}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getWalletConnections", "tags": ["MetricController"], "responses": {"200": {"description": "Return list of connections by wallet address and org id", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewUserActivities"}}}}}, "parameters": [{"name": "orgid", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "address", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getWalletConnections"}}, "/metric/utm/wallet-sources/{orgId}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getUTMWalletSources", "tags": ["MetricController"], "responses": {"200": {"description": "Return list of connections by wallet address and org id", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewUserActivities"}}}}}, "parameters": [{"name": "orgId", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "address", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getUTMWalletSources"}}, "/metric/utm/wallets/{orgid}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getWalletList", "tags": ["MetricController"], "responses": {"200": {"description": "Return list of wallets with a limit", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewUserActivities"}}}}}, "parameters": [{"name": "orgid", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "campaignid", "in": "query", "schema": {"type": "string"}}, {"name": "projectid", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getWalletList"}}, "/metric/wallet-flow/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getInflowOutflow", "tags": ["MetricController"], "responses": {"200": {"description": "Wallet Share for given network and address", "content": {"application/json": {"schema": {}}}}}, "parameters": [{"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "MetricController.getInflowOutflow"}}, "/metric/wallet-nfts/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getNFTs", "tags": ["MetricController"], "responses": {"200": {"description": "Wallet Share for given network and address", "content": {"application/json": {"schema": {}}}}}, "parameters": [{"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "MetricController.getNFTs"}}, "/metric/wallet-share/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getWalletShare", "tags": ["MetricController"], "responses": {"200": {"description": "Wallet Share for given network and address", "content": {"application/json": {"schema": {}}}}}, "parameters": [{"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "MetricController.getWalletShare"}}, "/metric/{network}/{address}/{metricName}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getMetric", "tags": ["MetricController"], "responses": {"200": {"description": "Persona Active Count", "content": {"application/json": {"schema": {}}}}}, "parameters": [{"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "metricName", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "enddate", "in": "query", "schema": {"type": "string"}}, {"name": "startdate", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getMetric"}}, "/organization/accept-invite": {"post": {"x-controller-name": "InviteController", "x-operation-name": "accept", "tags": ["InviteController"], "responses": {"200": {"description": "Invite model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Invite"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AcceptInviteRequest"}}}, "$ref": "#/components/schemas/AcceptInviteRequest", "definitions": {"AcceptInviteRequest": {"$ref": "#/components/schemas/AcceptInviteRequest"}}}, "operationId": "InviteController.accept"}}, "/organization/invite/{inviteCode}": {"get": {"x-controller-name": "InviteController", "x-operation-name": "getInvite", "tags": ["InviteController"], "responses": {"200": {"description": "Invite model instance", "content": {"application/json": {"schema": {"type": "object", "items": {"$ref": "#/components/schemas/InviteWithRelations", "definitions": {"InviteWithRelations": {"$ref": "#/components/schemas/InviteWithRelations"}}}}}}}}, "parameters": [{"name": "inviteCode", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "InviteController.getInvite"}}, "/organizations/count": {"get": {"x-controller-name": "OrganizationController", "x-operation-name": "count", "tags": ["OrganizationController"], "responses": {"200": {"description": "Organization count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "Organization.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Organization>"}}}}], "operationId": "OrganizationController.count"}}, "/organizations/{id}": {"put": {"x-controller-name": "OrganizationController", "x-operation-name": "replaceById", "tags": ["OrganizationController"], "responses": {"204": {"description": "Organization was updated"}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "number"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Organization"}}}, "x-parameter-index": 1}, "operationId": "OrganizationController.replaceById"}, "patch": {"x-controller-name": "OrganizationController", "x-operation-name": "updateById", "tags": ["OrganizationController"], "responses": {"204": {"description": "Organization was updated"}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "number"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationPartial"}}}, "x-parameter-index": 1}, "operationId": "OrganizationController.updateById"}, "get": {"x-controller-name": "OrganizationController", "x-operation-name": "findById", "tags": ["OrganizationController"], "responses": {"200": {"description": "Organization instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationWithRelations"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "number"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Organization.Filter"}}}}], "operationId": "OrganizationController.findById"}, "delete": {"x-controller-name": "OrganizationController", "x-operation-name": "deleteById", "tags": ["OrganizationController"], "responses": {"204": {"description": "Organization was deleted"}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "number"}, "required": true}], "operationId": "OrganizationController.deleteById"}}, "/organizations": {"post": {"x-controller-name": "OrganizationController", "x-operation-name": "create", "tags": ["OrganizationController"], "responses": {"200": {"description": "Organization instance created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Organization"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewOrganization"}}}}, "operationId": "OrganizationController.create"}, "patch": {"x-controller-name": "OrganizationController", "x-operation-name": "updateAll", "tags": ["OrganizationController"], "responses": {"200": {"description": "Count of Organization models updated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "Organization.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Organization>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationPartial"}}}}, "operationId": "OrganizationController.updateAll"}, "get": {"x-controller-name": "OrganizationController", "x-operation-name": "find", "tags": ["OrganizationController"], "responses": {"200": {"description": "Array of Organization instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrganizationWithRelations"}}}}}}, "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Organization.Filter"}}}}], "operationId": "OrganizationController.find"}}, "/ping": {"get": {"x-controller-name": "PingController", "x-operation-name": "ping", "tags": ["PingController"], "responses": {"200": {"description": "Ping Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PingResponse"}}}}}, "operationId": "PingController.ping"}}, "/projects/count": {"get": {"x-controller-name": "ProjectController", "x-operation-name": "count", "tags": ["ProjectController"], "responses": {"200": {"description": "Project count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "Project.WhereF<PERSON>er", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Project>"}}}}], "operationId": "ProjectController.count"}}, "/projects/{id}/data-connections": {"post": {"x-controller-name": "ProjectDataConnectionsController", "x-operation-name": "create", "tags": ["ProjectDataConnectionsController"], "responses": {"200": {"description": "Project model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataConnections"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "number"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewDataConnectionsInProject"}}}, "x-parameter-index": 1}, "operationId": "ProjectDataConnectionsController.create"}, "patch": {"x-controller-name": "ProjectDataConnectionsController", "x-operation-name": "patch", "tags": ["ProjectDataConnectionsController"], "responses": {"200": {"description": "Project.DataConnections PATCH success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "number"}, "required": true}, {"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "DataConnections.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<DataConnections>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataConnectionsPartial"}}}, "x-parameter-index": 1}, "operationId": "ProjectDataConnectionsController.patch"}, "get": {"x-controller-name": "ProjectDataConnectionsController", "x-operation-name": "find", "tags": ["ProjectDataConnectionsController"], "responses": {"200": {"description": "Array of Project has many DataConnections", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DataConnections"}}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "number"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true}}}}], "operationId": "ProjectDataConnectionsController.find"}, "delete": {"x-controller-name": "ProjectDataConnectionsController", "x-operation-name": "delete", "tags": ["ProjectDataConnectionsController"], "responses": {"200": {"description": "Project.DataConnections DELETE success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "number"}, "required": true}, {"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "DataConnections.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<DataConnections>"}}}}], "operationId": "ProjectDataConnectionsController.delete"}}, "/projects/{id}": {"put": {"x-controller-name": "ProjectController", "x-operation-name": "replaceById", "tags": ["ProjectController"], "responses": {"204": {"description": "Project was updated"}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "number"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Project"}}}, "x-parameter-index": 1}, "operationId": "ProjectController.replaceById"}, "patch": {"x-controller-name": "ProjectController", "x-operation-name": "updateById", "tags": ["ProjectController"], "responses": {"204": {"description": "Project was updated"}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "number"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectPartial"}}}, "x-parameter-index": 1}, "operationId": "ProjectController.updateById"}, "get": {"x-controller-name": "ProjectController", "x-operation-name": "findById", "tags": ["ProjectController"], "responses": {"200": {"description": "Project instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectWithRelations"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "number"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Project.Filter"}}}}], "operationId": "ProjectController.findById"}, "delete": {"x-controller-name": "ProjectController", "x-operation-name": "deleteById", "tags": ["ProjectController"], "responses": {"204": {"description": "Project was deleted"}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "number"}, "required": true}], "operationId": "ProjectController.deleteById"}}, "/projects": {"post": {"x-controller-name": "ProjectController", "x-operation-name": "create", "tags": ["ProjectController"], "responses": {"200": {"description": "Project instance created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Project"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewProject"}}}}, "operationId": "ProjectController.create"}, "patch": {"x-controller-name": "ProjectController", "x-operation-name": "updateAll", "tags": ["ProjectController"], "responses": {"200": {"description": "Count of Project models updated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "Project.WhereF<PERSON>er", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Project>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectPartial"}}}}, "operationId": "ProjectController.updateAll"}, "get": {"x-controller-name": "ProjectController", "x-operation-name": "find", "tags": ["ProjectController"], "responses": {"200": {"description": "Array of Project instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectWithRelations"}}}}}}, "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Project.Filter"}}}}], "operationId": "ProjectController.find"}}, "/segment/preview": {"post": {"x-controller-name": "SegmentsController", "x-operation-name": "previewSegment", "tags": ["SegmentsController"], "responses": {"200": {"description": "Previews a Segment", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Segment"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Segment"}}}, "$ref": "#/components/schemas/Segment", "definitions": {"Segment": {"$ref": "#/components/schemas/Segment"}}}, "operationId": "SegmentsController.previewSegment"}}, "/segment": {"post": {"x-controller-name": "SegmentsController", "x-operation-name": "buildSegment", "tags": ["SegmentsController"], "responses": {"200": {"description": "Creates a new segment definition in AWS for metric processing", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Segment"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Segment"}}}, "$ref": "#/components/schemas/Segment", "definitions": {"Segment": {"$ref": "#/components/schemas/Segment"}}}, "operationId": "SegmentsController.buildSegment"}, "get": {"x-controller-name": "SegmentsController", "x-operation-name": "executeSegment", "tags": ["SegmentsController"], "responses": {"200": {"description": "executes an existing segment", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Segment"}}}}}, "parameters": [{"name": "name", "in": "query", "schema": {"type": "string"}}, {"name": "network", "in": "query", "schema": {"type": "string"}}, {"name": "page", "in": "query", "schema": {"type": "string"}}, {"name": "pagesize", "in": "query", "schema": {"type": "string"}}, {"name": "orgid", "in": "query", "schema": {"type": "string"}}, {"name": "table", "in": "query", "schema": {"type": "string"}}, {"name": "queryid", "in": "query", "schema": {"type": "string"}}], "operationId": "SegmentsController.executeSegment"}, "delete": {"x-controller-name": "SegmentsController", "x-operation-name": "deleteSegment", "tags": ["SegmentsController"], "responses": {"204": {"description": "Deletes a segment"}}, "parameters": [{"name": "name", "in": "query", "schema": {"type": "string"}}, {"name": "network", "in": "query", "schema": {"type": "string"}}, {"name": "orgid", "in": "query", "schema": {"type": "string"}}], "operationId": "SegmentsController.deleteSegment"}}, "/segments/count": {"get": {"x-controller-name": "SegmentController", "x-operation-name": "count", "tags": ["SegmentController"], "responses": {"200": {"description": "Segment count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "Segment.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Segment>"}}}}], "operationId": "SegmentController.count"}}, "/segments/{id}": {"put": {"x-controller-name": "SegmentController", "x-operation-name": "replaceById", "tags": ["SegmentController"], "responses": {"204": {"description": "Segment was updated"}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "number"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Segment"}}}, "x-parameter-index": 1}, "operationId": "SegmentController.replaceById"}, "patch": {"x-controller-name": "SegmentController", "x-operation-name": "updateById", "tags": ["SegmentController"], "responses": {"204": {"description": "Segment was updated"}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "number"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SegmentPartial"}}}, "x-parameter-index": 1}, "operationId": "SegmentController.updateById"}, "get": {"x-controller-name": "SegmentController", "x-operation-name": "findById", "tags": ["SegmentController"], "responses": {"200": {"description": "Segment instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SegmentWithRelations"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "number"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Segment.Filter"}}}}], "operationId": "SegmentController.findById"}, "delete": {"x-controller-name": "SegmentController", "x-operation-name": "deleteById", "tags": ["SegmentController"], "responses": {"204": {"description": "Segment was deleted"}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "number"}, "required": true}], "operationId": "SegmentController.deleteById"}}, "/segments": {"post": {"x-controller-name": "SegmentController", "x-operation-name": "create", "tags": ["SegmentController"], "responses": {"200": {"description": "Segment instance created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Segment"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewSegment"}}}}, "operationId": "SegmentController.create"}, "patch": {"x-controller-name": "SegmentController", "x-operation-name": "updateAll", "tags": ["SegmentController"], "responses": {"200": {"description": "Count of Segment models updated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "Segment.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Segment>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SegmentPartial"}}}}, "operationId": "SegmentController.updateAll"}, "get": {"x-controller-name": "SegmentController", "x-operation-name": "find", "tags": ["SegmentController"], "responses": {"200": {"description": "Array of Segment instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SegmentWithRelations"}}}}}}, "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Segment.Filter"}}}}], "operationId": "SegmentController.find"}}, "/tags/{network}/{address}": {"get": {"x-controller-name": "MetricController", "x-operation-name": "getWalletTags", "tags": ["MetricController"], "responses": {"200": {"description": "List of Wallet Tags", "content": {"application/json": {"schema": {}}}}}, "parameters": [{"name": "network", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "address", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "organization", "in": "query", "schema": {"type": "string"}}], "operationId": "MetricController.getWalletTags"}}, "/user": {"post": {"x-controller-name": "UserManagementController", "x-operation-name": "create", "tags": ["UserManagementController"], "responses": {"200": {"description": "Create User", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewUserRequest"}}}, "$ref": "#/components/schemas/NewUserRequest", "definitions": {"NewUserRequest": {"$ref": "#/components/schemas/NewUserRequest"}}}, "operationId": "UserManagementController.create"}}, "/user-credentials/count": {"get": {"x-controller-name": "UserCredentialsController", "x-operation-name": "count", "tags": ["UserCredentialsController"], "responses": {"200": {"description": "UserCredentials count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "UserCredentials.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<UserCredentials>"}}}}], "operationId": "UserCredentialsController.count"}}, "/user-credentials/{id}": {"put": {"x-controller-name": "UserCredentialsController", "x-operation-name": "replaceById", "tags": ["UserCredentialsController"], "responses": {"204": {"description": "UserCredentials was updated"}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCredentials"}}}, "x-parameter-index": 1}, "operationId": "UserCredentialsController.replaceById"}, "patch": {"x-controller-name": "UserCredentialsController", "x-operation-name": "updateById", "tags": ["UserCredentialsController"], "responses": {"204": {"description": "UserCredentials was updated"}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCredentialsPartial"}}}, "x-parameter-index": 1}, "operationId": "UserCredentialsController.updateById"}, "get": {"x-controller-name": "UserCredentialsController", "x-operation-name": "findById", "tags": ["UserCredentialsController"], "responses": {"200": {"description": "UserCredentials instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCredentialsWithRelations"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCredentials.Filter"}}}}], "operationId": "UserCredentialsController.findById"}, "delete": {"x-controller-name": "UserCredentialsController", "x-operation-name": "deleteById", "tags": ["UserCredentialsController"], "responses": {"204": {"description": "UserCredentials was deleted"}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "UserCredentialsController.deleteById"}}, "/user-credentials": {"post": {"x-controller-name": "UserCredentialsController", "x-operation-name": "create", "tags": ["UserCredentialsController"], "responses": {"200": {"description": "UserCredentials instance created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCredentials"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewUserCredentials"}}}}, "operationId": "UserCredentialsController.create"}, "patch": {"x-controller-name": "UserCredentialsController", "x-operation-name": "updateAll", "tags": ["UserCredentialsController"], "responses": {"200": {"description": "Count of UserCredentials models updated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "UserCredentials.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<UserCredentials>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCredentialsPartial"}}}}, "operationId": "UserCredentialsController.updateAll"}, "get": {"x-controller-name": "UserCredentialsController", "x-operation-name": "find", "tags": ["UserCredentialsController"], "responses": {"200": {"description": "Array of UserCredentials instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserCredentialsWithRelations"}}}}}}, "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCredentials.Filter"}}}}], "operationId": "UserCredentialsController.find"}}, "/users/count": {"get": {"x-controller-name": "UserController", "x-operation-name": "count", "tags": ["UserController"], "responses": {"200": {"description": "User count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "User.<PERSON><PERSON><PERSON>er", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<User>"}}}}], "operationId": "UserController.count"}}, "/users/forgot-password": {"put": {"x-controller-name": "UserManagementController", "x-operation-name": "forgotPassword", "tags": ["UserManagementController"], "security": [{"jwt": []}], "responses": {"200": {"description": "The updated user profile", "content": {"application/json": {"schema": {"type": "object", "required": ["id"], "properties": {"id": {"type": "string"}, "email": {"type": "string"}, "name": {"type": "string"}}}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["email", "password"], "properties": {"email": {"type": "string", "format": "email"}, "password": {"type": "string", "minLength": 8}}}}}, "description": "The input of password reset function", "required": true}, "operationId": "UserManagementController.forgotPassword"}}, "/users/login": {"post": {"x-controller-name": "UserManagementController", "x-operation-name": "login", "tags": ["UserManagementController"], "responses": {"200": {"description": "Token", "content": {"application/json": {"schema": {"type": "object", "properties": {"token": {"type": "string"}}}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["email", "password"], "properties": {"email": {"type": "string", "format": "email"}, "password": {"type": "string", "minLength": 8}}}}}, "description": "The input of login function", "required": true}, "operationId": "UserManagementController.login"}}, "/users/reset-password/finish": {"put": {"x-controller-name": "UserManagementController", "x-operation-name": "resetPasswordFinish", "tags": ["UserManagementController"], "responses": {"200": {"description": "A successful password reset response"}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/KeyAndPassword"}}}}, "operationId": "UserManagementController.resetPasswordFinish"}}, "/users/reset-password/init": {"post": {"x-controller-name": "UserManagementController", "x-operation-name": "resetPasswordInit", "tags": ["UserManagementController"], "responses": {"200": {"description": "Confirmation that reset password email has been sent"}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordInit"}}}}, "operationId": "UserManagementController.resetPasswordInit"}}, "/users/who-am-i": {"get": {"x-controller-name": "UserManagementController", "x-operation-name": "printCurrentUser", "tags": ["UserManagementController"], "security": [{"jwt": []}], "responses": {"200": {"description": "The current user profile", "content": {"application/json": {"schema": {"type": "object", "required": ["id"], "properties": {"id": {"type": "string"}, "email": {"type": "string"}, "name": {"type": "string"}}}}}}}, "operationId": "UserManagementController.printCurrentUser"}}, "/users/{userId}": {"put": {"x-controller-name": "UserManagementController", "x-operation-name": "set", "tags": ["UserManagementController"], "security": [{"jwt": []}], "responses": {"200": {"description": "User", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}, "parameters": [{"name": "userId", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}, "description": "update user", "x-parameter-index": 1}, "operationId": "UserManagementController.set"}, "get": {"x-controller-name": "UserManagementController", "x-operation-name": "findById", "tags": ["UserManagementController"], "security": [{"jwt": []}], "responses": {"200": {"description": "User", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}, "parameters": [{"name": "userId", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "UserManagementController.findById"}}, "/users/{id}": {"put": {"x-controller-name": "UserController", "x-operation-name": "replaceById", "tags": ["UserController"], "responses": {"204": {"description": "User was updated"}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}, "x-parameter-index": 1}, "operationId": "UserController.replaceById"}, "patch": {"x-controller-name": "UserController", "x-operation-name": "updateById", "tags": ["UserController"], "responses": {"204": {"description": "User was updated"}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserPartial"}}}, "x-parameter-index": 1}, "operationId": "UserController.updateById"}, "get": {"x-controller-name": "UserController", "x-operation-name": "findById", "tags": ["UserController"], "responses": {"200": {"description": "User instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserWithRelations"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User.Filter"}}}}], "operationId": "UserController.findById"}, "delete": {"x-controller-name": "UserController", "x-operation-name": "deleteById", "tags": ["UserController"], "responses": {"204": {"description": "User was deleted"}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "UserController.deleteById"}}, "/users": {"post": {"x-controller-name": "UserController", "x-operation-name": "create", "tags": ["UserController"], "responses": {"200": {"description": "User instance created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewUser"}}}}, "operationId": "UserController.create"}, "patch": {"x-controller-name": "UserController", "x-operation-name": "updateAll", "tags": ["UserController"], "responses": {"200": {"description": "Count of User models updated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "User.<PERSON><PERSON><PERSON>er", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<User>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserPartial"}}}}, "operationId": "UserController.updateAll"}, "get": {"x-controller-name": "UserController", "x-operation-name": "find", "tags": ["UserController"], "responses": {"200": {"description": "Array of User instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserWithRelations"}}}}}}, "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User.Filter"}}}}], "operationId": "UserController.find"}}, "/wallets-overview": {"post": {"x-controller-name": "MetricController", "x-operation-name": "buildSegment", "tags": ["MetricController"], "responses": {"200": {"description": "Creates a new segment definition in AWS for metric processing", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WalletOverview"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WalletOverview"}}}, "$ref": "#/components/schemas/WalletOverview", "definitions": {"WalletOverview": {"$ref": "#/components/schemas/WalletOverview"}}}, "operationId": "MetricController.buildSegment"}}, "/widgets/count": {"get": {"x-controller-name": "WidgetController", "x-operation-name": "count", "tags": ["WidgetController"], "responses": {"200": {"description": "Widget count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "Widget.<PERSON><PERSON><PERSON><PERSON>", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Widget>"}}}}], "operationId": "WidgetController.count"}}, "/widgets/{id}": {"put": {"x-controller-name": "WidgetController", "x-operation-name": "replaceById", "tags": ["WidgetController"], "responses": {"204": {"description": "Widget was updated"}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "number"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Widget"}}}, "x-parameter-index": 1}, "operationId": "WidgetController.replaceById"}, "patch": {"x-controller-name": "WidgetController", "x-operation-name": "updateById", "tags": ["WidgetController"], "responses": {"204": {"description": "Widget was updated"}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "number"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WidgetPartial"}}}, "x-parameter-index": 1}, "operationId": "WidgetController.updateById"}, "get": {"x-controller-name": "WidgetController", "x-operation-name": "findById", "tags": ["WidgetController"], "responses": {"200": {"description": "Widget instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WidgetWithRelations"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "number"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Widget.Filter"}}}}], "operationId": "WidgetController.findById"}, "delete": {"x-controller-name": "WidgetController", "x-operation-name": "deleteById", "tags": ["WidgetController"], "responses": {"204": {"description": "Widget was deleted"}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "number"}, "required": true}], "operationId": "WidgetController.deleteById"}}, "/widgets": {"post": {"x-controller-name": "WidgetController", "x-operation-name": "create", "tags": ["WidgetController"], "responses": {"200": {"description": "Widget instance created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Widget"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewWidget"}}}}, "operationId": "WidgetController.create"}, "patch": {"x-controller-name": "WidgetController", "x-operation-name": "updateAll", "tags": ["WidgetController"], "responses": {"200": {"description": "Count of Widget models updated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "Widget.<PERSON><PERSON><PERSON><PERSON>", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Widget>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WidgetPartial"}}}}, "operationId": "WidgetController.updateAll"}, "get": {"x-controller-name": "WidgetController", "x-operation-name": "find", "tags": ["WidgetController"], "responses": {"200": {"description": "Array of Widget instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WidgetWithRelations"}}}}}}, "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Widget.Filter"}}}}], "operationId": "WidgetController.find"}}}, "servers": [{"url": "/"}], "components": {"schemas": {"MetricProof": {"title": "MetricProof", "type": "object", "properties": {"walletIds": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": true}, "AddressBalance": {"title": "AddressBalance", "type": "object", "properties": {"address": {"type": "string"}, "total": {"type": "number"}}, "required": ["address", "total"], "additionalProperties": false}, "LifetimeTransaction": {"title": "LifetimeTransaction", "type": "object", "properties": {"address_network_metric": {"type": "string"}, "address": {"type": "string"}, "date_processed": {"type": "string"}, "transaction_count": {"type": "string"}, "metric": {"type": "string"}}, "required": ["address_network_metric", "address", "date_processed", "transaction_count", "metric"], "additionalProperties": true}, "AddressTokenBalance": {"title": "AddressTokenBalance", "type": "object", "properties": {"address": {"type": "string"}, "token_tickers": {"type": "array", "items": {"type": "string"}}, "usd_values": {"type": "array", "items": {"type": "number"}}}, "required": ["address", "token_tickers", "usd_values"], "additionalProperties": false}, "AddressLastTransactionDate": {"title": "AddressLastTransactionDate", "type": "object", "properties": {"address": {"type": "string"}, "network": {"type": "string"}}, "required": ["address", "network"], "additionalProperties": false}, "NewUsers": {"title": "NewUsers", "type": "object", "properties": {"address": {"type": "string"}, "network": {"type": "string"}, "startDate": {"type": "string"}, "endDate": {"type": "string"}}, "required": ["address", "network"], "additionalProperties": true}, "ActiveUsers": {"title": "ActiveUsers", "type": "object", "properties": {"address": {"type": "string"}, "network": {"type": "string"}}, "required": ["address", "network"], "additionalProperties": true}, "AtRiskUsers": {"title": "AtRiskUsers", "type": "object", "properties": {"address": {"type": "string"}, "network": {"type": "string"}}, "required": ["address", "network"], "additionalProperties": true}, "DormantUsers": {"title": "DormantUsers", "type": "object", "properties": {"address": {"type": "string"}, "network": {"type": "string"}, "startDate": {"type": "string"}, "endDate": {"type": "string"}}, "required": ["address", "network"], "additionalProperties": true}, "ActiveUserPercentActivity": {"title": "ActiveUserPercentActivity", "type": "object", "properties": {"address": {"type": "string"}, "network": {"type": "string"}}, "required": ["address", "network"], "additionalProperties": true}, "UniqueWallets": {"title": "UniqueWallets", "type": "object", "properties": {"address": {"type": "string"}, "network": {"type": "string"}}, "required": ["address", "network"], "additionalProperties": true}, "ActiveUserInteractionCount": {"title": "ActiveUserInteractionCount", "type": "object", "properties": {"address": {"type": "string"}, "network": {"type": "string"}}, "required": ["address", "network"], "additionalProperties": true}, "TotalUSDValueOfWallets": {"title": "TotalUSDValueOfWallets", "type": "object", "properties": {"address": {"type": "string"}, "network": {"type": "string"}}, "required": ["address", "network"], "additionalProperties": true}, "HighestValueWallet": {"title": "HighestValueWallet", "type": "object", "properties": {"address": {"type": "string"}, "network": {"type": "string"}}, "required": ["address", "network"], "additionalProperties": true}, "MostActiveTime": {"title": "MostActiveTime", "type": "object", "properties": {"address": {"type": "string"}, "network": {"type": "string"}}, "required": ["address", "network"], "additionalProperties": true}, "TotalTokenHolders": {"title": "TotalTokenHolders", "type": "object", "properties": {"address": {"type": "string"}, "network": {"type": "string"}}, "required": ["address", "network"], "additionalProperties": true}, "InteractionsByType": {"title": "InteractionsByType", "type": "object", "properties": {"address": {"type": "string"}, "network": {"type": "string"}}, "required": ["address", "network"], "additionalProperties": true}, "CommonTokensHeld": {"title": "CommonTokensHeld", "type": "object", "properties": {"address": {"type": "string"}, "network": {"type": "string"}}, "required": ["address", "network"], "additionalProperties": true}, "AtRiskActivities": {"title": "AtRiskActivities", "type": "object", "properties": {"address": {"type": "string"}, "network": {"type": "string"}}, "required": ["address", "network"], "additionalProperties": true}, "NewUserActivities": {"title": "NewUserActivities", "type": "object", "properties": {"address": {"type": "string"}, "network": {"type": "string"}}, "required": ["address", "network"], "additionalProperties": true}, "WalletOverview": {"title": "WalletOverview", "type": "object", "properties": {"addresses": {"type": "array", "items": {"type": "string"}}, "network": {"type": "string"}}, "required": ["addresses", "network"], "additionalProperties": false}, "Widget": {"title": "Widget", "type": "object", "properties": {"id": {"type": "number"}, "type": {"type": "string"}, "data": {"type": "string"}, "name": {"type": "string"}, "label": {"type": "string"}, "projectId": {"type": "string"}, "size": {"type": "string"}, "dashboardId": {"type": "number"}}, "required": ["name", "projectId"], "additionalProperties": true}, "NewWidgetInDashboard": {"title": "NewWidgetInDashboard", "type": "object", "description": "(tsType: @loopback/repository-json-schema#Optional<Omit<Widget, 'id'>, 'dashboardId'>, schemaOptions: { title: 'NewWidgetInDashboard', exclude: [ 'id' ], optional: [ 'dashboardId' ] })", "properties": {"type": {"type": "string"}, "data": {"type": "string"}, "name": {"type": "string"}, "label": {"type": "string"}, "projectId": {"type": "string"}, "size": {"type": "string"}, "dashboardId": {"type": "number"}}, "required": ["name", "projectId"], "additionalProperties": true, "x-typescript-type": "@loopback/repository-json-schema#Optional<Omit<Widget, 'id'>, 'dashboardId'>"}, "WidgetPartial": {"title": "WidgetPartial", "type": "object", "description": "(tsType: Partial<Widget>, schemaOptions: { partial: true })", "properties": {"id": {"type": "number"}, "type": {"type": "string"}, "data": {"type": "string"}, "name": {"type": "string"}, "label": {"type": "string"}, "projectId": {"type": "string"}, "size": {"type": "string"}, "dashboardId": {"type": "number"}}, "additionalProperties": true, "x-typescript-type": "Partial<Widget>"}, "Invite": {"title": "Invite", "type": "object", "properties": {"id": {"type": "number"}, "email": {"type": "string"}, "orgId": {"type": "number"}, "dateSent": {"type": "string", "format": "date-time"}, "inviteCode": {"type": "string"}, "accepted": {"type": "boolean"}, "expirationDate": {"type": "string", "format": "date-time"}}, "required": ["email", "orgId"], "additionalProperties": true}, "NewInviteRequest": {"title": "NewInviteRequest", "type": "object", "properties": {"adminEmail": {"type": "string"}, "email": {"type": "string"}}, "required": ["adminEmail", "email"], "additionalProperties": false}, "AcceptInviteRequest": {"title": "AcceptInviteRequest", "type": "object", "properties": {"email": {"type": "string"}, "password": {"type": "string"}, "inviteCode": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}}, "required": ["email", "password", "inviteCode", "firstName", "lastName"], "additionalProperties": false}, "InvitePartial": {"title": "InvitePartial", "type": "object", "description": "(tsType: Partial<Invite>, schemaOptions: { partial: true })", "properties": {"id": {"type": "number"}, "email": {"type": "string"}, "orgId": {"type": "number"}, "dateSent": {"type": "string", "format": "date-time"}, "inviteCode": {"type": "string"}, "accepted": {"type": "boolean"}, "expirationDate": {"type": "string", "format": "date-time"}}, "additionalProperties": true, "x-typescript-type": "Partial<Invite>"}, "DataConnections": {"title": "DataConnections", "type": "object", "properties": {"name": {"type": "string"}, "id": {"type": "number"}, "description": {"type": "string"}, "type": {"type": "string"}, "address": {"type": "string"}, "startDate": {"type": "string", "format": "date-time"}, "projectId": {"type": "number"}}, "required": ["name"], "additionalProperties": true}, "NewDataConnectionsInProject": {"title": "NewDataConnectionsInProject", "type": "object", "description": "(tsType: @loopback/repository-json-schema#Optional<Omit<DataConnections, 'id'>, 'projectId'>, schemaOptions: { title: 'NewDataConnectionsInProject', exclude: [ 'id' ], optional: [ 'projectId' ] })", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "address": {"type": "string"}, "startDate": {"type": "string", "format": "date-time"}, "projectId": {"type": "number"}}, "required": ["name"], "additionalProperties": true, "x-typescript-type": "@loopback/repository-json-schema#Optional<Omit<DataConnections, 'id'>, 'projectId'>"}, "DataConnectionsPartial": {"title": "DataConnectionsPartial", "type": "object", "description": "(tsType: Partial<DataConnections>, schemaOptions: { partial: true })", "properties": {"name": {"type": "string"}, "id": {"type": "number"}, "description": {"type": "string"}, "type": {"type": "string"}, "address": {"type": "string"}, "startDate": {"type": "string", "format": "date-time"}, "projectId": {"type": "number"}}, "additionalProperties": true, "x-typescript-type": "Partial<DataConnections>"}, "Segment": {"title": "Segment", "type": "object", "properties": {"id": {"type": "number"}, "name": {"type": "string"}, "network": {"type": "string"}, "orgid": {"type": "number"}, "queries": {"type": "array", "items": {}}, "queryid": {"type": "string"}, "description": {"type": "string"}, "status": {"type": "string"}, "addressCount": {"type": "number"}, "viewname": {"type": "string"}}, "required": ["name", "network", "orgid", "queries"], "additionalProperties": false}, "User": {"title": "User", "type": "object", "properties": {"id": {"type": "string"}, "email": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "avatarColors": {"type": "string"}, "resetKey": {"type": "string"}, "resetCount": {"type": "number"}, "resetTimestamp": {"type": "string"}, "resetKeyTimestamp": {"type": "string"}, "roles": {"type": "array", "items": {"type": "string"}}, "OrganizationId": {"type": "number"}}, "required": ["email"], "additionalProperties": false}, "NewUserRequest": {"title": "NewUserRequest", "type": "object", "properties": {"email": {"type": "string"}, "password": {"type": "string"}}, "required": ["email", "password"], "additionalProperties": false}, "ResetPasswordInit": {"title": "ResetPasswordInit", "type": "object", "properties": {"email": {"type": "string"}}, "required": ["email"], "additionalProperties": false}, "KeyAndPassword": {"title": "KeyAndPassword", "type": "object", "properties": {"resetKey": {"type": "string"}, "password": {"type": "string"}, "confirmPassword": {"type": "string"}}, "additionalProperties": false}, "Audience": {"title": "Audience", "type": "object", "properties": {"name": {"type": "string"}, "id": {"type": "number"}, "description": {"type": "string"}, "createdDate": {"type": "string", "format": "date-time"}}, "required": ["name"], "additionalProperties": true}, "NewAudience": {"title": "NewAudience", "type": "object", "description": "(tsType: Omit<Audience, 'id'>, schemaOptions: { title: 'NewAudience', exclude: [ 'id' ] })", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "createdDate": {"type": "string", "format": "date-time"}}, "required": ["name"], "additionalProperties": true, "x-typescript-type": "Omit<Audience, 'id'>"}, "AudienceWithRelations": {"title": "AudienceWithRelations", "type": "object", "description": "(tsType: AudienceWithRelations, schemaOptions: { includeRelations: true })", "properties": {"name": {"type": "string"}, "id": {"type": "number"}, "description": {"type": "string"}, "createdDate": {"type": "string", "format": "date-time"}}, "required": ["name"], "additionalProperties": true, "x-typescript-type": "AudienceWithRelations"}, "AudiencePartial": {"title": "AudiencePartial", "type": "object", "description": "(tsType: Partial<Audience>, schemaOptions: { partial: true })", "properties": {"name": {"type": "string"}, "id": {"type": "number"}, "description": {"type": "string"}, "createdDate": {"type": "string", "format": "date-time"}}, "additionalProperties": true, "x-typescript-type": "Partial<Audience>"}, "Dashboard": {"title": "Dashboard", "type": "object", "properties": {"name": {"type": "string"}, "id": {"type": "number"}, "organizationId": {"type": "number"}, "description": {"type": "string"}, "ownerId": {"type": "string"}, "shared": {"type": "number"}}, "required": ["name"], "additionalProperties": true}, "NewDashboard": {"title": "NewDashboard", "type": "object", "description": "(tsType: Omit<Dashboard, 'id'>, schemaOptions: { title: 'NewDashboard', exclude: [ 'id' ] })", "properties": {"name": {"type": "string"}, "organizationId": {"type": "number"}, "description": {"type": "string"}, "ownerId": {"type": "string"}, "shared": {"type": "number"}}, "required": ["name"], "additionalProperties": true, "x-typescript-type": "Omit<Dashboard, 'id'>"}, "WidgetWithRelations": {"title": "WidgetWithRelations", "type": "object", "description": "(tsType: WidgetWithRelations, schemaOptions: { includeRelations: true })", "properties": {"id": {"type": "number"}, "type": {"type": "string"}, "data": {"type": "string"}, "name": {"type": "string"}, "label": {"type": "string"}, "projectId": {"type": "string"}, "size": {"type": "string"}, "dashboardId": {"type": "number"}}, "required": ["name", "projectId"], "additionalProperties": true, "x-typescript-type": "WidgetWithRelations"}, "DashboardWithRelations": {"title": "DashboardWithRelations", "type": "object", "description": "(tsType: DashboardWithRelations, schemaOptions: { includeRelations: true })", "properties": {"name": {"type": "string"}, "id": {"type": "number"}, "organizationId": {"type": "number"}, "description": {"type": "string"}, "ownerId": {"type": "string"}, "shared": {"type": "number"}, "widgets": {"type": "array", "items": {"$ref": "#/components/schemas/WidgetWithRelations"}}}, "required": ["name"], "additionalProperties": true, "x-typescript-type": "DashboardWithRelations"}, "DashboardPartial": {"title": "DashboardPartial", "type": "object", "description": "(tsType: Partial<Dashboard>, schemaOptions: { partial: true })", "properties": {"name": {"type": "string"}, "id": {"type": "number"}, "organizationId": {"type": "number"}, "description": {"type": "string"}, "ownerId": {"type": "string"}, "shared": {"type": "number"}}, "additionalProperties": true, "x-typescript-type": "Partial<Dashboard>"}, "NewDataConnections": {"title": "NewDataConnections", "type": "object", "description": "(tsType: Omit<DataConnections, 'id'>, schemaOptions: { title: 'NewDataConnections', exclude: [ 'id' ] })", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "address": {"type": "string"}, "startDate": {"type": "string", "format": "date-time"}, "projectId": {"type": "number"}}, "required": ["name"], "additionalProperties": true, "x-typescript-type": "Omit<DataConnections, 'id'>"}, "DataConnectionsWithRelations": {"title": "DataConnectionsWithRelations", "type": "object", "description": "(tsType: DataConnectionsWithRelations, schemaOptions: { includeRelations: true })", "properties": {"name": {"type": "string"}, "id": {"type": "number"}, "description": {"type": "string"}, "type": {"type": "string"}, "address": {"type": "string"}, "startDate": {"type": "string", "format": "date-time"}, "projectId": {"type": "number"}}, "required": ["name"], "additionalProperties": true, "x-typescript-type": "DataConnectionsWithRelations"}, "Organization": {"title": "Organization", "type": "object", "properties": {"id": {"type": "number"}, "name": {"type": "string"}, "dev": {"type": "boolean"}, "beta": {"type": "boolean"}, "attribution": {"type": "boolean"}}, "required": ["name"], "additionalProperties": false}, "NewOrganization": {"title": "NewOrganization", "type": "object", "description": "(tsType: Omit<Organization, 'id'>, schemaOptions: { title: 'NewOrganization', exclude: [ 'id' ] })", "properties": {"name": {"type": "string"}, "dev": {"type": "boolean"}, "beta": {"type": "boolean"}, "attribution": {"type": "boolean"}}, "required": ["name"], "additionalProperties": false, "x-typescript-type": "Omit<Organization, 'id'>"}, "OrganizationWithRelations": {"title": "OrganizationWithRelations", "type": "object", "description": "(tsType: OrganizationWithRelations, schemaOptions: { includeRelations: true })", "properties": {"id": {"type": "number"}, "name": {"type": "string"}, "dev": {"type": "boolean"}, "beta": {"type": "boolean"}, "attribution": {"type": "boolean"}}, "required": ["name"], "additionalProperties": false, "x-typescript-type": "OrganizationWithRelations"}, "OrganizationPartial": {"title": "OrganizationPartial", "type": "object", "description": "(tsType: Partial<Organization>, schemaOptions: { partial: true })", "properties": {"id": {"type": "number"}, "name": {"type": "string"}, "dev": {"type": "boolean"}, "beta": {"type": "boolean"}, "attribution": {"type": "boolean"}}, "additionalProperties": false, "x-typescript-type": "Partial<Organization>"}, "Project": {"title": "Project", "type": "object", "properties": {"name": {"type": "string"}, "id": {"type": "number"}, "description": {"type": "string"}, "activationDate": {"type": "string", "format": "date-time"}, "uuid": {"type": "string"}, "organizationId": {"type": "number"}, "dataConnectionsId": {"type": "number"}}, "required": ["name", "organizationId"], "additionalProperties": false}, "NewProject": {"title": "NewProject", "type": "object", "description": "(tsType: Omit<Project, 'id'>, schemaOptions: { title: 'NewProject', exclude: [ 'id' ] })", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "activationDate": {"type": "string", "format": "date-time"}, "uuid": {"type": "string"}, "organizationId": {"type": "number"}, "dataConnectionsId": {"type": "number"}}, "required": ["name", "organizationId"], "additionalProperties": false, "x-typescript-type": "Omit<Project, 'id'>"}, "ProjectWithRelations": {"title": "ProjectWithRelations", "type": "object", "description": "(tsType: ProjectWithRelations, schemaOptions: { includeRelations: true })", "properties": {"name": {"type": "string"}, "id": {"type": "number"}, "description": {"type": "string"}, "activationDate": {"type": "string", "format": "date-time"}, "uuid": {"type": "string"}, "organizationId": {"type": "number"}, "dataConnectionsId": {"type": "number"}, "dataConnections": {"type": "array", "items": {"$ref": "#/components/schemas/DataConnectionsWithRelations"}}}, "required": ["name", "organizationId"], "additionalProperties": false, "x-typescript-type": "ProjectWithRelations"}, "ProjectPartial": {"title": "ProjectPartial", "type": "object", "description": "(tsType: Partial<Project>, schemaOptions: { partial: true })", "properties": {"name": {"type": "string"}, "id": {"type": "number"}, "description": {"type": "string"}, "activationDate": {"type": "string", "format": "date-time"}, "uuid": {"type": "string"}, "organizationId": {"type": "number"}, "dataConnectionsId": {"type": "number"}}, "additionalProperties": false, "x-typescript-type": "Partial<Project>"}, "NewSegment": {"title": "NewSegment", "type": "object", "description": "(tsType: Omit<Segment, 'id'>, schemaOptions: { title: 'NewSegment', exclude: [ 'id' ] })", "properties": {"name": {"type": "string"}, "network": {"type": "string"}, "orgid": {"type": "number"}, "queries": {"type": "array", "items": {}}, "queryid": {"type": "string"}, "description": {"type": "string"}, "status": {"type": "string"}, "addressCount": {"type": "number"}, "viewname": {"type": "string"}}, "required": ["name", "network", "orgid", "queries"], "additionalProperties": false, "x-typescript-type": "Omit<Segment, 'id'>"}, "SegmentWithRelations": {"title": "SegmentWithRelations", "type": "object", "description": "(tsType: SegmentWithRelations, schemaOptions: { includeRelations: true })", "properties": {"id": {"type": "number"}, "name": {"type": "string"}, "network": {"type": "string"}, "orgid": {"type": "number"}, "queries": {"type": "array", "items": {}}, "queryid": {"type": "string"}, "description": {"type": "string"}, "status": {"type": "string"}, "addressCount": {"type": "number"}, "viewname": {"type": "string"}}, "required": ["name", "network", "orgid", "queries"], "additionalProperties": false, "x-typescript-type": "SegmentWithRelations"}, "SegmentPartial": {"title": "SegmentPartial", "type": "object", "description": "(tsType: Partial<Segment>, schemaOptions: { partial: true })", "properties": {"id": {"type": "number"}, "name": {"type": "string"}, "network": {"type": "string"}, "orgid": {"type": "number"}, "queries": {"type": "array", "items": {}}, "queryid": {"type": "string"}, "description": {"type": "string"}, "status": {"type": "string"}, "addressCount": {"type": "number"}, "viewname": {"type": "string"}}, "additionalProperties": false, "x-typescript-type": "Partial<Segment>"}, "UserCredentials": {"title": "UserCredentials", "type": "object", "properties": {"id": {"type": "string"}, "password": {"type": "string"}, "userId": {"type": "string"}}, "required": ["password", "userId"], "additionalProperties": false}, "NewUserCredentials": {"title": "NewUserCredentials", "type": "object", "description": "(tsType: Omit<UserCredentials, 'id'>, schemaOptions: { title: 'NewUserCredentials', exclude: [ 'id' ] })", "properties": {"password": {"type": "string"}, "userId": {"type": "string"}}, "required": ["password", "userId"], "additionalProperties": false, "x-typescript-type": "Omit<UserCredentials, 'id'>"}, "UserCredentialsWithRelations": {"title": "UserCredentialsWithRelations", "type": "object", "description": "(tsType: UserCredentialsWithRelations, schemaOptions: { includeRelations: true })", "properties": {"id": {"type": "string"}, "password": {"type": "string"}, "userId": {"type": "string"}}, "required": ["password", "userId"], "additionalProperties": false, "x-typescript-type": "UserCredentialsWithRelations"}, "UserCredentialsPartial": {"title": "UserCredentialsPartial", "type": "object", "description": "(tsType: Partial<UserCredentials>, schemaOptions: { partial: true })", "properties": {"id": {"type": "string"}, "password": {"type": "string"}, "userId": {"type": "string"}}, "additionalProperties": false, "x-typescript-type": "Partial<UserCredentials>"}, "NewUser": {"title": "NewUser", "type": "object", "description": "(tsType: Omit<User, 'id'>, schemaOptions: { title: 'NewUser', exclude: [ 'id' ] })", "properties": {"email": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "avatarColors": {"type": "string"}, "resetKey": {"type": "string"}, "resetCount": {"type": "number"}, "resetTimestamp": {"type": "string"}, "resetKeyTimestamp": {"type": "string"}, "roles": {"type": "array", "items": {"type": "string"}}, "OrganizationId": {"type": "number"}}, "required": ["email"], "additionalProperties": false, "x-typescript-type": "Omit<User, 'id'>"}, "UserWithRelations": {"title": "UserWithRelations", "type": "object", "description": "(tsType: UserWithRelations, schemaOptions: { includeRelations: true })", "properties": {"id": {"type": "string"}, "email": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "avatarColors": {"type": "string"}, "resetKey": {"type": "string"}, "resetCount": {"type": "number"}, "resetTimestamp": {"type": "string"}, "resetKeyTimestamp": {"type": "string"}, "roles": {"type": "array", "items": {"type": "string"}}, "OrganizationId": {"type": "number"}, "userCredentials": {"$ref": "#/components/schemas/UserCredentialsWithRelations"}, "organization": {"$ref": "#/components/schemas/OrganizationWithRelations"}}, "required": ["email"], "additionalProperties": false, "x-typescript-type": "UserWithRelations"}, "UserPartial": {"title": "UserPartial", "type": "object", "description": "(tsType: Partial<User>, schemaOptions: { partial: true })", "properties": {"id": {"type": "string"}, "email": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "avatarColors": {"type": "string"}, "resetKey": {"type": "string"}, "resetCount": {"type": "number"}, "resetTimestamp": {"type": "string"}, "resetKeyTimestamp": {"type": "string"}, "roles": {"type": "array", "items": {"type": "string"}}, "OrganizationId": {"type": "number"}}, "additionalProperties": false, "x-typescript-type": "Partial<User>"}, "NewWidget": {"title": "NewWidget", "type": "object", "description": "(tsType: Omit<Widget, 'id'>, schemaOptions: { title: 'NewWidget', exclude: [ 'id' ] })", "properties": {"type": {"type": "string"}, "data": {"type": "string"}, "name": {"type": "string"}, "label": {"type": "string"}, "projectId": {"type": "string"}, "size": {"type": "string"}, "dashboardId": {"type": "number"}}, "required": ["name", "projectId"], "additionalProperties": true, "x-typescript-type": "Omit<Widget, 'id'>"}, "loopback.Count": {"type": "object", "title": "loopback.Count", "x-typescript-type": "@loopback/repository#Count", "properties": {"count": {"type": "number"}}}, "Audience.Filter": {"type": "object", "title": "<PERSON><PERSON>Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "Audience.Where<PERSON><PERSON>er", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"name": {"type": "boolean"}, "id": {"type": "boolean"}, "description": {"type": "boolean"}, "createdDate": {"type": "boolean"}}, "additionalProperties": true}, {"type": "array", "items": {"type": "string", "example": "name"}, "uniqueItems": true}], "title": "<PERSON><PERSON>Fields"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Audience>"}, "Dashboard.ScopeFilter": {"type": "object", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {}, "additionalProperties": true}, {"type": "array", "items": {"type": "string"}, "uniqueItems": true}]}, "include": {"type": "array", "items": {"type": "object", "properties": {}, "additionalProperties": true}}}, "additionalProperties": false, "title": "Dashboard.ScopeFilter"}, "Dashboard.IncludeFilter.Items": {"title": "Dashboard.IncludeFilter.Items", "type": "object", "properties": {"relation": {"type": "string"}, "scope": {"$ref": "#/components/schemas/Dashboard.ScopeFilter"}}}, "Dashboard.Filter": {"type": "object", "title": "Dashboard.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "Dashboard.WhereF<PERSON>er", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"name": {"type": "boolean"}, "id": {"type": "boolean"}, "organizationId": {"type": "boolean"}, "description": {"type": "boolean"}, "ownerId": {"type": "boolean"}, "shared": {"type": "boolean"}}, "additionalProperties": true}, {"type": "array", "items": {"type": "string", "example": "name"}, "uniqueItems": true}], "title": "Dashboard.Fields"}, "include": {"title": "Dashboard.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/Dashboard.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Dashboard>"}, "DataConnections.Filter": {"type": "object", "title": "DataConnections.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "DataConnections.WhereFilter", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"name": {"type": "boolean"}, "id": {"type": "boolean"}, "description": {"type": "boolean"}, "type": {"type": "boolean"}, "address": {"type": "boolean"}, "startDate": {"type": "boolean"}, "projectId": {"type": "boolean"}}, "additionalProperties": true}, {"type": "array", "items": {"type": "string", "example": "name"}, "uniqueItems": true}], "title": "DataConnections.Fields"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<DataConnections>"}, "InviteWithRelations": {"title": "InviteWithRelations", "type": "object", "description": "(tsType: InviteWithRelations, schemaOptions: { includeRelations: true })", "properties": {"id": {"type": "number"}, "email": {"type": "string"}, "orgId": {"type": "number"}, "dateSent": {"type": "string", "format": "date-time"}, "inviteCode": {"type": "string"}, "accepted": {"type": "boolean"}, "expirationDate": {"type": "string", "format": "date-time"}}, "required": ["email", "orgId"], "additionalProperties": true, "x-typescript-type": "InviteWithRelations"}, "Organization.Filter": {"type": "object", "title": "Organization.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "Organization.WhereFilter", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"id": {"type": "boolean"}, "name": {"type": "boolean"}, "dev": {"type": "boolean"}, "beta": {"type": "boolean"}, "attribution": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["id", "name", "dev", "beta", "attribution"], "example": "id"}, "uniqueItems": true}], "title": "Organization.Fields"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Organization>"}, "PingResponse": {"type": "object", "title": "PingResponse", "properties": {"greeting": {"type": "string"}, "date": {"type": "string"}, "url": {"type": "string"}, "headers": {"type": "object", "properties": {"Content-Type": {"type": "string"}}, "additionalProperties": true}}}, "Project.ScopeFilter": {"type": "object", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {}, "additionalProperties": true}, {"type": "array", "items": {"type": "string"}, "uniqueItems": true}]}, "include": {"type": "array", "items": {"type": "object", "properties": {}, "additionalProperties": true}}}, "additionalProperties": false, "title": "<PERSON><PERSON>"}, "Project.IncludeFilter.Items": {"title": "Project.IncludeFilter.Items", "type": "object", "properties": {"relation": {"type": "string"}, "scope": {"$ref": "#/components/schemas/Project.ScopeFilter"}}}, "Project.Filter": {"type": "object", "title": "Project.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "Project.WhereF<PERSON>er", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"name": {"type": "boolean"}, "id": {"type": "boolean"}, "description": {"type": "boolean"}, "activationDate": {"type": "boolean"}, "uuid": {"type": "boolean"}, "organizationId": {"type": "boolean"}, "dataConnectionsId": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["name", "id", "description", "activationDate", "uuid", "organizationId", "dataConnectionsId"], "example": "name"}, "uniqueItems": true}], "title": "Project.Fields"}, "include": {"title": "Project.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/Project.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Project>"}, "Segment.Filter": {"type": "object", "title": "Segment.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "Segment.WhereFilter", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"id": {"type": "boolean"}, "name": {"type": "boolean"}, "network": {"type": "boolean"}, "orgid": {"type": "boolean"}, "queries": {"type": "boolean"}, "queryid": {"type": "boolean"}, "description": {"type": "boolean"}, "status": {"type": "boolean"}, "addressCount": {"type": "boolean"}, "viewname": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["id", "name", "network", "orgid", "queries", "queryid", "description", "status", "addressCount", "viewname"], "example": "id"}, "uniqueItems": true}], "title": "Segment.Fields"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Segment>"}, "UserCredentials.Filter": {"type": "object", "title": "UserCredentials.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "UserCredentials.WhereFilter", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"id": {"type": "boolean"}, "password": {"type": "boolean"}, "userId": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["id", "password", "userId"], "example": "id"}, "uniqueItems": true}], "title": "UserCredentials.Fields"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<UserCredentials>"}, "User.ScopeFilter": {"type": "object", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {}, "additionalProperties": true}, {"type": "array", "items": {"type": "string"}, "uniqueItems": true}]}, "include": {"type": "array", "items": {"type": "object", "properties": {}, "additionalProperties": true}}}, "additionalProperties": false, "title": "<PERSON><PERSON><PERSON>"}, "User.IncludeFilter.Items": {"title": "User.IncludeFilter.Items", "type": "object", "properties": {"relation": {"type": "string"}, "scope": {"$ref": "#/components/schemas/User.ScopeFilter"}}}, "User.Filter": {"type": "object", "title": "User.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "User.<PERSON><PERSON><PERSON>er", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"id": {"type": "boolean"}, "email": {"type": "boolean"}, "firstName": {"type": "boolean"}, "lastName": {"type": "boolean"}, "avatarColors": {"type": "boolean"}, "resetKey": {"type": "boolean"}, "resetCount": {"type": "boolean"}, "resetTimestamp": {"type": "boolean"}, "resetKeyTimestamp": {"type": "boolean"}, "roles": {"type": "boolean"}, "OrganizationId": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["id", "email", "firstName", "lastName", "avatarColors", "reset<PERSON>ey", "resetCount", "resetTimestamp", "resetKeyTimestamp", "roles", "OrganizationId"], "example": "id"}, "uniqueItems": true}], "title": "<PERSON><PERSON><PERSON>"}, "include": {"title": "User.<PERSON><PERSON><PERSON><PERSON>er", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/User.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<User>"}, "Widget.Filter": {"type": "object", "title": "Widget.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "Widget.<PERSON><PERSON><PERSON><PERSON>", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"id": {"type": "boolean"}, "type": {"type": "boolean"}, "data": {"type": "boolean"}, "name": {"type": "boolean"}, "label": {"type": "boolean"}, "projectId": {"type": "boolean"}, "size": {"type": "boolean"}, "dashboardId": {"type": "boolean"}}, "additionalProperties": true}, {"type": "array", "items": {"type": "string", "example": "id"}, "uniqueItems": true}], "title": "Widget.Fields"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Widget>"}}, "securitySchemes": {"jwt": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}, "security": [{"jwt": []}]}