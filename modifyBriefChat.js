const fs = require('fs');

const filePath = '/home/<USER>/Documents/Code/raleon_webapp/public/client/components/agent-task/BriefChat.ts.vue';
let content = fs.readFileSync(filePath, 'utf8');

// Add state variables for confirm modal
content = content.replace(
  /const isAtBottom = ref\(true\);/,
  `const isAtBottom = ref(true);

\t\t// Confirm modal state
\t\tconst showConfirmModal = ref(false);
\t\tconst pendingBriefSegment = ref<ChatMessageSegment  < /dev/null |  null>(null);`
);

// Modify loadHistoricBrief function
content = content.replace(
  /const loadHistoricBrief = \(segment: ChatMessageSegment\) => \{\n\t\tconsole\.log\('Loading brief:', segment\.id, segment\.briefData \? 'Data present' : 'Data MISSING'\);\n\t\tif \(segment\.briefData\) \{/,
  `const loadHistoricBrief = (segment: ChatMessageSegment) => {
\t\tconsole.log('Loading brief:', segment.id, segment.briefData ? 'Data present' : 'Data MISSING');
\t\t
\t\t// Check if there are unsaved changes in the current brief
\t\tif (props.currentBriefText && props.currentBriefText.trim() \!== '') {
\t\t\t// Store the segment to be loaded after confirmation
\t\t\tpendingBriefSegment.value = segment;
\t\t\t// Show confirmation modal
\t\t\tshowConfirmModal.value = true;
\t\t\treturn;
\t\t}
\t\t
\t\t// No unsaved changes, proceed with loading
\t\tapplyHistoricBrief(segment);
\t};
\t
\t// Function to apply the selected brief after confirmation or directly
\tconst applyHistoricBrief = (segment: ChatMessageSegment) => {
\t\tif (segment.briefData) {`
);

// Add confirm function before return statement
content = content.replace(
  /return \{/,
  `// Confirm modal handlers
\tconst confirmBriefLoad = () => {
\t\tif (pendingBriefSegment.value) {
\t\t\tapplyHistoricBrief(pendingBriefSegment.value);
\t\t\tpendingBriefSegment.value = null;
\t\t}
\t\tshowConfirmModal.value = false;
\t};

\treturn {`
);

// Add modal-related properties to the returned object
content = content.replace(
  /handleErrorRetry, \/\/ Add the error retry handler/,
  `handleErrorRetry, // Add the error retry handler
\t\t// Confirm modal
\t\tshowConfirmModal,
\t\tconfirmBriefLoad,`
);

fs.writeFileSync(filePath, content);
console.log('BriefChat.ts.vue has been updated');
