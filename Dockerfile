# Check out https://hub.docker.com/_/node to select a new base image
FROM node:18-slim

# Install PM2 globally
RUN npm install -g pm2

# Set to a non-root built-in user `node`
USER node

# Create app directory (with user `node`)
RUN mkdir -p /home/<USER>/app

WORKDIR /home/<USER>/app

# Install app dependencies
# A wildcard is used to ensure both package.json AND package-lock.json are copied
# where available (npm@5+)
COPY --chown=node package*.json ./

COPY --chown=node template.env ./.env

RUN npm install

# Bundle app source code
COPY --chown=node . .

RUN npm run build

RUN mkdir -p /home/<USER>/app/logs

# Bind to all network interfaces so that it can be mapped to the host OS
ENV HOST=0.0.0.0 PORT=3030

EXPOSE ${PORT}
CMD [ "pm2-runtime", "ecosystem.config.js" ]
