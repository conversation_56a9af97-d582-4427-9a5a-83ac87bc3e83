# raleon-webapp

This application is generated using [LoopBack 4 CLI](https://loopback.io/doc/en/lb4/Command-line-interface.html) with the
[initial project layout](https://loopback.io/doc/en/lb4/Loopback-application-layout.html).

## Install dependencies

By default, dependencies were installed when this application was generated.
Whenever dependencies in `package.json` are changed, run the following command:

```sh
npm install
```

To only install resolved dependencies in `package-lock.json`:

```sh
npm ci
```

## Run the application

```sh
npm start
```

## Working with the UI

Open a second terminal and run this command
```sh
npm run dev:ui
```

In the second terminal you will see the location to view the live version of the UI.


You can also run `node .` to skip the build step.

Open http://127.0.0.1:3030 in your browser.

## Rebuild the project

To incrementally build the project:

```sh
npm run build
```

To force a full build by cleaning up cached artifacts:

```sh
npm run rebuild
```

## Fix code style and formatting issues

```sh
npm run lint
```

To automatically fix such issues:

```sh
npm run lint:fix
```

## Other useful commands

- `npm run migrate`: Migrate database schemas for models
- `npm run openapi-spec`: Generate OpenAPI spec into a file
- `npm run docker:build`: Build a Docker image for this application
- `npm run docker:run`: Run this application inside a Docker container

## Tests

```sh
npm test
```

[![LoopBack](https://github.com/loopbackio/loopback-next/raw/master/docs/site/imgs/branding/Powered-by-LoopBack-Badge-(blue)-@2x.png)](http://loopback.io/)


## Adding a Table to Postgres

- Create a model using lb4 model
- Create a repository using lb4 repository selecting (db_dev)
- To create crud endpoints for model `lb4 rest-crud --datasource dev_db --model modelname
- npm run migrate - Note this will change the database

## Adding a new relationship

- Use `lb4 relation` via CLI to generate the relationship

## Adding the Environment Variables Using dotenv
- create a `.env` file in the root of the project with the below contents:
```dotenv
SENDER=<EMAIL>
APPLICATION_URL=http://localhost:3000
SES_IAM_ACCESS_KEY=
SES_IAM_SECRET_KEY=
REGION=us-east-1
PASSWORD_RESET_EMAIL_LIMIT=10
```

Ray Leon was born and named Noah
