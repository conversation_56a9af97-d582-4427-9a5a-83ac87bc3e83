# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

#local scripts
migrate_and_copy_dev_to_prod.ps1

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage

# nyc test coverage
.nyc_output

# Grunt intermediate storage (http://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (http://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules/
jspm_packages/

# Typescript v1 declaration files
typings/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# Transpiled JavaScript files from Typescript
/dist

# Cache used by TypeScript's incremental build
*.tsbuildinfo

# UI Build
public/dist

.vscode/*
.vscode/settings.json
.DS_Store
**/.DS_Store
**/*/.DS_Store

# Cypress Automated Testing
**/cypress/screenshots/*
**/cypress/videos/*
**/cypress/downloads/*

node_modules
.env
coverage
coverage.json
typechain
typechain-types

# Hardhat files
cache

# aider
.aider-desk/*
.aider.*

**/.claude/settings.local.json

# Test build output
test_build/
test/node_modules/
